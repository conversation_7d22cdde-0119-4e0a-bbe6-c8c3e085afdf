import datetime

from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config


class ProjectPersonnelDataQuery(object):
    """
        项目人员数据查询
    """

    def project_personnel(self):
        """
            数据查询
        """

        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT PM, architecture, beautification, tree_sutra, " \
                    "weak_current, business, supply_chain, inform_a_person " \
                    "FROM project_team_information_data"
        personnel_result = db.get_all(query_sql)
        # 获取七彩石配置

        table = config.get_config_map(
            "intranet_question_permissions"
        )
        # 数经
        tree_sutra_list = []
        # 其他成员
        other_list = []

        for row in personnel_result:
            PM = row.get("PM")
            architecture = row.get("architecture")
            beautification = row.get("beautification")
            tree_sutra = row.get("tree_sutra")
            weak_current = row.get("weak_current")
            business = row.get("business")
            supply_chain = row.get("supply_chain")
            inform_a_person = row.get("inform_a_person")

            # 判断并拆分 tree_sutra
            if tree_sutra:
                if ";" in tree_sutra:
                    tree_sutra_list.extend(tree_sutra.split(";"))
                else:
                    tree_sutra_list.append(tree_sutra)

            # 判断并拆分其他成员
            for member in [PM, architecture, beautification, weak_current, business, supply_chain, inform_a_person]:
                if member:
                    if ";" in member:
                        other_list.extend(member.split(";"))
                    else:
                        other_list.append(member)

        # 将三个字典添加到data列表中
        data = {
            '数经': list(set(tree_sutra_list)),
            '横向团队': list(set(other_list)),
            '白名单': list(set(table))
        }

        return data
