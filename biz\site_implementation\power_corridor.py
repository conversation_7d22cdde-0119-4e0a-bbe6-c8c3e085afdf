import uuid

import numpy as np
import pandas as pd

from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class Precondition(object):
    """
        前置条件风险判断
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def risk_judgment(self):
        flow.complete_task(self.ctx.task_id, variables={})


class UploadData(AjaxTodoBase):
    """
        准备阶段资料上传
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(UploadData, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_master_plan_file_is_upload(self, overall_control_plan: list):
        try:
            datas1, bools1 = overall_control_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "总控计划未上传完成，请等待总控计划上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "总控计划未上传完成，请等待总控计划上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_drawing_is_upload(self, deepen_design_drawings: list):
        try:
            datas2, bools2 = deepen_design_drawings[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "图纸未上传完成，请等待图纸上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "图纸未上传完成，请等待图纸上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_bid_file_is_upload(self, bidding_documents: list):
        try:
            datas3, bools3 = bidding_documents[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "招投标文件未上传完成，请等待招投标文件上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "招投标文件未上传完成，请等待招投标文件上传完成后再做结单处理"}, True
        return datas3, bools3

    def verify_whether_the_upload_is_correct(self, overall_control_plan: list):
        # 获取上传文件的url
        url = overall_control_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        # 为文件重命名
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel判断文件是否上传成功
        """
        exclpath = "/data/nbroker/" + new_file_name
        # 读取下载成功后的数据
        # 并解析
        global condition, content
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        required_values = {
            '3.1.1': '方仓及框架安装（结构工程）', '3.1.2': '电气工程（强电工程）', '3.1.3': '弱电系统（弱电工程）',
            '3.1.4': '装修工程', '3.1.5': '消防工程', '3.1.6': '暖通工程', '3.1.7': '设备上电调试',
            '3.2.1': '方仓及框架安装（结构工程）', '3.2.2': '电气工程（强电工程）', '3.2.3': '弱电系统（弱电工程）',
            '3.2.4': '装修工程', '3.2.5': '消防工程', '3.2.6': '暖通工程', '3.2.7': '设备上电调试',
            '3.3.1': '方仓及框架安装（结构工程）', '3.3.2': '电气工程（强电工程）', '3.3.3': '弱电系统（弱电工程）',
            '3.3.4': '装修工程', '3.3.5': '消防工程', '3.3.6': '暖通工程', '3.3.7': '设备上电调试',
            '3.4.1': '方仓及框架安装（结构工程）', '3.4.2': '电气工程（强电工程）', '3.4.3': '弱电系统（弱电工程）',
            '3.4.4': '装修工程', '3.4.5': '消防工程', '3.4.6': '暖通工程', '3.4.7': '设备上电调试',
            '3.5.1': '电力方仓安装', '3.5.2': '外市电工程（红线内）', '3.5.3': '补/排风系统', '3.5.4': '设备上电调试',
            '3.6.1': '柴发方仓安装', '3.6.2': '柴发油路系统', '3.6.3': '水处理', '3.6.4': '设备上电调试',
            '3.7.1': '装修（结构工程）', '3.7.2': '暖通工程', '3.7.3': '电气工程', '3.7.4': '弱电工程',
            '3.7.5': '设备上电调试',
            '3.8.1': '装修（结构工程）', '3.8.2': '暖通工程', '3.8.3': '电气工程', '3.8.4': '弱电工程',
            '3.8.5': '消防工程', '3.8.6': '设备上电调试'
        }
        missing_values = []
        for condition, content in required_values.items():
            if not ((df['序号'] == condition) & (df['工作内容'] == content)).any():
                missing_values.append((condition, content))

        if missing_values:
            print("缺失的值为：")
            for condition, content in missing_values:
                print(condition, content)
            return {"code": -1, "msg": f"总项计划上传错误，错误为{condition, content}，请重新上传"}, True

        return {"code": 0, "msg": "总项计划上传正确"}, False

    def designatedbidder(self, staff_name, msg_content, msg_type):
        """
            推消息到个人
            :param staff_name: 英文名
            :param msg_content: 文本
            :param msg_type: text/markdown
            :return:
        """
        ChatOpsSend(
            user_name=staff_name,
            msg_content=msg_content,
            msg_type=msg_type,
            des_type='single'
        ).call()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        handlers = self.ctx.variables.get("handlers")
        # 总控计划上传
        overall_control_plan = process_data.get('overall_control_plan', None)
        deepen_design_drawings = process_data.get('deepen_design_drawings', None)
        bidding_documents = process_data.get('bidding_documents', None)
        datas1, bools1 = self.check_master_plan_file_is_upload(overall_control_plan)
        datas2, bools2 = self.check_drawing_is_upload(deepen_design_drawings)
        datas3, bools3 = self.check_bid_file_is_upload(bidding_documents)
        # 调用当发传递相关参数
        total_plan_data, total_plan_bool = self.verify_whether_the_upload_is_correct(overall_control_plan)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        if total_plan_bool:
            return total_plan_data
        insert_data1 = {}
        if overall_control_plan and len(overall_control_plan) > 0 and 'response' in overall_control_plan[0]:
            insert_data1 = {
                'overall_control_plan': overall_control_plan[0]["response"]["FileList"][0]["url"],
                'deepen_design_drawings': deepen_design_drawings[0]["response"]["FileList"][0]["url"],
                'bidding_documents': bidding_documents[0]["response"]["FileList"][0]["url"],
                'dcops_ticket_id': ticket_id
            }

        # 风险项
        upgrade_list = []
        upgrade_paths = []
        precondition_list = process_data.get('precondition_list', None)
        if precondition_list:
            for item in precondition_list:
                is_upgrade = item.get('is_upgrade', None)
                upgrade_path = item.get('upgrade_path', None)
                if is_upgrade == '是' and upgrade_path:
                    upgrade_list.append({
                        'risk_item': item.get('risk_item', None),
                        'upgrade_path': upgrade_path.split(';')
                    })
                if is_upgrade == '是' and upgrade_path:
                    upgrade_paths.extend(upgrade_path.split(';'))
            for item in upgrade_list:
                risk_item = item['risk_item']
                upgrade_path = item['upgrade_path']

                for paths in upgrade_path:
                    paths_list = paths.split(';')
                    for path in paths_list:
                        message = f'{project_name},{risk_item}存在风险，请关注@{path}'
                        self.designatedbidder(path, message, 'text')

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("power_corridor_data", insert_data1)
        tb_db.commit()

        variables = {
            'overall_control_plan': overall_control_plan,
            'deepen_design_drawings': deepen_design_drawings,
            'bidding_documents': bidding_documents,
            'upgrade_list': upgrade_list,
            'precondition_list': precondition_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CosLib(object):
    """
        cos下载excel
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def mastercontrolplan(self, overall_control_plan):

        """
            cos下载总控计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        url = overall_control_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            excel解析总控计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            设备信息解析
        """
        sbxx_list = []
        start_flag = False
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 2.5:
                start_flag = True
            if start_flag:
                work_content = row['工作内容']
                construction_period = row['工期(日历日）']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                input = row['输入物']
                output = row['输出物']
                sbxx_list.append(
                    {
                        'ticket_id': ticket_id,
                        'serial_number': serial_number,
                        'work_content': work_content,
                        'construction_period': construction_period,
                        'start_time': plan_start_time,
                        'finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'input': input,
                        'output': output,
                        'construction_attribute': construction_attribute
                    }
                )
            if serial_number == 2.7:
                break
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("equipment_arrival_information", sbxx_list)
        tb_db.commit()
        """
            电力走廊数据解析
        """
        dlzl_list = []
        dlzl_responsible_person = ''
        block1_responsible_person = ''
        block2_responsible_person = ''
        block3_responsible_person = ''
        block4_responsible_person = ''
        cfscl_responsible_person = ''
        ahu_responsible_person = ''
        bg_responsible_person = ''
        syjy_responsible_person = ''
        sd_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.5:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                dlzl_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if dlzl_list[0]['responsible_person'] is None or dlzl_list[0]['responsible_person'] == 'Js_xxx':
                    dlzl_responsible_person = item_tube
                else:
                    dlzl_responsible_person = dlzl_list[0]['responsible_person']
                update_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                conditions = {
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("power_corridor_data", update_data, conditions)
                tb_db.commit()
        """
            IT-block1数据解析
        """
        block1_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.1:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                block1_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_list[0]['responsible_person'] is None or block1_list[0]['responsible_person'] == 'Js_xxx':
                    block1_responsible_person = item_tube
                else:
                    block1_responsible_person = block1_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("it_block1", insert_data)
                tb_db.commit()
        """
            IT-block2数据解析
        """
        block2_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.2:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                block2_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_list[0]['responsible_person'] is None or block2_list[0]['responsible_person'] == 'Js_xxx':
                    block2_responsible_person = item_tube
                else:
                    block2_responsible_person = block2_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("it_block2", insert_data)
                tb_db.commit()
        """
            IT-block3数据解析
        """
        block3_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.3:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                block3_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_list[0]['responsible_person'] is None or block3_list[0]['responsible_person'] == 'Js_xxx':
                    block3_responsible_person = item_tube
                else:
                    block3_responsible_person = block3_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("it_block3", insert_data)
                tb_db.commit()
        """
            IT-block4数据解析
        """
        block4_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.4:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                block4_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_list[0]['responsible_person'] is None or block4_list[0]['responsible_person'] == 'Js_xxx':
                    block4_responsible_person = item_tube
                else:
                    block4_responsible_person = block4_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("it_block4", insert_data)
                tb_db.commit()
        """
            柴发和水处理区域数据解析
        """
        cfscl_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.6:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                cfscl_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if cfscl_list[0]['responsible_person'] is None or cfscl_list[0]['responsible_person'] == 'Js_xxx':
                    cfscl_responsible_person = item_tube
                else:
                    cfscl_responsible_person = cfscl_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("firewoodhair_water_data", insert_data)
                tb_db.commit()
        """
            AHU区域数据解析
        """
        ahu_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.7:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                ahu_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_list[0]['responsible_person'] is None or ahu_list[0]['responsible_person'] == 'Js_xxx':
                    ahu_responsible_person = item_tube
                else:
                    ahu_responsible_person = ahu_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("ahu_data", insert_data)
                tb_db.commit()
        """
            办公区域数据解析
        """
        bg_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.8:
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                bg_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_list[0]['responsible_person'] is None or bg_list[0]['responsible_person'] == 'Js_xxx':
                    bg_responsible_person = item_tube
                else:
                    bg_responsible_person = bg_list[0]['responsible_person']
                insert_data = {
                    'task_name': task_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'output': output,
                    'dcops_ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("administrative_data", insert_data)
                tb_db.commit()
        """
            获取试验检验节点的处理人
        """
        syjy_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '2.9.6':
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                input_object = row['输入物']
                syjy_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output,
                        'input_object': input_object,
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if syjy_list[0]['responsible_person'] is None or syjy_list[0]['responsible_person'] == 'Js_xxx':
                    syjy_responsible_person = item_tube
                else:
                    syjy_responsible_person = syjy_list[0]['responsible_person']
        """
            获取送电处理人
        """
        sd_list = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '2.9.7':
                task_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output = row['输出物']
                input_object = row['输入物']
                sd_list.append(
                    {
                        'task_name': task_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,
                        'output': output,
                        'input_object': input_object,
                    }
                )
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if sd_list[0]['responsible_person'] is None or sd_list[0]['responsible_person'] == 'Js_xxx':
                    sd_responsible_person = item_tube
                else:
                    sd_responsible_person = sd_list[0]['responsible_person']
        variables = {
            'dlzl_list': dlzl_list,
            'block1_list': block1_list,
            'block2_list': block2_list,
            'block3_list': block3_list,
            'block4_list': block4_list,
            'cfscl_list': cfscl_list,
            'ahu_list': ahu_list,
            'bg_list': bg_list,
            'dlzl_responsible_person': dlzl_responsible_person,
            'block1_responsible_person': block1_responsible_person,
            'block2_responsible_person': block2_responsible_person,
            'block3_responsible_person': block3_responsible_person,
            'block4_responsible_person': block4_responsible_person,
            'cfscl_responsible_person': cfscl_responsible_person,
            'ahu_responsible_person': ahu_responsible_person,
            'bg_responsible_person': bg_responsible_person,
            'syjy_responsible_person': syjy_responsible_person,
            'sd_responsible_person': sd_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def dlzl_itemizedplan(self, dlzl_sub_plan):
        """
            cos下载电力走廊分项计划
        """
        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        global plan_start_time, plan_finish_time, responsible_person, tasks_name
        ticket_id = self.ctx.variables.get("ticket_id")
        url = dlzl_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel电力走廊解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            电力走廊-电力方仓安装
        """
        insert_list = []
        dlzl_dlfc_list = []
        work_list = []
        start_extracting = False
        dlzl_dlfc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.5.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                dlzl_dlfc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if dlzl_dlfc_list['responsible_person'] is None or dlzl_dlfc_list['responsible_person'] == 'Js_xxx':
                    dlzl_dlfc_responsible_person = item_tube
                else:
                    dlzl_dlfc_responsible_person = dlzl_dlfc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.6':
                break
            elif start_extracting:
                if serial_number == '3.5.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'fcaz_work_content': task_name,
                        'fcaz_start_time': start_time,
                        'fcaz_finish_time': finish_time,
                        'fcaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("power_corridor_data", insert_list)
        tb_db.commit()
        """
            电力走廊-补/排风系统
        """
        insert_list = []
        dlzl_bfxt_list = []
        work_list = []
        start_extracting = False
        dlzl_bfxt_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.5.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                dlzl_bfxt_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if dlzl_bfxt_list['responsible_person'] is None or dlzl_bfxt_list['responsible_person'] == 'Js_xxx':
                    dlzl_bfxt_responsible_person = item_tube
                else:
                    dlzl_bfxt_responsible_person = dlzl_bfxt_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.6':
                break
            elif start_extracting:
                if serial_number == '3.5.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'pfxt_work_content': task_name,
                        'pfxt_start_time': start_time,
                        'pfxt_finish_time': finish_time,
                        'pfxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("power_corridor_data", insert_list)
        tb_db.commit()
        """
            电力走廊-外市电工程
        """
        insert_list = []
        dlzl_wsd_list = []
        work_list = []
        start_extracting = False
        dlzl_wsd_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.5.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                dlzl_wsd_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if dlzl_wsd_list['responsible_person'] is None or dlzl_wsd_list['responsible_person'] == 'Js_xxx':
                    dlzl_wsd_responsible_person = item_tube
                else:
                    dlzl_wsd_responsible_person = dlzl_wsd_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.6':
                break
            elif start_extracting:
                if serial_number == '3.5.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'wsdgc_work_content': task_name,
                        'wsdgc_start_time': start_time,
                        'wsdgc_finish_time': finish_time,
                        'wsdgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("power_corridor_data", insert_list)
        tb_db.commit()
        """
            电力走廊-设备上电调试
        """
        insert_list = []
        dlzl_sdts_list = []
        work_list = []
        start_extracting = False
        dlzl_sdts_list_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.6:
                break
            if serial_number == '3.5.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                dlzl_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if dlzl_sdts_list['responsible_person'] is None or dlzl_sdts_list['responsible_person'] == 'Js_xxx':
                    dlzl_sdts_list_responsible_person = item_tube
                else:
                    dlzl_sdts_list_responsible_person = dlzl_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("power_corridor_data", insert_list)
        tb_db.commit()

        variables = {
            'dlzl_dlfc_list': dlzl_dlfc_list,
            'dlzl_bfxt_list': dlzl_bfxt_list,
            'dlzl_wsd_list': dlzl_wsd_list,
            'dlzl_sdts_list': dlzl_sdts_list,
            'dlzl_dlfc_responsible_person': dlzl_dlfc_responsible_person,
            'dlzl_bfxt_responsible_person': dlzl_bfxt_responsible_person,
            'dlzl_wsd_responsible_person': dlzl_wsd_responsible_person,
            'dlzl_sdts_list_responsible_person': dlzl_sdts_list_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def block1_itemizedplan(self, block1_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载方仓1分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = block1_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel方仓1解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            方仓1-电气工程
        """
        insert_list = []
        block1_qdgc_list = []
        work_list = []
        start_extracting = False
        block1_qdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_qdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_qdgc_list['responsible_person'] is None or block1_qdgc_list['responsible_person'] == 'Js_xxx':
                    block1_qdgc_responsible_person = item_tube
                else:
                    block1_qdgc_responsible_person = block1_qdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()

        """
            方仓1-弱电系统
        """
        insert_list = []
        block1_rdgc_list = []
        work_list = []
        start_extracting = False
        block1_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_rdgc_list['responsible_person'] is None or block1_rdgc_list['responsible_person'] == 'Js_xxx':
                    block1_rdgc_responsible_person = item_tube
                else:
                    block1_rdgc_responsible_person = block1_rdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdxt_work_content': task_name,
                        'rdxt_start_time': start_time,
                        'rdxt_finish_time': finish_time,
                        'rdxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()
        """
            方仓1-方仓及框架安装
        """
        insert_list = []
        block1_fckj_list = []
        work_list = []
        start_extracting = False
        block1_fckj_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_fckj_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_fckj_list['responsible_person'] is None or block1_fckj_list['responsible_person'] == 'Js_xxx':
                    block1_fckj_responsible_person = item_tube
                else:
                    block1_fckj_responsible_person = block1_fckj_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'kjaz_work_content': task_name,
                        'kjaz_start_time': start_time,
                        'kjaz_finish_time': finish_time,
                        'kjaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()
        """
            方仓1-装修工程
        """
        insert_list = []
        block1_zxgc_list = []
        work_list = []
        start_extracting = False
        block1_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_zxgc_list['responsible_person'] is None or block1_zxgc_list['responsible_person'] == 'Js_xxx':
                    block1_zxgc_responsible_person = item_tube
                else:
                    block1_zxgc_responsible_person = block1_zxgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zxgc_work_content': task_name,
                        'zxgc_start_time': start_time,
                        'zxgc_finish_time': finish_time,
                        'zxgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()
        """
            方仓1-暖通工程
        """
        insert_list = []
        block1_ntgc_list = []
        work_list = []
        start_extracting = False
        block1_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.6':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_ntgc_list['responsible_person'] is None or block1_ntgc_list['responsible_person'] == 'Js_xxx':
                    block1_ntgc_responsible_person = item_tube
                else:
                    block1_ntgc_responsible_person = block1_ntgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.7':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()
        """
            方仓1-消防工程
        """
        insert_list = []
        block1_xfgc_list = []
        work_list = []
        start_extracting = False
        block1_xfgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.1.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_xfgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_xfgc_list['responsible_person'] is None or block1_xfgc_list['responsible_person'] == 'Js_xxx':
                    block1_xfgc_responsible_person = item_tube
                else:
                    block1_xfgc_responsible_person = block1_xfgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.2':
                break
            elif start_extracting:
                if serial_number == '3.1.6':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'xfgc_work_content': task_name,
                        'xfgc_start_time': start_time,
                        'xfgc_finish_time': finish_time,
                        'xfgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()
        """
            方仓1-设备上电调试
        """
        insert_list = []
        block1_sdts_list = []
        work_list = []
        start_extracting = False
        block1_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.2:
                break
            if serial_number == '3.1.7':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block1_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block1_sdts_list['responsible_person'] is None or block1_sdts_list['responsible_person'] == 'Js_xxx':
                    block1_sbsdts_responsible_person = item_tube
                else:
                    block1_sbsdts_responsible_person = block1_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()

        variables = {
            'block1_qdgc_list': block1_qdgc_list,
            'block1_rdgc_list': block1_rdgc_list,
            'block1_fckj_list': block1_fckj_list,
            'block1_zxgc_list': block1_zxgc_list,
            'block1_ntgc_list': block1_ntgc_list,
            'block1_xfgc_list': block1_xfgc_list,
            'block1_sdts_list': block1_sdts_list,
            'block1_qdgc_responsible_person': block1_qdgc_responsible_person,
            'block1_rdgc_responsible_person': block1_rdgc_responsible_person,
            'block1_fckj_responsible_person': block1_fckj_responsible_person,
            'block1_zxgc_responsible_person': block1_zxgc_responsible_person,
            'block1_ntgc_responsible_person': block1_ntgc_responsible_person,
            'block1_xfgc_responsible_person': block1_xfgc_responsible_person,
            'block1_sbsdts_responsible_person': block1_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def block2_itemizedplan(self, block2_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载方仓2分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = block2_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel方仓2解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            方仓2-电气工程
        """
        insert_list = []
        block2_qdgc_list = []
        work_list = []
        start_extracting = False
        block2_qdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_qdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_qdgc_list['responsible_person'] is None or block2_qdgc_list['responsible_person'] == 'Js_xxx':
                    block2_qdgc_responsible_person = item_tube
                else:
                    block2_qdgc_responsible_person = block2_qdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block1", insert_list)
        tb_db.commit()

        """
            方仓2-弱电系统
        """
        insert_list = []
        block2_rdgc_list = []
        work_list = []
        start_extracting = False
        block2_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_rdgc_list['responsible_person'] is None or block2_rdgc_list['responsible_person'] == 'Js_xxx':
                    block2_rdgc_responsible_person = item_tube
                else:
                    block2_rdgc_responsible_person = block2_rdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdxt_work_content': task_name,
                        'rdxt_start_time': start_time,
                        'rdxt_finish_time': finish_time,
                        'rdxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        """
            方仓2-方仓及框架安装
        """
        insert_list = []
        block2_fckj_list = []
        work_list = []
        start_extracting = False
        block2_fckj_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_fckj_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_fckj_list['responsible_person'] is None or block2_fckj_list['responsible_person'] == 'Js_xxx':
                    block2_fckj_responsible_person = item_tube
                else:
                    block2_fckj_responsible_person = block2_fckj_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'kjaz_work_content': task_name,
                        'kjaz_start_time': start_time,
                        'kjaz_finish_time': finish_time,
                        'kjaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        """
            方仓2-装修工程
        """
        insert_list = []
        block2_zxgc_list = []
        work_list = []
        start_extracting = False
        block2_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_zxgc_list['responsible_person'] is None or block2_zxgc_list['responsible_person'] == 'Js_xxx':
                    block2_zxgc_responsible_person = item_tube
                else:
                    block2_zxgc_responsible_person = block2_zxgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zxgc_work_content': task_name,
                        'zxgc_start_time': start_time,
                        'zxgc_finish_time': finish_time,
                        'zxgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        """
            方仓2-暖通工程
        """
        insert_list = []
        block2_ntgc_list = []
        work_list = []
        start_extracting = False
        block2_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.6':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_ntgc_list['responsible_person'] is None or block2_ntgc_list['responsible_person'] == 'Js_xxx':
                    block2_ntgc_responsible_person = item_tube
                else:
                    block2_ntgc_responsible_person = block2_ntgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.7':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        """
            方仓2-消防工程
        """
        insert_list = []
        block2_xfgc_list = []
        work_list = []
        start_extracting = False
        block2_xfgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.2.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_xfgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_xfgc_list['responsible_person'] is None or block2_xfgc_list['responsible_person'] == 'Js_xxx':
                    block2_xfgc_responsible_person = item_tube
                else:
                    block2_xfgc_responsible_person = block2_xfgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.3':
                break
            elif start_extracting:
                if serial_number == '3.2.6':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'xfgc_work_content': task_name,
                        'xfgc_start_time': start_time,
                        'xfgc_finish_time': finish_time,
                        'xfgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        """
            方仓2-设备上电调试
        """
        insert_list = []
        block2_sdts_list = []
        work_list = []
        start_extracting = False
        block2_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.3:
                break
            if serial_number == '3.2.7':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block2_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block2_sdts_list['responsible_person'] is None or block2_sdts_list['responsible_person'] == 'Js_xxx':
                    block2_sbsdts_responsible_person = item_tube
                else:
                    block2_sbsdts_responsible_person = block2_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block2", insert_list)
        tb_db.commit()
        variables = {
            'block2_qdgc_list': block2_qdgc_list,
            'block2_rdgc_list': block2_rdgc_list,
            'block2_fckj_list': block2_fckj_list,
            'block2_zxgc_list': block2_zxgc_list,
            'block2_ntgc_list': block2_ntgc_list,
            'block2_xfgc_list': block2_xfgc_list,
            'block2_sdts_list': block2_sdts_list,
            'block2_qdgc_responsible_person': block2_qdgc_responsible_person,
            'block2_rdgc_responsible_person': block2_rdgc_responsible_person,
            'block2_fckj_responsible_person': block2_fckj_responsible_person,
            'block2_zxgc_responsible_person': block2_zxgc_responsible_person,
            'block2_ntgc_responsible_person': block2_ntgc_responsible_person,
            'block2_xfgc_responsible_person': block2_xfgc_responsible_person,
            'block2_sbsdts_responsible_person': block2_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def block3_itemizedplan(self, block3_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载方仓3分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = block3_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel方仓3解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            方仓3-电气工程
        """
        insert_list = []
        block3_qdgc_list = []
        work_list = []
        start_extracting = False
        block3_qdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_qdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_qdgc_list['responsible_person'] is None or block3_qdgc_list['responsible_person'] == 'Js_xxx':
                    block3_qdgc_responsible_person = item_tube
                else:
                    block3_qdgc_responsible_person = block3_qdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()

        """
            方仓3-弱电系统
        """
        insert_list = []
        block3_rdgc_list = []
        work_list = []
        start_extracting = False
        block3_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_rdgc_list['responsible_person'] is None or block3_rdgc_list['responsible_person'] == 'Js_xxx':
                    block3_rdgc_responsible_person = item_tube
                else:
                    block3_rdgc_responsible_person = block3_rdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdxt_work_content': task_name,
                        'rdxt_start_time': start_time,
                        'rdxt_finish_time': finish_time,
                        'rdxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        """
            方仓3-方仓及框架安装
        """
        insert_list = []
        block3_fckj_list = []
        work_list = []
        start_extracting = False
        block3_fckj_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_fckj_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_fckj_list['responsible_person'] is None or block3_fckj_list['responsible_person'] == 'Js_xxx':
                    block3_fckj_responsible_person = item_tube
                else:
                    block3_fckj_responsible_person = block3_fckj_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'kjaz_work_content': task_name,
                        'kjaz_start_time': start_time,
                        'kjaz_finish_time': finish_time,
                        'kjaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        """
            方仓3-装修工程
        """
        insert_list = []
        block3_zxgc_list = []
        work_list = []
        start_extracting = False
        block3_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_zxgc_list['responsible_person'] is None or block3_zxgc_list['responsible_person'] == 'Js_xxx':
                    block3_zxgc_responsible_person = item_tube
                else:
                    block3_zxgc_responsible_person = block3_zxgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zxgc_work_content': task_name,
                        'zxgc_start_time': start_time,
                        'zxgc_finish_time': finish_time,
                        'zxgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        """
            方仓3-暖通工程
        """
        insert_list = []
        block3_ntgc_list = []
        work_list = []
        start_extracting = False
        block3_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.6':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_ntgc_list['responsible_person'] is None or block3_ntgc_list['responsible_person'] == 'Js_xxx':
                    block3_ntgc_responsible_person = item_tube
                else:
                    block3_ntgc_responsible_person = block3_ntgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.7':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        """
            方仓3-消防工程
        """
        insert_list = []
        block3_xfgc_list = []
        work_list = []
        start_extracting = False
        block3_xfgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.3.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_xfgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_xfgc_list['responsible_person'] is None or block3_xfgc_list['responsible_person'] == 'Js_xxx':
                    block3_xfgc_responsible_person = item_tube
                else:
                    block3_xfgc_responsible_person = block3_xfgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.4':
                break
            elif start_extracting:
                if serial_number == '3.3.6':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'xfgc_work_content': task_name,
                        'xfgc_start_time': start_time,
                        'xfgc_finish_time': finish_time,
                        'xfgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        """
            方仓3-设备上电调试
        """
        insert_list = []
        block3_sdts_list = []
        work_list = []
        start_extracting = False
        block3_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.4:
                break
            if serial_number == '3.3.7':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block3_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block3_sdts_list['responsible_person'] is None or block3_sdts_list['responsible_person'] == 'Js_xxx':
                    block3_sbsdts_responsible_person = item_tube
                else:
                    block3_sbsdts_responsible_person = block3_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block3", insert_list)
        tb_db.commit()
        variables = {
            'block3_qdgc_list': block3_qdgc_list,
            'block3_rdgc_list': block3_rdgc_list,
            'block3_fckj_list': block3_fckj_list,
            'block3_zxgc_list': block3_zxgc_list,
            'block3_ntgc_list': block3_ntgc_list,
            'block3_xfgc_list': block3_xfgc_list,
            'block3_sdts_list': block3_sdts_list,
            'block3_qdgc_responsible_person': block3_qdgc_responsible_person,
            'block3_rdgc_responsible_person': block3_rdgc_responsible_person,
            'block3_fckj_responsible_person': block3_fckj_responsible_person,
            'block3_zxgc_responsible_person': block3_zxgc_responsible_person,
            'block3_ntgc_responsible_person': block3_ntgc_responsible_person,
            'block3_xfgc_responsible_person': block3_xfgc_responsible_person,
            'block3_sbsdts_responsible_person': block3_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def block4_itemizedplan(self, block4_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载方仓4分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = block4_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel方仓4解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            方仓4-电气工程
        """
        insert_list = []
        block4_qdgc_list = []
        work_list = []
        start_extracting = False
        block4_qdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_qdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_qdgc_list['responsible_person'] is None or block4_qdgc_list['responsible_person'] == 'Js_xxx':
                    block4_qdgc_responsible_person = item_tube
                else:
                    block4_qdgc_responsible_person = block4_qdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()

        """
            方仓4-弱电系统
        """
        insert_list = []
        block4_rdgc_list = []
        work_list = []
        start_extracting = False
        block4_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_rdgc_list['responsible_person'] is None or block4_rdgc_list['responsible_person'] == 'Js_xxx':
                    block4_rdgc_responsible_person = item_tube
                else:
                    block4_rdgc_responsible_person = block4_rdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdxt_work_content': task_name,
                        'rdxt_start_time': start_time,
                        'rdxt_finish_time': finish_time,
                        'rdxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        """
            方仓4-方仓及框架安装
        """
        insert_list = []
        block4_fckj_list = []
        work_list = []
        start_extracting = False
        block4_fckj_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_fckj_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_fckj_list['responsible_person'] is None or block4_fckj_list['responsible_person'] == 'Js_xxx':
                    block4_fckj_responsible_person = item_tube
                else:
                    block4_fckj_responsible_person = block4_fckj_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'kjaz_work_content': task_name,
                        'kjaz_start_time': start_time,
                        'kjaz_finish_time': finish_time,
                        'kjaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        """
            方仓4-装修工程
        """
        insert_list = []
        block4_zxgc_list = []
        work_list = []
        start_extracting = False
        block4_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_zxgc_list['responsible_person'] is None or block4_zxgc_list['responsible_person'] == 'Js_xxx':
                    block4_zxgc_responsible_person = item_tube
                else:
                    block4_zxgc_responsible_person = block4_zxgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zxgc_work_content': task_name,
                        'zxgc_start_time': start_time,
                        'zxgc_finish_time': finish_time,
                        'zxgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        """
            方仓4-暖通工程
        """
        insert_list = []
        block4_ntgc_list = []
        work_list = []
        start_extracting = False
        block4_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.6':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_ntgc_list['responsible_person'] is None or block4_ntgc_list['responsible_person'] == 'Js_xxx':
                    block4_ntgc_responsible_person = item_tube
                else:
                    block4_ntgc_responsible_person = block4_ntgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.7':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        """
            方仓4-消防工程
        """
        insert_list = []
        block4_xfgc_list = []
        work_list = []
        start_extracting = False
        block4_xfgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.4.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_xfgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_xfgc_list['responsible_person'] is None or block4_xfgc_list['responsible_person'] == 'Js_xxx':
                    block4_xfgc_responsible_person = item_tube
                else:
                    block4_xfgc_responsible_person = block4_xfgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.5':
                break
            elif start_extracting:
                if serial_number == '3.4.6':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'xfgc_work_content': task_name,
                        'xfgc_start_time': start_time,
                        'xfgc_finish_time': finish_time,
                        'xfgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        """
            方仓4-设备上电调试
        """
        insert_list = []
        block4_sdts_list = []
        work_list = []
        start_extracting = False
        block4_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.5:
                break
            if serial_number == '3.4.7':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                block4_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if block4_sdts_list['responsible_person'] is None or block4_sdts_list['responsible_person'] == 'Js_xxx':
                    block4_sbsdts_responsible_person = item_tube
                else:
                    block4_sbsdts_responsible_person = block4_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("it_block4", insert_list)
        tb_db.commit()
        variables = {
            'block4_qdgc_list': block4_qdgc_list,
            'block4_rdgc_list': block4_rdgc_list,
            'block4_fckj_list': block4_fckj_list,
            'block4_zxgc_list': block4_zxgc_list,
            'block4_ntgc_list': block4_ntgc_list,
            'block4_xfgc_list': block4_xfgc_list,
            'block4_sdts_list': block4_sdts_list,
            'block4_qdgc_responsible_person': block4_qdgc_responsible_person,
            'block4_rdgc_responsible_person': block4_rdgc_responsible_person,
            'block4_fckj_responsible_person': block4_fckj_responsible_person,
            'block4_zxgc_responsible_person': block4_zxgc_responsible_person,
            'block4_ntgc_responsible_person': block4_ntgc_responsible_person,
            'block4_xfgc_responsible_person': block4_xfgc_responsible_person,
            'block4_sbsdts_responsible_person': block4_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def cfhscl_itemizedplan(self, cfscl_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载柴发和水处理区域分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = cfscl_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel柴发和水处理区域解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            柴发和水处理区域-柴发方仓安装
        """
        insert_list = []
        cfscl_cffc_list = []
        work_list = []
        start_extracting = False
        cfscl_cffc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.6.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                cfscl_cffc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if cfscl_cffc_list['responsible_person'] is None or cfscl_cffc_list['responsible_person'] == 'Js_xxx':
                    cfscl_cffc_responsible_person = item_tube
                else:
                    cfscl_cffc_responsible_person = cfscl_cffc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.6.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'cffcaz_work_content': task_name,
                        'cffcaz_start_time': start_time,
                        'cffcaz_finish_time': finish_time,
                        'cffcaz_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("firewoodhair_water_data", insert_list)
        tb_db.commit()

        """
            柴发和水处理区域-水处理
        """
        insert_list = []
        cfscl_scl_list = []
        work_list = []
        start_extracting = False
        cfscl_scl_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.6.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                cfscl_scl_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if cfscl_scl_list['responsible_person'] is None or cfscl_scl_list['responsible_person'] == 'Js_xxx':
                    cfscl_scl_responsible_person = item_tube
                else:
                    cfscl_scl_responsible_person = cfscl_scl_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.6.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'scl_work_content': task_name,
                        'scl_start_time': start_time,
                        'scl_finish_time': finish_time,
                        'scl_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("firewoodhair_water_data", insert_list)
        tb_db.commit()
        """
            柴发和水处理区域-柴发油路系统
        """
        insert_list = []
        cfscl_cfly_list = []
        work_list = []
        start_extracting = False
        cfscl_cfly_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.6.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                cfscl_cfly_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if cfscl_cfly_list['responsible_person'] is None or cfscl_cfly_list['responsible_person'] == 'Js_xxx':
                    cfscl_cfly_responsible_person = item_tube
                else:
                    cfscl_cfly_responsible_person = cfscl_cfly_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.6.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ylxt_work_content': task_name,
                        'ylxt_start_time': start_time,
                        'ylxt_finish_time': finish_time,
                        'ylxt_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("firewoodhair_water_data", insert_list)
        tb_db.commit()
        """
           柴发和水处理区域-设备上电调试
        """
        insert_list = []
        cfscl_sdts_list = []
        work_list = []
        start_extracting = False
        cfscl_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.7:
                break
            if serial_number == '3.6.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                cfscl_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if cfscl_sdts_list['responsible_person'] is None or cfscl_sdts_list['responsible_person'] == 'Js_xxx':
                    cfscl_sbsdts_responsible_person = item_tube
                else:
                    cfscl_sbsdts_responsible_person = cfscl_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("firewoodhair_water_data", insert_list)
        tb_db.commit()
        variables = {
            'cfscl_cffc_list': cfscl_cffc_list,
            'cfscl_scl_list': cfscl_scl_list,
            'cfscl_cfly_list': cfscl_cfly_list,
            'cfscl_sdts_list': cfscl_sdts_list,
            'cfscl_cffc_responsible_person': cfscl_cffc_responsible_person,
            'cfscl_scl_responsible_person': cfscl_scl_responsible_person,
            'cfscl_cfly_responsible_person': cfscl_cfly_responsible_person,
            'cfscl_sbsdts_responsible_person': cfscl_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def ahu_itemizedplan(self, ahu_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载AHU分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = ahu_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excelAHU解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            AHU-电气工程
        """
        insert_list = []
        ahu_dqgc_list = []
        work_list = []
        start_extracting = False
        ahu_dqgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.7.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                ahu_dqgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_dqgc_list['responsible_person'] is None or ahu_dqgc_list['responsible_person'] == 'Js_xxx':
                    ahu_dqgc_responsible_person = item_tube
                else:
                    ahu_dqgc_responsible_person = ahu_dqgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.7.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("ahu_data", insert_list)
        tb_db.commit()

        """
            AHU-暖通工程 
        """
        insert_list = []
        ahu_ntgc_list = []
        work_list = []
        start_extracting = False
        ahu_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.7.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                ahu_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_ntgc_list['responsible_person'] is None or ahu_ntgc_list['responsible_person'] == 'Js_xxx':
                    ahu_ntgc_responsible_person = item_tube
                else:
                    ahu_ntgc_responsible_person = ahu_ntgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.7.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("ahu_data", insert_list)
        tb_db.commit()
        """
            AHU-装修(结构工程)
        """
        insert_list = []
        ahu_zxgc_list = []
        work_list = []
        start_extracting = False
        ahu_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.7.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                ahu_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_zxgc_list['responsible_person'] is None or ahu_zxgc_list['responsible_person'] == 'Js_xxx':
                    ahu_zxgc_responsible_person = item_tube
                else:
                    ahu_zxgc_responsible_person = ahu_zxgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.7.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zx_work_content': task_name,
                        'zx_start_time': start_time,
                        'zx_finish_time': finish_time,
                        'zx_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("ahu_data", insert_list)
        tb_db.commit()
        """
            AHU-弱电工程
        """
        insert_list = []
        ahu_rdgc_list = []
        work_list = []
        start_extracting = False
        ahu_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.7.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                ahu_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_rdgc_list['responsible_person'] is None or ahu_rdgc_list['responsible_person'] == 'Js_xxx':
                    ahu_rdgc_responsible_person = item_tube
                else:
                    ahu_rdgc_responsible_person = ahu_rdgc_list['responsible_person']
                start_extracting = True
            elif serial_number == '3.8':
                break
            elif start_extracting:
                if serial_number == '3.7.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdgc_work_content': task_name,
                        'rdgc_start_time': start_time,
                        'rdgc_finish_time': finish_time,
                        'rdgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("ahu_data", insert_list)
        tb_db.commit()
        """
            AHU-设备上电调试
        """
        insert_list = []
        ahu_sdts_list = []
        work_list = []
        start_extracting = False
        ahu_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 3.8:
                break
            if serial_number == '3.7.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                ahu_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if ahu_sdts_list['responsible_person'] is None or ahu_sdts_list['responsible_person'] == 'Js_xxx':
                    ahu_sbsdts_responsible_person = item_tube
                else:
                    ahu_sbsdts_responsible_person = ahu_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("ahu_data", insert_list)
        tb_db.commit()
        variables = {
            'ahu_dqgc_list': ahu_dqgc_list,
            'ahu_ntgc_list': ahu_ntgc_list,
            'ahu_zxgc_list': ahu_zxgc_list,
            'ahu_rdgc_list': ahu_rdgc_list,
            'ahu_sdts_list': ahu_sdts_list,
            'ahu_dqgc_responsible_person': ahu_dqgc_responsible_person,
            'ahu_ntgc_responsible_person': ahu_ntgc_responsible_person,
            'ahu_zxgc_responsible_person': ahu_zxgc_responsible_person,
            'ahu_rdgc_responsible_person': ahu_rdgc_responsible_person,
            'ahu_sbsdts_responsible_person': ahu_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def bg_itemizedplan(self, bg_sub_plan):
        global plan_start_time, plan_finish_time, responsible_person, tasks_name

        project_name = self.ctx.variables.get("project_name")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        item_tube = account_dict.get('项管-项目经理')

        """
            cos下载办公区域分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        url = bg_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel办公区域解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        """
            办公区域-电气工程
        """
        insert_list = []
        bg_dqgc_list = []
        work_list = []
        start_extracting = False
        bg_dqgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.8.3':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_dqgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_dqgc_list['responsible_person'] is None or bg_dqgc_list['responsible_person'] == 'Js_xxx':
                    bg_dqgc_responsible_person = item_tube
                else:
                    bg_dqgc_responsible_person = bg_dqgc_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                if serial_number == '3.8.4':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'dqgc_work_content': task_name,
                        'dqgc_start_time': start_time,
                        'dqgc_finish_time': finish_time,
                        'dqgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()

        """
            办公区域-暖通工程 
        """
        insert_list = []
        bg_ntgc_list = []
        work_list = []
        start_extracting = False
        bg_ntgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.8.2':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_ntgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_ntgc_list['responsible_person'] is None or bg_ntgc_list['responsible_person'] == 'Js_xxx':
                    bg_ntgc_responsible_person = item_tube
                else:
                    bg_ntgc_responsible_person = bg_ntgc_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                if serial_number == '3.8.3':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'ntgc_work_content': task_name,
                        'ntgc_start_time': start_time,
                        'ntgc_finish_time': finish_time,
                        'ntgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()
        """
            办公区域-装修(结构工程)
        """
        insert_list = []
        bg_zxgc_list = []
        work_list = []
        start_extracting = False
        bg_zxgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.8.1':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_zxgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_zxgc_list['responsible_person'] is None or bg_zxgc_list['responsible_person'] == 'Js_xxx':
                    bg_zxgc_responsible_person = item_tube
                else:
                    bg_zxgc_responsible_person = bg_zxgc_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                if serial_number == '3.8.2':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'zx_work_content': task_name,
                        'zx_start_time': start_time,
                        'zx_finish_time': finish_time,
                        'zx_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()
        """
            办公区域-弱电工程
        """
        insert_list = []
        bg_rdgc_list = []
        work_list = []
        start_extracting = False
        bg_rdgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.8.4':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_rdgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_rdgc_list['responsible_person'] is None or bg_rdgc_list['responsible_person'] == 'Js_xxx':
                    bg_rdgc_responsible_person = item_tube
                else:
                    bg_rdgc_responsible_person = bg_rdgc_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                if serial_number == '3.8.5':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'rdgc_work_content': task_name,
                        'rdgc_start_time': start_time,
                        'rdgc_finish_time': finish_time,
                        'rdgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()
        """
            办公区域-消防工程
        """
        insert_list = []
        bg_xfgc_list = []
        work_list = []
        start_extracting = False
        bg_xfgc_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == '3.8.5':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_xfgc_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_xfgc_list['responsible_person'] is None or bg_xfgc_list['responsible_person'] == 'Js_xxx':
                    bg_xfgc_responsible_person = item_tube
                else:
                    bg_xfgc_responsible_person = bg_xfgc_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                if serial_number == '3.8.6':
                    start_extracting = False
                else:
                    task_name = row['工作内容']
                    start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['开始时间（年/月/日）']) else None
                    finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                        row['完成时间（年/月/日）']) else None
                    construction_time = row['工期(日历日）']
                    work_list.append({
                        'work_content': task_name,
                        'start_time': start_time,
                        'finish_time': finish_time,
                        'construction_time': construction_time
                    })
                    insert_list.append({
                        'task_name': tasks_name,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'responsible_person': responsible_person,

                        'xfgc_work_content': task_name,
                        'xfgc_start_time': start_time,
                        'xfgc_finish_time': finish_time,
                        'xfgc_construction_time': construction_time,
                        'dcops_ticket_id': ticket_id
                    })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()
        """
            办公区域-设备上电调试
        """
        insert_list = []
        bg_sdts_list = []
        work_list = []
        start_extracting = False
        bg_sbsdts_responsible_person = ''
        for _, row in df.iterrows():
            serial_number = row['序号']
            if serial_number == 4:
                break
            if serial_number == '3.8.6':
                tasks_name = row['工作内容']
                plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                work_list = []
                bg_sdts_list = {
                    'start_time': plan_start_time,
                    'finish_time': plan_finish_time,
                    'responsible_person': responsible_person,
                    'work_list': work_list,
                }
                # 判断从列表里面提取出的责任人是否是空或者是否是模板里面的格式
                # 如果是，就将责任人命名为youngshi，如果里面有数据并且不是模板里面的格式，那么就提取并赋值
                if bg_sdts_list['responsible_person'] is None or bg_sdts_list['responsible_person'] == 'Js_xxx':
                    bg_sbsdts_responsible_person = item_tube
                else:
                    bg_sbsdts_responsible_person = bg_sdts_list['responsible_person']
                start_extracting = True
            elif start_extracting:
                task_name = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                construction_time = row['工期(日历日）']
                work_list.append({
                    'work_content': task_name,
                    'start_time': start_time,
                    'finish_time': finish_time,
                    'construction_time': construction_time
                })
                insert_list.append({
                    'task_name': tasks_name,
                    'plan_start_time': plan_start_time,
                    'plan_finish_time': plan_finish_time,
                    'responsible_person': responsible_person,

                    'sdts_work_content': task_name,
                    'sdts_start_time': start_time,
                    'sdts_finish_time': finish_time,
                    'sdts_construction_time': construction_time,
                    'dcops_ticket_id': ticket_id
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("administrative_data", insert_list)
        tb_db.commit()
        variables = {
            'bg_dqgc_list': bg_dqgc_list,
            'bg_ntgc_list': bg_ntgc_list,
            'bg_zxgc_list': bg_zxgc_list,
            'bg_rdgc_list': bg_rdgc_list,
            'bg_xfgc_list': bg_xfgc_list,
            'bg_sdts_list': bg_sdts_list,
            'bg_dqgc_responsible_person': bg_dqgc_responsible_person,
            'bg_ntgc_responsible_person': bg_ntgc_responsible_person,
            'bg_zxgc_responsible_person': bg_zxgc_responsible_person,
            'bg_rdgc_responsible_person': bg_rdgc_responsible_person,
            'bg_xfgc_responsible_person': bg_xfgc_responsible_person,
            'bg_sbsdts_responsible_person': bg_sbsdts_responsible_person
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class Corridor(AjaxTodoBase):
    """
        电力走廊
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(Corridor, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def check_itemized_plan_is_upload(self, dlzl_sub_plan: list):
        try:
            datas, bools = dlzl_sub_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        return datas, bools

    def verify_whether_the_upload_is_correct(self, dlzl_sub_plan: list):
        url = dlzl_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel电力方仓解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        for _, row in df.iterrows():
            serial_number = row['序号']
            task_name = row['工作内容']
            if serial_number == 3.5 and task_name == '电力走廊':
                return {"code": 0, "msg": "分项计划上传正确"}, False
        return {"code": -1, "msg": "分项计划上传错误，请重新上传"}, True

    def start(self):
        pass

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        dlzl_sub_plan = process_data.get('dlzl_sub_plan', None)
        datas, bools = self.check_itemized_plan_is_upload(dlzl_sub_plan)
        sub_plan_data, sub_plan_bool = self.verify_whether_the_upload_is_correct(dlzl_sub_plan)
        if bools:
            return datas
        if sub_plan_bool:
            return sub_plan_data
        update_data = {}
        if dlzl_sub_plan and len(dlzl_sub_plan) > 0 and 'response' in dlzl_sub_plan[0]:
            update_data = {
                'dlzl_sub_plan': dlzl_sub_plan[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'dlzl_sub_plan': dlzl_sub_plan,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SquareBinInstallation(AjaxTodoBase):
    """
        电力方仓安装
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(SquareBinInstallation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        dlzl_dlfc_list = process_data.get("dlzl_dlfc_list", {})
        work_list = dlzl_dlfc_list.get("work_list", [])
        dlfc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')
        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            dlfc_dict = {
                'work_content': '电力方仓安装',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'dlfc_dict': dlfc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'fcaz_actual_start_time': actual_start_time,
                'fcaz_actual_finish_time': actual_finish_time,
                'fcaz_actual_construction_time': actual_construction_time,
                'fcaz_schedule_deviation': schedule_deviation,
                'fcaz_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '电力方仓安装',
                'fcaz_work_content': work_content,
            }
            tb_db.update("power_corridor_data", update2_data, conditions2)
        complete_report = dlzl_dlfc_list.get('complete_report', None)
        complete_photo = dlzl_dlfc_list.get('complete_photo', None)
        complete_report_review_report = dlzl_dlfc_list.get('complete_report_review_report', None)

        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3

        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'fcaz_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'fcaz_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'fcaz_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"]
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '电力方仓安装'
        }
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'dlzl_dlfc_list': dlzl_dlfc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'dlfc_dict': dlfc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ExhaustSystem(AjaxTodoBase):
    """
        排风系统
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(ExhaustSystem, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        dlzl_bfxt_list = process_data.get("dlzl_bfxt_list", {})
        complete_report = dlzl_bfxt_list.get('complete_report', None)
        complete_photo = dlzl_bfxt_list.get('complete_photo', None)
        complete_report_review_report = dlzl_bfxt_list.get('complete_report_review_report', None)
        work_list = dlzl_bfxt_list.get("work_list", [])

        pfxt_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')
        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            pfxt_dict = {
                'work_content': '补/排风系统',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'pfxt_dict': pfxt_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'pfxt_actual_start_time': actual_start_time,
                'pfxt_actual_finish_time': actual_finish_time,
                'pfxt_actual_construction_time': actual_construction_time,
                'pfxt_schedule_deviation': schedule_deviation,
                'pfxt_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '补/排风系统',
                'pfxt_work_content': work_content,
            }
            tb_db.update("power_corridor_data", update2_data, conditions2)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3

        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'pfxt_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'pfxt_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'pfxt_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '补/排风系统'
        }
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'dlzl_bfxt_list': dlzl_bfxt_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'pfxt_dict': pfxt_dict,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class OutsidePowerProject(AjaxTodoBase):
    """
        外市电工程
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(OutsidePowerProject, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        dlzl_wsd_list = process_data.get("dlzl_wsd_list", {})
        complete_report = dlzl_wsd_list.get('complete_report', None)
        complete_photo = dlzl_wsd_list.get('complete_photo', None)
        complete_report_review_report = dlzl_wsd_list.get('complete_report_review_report', None)
        work_list = dlzl_wsd_list.get("work_list", [])
        wsd_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')
        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            wsd_dict = {
                'work_content': '外市电工程(红线内)',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            dlfc_dict = self.ctx.variables.get('dlfc_dict')
            pfxt_dict = self.ctx.variables.get('pfxt_dict')
            dlzl_skip_list = []
            if dlfc_dict:
                dlzl_skip_list.append(dlfc_dict)

            if pfxt_dict:
                dlzl_skip_list.append(pfxt_dict)

            if wsd_dict:
                dlzl_skip_list.append(wsd_dict)

            variables = {
                'wsd_dict': wsd_dict,
                'dlzl_skip_list': dlzl_skip_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            dlfc_dict = self.ctx.variables.get('dlfc_dict')
            pfxt_dict = self.ctx.variables.get('pfxt_dict')
            dlzl_skip_list = []
            if dlfc_dict:
                dlzl_skip_list.append(dlfc_dict)

            if pfxt_dict:
                dlzl_skip_list.append(pfxt_dict)

            if wsd_dict:
                dlzl_skip_list.append(wsd_dict)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'wsdgc_actual_start_time': actual_start_time,
                'wsdgc_actual_finish_time': actual_finish_time,
                'wsdgc_actual_construction_time': actual_construction_time,
                'wsdgc_schedule_deviation': schedule_deviation,
                'wsdgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '外市电工程（红线内）',
                'wsdgc_work_content': work_content,
            }
            tb_db.update("power_corridor_data", update2_data, conditions2)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'wsdgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'wsdgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'wsdgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '外市电工程（红线内）'
        }
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'dlzl_wsd_list': dlzl_wsd_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'wsd_dict': wsd_dict,
            'dlzl_skip_list': dlzl_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlPMApproval(AjaxTodoBase):
    """
        电力走廊PM审批
    """

    def __init__(self):
        super(DlzlPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dlzl_PM_approval = process_data.get('dlzl_pm_approval')
        dlzl_PM_remark = process_data.get('dlzl_PM_remark')
        if dlzl_PM_approval == '驳回':
            dlzl_skip_list = []
            if not dlzl_PM_remark:
                return {'code': -1, 'msg': '驳回时，未填写备注'}

        variables = {
            'dlzl_PM_approval': dlzl_PM_approval,
            'dlzl_PM_remark': dlzl_PM_remark,
            'dlzl_skip_list': dlzl_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TestInspection(AjaxTodoBase):
    """
        试验检验
    """

    def __init__(self):
        super(TestInspection, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def check_inspection_report_is_upload(self, inspection_report: list):
        try:
            datas, bools = inspection_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "检验报告未上传完成，请等待检验报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "检验报告未上传完成，请等待检验报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        inspection_report = process_data.get('inspection_report', None)
        datas, bools = self.check_inspection_report_is_upload(inspection_report)
        if bools:
            return datas
        update_data = {}
        if inspection_report and len(inspection_report) > 0 and 'response' in inspection_report[0]:
            update_data = {
                'inspection_report': inspection_report[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'inspection_report': inspection_report,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PowerTransmissionReport(AjaxTodoBase):
    """
        送电
    """

    def __init__(self):
        super(PowerTransmissionReport, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def check_send_electricity_report_is_upload(self, send_electricity_report: list):
        try:
            datas, bools = send_electricity_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "送电报告未上传完成，请等待送电报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "送电报告未上传完成，请等待送电报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        send_electricity_report = process_data.get('send_electricity_report', None)
        datas, bools = self.check_send_electricity_report_is_upload(send_electricity_report)
        if bools:
            return datas
        update_data = {}
        if send_electricity_report and len(send_electricity_report) > 0 and 'response' in send_electricity_report[0]:
            update_data = {
                'send_electricity_report': send_electricity_report[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'send_electricity_report': send_electricity_report,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DevicePowerDebuggingPowerCorridor(AjaxTodoBase):
    """
        设备上电测试——电气走廊
    """

    def __init__(self):
        super(DevicePowerDebuggingPowerCorridor, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def check_power_debug_is_upload(self, power_debug: list):
        try:
            datas, bools = power_debug[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        dlzl_sdts_list = process_data.get('dlzl_sdts_list')
        ticket_id = self.ctx.variables.get("ticket_id")
        work_list = dlzl_sdts_list.get("work_list", [])
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'sdts_actual_start_time': actual_start_time,
                'sdts_actual_finish_time': actual_finish_time,
                'sdts_actual_construction_time': actual_construction_time,
                'sdts_schedule_deviation': schedule_deviation,
                'sdts_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '设备上电调试',
                'sdts_work_content': work_content,
            }
            tb_db.update("power_corridor_data", update2_data, conditions2)
        power_debug = dlzl_sdts_list.get('power_debug', None)
        datas, bools = self.check_power_debug_is_upload(power_debug)
        if bools:
            return datas
        update_data = {}
        if power_debug and len(power_debug) > 0 and 'response' in power_debug[0]:
            update_data = {
                'dlzl_power_debug': power_debug[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '设备上电调试'
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("power_corridor_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'dlzl_sdts_list': dlzl_sdts_list,
            'dlzl_power_debug': power_debug,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class UpdateStatus(object):
    def __init__(self):
        super(UpdateStatus, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def change(self):
        ticket_id = self.ctx.variables.get("ticket_id")

        update_data1 = {
            'status': 1
        }
        update_data2 = {
            'status': 1
        }

        conditions = {
            'ticketid': ticket_id
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("week_create_info", update_data1, conditions)
        tb_db.update("daily_create_info", update_data2, conditions)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})
