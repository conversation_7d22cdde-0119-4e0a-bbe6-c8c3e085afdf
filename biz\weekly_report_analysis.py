import pandas as pd
import uuid
from biz.construction_process.cos_lib import COSLib


def parse_and_store_data(url, sheet_name):
    relative_url = url.split("cosfile")[1]
    new_file_name = str(uuid.uuid4()) + '.xlsx'
    COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)

    data = []
    if sheet_name == '周报简报':
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=sheet_name, parse_dates=False)
        df = df.replace({pd.np.nan: None})

        for _, row in df.iterrows():
            table_of_contents = row['目录']
            details = row['具体内容']
            if any([table_of_contents, details]):
                data.append({
                    'contents': table_of_contents,
                    'details': details
                })

    return data


# # 调用函数并传入指定的 URL 和 sheet_name
# url = "https://test.otob.dcops.qq.com/relay/cosfile/relay/20240612/腾讯贵阳七星B2-1项目周报（5月23日-5月29日）" \
#       "_e7852bd147ac826541ae54cfb580e1b6_14b18fd4389e390564eec0b257e6b46e.xlsx"
# sheet_name = '周报简报'
#
# result = parse_and_store_data(url, sheet_name)
# print(result)
