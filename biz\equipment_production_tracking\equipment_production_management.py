import time
from datetime import datetime
import re

from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate, WorkflowContinue
from iBroker.lib import mysql, curl

from biz.project_interface.organizational_structure_membership_details import refresh_corresponding_account_of_role


def whether_is_rental_project(project_name):
    """
    判断是否为合建项目
    """
    db = mysql.new_mysql_instance("tbconstruct")
    sql = (
            "SELECT construction_mode FROM risk_early_warning_data WHERE project_name='%s'"
            % project_name
    )
    construction_mode = db.get_row(sql)
    if not construction_mode:
        raise Exception("项目报建流程未找到对应项目的建设模式")
    if construction_mode.get("construction_mode") == "自建":
        is_hire = False
    else:
        is_hire = True
    return is_hire


class EquipmentProductionPlan(AjaxTodoBase):
    """
    设备生产跟踪：
        生产计划上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = process_data.get("campus")
        project_name = process_data.get("project_name")
        equipment_type = process_data.get("equipment_type")
        device_name = process_data.get("device_name")
        supplier = process_data.get("supplier")
        testing_plan_list = process_data.get("testing_plan_list")
        po = []
        po_list = []
        works_list = []
        po_material_info = []
        # if equipment_type == "甲供":
        #     # 获取需求单号
        #     db = mysql.new_mysql_instance("tbconstruct")
        #     sql = (
        #         "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
        #         "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
        #         f"WHERE rewd.project_name = '{project_name}' "
        #     )
        #     code_id_list = db.query(sql)
        #     if code_id_list:
        #         material_id_list = []
        #         # 通过需求单号查询po信息
        #         for i in code_id_list:
        #             code_id = i.get("idcrmOrderCode")
        #             # # 用需求单号获取po信息
        #             # po_info = gnetops.request(action="Tbconstruct",
        #             #                           method="QueryPoinfoByRequirement",
        #             #                           ext_data={
        #             #                               "RequirementId": code_id
        #             #                           }
        #             #                           )
        #             resp = curl.get(
        #                 title="Dcops获取po信息",
        #                 system_name="Dcops",
        #                 url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
        #                 postdata="",
        #                 append_header={"Content-Type": "application/json"})
        #             po_info = resp.json()
        #             if po_info:
        #                 po_info_list = []
        #                 # 所有的物料id
        #                 for data in po_info.get("data"):
        #                     id = data.get("orderItemId")
        #                     data["orderItemId"] = str(id)
        #                     po_info_list.append(data)
        #                 po_material_info += po_info_list
        #         # 提取所有的物料id
        #         for j in po_material_info:
        #             material_id = j.get("materialId")
        #             if material_id not in material_id_list:
        #                 material_id_list.append(material_id)
        #         self.workflow_detail.add_kv_table("物料id", {"message": material_id_list})
        #
        #         # 通过物料id获取品名信息
        #         material_list = gnetops.request(action="Tbconstruct",
        #                                         method="ObtainMaterialInfoById",
        #                                         ext_data={
        #                                             "MaterialCodeId": material_id_list
        #                                         }
        #                                         )
        #
        #         if material_list:
        #             material_info_list = material_list.get("materialList")
        #             # 构造品名、物料编码、物料id
        #             data = []
        #             for material in material_info_list:
        #                 data.append(
        #                     {
        #                         # 物料编码
        #                         "material_code": material.get("materialCode"),
        #                         # 物料id
        #                         "material_id": material.get("materialQueryDTO").get(
        #                             "materialId"
        #                         ),
        #                         # 物料名称
        #                         "material_name": material.get("materialQueryDTO").get(
        #                             "materialName"
        #                         ),
        #                         # 品名
        #                         "material_category_name": material.get(
        #                             "materialQueryDTO"
        #                         ).get("materialCategoryName"),
        #                         # 品名编码
        #                         "material_category_code": material.get(
        #                             "materialQueryDTO"
        #                         ).get("materialCategoryCode"),
        #                     }
        #                 )
        #             self.workflow_detail.add_kv_table("构造的数据", {"message": data})
        #             # 查询品名合设备名称关系
        #             db = mysql.new_mysql_instance("tbconstruct")
        #             sql = (
        #                 "SELECT material_category_name FROM equipment_category_mapping_relationship "
        #                 f"WHERE device_name = '{device_name}' "
        #             )
        #             material_category_name_list = db.query(sql)
        #             # 对应品名后的物料信息
        #
        #             if material_category_name_list:
        #                 # 一个设备所对应的所以品名
        #                 material_category_name = []
        #                 for category in material_category_name_list:
        #                     material_category_name.append(
        #                         category.get("material_category_name")
        #                     )
        #                 for d in data:
        #                     # 获取与设备对应的品名信息
        #                     if (
        #                             d.get("material_category_name")
        #                             in material_category_name
        #                     ):
        #                         works_list.append(d)
        #
        #                 if works_list:
        #                     for works in works_list:
        #                         works_id = works.get("material_id")
        #                         for item in po_material_info:
        #                             if works_id == item.get("materialId"):
        #                                 po_id = item.get("orderCode")
        #                                 if po_id not in po:
        #                                     po.append(po_id)
        #                                     po_list.append(
        #                                         {
        #                                             "po": po_id,
        #                                             "material_category_name": works.get(
        #                                                 "material_category_name"
        #                                             ),
        #                                         }
        #                                     )
        #                                 works["PO_id"] = po_id
        #                         works["ticket_id"] = ticket_id
        #                         works["device_name"] = device_name
        #                         works["equipment_type"] = equipment_type
        #                         works["project_name"] = project_name
        #
        #                     tb_db = mysql.new_mysql_instance("tbconstruct")
        #                     tb_db.begin()
        #                     tb_db.insert_batch(
        #                         "equipment_production_racking_po", works_list
        #                     )
        #                     tb_db.commit()
        #                 else:
        #                     self.workflow_detail.add_kv_table(
        #                         "获取po信息", {"message": "po获取失败"}
        #                     )
        #             else:
        #                 self.workflow_detail.add_kv_table(
        #                     "获取po信息", {"message": "此物料id无法获取品名信息"}
        #                 )
        #         else:
        #             self.workflow_detail.add_kv_table(
        #                 "获取po信息", {"message": "此物料id无法获取品名信息"}
        #             )
        #     else:
        #         self.workflow_detail.add_kv_table(
        #             "获取po信息", {"message": "获取不到需求单号"}
        #         )
        # 周报模板
        template = []
        # 生产准备
        production_preparation_list = []
        # 备料
        prepare_materials_list = []
        # 生产
        production_list = []
        # 出厂检验
        factory_inspection_list = []
        # 计划发货时间
        fh_list = []
        # 计划到货时间
        planned_arrival_time = ''
        if testing_plan_list:
            for i in testing_plan_list:
                i["ticket_id"] = ticket_id
                i["project_name"] = project_name
                production_preparation_list.append(
                    {
                        "plan_time": i.get("production_preparation"),
                        "PO_id": ";".join(po),
                    }
                )
                prepare_materials_list.append({"plan_time": i.get("prepare_materials")})
                production_list.append({"plan_time": i.get("production")})
                factory_inspection_list.append(
                    {"plan_time": i.get("factory_inspection")}
                )
                fh_list.append({"scheduled_delivery_time": i.get("shipping_application")})
                planned_arrival_time = i.get("receiving_sign")

                template.append(
                    {
                        "stage": "生产准备",
                        "plan_time": i.get("production_preparation"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )
                template.append(
                    {
                        "stage": "备料",
                        "plan_time": i.get("prepare_materials"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )
                template.append(
                    {
                        "stage": "生产",
                        "plan_time": i.get("production"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )
                template.append(
                    {
                        "stage": "出厂检验",
                        "plan_time": i.get("factory_inspection"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )
                template.append(
                    {
                        "stage": "发货申请",
                        "plan_time": i.get("shipping_application"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )
                template.append(
                    {
                        "stage": "到货签收",
                        "plan_time": i.get("receiving_sign"),
                        "schedule": "正常",
                        "progress": "0%",
                    }
                )

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("equipment_production_racking_plan", testing_plan_list)
            tb_db.commit()
        insert_data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "campus": campus,
            "equipment_type": equipment_type,
            "device_name": device_name,
            "supplier": supplier,
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_production_racking_process", insert_data)
        tb_db.commit()
        variables = {
            "campus": campus,
            "project_name": project_name,
            "equipment_type": equipment_type,
            "device_name": device_name,
            "supplier": supplier,
            "testing_plan_list": testing_plan_list,
            "production_preparation_list": production_preparation_list,
            "prepare_materials_list": prepare_materials_list,
            "production_list": production_list,
            "factory_inspection_list": factory_inspection_list,
            "planned_arrival_time": planned_arrival_time,
            "fh_list": fh_list,
            "po": po,
            "po_list": po_list,
            "works_list": works_list,
            "po_material_info": po_material_info,
            "report": template,
            "template": template,
            "weekly_list": []
        }

        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        variables.update({"xg": xg})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentProductionPreparation(AjaxTodoBase):
    """
    设备生产跟踪：
        生产准备
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        campus = self.ctx.variables.get("campus")
        equipment_type = self.ctx.variables.get("equipment_type")
        device_name = self.ctx.variables.get("device_name")
        supplier = self.ctx.variables.get("supplier")
        po_list = self.ctx.variables.get("po_list")
        po = self.ctx.variables.get("po")
        production_preparation_list = process_data.get("production_preparation_list")

        if production_preparation_list:
            for i in production_preparation_list:
                upload_url_str = str(i.get("upload_info"))
                po_id = i.get("PO_id")
                if po_id:
                    po_data_list = po_id.split(";")
                    for data in po_data_list:
                        if data not in po:
                            po.append(data)
                            po_list.append({
                                "po": data
                            })
                if not upload_url_str:
                    # upload_url_list, upload_url_str = url_splicing(upload_info)
                    # if upload_url_list == 0 and upload_url_str == 0:
                    return {"code": -1, "msg": "未上传所需文件"}
                i["ticket_id"] = ticket_id
                i["project_name"] = project_name
                i["campus"] = campus
                i["equipment_type"] = equipment_type
                i["device_name"] = device_name
                i["supplier"] = supplier
                i["type"] = "生产准备"
                i["upload_url"] = upload_url_str

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch(
                "equipment_production_racking_info", production_preparation_list
            )
            tb_db.commit()

        variables = {
            "production_preparation_list": production_preparation_list,
            "po_list": po_list,
            "po": po
        }

        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        variables.update({"xg": xg})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentPrepareMaterials(AjaxTodoBase):
    """
    设备生产跟踪：
        备料
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        campus = self.ctx.variables.get("campus")
        equipment_type = self.ctx.variables.get("equipment_type")
        device_name = self.ctx.variables.get("device_name")
        supplier = self.ctx.variables.get("supplier")
        prepare_materials_list = process_data.get("prepare_materials_list")
        if prepare_materials_list:
            for i in prepare_materials_list:
                upload_url_str = str(i.get("upload_info"))
                if not upload_url_str:
                    # upload_url_list, upload_url_str = url_splicing(upload_info)
                    # if upload_url_list == 0 and upload_url_str == 0:
                    return {"code": -1, "msg": "未上传所需文件"}
                i["ticket_id"] = ticket_id
                i["project_name"] = project_name
                i["campus"] = campus
                i["equipment_type"] = equipment_type
                i["device_name"] = device_name
                i["supplier"] = supplier
                i["type"] = "备料"
                i["upload_url"] = upload_url_str

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch(
                "equipment_production_racking_info", prepare_materials_list
            )
            tb_db.commit()
        variables = {"prepare_materials_list": prepare_materials_list}

        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        variables.update({"xg": xg})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentProduction(AjaxTodoBase):
    """
    设备生产跟踪：
        生产
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        campus = self.ctx.variables.get("campus")
        equipment_type = self.ctx.variables.get("equipment_type")
        device_name = self.ctx.variables.get("device_name")
        supplier = self.ctx.variables.get("supplier")
        production_list = process_data.get("production_list")
        if production_list:
            for i in production_list:
                upload_url_str = str(i.get("upload_info"))
                if not upload_url_str:
                    # upload_url_list, upload_url_str = url_splicing(upload_info)
                    # if upload_url_list == 0 and upload_url_str == 0:
                    return {"code": -1, "msg": "未上传所需文件"}
                i["ticket_id"] = ticket_id
                i["project_name"] = project_name
                i["campus"] = campus
                i["equipment_type"] = equipment_type
                i["device_name"] = device_name
                i["supplier"] = supplier
                i["type"] = "生产"
                i["upload_url"] = upload_url_str

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("equipment_production_racking_info", production_list)
            tb_db.commit()
        variables = {"production_list": production_list}

        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        variables.update({"xg": xg})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentFactoryInspection(AjaxTodoBase):
    """
    设备生产跟踪：
        出厂检验
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        campus = self.ctx.variables.get("campus")
        equipment_type = self.ctx.variables.get("equipment_type")
        device_name = self.ctx.variables.get("device_name")
        supplier = self.ctx.variables.get("supplier")
        factory_inspection_list = process_data.get("factory_inspection_list")
        if factory_inspection_list:
            for i in factory_inspection_list:
                upload_url_str = str(i.get("upload_info"))
                if not upload_url_str:
                    # upload_url_list, upload_url_str = url_splicing(upload_info)
                    # if upload_url_list == 0 and upload_url_str == 0:
                    return {"code": -1, "msg": "未上传所需文件"}
                i["ticket_id"] = ticket_id
                i["project_name"] = project_name
                i["campus"] = campus
                i["equipment_type"] = equipment_type
                i["device_name"] = device_name
                i["supplier"] = supplier
                i["type"] = "出厂检验"
                i["upload_url"] = upload_url_str

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch(
                "equipment_production_racking_info", factory_inspection_list
            )
            tb_db.commit()
        if campus in project_name:
            project = project_name.replace(campus, '', 1).strip()
        else:
            project = ''
        variables = {"factory_inspection_list": factory_inspection_list,
                     "project": project}

        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        jl = refresh_corresponding_account_of_role(self.ctx.variables.get("jl"), project_name)
        variables.update({"xg": xg,"jl":jl})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentUploadShipping(AjaxTodoBase):
    """
        设备发货
    """

    def __init__(self):
        super(EquipmentUploadShipping, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        insert_data = {}
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project_name = self.ctx.variables.get("project_name")
        fh_remark = process_data.get("fh_remark", None)
        equipment_category = process_data.get('equipment_category', None)
        supplier = process_data.get('supplier', None)
        scheduled_delivery_time = process_data.get('scheduled_delivery_time', None)
        actual_delivery_time = process_data.get('actual_delivery_time', None)
        upload_shipping_information = process_data.get('upload_shipping_information', None)
        project_name_list = project_name.split("B")
        if len(project_name_list) > 1:
            project = "B" + project_name_list[1]
        else:
            project = ''
        if upload_shipping_information and len(upload_shipping_information) > 0 and 'response' in \
                upload_shipping_information[0]:
            insert_data = {
                'upload_shipping_information': upload_shipping_information[0]["response"]["FileList"][0]["url"],
                'campus': campus,
                'project_name': project,
                'equipment_category': equipment_category,
                'supplier': supplier,
                'scheduled_delivery_time': scheduled_delivery_time,
                'actual_delivery_time': actual_delivery_time,
                'ticket_id': ticket_id
            }

        variables = {
            "upload_shipping_insert": insert_data,
            'upload_shipping_information': upload_shipping_information,
            'project': project,
            'equipment_category': equipment_category,
            'supplier': supplier,
            'scheduled_delivery_time': scheduled_delivery_time,
            'actual_delivery_time': actual_delivery_time,
            'fh_remark': fh_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentUploadReceiving(AjaxTodoBase):
    """
            设备收货
        """

    def __init__(self):
        super(EquipmentUploadReceiving, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        planned_arrival_time = process_data.get('planned_arrival_time', None)
        actual_arrival_time = process_data.get('actual_arrival_time', None)
        dh_remark = process_data.get('dh_remark', None)
        update_data = {}
        upload_receiving_information = process_data.get('upload_receiving_information', None)

        if upload_receiving_information and len(upload_receiving_information) > 0 and 'response' in \
                upload_receiving_information[0]:
            update_data = {
                'upload_receiving_information': upload_receiving_information[0]["response"]["FileList"][0]["url"],
                'planned_arrival_time': planned_arrival_time,
                'actual_arrival_time': actual_arrival_time,
            }

        variables = {
            'upload_receiving_update': update_data,
            'upload_receiving_information': upload_receiving_information,
            'planned_arrival_time': planned_arrival_time,
            'actual_arrival_time': actual_arrival_time,
            'dh_remark': dh_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentUploadShippingNew(AjaxTodoBase):
    """
    设备发货:新接口
    """

    def __init__(self):
        super(EquipmentUploadShippingNew, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project_name = self.ctx.variables.get("project_name")
        planned_arrival_time = self.ctx.variables.get("planned_arrival_time")
        fh_list = process_data.get("fh_list")
        equipment_category = process_data.get("equipment_category")
        supplier = process_data.get("supplier")
        # project_name_list = project_name.split("B")
        if campus in project_name:
            project = project_name.replace(campus, '', 1).strip()
        else:
            project = ''
        insert_list = []
        dh_list = []
        for i in fh_list:
            equipment_batch = i.get("equipment_batch", None)
            scheduled_delivery_time = i.get('scheduled_delivery_time', None)
            actual_delivery_time = i.get('actual_delivery_time', None)
            upload_shipping_information = i.get('upload_shipping_information', None)
            dh_list.append({
                "planned_arrival_time": planned_arrival_time,
                "equipment_batch": equipment_batch
            })
            if (upload_shipping_information and len(upload_shipping_information) > 0
                    and "response" in upload_shipping_information[0]):
                insert_list.append({
                    "upload_shipping_information": upload_shipping_information[0]["response"]["FileList"][0]["url"],
                    "campus": campus,
                    "project_name": project,
                    "equipment_category": equipment_category,
                    "supplier": supplier,
                    "scheduled_delivery_time": scheduled_delivery_time,
                    "actual_delivery_time": actual_delivery_time,
                    "ticket_id": ticket_id,
                    "equipment_batch": equipment_batch,
                })
        variables = {
            "project": project,
            "insert_list": insert_list,
            "fh_list": fh_list,
            "dh_list": dh_list,
            "equipment_category": equipment_category,
            "supplier": supplier,
            "upload_shipping_insert": insert_list,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class GetEquipmentProductionDeliveryData(object):
    """
    甲供设备生产发货数据获取
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_equipment_production_delivery_data(self):
        jcs = self.ctx.variables.get("jcs")
        jl = self.ctx.variables.get("jl")
        xg = self.ctx.variables.get("xg")
        PM = self.ctx.variables.get("PM")
        project_name = self.ctx.variables.get("project_name")
        equipment_category = self.ctx.variables.get("equipment_category")
        planned_arrival_time = self.ctx.variables.get("planned_arrival_time")
        campus = self.ctx.variables.get("campus")
        ticket_id = self.ctx.variables.get("ticket_id")
        if campus in project_name:
            project = project_name.replace(campus, '', 1).strip()
        else:
            project = ''
        supplier = self.ctx.variables.get("supplier")
        f_instance_id = self.ctx.variables.get("f_instance_id")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT item_number FROM risk_early_warning_data where project_name = '{project_name}'"
        query_list = db.get_all(sql)
        fh_list = self.ctx.variables.get("fh_list", [])
        fh_review_list = []
        dh_review_list = []
        reviewed_list = self.ctx.variables.get("reviewed_list", [])
        review_ticket_list = self.ctx.variables.get("review_ticket_list", [])
        all_is_reviewed = False
        sql_2 = f"SELECT device_name,jg_material_category_name FROM equipment_category_mapping_relationship"
        map_list = db.get_all(sql_2)
        category_name = ""
        for i in map_list:
            if i.get("device_name") == equipment_category:
                category_name = i.get("jg_material_category_name")
                break
        self.workflow_detail.add_kv_table("映射信息", {
            "映射信息": {"设备名称": equipment_category, "设备类别": category_name,
                         "项目编号": query_list[0].get("item_number")}})
        is_or_not = self.ctx.variables.get("is_or_not", "否")
        # 合作伙伴版本子单数据获取
        if is_or_not == "是":
            device_name = self.ctx.variables.get("device_name")
            system_type = self.ctx.variables.get("system_type")
            receiving_sign = self.ctx.variables.get("receiving_sign")
            PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
            system_id = self.ctx.variables.get("system_id")
            Facilitator = self.ctx.variables.get("Facilitator")
        fh_num = 0
        delivery_total = 0
        query_data = {}
        have_delivery_information = ""
        if query_list:
            # query_data = gnetops.request(
            #     action="Tbconstruct",
            #     method="QueryProductDeliveryInfo",
            #     ext_data={
            #         "ProjectCode": query_list[0].get("item_number")
            #     },
            #     scheme="ifob-infrastructure"
            # )
            sql3 = (
                f"SELECT batch,submit_time,review_time,file_url,category_name,delivery_num,delivery_total "
                f"FROM product_query_info WHERE project_name = '{project_name}'")
            data_info = db.get_all(sql3)
            # if query_data:
            # data_info = query_data.get("data", [])
            if data_info:
                for item in data_info:
                    fh_review_list = []
                    dh_review_list = []
                    time.sleep(1)
                    if item.get("category_name") == category_name and item.get("delivery_total") \
                            and item.get("delivery_num") and item.get("batch"):
                        have_delivery_information = "1"
                        if not item.get("delivery_total"):
                            continue
                        equipment_batch = item.get("batch")
                        if equipment_batch not in reviewed_list:
                            delivery_total = int(item.get("delivery_total"))
                            delivery_num = item.get("delivery_num")
                            fh_num += int(delivery_num)
                            fh_review_list.append({
                                "equipment_batch": equipment_batch,
                                "scheduled_delivery_time": item.get("submit_time"),
                                "actual_delivery_time": item.get("review_time"),
                                "upload_shipping_information": item.get("file_url"),
                            })
                            fh_list.append({
                                "equipment_batch": equipment_batch,
                                "scheduled_delivery_time": item.get("submit_time"),
                                "actual_delivery_time": item.get("review_time"),
                                "upload_shipping_information": item.get("file_url"),
                            })
                            dh_review_list.append({
                                "equipment_batch": equipment_batch,
                                "planned_arrival_time": planned_arrival_time
                            })
                            child_variables = {
                                "equipment_category": equipment_category,  # 设备名称
                                "campus": campus,  # 园区
                                "project": project,  # 项目名
                                "project_name": project_name,  # 项目名称
                                "supplier": supplier,  # 供应商
                                "fh_list": fh_review_list,  # 发货信息
                                "dh_list": dh_review_list,  # 到货信息
                                "f_instance_id": f_instance_id,  # 主工单流程id
                                "jcs": jcs,  # 审批人-集成商
                                "jl": jl,  # 审批人-监理
                                "xg": xg,  # 审批人-项管
                                "PM": PM,
                                "planned_arrival_time": planned_arrival_time,  # 计划到货时间
                                "have_delivery_information": have_delivery_information,  # 是否有发货信息
                                "main_ticket_id": ticket_id,  # 主工单号
                                "is_or_not": is_or_not,  # 是否走合作商版本
                                "equipment_batch": equipment_batch,  # 设备批次
                                "delivery_total": delivery_total,  # 设备总数
                                "delivery_num": delivery_num  # 设备数量
                            }
                            if is_or_not == "是":
                                child_variables.update({
                                    "device_name": device_name,
                                    "equipment_category": device_name,
                                    "system_type": system_type,
                                    "receiving_sign": receiving_sign,
                                    "Facilitator": Facilitator,
                                    "PartnerTicketId": PartnerTicketId,
                                    "system_id": system_id
                                })
                            ticket_info = gnetops.create_ticket(
                                flow_key="equipment_production_arrival_data_approval",
                                description='该工单为："' + project_name + equipment_category
                                            + '设备生产发货到货资料上传审核',
                                ticket_level=3,
                                title=project_name + equipment_category + '：设备生产发货到货资料上传审核',
                                creator="v_zongxiyu",
                                concern="v_zongxiyu",  # 关注人, 这里暂为空
                                deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                                source="",
                                custom_var=child_variables
                            )
                            insert_data = {
                                "upload_shipping_information": item.get("file_url"),
                                "campus": campus,
                                "project_name": project,
                                "equipment_category": equipment_category,
                                "supplier": supplier,
                                "scheduled_delivery_time": item.get("submit_time"),
                                "actual_delivery_time": item.get("review_time"),
                                "ticket_id": ticket_id,
                                "equipment_batch": equipment_batch,
                            }
                            db.insert("equipment_arrival_data", insert_data)
                            review_ticket_list.append(ticket_info.get("TicketId"))
                            reviewed_list.append(equipment_batch)
                if delivery_total and delivery_total == fh_num:
                    all_is_reviewed = True
        if all_is_reviewed:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    "reviewed_batch_list": reviewed_list,
                    "review_ticket_list": review_ticket_list,
                    "fh_list": fh_list,
                    "fh_num": fh_num,
                    "delivery_total": delivery_total,
                    "project": project,
                }
            ).call()
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}
        else:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    "reviewed_list": reviewed_list,
                    "review_ticket_list": review_ticket_list,
                    "fh_list": fh_list,
                    "query_list": query_list,
                    "query_data": query_data
                }
            ).call()
            return {"success": False, "data": "尚有子流程未结束"}

    def send_email_after_get_delivery_data(self):
        """
        获取到甲供发货资料发送通知邮件
        """
        jcs = self.ctx.variables.get("jcs", "")
        jl = self.ctx.variables.get("jl", "")
        xg = self.ctx.variables.get("xg", "")
        fh_list = self.ctx.variables.get("fh_list", [])
        project_name = self.ctx.variables.get("project_name", "")
        campus = self.ctx.variables.get("campus", "")
        equipment_category = self.ctx.variables.get("equipment_category", "")
        supplier = self.ctx.variables.get("supplier", "")
        email_title = f"{project_name}-{equipment_category}设备发货信息"
        email_data = (f"项目名称：{project_name}<br>"
                      f"园区：{campus}<br>"
                      f"设备名称：{equipment_category}<br>"
                      f"供应商：{supplier}<br>")
        if fh_list:
            equipment_batch = fh_list[0].get("equipment_batch")
            scheduled_delivery_time = fh_list[0].get("scheduled_delivery_time")
            actual_delivery_time = fh_list[0].get("actual_delivery_time")
            upload_shipping_information = fh_list[0].get("upload_shipping_information")
            email_data += f"发货批次：{equipment_batch}<br>"
            email_data += f"计划发货时间：{scheduled_delivery_time}<br>"
            email_data += f"实际发货时间：{actual_delivery_time}<br>"
            email_data += f"发货资料链接：{upload_shipping_information}<br>"
        sql = f"SELECT account,email FROM project_role_account WHERE project_name='{project_name}'"
        email_receiver_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        query_data = db.get_all(sql)
        for item in query_data:
            if item.get("account") in [jcs, jl, xg]:
                email_receiver_list.append(item.get("email"))
        email_Cc_list = []
        tof.send_email(sendTitle=email_title, msgContent=email_data,
                       sendTo=["<EMAIL>"],
                       sendCopy=email_Cc_list)
        variables = {
            "email_receiver_list": email_receiver_list
        }
        flow.complete_task(self.ctx.task_id, variables)

    def wait_delivery_data_review(self):
        """
        等待所有审核子单结束
        """
        review_ticket_list = self.ctx.variables.get("review_ticket_list", [])
        data = {
            "ResultColumns": {
                "CreateTime": "",
                "EndTime": "",
                "InstanceId": "",
                # 是否强制结单
                "IsForceEnd": "",
                # 流程定义标识
                "ProcessDefinitionKey": "",
                "ServiceRecoverTime": "",
                "StartProcessTime": "",
                # 单据状态[OPEN:运行中;END:已结束]
                "TicketStatus": "",
                "Title": "",
                "CustomRequestVarKV": "",
            },
            "SearchCondition": {'TicketId': review_ticket_list}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']
        cnt = 0  # 用于记录有多少个工单正在运行中
        for item in res_list:
            if item.get('TicketStatus') == 'OPEN':
                cnt += 1
        if cnt:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}

    def data_review_back(self, project_name, equipment_type, PM, po, po_material_info, upload_receiving_information,
                         acceptance_time, choose_list):
        """
        甲供发货到货信息审批回传
        """
        project = self.ctx.variables.get("project", '')
        f_instance_id = self.ctx.variables.get("f_instance_id")
        dh_list = self.ctx.variables.get("dh_list", [])
        main_variables = flow.get_variables(f_instance_id, ["dh_list", "acceptance_time", "actual_arrival_time"])
        main_dh_list = main_variables.get("dh_list", [])
        if not main_dh_list:
            main_dh_list = dh_list
        else:
            main_dh_list.extend(dh_list)
        main_acceptance_time = main_variables.get("acceptance_time", "")
        main_actual_arrival_time = main_variables.get("actual_arrival_time", "")
        actual_arrival_time = self.ctx.variables.get("actual_arrival_time", "")

        # 将字符串时间转换为 datetime 对象
        def parse_time(time_str):
            try:
                return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S") if time_str else None
            except ValueError:
                return None

        # 转换时间字符串为 datetime 对象
        acceptance_time_dt = parse_time(acceptance_time)
        actual_arrival_time_dt = parse_time(actual_arrival_time)
        main_acceptance_time_dt = parse_time(main_acceptance_time)
        main_actual_arrival_time_dt = parse_time(main_actual_arrival_time)

        # 计算 acceptance_time 的最晚时间
        latest_acceptance_time_dt = max(
            [t for t in [acceptance_time_dt, main_acceptance_time_dt] if t],
            default=None
        )
        latest_acceptance_time = latest_acceptance_time_dt.strftime(
            "%Y-%m-%d %H:%M:%S") if latest_acceptance_time_dt else ""

        # 计算 actual_arrival_time 的最晚时间
        latest_actual_arrival_time_dt = max(
            [t for t in [actual_arrival_time_dt, main_actual_arrival_time_dt] if t],
            default=None
        )
        latest_actual_arrival_time = latest_actual_arrival_time_dt.strftime(
            "%Y-%m-%d %H:%M:%S") if latest_actual_arrival_time_dt else ""

        # 更新流程变量
        WorkflowVarUpdate(
            instance_id=f_instance_id,
            variables={
                "project_name": project_name,
                "PM": PM,
                # "upload_receiving_information": upload_receiving_information,
                "acceptance_time": latest_acceptance_time,
                "choose_list": choose_list,
                "actual_arrival_time": latest_actual_arrival_time,
                "project": project,
                "dh_list": main_dh_list
            }
        ).call()
        flow.complete_task(task_id=self.ctx.task_id)

    def send_notification(self):
        pass


class EquipmentUploadReceivingNew(AjaxTodoBase):
    """
    设备收货：新接口
    """

    def __init__(self):
        super(EquipmentUploadReceivingNew, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        dh_list = process_data.get('dh_list', None)
        actual_arrival_time = ''
        upload_receiving_information_list = []
        receipt_information_list = []
        tb_db = mysql.new_mysql_instance("tbconstruct")
        main_ticket_id = self.ctx.variables.get("main_ticket_id", "")
        for i in dh_list:
            equipment_batch = i.get("equipment_batch", None)
            planned_arrival_time = i.get('planned_arrival_time', None)
            actual_arrival_time = i.get('actual_arrival_time', None)
            upload_receiving_information = i.get('upload_receiving_information', None)
            if upload_receiving_information and len(upload_receiving_information) > 0 and 'response' in \
                    upload_receiving_information[0]:
                upload_receiving_information_list.append(
                    upload_receiving_information[0]["response"]["FileList"][0]["url"]
                )
                update_data = {
                    'upload_receiving_information': upload_receiving_information[0]["response"]["FileList"][0]["url"],
                    'planned_arrival_time': planned_arrival_time,
                    'actual_arrival_time': actual_arrival_time,
                }
                conditions = {
                    'ticket_id': ticket_id if not main_ticket_id else main_ticket_id,
                    'equipment_batch': equipment_batch
                }
                tb_db.begin()
                tb_db.update("equipment_arrival_data", update_data, conditions)
                tb_db.commit()
            receipt_time = i.get('receipt_time', '')
            receipt_information = i.get('receipt_information', None)
            if receipt_information and len(receipt_information) > 0 and 'response' in receipt_information[0]:
                receipt_information_list.append(
                    receipt_information[0]["response"]["FileList"][0]["url"]
                )
                update_data = {
                    'receipt_time': receipt_time,
                    'receipt_information': receipt_information[0]["response"]["FileList"][0]["url"],
                }
                conditions = {
                    'ticket_id': ticket_id if not main_ticket_id else main_ticket_id,
                    'equipment_batch': equipment_batch
                }
                tb_db.begin()
                tb_db.update("equipment_arrival_data", update_data, conditions)
                tb_db.commit()
        if dh_list[-1].get("receipt_time"):
            parsed_time = datetime.strptime(dh_list[-1]["receipt_time"], '%Y-%m-%d')
            acceptance_time = parsed_time.strftime('%Y-%m-%d 00:00:00')
        else:
            parsed_time = datetime.strptime(dh_list[-1]["actual_arrival_time"], '%Y-%m-%d')
            acceptance_time = parsed_time.strftime('%Y-%m-%d 00:00:00')
        variables = {
            'dh_list': dh_list,
            'actual_arrival_time': actual_arrival_time,
            'upload_receiving_information': upload_receiving_information_list,
            'acceptance_time': acceptance_time
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentChangingState(object):
    """
        更改流程状态
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def save_weekly_report(self, project_name, ticket_id, report=None):
        """
        提交周报
        """
        data_list = []
        if report:
            for i in report:
                photo = i.get("photo")
                photo_list = []
                plan_time = i.get("plan_time")
                real_time = i.get("real_time")
                schedule = i.get("schedule")
                content = i.get("content")
                progress = str(i.get("progress"))
                question = i.get("question")
                # 校验时间格式
                if plan_time and ("-" not in plan_time):
                    return {"code": 400, "msg": "计划时间格式错误"}

                if real_time and ("-" not in real_time):
                    return {"code": 400, "msg": "实际完成时间格式错误"}
                # 校验进度百分比
                if progress:
                    if progress.endswith("%"):
                        try:
                            percentage = float(progress[:-1])
                            if percentage < 0 or percentage > 100:
                                return {'code': 400, 'msg': '进展百分比错误：数值超出0-100范围'}
                        except ValueError:
                            return {"code": 400, "msg": "进展百分比错误：数据错误"}
                    else:
                        return {"code": 400, "msg": "进展百分比错误：未以%结尾"}
                else:
                    return {"code": 400, "msg": "进展百分比未填写"}
                if photo:
                    for j in photo:
                        try:
                            url = j["response"]["FileList"][0]["url"]
                            photo_list.append(
                                {
                                    "url": url,
                                    "height": "150px",
                                    "width": "150px",
                                    "marginRight": "5px",
                                }
                            )
                        except IndexError as e:
                            return {"code": 400, "msg": f"照片上传失败:{e}"}
                        except KeyError as e:
                            return {"code": 400, "msg": f"照片上传失败:{e}"}
                data_list.append(
                    {
                        "stage": i.get("stage"),
                        "plan_time": plan_time,
                        "real_time": real_time,
                        "schedule": schedule,
                        "content": content,
                        "progress": progress,
                        "question": question,
                        "photo": photo,
                        "photo_list": photo_list,
                    }
                )

            query_data = {
                "SchemaId": "ticket_base",
                "Data": {
                    "ResultColumns": {
                        "InstanceId": "",
                        "TicketId": ""
                    },
                    "SearchCondition": {
                        "TicketId": [str(ticket_id)]
                    }
                }
            }
            query_result = gnetops.request(
                action="QueryData",
                method="Run",
                ext_data=query_data
            )
            result_list = query_result.get("List")
            if result_list:
                instance_id = result_list[0].get('InstanceId')
            else:
                return {'code': 400, 'msg': '周报提交失败:未获取到流程id'}
            # 冲流程变量获取数据
            main_flow_vars = flow.get_variables(instance_id, ["weekly_list", "template"])
            weekly_list = main_flow_vars.get("weekly_list", [])
            template = main_flow_vars.get("template", None)

            if len(weekly_list) > 0:
                week = weekly_list[0].get("week")
                num = int(week) + 1
                weekly_list.insert(0, {"week": num, "report": data_list})
            else:
                num = "1"
                weekly_list = [{"week": num, "report": data_list}]
            WorkflowVarUpdate(instance_id=instance_id,
                              variables={
                                  "weekly_list": weekly_list,
                                  "report": template
                              }
                              ).call()
            insert_data = []
            for data in report:
                insert_data.append({
                    'stage': data.get('stage'),
                    'plan_time': data.get('plan_time'),
                    'real_time': data.get('real_time'),
                    'schedule': data.get('schedule'),
                    'content': data.get('content'),
                    'progress': data.get('progress'),
                    'question': data.get('question'),
                    'photo': str(data.get('photo')),
                    'project_name': project_name,
                    'ticket_id': ticket_id,
                    'week': num
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("equipment_production_racking_weekly", insert_data)
            tb_db.commit()
            return {'code': 200, 'msg': "周报提交成功"}
        else:
            return {'code': 400, 'msg': '请填写周报信息'}

    def changing_the_state_of_process(self, ticket_id):
        con = {'ticket_id': ticket_id}
        data = {'state': 1}
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("equipment_production_racking_process", data, con)
        tb_db.commit()
        flow.complete_task(self.ctx.task_id)

    def get_project(self):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT campus,project_name FROM risk_early_warning_data"
        campus_list = []
        seen = []
        project_name_list = []
        result = db.get_all(sql1)
        for i in result:
            if i.get('campus') not in seen:
                campus_list.append(
                    {
                        "label": i.get('campus'),
                        'value': i.get('campus')
                    }
                )
                seen.append(i.get('campus'))
            project_name_list.append(
                {
                    "label": i.get('project_name'),
                    'value': i.get('project_name')
                }
            )
        variables = {
            "project_name_list": project_name_list,
            "campus_list": campus_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_responsible(self, project_name):
        device_name = self.ctx.variables.get('device_name')
        db = mysql.new_mysql_instance("tbconstruct")

        pm_sql = f"SELECT PM FROM risk_early_warning_data where project_name = '{project_name}'"
        jcs_sql = "SELECT account FROM project_role_account " \
                  f"WHERE project_name = '{project_name}' and role = '集成商-项目经理' and del_flag = '0'"
        xg_sql = "SELECT account FROM project_role_account " \
                 f"WHERE project_name = '{project_name}' and role = '项管-项目经理' and del_flag = '0'"
        jl_sql = "SELECT account FROM project_role_account " \
                 f"WHERE project_name = '{project_name}' and role = '监理-总监理工程师' and del_flag = '0'"

        is_hire = whether_is_rental_project(project_name)
        if is_hire:
            jcs_sql = "SELECT account FROM project_role_account " \
                      f"WHERE project_name = '{project_name}' and role = '总包-项目经理' and del_flag = '0'"
            xg_sql = "SELECT account FROM project_role_account " \
                     f"WHERE project_name = '{project_name}' and role = '合建方-项目经理' and del_flag = '0'"

        sql1 = f"SELECT campus,project,project_name FROM risk_early_warning_data WHERE project_name='{project_name}'"
        campus_list = []
        seen = []
        result = db.get_all(sql1)
        campus = ''
        project = ''
        match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
        if match:
            campus = match.group(1)
            project = match.group(2)
        campus = result[0].get('campus')
        project = result[0].get('project')

        for i in result:
            if i.get('campus') not in seen:
                campus_list.append(
                    {
                        "label": i.get('campus'),
                        'value': i.get('campus')
                    }
                )
                seen.append(i.get('campus'))
        pm_list = db.get_all(pm_sql)
        jcs_list = db.get_all(jcs_sql)
        xg_list = db.get_all(xg_sql)
        jl_list = db.get_all(jl_sql)
        pm = ""
        jcs = ""
        xg = ""
        jl = ""
        if pm_list:
            pm = pm_list[0].get("PM")
        if jcs_list:
            jcs = jcs_list[0].get("account")
        if xg_list:
            xg = xg_list[0].get("account")
        if jl_list:
            jl = jl_list[0].get("account")
        equipment_category = device_name
        f_instance_id = self.ctx.instance_id
        variables = {
            "PM": pm,
            "jcs": jcs,
            "xg": xg,
            "jl": jl,
            "campus": campus,
            "project": project,
            "campus_list": campus_list,
            "equipment_category": equipment_category,
            "f_instance_id": f_instance_id
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentDeliverySupervision(AjaxTodoBase):
    """
        发货资料审批（监理）
    """

    def __init__(self):
        super(EquipmentDeliverySupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fh_jl_review = process_data.get('fh_jl_review', None)
        fh_jl_rejection = process_data.get('fh_jl_rejection', None)
        fh_jl_remark = process_data.get('fh_jl_remark', None)
        if fh_jl_review == '驳回':
            if fh_jl_rejection:
                variables = {
                    'fh_jl_review': fh_jl_review,
                    'fh_jl_rejection': fh_jl_rejection,
                    "fh_jl_remark": fh_jl_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'fh_jl_review': fh_jl_review,
                "fh_jl_remark": fh_jl_remark
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentDeliveryItemTube(AjaxTodoBase):
    """
        发货资料审批（项管）
    """

    def __init__(self):
        super(EquipmentDeliveryItemTube, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        upload_shipping_insert = self.ctx.variables.get("upload_shipping_insert")
        fh_xg_review = process_data.get('fh_xg_review', None)
        fh_xg_rejection = process_data.get('fh_xg_rejection', None)
        fh_xg_remark = process_data.get('fh_xg_remark', None)
        if fh_xg_review == '驳回':
            if fh_xg_rejection:
                variables = {
                    'fh_xg_review': fh_xg_review,
                    'fh_xg_rejection': fh_xg_rejection,
                    "fh_xg_remark": fh_xg_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            if type(upload_shipping_insert) == list:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("equipment_arrival_data", upload_shipping_insert)
                tb_db.commit()
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("equipment_arrival_data", upload_shipping_insert)
                tb_db.commit()

            variables = {
                'fh_xg_review': fh_xg_review,
                "fh_xg_remark": fh_xg_remark
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentDeliveryItemTubeNew(AjaxTodoBase):
    """
        发货资料审批（项管）
    """

    def __init__(self):
        super(EquipmentDeliveryItemTubeNew, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fh_xg_review = process_data.get('fh_xg_review', None)
        fh_xg_rejection = process_data.get('fh_xg_rejection', None)
        fh_xg_remark = process_data.get('fh_xg_remark', None)
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        project_name = self.ctx.variables.get("project_name")
        if not project_name:
            project_name = campus + project
        planned_arrival_time = self.ctx.variables.get("planned_arrival_time")
        fh_list = process_data.get("fh_list")
        equipment_category = process_data.get("equipment_category")
        supplier = process_data.get("supplier")
        insert_list = []
        dh_list = []
        for i in fh_list:
            equipment_batch = i.get("equipment_batch", None)
            scheduled_delivery_time = i.get('scheduled_delivery_time', None)
            actual_delivery_time = i.get('actual_delivery_time', None)
            upload_shipping_information = i.get('upload_shipping_information', None)
            dh_list.append({
                "planned_arrival_time": planned_arrival_time,
                "equipment_batch": equipment_batch
            })
            if upload_shipping_information:
                insert_list.append({
                    "upload_shipping_information": upload_shipping_information[0]["response"]["FileList"][0]["url"],
                    "campus": campus,
                    "project_name": project,
                    "equipment_category": equipment_category,
                    "supplier": supplier,
                    "scheduled_delivery_time": scheduled_delivery_time,
                    "actual_delivery_time": actual_delivery_time,
                    "ticket_id": ticket_id,
                    "equipment_batch": equipment_batch,
                })
        if fh_xg_review == '驳回':
            if fh_xg_rejection:
                variables = {
                    'fh_xg_review': fh_xg_review,
                    'fh_xg_rejection': fh_xg_rejection,
                    "fh_xg_remark": fh_xg_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            upload_shipping_insert = insert_list
            if type(upload_shipping_insert) == list:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("equipment_arrival_data", upload_shipping_insert)
                tb_db.commit()
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("equipment_arrival_data", upload_shipping_insert)
                tb_db.commit()

            variables = {
                'fh_xg_review': fh_xg_review,
                "fh_xg_remark": fh_xg_remark,
                "dh_list": dh_list,
                "project_name": project_name
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentReceiptSupervision(AjaxTodoBase):
    """
        收货资料审批（监理）
    """

    def __init__(self):
        super(EquipmentReceiptSupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dh_jl_review = process_data.get('dh_jl_review', None)
        dh_jl_rejection = process_data.get('dh_jl_rejection', None)
        dh_jl_remark = process_data.get('dh_jl_remark', None)

        if dh_jl_review == '驳回':
            if dh_jl_rejection:
                variables = {
                    'dh_jl_review': dh_jl_review,
                    'dh_jl_rejection': dh_jl_rejection,
                    "dh_jl_remark": dh_jl_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'dh_jl_review': dh_jl_review,
                "dh_jl_remark": dh_jl_remark
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentReceiptItemTube(AjaxTodoBase):
    """
        收货资料审批（项管）
    """

    def __init__(self):
        super(EquipmentReceiptItemTube, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        upload_receiving_update = self.ctx.variables.get("upload_receiving_update")
        dh_xg_review = process_data.get('dh_xg_review', None)
        dh_xg_rejection = process_data.get('dh_xg_rejection', None)
        dh_xg_remark = process_data.get('dh_xg_remark', None)

        if dh_xg_review == '驳回':
            if dh_xg_rejection:
                variables = {
                    'dh_xg_review': dh_xg_review,
                    'dh_xg_rejection': dh_xg_rejection,
                    "dh_xg_remark": dh_xg_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'dh_xg_review': dh_xg_review,
                "dh_xg_remark": dh_xg_remark
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentReceiptPM(AjaxTodoBase):
    """
        收货资料审批（PM）
    """

    def __init__(self):
        super(EquipmentReceiptPM, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        main_ticket_id = self.ctx.variables.get("main_ticket_id")
        # 适配子流程等待到货资料
        if main_ticket_id:
            ticket_id = main_ticket_id
        pm_review = process_data.get("pm_review", None)
        pm_rejection = process_data.get("pm_rejection", None)
        pm_remark = process_data.get("pm_remark", None)
        choose_list = process_data.get("choose_list", None)
        acceptance_time = process_data.get("acceptance_time")
        # if not acceptance_time:
        #     return {"code": -1, "msg": "请填写时间"}

        if pm_review == "驳回":
            if pm_rejection:
                variables = {
                    "choose_list": choose_list,
                    "pm_review": pm_review,
                    "pm_rejection": pm_rejection,
                    "pm_remark": pm_remark,
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            tb_db = mysql.new_mysql_instance("tbconstruct")
            update = {"pm_confirm": "确认"}
            con = {"ticket_id": ticket_id}
            tb_db.begin()
            tb_db.update("equipment_production_racking_process", update, con)
            tb_db.commit()
            variables = {
                "choose_list": choose_list,
                "pm_review": pm_review,
                "pm_remark": pm_remark,
                "acceptance_time": acceptance_time
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EquipmentDataTransfer(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def transform_data(self):
        flow.complete_task(self.ctx.task_id)

    def transform_data_new(self, project_name, equipment_type, PM, po, po_material_info, upload_receiving_information,
                           acceptance_time, choose_list):
        # 更新收货时间
        # 流程标识：UpdateDeviceReceiveStatus
        # {
        #     "Mozu": "重庆泰和模组01", // 模组名称
        # "PartyAInfo": {"品类名": "2024-08-30"} // 品类名称 + 收货时间
        # }
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = (f"SELECT module_name FROM project_module_mapping_relationship "
                f"WHERE project_name = '{project_name}'")
        query_result = db.get_row(sql1)
        mozu_name = query_result.get("module_name")
        equipment_category = self.ctx.variables.get("equipment_category")
        spare_time = self.ctx.variables.get("actual_arrival_time")
        if acceptance_time:
            try:
                acceptance_time = datetime.strptime(acceptance_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                parsed_time = datetime.strptime(acceptance_time, '%Y-%m-%d %H:%M')
                acceptance_time = parsed_time.replace(hour=0, minute=0, second=0)
        else:
            acceptance_time = datetime.strptime(spare_time + " 00:00:00", '%Y-%m-%d %H:%M:%S')
        acceptance_time = acceptance_time.strftime('%Y-%m-%d %H:%M:%S')
        # 提取日期部分并格式化
        formatted_date = acceptance_time.split(' ')[0]
        ticket_info = gnetops.create_ticket(
            flow_key="UpdateDeviceReceiveStatus",
            description='该工单为："' + project_name + f':{equipment_category}' + '更新收货状态',
            ticket_level=3,
            title=project_name + f':{equipment_category}设备生产跟踪' + '更新收货状态',
            creator="v_zongxiyu",
            concern="kerwinzlin",  # 关注人, 这里暂为空
            deal="kerwinzlin",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var={
                "Mozu": mozu_name,
                "PartyAInfo": {equipment_category: formatted_date}
            }
        )
        update_time_ticket = [ticket_info.get('TicketId')]
        if not equipment_type:
            raise ValueError(f"{project_name}-{equipment_category}设备生产跟踪未获取到设备类型字段")
        if equipment_type == "甲供":
            po_material_info = []
            # 获取po
            sql = (
                "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
                "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
                f"WHERE rewd.project_name = '{project_name}' "
            )
            code_id_list = db.query(sql)
            self.workflow_detail.add_kv_table("1.获取需求id", {"len(code_id_list)": len(code_id_list)})
            if code_id_list:
                material_id_list = []
                # 需求单号
                for i in code_id_list:
                    is_po_info = False
                    code_id = i.get("idcrmOrderCode")
                    resp = curl.get(
                        title="Dcops获取po信息",
                        system_name="Dcops",
                        url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/"
                            f"queryOrderByFormCode/{code_id}",
                        postdata="",
                        append_header={"Content-Type": "application/json"})
                    po_info = resp.json()
                    if po_info:
                        po_info_list = []
                        # 所有的物料id
                        for data in po_info.get("data"):
                            id = data.get("orderItemId")
                            data["orderItemId"] = str(id)
                            po_info_list.append(data)
                        po_material_info += po_info_list
                        if not is_po_info:
                            is_po_info = True
                        self.workflow_detail.add_kv_table("po信息及物料id",
                                                          {"len(po_info_list)": len(po_info_list)})
                # 提取所有的物料id
                for j in po_material_info:
                    material_id = j.get("materialId")
                    if material_id not in material_id_list:
                        material_id_list.append(material_id)
                self.workflow_detail.add_kv_table("物料id", {"message": material_id_list})

                # 通过物料id获取品名信息
                material_list = gnetops.request(action="Tbconstruct",
                                                method="ObtainMaterialInfoById",
                                                ext_data={
                                                    "MaterialCodeId": material_id_list
                                                }
                                                )

                if material_list:
                    material_info_list = material_list.get("materialList")
                    # 构造品名、物料编码、物料id
                    data = []
                    if material_info_list:
                        for material in material_info_list:
                            data.append(
                                {
                                    # 物料编码
                                    "material_code": material.get("materialCode"),
                                    # 物料id
                                    "material_id": material.get("materialQueryDTO").get(
                                        "materialId"
                                    ),
                                    # 物料名称
                                    "material_name": material.get("materialQueryDTO").get(
                                        "materialName"
                                    ),
                                    # 品名
                                    "material_category_name": material.get(
                                        "materialQueryDTO"
                                    ).get("materialCategoryName"),
                                    # 品名编码
                                    "material_category_code": material.get(
                                        "materialQueryDTO"
                                    ).get("materialCategoryCode"),
                                }
                            )
                        self.workflow_detail.add_kv_table("构造的数据", {"message": data})

                        # 查询品名合设备名称关系
                        ticket_id = self.ctx.variables.get("ticket_id")
                        device_name = self.ctx.variables.get("device_name")
                        po_list = []
                        works_list = []
                        db = mysql.new_mysql_instance("tbconstruct")
                        sql = (
                            "SELECT material_category_name FROM equipment_category_mapping_relationship "
                            f"WHERE device_name = '{device_name}' "
                        )
                        material_category_name_list = db.query(sql)
                        # 对应品名后的物料信息

                        if material_category_name_list:
                            # 一个设备所对应的所以品名
                            material_category_name = []
                            for category in material_category_name_list:
                                if category.get("material_category_name"):
                                    material_category_name.append(
                                        category.get("material_category_name")
                                    )
                            for d in data:
                                # 获取与设备对应的品名信息
                                if (
                                        d.get("material_category_name")
                                        in material_category_name
                                ):
                                    works_list.append(d)

                            if works_list:
                                for works in works_list:
                                    works_id = works.get("material_id")
                                    for item in po_material_info:
                                        if works_id == item.get("materialId"):
                                            po_id = item.get("orderCode")
                                            if po_id not in po:
                                                po.append(po_id)
                                                po_list.append(
                                                    {
                                                        "po": po_id,
                                                        "material_category_name": works.get(
                                                            "material_category_name"
                                                        ),
                                                    }
                                                )
                                            works["PO_id"] = po_id
                                    works["ticket_id"] = ticket_id
                                    works["device_name"] = device_name
                                    works["equipment_type"] = equipment_type
                                    works["project_name"] = project_name

                                tb_db = mysql.new_mysql_instance("tbconstruct")
                                tb_db.begin()
                                tb_db.insert_batch(
                                    "equipment_production_racking_po", works_list
                                )
                                tb_db.commit()
                            else:
                                self.workflow_detail.add_kv_table(
                                    "获取po信息", {"message": "po获取失败"}
                                )
                        else:
                            self.workflow_detail.add_kv_table(
                                "获取po信息", {"message": "此物料id无法获取品名信息"}
                            )
                else:
                    self.workflow_detail.add_kv_table(
                        "获取po信息", {"message": "此物料id无法获取品名信息"}
                    )
            else:
                self.workflow_detail.add_kv_table(
                    "获取po信息", {"message": "获取不到需求单号"}
                )
            data_list = []
            if choose_list:
                for i in choose_list:
                    for j in po_material_info:
                        if i.get('po') == j.get("orderCode"):
                            data_list.append({"AssetCode": "",
                                              "LineDetailId": str(j.get('orderItemId')),
                                              "QuantityReceived": j.get('orderAmount'),
                                              "SnCode": "",
                                              "AttachmentList": upload_receiving_information
                                              })
            else:
                for i in po:
                    for j in po_material_info:
                        if i == j.get("orderCode"):
                            data_list.append({"AssetCode": "",
                                              "LineDetailId": str(j.get('orderItemId')),
                                              "QuantityReceived": j.get('orderAmount'),
                                              "SnCode": "",
                                              "AttachmentList": upload_receiving_information
                                              })

            self.workflow_detail.add_kv_table("传参信息", {"message": len(data_list)})
            # 获取企微id
            chat_info = gnetops.request(action="Tof",
                                        method="GetIDCStaffIDByEngName",
                                        ext_data={
                                            "EngName": str(PM),
                                        }
                                        )
            self.workflow_detail.add_kv_table('获取企微id', {'message': str(chat_info)})

            chat_id = ''
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table('获取企微id',
                                                  {"success": False, "data": f"获取失败:{str(chat_info)}"})

            # info = gnetops.request(action="Tbconstruct",
            #                        method="FeedbackAcceptanceStatusToStar",
            #                        ext_data={
            #                            "FeedbackType": 0,  # 0=到货,1=安装,2=初验,3=终,人员入场=4
            #                            "ReceiveDate": acceptance_time,  # 时间
            #                            "ReceiveUserId": chat_id,  # id
            #                            "Orderdetails": data_list,
            #                        }
            #                        )
            #
            # if not info.get("ok") or info.get("status") != "success":
            #     raise Exception("触发商务状态失败")


            variables = {
                "acceptance_time": acceptance_time,
                "data_list": data_list,
                # "info": str(info),
                "update_time_ticket": update_time_ticket,
                "po_material_info": po_material_info
            }
        else:
            variables = {}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def equipment_latest_time_update(self):
        res_list = self.ctx.variables.get('res_list')
        project_name = self.ctx.variables.get('project_name')
        project = ""
        campus = ""
        dcopsTicketId = ""
        db = mysql.new_mysql_instance("tbconstruct")
        sql_1 = (f"SELECT project,campus,dcopsTicketId FROM risk_early_warning_data "
                 f"WHERE project_name='%s'") % project_name
        project_info = db.get_row(sql_1)
        if project_info:
            project = project_info.get('project')
            campus = project_info.get('campus')
            dcopsTicketId = project_info.get('dcopsTicketId')
        # 将 res_list 转换为字符串形式，每个元素用单引号包裹
        formatted_res_list = "', '".join(map(str, res_list))

        # 构造 SQL 查询
        sql_2 = (
            "SELECT actual_arrival_time FROM equipment_arrival_data "
            f"WHERE ticket_id IN ('{formatted_res_list}')"
        )

        # 执行查询
        actual_arrival_time_list = db.get_all(sql_2)
        # 初始化变量
        is_all_finished = True
        latest_time = None

        # 遍历结果列表，找出最晚时间
        if actual_arrival_time_list:
            for record in actual_arrival_time_list:
                actual_arrival_time = record.get('actual_arrival_time')

                # 检查是否为有效的 datetime 对象
                if isinstance(actual_arrival_time, datetime):
                    # 如果当前记录的时间比 latest_time 晚，则更新 latest_time
                    if latest_time is None or actual_arrival_time > latest_time:
                        latest_time = actual_arrival_time
                else:
                    # 如果 actual_arrival_time 不是 datetime 对象（例如为 None 或其他类型），标记为未完成
                    is_all_finished = False
        else:
            is_all_finished = False

        # 如果所有记录都已完成
        if is_all_finished:
            if project and campus and dcopsTicketId:
                # 将 latest_time 转换回字符串格式（如果存在）
                latest_time_str = latest_time.strftime("%Y-%m-%d") if latest_time else None

                response_data = gnetops.request(
                    action="Project",
                    method="UpdateSummaryData",
                    ext_data={
                        "campus": campus,
                        "project_name": project,
                        "ticket_id": dcopsTicketId,
                        "update_filed": "SupplyEndTime",
                        "update_value": latest_time_str  # 使用字符串格式的日期时间
                    },
                    scheme="ifob-infrastructure",
                )
                return {"success": True, "data": "流程已结束"}
        else:
            return {"success": False, "data": "流程未结束"}


class EquipmentAutomatic(object):
    """
        设备生产跟踪流程自动起单
    """

    def __init__(self):
        super(EquipmentAutomatic, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def equipment_automatic(self, project_name, campus, equipment_list):
        res_list = []
        # equipment_list = [{'type': "甲供", "name": "机柜"},
        #                   {'type': "甲供", "name": "PDU"},
        #                   {'type': "甲供", "name": "综合配电柜"},
        #                   {'type': "甲供", "name": "HVDC"},
        #                   {'type': "甲供", "name": "中压柜"},
        #                   {'type': "甲供", "name": "变压器"},
        #                   {'type': "甲供", "name": "低压柜"},
        #                   {'type': "甲供", "name": "柴发方仓"},
        #                   {'type': "甲供", "name": "AHU方仓"},
        #                   {'type': "甲供", "name": "蓄电池"},
        #                   {'type': "甲供", "name": "T-Box、交换机、传感器等"},
        #                   {'type': "乙供", "name": "IT方仓"},
        #                   {'type': "乙供", "name": "不间断电源方仓"},
        #                   {'type': "乙供", "name": "电池架"},
        #                   {'type': "乙供", "name": "低压方仓"},
        #                   {'type': "乙供", "name": "中压方仓"},
        #                   {'type': "乙供", "name": "柴发并机方仓"},
        #                   {'type': "乙供", "name": "水处理方仓"},
        #                   {'type': "乙供", "name": "备件方仓"},
        #                   {'type': "乙供", "name": "假负载方仓"},
        #                   {'type': "乙供", "name": "低压母联"},
        #                   {'type': "乙供", "name": "油罐"},
        #                   {'type': "乙供", "name": "室内RO设备"},
        #                   {'type': "乙供", "name": "加除湿机"},
        #                   ]
        for i in equipment_list:
            processTitle = f"{project_name}:{i.get('name')}设备生产跟踪"
            data = {
                "CustomVariables": {
                    "device_name": i.get('name'),
                    "project_name": project_name,
                    "campus": campus,
                    "equipment_type": i.get("type")
                },
                "ProcessDefinitionKey": "equipment_production_tracking_management",
                "Source": "",
                "TicketDescription": "设备生产跟踪",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan;youngshi;v_zongxiyu",
                    "Creator": "v_zongxiyu",
                    "Deal": "v_zongxiyu"
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Ticket", method="Create", data=data)
            res_list.append(res.get('TicketId'))
        variables = {
            'res_list': res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def cooperative_equipment_automatic(self):
        res_list = []
        equipment_list = [{'type': "甲供", "name": "机柜"},
                          {'type': "甲供", "name": "PDU"},
                          {'type': "甲供", "name": "弹性一体柜(HVDC)"},
                          {'type': "甲供", "name": "中压柜"},
                          {'type': "甲供", "name": "变压器"},
                          {'type': "甲供", "name": "低压柜"},
                          {'type': "甲供", "name": "柴发方仓"},
                          {'type': "甲供", "name": "AHU方仓"},
                          {'type': "甲供", "name": "蓄电池"},
                          {'type': "甲供", "name": "T-Box"},
                          {'type': "乙供", "name": "IT方仓"},
                          {'type': "乙供", "name": "不间断电源方仓"},
                          {'type': "乙供", "name": "电池架"},
                          {'type': "乙供", "name": "低压方仓"},
                          {'type': "乙供", "name": "中压方仓"},
                          {'type': "乙供", "name": "柴发并机方仓"},
                          {'type': "乙供", "name": "水处理方仓"},
                          {'type': "乙供", "name": "备件方仓"},
                          {'type': "乙供", "name": "假负载方仓"},
                          {'type': "乙供", "name": "低压母联"},
                          {'type': "乙供", "name": "油罐"},
                          {'type': "乙供", "name": "室内RO设备"},
                          {'type': "乙供", "name": "加除湿机"},
                          ]
        project_name_dict = {
            "本贸":["天津高新B4-2","天津高新B4-1","天津高新B3-2","天津高新B3-1"],
            "中兴":["怀来瑞北B4-1","怀来东园B4-1","怀来东园B6-1","怀来东园B6-2","清远清新B6-1","清远清新B6-2"],
            "科华":["天津高新B2-1","天津高新B2-2","怀来瑞北B5-1","怀来瑞北B5-2"],
            "维谛":["怀来东园B5-1","怀来东园B5-2"]
        }
        for supplier,project_name_list in project_name_dict.items():
            Facilitator = supplier
            for item in project_name_list:
                project_name = item
                for i in equipment_list:
                    processTitle = f"{project_name}:{i.get('name')}设备生产跟踪"
                    data = {
                        "CustomVariables": {
                            "device_name": i.get('name'),
                            "project_name": project_name,
                            "equipment_type": i.get("type"),
                            "Facilitator": Facilitator,
                            "category_name": i.get('name')
                        },
                        "ProcessDefinitionKey": "cooperative_partner_equipment_production_arrived",
                        "Source": "",
                        "TicketDescription": "设备生产跟踪",
                        "TicketLevel": "3",
                        "TicketTitle": processTitle,
                        "UserInfo": {
                            "Concern": "reywmwang;youngshi;v_zongxiyu,v_mmywang",
                            "Creator": "v_zongxiyu",
                            "Deal": "v_zongxiyu"
                        }
                    }
                    # 起单，并抛入data
                    res = gnetops.request(action="Ticket", method="Create", data=data)
                    res_list.append(res.get('TicketId'))
                    time.sleep(1)
        variables = {
            'res_list': res_list
        }
        return res_list
        # flow.complete_task(self.ctx.task_id, variables=variables)
