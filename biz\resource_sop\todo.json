{"key": "nbroker.protocol.approve", "name": "审批节点", "ajax_service": "approve", "is_end": "{{ is_end }}", "basic_views": [{"name": "流程ID", "type": "KVField", "data": "{{ctx.instance_id}}"}, {"name": "任务ID", "type": "KVField", "data": "{{ctx.task_id}}"}, {"name": "处理人", "type": "KVField", "data": "{{ctx.C<PERSON>}}"}], "todo_views": [{"name": "操作指引", "type": "KVField", "data": "{{ctx.variables.请}}"}, {"name": "请填写待办操作名称", "type": "OrderTable", "data": "{{ctx.variables.请填写有序列表类型变量名称}}", "openStatus": true, "header": ["请填写有序列表类型字段", "请填写有序列表类型字段", "......."]}, {"name": "请填写待办操作名称", "type": "Json", "data": "{{ctx.variables.请填写JSON类型变量名称}}", "key": "请填写JSON类型数据KEY"}, {"name": "请填写待办操作名称", "type": "TextArea", "data": "{{ctx.variables.请填写TEXT类型数据变量名称}}", "keys": "请填写TEXT类型数据KEY"}, {"name": "请填写待办操作名称", "type": "Plot", "data": "{{ctx.variables.请填写PLOT类型数据KEY}}"}, {"name": "请填写待办操作名称", "type": "MultiPlot", "data": "{{ctx.variables.请填写多PLOT类型数据变量名称}}"}], "operate_views": [{"data": "请填写流条件标示默认值", "key": "请填写流条件标示", "name": "请填写流条件名称", "options": [{"name": "请填写流条件选项", "data": "请填写流条件标示值"}, {"name": "请填写流条件选项", "data": "请填写流条件标示值"}], "type": "Radio"}, {"name": "请填写确认按钮名称", "key": "end_msg", "type": "<PERSON><PERSON>", "buttonType": "danger", "secondary_confirm": true, "callback_method": "请填写回调方法"}]}