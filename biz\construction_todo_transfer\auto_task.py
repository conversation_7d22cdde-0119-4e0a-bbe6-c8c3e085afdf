from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class TodoTransferAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 调接口转派待办
    def toTransfer(self, transfer_info_list):
        data = {"Datalist": []}
        for item in transfer_info_list:
            data["Datalist"].append(
                {
                    "RawProcessUser": item["origin_user"],
                    "TransProcessUser": item["target_user"],
                }
            )
        self.workflow_detail.add_kv_table("1.入参", {"message": data})
        res = gnetops.request(
            action="TaskTodo", method="BatchUpdateProcessUseV1", data=data
        )
        self.workflow_detail.add_kv_table("2.出参", {"message": res})
        flow.complete_task(self.ctx.task_id)
