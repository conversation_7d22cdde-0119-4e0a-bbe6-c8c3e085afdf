from iBroker.lib import mysql


class Construction_issues(object):
    def basic_function(self, list1, list2, count):
        # 将两个列表合并
        all_list = list1 + list2
        merge = [dict(t) for t in {tuple(d.items()) for d in all_list}]
        # 去除项目名称为空的
        merge_list = [item for item in merge if item["project_name"] is not None and item["project_name"].strip() != ""]

        result = {}  # 用于存储合并后的结果

        for item in merge_list:
            project_name = item["project_name"]
            project_count = item[count]

            if project_name in result:
                result[project_name] += project_count  # 如果项目名称已存在，将项目数量相加
            else:
                result[project_name] = project_count  # 如果项目名称不存在，直接添加项目名称和数量
        merged_result = [{"project_name": key, count: value} for key, value in result.items()]
        return merged_result

    def construction_issues_num(self):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT project_name, ticketid FROM week_create_info"
        sql1 = "SELECT project_name, COUNT(*) AS quality_unsolved_count FROM quality_technics_management " \
               "WHERE problem_end_time IS NULL GROUP BY project_name "
        sql2 = "SELECT ticketid AS project_name, COUNT(*) AS quality_unsolved_count FROM quality_technics_management " \
               "WHERE problem_end_time IS NULL GROUP BY ticketid "

        sql3 = "SELECT project_name, COUNT(*) AS quality_solved_count FROM quality_technics_management " \
               "WHERE problem_end_time IS not NULL GROUP BY project_name "
        sql4 = "SELECT ticketid AS project_name, COUNT(*) AS quality_solved_count FROM quality_technics_management " \
               "WHERE problem_end_time IS not NULL GROUP BY ticketid "

        sql5 = "SELECT project_name, COUNT(*) AS equipment_unsolved_count FROM equipment_management " \
               "WHERE problem_end_time IS NULL GROUP BY project_name "
        sql6 = "SELECT ticketid AS project_name, COUNT(*) AS equipment_unsolved_count FROM equipment_management " \
               "WHERE problem_end_time IS NULL GROUP BY ticketid "

        sql7 = "SELECT project_name, COUNT(*) AS equipment_solved_count FROM equipment_management " \
               "WHERE problem_end_time IS not NULL GROUP BY project_name "
        sql8 = "SELECT ticketid AS project_name, COUNT(*) AS equipment_solved_count FROM equipment_management " \
               "WHERE problem_end_time IS not NULL GROUP BY ticketid "

        sql9 = "SELECT project_name, COUNT(*) AS safety_unsolved_count FROM safety_problem " \
               "WHERE problem_end_time IS NULL GROUP BY project_name "
        sql10 = "SELECT ticketid AS project_name, COUNT(*) AS safety_unsolved_count FROM safety_problem " \
                "WHERE problem_end_time IS NULL GROUP BY ticketid "

        sql11 = "SELECT project_name, COUNT(*) AS safety_solved_count FROM safety_problem " \
                "WHERE problem_end_time IS not NULL GROUP BY project_name "
        sql12 = "SELECT ticketid AS project_name, COUNT(*) AS safety_solved_count FROM safety_problem " \
                "WHERE problem_end_time IS not NULL GROUP BY ticketid "
        project_name_list = db.get_all(sql)
        # 质量
        quality_unsolved_list1 = db.get_all(sql1)
        quality_unsolved_list2 = db.get_all(sql2)
        quality_solved_list1 = db.get_all(sql3)
        quality_solved_list2 = db.get_all(sql4)
        # 设备
        equipment_unsolved_list1 = db.get_all(sql5)
        equipment_unsolved_list2 = db.get_all(sql6)
        equipment_solved_list1 = db.get_all(sql7)
        equipment_solved_list2 = db.get_all(sql8)
        # 安全
        safety_unsolved_list1 = db.get_all(sql9)
        safety_unsolved_list2 = db.get_all(sql10)
        safety_solved_list1 = db.get_all(sql11)
        safety_solved_list2 = db.get_all(sql12)

        # 将ticketid换成项目名称
        # 质量
        for i in quality_unsolved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        for i in quality_solved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        # 设备
        for i in equipment_unsolved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        for i in equipment_solved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        # 安全
        for i in safety_unsolved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        for i in safety_solved_list2:
            for j in project_name_list:
                if i.get('project_name') == j.get('ticketid'):
                    i['project_name'] = j.get('project_name')
        # 调用自定义函数
        # 质量
        quality_unsolved_list = self.basic_function(quality_unsolved_list1, quality_unsolved_list2,
                                                    count="quality_unsolved_count")
        quality_solved_list = self.basic_function(quality_solved_list1, quality_solved_list2,
                                                  count="quality_solved_count")
        # 设备
        equipment_unsolved_list = self.basic_function(equipment_unsolved_list1, equipment_unsolved_list2,
                                                      count="equipment_unsolved_count")
        equipment_solved_list = self.basic_function(equipment_solved_list1, equipment_solved_list2,
                                                    count="equipment_solved_count")
        # 安全
        safety_unsolved_list = self.basic_function(safety_unsolved_list1, safety_unsolved_list2,
                                                   count="safety_unsolved_count")
        safety_solved_list = self.basic_function(safety_solved_list1, safety_solved_list2,
                                                 count="safety_solved_count")
        all_lists = [quality_unsolved_list, quality_solved_list, equipment_unsolved_list, equipment_solved_list,
                     safety_unsolved_list, safety_solved_list]
        result_list = []
        for i in range(len(all_lists)):
            for item in all_lists[i]:
                found = False
                for merged_item in result_list:
                    if item["project_name"] == merged_item["project_name"]:
                        merged_item.update(item)  # 更新已存在的字典项
                        found = True
                        break
                if not found:
                    result_list.append(item)  # 添加新的字典项

        # 确保每个不同的 project_name 都包含指定字段，不存在的值为 0
        for item in result_list:
            item.setdefault("quality_unsolved_count", 0)
            item.setdefault("quality_solved_count", 0)
            item.setdefault("equipment_unsolved_count", 0)
            item.setdefault("equipment_solved_count", 0)
            item.setdefault("safety_unsolved_count", 0)
            item.setdefault("safety_solved_count", 0)
            item['quality_count'] = item['quality_unsolved_count'] + item['quality_solved_count']
            item['equipment_count'] = item['equipment_unsolved_count'] + item['equipment_solved_count']
            item['safety_count'] = item['safety_unsolved_count'] + item['safety_solved_count']

        return result_list
