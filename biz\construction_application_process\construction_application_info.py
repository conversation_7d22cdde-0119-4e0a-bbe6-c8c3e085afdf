from iBroker.lib import mysql


# 施工准备阶段-报建流程
class ConstructionApplicationInfoQuery(object):

    # 查总控计划id(by project_name)
    # @staticmethod
    # def get_project_overall_control_plan_ticket_id(project_name):
    #     query_sql = (
    #         "select * from week_create_info where project_name = '%s' " % project_name
    #     )
    #     tb_db = mysql.new_mysql_instance("tbconstruct")
    #     query_result = tb_db.query(query_sql)
    #     ticket_id = ""
    #     if query_result:
    #         ticket_id = query_result[0]["ticketid"]
    #     return ticket_id

    # 查专项(二级)信息
    @staticmethod
    def get_special_work_info(project_name, seq_no):
        query_sql = (
            "select * from excel_data where project_name = '%s' and serial_number = '%s' "
            % (project_name, seq_no)
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        res_list = [
            # {
            #     "overall_control_plan_ticket_id": "",
            #     "seq_no": seq_no,
            #     "work_content": "",
            #     "construction_time": "",
            #     "start_time": "",
            #     "finish_time": "",
            #     "responsible_person": "",
            #     "output": "",
            #     "input": "",
            #     "construction_attribute": "",
            # }
        ]
        if query_result and query_result[0]:
            res_list = [
                {
                    "overall_control_plan_ticket_id": query_result[0].get("ticket_id"),
                    "seq_no": query_result[0]["serial_number"],
                    "work_content": query_result[0]["work_content"],
                    "construction_time": (
                        query_result[0]["construction_time"]
                        if "construction_time" in query_result[0]
                        else ""
                    ),
                    "start_time": query_result[0]["start_time"],
                    "finish_time": query_result[0]["completion_time"],
                    "responsible_person": query_result[0]["responsible_person"],
                    "output": query_result[0]["output_object"],
                    "input": query_result[0]["input_object"],
                    "construction_attribute": query_result[0]["construction_attribute"],
                }
            ]
        return res_list

    # 查项目报建流程(二级)信息
    @staticmethod
    def get_construction_application_second_level_info(project_name, seq_no):
        query_sql = (
            "select * from construction_application_second_level where project_name = '%s' and seq_no = '%s' "
            % (project_name, seq_no)
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        return query_result

    # 查分项分解任务(四级)实际开始时间、实际完成时间
    @staticmethod
    def get_subtasks_info_from_weekly_report(overall_control_plan_ticket_id, seq_no):
        query_sql = (
            "select * from schedule_management where ticketid = '%s' and serial_number = '%s' "
            % (overall_control_plan_ticket_id, seq_no)
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        res_list = {
            "actual_start_time": "",
            "actual_finish_time": "",
        }
        if query_result and query_result[0]:
            res_list = {
                "actual_start_time": query_result[0]["actual_start_time"],
                "actual_finish_time": query_result[0]["actual_finish_time"],
            }
        return res_list

    # "消防第三方测试报告"待办处理人获取-集成商
    @staticmethod
    def get_fire_report_handler(project_name):
        # 从项目启动团队组建获取 集成商
        # query_sql = (
        #     "select integrator from project_team_information_data where project_name = '%s' "
        #     % project_name
        # )
        # 从账号开通表获取 集成商-项目经理
        query_sql = (
            "select account from project_role_account where project_name = '%s' and role = '集成商-项目经理' "
            % project_name
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        fire_report_handler = ""
        if query_result and query_result[0]:
            # fire_report_handler = query_result[0].get("integrator")
            fire_report_handler = query_result[0].get("account")
        return fire_report_handler

    # 项目PM获取,找不到责任人的待办都给PM
    @staticmethod
    def get_PM(project_name):
        query_sql = f"SELECT PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        PM = ""
        if query_result and query_result[0]:
            PM = query_result[0].get("PM")
        return PM

    # 流程待办处理人获取(从project_role_account获取)
    @staticmethod
    def get_todo_handler(project_name, role):
        query_sql = (
            "select account from project_role_account "
            f"where project_name = '{project_name}' and role = '{role}' and del_flag = 0"
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        handler = ""
        if query_result:
            handler = query_result[len(query_result) - 1].get("account")
        else:
            handler = ConstructionApplicationInfoQuery.get_PM(project_name)
        return handler

    # 判断是否为合建项目
    @staticmethod
    def whether_is_rental_project(project_name):
        """
        判断是否为合建项目
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT construction_mode FROM risk_early_warning_data WHERE project_name='%s'"
            % project_name
        )
        construction_mode = db.get_row(sql)
        if not construction_mode:
            raise Exception("项目报建流程未找到对应项目的建设模式")
        if construction_mode.get("construction_mode") == "自建":
            is_hire = False
        else:
            is_hire = True
        return is_hire
