#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的数据合并脚本
先测试数据库连接，然后运行数据合并
"""

import pymysql
import logging
from datetime import datetime
import time
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_connection():
    """测试数据库连接并获取配置"""
    logger.info("🔍 测试数据库连接...")
    
    # 尝试不同的数据库配置
    configs = [
        {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'tbconstruct',
            'charset': 'utf8mb4'
        },
        {
            'host': '127.0.0.1',
            'user': 'root',
            'password': '',
            'database': 'tbconstruct',
            'charset': 'utf8mb4'
        },
        {
            'host': 'localhost',
            'user': 'root',
            'password': 'root',
            'database': 'tbconstruct',
            'charset': 'utf8mb4'
        }
    ]
    
    for i, config in enumerate(configs):
        try:
            logger.info(f"尝试配置 {i+1}: {config['host']}:{config['user']}")
            connection = pymysql.connect(**config)
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            # 测试查询
            cursor.execute("SELECT COUNT(*) as count FROM consolidated_equipment_data")
            result = cursor.fetchone()
            logger.info(f"✅ 连接成功！consolidated_equipment_data表中有 {result['count']} 条记录")
            
            cursor.close()
            connection.close()
            
            return config
            
        except Exception as e:
            logger.warning(f"❌ 配置 {i+1} 连接失败: {e}")
            continue
    
    logger.error("❌ 所有数据库配置都连接失败")
    return None

def test_table_structure():
    """测试表结构"""
    config = test_database_connection()
    if not config:
        return False
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 检查关键表是否存在
        tables = [
            'all_construction_ticket_data',
            'equipment_production_racking_process',
            'delivery_schedule_management_table',
            'equipment_category_mapping_relationship',
            'construct_plan_data',
            'payment_info'
        ]
        
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table} LIMIT 1")
                result = cursor.fetchone()
                logger.info(f"✅ 表 {table}: {result['count']} 条记录")
            except Exception as e:
                logger.warning(f"❌ 表 {table} 不存在或无法访问: {e}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试表结构失败: {e}")
        return False

def show_sample_data():
    """显示示例数据"""
    config = test_database_connection()
    if not config:
        return False
    
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 显示delivery_schedule_management_table中的示例数据
        cursor.execute("""
            SELECT project_name, device_type, expected_time_equipment, 
                   estimated_time_delivery, delivery_gap
            FROM delivery_schedule_management_table 
            WHERE expected_time_equipment IS NOT NULL 
            LIMIT 5
        """)
        results = cursor.fetchall()
        
        logger.info("📊 delivery_schedule_management_table 示例数据:")
        for row in results:
            logger.info(f"  项目: {row['project_name']}")
            logger.info(f"  设备类型: {row['device_type']}")
            logger.info(f"  预期时间: {row['expected_time_equipment']}")
            logger.info(f"  预计交付: {row['estimated_time_delivery']}")
            logger.info(f"  交付差距: {row['delivery_gap']}")
            logger.info("  ---")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 显示示例数据失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始测试优化的数据合并脚本")
    
    # 测试数据库连接
    if not test_database_connection():
        logger.error("❌ 数据库连接测试失败，请检查数据库配置")
        return False
    
    # 测试表结构
    if not test_table_structure():
        logger.error("❌ 表结构测试失败")
        return False
    
    # 显示示例数据
    if not show_sample_data():
        logger.error("❌ 示例数据显示失败")
        return False
    
    logger.info("🎉 所有测试通过！可以运行数据合并脚本")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
