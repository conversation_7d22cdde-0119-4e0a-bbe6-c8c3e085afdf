import ast
import datetime
import json

from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import <PERSON>TodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate, WorkflowContinue

from biz.cooperative_partner.cooperative_partner_general_method import CooperativePartnerGeneralMethod, \
    calling_external_interfaces, obtain_supplier_information
from biz.problem_rectification.modify_question import transform_url_list
from biz.utils.change_url import download_and_upload_file
from biz.utils.collaboration import collaboration_api_calling


def can_convert_to_list(appendix_url):
    """
    判断字符串是否能安全转换为列表
    
    参数:
        appendix_url: 要判断的字符串
        
    返回:
        bool: 是否能转换为列表
    """
    try:
        result = ast.literal_eval(appendix_url)
        return isinstance(result, list)  # 确保转换结果是列表类型
    except (ValueError, SyntaxError):
        # 捕获两种可能的异常：
        # 1. ValueError - 字符串格式不正确
        # 2. SyntaxError - 字符串包含不安全或无效语法
        return False


class FeedbackApprovalResultsTestPlatform(object):
    """
        反馈审批结果
    """

    def __init__(self):
        super(FeedbackApprovalResultsTestPlatform, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def feedback_approval_results_test_platform(self, Facilitator, whether_reject_not, explain):
        """
            反馈审批结果
        """
        question_ticket = self.ctx.variables.get("question_ticket")
        resp = gnetops.request(
            action="Verificationrectificationofresponsibleunit",
            method="QuestionSubmitCheckItem",
            ext_data={
                "ProcessUser": Facilitator,
                "IsReject": "驳回",
                "RejectReason": explain,
                "TicketId": question_ticket
            },
            scheme="ifob-fre"
        )
        self.workflow_detail.add_kv_table("回传信息", {"resp": resp})
        if resp != "提交成功":
            return {"code": -1, "msg": "驳回状态传递失败，请稍后重试或联系开发人员"}
        flow.complete_task(self.ctx.task_id, variables={'resp': resp})


class IsItOrNotCooperativePartnerWd(object):
    """
        是否为合作伙伴流程
        是的话是Dcops系统还是外部系统
    """

    def __init__(self):
        super(IsItOrNotCooperativePartnerWd, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def is_it_cooperative_partner_wd(self):
        """
            测试问题整改判断是否为合作伙伴
        """
        Facilitator = ''
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT integrators " \
              "FROM supplier_resources " \
              f"WHERE campus = '{campus}' " \
              f"AND project = '{project}' "
        result_list = db.get_all(sql)
        for row in result_list:
            Facilitator = row.get("integrators")
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map("cooperative_partner_process")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        # 首先判断是否为合作伙伴
        if Facilitator in ['维谛', '中兴']:
            is_or_not = '是'
        else:
            is_or_not = '否'

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        variables = {
            'Facilitator': Facilitator,
            'is_or_not': is_or_not,
            'system_type': system_type
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def is_it_cooperative_partner_js(self):
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map("cooperative_partner_process")
        # 获取合作伙伴项目名称
        cooperative_partner_project_name = config.get_config_map("cooperative_partner_project_name")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        Facilitator = ''
        is_or_not = ''
        system_type = ''
        project_name = self.ctx.variables.get("project_name")
        if project_name in cooperative_partner_project_name:
            db = mysql.new_mysql_instance("tbconstruct")
            sql = f"SELECT integrators FROM supplier_resources WHERE CONCAT(campus,project) = '{project_name}' "
            result_list = db.get_all(sql)
            for row in result_list:
                Facilitator = row.get("integrators")

            # 首先判断是否为合作伙伴
            if Facilitator in cooperative_partner_process:
                is_or_not = '是'

            # 在判断供应商为Dcops系统还是外部系统
            if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
                system_type = "Dcops"
            elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
                system_type = "外部"
            else:
                system_type = "未知"
        else:
            is_or_not = '否'

        variables = {
            'Facilitator': Facilitator,
            'is_or_not': is_or_not,
            'system_type': system_type
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


def get_personnel_account_information(project_name):
    db = mysql.new_mysql_instance("tbconstruct")
    # 获取厂家信息
    cj_sql = (
            "SELECT role,account,service_provider FROM project_role_account"
            " WHERE project_name='%s' AND role LIKE '厂家%%' and del_flag = '0'"
            % project_name
    )
    # 获取集成商信息
    jcs_sql = (
            "SELECT role,account,service_provider FROM project_role_account"
            " WHERE project_name='%s' AND role LIKE '集成商%%' and del_flag = '0'"
            % project_name
    )
    cj_data = db.get_all(cj_sql)
    jcs_data = db.get_all(jcs_sql)
    # 合并成一个新列表
    rectifier_data = cj_data + jcs_data

    return rectifier_data


class RectificationProblemPreparationWB(object):
    """
        问题整改
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(RectificationProblemPreparationWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_test_problem(self):
        """
            创建工单--问题整改(测试问题)
        """
        role_list = []
        problem_photo_list = []
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        # 此节点再七彩石配置中的key
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        problem_list = self.ctx.variables.get("problem_list")
        rectifier_data = get_personnel_account_information(project_name)
        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map("cooperative_partner_process_identification")
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '问题整改':
                    ProcessDefinitionKey = process.get('process_identification')

        for data in rectifier_data:
            role = data.get('role')
            account = data.get('account')

            role_list.append({
                "role": role,
                "account": account
            })
        for row in problem_list:
            if row.get('major') == '电气':
                row['major'] = '配电'
            major = row.get('major')
            proposer = row.get('proposer')
            room_id = row.get('room_id') or ''
            device_name = row.get('device_name')
            device_id = row.get('device_id')
            problem_location_and_description = row.get('problem_location_and_description')
            problem_put_forward_time = row.get('problem_put_forward_time')
            problem_level = row.get('problem_level')
            problem_type = row.get('problem_type')
            problem_photo = row.get('problem_photo')
            for item in problem_photo:
                url = item.get('url')
                problem_photo_list.append(url)
            create_test_problem_data = {
                "ProjectName": project_name,
                "TencentTicketId": ticket_id,
                "QuestionClassification": '测试问题',
                "Specialty": major,
                "Introducer": proposer,
                "DeviceName": device_name,
                "EquipmentNumber": device_id,
                "AreaNumber": '',
                "FunctionRoomNumber": room_id,
                "ProblemDescription": problem_location_and_description,
                "ProblemLevel": problem_level,
                "ProblemClassification": problem_type,
                "ProblemPhoto": problem_photo_list,
                "AccountabilityUnit": Facilitator,
                "QuestionTime": problem_put_forward_time,
                "RoleList": role_list
            }
        info = {}
        PartnerInstanceId = ""
        PartnerTicketId = 0
        if system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": create_test_problem_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                  "test_problem_rectification_process_initiated"
                }
            )
            PartnerTicketId = str(info['Data'])
            # elif Facilitator == '本贸':
            #     info = gnetops.request(
            #         action='Request',
            #         method='RequestToFacilitator',
            #         ext_data={
            #             "Facilitator": Facilitator,
            #             "ReqData": create_test_problem_data,
            #             "RequestMethod": "POST",
            #             "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/test_problem"
            #         }
            #     )
            #     PartnerTicketId = str(info['Data'])
        elif system_type == 'Dcops':
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": create_test_problem_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:测试问题整改",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:测试问题整改",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)
            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])
        variables = {
            'problem_info': str(info),
            'problem_data': create_test_problem_data,
            'create_test_problem_info': str(info),
            'create_test_problem_data': create_test_problem_data,
            'PartnerTicketId': PartnerTicketId,
            'PartnerInstanceId': PartnerInstanceId,
            "system_id": system_id
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_construction_problem(self):
        """
            创建工单--问题整改(建设问题)
        """
        role_list = []
        problem_photo_list = []
        problem_list = self.ctx.variables.get("problem_list")
        Creator = self.ctx.variables.get("Creator")
        next_op = self.ctx.variables.get("next_op")
        if next_op == 1:
            question_classification = '甲供设备问题'
        elif next_op == 2:
            question_classification = '质量工艺问题'
        elif next_op == 3:
            question_classification = '安全问题'
        project_name = self.ctx.variables.get("project_name")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        # 此节点再七彩石配置中的key
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        ticket_id = self.ctx.variables.get("ticket_id")
        rectifier_data = get_personnel_account_information(project_name)

        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map("cooperative_partner_process_identification")
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '问题整改':
                    ProcessDefinitionKey = process.get('process_identification')

        for data in rectifier_data:
            role = data.get('role')
            account = data.get('account')

            role_list.append({
                "role": role,
                "account": account
            })
        for row in problem_list:
            device_name = row.get('device_name')
            construction_unit = row.get('construction_unit')
            question_item = row.get('question_item')
            belong_professional = row.get('belong_professional')
            problem_location = row.get('problem_location')
            problem_description = row.get('problem_description')
            problem_level = row.get('problem_level')
            problem_category = row.get('problem_category')
            impact_classification = row.get('impact_classification')
            problem_impact = row.get('problem_impact')
            responsible_unit = row.get('responsible_unit')
            problem_put_forward_time = row.get('problem_put_forward_time')
            problem_photo = row.get('problem_photo')
            for item in problem_photo:
                url = item.get('url')
                problem_photo_list.append(url)
            create_construction_problem_data = {
                "ProjectName": project_name,
                "TencentTicketId": ticket_id,
                "QuestionClassification": question_classification,
                "BriefDescriptionProblem": question_item,
                "ConstructionOrganization": construction_unit,
                "Specialty": belong_professional,
                "Introducer": Creator,
                "DeviceName": device_name,
                "EquipmentNumber": '',
                "ProblemDescription": problem_location + problem_description,
                "ProblemLevel": problem_level,
                "ProblemClassification": problem_category,
                "ProblemPhoto": problem_photo_list,
                "ImpactClassification": impact_classification,
                "ConcreteImpact": problem_impact,
                "AccountabilityUnit": responsible_unit,
                "QuestionTime": problem_put_forward_time,
                "RoleList": role_list
            }
        info = {}
        PartnerTicketId = 0
        if system_type == "外部":
            if Facilitator == '维谛':
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": create_construction_problem_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                      "test_problem_rectification_process_initiated"
                    }
                )
                PartnerTicketId = str(info['Data'])
            elif Facilitator == '本贸':
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": create_construction_problem_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/construct_problem"
                    }
                )
                PartnerTicketId = str(info['Data'])
        elif system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": create_construction_problem_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:建设问题整改",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:建设问题整改",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)
            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])
        variables = {
            'problem_info': str(info),
            'problem_data': create_construction_problem_data,
            'create_test_problem_info': str(info),
            'create_test_problem_data': create_construction_problem_data,
            'rectifier_data': rectifier_data,
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_dimension_problem(self):
        """
            创建工单--问题整改(接维问题)
        """
        role_list = []
        problem_photo_list = []
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        # 此节点再七彩石配置中的key
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        problem_list = self.ctx.variables.get("problem_list")
        rectifier_data = get_personnel_account_information(project_name)

        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map("cooperative_partner_process_identification")
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '问题整改':
                    ProcessDefinitionKey = process.get('process_identification')

        for data in rectifier_data:
            role = data.get('role')
            account = data.get('account')

            role_list.append({
                "role": role,
                "account": account
            })

        for item in problem_list[0].get('issue_photo'):
            url = item.get('url')
            problem_photo_list.append(url)
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        create_dimension_problem_data = {
            "ProjectName": project_name,
            "TencentTicketId": ticket_id,
            "QuestionClassification": "接维问题",
            "Specialty": problem_list[0].get('major'),
            "Introducer": '接维平台',
            "DeviceName": '',
            "StorageZoneDeviceType": problem_list[0].get('device_type'),
            "EquipmentNumber": problem_list[0].get('device_number'),
            "ProblemDescription": problem_list[0].get('issue_description'),
            "ProblemLevel": problem_list[0].get('problem_level'),
            "ProblemClassification": problem_list[0].get('problem_type'),
            "ProblemPhoto": problem_photo_list,
            "QuestionTime": now_time,
            "RoleList": role_list

        }
        info = {}
        PartnerTicketId = 0
        if system_type == "外部":
            if Facilitator == '维谛':
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": create_dimension_problem_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                      "test_problem_rectification_process_initiated"
                    }
                )
                PartnerTicketId = str(info['Data'])
            elif Facilitator == '本贸':
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": create_dimension_problem_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/jiewei_problem"
                    }
                )
                PartnerTicketId = str(info['Data'])
        elif system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": create_dimension_problem_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:接维问题整改",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:接维问题整改",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)
            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])

        variables = {
            'problem_info': str(info),
            'problem_data': create_dimension_problem_data,
            'create_test_problem_info': str(info),
            'create_test_problem_data': create_dimension_problem_data,
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def rectification_results_review(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            整改结果审核反馈
        """
        problem_data = self.ctx.variables.get('problem_data', {})
        create_test_problem_data = self.ctx.variables.get('create_test_problem_data', {})
        ticket_id = self.ctx.variables.get("ticket_id")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意" or examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "测试平台" or dismiss_role == '接维平台':
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'

        if examine_approve_result == '驳回':
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            if problem_data.get('QuestionClassification') == "测试问题" or \
                    create_test_problem_data.get('QuestionClassification') == '测试问题':
                update_data = {
                    'solve_type': None,
                    'problem_end_time': None,
                    'causes_and_solutions': None,
                    'fix_photo_url': None,
                    'product_owner': None,
                    'appendix_url': None
                }
                conditions = {
                    'project_name': project_name,
                    'ticket_id': ticket_id
                }
                tb_db.update("problem_rectification", update_data, conditions)
                tb_db.commit()
            tb_db.commit()

        rectification_results_review_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、测试平台；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId,  # 合作伙伴工单号
        }
        info = {}
        method_name = "rectification_results_review"
        if Facilitator == '本贸':
            if problem_data.get('QuestionClassification') == '测试问题':
                method_name = "test_rectification_results_review"
            elif problem_data.get('QuestionClassification') in (
                        '甲供设备问题', '质量工艺问题', '安全问题'):
                method_name = "construct_rectification_results_review"
            elif problem_data.get('QuestionClassification') == '接维问题':
                method_name = "jiewei_rectification_results_review"
        code, message, data = collaboration_api_calling(facilitator=Facilitator,
                                                        request_data=rectification_results_review_data,
                                                        method_name=method_name,
                                                        error_proces_data={
                                                            'InstanceId':  self.ctx.instance_id,
                                                            'TicketId': self.ctx.variables.get('TicketId'),
                                                            'TaskId': self.ctx.task_id
                                                        })
        if code != 0 and code != 200:
            raise Exception(f"报错信息为： {message},数据为：{data}")
        # if system_type == '外部':
        #     if Facilitator == '本贸':
        #         if problem_data.get('QuestionClassification') == '测试问题':
        #             info = gnetops.request(
        #                 action='Request',
        #                 method='RequestToFacilitator',
        #                 ext_data={
        #                     "Facilitator": Facilitator,
        #                     "ReqData": rectification_results_review_data,
        #                     "RequestMethod": "POST",
        #                     "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
        #                                   "test_rectification_results_review"
        #                 }
        #             )
        #             if info.get('ErrorCode') != 0:
        #                 raise Exception(f'报错信息为：{info.get("Message")}')
        #         elif problem_data.get('QuestionClassification') in (
        #                 '甲供设备问题', '质量工艺问题', '安全问题'):
        #             info = gnetops.request(
        #                 action='Request',
        #                 method='RequestToFacilitator',
        #                 ext_data={
        #                     "Facilitator": Facilitator,
        #                     "ReqData": rectification_results_review_data,
        #                     "RequestMethod": "POST",
        #                     "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
        #                                   "construct_rectification_results_review"
        #                 }
        #             )
        #             if info.get('ErrorCode') != 0:
        #                 raise Exception(f'报错信息为：{info.get("Message")}')
        #         elif problem_data.get('QuestionClassification') == '接维问题':
        #             info = gnetops.request(
        #                 action='Request',
        #                 method='RequestToFacilitator',
        #                 ext_data={
        #                     "Facilitator": Facilitator,
        #                     "ReqData": rectification_results_review_data,
        #                     "RequestMethod": "POST",
        #                     "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
        #                                   "jiewei_rectification_results_review"
        #                 }
        #             )
        #             if info.get('ErrorCode') != 0:
        #                 raise Exception(f'报错信息为：{info.get("Message")}')
        #     else:
        #         info = gnetops.request(
        #             action='Request',
        #             method='RequestToFacilitator',
        #             ext_data={
        #                 "Facilitator": Facilitator,
        #                 "ReqData": rectification_results_review_data,
        #                 "RequestMethod": "POST",
        #                 "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
        #                               "rectification_results_review"
        #             }
        #         )
        #         if info.get('ErrorCode') != 0:
        #             raise Exception(f'报错信息为：{info.get("Message")}')
        # elif system_type == "Dcops":
        #     # 此节点再七彩石配置中的key
        #     interface_info = 'create_order'
        #     # 构造请求数据
        #     data = {
        #         "SystemId": "13",
        #         "Action": "Nbroker",
        #         "Method": "UrlProxy",
        #         "NbrokerData": {
        #             "context": {
        #                 "service_name": "TCForward",  # (*必填) 原样填写
        #                 "method_name": "index"  # (*必填) 原样填写
        #             },
        #             "args": {
        #                 "function_name": "rectification_results_review",  # (*必填) 云函数名称
        #                 "data": rectification_results_review_data,  # 传递给云函数的入参
        #                 "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
        #             }
        #         },
        #         "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        #     }
        #     # 获取非默认命名空间配置
        #     not_default_namespace = config.get_config_map("partner_method_not_default_namespace")
        #     if not_default_namespace.get(Facilitator):
        #         for key, value in not_default_namespace[Facilitator].items():
        #             if data["NbrokerData"]["args"]["function_name"] in value:
        #                 data["NbrokerData"]["args"]["namespace"] = key
        #     info = calling_external_interfaces(interface_info, data)

        #     result_str = info['data']

        #     # 获取 code 的值
        #     code = result_str.get('code')
        #     message = result_str.get('msg')
        #     if code != 0:
        #         raise Exception(f"报错信息为： {message}")

        is_or_not = self.ctx.variables.get("is_or_not")
        if not dismiss_role:
            is_or_not = "否"
        variables = {
            'rectification_results_review_info': str(info),
            'rectification_results_review_data': rectification_results_review_data,
            'is_or_not': is_or_not
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceWtzg(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceWtzg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_corrective_plan_feedback_new(self):
        """
            等待整改计划提交
        """
        pass

    def wait_rectification_plan_submission_new(self):
        """
            等待整改结果提交
        """
        pass

    def wait_corrective_plan_feedback(self):
        """
            等待整改计划提交
        """
        responsible_unit_list = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")
        problem_list = self.ctx.variables.get("problem_list")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"""
                    SELECT question_ticket,whether_total_liability,whether_reject_not,corrector,'explain',plan_resolution_date 
                    FROM problem_rectification 
                    WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'
                """
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('整改计划查询信息', {'message': result_list})
        if result_list:
            if system_type == 'Dcops':
                for row in result_list:
                    question_ticket = row.get('question_ticket')
                    whether_total_liability = row.get('whether_total_liability')
                    whether_reject_not = row.get('whether_reject_not')
                    corrector = row.get('corrector')
                    explain = row.get('explain')
                    plan_resolution_date = row.get('plan_resolution_date')

                    if whether_reject_not == 1:
                        variables = {
                            "question_ticket": question_ticket,
                            "whether_total_liability": whether_total_liability,
                            "whether_reject_not": whether_reject_not,
                            "corrector": corrector,
                            "explain": explain,
                            "plan_resolution_date": plan_resolution_date
                        }
                        flow.complete_task(self.ctx.task_id, variables=variables)
                        return {"success": True, "data": "流程已结束"}
                    elif whether_reject_not == 2:
                        if whether_total_liability == 2:
                            service_provider = None
                            if corrector:
                                role, account = corrector.split(";")
                                # 查询责任单位
                                db = mysql.new_mysql_instance("tbconstruct")
                                sql_all = f"""
                                                SELECT service_provider 
                                                FROM project_role_account 
                                                WHERE project_name='{project_name}' 
                                                AND del_flag = '0' 
                                                AND account = '{account}'
                                                AND role = '{role}'
                                            """
                                service_provider_list = db.get_all(sql_all)
                                for item in service_provider_list:
                                    service_provider = item.get('service_provider')

                                if service_provider:
                                    problem_list[0]['responsible_unit'] = service_provider

                                # 责任单位下拉框选项
                                db = mysql.new_mysql_instance("tbconstruct")
                                sql = f"SELECT supplier FROM category_supplier_info"
                                field_value_query = db.get_all(sql)
                                responsible_unit_set = set()
                                if field_value_query:
                                    for record in field_value_query:
                                        field_value = record.get("supplier")
                                        if field_value:
                                            field_value_list = field_value.split(";")
                                            for i in field_value_list:
                                                if i.strip():  # 确保不添加空字符串
                                                    responsible_unit_set.add(i.strip())

                                    # 将去重后的供应商信息加入 responsible_unit_list
                                for unit in responsible_unit_set:
                                    responsible_unit_list.append({
                                        "label": unit,
                                        "value": unit,
                                    })

                                variables = {
                                    "question_ticket": question_ticket,
                                    "whether_total_liability": whether_total_liability,
                                    "whether_reject_not": whether_reject_not,
                                    "role": role,
                                    "account": account,
                                    "corrector": corrector,
                                    "explain": explain,
                                    "plan_resolution_date": plan_resolution_date,
                                    "problem_list": problem_list,
                                    "responsible_unit_list": responsible_unit_list
                                }
                                flow.complete_task(self.ctx.task_id, variables=variables)
                                return {"success": True, "data": "流程已结束"}
                        elif whether_total_liability == 1:
                            variables = {
                                "question_ticket": question_ticket,
                                "whether_total_liability": whether_total_liability,
                                "whether_reject_not": whether_reject_not,
                                "corrector": corrector,
                                "explain": explain,
                                "plan_resolution_date": plan_resolution_date
                            }
                            flow.complete_task(self.ctx.task_id, variables=variables)
                            return {"success": True, "data": "流程已结束"}
            elif system_type == '外部':
                for row in result_list:
                    question_ticket = row.get('question_ticket')
                    whether_total_liability = row.get('whether_total_liability')
                    whether_reject_not = row.get('whether_reject_not')
                    corrector = row.get('corrector')
                    explain = row.get('explain')
                    plan_resolution_date = row.get('plan_resolution_date')

                    if whether_reject_not == 1:
                        variables = {
                            "question_ticket": question_ticket,
                            "whether_total_liability": whether_total_liability,
                            "whether_reject_not": whether_reject_not,
                            "corrector": corrector,
                            "explain": explain,
                            "plan_resolution_date": plan_resolution_date
                        }
                        flow.complete_task(self.ctx.task_id, variables=variables)
                        return {"success": True, "data": "流程已结束"}
                    elif whether_reject_not == 2:
                        if whether_total_liability == 2:
                            service_provider = None
                            if corrector:
                                role, account = corrector.split(";")
                                # 查询责任单位
                                db = mysql.new_mysql_instance("tbconstruct")
                                sql_all = f"""
                                                SELECT service_provider 
                                                FROM project_role_account 
                                                WHERE project_name='{project_name}' 
                                                AND del_flag = '0' 
                                                AND account = '{account}'
                                                AND role = '{role}'
                                            """
                                service_provider_list = db.get_all(sql_all)
                                for item in service_provider_list:
                                    service_provider = item.get('service_provider')
                                if service_provider:
                                    problem_list[0]['responsible_unit'] = service_provider

                                # 责任单位下拉框选项
                                db = mysql.new_mysql_instance("tbconstruct")
                                sql = f"SELECT supplier FROM category_supplier_info"
                                field_value_query = db.get_all(sql)
                                responsible_unit_set = set()
                                if field_value_query:
                                    for record in field_value_query:
                                        field_value = record.get("supplier")
                                        if field_value:
                                            field_value_list = field_value.split(";")
                                            for i in field_value_list:
                                                if i.strip():  # 确保不添加空字符串
                                                    responsible_unit_set.add(i.strip())

                                    # 将去重后的供应商信息加入 responsible_unit_list
                                for unit in responsible_unit_set:
                                    responsible_unit_list.append({
                                        "label": unit,
                                        "value": unit,
                                    })
                                variables = {
                                    "question_ticket": question_ticket,
                                    "whether_total_liability": whether_total_liability,
                                    "whether_reject_not": whether_reject_not,
                                    "role": role,
                                    "responsible": account,
                                    "corrector": corrector,
                                    "explain": explain,
                                    "plan_resolution_date": plan_resolution_date,
                                    "problem_list": problem_list,
                                    "responsible_unit_list": responsible_unit_list
                                }
                                flow.complete_task(self.ctx.task_id, variables=variables)
                                return {"success": True, "data": "流程已结束"}
                        elif whether_total_liability == 1:
                            variables = {
                                "question_ticket": question_ticket,
                                "whether_total_liability": whether_total_liability,
                                "whether_reject_not": whether_reject_not,
                                "corrector": corrector,
                                "explain": explain,
                                "plan_resolution_date": plan_resolution_date
                            }
                            flow.complete_task(self.ctx.task_id, variables=variables)
                            return {"success": True, "data": "流程已结束"}
        return {"success": False, "data": "未找到符合条件的数据"}

    def wait_rectification_plan_submission(self):
        """
            等待整改结果提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")
        problem_list = self.ctx.variables.get("problem_list")
        for row in problem_list:
            question_ticket = row.get('question_ticket')
            problem_type = row.get('problem_type')

        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"""
            SELECT plan_resolution_date,product_owner,responsible_unit,solve_type,problem_end_time,causes_and_solutions,
            fix_photo_url,appendix_url 
            FROM problem_rectification 
            WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'
        """
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('整改结果查询信息', {'message': result_list})

        if system_type == '外部':
            if result_list:
                # 检查每个记录是否所有字段都有值
                all_fields_filled = True
                for row in result_list:
                    # 检查每个字段是否为 None
                    for key, value in row.items():
                        if key == 'appendix_url':
                            continue
                        if value is None:
                            all_fields_filled = False
                            break
                    if not all_fields_filled:
                        break
                if all_fields_filled:
                    appendix_url_list = []
                    fix_photo_url_list = []
                    after_rectification_list = []
                    for row in result_list:
                        plan_resolution_date = row.get('plan_resolution_date')
                        product_owner = row.get('product_owner')
                        responsible_unit = row.get('responsible_unit')
                        solve_type = row.get('solve_type')
                        problem_end_time = row.get('problem_end_time').strftime("%Y-%m-%d")
                        causes_and_solutions = row.get('causes_and_solutions')
                        appendix_url = row.get('appendix_url')
                        fix_photo_url = row.get('fix_photo_url')

                        if appendix_url and can_convert_to_list(appendix_url):
                            appendix_url = ast.literal_eval(appendix_url)
                            if isinstance(appendix_url, list):
                                for i in appendix_url:
                                    appendix_url_list.append({
                                        "appendix_url": i
                                    })
                        if fix_photo_url and can_convert_to_list(fix_photo_url):
                            fix_photo_url = ast.literal_eval(fix_photo_url)
                            if isinstance(fix_photo_url, list):
                                for i in fix_photo_url:
                                    fix_photo_url_list.append({
                                        "fix_photo_url": i
                                    })
                        after_rectification_list.append({
                            "plan_resolution_date": plan_resolution_date,
                            "product_owner": product_owner,
                            "responsible_unit": responsible_unit,
                            "solve_type": solve_type,
                            "problem_end_time": problem_end_time,
                            "causes_and_solutions": causes_and_solutions,
                            "fix_photo_url": fix_photo_url_list
                        })
                    # 合并成一个新列表
                    merged_list = []
                    for prob, res in zip(problem_list, after_rectification_list):
                        new_dict = prob.copy()
                        new_dict.update(res)
                        merged_list.append(new_dict)

                    variables = {
                        "solve_list": merged_list,
                        "remark": '',
                        "question_ticket": question_ticket,
                        "problem_type": problem_type,
                        "solve_type": solve_type,
                        "product_owner": product_owner,
                        "causes_and_solutions": causes_and_solutions,
                        "responsible_unit": responsible_unit,
                        "appendix_url_list": appendix_url_list
                    }
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return {"success": True, "data": "流程已结束"}
                else:
                    return {"success": False, "data": "存在未填字段，流程未结束"}
        elif system_type == 'Dcops':
            if result_list:
                # 检查每个记录是否所有字段都有值
                all_fields_filled = True
                for row in result_list:
                    # 检查每个字段是否为 None
                    for key, value in row.items():
                        if key == 'appendix_url':
                            continue
                        if value is None:
                            all_fields_filled = False
                            break
                    if not all_fields_filled:
                        break
                if all_fields_filled:
                    appendix_url_list = []
                    fix_photo_url_list = []
                    after_rectification_list = []
                    for row in result_list:
                        plan_resolution_date = row.get('plan_resolution_date')
                        product_owner = row.get('product_owner')
                        responsible_unit = row.get('responsible_unit')
                        solve_type = row.get('solve_type')
                        problem_end_time = row.get('problem_end_time').strftime("%Y-%m-%d")
                        causes_and_solutions = row.get('causes_and_solutions')
                        appendix_url = row.get('appendix_url')
                        fix_photo_url = row.get('fix_photo_url')

                        if appendix_url and can_convert_to_list(appendix_url):
                            appendix_url = ast.literal_eval(appendix_url)
                            if isinstance(appendix_url, list):
                                for item in appendix_url:
                                    if isinstance(item, dict):
                                        file = item.get('response', {}).get('FileList', [])[0].get('url', '')
                                        appendix_url_list.append({
                                            "appendix_url": file
                                        })
                        if fix_photo_url and can_convert_to_list(fix_photo_url):
                            fix_photo_url = ast.literal_eval(fix_photo_url)
                            if isinstance(fix_photo_url, list):
                                for item in fix_photo_url:
                                    if isinstance(item, dict):
                                        file = item.get('response', {}).get('FileList', [])[0].get('url', '')
                                        fix_photo_url_list.append({
                                            "fix_photo_url": file
                                        })
                        after_rectification_list.append({
                            "plan_resolution_date": plan_resolution_date,
                            "product_owner": product_owner,
                            "responsible_unit": responsible_unit,
                            "solve_type": solve_type,
                            "problem_end_time": problem_end_time,
                            "causes_and_solutions": causes_and_solutions,
                            "fix_photo_url": fix_photo_url_list
                        })
                    # 合并成一个新列表
                    merged_list = []
                    for prob, res in zip(problem_list, after_rectification_list):
                        new_dict = prob.copy()
                        new_dict.update(res)
                        merged_list.append(new_dict)

                    variables = {
                        "remark": '',
                        "question_ticket": question_ticket,
                        "problem_type": problem_type,
                        "solve_type": solve_type,
                        "product_owner": product_owner,
                        "causes_and_solutions": causes_and_solutions,
                        "responsible_unit": responsible_unit,
                        "solve_list": merged_list,
                        "appendix_url_list": appendix_url_list
                    }
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return {"success": True, "data": "流程已结束"}
                else:
                    return {"success": False, "data": "未找到符合条件的数据"}


class PartnerCallInterfaceWtzgCs(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceWtzgCs, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def corrective_plan_feedback(self, TencentTicketId, ProjectName, WhetherTotalLiability, Explain,
                                 Corrector, PlannedResolutionTime, WhetherRejectNot=None):
        """
            整改计划反馈
        """
        # 校验 TencentTicketId 和 ProjectName
        if not Corrector:
            return {'code': -1, 'msg': '请填写整改人信息'}
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        if WhetherTotalLiability == 2:
            if not Explain:
                return {'code': -1, 'msg': '如不为总包责任，请填写说明'}
        else:
            if not PlannedResolutionTime:
                return {'code': -1, 'msg': '请填写计划整改时间'}
        if WhetherRejectNot == 1:
            if not Explain:
                return {'code': -1, 'msg': '驳回时需要填写说明'}
        # 通过工单号获取流程变量
        # 获取instance_id
        instance_id = ''
        TaskId = ''

        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                },
                "SearchCondition": {
                    "TicketId": TencentTicketId
                }
            }
        }

        ticket_info = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        if not instance_id:
            return {'code': -1, 'msg': '未找到该工单'}

        # 请求获取工作流日志
        data_instanceId = {
            "InstanceId": instance_id
        }

        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        for row in res:
            TaskName = row.get('TaskName')
            TaskStatus = row.get('TaskStatus')
            if (TaskName in ['等待测试问题-整改计划反馈', '等待整改计划提交']
                    and TaskStatus == "进行中"):
                TaskId = row.get('TaskId')

        # 通过instance_id获取流程变量
        process_variable_data = {
            "InstanceId": instance_id,
            "Keylist": ["rectifier_data", "problem_list", "problem_data",
                        "create_test_problem_data", "system_type", "Facilitator"]
        }
        process_variable_info = gnetops.request(
            action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
        )
        rectifier_data = process_variable_info['rectifier_data']
        problem_list = process_variable_info.get('problem_list', [])  # 默认空列表
        problem_data = process_variable_info.get('problem_data', {})  # 默认空字典
        create_test_problem_data = process_variable_info.get('create_test_problem_data', {})  # 默认空字典
        system_type = process_variable_info.get('system_type', '未知')
        Facilitator = process_variable_info.get('Facilitator')
        responsible_unit = ''
        responsible_unit_list = []

        if problem_data.get('QuestionClassification') == "测试问题" or \
                create_test_problem_data.get('QuestionClassification') == '测试问题':
            if WhetherTotalLiability == 2:

                # 通过工单号获取流程变量
                # 获取instance_id
                role, account = Corrector.split(";")
                for row in rectifier_data:
                    if role == row.get('role') and account == row.get('account'):
                        responsible_unit = row.get('service_provider')
                        if Facilitator == responsible_unit:
                            responsible_unit = f"{Facilitator}(厂家)"
            elif WhetherTotalLiability == 1:
                # db = mysql.new_mysql_instance("tbconstruct")
                # sql_all = f"""
                #             SELECT service_provider
                #             FROM project_role_account
                #             WHERE project_name='{ProjectName}' and del_flag = '0' and name = '{Corrector}'
                #         """
                # service_provider_list = db.get_all(sql_all)
                # for row in service_provider_list:
                #     responsible_unit = row.get('service_provider')
                responsible_unit = f"{Facilitator}(总包)"

            conditions = {
                'project_name': ProjectName,
                'ticket_id': TencentTicketId
            }
            update_data = {
                "whether_total_liability": WhetherTotalLiability,
                "`explain`": Explain,
                "responsible_unit": responsible_unit,
                "corrector": Corrector,
                "plan_resolution_date": PlannedResolutionTime,
                "whether_reject_not": WhetherRejectNot,
            }

            if WhetherRejectNot == 1:
                update_data.update({"cause_rejection": Explain})

            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("problem_rectification", update_data, conditions)
            tb_db.commit()

            sql = f"SELECT supplier FROM category_supplier_info"
            field_value_query = tb_db.get_all(sql)
            responsible_unit_set = set()
            if field_value_query:
                for record in field_value_query:
                    field_value = record.get("supplier")
                    if field_value:
                        field_value_list = field_value.split(";")
                        for i in field_value_list:
                            if i.strip():  # 确保不添加空字符串
                                responsible_unit_set.add(i.strip())

                # 将去重后的供应商信息加入 responsible_unit_list
            for unit in responsible_unit_set:
                responsible_unit_list.append({
                    "label": unit,
                    "value": unit,
                })
            sql = f"""
                    SELECT question_ticket 
                    FROM problem_rectification 
                    WHERE ticket_id = '{TencentTicketId}'and project_name = '{ProjectName}'
                """
            result_list = tb_db.get_row(sql)
            question_ticket = ""
            if result_list:
                question_ticket = result_list['question_ticket']
            if WhetherRejectNot == 1:
                variables = {
                    "question_ticket": question_ticket,
                    "whether_total_liability": WhetherTotalLiability,
                    "whether_reject_not": WhetherRejectNot,
                    "corrector": Corrector,
                    "explain": Explain,
                    "plan_resolution_date": PlannedResolutionTime,
                    "responsible_unit": responsible_unit
                }

            elif WhetherRejectNot == 2:
                if WhetherTotalLiability == 2:
                    problem_list[0]['responsible_unit'] = responsible_unit
                    variables = {
                        "question_ticket": question_ticket,
                        "whether_total_liability": WhetherTotalLiability,
                        "whether_reject_not": WhetherRejectNot,
                        "role": role,
                        "responsible": account,
                        "corrector": Corrector,
                        "explain": Explain,
                        "plan_resolution_date": PlannedResolutionTime,
                        "problem_list": problem_list,
                        "responsible_unit_list": responsible_unit_list,
                        "responsible_unit": responsible_unit
                    }
                elif WhetherTotalLiability == 1:
                    variables = {
                        "question_ticket": question_ticket,
                        "whether_total_liability": WhetherTotalLiability,
                        "whether_reject_not": WhetherRejectNot,
                        "corrector": Corrector,
                        "explain": Explain,
                        "plan_resolution_date": PlannedResolutionTime,
                        "responsible_unit": responsible_unit
                    }

            WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
            WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}
        else:
            if WhetherRejectNot == 1:
                question_ticket = ''
                if problem_data.get('QuestionClassification') == "测试问题":
                    question_ticket = problem_list[0]['question_ticket']
                variables = {
                    "whether_total_liability": WhetherTotalLiability,
                    "explain": Explain,
                    "responsible_unit": responsible_unit,
                    "question_ticket": question_ticket,
                    "corrector": Corrector,
                    "plan_resolution_date": PlannedResolutionTime,
                    "whether_reject_not": WhetherRejectNot,
                }
                WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                WorkflowContinue(task_id=TaskId).call()
            elif WhetherRejectNot == 2 or not WhetherRejectNot:
                if WhetherTotalLiability == 2:
                    question_ticket = ""

                    # 获取角色和账号
                    role, account = Corrector.split(";")

                    for row in rectifier_data:
                        if role == row.get('role') and account == row.get('account'):
                            responsible_unit = row.get('service_provider')
                    problem_list[0]['responsible_unit'] = responsible_unit

                    # 责任单位下拉框选项
                    db = mysql.new_mysql_instance("tbconstruct")
                    sql = f"SELECT supplier FROM category_supplier_info"
                    field_value_query = db.get_all(sql)
                    responsible_unit_set = set()
                    if field_value_query:
                        for record in field_value_query:
                            field_value = record.get("supplier")
                            if field_value:
                                field_value_list = field_value.split(";")
                                for i in field_value_list:
                                    if i.strip():  # 确保不添加空字符串
                                        responsible_unit_set.add(i.strip())

                        # 将去重后的供应商信息加入 responsible_unit_list
                    for unit in responsible_unit_set:
                        responsible_unit_list.append({
                            "label": unit,
                            "value": unit,
                        })
                    if problem_data.get('QuestionClassification') == "测试问题":
                        question_ticket = problem_list[0]['question_ticket']
                    variables = {
                        "whether_total_liability": WhetherTotalLiability,
                        "explain": Explain,
                        "responsible_unit": responsible_unit,
                        "problem_list": problem_list,
                        "responsible_unit_list": responsible_unit_list,
                        "corrector": Corrector,
                        "plan_resolution_date": PlannedResolutionTime,
                        "whether_reject_not": WhetherRejectNot,
                        "question_ticket": question_ticket,
                        "rectification_person": account,
                        "responsible": account
                    }
                    WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                    WorkflowContinue(task_id=TaskId).call()
                elif WhetherTotalLiability == 1:
                    question_ticket = ""
                    db = mysql.new_mysql_instance("tbconstruct")
                    sql_all = f"""
                                SELECT service_provider 
                                FROM project_role_account 
                                WHERE project_name='{ProjectName}' and del_flag = '0' and name = '{Corrector}'
                            """
                    service_provider_list = db.get_all(sql_all)
                    for row in service_provider_list:
                        responsible_unit = row.get('service_provider')

                    if problem_data.get('QuestionClassification') == "测试问题":
                        question_ticket = problem_list[0]['question_ticket']
                    variables = {
                        "whether_total_liability": WhetherTotalLiability,
                        "explain": Explain,
                        "responsible_unit": responsible_unit,
                        "corrector": Corrector,
                        "plan_resolution_date": PlannedResolutionTime,
                        "whether_reject_not": WhetherRejectNot,
                        "question_ticket": question_ticket,
                    }
                    WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                    WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}

    def rectification_result_submission(self, TencentTicketId, ProjectName, SolutionType, ProblemEndTime,
                                        CauseAndSolutionProcess, RectificationPhoto, ProductOwner, Appendix=None):
        """
            整改结果提交
        """
        question_ticket = ''
        problem_type = ''
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号 或 项目名称 缺失'}
        # 校验 SolutionType 和 CauseAndSolutionProcess
        if not SolutionType or not CauseAndSolutionProcess or not ProblemEndTime:
            return {'code': -1, 'msg': '解决类型 或 原因及解决过程 或 实际解决时间 缺失'}
        if not RectificationPhoto:
            return {'code': -1, 'msg': '整改照片缺失'}

        # 通过工单号获取流程变量
        # 获取instance_id
        instance_id = ''
        TaskId = ''
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                },
                "SearchCondition": {
                    "TicketId": TencentTicketId
                }
            }
        }

        ticket_info = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        if not instance_id:
            return {'code': -1, 'msg': '未找到该工单'}

        # 请求获取工作流日志
        data_instanceId = {
            "InstanceId": instance_id
        }

        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        for row in res:
            TaskName = row.get('TaskName')
            TaskStatus = row.get('TaskStatus')
            if (TaskName in ['等待测试问题-整改结果提交', '等待整改结果提交']
                    and TaskStatus == "进行中"):
                TaskId = row.get('TaskId')
        # 通过instance_id获取流程变量
        process_variable_data = {
            "InstanceId": instance_id,
            "Keylist": ["plan_resolution_date", "responsible_unit", "problem_list", "problem_data",
                        "create_test_problem_data"]
        }
        process_variable_info = gnetops.request(
            action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
        )
        plan_resolution_date = process_variable_info.get('plan_resolution_date')
        responsible_unit = process_variable_info.get('responsible_unit')
        problem_list = process_variable_info.get('problem_list', [])  # 默认空列表
        problem_data = process_variable_info.get('problem_data', {})  # 默认空字典
        create_test_problem_data = process_variable_info.get('create_test_problem_data', {})  # 默认空字典

        if problem_data.get('QuestionClassification') == "测试问题" or \
                create_test_problem_data.get('QuestionClassification') == '测试问题':
            if SolutionType not in ["维修", "换新", "参数设置", "其他"]:
                return {'code': -1, 'msg': '测试问题只能选择:维修、换新、参数设置、其他'}
            if ProductOwner not in ["间接蒸发制冷空调", "电池", "PDU", "高压直流柜", "综合配电柜", "IT机柜",
                                    "柴油发电机组", "中压配电柜", "低压配电柜", "变压器", "IT氟泵自然冷空调",
                                    "其他非10大件产品", "总包施工", "非总包施工", "智维动环"]:
                return {'code': -1,
                        'msg': '产品归属选择有误，只能选择: 间接蒸发制冷空调、电池、PDU、高压直流柜、综合配电柜、'
                               'IT机柜、柴油发电机组、中压配电柜、低压配电柜、变压器、IT氟泵自然冷空调、'
                               '其他非10大件产品、总包施工、非总包施工、智维动环'}
            problem_type = problem_list[0].get('problem_type')
            if problem_type == "安装工艺" and ProductOwner in ["间接蒸发制冷空调", "电池", "PDU",
                                                               "高压直流柜", "综合配电柜", "IT机柜",
                                                               "柴油发电机组", "中压配电柜", "低压配电柜", "变压器",
                                                               "IT氟泵自然冷空调"]:
                return {'code': -1, 'msg': '安装工艺问题不能选择: 间接蒸发制冷空调、电池、PDU、高压直流柜、综合配电柜、'
                                         'IT机柜、柴油发电机组、中压配电柜、低压配电柜、变压器、IT氟泵自然冷空调'}
            fix_photo_url_list = []
            upload_list = []
            for photo_url in RectificationPhoto:
                url_new = download_and_upload_file(photo_url)
                upload_list.append(url_new)
                fix_photo_url_list.append({
                    "fix_photo_url": url_new
                })
            fix_photo_url_str = ";".join(upload_list)

            question_ticket = problem_list[0]['question_ticket']

            appendix_url = ""
            if Appendix:
                conditions = {
                    'project_name': ProjectName,
                    'ticket_id': TencentTicketId
                }
                update_data = {
                }
                appendix_url = ";".join(Appendix)
                update_data["appendix_url"] = appendix_url
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("problem_rectification", update_data, conditions)
                tb_db.commit()

            after_rectification_list = []

            after_rectification_list.append({
                "plan_resolution_date": plan_resolution_date,
                "product_owner": ProductOwner,
                "responsible_unit": responsible_unit,
                "solve_type": SolutionType,
                "problem_end_time": ProblemEndTime,
                "causes_and_solutions": CauseAndSolutionProcess,
                "fix_photo_url": fix_photo_url_list
            })
            # 合并成一个新列表
            merged_list = []
            for prob, res in zip(problem_list, after_rectification_list):
                new_dict = prob.copy()
                new_dict.update(res)
                merged_list.append(new_dict)

            insert_data = {
                "appendix_url": appendix_url,
                "causes_and_solutions": CauseAndSolutionProcess,
                "fix_photo_url": fix_photo_url_str,
                "plan_resolution_date": plan_resolution_date,
                "problem_end_time": ProblemEndTime,
                "problem_type": problem_type,
                "responsible_unit": responsible_unit,
                "solve_type": SolutionType,
            }

            variables = {
                "solve_list": merged_list,
                "remark": '',
                "question_ticket": question_ticket,
                "problem_type": problem_type,
                "solve_type": SolutionType,
                "product_owner": ProductOwner,
                "causes_and_solutions": CauseAndSolutionProcess,
                "responsible_unit": responsible_unit,
                "appendix_url": appendix_url,
                "insert_data": insert_data,
                "fix_photo_url": json.dumps(upload_list)
            }

            WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
            WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}
        else:
            if not responsible_unit:
                responsible_unit = problem_list[0]['responsible_unit']

            if problem_data.get('QuestionClassification') == "测试问题":
                question_ticket = problem_list[0]['question_ticket']
            for row in problem_list:
                problem_type = row.get('problem_type')

            new_photo_url = []
            for photo_url in RectificationPhoto:
                url_one = download_and_upload_file(photo_url)
                new_photo_url.append(url_one)
            photo_result = ";".join(map(str, new_photo_url))

            appendix_url_list = []
            fix_photo_url_list = []
            after_rectification_list = []

            if Appendix:
                for i in Appendix:
                    appendix_url_list.append({
                        "appendix_url": i
                    })
            if new_photo_url:
                for i in new_photo_url:
                    fix_photo_url_list.append({
                        "photo": i
                    })
            photo = CooperativePartnerGeneralMethod()
            fix_photo = photo.photo_format_stitching(fix_photo_url_list)
            if problem_data.get('QuestionClassification') == "测试问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "causes_and_solutions": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "接维问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "causes_and_solutions": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "甲供设备问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "质量工艺问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "安全问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            # 合并成一个新列表
            merged_list = []
            for prob, res in zip(problem_list, after_rectification_list):
                new_dict = prob.copy()
                new_dict.update(res)
                merged_list.append(new_dict)

            variables = {
                "solve_list": merged_list,
                "remark": '',
                "question_ticket": question_ticket,
                "problem_type": problem_type,
                "solve_type": SolutionType,
                "product_owner": ProductOwner,
                "causes_and_solutions": CauseAndSolutionProcess,
                "responsible_unit": responsible_unit,
                "new_photo_url": new_photo_url,
                "photo_result": photo_result,
                "problem_end_time": ProblemEndTime,
                "appendix_url_list": appendix_url_list
            }
            WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
            WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}

    def corrective_plan_feedback_bak(self, TencentTicketId, ProjectName, WhetherTotalLiability, WhetherRejectNot,
                                     Explain,
                                     Corrector, PlannedResolutionTime):
        """
            整改计划反馈
        """
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        # 通过工单号获取流程变量
        # 获取instance_id
        instance_id = ''
        TaskId = ''
        instance_data = {
            "ResultColumns": {
                "InstanceId": ""
            },
            "SearchCondition": {'TicketId': TencentTicketId}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=instance_data, ext_data=extra_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        if not instance_id:
            return {'code': -1, 'msg': '未找到该工单'}

        # 请求获取工作流日志
        data_instanceId = {
            "InstanceId": instance_id
        }

        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        for row in res:
            TaskName = row.get('TaskName')
            if TaskName == '等待整改计划提交':
                TaskId = row.get('TaskId')

        # 通过instance_id获取流程变量
        process_variable_data = {
            "InstanceId": instance_id,
            "Keylist": ["rectifier_data", "problem_list", "problem_data", "create_test_problem_data"]
        }
        process_variable_info = gnetops.request(
            action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
        )
        rectifier_data = process_variable_info['rectifier_data']
        problem_list = process_variable_info.get('problem_list', [])  # 默认空列表
        problem_data = process_variable_info.get('problem_data', {})  # 默认空字典
        create_test_problem_data = process_variable_info.get('create_test_problem_data', {})  # 默认空字典
        ResponsibleUnit = ''
        responsible_unit_list = []

        if problem_data.get('QuestionClassification') == "测试问题" or \
                create_test_problem_data.get('QuestionClassification') == '测试问题':
            ResponsibleUnit = ''
            # 校验 TencentTicketId 和 ProjectName
            if not TencentTicketId or not ProjectName:
                return {'code': -1, 'msg': '工单号或项目名称缺失'}
            if WhetherTotalLiability == 2:
                if not Corrector:
                    return {'code': -1, 'msg': '不为总包责任请填写厂家账号'}

                if not Explain:
                    return {'code': -1, 'msg': '如不为总包责任，请填写说明'}

                # 通过工单号获取流程变量
                # 获取instance_id
                role, account = Corrector.split(";")
                instance_id = ''
                instance_data = {
                    "ResultColumns": {
                        "InstanceId": ""
                    },
                    "SearchCondition": {'TicketId': TencentTicketId}
                }

                extra_data = {"SchemaId": "ticket_base"}
                ticket_info = gnetops.request(
                    action="QueryData", method="Run", data=instance_data, ext_data=extra_data
                )
                res_list = ticket_info['List']

                for row in res_list:
                    instance_id = row.get('InstanceId')

                if not instance_id:
                    return {'code': -1, 'msg': '未找到该工单'}
                # 通过instance_id获取流程变量
                process_variable_data = {
                    "InstanceId": instance_id,
                    "Keylist": ["rectifier_data"]
                }
                process_variable_info = gnetops.request(
                    action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
                )
                rectifier_data = process_variable_info['rectifier_data']
                for row in rectifier_data:
                    if role == row.get('role') and account == row.get('account'):
                        ResponsibleUnit = row.get('service_provider')
            elif WhetherTotalLiability == 1:
                db = mysql.new_mysql_instance("tbconstruct")
                sql_all = f"""
                                    SELECT service_provider 
                                    FROM project_role_account 
                                    WHERE project_name='{ProjectName}' and del_flag = '0' and name = '{Corrector}'
                                """
                service_provider_list = db.get_all(sql_all)
                for row in service_provider_list:
                    ResponsibleUnit = row.get('service_provider')
            if WhetherRejectNot == 1:
                if not Explain:
                    return {'code': -1, 'msg': '驳回时需要填写说明'}

            conditions = {
                'project_name': ProjectName,
                'ticket_id': TencentTicketId
            }
            update_data = {
                "whether_total_liability": WhetherTotalLiability,
                "`explain`": Explain,
                "responsible_unit": ResponsibleUnit,
                "corrector": Corrector,
                "plan_resolution_date": PlannedResolutionTime,
                "whether_reject_not": WhetherRejectNot,
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("problem_rectification", update_data, conditions)
            tb_db.commit()
            return {'code': 200, 'msg': '成功'}
        else:
            if WhetherRejectNot == 1:
                question_ticket = ''
                if not Explain:
                    return {'code': -1, 'msg': '驳回时需要填写说明'}
                if problem_data.get('QuestionClassification') == "测试问题":
                    question_ticket = problem_list[0]['question_ticket']
                variables = {
                    "whether_total_liability": WhetherTotalLiability,
                    "explain": Explain,
                    "responsible_unit": ResponsibleUnit,
                    "question_ticket": question_ticket,
                    "corrector": Corrector,
                    "plan_resolution_date": PlannedResolutionTime,
                    "whether_reject_not": WhetherRejectNot,
                }
                WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                WorkflowContinue(task_id=TaskId).call()
            elif WhetherRejectNot == 2 or not WhetherRejectNot:
                if WhetherTotalLiability == 2:
                    question_ticket = ""
                    if not Corrector:
                        return {'code': -1, 'msg': '不是总包责任请填写厂家账号'}

                    if not Explain:
                        return {'code': -1, 'msg': '如不是总包责任，请填写说明'}

                    # 获取角色和账号
                    role, account = Corrector.split(";")

                    for row in rectifier_data:
                        if role == row.get('role') and account == row.get('account'):
                            ResponsibleUnit = row.get('service_provider')
                    problem_list[0]['responsible_unit'] = ResponsibleUnit

                    # 责任单位下拉框选项
                    db = mysql.new_mysql_instance("tbconstruct")
                    sql = f"SELECT supplier FROM category_supplier_info"
                    field_value_query = db.get_all(sql)
                    responsible_unit_set = set()
                    if field_value_query:
                        for record in field_value_query:
                            field_value = record.get("supplier")
                            if field_value:
                                field_value_list = field_value.split(";")
                                for i in field_value_list:
                                    if i.strip():  # 确保不添加空字符串
                                        responsible_unit_set.add(i.strip())

                        # 将去重后的供应商信息加入 responsible_unit_list
                    for unit in responsible_unit_set:
                        responsible_unit_list.append({
                            "label": unit,
                            "value": unit,
                        })
                    if problem_data.get('QuestionClassification') == "测试问题":
                        question_ticket = problem_list[0]['question_ticket']
                    variables = {
                        "whether_total_liability": WhetherTotalLiability,
                        "explain": Explain,
                        "responsible_unit": ResponsibleUnit,
                        "problem_list": problem_list,
                        "responsible_unit_list": responsible_unit_list,
                        "corrector": Corrector,
                        "plan_resolution_date": PlannedResolutionTime,
                        "whether_reject_not": WhetherRejectNot,
                        "question_ticket": question_ticket,
                        "rectification_person": account,
                        "responsible": account
                    }
                    WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                    WorkflowContinue(task_id=TaskId).call()
                elif WhetherTotalLiability == 1:
                    question_ticket = ""
                    db = mysql.new_mysql_instance("tbconstruct")
                    sql_all = f"""
                                SELECT service_provider 
                                FROM project_role_account 
                                WHERE project_name='{ProjectName}' and del_flag = '0' and name = '{Corrector}'
                            """
                    service_provider_list = db.get_all(sql_all)
                    for row in service_provider_list:
                        ResponsibleUnit = row.get('service_provider')

                    if problem_data.get('QuestionClassification') == "测试问题":
                        question_ticket = problem_list[0]['question_ticket']
                    variables = {
                        "whether_total_liability": WhetherTotalLiability,
                        "explain": Explain,
                        "responsible_unit": ResponsibleUnit,
                        "corrector": Corrector,
                        "plan_resolution_date": PlannedResolutionTime,
                        "whether_reject_not": WhetherRejectNot,
                        "question_ticket": question_ticket,
                    }
                    WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
                    WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}

    def rectification_result_submission_bak(self, TencentTicketId, ProjectName, SolutionType, ProblemEndTime,
                                            CauseAndSolutionProcess, RectificationPhoto, ProductOwner, Appendix):
        """
            整改结果提交
        """
        question_ticket = ''
        problem_type = ''
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号 或 项目名称 缺失'}
        # 校验 SolutionType 和 CauseAndSolutionProcess
        if not SolutionType or not CauseAndSolutionProcess or not ProblemEndTime:
            return {'code': -1, 'msg': '解决类型 或 原因及解决过程 或 实际解决时间 缺失'}
        if not RectificationPhoto:
            return {'code': -1, 'msg': '整改照片缺失'}

        # 通过工单号获取流程变量
        # 获取instance_id
        instance_id = ''
        TaskId = ''
        instance_data = {
            "ResultColumns": {
                "InstanceId": ""
            },
            "SearchCondition": {'TicketId': TencentTicketId}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=instance_data, ext_data=extra_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        if not instance_id:
            return {'code': -1, 'msg': '未找到该工单'}

        # 请求获取工作流日志
        data_instanceId = {
            "InstanceId": instance_id
        }

        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        for row in res:
            TaskName = row.get('TaskName')
            if TaskName == '等待整改结果提交':
                TaskId = row.get('TaskId')
        # 通过instance_id获取流程变量
        process_variable_data = {
            "InstanceId": instance_id,
            "Keylist": ["plan_resolution_date", "responsible_unit", "problem_list", "problem_data",
                        "create_test_problem_data"]
        }
        process_variable_info = gnetops.request(
            action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
        )
        plan_resolution_date = process_variable_info.get('plan_resolution_date')
        responsible_unit = process_variable_info.get('responsible_unit')
        problem_list = process_variable_info.get('problem_list', [])  # 默认空列表
        problem_data = process_variable_info.get('problem_data', {})  # 默认空字典
        create_test_problem_data = process_variable_info.get('create_test_problem_data', {})  # 默认空字典

        if problem_data.get('QuestionClassification') == "测试问题" or \
                create_test_problem_data.get('QuestionClassification') == '测试问题':
            new_photo_url = []
            for photo_url in RectificationPhoto:
                url_one = download_and_upload_file(photo_url)
                new_photo_url.append(url_one)
            new_photo_url = json.dumps(new_photo_url, ensure_ascii=False)

            conditions = {
                'project_name': ProjectName,
                'ticket_id': TencentTicketId
            }
            update_data = {
                "solve_type": SolutionType,
                "problem_end_time": ProblemEndTime,
                "causes_and_solutions": CauseAndSolutionProcess,
                "fix_photo_url": new_photo_url,
                "product_owner": ProductOwner
            }
            if Appendix:
                Appendix = json.dumps(Appendix, ensure_ascii=False)
                update_data["appendix_url"] = Appendix
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("problem_rectification", update_data, conditions)
            tb_db.commit()
            return {'code': 200, 'msg': '成功'}
        else:
            if not responsible_unit:
                responsible_unit = problem_list[0]['responsible_unit']

            if problem_data.get('QuestionClassification') == "测试问题":
                question_ticket = problem_list[0]['question_ticket']
            for row in problem_list:
                problem_type = row.get('problem_type')

            new_photo_url = []
            for photo_url in RectificationPhoto:
                url_one = download_and_upload_file(photo_url)
                new_photo_url.append(url_one)
            photo_result = ";".join(map(str, new_photo_url))

            appendix_url_list = []
            fix_photo_url_list = []
            after_rectification_list = []

            if Appendix:
                for i in Appendix:
                    appendix_url_list.append({
                        "appendix_url": i
                    })
            if new_photo_url:
                for i in new_photo_url:
                    fix_photo_url_list.append({
                        "photo": i
                    })
            photo = CooperativePartnerGeneralMethod()
            fix_photo = photo.photo_format_stitching(fix_photo_url_list)
            if problem_data.get('QuestionClassification') == "测试问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "causes_and_solutions": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "接维问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "causes_and_solutions": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "甲供设备问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "质量工艺问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            elif problem_data.get('QuestionClassification') == "安全问题":
                after_rectification_list.append({
                    "plan_resolution_date": plan_resolution_date,
                    "product_owner": ProductOwner,
                    "responsible_unit": responsible_unit,
                    "solve_type": SolutionType,
                    "problem_end_time": ProblemEndTime,
                    "rectification_reform_measures": CauseAndSolutionProcess,
                    "fix_photo": fix_photo
                })
            # 合并成一个新列表
            merged_list = []
            for prob, res in zip(problem_list, after_rectification_list):
                new_dict = prob.copy()
                new_dict.update(res)
                merged_list.append(new_dict)

            variables = {
                "solve_list": merged_list,
                "remark": '',
                "question_ticket": question_ticket,
                "problem_type": problem_type,
                "solve_type": SolutionType,
                "product_owner": ProductOwner,
                "causes_and_solutions": CauseAndSolutionProcess,
                "responsible_unit": responsible_unit,
                "new_photo_url": new_photo_url,
                "photo_result": photo_result,
                "problem_end_time": ProblemEndTime,
                "appendix_url_list": appendix_url_list
            }
            WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
            WorkflowContinue(task_id=TaskId).call()
            return {'code': 200, 'msg': '成功'}


class WtzgSupervisionAndReview(AjaxTodoBase):
    """
    监理审核
    """

    def __init__(self):
        super(WtzgSupervisionAndReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jl_remark = process_data.get("jl_remark")
        jl_review = process_data.get("jl_review")
        jl_rejection = process_data.get("jl_rejection")
        if jl_review:
            if jl_review == "驳回":
                if jl_rejection:
                    variables = {
                        "jl_review": jl_review,
                        "jl_rejection": jl_rejection,
                        "jl_remark": jl_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {"jl_review": jl_review, "jl_remark": jl_remark}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WtzgProjectManagementReview(AjaxTodoBase):
    """
    项管审核
    """

    def __init__(self):
        super(WtzgProjectManagementReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xg_remark = process_data.get("xg_remark")
        xg_review = process_data.get("xg_review")
        xg_rejection = process_data.get("xg_rejection")
        problem_data = self.ctx.variables.get('problem_data', {})
        create_test_problem_data = self.ctx.variables.get('create_test_problem_data', {})
        project_name = self.ctx.variables.get('project_name')
        causes_and_solutions = self.ctx.variables.get('causes_and_solutions')  # 问题及解决过程
        photo_result = self.ctx.variables.get('photo_result')  # 问题照片（字符串格式）
        new_photo_url = self.ctx.variables.get('new_photo_url')  # 问题照片（列表格式）
        problem_end_time = self.ctx.variables.get('problem_end_time')  # 问题解决时间
        whether_total_liability = self.ctx.variables.get('whether_total_liability')  # 是否为总包责任
        explain = self.ctx.variables.get('explain')  # 说明
        corrector = self.ctx.variables.get('corrector')  # 整改人
        plan_resolution_date = self.ctx.variables.get('plan_resolution_date')  # 计划解决时间
        solve_list = self.ctx.variables.get('solve_list')  # 计划解决时间
        ticket_id = self.ctx.variables.get('TicketId')
        if xg_review:
            if xg_review == "驳回":
                if xg_rejection:
                    variables = {
                        "xg_review": xg_review,
                        "xg_rejection": xg_rejection,
                        "xg_remark": xg_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                update_data = {}
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                if problem_data.get('QuestionClassification') == "甲供设备问题" or \
                        create_test_problem_data.get('QuestionClassification') == '甲供设备问题':
                    # 如果是’甲供设备问题‘，更新 equipment_management 表
                    conditions = {
                        'project_name': project_name,
                        't_id': ticket_id
                    }
                    update_data = {
                        "`explain`": explain,
                        "corrector": corrector,
                        "plan_resolution_date": plan_resolution_date,
                        "whether_total_liability": whether_total_liability,
                        "problem_end_time": problem_end_time,
                        "rectification_reform_measures": causes_and_solutions,
                        "photos_after_rectification": photo_result,
                    }
                    tb_db.update("equipment_management", update_data, conditions)
                    tb_db.commit()
                elif problem_data.get('QuestionClassification') == "质量工艺问题" or \
                        create_test_problem_data.get('QuestionClassification') == '质量工艺问题':
                    # 如果是‘质量工艺问题’，更新 quality_technics_management 表
                    conditions = {
                        'project_name': project_name,
                        't_id': ticket_id
                    }
                    update_data = {
                        "`explain`": explain,
                        "corrector": corrector,
                        "plan_resolution_date": plan_resolution_date,
                        "whether_total_liability": whether_total_liability,
                        "problem_end_time": problem_end_time,
                        "rectification_reform_measures": causes_and_solutions,
                        "photos_after_rectification": photo_result,
                    }
                    tb_db.update("quality_technics_management", update_data, conditions)
                    tb_db.commit()
                elif problem_data.get('QuestionClassification') == "安全问题" or \
                        create_test_problem_data.get('QuestionClassification') == '安全问题':
                    # 如果‘安全问题’，更新 safety_problem 表
                    conditions = {
                        'project_name': project_name,
                        't_id': ticket_id
                    }
                    update_data = {
                        "`explain`": explain,
                        "corrector": corrector,
                        "plan_resolution_date": plan_resolution_date,
                        "whether_total_liability": whether_total_liability,
                        "problem_end_time": problem_end_time,
                        "rectification_reform_measures": causes_and_solutions,
                        "photos_after_rectification": photo_result,
                    }
                    tb_db.update("safety_problem", update_data, conditions)
                    tb_db.commit()
                elif problem_data.get('QuestionClassification') == "接维问题" or \
                        create_test_problem_data.get('QuestionClassification') == '接维问题':
                    data = {}
                    new_photo_url = json.dumps(new_photo_url, ensure_ascii=False)
                    issue_close_photo = transform_url_list(new_photo_url)
                    if solve_list and len(solve_list) > 0:
                        for i in solve_list:
                            data["cause_solution_process"] = i.get("cause_solution_process")
                            data["solve_type"] = i.get("solve_type")
                            data["responsible_manufacturer"] = i.get("responsible_manufacturer")
                            data["plan_resolution_date"] = i.get("plan_resolution_date")
                            data["fix_photo_url"] = photo_result
                            data["issue_close_photo"] = issue_close_photo
                            data["problem_end_time"] = i.get("problem_end_time")

                variables = {"xg_review": xg_review, "xg_remark": xg_remark, 'update_data': update_data}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ModifyAgreement(object):
    """
        修改待办协议
    """

    def __init__(self):
        super(ModifyAgreement, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def modify_agreement(self, ticket_id_list):
        instance_id = ''
        a = {
            "name": "监理审核",
            "ajax_service": "WtzgSupervisionAndReview",
            "is_end": "{{ is_end }}",
            "basic_views": [],
            "todo_views": [],
            "operate_views": [
                {
                    "name": "提交",
                    "key": "submit_msg",
                    "type": "Button",
                    "callback_method": "end"
                },
                {
                    "type": "handover",
                    "handoverUsers": "count_all"
                }
            ],
            "operate_form_schema": {
                "form": {
                    "labelCol": 0,
                    "wrapperCol": 0,
                    "labelWidth": 120
                },
                "schema": {
                    "type": "object",
                    "properties": {
                        "solve_list": {
                            "type": "array",
                            "x-decorator": "FormItem",
                            "x-component": "ArrayTable",
                            "x-validator": [],
                            "x-component-props": {
                                "pagination": {
                                    "layout": "prev, pager, next, total",
                                    "pageSize": 10
                                },
                                "newPage": False
                            },
                            "x-decorator-props": {},
                            "x-designable-id": "5cw9qwuzm57",
                            "x-designable-source-name": "no6pipoipxp",
                            "x-index": 0,
                            "name": "solve_list",
                            "items": {
                                "type": "object",
                                "x-designable-id": "3np6spqxsxx",
                                "x-designable-source-name": "3np6spqxsxx",
                                "x-validator": [],
                                "properties": {
                                    "major": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "专业"
                                        },
                                        "name": "major",
                                        "x-designable-id": "h1ajc9f53gg",
                                        "x-designable-source-name": "h1ajc9f53gg",
                                        "x-index": 0,
                                        "properties": {
                                            "major": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-designable-id": "bl11wg4qpwo",
                                                "x-designable-source-name": "bl11wg4qpwo",
                                                "x-index": 0,
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "major"
                                            }
                                        }
                                    },
                                    "nrf4qadbnxc": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "提出人"
                                        },
                                        "x-designable-id": "nrf4qadbnxc",
                                        "x-designable-source-name": "nrf4qadbnxc",
                                        "x-index": 1,
                                        "properties": {
                                            "proposer": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "proposer",
                                                "x-designable-id": "05pu6bev8a9",
                                                "x-designable-source-name": "05pu6bev8a9",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "zxi5xom2ltc": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "雷达链接"
                                        },
                                        "x-designable-id": "zxi5xom2ltc",
                                        "x-designable-source-name": "zxi5xom2ltc",
                                        "x-index": 2,
                                        "x-reactions": {
                                            "dependencies": [
                                                {
                                                    "property": "value",
                                                    "type": "any"
                                                }
                                            ],
                                            "fulfill": {
                                                "run": "$effect(() => {\r\n  if ($form.values.subordinate_work_order_id) {\r\n    $self.visible = True\r\n  } else {\r\n    $self.visible = False\r\n  }\r\n}, [$form.values.subordinate_work_order_id])\r\n"
                                            }
                                        },
                                        "properties": {
                                            "subordinate_work_order_id": {
                                                "type": "string | object",
                                                "x-component": "Link",
                                                "x-decorator": "FormItem",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "x-decorator-props": {},
                                                "name": "subordinate_work_order_id",
                                                "x-designable-id": "62jxbvovcic",
                                                "x-designable-source-name": "lfpwbsri6oa",
                                                "x-index": 0,
                                                "x-reactions": {
                                                    "dependencies": [
                                                        {
                                                            "property": "value",
                                                            "type": "any"
                                                        }
                                                    ],
                                                    "fulfill": {
                                                        "run": "$effect(() => {\r\n  $self.component[1].content = \"点击跳转雷达链接\"\r\n  $self.component[1].href = $form.values.subordinate_work_order_id\r\n}, [])\r\n"
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    "7579cosm405": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "设备名称"
                                        },
                                        "x-designable-id": "7579cosm405",
                                        "x-designable-source-name": "7579cosm405",
                                        "x-index": 3,
                                        "properties": {
                                            "device_name": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "device_name",
                                                "x-designable-id": "bl7krnpsmsw",
                                                "x-designable-source-name": "bl7krnpsmsw",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "z3zso8yrqau": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "设备编号"
                                        },
                                        "x-designable-id": "z3zso8yrqau",
                                        "x-designable-source-name": "z3zso8yrqau",
                                        "x-index": 4,
                                        "properties": {
                                            "device_id": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "device_id",
                                                "x-designable-id": "2e6t7sx8udh",
                                                "x-designable-source-name": "2e6t7sx8udh",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "fw491y5onm0": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "房间编号"
                                        },
                                        "x-designable-id": "fw491y5onm0",
                                        "x-designable-source-name": "fw491y5onm0",
                                        "x-index": 5,
                                        "properties": {
                                            "room_id": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "room_id",
                                                "x-designable-id": "7isp4b1ee7z",
                                                "x-designable-source-name": "7isp4b1ee7z",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "b4ttdo83tdb": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "问题位置及描述"
                                        },
                                        "x-designable-id": "b4ttdo83tdb",
                                        "x-designable-source-name": "b4ttdo83tdb",
                                        "x-index": 6,
                                        "properties": {
                                            "problem_location_and_description": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "problem_location_and_description",
                                                "x-designable-id": "3ppfwc7dp1d",
                                                "x-designable-source-name": "3ppfwc7dp1d",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "79pgp09y8k7": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "问题级别"
                                        },
                                        "x-designable-id": "79pgp09y8k7",
                                        "x-designable-source-name": "79pgp09y8k7",
                                        "x-index": 7,
                                        "properties": {
                                            "problem_level": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "problem_level",
                                                "x-designable-id": "f1s0vp3c8un",
                                                "x-designable-source-name": "f1s0vp3c8un",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "2xul392l2x8": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "问题类别"
                                        },
                                        "x-designable-id": "2xul392l2x8",
                                        "x-designable-source-name": "2xul392l2x8",
                                        "x-index": 8,
                                        "properties": {
                                            "problem_type": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "problem_type",
                                                "x-designable-id": "zrx6bdg44ds",
                                                "x-designable-source-name": "zrx6bdg44ds",
                                                "x-index": 0,
                                                "required": True
                                            }
                                        }
                                    },
                                    "h1shzemza0q": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "问题照片"
                                        },
                                        "x-designable-id": "h1shzemza0q",
                                        "x-designable-source-name": "h1shzemza0q",
                                        "x-index": 9,
                                        "properties": {
                                            "problem_photo": {
                                                "type": "array",
                                                "title": "",
                                                "x-component": "Images",
                                                "x-decorator": "FormItem",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "x-decorator-props": {},
                                                "name": "problem_photo",
                                                "x-designable-id": "darsb4kcvmn",
                                                "x-designable-source-name": "dmoou4aanwl",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "i0ku7hwbmz1": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "原因及解决过程"
                                        },
                                        "x-designable-id": "i0ku7hwbmz1",
                                        "x-designable-source-name": "i0ku7hwbmz1",
                                        "x-index": 10,
                                        "properties": {
                                            "causes_and_solutions": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "causes_and_solutions",
                                                "x-designable-id": "a15crv4b1x1",
                                                "x-designable-source-name": "a15crv4b1x1",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "16qmbfc3z5r": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "解决类型"
                                        },
                                        "x-designable-id": "16qmbfc3z5r",
                                        "x-designable-source-name": "16qmbfc3z5r",
                                        "x-index": 11,
                                        "properties": {
                                            "solve_type": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "solve_type",
                                                "x-designable-id": "xdsovquzofc",
                                                "x-designable-source-name": "xdsovquzofc",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "2hp1rcda2nm": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "整改照片"
                                        },
                                        "x-designable-id": "2hp1rcda2nm",
                                        "x-designable-source-name": "h1shzemza0q",
                                        "x-index": 12,
                                        "properties": {
                                            "fix_photo_url": {
                                                "type": "array",
                                                "x-decorator": "FormItem",
                                                "x-component": "ArrayTable",
                                                "x-validator": [],
                                                "x-component-props": {
                                                    "pagination": {
                                                        "layout": "prev, pager, next, total",
                                                        "pageSize": 10
                                                    },
                                                    "newPage": False
                                                },
                                                "x-decorator-props": {},
                                                "name": "fix_photo_url",
                                                "x-designable-id": "t9dx4tb9m7q",
                                                "x-designable-source-name": "kuuevpa18ww",
                                                "x-index": 0,
                                                "items": {
                                                    "type": "object",
                                                    "x-validator": [],
                                                    "x-designable-id": "no2tz37mlbt",
                                                    "x-designable-source-name": "no2tz37mlbt",
                                                    "properties": {
                                                        "rsh7490uurr": {
                                                            "type": "void",
                                                            "x-component": "ArrayTable.Column",
                                                            "x-component-props": {
                                                                "title": ""
                                                            },
                                                            "x-designable-id": "rsh7490uurr",
                                                            "x-designable-source-name": "rsh7490uurr",
                                                            "x-index": 0,
                                                            "properties": {
                                                                "fix_photo_url": {
                                                                    "type": "string | object",
                                                                    "x-component": "Link",
                                                                    "x-decorator": "FormItem",
                                                                    "x-validator": [],
                                                                    "x-component-props": {},
                                                                    "x-decorator-props": {},
                                                                    "name": "fix_photo_url",
                                                                    "x-designable-id": "6rb104aavuz",
                                                                    "x-designable-source-name": "e461ibqvtdv",
                                                                    "x-index": 0
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    "n8kpw02mwsf": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "产品归属"
                                        },
                                        "x-designable-id": "n8kpw02mwsf",
                                        "x-designable-source-name": "n8kpw02mwsf",
                                        "x-index": 13,
                                        "properties": {
                                            "product_owner": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "product_owner",
                                                "x-designable-id": "masxiufrzvs",
                                                "x-designable-source-name": "masxiufrzvs",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "12zo07xl0ss": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "责任单位"
                                        },
                                        "x-designable-id": "12zo07xl0ss",
                                        "x-designable-source-name": "12zo07xl0ss",
                                        "x-index": 14,
                                        "properties": {
                                            "responsible_unit": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "responsible_unit",
                                                "x-designable-id": "emsa7vsala7",
                                                "x-designable-source-name": "emsa7vsala7",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "2qom09w6dzq": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "问题提出时间"
                                        },
                                        "x-designable-id": "2qom09w6dzq",
                                        "x-designable-source-name": "2qom09w6dzq",
                                        "x-index": 15,
                                        "properties": {
                                            "problem_put_forward_time": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "problem_put_forward_time",
                                                "x-designable-id": "rk8gxkjhwio",
                                                "x-designable-source-name": "rk8gxkjhwio",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "d0jyy3r7t9g": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "计划解决日期"
                                        },
                                        "x-designable-id": "d0jyy3r7t9g",
                                        "x-designable-source-name": "2qom09w6dzq",
                                        "x-index": 16,
                                        "properties": {
                                            "plan_resolution_date": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "plan_resolution_date",
                                                "x-designable-id": "1nqs6ayg7au",
                                                "x-designable-source-name": "rk8gxkjhwio",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "a2yv17ii20u": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "实际解决时间"
                                        },
                                        "x-designable-id": "a2yv17ii20u",
                                        "x-designable-source-name": "2qom09w6dzq",
                                        "x-index": 17,
                                        "properties": {
                                            "problem_end_time": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "problem_end_time",
                                                "x-designable-id": "xrd3hv2fsdz",
                                                "x-designable-source-name": "rk8gxkjhwio",
                                                "x-index": 0
                                            }
                                        }
                                    },
                                    "8bsbjsqpbc7": {
                                        "type": "void",
                                        "x-component": "ArrayTable.Column",
                                        "x-component-props": {
                                            "title": "备注"
                                        },
                                        "x-designable-id": "8bsbjsqpbc7",
                                        "x-designable-source-name": "8bsbjsqpbc7",
                                        "x-index": 18,
                                        "properties": {
                                            "remark": {
                                                "type": "string",
                                                "x-component": "Text",
                                                "x-validator": [],
                                                "x-component-props": {},
                                                "name": "remark",
                                                "x-designable-id": "zex04ri0051",
                                                "x-designable-source-name": "zex04ri0051",
                                                "x-index": 0
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "21ncalqlmpf": {
                            "type": "void",
                            "x-component": "FormLayout",
                            "x-component-props": {},
                            "x-reactions": {
                                "dependencies": [
                                    {
                                        "property": "value",
                                        "type": "any"
                                    }
                                ],
                                "fulfill": {
                                    "run": "$effect(() => {\n  if ($form.values.show === 2) {\n    $self.visible = True\n  } else {\n    $self.visible = False\n  }\n}, [$form.values.show])\n"
                                }
                            },
                            "x-designable-id": "21ncalqlmpf",
                            "x-designable-source-name": "16x5yrs8525",
                            "x-index": 1,
                            "properties": {
                                "24z6nxgivoy": {
                                    "title": "",
                                    "type": "string",
                                    "x-decorator": "FormItem",
                                    "x-component": "Input",
                                    "x-validator": [],
                                    "x-component-props": {},
                                    "x-decorator-props": {},
                                    "default": "未上传整改照片",
                                    "x-designable-id": "24z6nxgivoy",
                                    "x-designable-source-name": "i2woybn9o1i",
                                    "x-index": 0,
                                    "x-pattern": "readPretty"
                                },
                                "no_photos": {
                                    "title": "备注",
                                    "type": "string",
                                    "x-decorator": "FormItem",
                                    "x-component": "Input",
                                    "x-validator": [],
                                    "x-component-props": {},
                                    "x-decorator-props": {},
                                    "name": "no_photos",
                                    "x-designable-id": "fqjm0qkc3zk",
                                    "x-designable-source-name": "i2woybn9o1i",
                                    "x-index": 1,
                                    "x-pattern": "readPretty"
                                }
                            }
                        },
                        "jl_remark": {
                            "title": "备注",
                            "type": "string",
                            "x-decorator": "FormItem",
                            "x-component": "Input.TextArea",
                            "x-validator": [],
                            "x-component-props": {},
                            "x-decorator-props": {},
                            "name": "jl_remark",
                            "x-designable-id": "nmje8kdzx9d",
                            "x-designable-source-name": "8ddz7pqn6ve",
                            "x-index": 2
                        },
                        "jl_rejection": {
                            "title": "驳回原因",
                            "type": "string",
                            "x-decorator": "FormItem",
                            "x-component": "Input.TextArea",
                            "x-validator": [],
                            "x-component-props": {},
                            "x-decorator-props": {},
                            "x-designable-id": "1znaykznjd9",
                            "x-designable-source-name": "943domxzjf9",
                            "x-index": 3,
                            "name": "jl_rejection"
                        },
                        "jl_review": {
                            "type": "string | number",
                            "title": "监理审核",
                            "x-decorator": "FormItem",
                            "x-component": "Radio.Group",
                            "enum": [
                                {
                                    "label": "通过",
                                    "value": "通过"
                                },
                                {
                                    "label": "驳回",
                                    "value": "驳回"
                                }
                            ],
                            "x-validator": [],
                            "x-component-props": {},
                            "x-decorator-props": {},
                            "name": "jl_review",
                            "x-designable-id": "6xkjiu63x6y",
                            "x-designable-source-name": "g6qqgcl3yx1",
                            "x-index": 4,
                            "required": True
                        }
                    },
                    "x-designable-id": "z1bqjhepfkj",
                    "x-designable-source-name": "z1bqjhepfkj"
                }
            },
            "operate_form_fields": "solve_list;jl_rejection;xg_rejection;jl_remark;no_photos;show;subordinate_work_order_id",
            "operate_form_data": {
                "solve_list": "{{ctx.variables.solve_list}}",
                "jl_rejection": "{{ctx.variables.jl_rejection}}",
                "xg_rejection": "{{ctx.variables.xg_rejection}}",
                "jl_remark": "{{ctx.variables.jl_remark}}",
                "no_photos": "{{ctx.variables.no_photos}}",
                "show": "{{ctx.variables.show}}",
                "subordinate_work_order_id": "{{ctx.variables.subordinate_work_order_id}}"
            }
        }

        json_string = json.dumps(a, ensure_ascii=False, indent=4)

        for row in ticket_id_list:
            ticket_id = row
            # 通过工单号获取instance_id
            data = {
                "ResultColumns": {
                    "InstanceId": ""
                },
                "SearchCondition": {'TicketId': ticket_id}
            }

            extra_data = {"SchemaId": "ticket_base"}

            ticket_info = gnetops.request(
                action="QueryData", method="Run", data=data, ext_data=extra_data
            )
            res_list = ticket_info['List']

            for item in res_list:
                instance_id = item.get('InstanceId')

            if not instance_id:
                return {'code': -1, 'msg': '未找到instance_id'}
            if instance_id:
                data_instanceId = {
                    "InstanceId": instance_id
                }
                # 请求获取工作流日志
                res_list = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
                for item in res_list:
                    TaskName = item.get('TaskName')
                    TaskId = item.get('TaskId')
                    if TaskName == '监理审核':
                        contract_info = gnetops.request(action="Flow", method="SetVariables",
                                                        data={"InstanceId": f"{instance_id}",
                                                              "Variables": {
                                                                  f"protocol_data.{TaskId}"
                                                                  : json_string
                                                              }})
