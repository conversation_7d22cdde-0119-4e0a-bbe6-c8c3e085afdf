import ast
import copy
import datetime
import hashlib
import hmac
import json
import re
import threading
import time

from iBroker.lib import mysql, curl, config
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.logone import logger
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate

from biz.project_interface.organizational_structure_membership_details import refresh_corresponding_account_of_role
from biz.utils.change_url import test_download_link, download_and_upload_file, transform_url_list


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers,
                     timeout=60)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class RequestMergerInterface(object):
    """
        设备到货管理
        腾讯方调用接口：输出数据
    """

    def __init__(self):
        super(RequestMergerInterface, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_order_A(self, device_name, project_name, category_name, supply_type):
        """
            创建工单--甲供
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")

        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map("cooperative_partner_process_identification")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '设备生产跟踪-甲供':
                    ProcessDefinitionKey = process.get('process_identification')

        PartnerInstanceId = ''
        if system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": {
                        "DeviceName": device_name,
                        "ProjectName": project_name,
                        "SupplyType": supply_type,
                        "CategoryName": category_name,
                        "TencentTicketId": ticket_id
                    },
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}-{device_name}:合作版设备生产跟踪",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}-{device_name}:合作版设备生产跟踪",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)
            ticket_id = self.ctx.variables.get("ticket_id")
            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])
        elif system_type == "外部":
            data = {
                "TencentTicketId": ticket_id,
                "SupplyType": supply_type,
                "ProjectName": project_name,
                "DeviceName": device_name,
                "CategoryName": category_name
            }
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/CreateTCdelivery"
                }
            )
            PartnerTicketId = 0
            if 'Data' in info:
                PartnerTicketId = str(info['Data'])
            elif 'FlowID' in info:
                PartnerTicketId = str(info['FlowID'])
        data_dict = {
            'ticket_id': ticket_id,
            'project_name': project_name,
            'equipment_type': supply_type,
            'device_name': device_name,
            'partner_ticket_id': PartnerTicketId,
            'partner_instance_id': PartnerInstanceId
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_production_racking_process", data_dict)
        tb_db.commit()
        variables = {
            'create_order': str(info),
            'system_id': system_id,
            'system_type': system_type,
            'PartnerInstanceId': PartnerInstanceId,
            'PartnerTicketId': PartnerTicketId,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_order_B(self, device_name, project_name, category_name, supply_type):
        """
            创建工单--乙供
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map("cooperative_partner_process_identification")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '设备生产跟踪-乙供':
                    ProcessDefinitionKey = process.get('process_identification')

        PartnerInstanceId = ''
        if system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": {
                        "DeviceName": device_name,
                        "ProjectName": project_name,
                        "SupplyType": supply_type,
                        "CategoryName": category_name,
                        "TencentTicketId": ticket_id
                    },
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}-{device_name}:合作版设备生产跟踪",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}-{device_name}:合作版设备生产跟踪",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mtdcwang;v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mtdcwang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)

            ticket_id = self.ctx.variables.get("ticket_id")
            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])
        elif system_type == "外部":
            data = {
                "TencentTicketId": ticket_id,
                "SupplyType": supply_type,
                "ProjectName": project_name,
                "DeviceName": device_name,
                "CategoryName": category_name
            }
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/CreateTCdelivery"
                }
            )
            PartnerTicketId = 0
            if 'Data' in info:
                PartnerTicketId = str(info['Data'])
            elif 'FlowID' in info:
                PartnerTicketId = str(info['FlowID'])

        data_dict = {
            'ticket_id': ticket_id,
            'project_name': project_name,
            'equipment_type': supply_type,
            'device_name': device_name,
            'partner_ticket_id': PartnerTicketId,
            'partner_instance_id': PartnerInstanceId
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_production_racking_process", data_dict)
        tb_db.commit()
        variables = {
            'system_id': system_id,
            'create_order': str(info),
            'system_type': system_type,
            'PartnerInstanceId': PartnerInstanceId,
            'PartnerTicketId': PartnerTicketId,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 甲供计划
    def supply_equipment_plan(self, project_name, device_name, testing_plan_list):
        """
            甲供设备计划同步
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        supplier = self.ctx.variables.get("supplier")
        system_id = self.ctx.variables.get("system_id")
        # 获取传递数据
        if testing_plan_list:
            passing_parameters = {
                "ProjectName": project_name,  # 项目名称
                "DeviceName": device_name,  # 设备名称
                "Supplier": supplier,  # 供应商
                "ProductionPreparationPlan": testing_plan_list[0].get("production_preparation"),  # 生产准备完成时间
                "PrepareMaterialsPlan": testing_plan_list[0].get("prepare_materials"),  # 备料完成时间
                "ProductionPlan": testing_plan_list[0].get("production"),  # 生产完成时间
                "FactoryInspectionPlan": testing_plan_list[0].get("factory_inspection"),  # 出厂检验完成时间
                "ShippingApplicationPlan": testing_plan_list[0].get("shipping_application"),  # 发货完成时间
                "ReceivingSignPlan": testing_plan_list[0].get("receiving_sign"),  # 到货完成时间
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "supply_equipment_plan",  # (*必填) 云函数名称
                            "data": passing_parameters,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": passing_parameters,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/supply_equipment_plan"
                    }, timeout=1800
                )

                if info.get('ErrorCode') != 0:
                    raise Exception(f'报错信息为：{info.get("Message")}')

            variables = {
                'supply_equipment_plan': str(info),
                'production_preparation': testing_plan_list[0].get("production_preparation"),
                'prepare_materials': testing_plan_list[0].get("prepare_materials"),
                'production': testing_plan_list[0].get("production"),
                'factory_inspection': testing_plan_list[0].get("factory_inspection"),
                'shipping_application': testing_plan_list[0].get("shipping_application"),
                'receiving_sign': testing_plan_list[0].get("receiving_sign")
            }

            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('传递甲供计划', {"data": "甲供计划为空"})

    def supply_production_preparation_progress(self, project_name, device_name,
                                               production_preparation_list):
        """
            甲供设备生产准备进度同步
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        # 获取传递数据
        if production_preparation_list:
            passing_parameters = {
                "ProjectName": project_name,  # 项目名称
                "DeviceName": device_name,  # 设备名称
                "PoIssueTime": production_preparation_list[0].get("real_time"),  # PO下发时间
                "DesignConfirmationTime": production_preparation_list[0].get("confirm_time"),  # 设备确认时间
                "ProductionPreparation": production_preparation_list[0].get("production_preparation_time"),  # 生产准备完成时间
                "Remark": production_preparation_list[0].get("remark"),  # 备注
                "Appendix": production_preparation_list[0].get("upload_info"),  # 附件
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "supply_production_preparation_progress",  # (*必填) 云函数名称
                            "data": passing_parameters,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                # 初始化 URL 列表
                url_list = []

                # 遍历 production_preparation_list 中的每个元素
                for item in production_preparation_list[0].get("upload_info", []):
                    # 获取 response 中的 FileList
                    file_list = item.get("response", {}).get("FileList", [])

                    # 遍历 FileList 并提取 URL
                    for file_info in file_list:
                        url = file_info.get("url")
                        if url:  # 确保 URL 存在
                            url_list.append(url)
                data = {
                    "ProjectName": project_name,  # 项目名称
                    "DeviceName": device_name,  # 设备名称
                    "PoIssueTime": production_preparation_list[0].get("real_time"),  # PO下发时间
                    "DesignConfirmationTime": production_preparation_list[0].get("confirm_time"),  # 设备确认时间
                    # 生产准备完成时间
                    "ProductionPreparation": production_preparation_list[0].get("production_preparation_time"),
                    "Remark": production_preparation_list[0].get("remark"),  # 备注
                    "Appendix": url_list,  # 附件
                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                }
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                      "supply_production_preparation_progress"
                    }, timeout=1800
                )

                if info.get('ErrorCode') != 0:
                    raise Exception(f'报错信息为：{info.get("Message")}')

            variables = {
                'supply_production_preparation_progress': str(info)
            }

            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('传递甲供设备生产进度', {"data": "甲供设备生产进度为空"})

    def supply_equipment_progress(self, project_name, device_name, schedule_list):
        """
            甲供设备进度同步
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        # 获取传递数据
        if schedule_list:
            if system_type == "Dcops":
                ProgressStage = 0
                if schedule_list[0].get("type") == '备料':
                    ProgressStage = 1
                elif schedule_list[0].get("type") == '生产':
                    ProgressStage = 2
                elif schedule_list[0].get("type") == '出厂检验':
                    ProgressStage = 3
                passing_parameters = {
                    "ProjectName": project_name,  # 项目名称
                    "DeviceName": device_name,  # 设备名称
                    "ProgressStage": ProgressStage,  # 阶段（备料、生产、出场）
                    "ActualCompletion": schedule_list[0].get("real_time"),  # 备料、生产、出场完成时间
                    "Remark": schedule_list[0].get("remark"),  # 备注
                    "Appendix": schedule_list[0].get("upload_info"),  # 附件
                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                }
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "supply_equipment_progress",  # (*必填) 云函数名称
                            "data": passing_parameters,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                # 初始化 URL 列表
                url_list = []

                # 遍历 schedule_list 中的每个元素
                for item in schedule_list[0].get("upload_info", []):
                    # 获取 response 中的 FileList
                    file_list = item.get("response", {}).get("FileList", [])

                    # 遍历 FileList 并提取 URL
                    for file_info in file_list:
                        url = file_info.get("url")
                        if url:  # 确保 URL 存在
                            url_list.append(url)

                ProgressStage = 0
                if schedule_list[0].get("type") == '备料':
                    ProgressStage = 1
                elif schedule_list[0].get("type") == '生产':
                    ProgressStage = 2
                elif schedule_list[0].get("type") == '出厂检验':
                    ProgressStage = 3
                data = {
                    "ProjectName": project_name,  # 项目名称
                    "DeviceName": device_name,  # 设备名称
                    "ProgressStage": ProgressStage,  # 阶段（备料、生产、出厂）
                    "ActualCompletion": schedule_list[0].get("real_time"),  # 备料、生产、出厂完成时间
                    "Remark": schedule_list[0].get("remark"),  # 备注
                    "Appendix": url_list,  # 附件
                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                }
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/supply_equipment_progress"
                    }, timeout=1800
                )

                if info.get('ErrorCode') != 0:
                    raise Exception(f'报错信息为：{info.get("Message")}')

            variables = {
                'supply_equipment_progress': str(info)
            }

            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('传递甲供设备生产进度', {"data": "甲供设备生产进度为空"})

    def supplied_equipment_plan_approval(self, project_name, device_name, ygjh_xg_review, ygjh_xg_remark):
        """
            乙供设备计划审批结果回传
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not ygjh_xg_remark:
            ygjh_xg_remark = '无'

        ygjh_xg_number = 0
        if ygjh_xg_review == '同意':
            ygjh_xg_number = 1
        elif ygjh_xg_review == '驳回':
            ygjh_xg_number = 2

        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "SuppliedEquipmentPlanApproval": ygjh_xg_number,  # 项管审批
            "SuppliedEquipmentPlanRemark": ygjh_xg_remark,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if ygjh_xg_review == '驳回':
            query_sql = f"DELETE FROM equipment_production_racking_plan WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "supplied_equipment_plan_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/supplied_equipment_plan_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "supplied_equipment_plan_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def preparation_progress_synchronization_approval(self, project_name, device_name, ygsc_xg_review, ygsc_xg_remark):
        """
            乙供生产准备审批结果回传
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not ygsc_xg_remark:
            ygsc_xg_remark = '无'

        ygsc_xg_number = 0
        if ygsc_xg_review == '同意':
            ygsc_xg_number = 1
        elif ygsc_xg_review == '驳回':
            ygsc_xg_number = 2

        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "PreparationProgressSynchronizationApproval": ygsc_xg_number,  # 项管审批
            "PreparationProgressSynchronizationRemark": ygsc_xg_remark,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if ygsc_xg_review == '驳回':
            query_sql = f"DELETE FROM equipment_arrival_data WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "preparation_progress_synchronization_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                  "preparation_progress_synchronization_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "preparation_progress_synchronization_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def bl_progress_synchronization_approval(self, project_name, device_name, ygjd_xg_review_bl, ygjd_xg_remark_bl):
        """
            乙供进度（备料）审批结果回传
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not ygjd_xg_remark_bl:
            ygjd_xg_remark_bl = '无'

        ygjd_xg_bl_number = 0
        if ygjd_xg_review_bl == '同意':
            ygjd_xg_bl_number = 1
        elif ygjd_xg_review_bl == '驳回':
            ygjd_xg_bl_number = 2

        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "BlProgressSynchronizationApproval": ygjd_xg_bl_number,  # 项管审批
            "BlProgressSynchronizationRemark": ygjd_xg_remark_bl,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if ygjd_xg_review_bl == '驳回':
            query_sql = f"DELETE FROM equipment_arrival_data WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "bl_progress_synchronization_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/bl_progress_synchronization_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "bl_progress_synchronization_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def sc_progress_synchronization_approval(self, project_name, device_name, ygjd_xg_review_sc, ygjd_xg_remark_sc):
        """
            乙供进度（生产）审批结果回传
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        if not ygjd_xg_remark_sc:
            ygjd_xg_remark_sc = '无'

        ygjd_xg_sc_number = 0
        if ygjd_xg_review_sc == '同意':
            ygjd_xg_sc_number = 1
        elif ygjd_xg_review_sc == '驳回':
            ygjd_xg_sc_number = 2

        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "ScProgressSynchronizationApproval": ygjd_xg_sc_number,  # 项管审批
            "ScProgressSynchronizationRemark": ygjd_xg_remark_sc,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if ygjd_xg_review_sc == '驳回':
            query_sql = f"DELETE FROM equipment_arrival_data WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "sc_progress_synchronization_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/sc_progress_synchronization_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            "sc_progress_synchronization_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def cc_progress_synchronization_approval(self, project_name, device_name, ygjd_xg_review_cc, ygjd_xg_remark_cc):
        """
            乙供进度（出厂）审批结果回传
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not ygjd_xg_remark_cc:
            ygjd_xg_remark_cc = '无'

        ygjd_xg_cc_number = 0
        if ygjd_xg_review_cc == '同意':
            ygjd_xg_cc_number = 1
        elif ygjd_xg_review_cc == '驳回':
            ygjd_xg_cc_number = 2

        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "CcProgressSynchronizationApproval": ygjd_xg_cc_number,  # 项管审批
            "CcProgressSynchronizationRemark": ygjd_xg_remark_cc,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if ygjd_xg_review_cc == '驳回':
            query_sql = f"DELETE FROM equipment_arrival_data WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "cc_progress_synchronization_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/cc_progress_synchronization_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "cc_progress_synchronization_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def shipping_approval(self, project_name, device_name, fh_jl_review, fh_jl_remark, fh_xg_review, fh_xg_remark):
        """
            发货资料审批
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not fh_jl_remark:
            fh_jl_remark = '无'
        if not fh_xg_remark:
            fh_xg_remark = '无'

        fh_jl_number = 0
        fh_xg_number = 0
        if fh_jl_review == '同意':
            fh_jl_number = 1
        elif fh_jl_review == '驳回':
            fh_jl_number = 2
        if fh_xg_review == '同意':
            fh_xg_number = 1
        elif fh_xg_review == '驳回':
            fh_xg_number = 2
        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "ShippingSupervisionApproval": fh_jl_number,  # 监理审批
            "ShippingSupervisionRemark": fh_jl_remark,  # 监理备注
            "ShippingItemApproval": fh_xg_number,  # 项管审批
            "ShippingItemRemark": fh_xg_remark,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        fh_result = "同意"
        if fh_jl_review == '驳回' or fh_xg_review == '驳回':
            fh_result = "驳回"
        if fh_result == '驳回':
            query_sql = f"DELETE FROM equipment_arrival_data WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "shipping_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/shipping_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "shipping_approval": str(info),
            "fh_result": fh_result
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def receiving_approval(self, project_name, device_name, sh_jl_review, sh_jl_remark, sh_xg_review, sh_xg_remark):
        """
            收货资料审批
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        if not sh_jl_remark:
            sh_jl_remark = '无'
        if not sh_xg_remark:
            sh_xg_remark = '无'

        sh_jl_number = 0
        sh_xg_number = 0
        if sh_jl_review == '同意':
            sh_jl_number = 1
        elif sh_jl_review == '驳回':
            sh_jl_number = 2
        if sh_xg_review == '同意':
            sh_xg_number = 1
        elif sh_xg_review == '驳回':
            sh_xg_number = 2
        passing_parameters = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 设备名称
            "ReceivingSupervisionApproval": sh_jl_number,  # 监理审批
            "ReceivingSupervisionRemark": sh_jl_remark,  # 监理备注
            "ReceivingItemApproval": sh_xg_number,  # 项管审批
            "ReceivingItemRemark": sh_xg_remark,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        sh_result = "同意"
        if sh_jl_review == '驳回' or sh_xg_review == '驳回':
            sh_result = "驳回"
        if sh_result == '驳回':
            data = {
                'actual_arrival_time': None,
                'sh_remark': None,
                'upload_receiving_information': None
            }
            conditions = {
                'ticket_id': ticket_id
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_arrival_data", data, conditions)
            tb_db.commit()
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "receiving_approval",  # (*必填) 云函数名称
                        "data": passing_parameters,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": passing_parameters,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/receiving_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "receiving_approval": str(info),
            "sh_result": sh_result
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def transmit_shipping_data(self):
        """
            甲供发货资料发送
        """
        PartnerTicketId = self.ctx.variables.get('PartnerTicketId')
        project_name = self.ctx.variables.get('project_name')
        device_name = self.ctx.variables.get('device_name')
        equipment_batch = self.ctx.variables.get('equipment_batch')
        delivery_total = self.ctx.variables.get('delivery_total')
        delivery_num = self.ctx.variables.get('delivery_num')
        fh_list = self.ctx.variables.get('fh_list')
        system_type = self.ctx.variables.get('system_type')
        system_id = self.ctx.variables.get('system_id')
        Facilitator = self.ctx.variables.get('Facilitator')

        appendix_list = []
        for row in fh_list:
            Appendix = row.get('upload_shipping_information')
            if Appendix:
                appendix_list.append(Appendix)

        transmit_shipping_data = {
            'ProjectName': project_name,
            'DeviceName': device_name,
            'EquipmentBatch': equipment_batch,
            'DeliveryTotal': delivery_total,
            'DeliveryNum': delivery_num,
            'Appendix': appendix_list,
            'PartnerTicketId': PartnerTicketId
        }

        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "transmit_shipping_data",  # (*必填) 云函数名称
                        "data": transmit_shipping_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": transmit_shipping_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/transmit_shipping_data"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "transmit_shipping_data": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def factory_approval(self, project_name, device_name, examine_approve_result, dismiss_role, remark):
        """
            甲供收货审批回传
        """
        main_ticket_id = self.ctx.variables.get("main_ticket_id")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        equipment_batch = self.ctx.variables.get('equipment_batch')
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif not dismiss_role:
            role = 3

        if not remark:
            remark = '无'

        if examine_approve_result == '驳回':
            data = {
                'actual_arrival_time': None,
                'sh_remark': None,
                'upload_receiving_information': None
            }
            conditions = {
                'ticket_id': main_ticket_id
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_arrival_data", data, conditions)
            tb_db.commit()

        factory_approval_data = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 项目名称
            "EquipmentBatch": equipment_batch,  # 设备批次
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }

        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "factory_approval",  # (*必填) 云函数名称
                        "data": factory_approval_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": factory_approval_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/factory_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "factory_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def trigger_state(self, project_name, device_name):
        """
            触发状态
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")

        trigger_state_data = {
            "ProjectName": project_name,
            "DeviceName": device_name,
            "State": True,
            "PartnerTicketId": PartnerTicketId
        }

        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "trigger_state",  # (*必填) 云函数名称
                        "data": trigger_state_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": trigger_state_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/trigger_state"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "trigger_state": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def site_receipt_goods_information_approval(self, project_name, device_name, examine_approve_result, dismiss_role,
                                                remark):
        """
            现场签收到货资料审批回传接口
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        system_id = self.ctx.variables.get("system_id")
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'

        if examine_approve_result == '驳回':
            data = {
                'receipt_time': None,
                'receipt_information': None
            }
            conditions = {
                'ticket_id': ticket_id
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_arrival_data", data, conditions)
            tb_db.commit()

        site_receipt_goods_information_approval_data = {
            "ProjectName": project_name,  # 项目名称
            "DeviceName": device_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }

        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "site_receipt_goods_information_approval",  # (*必填) 云函数名称
                        "data": site_receipt_goods_information_approval_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": site_receipt_goods_information_approval_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/site_receipt_goods_information_approval"
                }, timeout=1800
            )

            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')

        variables = {
            "site_receipt_goods_information_approval": str(info)
        }

        flow.complete_task(self.ctx.task_id, variables=variables)


class PartnerCallInterface(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterface, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_basic_info(self, project_name, device_name, TencentTicketId):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT ticket_id FROM equipment_production_racking_process " \
              f"WHERE device_name = '{device_name}' " \
              f"AND project_name = '{project_name}' " \
              f"AND ticket_id = '{TencentTicketId}'"
        result_list = db.get_all(sql)
        if result_list:
            project_name = result_list[0].get("ticket_id")
            return project_name
        else:
            return 0

    def supplied_equipment_plan(self, TencentTicketId, ProjectName, DeviceName, Supplier, ProductionPreparationPlan,
                                PrepareMaterialsPlan, ProductionPlan, FactoryInspectionPlan,
                                ShippingApplicationPlan, ReceivingSignPlan):
        """
            乙供设备计划提交
        """
        if (TencentTicketId and ProjectName and DeviceName and Supplier and ProductionPreparationPlan and
                PrepareMaterialsPlan and FactoryInspectionPlan and ShippingApplicationPlan and ReceivingSignPlan):
            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)
            if info == 0:
                return {'code': -1, 'msg': '参数错误'}
            data = {
                "ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "device_name": DeviceName,
                "Facilitator": Supplier,
                "production_preparation": ProductionPreparationPlan,
                "prepare_materials": PrepareMaterialsPlan,
                "production": ProductionPlan,
                "factory_inspection": FactoryInspectionPlan,
                "shipping_application": ShippingApplicationPlan,
                "receiving_sign": ReceivingSignPlan
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("equipment_production_racking_plan", data)
            tb_db.commit()

            return {'code': 200, 'msg': '成功'}
        else:
            return {'code': -1, 'msg': '缺少参数'}

    def preparation_progress_synchronization(self, TencentTicketId, ProjectName, DeviceName, PoIssueTime,
                                             DesignConfirmationTime, ProductionPreparation, Remark, Appendix):
        """
            乙供生产准备进度同步
        """
        equipment_type = "乙供"
        if not Remark:
            Remark = '无'
        Facilitator = ''
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT Facilitator " \
              "FROM equipment_production_racking_plan " \
              f"WHERE ticket_id = '{TencentTicketId}' " \
              f"AND project_name = '{ProjectName}' " \
              f"AND device_name = '{DeviceName}'"
        result_list = db.get_all(sql)
        if result_list:
            for row in result_list:
                Facilitator = row.get('Facilitator')
        if (TencentTicketId and ProjectName and DeviceName and PoIssueTime and DesignConfirmationTime and
                ProductionPreparation and Remark):
            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)
            if info == 0:
                return {'code': -1, 'msg': '参数错误'}
            Appendix = json.dumps(Appendix, ensure_ascii=False)
            data = {
                "ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "equipment_type": equipment_type,
                "device_name": DeviceName,
                "supplier": Facilitator,
                'type': '生产准备',
                "real_time": PoIssueTime,
                "confirm_time": DesignConfirmationTime,
                "production_preparation_time": ProductionPreparation,
                "remark": Remark,
                "upload_url": Appendix
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("equipment_production_racking_info", data)
            tb_db.commit()
            return {'code': 200, 'msg': '成功'}

        else:
            return {'code': -1, 'msg': '缺少参数'}

    def progress_synchronization(self, TencentTicketId, ProjectName, DeviceName, ProgressStage,
                                 ActualCompletion, Remark, Appendix):
        """
            乙供进度同步
        """
        if not Remark:
            Remark = '无'
        equipment_type = "乙供"
        Facilitator = ''
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT Facilitator " \
              "FROM equipment_production_racking_plan " \
              f"WHERE ticket_id = '{TencentTicketId}' " \
              f"AND project_name = '{ProjectName}' " \
              f"AND device_name = '{DeviceName}'"
        result_list = db.get_all(sql)
        if result_list:
            for row in result_list:
                Facilitator = row.get('Facilitator')
        if TencentTicketId and ProjectName and DeviceName and ProgressStage and ActualCompletion and Remark:
            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)

            if info == 0:
                return {'code': -1, 'msg': '参数错误'}
            Appendix = json.dumps(Appendix, ensure_ascii=False)
            if ProgressStage == 1:
                type = '备料'
            elif ProgressStage == 2:
                type = '生产'
            elif ProgressStage == 3:
                type = '出厂检验'
            data = {
                "ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "device_name": DeviceName,
                "equipment_type": equipment_type,
                "supplier": Facilitator,
                "type": type,
                "real_time": ActualCompletion,
                "remark": Remark,
                "upload_url": Appendix,
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("equipment_production_racking_info", data)
            tb_db.commit()
            return {'code': 200, 'msg': '成功'}

        else:
            return {'code': -1, 'msg': '缺少参数'}

    def weekly_newspaper(self, TencentTicketId, ProjectName, DeviceName, Week, Stage, PlanTime, RealTime, Schedule,
                         Content, Progress, Question, Photo):
        """
            设备生产周报
        """
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{ProjectName}' " \
                    "and del_flag = 0"

        project_result = tb_db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        # item_tube = account_dict.get('项管-项目经理')
        item_tube = 'v_mmywang'
        if TencentTicketId and ProjectName and DeviceName and Week and Stage and PlanTime and RealTime and Schedule \
                and Content and Progress and Question:

            PlanTime = datetime.datetime.strptime(PlanTime, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")
            RealTime = datetime.datetime.strptime(RealTime, "%Y-%m-%d %H:%M:%S").strftime("%Y-%m-%d")

            if Stage == 1:
                stage = '生产准备'
            elif Stage == 2:
                stage = '备料'
            elif Stage == 3:
                stage = '生产'
            elif Stage == 4:
                stage = '出厂检验'
            elif Stage == 5:
                stage = '发货'
            elif Stage == 6:
                stage = '收货'

            if Schedule == 1:
                schedule = '正常'
            elif Schedule == 2:
                schedule = '滞后'

            # 判断数据类型并提取 URL
            accessory_urls = []
            if isinstance(Photo, list):
                for item in Photo:
                    if isinstance(item, str):
                        # 如果是字符串，直接添加到 extracted_urls
                        accessory_urls.append(item)
                    elif isinstance(item, dict):
                        # 检查 response 中的 FileList
                        file_list = item.get('response', {}).get('FileList', [])
                        for file in file_list:
                            url = file.get('url')
                            if url:
                                accessory_urls.append(url)  # 添加每个文件的 URL

            # 构建附件链接
            accessory_links = []
            for url in accessory_urls:
                accessory_links.append(f'[周报附件]({url})')
            # 将所有链接连接成一个字符串，用换行符分隔
            accessory_links_str = '\n'.join(accessory_links)

            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)
            if info == 0:
                return {'code': -1, 'msg': '参数错误'}

            data = {
                "ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "week": Week,
                "stage": stage,
                "plan_time": PlanTime,
                "real_time": RealTime,
                "schedule": schedule,
                "content": Content,
                "progress": Progress,
                "question": Question,
                "photo": str(Photo)
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("equipment_production_racking_weekly", data)
            tb_db.commit()

            text = f'# 收到项目生产周报:\n' \
                   f'## 【项目名称】：{ProjectName}\n' \
                   f'## 【设备品类】：{DeviceName}\n' \
                   f'## 【周报名称】：{stage}阶段生产的第{Week}周周报\n' \
                   f'【{stage}的计划完成时间】：{PlanTime}\n' \
                   f'【{stage}的实际完成时间】：{RealTime}\n' \
                   f'【{stage}的进度】：{schedule}\n' \
                   f'【本周工作内容】：{Content}\n' \
                   f'【当前进展】：{Progress}\n' \
                   f'【存在问题】：{Question}\n' \
                   f'【生产周报附件】：\n{accessory_links_str}\n' \
                   f'（注！如果出现多个链接的时候，说明有多个文件，请点击链接进行文件的下载！！！）'

            ChatOpsSend(
                user_name=item_tube,
                msg_content=text,
                msg_type='markdown',
                des_type='single'
            ).call()

            return {'code': 200, 'msg': '成功'}

        else:
            return {'code': -1, 'msg': '缺少参数'}

    def delivery_async_process_appendix(self, data, idx):
        """
        异步处理每个附件
        :param data: 当前条目的原始数据
        :param idx: 条目索引（用于定位唯一记录）
        """
        file_domain = config.get_config_map("dcops_file_domain_name")
        try:
            original_urls = json.loads(data["upload_shipping_information"])
            if not isinstance(original_urls, list):
                original_urls = [original_urls]

            new_urls = []

            for url in original_urls:
                matched = False
                for domain in file_domain:
                    if url.startswith(domain):
                        new_urls.append(url)
                        matched = True
                        break  # 域名匹配成功，不再继续判断
                if matched:
                    continue
                try:
                    new_url = download_and_upload_file(url)
                    new_urls.append(new_url if new_url else url)
                except Exception as e:
                    tof.send_company_wechat_message(receivers=["v_zongxiyu"], title="超时报错1", message=f"错误信息：{e}")
                    new_urls.append(url)

            # 判断是否已存在该记录
            # 更新数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            ticket_id = data.get("ticket_id")
            equipment_batch = data.get("equipment_batch")
            exists_sql = ("SELECT ticket_id FROM equipment_arrival_data "
                          "WHERE ticket_id = '%s' AND equipment_batch = '%s'" % (ticket_id, equipment_batch))
            exists = tb_db.get_row(exists_sql)
            tof.send_company_wechat_message(
                receivers=["v_zongxiyu"],
                title="附件下载失败",
                message=f"查询数据：{exists}"
            )
            if exists:
                logger.info(f"[{idx}] 数据已存在，跳过插入: {ticket_id}, {equipment_batch}")
                return
            data_list = [{
                "ticket_id": data.get("ticket_id"),
                "campus": data.get("campus"),
                "project_name": data.get("project_name"),
                "equipment_category": data.get("equipment_category"),
                "actual_delivery_time": data.get("actual_delivery_time"),
                "fh_remark": data.get("fh_remark"),
                "upload_shipping_information": json.dumps(new_urls, ensure_ascii=False),
                'equipment_batch': data.get("equipment_batch")
            }]
            tb_db.insert_batch("equipment_arrival_data", data_list)

            logger.info(f"[{idx}] 链接已更新为: {new_urls}")

        except Exception as e:
            logger.info(f"[{idx}] 异步处理失败: {e}")

    def delivery_request(self, TencentTicketId, ProjectName, DeviceName, DeliveryList):
        """
            发货申请
        """
        # 输出转换后的数据
        if TencentTicketId and ProjectName and DeviceName and DeliveryList:
            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)
            if info == 0:
                return {'code': -1, 'msg': '参数错误'}
            match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", ProjectName)
            if match:
                campus = match.group(1)
                project = match.group(2)
            else:
                campus = ProjectName
                project = ProjectName
            data_list = []

            query_data = {
                "SchemaId": "ticket_base",
                "Data": {
                    "ResultColumns": {
                        "InstanceId": "",
                    },
                    "SearchCondition": {
                        "TicketId": TencentTicketId
                    }
                }
            }
            ticket_info = gnetops.request(
                action="QueryData", method="Run", ext_data=query_data
            )
            res_list = ticket_info['List']
            for row in res_list:
                instance_id = row.get('InstanceId')
            WorkflowVarUpdate(instance_id=instance_id, variables={"DeliveryList": DeliveryList}).call()

            for idx, item in enumerate(DeliveryList):
                ActualDeliveryTime = item.get('ActualDeliveryTime')
                Remark = item.get('Remark')
                Appendix = item.get('Appendix') or ''
                EquipmentBatch = item.get('EquipmentBatch')

                if not all([ActualDeliveryTime, EquipmentBatch]):
                    return {'code': -1, 'msg': '参数错误'}

                # 统一处理 Appendix 的两种格式：str 或 [str]
                if isinstance(Appendix, str):
                    original_urls = [Appendix]
                    is_valid, msg = test_download_link(Appendix)
                    if not is_valid:
                        return {'code': -1, 'msg': f"[{idx}] 链接不可用: {Appendix}, 原因: {msg}"}
                elif isinstance(Appendix, list):
                    for idx_2, appendix_url in enumerate(Appendix):
                        is_valid, msg = test_download_link(appendix_url)
                        if not is_valid:
                            return {'code': -1, 'msg': f"[{idx_2}] 链接不可用: {appendix_url}, 原因: {msg}"}
                    original_urls = Appendix
                else:
                    original_urls = []

                # 构建插入数据（保留原链接）
                upload_shipping_information = json.dumps(original_urls, ensure_ascii=False)
                if not Remark:
                    Remark = '无'

                data = {
                    "ticket_id": TencentTicketId,
                    "campus": campus,
                    "project_name": project,
                    "equipment_category": DeviceName,
                    "actual_delivery_time": ActualDeliveryTime,
                    "fh_remark": Remark,
                    "upload_shipping_information": upload_shipping_information,
                    'equipment_batch': EquipmentBatch
                }
                data_list.append(data)

                # 启动后台线程处理附件下载与更新
                thread = threading.Thread(
                    target=self.delivery_async_process_appendix,
                    args=(copy.deepcopy(data), idx)
                )
                thread.daemon = True  # 设置为守护线程，防止程序挂起
                thread.start()
            return {'code': 200, 'msg': '成功'}

        else:
            return {'code': -1, 'msg': '缺少参数'}

    def receipt_async_process_appendix(self, data, idx):
        """
        异步处理附件（支持任意字段）
        :param data: 原始数据
        :param idx: 索引（日志用）
        :param field_name: 要处理的字段名，默认 upload_receiving_information
        """
        file_domain = config.get_config_map("dcops_file_domain_name")
        try:
            original_urls = json.loads(data["upload_receiving_information"])
            if not isinstance(original_urls, list):
                original_urls = [original_urls]

            new_urls = []

            for url in original_urls:
                matched = False
                for domain in file_domain:
                    if url.startswith(domain):
                        new_urls.append(url)
                        matched = True
                        break
                if matched:
                    continue
                try:
                    new_url = download_and_upload_file(url)
                    new_urls.append(new_url if new_url else url)
                except Exception as e:
                    new_urls.append(url)

            # 更新数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            conditions = {
                "campus": data["campus"],
                "project_name": data["project_name"],
                "equipment_category": data["equipment_category"],
                "ticket_id": data["ticket_id"],
                "equipment_batch": data["equipment_batch"]
            }

            update_data = {
                "upload_receiving_information": json.dumps(new_urls, ensure_ascii=False),
                "actual_arrival_time": data["actual_arrival_time"],
                "sh_remark": data["sh_remark"]
            }

            tb_db.update("equipment_arrival_data", update_data, conditions)
            logger.info(f"[{idx}] 'upload_receiving_information' 已更新为: {new_urls}")

        except Exception as e:
            logger.error(f"[{idx}] 异步处理失败: {e}", exc_info=True)

    def receipt_request(self, TencentTicketId, ProjectName, DeviceName, ReceiptList):
        """
            收货申请
        """
        if not all([TencentTicketId, ProjectName, DeviceName, ReceiptList]):
            return {'code': -1, 'msg': '缺少参数'}

        info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                   TencentTicketId=TencentTicketId)
        if info == 0:
            return {'code': -1, 'msg': '参数错误'}

        match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", ProjectName)
        if match:
            campus = match.group(1)
            project = match.group(2)
        else:
            campus = ProjectName
            project = ProjectName

        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                },
                "SearchCondition": {
                    "TicketId": TencentTicketId
                }
            }
        }
        ticket_info = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        res_list = ticket_info['List']
        for row in res_list:
            instance_id = row.get('InstanceId')
        WorkflowVarUpdate(instance_id=instance_id, variables={"ReceiptList": ReceiptList}).call()

        for idx, item in enumerate(ReceiptList):
            ActualReceivingTime = item.get('ActualReceivingTime')
            Remark = item.get('Remark')
            Appendix = item.get('Appendix')
            EquipmentBatch = item.get('EquipmentBatch')

            if not all([ActualReceivingTime, EquipmentBatch]):
                return {'code': -1, 'msg': '参数错误'}

            # 统一处理 Appendix
            if isinstance(Appendix, str):
                original_urls = [Appendix]
                is_valid, msg = test_download_link(Appendix)
                if not is_valid:
                    return {'code': -1, 'msg': f"[{idx}] 链接不可用: {Appendix}, 原因: {msg}"}
            elif isinstance(Appendix, list):
                for idx_2, appendix_url in enumerate(Appendix):
                    is_valid, msg = test_download_link(appendix_url)
                    if not is_valid:
                        return {'code': -1, 'msg': f"[{idx_2}] 链接不可用: {appendix_url}, 原因: {msg}"}
                original_urls = Appendix
            else:
                original_urls = []

            upload_receiving_information = json.dumps(original_urls, ensure_ascii=False)
            if not Remark:
                Remark = '无'

            data = {
                "ticket_id": TencentTicketId,
                "campus": campus,
                "project_name": project,
                "equipment_category": DeviceName,
                "actual_arrival_time": ActualReceivingTime,
                "sh_remark": Remark,
                "upload_receiving_information": upload_receiving_information,
                "equipment_batch": EquipmentBatch
            }

            # 插入初始记录（或确保已存在），可选
            # tb_db.insert(...) or update(...)

            # 启动后台线程处理附件
            thread = threading.Thread(
                target=self.receipt_async_process_appendix,
                args=(copy.deepcopy(data), idx)
            )
            thread.daemon = True
            thread.start()
        return {'code': 200, 'msg': '成功'}

    def factory_dispatch_request(self, TencentTicketId, ProjectName, DeviceName, EquipmentBatch,
                                 ActualReceivingTime, Appendix, Remark):
        """
            甲供收货申请提交接口
        """
        if not TencentTicketId or not ProjectName or not DeviceName \
                or not EquipmentBatch or not ActualReceivingTime:
            return {'code': -1, 'msg': '缺少参数'}
        match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", ProjectName)
        if match:
            campus = match.group(1)
            project = match.group(2)
        else:
            campus = ProjectName
            project = ProjectName
        # 输出转换后的数据
        if ActualReceivingTime and EquipmentBatch:
            upload_receiving_information = json.dumps(Appendix, ensure_ascii=False)
            if not Remark:
                Remark = '无'
            conditions = {
                "campus": campus,
                "project_name": project,
                'equipment_category': DeviceName,
                'ticket_id': TencentTicketId,
                'equipment_batch': EquipmentBatch
            }
            data = {
                "actual_arrival_time": ActualReceivingTime,
                "sh_remark": Remark,
                "upload_receiving_information": upload_receiving_information
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_arrival_data", data, conditions)
            tb_db.commit()
            return {'code': 200, 'msg': '成功'}

    def sign_received_goods_site(self, TencentTicketId, ProjectName, DeviceName, ReceiptTime, ReceiptInformation,
                                 Remark):
        """
            现场签收到货资料
        """
        if not TencentTicketId or not ProjectName or not DeviceName \
                or not ReceiptTime or not ReceiptInformation:
            return {'code': -1, 'msg': '缺少参数'}
        match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", ProjectName)
        if match:
            campus = match.group(1)
            project = match.group(2)
        else:
            campus = ProjectName
            project = ProjectName

        receipt_information = json.dumps(ReceiptInformation, ensure_ascii=False)
        if not Remark:
            Remark = '无'
        conditions = {
            "campus": campus,
            "project_name": project,
            'equipment_category': DeviceName,
            'ticket_id': TencentTicketId
        }
        data = {
            "receipt_time": ReceiptTime,
            "receipt_information": receipt_information
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("equipment_arrival_data", data, conditions)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class WaitPartnerCallInterface(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterface, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_supplied_equipment_plan(self):
        """
            等待乙供设备计划提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT production_preparation,prepare_materials,production,factory_inspection,shipping_application," \
              "receiving_sign FROM equipment_production_racking_plan " \
              f"WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}' and device_name = '{device_name}'"
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('乙供计划查询信息', {'message': result_list})
        if system_type == 'Dcops':
            if result_list:
                for time in result_list:
                    production_preparation = time.get('production_preparation')
                    prepare_materials = time.get('prepare_materials')
                    production = time.get('production')
                    factory_inspection = time.get('factory_inspection')
                    shipping_application = time.get('shipping_application')
                    receiving_sign = time.get('receiving_sign')
                variables = {
                    "wait_supplied_equipment_plan": result_list,
                    "production_preparation": production_preparation,
                    "prepare_materials": prepare_materials,
                    "production": production,
                    "factory_inspection": factory_inspection,
                    "shipping_application": shipping_application,
                    "receiving_sign": receiving_sign
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            if result_list:
                for time in result_list:
                    production_preparation = time.get('production_preparation')
                    prepare_materials = time.get('prepare_materials')
                    production = time.get('production')
                    factory_inspection = time.get('factory_inspection')
                    shipping_application = time.get('shipping_application')
                    receiving_sign = time.get('receiving_sign')
                variables = {
                    "wait_supplied_equipment_plan": result_list,
                    "production_preparation": production_preparation,
                    "prepare_materials": prepare_materials,
                    "production": production,
                    "factory_inspection": factory_inspection,
                    "shipping_application": shipping_application,
                    "receiving_sign": receiving_sign
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_preparation_progress_synchronization(self):
        """
            等待乙供生产准备进度同步
        """
        # 生产准备
        production_preparation = self.ctx.variables.get("production_preparation")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT real_time,confirm_time,production_preparation_time,remark,upload_url " \
              "FROM equipment_production_racking_info " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"and project_name = '{project_name}' " \
              f"and device_name = '{device_name}' " \
              f"and type = '生产准备'"
        result_list = db.get_all(sql)
        # 在每个记录中添加 production_preparation 字段
        if system_type == 'Dcops':
            for record in result_list:
                record['plan_time'] = production_preparation
            if result_list:
                for row in result_list:
                    upload_url = row.get('upload_url')
                    if upload_url is not None:
                        # 替换单引号为双引号
                        upload_url = upload_url.replace("'", '"')
                        row['upload_url'] = json.loads(upload_url)
                    row['upload_info'] = row.pop('upload_url')  # 修改键名为'upload_info'
                variables = {
                    "wait_preparation_progress_synchronization": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            for record in result_list:
                record['plan_time'] = production_preparation

                # 处理 upload_url
                upload_url = record.get('upload_url')
                if upload_url:
                    try:
                        # 尝试解析 upload_url
                        upload_url_list = json.loads(upload_url)

                        # 确保 upload_url_list 是一个列表
                        if isinstance(upload_url_list, list):
                            # 如果是多个 URL，转换为以逗号分隔的字符串
                            record['upload_url'] = ', '.join(upload_url_list)
                        else:
                            # 如果是单个 URL，直接使用
                            record['upload_url'] = upload_url_list
                    except json.JSONDecodeError:
                        # 如果解析失败，保留原始值或设置为 None
                        record['upload_url'] = upload_url  # 或者可以设置为 None
            if result_list:
                for record in result_list:
                    upload_url = record.get('upload_url')
                    if upload_url:
                        try:
                            # 使用 ast.literal_eval 将字符串解析为列表
                            upload_url_list = ast.literal_eval(upload_url)
                            if isinstance(upload_url_list, list):
                                # 如果是列表，转换为以逗号分隔的字符串
                                record['upload_url'] = ', '.join(upload_url_list)
                        except (ValueError, SyntaxError):
                            # 如果解析失败，保留原始值或设置为 None
                            record['upload_url'] = upload_url  # 或者可以设置为 None
                    record['upload_info'] = record.pop('upload_url')  # 修改键名为'dh_remark'
                variables = {
                    "wait_preparation_progress_synchronization": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_progress_synchronization_bl(self):
        """
            等待乙供进度提交（备料）
        """
        # 备料
        prepare_materials = self.ctx.variables.get("prepare_materials")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT type,real_time,remark,upload_url " \
              "FROM equipment_production_racking_info " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"and project_name = '{project_name}' " \
              f"and device_name = '{device_name}' " \
              "and type = '备料'"
        result_list = db.get_all(sql)
        if system_type == 'Dcops':
            # 遍历 result_list 并根据 type 添加新字段
            for record in result_list:
                record['plan_time'] = prepare_materials  # 或者可以设置为其他值
            if result_list:
                for row in result_list:
                    upload_url = row.get('upload_url')
                    if upload_url is not None:
                        # 替换单引号为双引号
                        upload_url = upload_url.replace("'", '"')
                        row['upload_url'] = json.loads(upload_url)
                    row['upload_info'] = row.pop('upload_url')  # 修改键名为'upload_info'
                variables = {
                    "wait_progress_synchronization_bl": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            for record in result_list:
                record['plan_time'] = prepare_materials  # 或者可以设置为其他值

                # 处理 upload_url
                upload_url = record.get('upload_url')
                if upload_url:
                    try:
                        # 尝试解析 upload_url
                        upload_url_list = json.loads(upload_url)

                        # 确保 upload_url_list 是一个列表
                        if isinstance(upload_url_list, list):
                            # 如果是多个 URL，转换为以逗号分隔的字符串
                            record['upload_url'] = ', '.join(upload_url_list)
                        else:
                            # 如果是单个 URL，直接使用
                            record['upload_url'] = upload_url_list
                    except json.JSONDecodeError:
                        # 如果解析失败，保留原始值或设置为 None
                        record['upload_url'] = upload_url  # 或者可以设置为 None
            if result_list:
                for record in result_list:
                    upload_url = record.get('upload_url')
                    if upload_url:
                        try:
                            # 使用 ast.literal_eval 将字符串解析为列表
                            upload_url_list = ast.literal_eval(upload_url)
                            if isinstance(upload_url_list, list):
                                # 如果是列表，转换为以逗号分隔的字符串
                                record['upload_url'] = ', '.join(upload_url_list)
                        except (ValueError, SyntaxError):
                            # 如果解析失败，保留原始值或设置为 None
                            record['upload_url'] = upload_url  # 或者可以设置为 None
                    record['upload_info'] = record.pop('upload_url')  # 修改键名为'dh_remark'
                variables = {
                    "wait_progress_synchronization_bl": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_progress_synchronization_sc(self):
        """
            等待乙供进度提交（生产）
        """
        # 生产
        production = self.ctx.variables.get("production")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT type,real_time,remark,upload_url " \
              "FROM equipment_production_racking_info " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"and project_name = '{project_name}' " \
              f"and device_name = '{device_name}' " \
              "and type = '生产'"
        result_list = db.get_all(sql)
        if system_type == 'Dcops':
            # 遍历 result_list 并根据 type 添加新字段
            for record in result_list:
                record['plan_time'] = production  # 或者可以设置为其他值
            if result_list:
                for row in result_list:
                    upload_url = row.get('upload_url')
                    if upload_url is not None:
                        # 替换单引号为双引号
                        upload_url = upload_url.replace("'", '"')
                        row['upload_url'] = json.loads(upload_url)
                    row['upload_info'] = row.pop('upload_url')  # 修改键名为'upload_info'
                variables = {
                    "wait_progress_synchronization_sc": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            for record in result_list:
                record['plan_time'] = production  # 或者可以设置为其他值

                # 处理 upload_url
                upload_url = record.get('upload_url')
                if upload_url:
                    try:
                        # 尝试解析 upload_url
                        upload_url_list = json.loads(upload_url)

                        # 确保 upload_url_list 是一个列表
                        if isinstance(upload_url_list, list):
                            # 如果是多个 URL，转换为以逗号分隔的字符串
                            record['upload_url'] = ', '.join(upload_url_list)
                        else:
                            # 如果是单个 URL，直接使用
                            record['upload_url'] = upload_url_list
                    except json.JSONDecodeError:
                        # 如果解析失败，保留原始值或设置为 None
                        record['upload_url'] = upload_url  # 或者可以设置为 None
            if result_list:
                for record in result_list:
                    upload_url = record.get('upload_url')
                    if upload_url:
                        try:
                            # 使用 ast.literal_eval 将字符串解析为列表
                            upload_url_list = ast.literal_eval(upload_url)
                            if isinstance(upload_url_list, list):
                                # 如果是列表，转换为以逗号分隔的字符串
                                record['upload_url'] = ', '.join(upload_url_list)
                        except (ValueError, SyntaxError):
                            # 如果解析失败，保留原始值或设置为 None
                            record['upload_url'] = upload_url  # 或者可以设置为 None
                    record['upload_info'] = record.pop('upload_url')  # 修改键名为'dh_remark'
                variables = {
                    "wait_progress_synchronization_sc": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_progress_synchronization_cc(self):
        """
            等待乙供进度提交（出厂检验）
        """
        # 出厂检验
        factory_inspection = self.ctx.variables.get("factory_inspection")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT type,real_time,remark,upload_url " \
              "FROM equipment_production_racking_info " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"and project_name = '{project_name}' " \
              f"and device_name = '{device_name}' " \
              "and type = '出厂检验'"
        result_list = db.get_all(sql)
        if system_type == 'Dcops':
            # 遍历 result_list 并根据 type 添加新字段
            for record in result_list:
                record['plan_time'] = factory_inspection  # 或者可以设置为其他值
            if result_list:
                for row in result_list:
                    upload_url = row.get('upload_url')
                    if upload_url is not None:
                        # 替换单引号为双引号
                        upload_url = upload_url.replace("'", '"')
                        row['upload_url'] = json.loads(upload_url)
                    row['upload_info'] = row.pop('upload_url')  # 修改键名为'upload_info'
                variables = {
                    "wait_progress_synchronization_cc": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            for record in result_list:
                record['plan_time'] = factory_inspection  # 或者可以设置为其他值

                # 处理 upload_url
                upload_url = record.get('upload_url')
                if upload_url:
                    try:
                        # 尝试解析 upload_url
                        upload_url_list = json.loads(upload_url)

                        # 确保 upload_url_list 是一个列表
                        if isinstance(upload_url_list, list):
                            # 如果是多个 URL，转换为以逗号分隔的字符串
                            record['upload_url'] = ', '.join(upload_url_list)
                        else:
                            # 如果是单个 URL，直接使用
                            record['upload_url'] = upload_url_list
                    except json.JSONDecodeError:
                        # 如果解析失败，保留原始值或设置为 None
                        record['upload_url'] = upload_url  # 或者可以设置为 None
            if result_list:
                for record in result_list:
                    upload_url = record.get('upload_url')
                    if upload_url:
                        try:
                            # 使用 ast.literal_eval 将字符串解析为列表
                            upload_url_list = ast.literal_eval(upload_url)
                            if isinstance(upload_url_list, list):
                                # 如果是列表，转换为以逗号分隔的字符串
                                record['upload_url'] = ', '.join(upload_url_list)
                        except (ValueError, SyntaxError):
                            # 如果解析失败，保留原始值或设置为 None
                            record['upload_url'] = upload_url  # 或者可以设置为 None
                    record['upload_info'] = record.pop('upload_url')  # 修改键名为'dh_remark'
                variables = {
                    "wait_progress_synchronization_cc": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_delivery_request(self):
        """
            等待发货申请数据
        """
        shipping_application = self.ctx.variables.get("shipping_application")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT equipment_batch, actual_delivery_time,fh_remark,upload_shipping_information " \
              "FROM equipment_arrival_data " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and CONCAT(campus, project_name) = '{project_name}' and equipment_category = '{device_name}'"
        result_list = db.get_all(sql)
        # 遍历 result_list 并根据 type 添加新字段
        if system_type == 'Dcops':
            for record in result_list:
                record['scheduled_delivery_time'] = shipping_application  # 或者可以设置为其他值
            if result_list:
                for row in result_list:
                    actual_delivery_time = row.get('actual_delivery_time')
                    upload_shipping_information_old = row.get('upload_shipping_information')
                    upload_shipping_information_new = transform_url_list(upload_shipping_information_old)
                    row['upload_shipping_information'] = json.loads(upload_shipping_information_new)
                    if actual_delivery_time:
                        row['actual_delivery_time'] = actual_delivery_time.strftime('%Y-%m-%d %H:%M:%S')

                variables = {
                    "fh_list": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == '外部':
            for record in result_list:
                record['scheduled_delivery_time'] = shipping_application  # 或者可以设置为其他值
            if result_list:
                for row in result_list:
                    actual_delivery_time = row.get('actual_delivery_time')
                    upload_shipping_information = row.get('upload_shipping_information')
                    if actual_delivery_time:
                        row['actual_delivery_time'] = actual_delivery_time.strftime('%Y-%m-%d %H:%M:%S')
                    if upload_shipping_information:
                        upload_shipping_information_old = row.get('upload_shipping_information')
                        upload_shipping_information_new = transform_url_list(upload_shipping_information_old)
                        row['upload_shipping_information'] = json.loads(upload_shipping_information_new)

                variables = {
                    "fh_list": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_receipt_request(self):
        """
            等待收货申请数据
        """
        receiving_sign = self.ctx.variables.get("receiving_sign")
        ticket_id = self.ctx.variables.get("ticket_id")
        main_ticket_id = self.ctx.variables.get("main_ticket_id")
        # 适配子流程等待到货资料
        if main_ticket_id:
            ticket_id = main_ticket_id
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT equipment_batch, actual_arrival_time, sh_remark, upload_receiving_information " \
              "FROM equipment_arrival_data " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"AND CONCAT(campus, project_name) = '{project_name}' " \
              f"AND equipment_category = '{device_name}'"
        result_list = db.get_all(sql)
        # 遍历 result_list 并根据 type 添加新字段
        if system_type == "Dcops":
            for record in result_list:
                record['planned_arrival_time'] = receiving_sign  # 或者可以设置为其他值
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                # 检查每个字段是否为 None
                for key, value in row.items():
                    if value is None:
                        all_fields_filled = False
                        break
                if not all_fields_filled:
                    break

            if all_fields_filled:
                for row in result_list:
                    actual_arrival_time = row.get('actual_arrival_time')
                    upload_receiving_information_old = row.get('upload_receiving_information')
                    upload_receiving_information_new = transform_url_list(upload_receiving_information_old)
                    row['upload_receiving_information'] = json.loads(upload_receiving_information_new)
                    if actual_arrival_time is not None:
                        row['actual_arrival_time'] = actual_arrival_time.strftime('%Y-%m-%d %H:%M:%S')

                    row['dh_remark'] = row.pop('sh_remark')  # 修改键名为'dh_remark'

                variables = {
                    "dh_list": result_list,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "存在未填字段，流程未结束"}
        elif system_type == "外部":
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                # 检查每个字段是否为 None
                for key, value in row.items():
                    if value is None:
                        all_fields_filled = False
                        break
                if not all_fields_filled:
                    break

            if all_fields_filled:
                for row in result_list:
                    row['planned_arrival_time'] = receiving_sign
                    actual_arrival_time = row.get('actual_arrival_time')
                    upload_receiving_information = row.get('upload_receiving_information')
                    row['dh_remark'] = row.pop('sh_remark')  # 修改键名为'dh_remark'
                    if actual_arrival_time is not None:
                        row['actual_arrival_time'] = actual_arrival_time.strftime('%Y-%m-%d %H:%M:%S')

                    if upload_receiving_information:
                        upload_receiving_information_old = row.get('upload_receiving_information')
                        upload_receiving_information_new = transform_url_list(upload_receiving_information_old)
                        row['upload_receiving_information'] = json.loads(upload_receiving_information_new)

                variables = {
                    "dh_list": result_list,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "存在未填字段，流程未结束"}

    def wait_receipt_request_bak(self):
        """
            甲供子单等待收货申请数据
        """
        receiving_sign = self.ctx.variables.get("receiving_sign")
        main_ticket_id = self.ctx.variables.get("main_ticket_id")
        # 适配子流程等待到货资料
        if main_ticket_id:
            ticket_id = main_ticket_id
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")
        equipment_batch = self.ctx.variables.get("equipment_batch")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT equipment_batch, actual_arrival_time, sh_remark, upload_receiving_information " \
              "FROM equipment_arrival_data " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"AND CONCAT(campus, project_name) = '{project_name}' " \
              f"AND equipment_category = '{device_name}' " \
              f"AND equipment_batch = '{equipment_batch}'"
        result_list = db.get_all(sql)

        # 初始化最终到货信息列表
        dh_list = []

        # 遍历 result_list 并根据 type 添加新字段
        if system_type == "Dcops":
            for record in result_list:
                record['planned_arrival_time'] = receiving_sign  # 或者可以设置为其他值
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                # 检查每个字段是否为 None
                for key, value in row.items():
                    if not value:
                        all_fields_filled = False
                        break
                if not all_fields_filled:
                    break

            if all_fields_filled:
                for row in result_list:
                    equipment_batch = row.get('equipment_batch')
                    sh_remark = row.get('sh_remark')
                    actual_arrival_time = row.get('actual_arrival_time')
                    upload_receiving_information = json.loads(row.get('upload_receiving_information'))
                    if actual_arrival_time is not None:
                        actual_arrival_time = actual_arrival_time.strftime('%Y-%m-%d %H:%M:%S')
                    if upload_receiving_information:
                        if isinstance(upload_receiving_information, list):
                            for item in upload_receiving_information:
                                if isinstance(item, dict):
                                    file_url = item.get('response', {}).get('FileList', [])[0].get('url', '')
                                    if file_url:
                                        dh_list.append({
                                            'equipment_batch': equipment_batch,
                                            'actual_arrival_time': actual_arrival_time,
                                            'planned_arrival_time': receiving_sign,
                                            'dh_remark': sh_remark,
                                            'upload_receiving_information': file_url
                                        })
                    else:
                        return {"success": False, "data": "存在未填字段，流程未结束"}

                variables = {
                    "dh_list": dh_list,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "存在未填字段，流程未结束"}
        elif system_type == "外部":
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                # 检查每个字段是否为 None
                for key, value in row.items():
                    if not value:
                        all_fields_filled = False
                        break
                if not all_fields_filled:
                    break

            if all_fields_filled:
                for row in result_list:
                    row['planned_arrival_time'] = receiving_sign  # 或者可以设置为其他值
                    equipment_batch = row.get('equipment_batch')
                    sh_remark = row.get('sh_remark')
                    actual_arrival_time = row.get('actual_arrival_time')
                    upload_receiving_information = row.get('upload_receiving_information')
                    if actual_arrival_time is not None:
                        actual_arrival_time = actual_arrival_time.strftime('%Y-%m-%d %H:%M:%S')
                    if upload_receiving_information:
                        try:
                            # 使用 ast.literal_eval 将字符串解析为列表
                            upload_receiving_list = json.loads(upload_receiving_information)
                            if isinstance(upload_receiving_list, list):
                                for item in upload_receiving_list:
                                    dh_list.append({
                                        'equipment_batch': equipment_batch,
                                        'actual_arrival_time': actual_arrival_time,
                                        'planned_arrival_time': receiving_sign,
                                        'dh_remark': sh_remark,
                                        'upload_receiving_information': item
                                    })
                        except (ValueError, SyntaxError):
                            # 如果解析失败，保留原始值或设置为 None
                            dh_list.append({
                                'equipment_batch': equipment_batch,
                                'actual_arrival_time': actual_arrival_time,
                                'planned_arrival_time': receiving_sign,
                                'dh_remark': sh_remark,
                                'upload_receiving_information': upload_receiving_information
                            })

                variables = {
                    "dh_list": dh_list,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "存在未填字段，流程未结束"}

    def wait_receipt_request_receipt_documents(self):
        """
            甲供等待现场签收资料上传
        """
        receiving_sign = self.ctx.variables.get("receiving_sign")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        device_name = self.ctx.variables.get("device_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT equipment_batch, actual_arrival_time, sh_remark, upload_receiving_information, " \
              "receipt_time, receipt_information " \
              "FROM equipment_arrival_data " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"AND CONCAT(campus, project_name) = '{project_name}' " \
              f"AND equipment_category = '{device_name}' "
        result_list = db.get_all(sql)

        # 初始化变量
        receipt_time = ""
        receipt_information = ""
        receipt_list = []

        # 遍历 result_list 并根据 type 添加新字段
        if system_type == "Dcops":
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                if not (row.get("receipt_time") and row.get("receipt_information")):
                    all_fields_filled = False
                    break

            if all_fields_filled:
                for row in result_list:
                    receipt_time = row.get("receipt_time", "")
                    receipt_information = row.get("receipt_information", "")
                    receipt_information_list = json.loads(receipt_information)
                    # 尝试解析 receipt_information
                    if receipt_information:
                        if isinstance(receipt_information_list, list):
                            for item in receipt_information_list:
                                if isinstance(item, dict):
                                    file_url = item.get('response', {}).get('FileList', [])[0].get('url', '')
                                    receipt_list.append({
                                        "receipt_information": file_url
                                    })
                    else:
                        all_fields_filled = False

                if all_fields_filled and receipt_time and receipt_information:
                    variables = {
                        "receipt_time": receipt_time,
                        "receipt_information": receipt_information,
                        "receipt_list": receipt_list
                    }
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return {"success": True, "data": "流程已结束"}
                else:
                    return {"success": False, "data": "存在未填字段或数据无效，流程未结束"}

        elif system_type == "外部":
            # 检查每个记录是否所有字段都有值
            all_fields_filled = True
            for row in result_list:
                if not (row.get("receipt_time") and row.get("receipt_information")):
                    all_fields_filled = False
                    break

            if all_fields_filled:
                for row in result_list:
                    receipt_time = row.get("receipt_time", "")
                    receipt_information = row.get("receipt_information", "")

                    # 尝试解析 receipt_information
                    if receipt_information:
                        try:
                            receipt_information_list = json.loads(receipt_information)
                            if isinstance(receipt_information_list, list):
                                receipt_information = ", ".join(receipt_information_list)
                                for i in receipt_information_list:
                                    receipt_list.append({
                                        "receipt_information": i
                                    })
                            else:
                                receipt_information = ""
                                all_fields_filled = False
                                break
                        except (ValueError, SyntaxError):
                            receipt_information = ""
                            all_fields_filled = False
                            break

                if all_fields_filled and receipt_time and receipt_information:
                    variables = {
                        "receipt_time": receipt_time,
                        "acceptance_time": receipt_time,
                        "receipt_information": receipt_information,
                        "receipt_list": receipt_list
                    }
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return {"success": True, "data": "流程已结束"}
                else:
                    return {"success": False, "data": "存在未填字段或数据无效，流程未结束"}

        return {"success": False, "data": "未找到符合条件的数据"}


class ProcessNodeInterface(object):
    """
        流程中数据处理节点
    """

    def __init__(self):
        super(ProcessNodeInterface, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def main_process_storage(self, ticket_id, device_name, project_name, equipment_type, supply_type):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT campus FROM risk_early_warning_data where project_name = '{project_name}' "
        result = db.get_all(sql1)
        campus = ''
        if result:
            campus = result[0].get('campus')

        variables = {
            "campus": campus
        }
        data = {
            "ticket_id": ticket_id,
            "device_name": device_name,
            "project_name": project_name,
            "equipment_type": equipment_type,
            "campus": campus
        }
        db.begin()
        db.insert("equipment_production_racking_process", data)
        db.commit()
        flow.complete_task(self.ctx.task_id, variables=variables)


class YGJHXgApproval(AjaxTodoBase):
    """
        乙供设备计划--项管审批
    """

    def __int__(self):
        super(YGJHXgApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        wait_supplied_equipment_plan = self.ctx.variables.get("wait_supplied_equipment_plan")
        ygjh_xg_review = process_data.get('ygjh_xg_review')
        ygjh_xg_remark = process_data.get('ygjh_xg_remark')
        for jh_time in wait_supplied_equipment_plan:
            # 生产准备
            production_preparation = jh_time.get('production_preparation')
            # 备料
            prepare_materials = jh_time.get('prepare_materials')
            # 生产
            production = jh_time.get('production')
            # 出厂检验
            factory_inspection = jh_time.get('factory_inspection')
            # 发货
            shipping_application = jh_time.get('shipping_application')
            # 到货
            receiving_sign = jh_time.get('receiving_sign')

        if ygjh_xg_review == '驳回':
            if not ygjh_xg_remark:
                return {"code": -1, "msg": "项管驳回时，请填写驳回原因"}

            query_sql = f"DELETE FROM equipment_production_racking_plan WHERE ticket_id = '{ticket_id}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            "ygjh_xg_review": ygjh_xg_review,
            "ygjh_xg_remark": ygjh_xg_remark,
            "production_preparation": production_preparation,
            "prepare_materials": prepare_materials,
            "production": production,
            "factory_inspection": factory_inspection,
            "shipping_application": shipping_application,
            "receiving_sign": receiving_sign
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class YGSCZBXgApproval(AjaxTodoBase):
    """
        乙供生产准备进度--项管审批
    """

    def __int__(self):
        super(YGSCZBXgApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ygsc_xg_review = process_data.get('ygsc_xg_review')
        ygsc_xg_remark = process_data.get('ygsc_xg_remark')

        if ygsc_xg_review == '驳回':
            if not ygsc_xg_remark:
                return {"code": -1, "msg": "项管驳回时，请填写驳回原因"}

            query_sql = f"DELETE FROM equipment_production_racking_info " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND type = '生产准备'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            "ygsc_xg_review": ygsc_xg_review,
            "ygsc_xg_remark": ygsc_xg_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class YGJDXgApprovalBl(AjaxTodoBase):
    """
        乙供进度(备料)--项管审批
    """

    def __int__(self):
        super(YGJDXgApprovalBl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ygjd_xg_review_bl = process_data.get('ygjd_xg_review_bl')
        ygjd_xg_remark_bl = process_data.get('ygjd_xg_remark_bl')

        if ygjd_xg_review_bl == '驳回':
            if not ygjd_xg_remark_bl:
                return {"code": -1, "msg": "项管驳回时，请填写驳回原因"}

            query_sql = f"DELETE FROM equipment_production_racking_info " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND type = '备料'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            "ygjd_xg_review_bl": ygjd_xg_review_bl,
            "ygjd_xg_remark_bl": ygjd_xg_remark_bl
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class YGJDXgApprovalSC(AjaxTodoBase):
    """
        乙供进度(生产)--项管审批
    """

    def __int__(self):
        super(YGJDXgApprovalSC, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ygjd_xg_review_sc = process_data.get('ygjd_xg_review_sc')
        ygjd_xg_remark_sc = process_data.get('ygjd_xg_remark_sc')

        if ygjd_xg_review_sc == '驳回':
            if not ygjd_xg_remark_sc:
                return {"code": -1, "msg": "项管驳回时，请填写驳回原因"}

            query_sql = f"DELETE FROM equipment_production_racking_info " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND type = '生产'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            "ygjd_xg_review_sc": ygjd_xg_review_sc,
            "ygjd_xg_remark_sc": ygjd_xg_remark_sc
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class YGJDXgApprovalCc(AjaxTodoBase):
    """
        乙供进度(出厂检验)--项管审批
    """

    def __int__(self):
        super(YGJDXgApprovalCc, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ygjd_xg_review_cc = process_data.get('ygjd_xg_review_cc')
        ygjd_xg_remark_cc = process_data.get('ygjd_xg_remark_cc')

        if ygjd_xg_review_cc == '驳回':
            if not ygjd_xg_remark_cc:
                return {"code": -1, "msg": "项管驳回时，请填写驳回原因"}

            query_sql = f"DELETE FROM equipment_production_racking_info " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND type = '出厂检验'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            "ygjd_xg_review_cc": ygjd_xg_review_cc,
            "ygjd_xg_remark_cc": ygjd_xg_remark_cc
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeliveryDataReviewJl(AjaxTodoBase):
    """
        发货资料审核（监理）
    """

    def __int__(self):
        super(DeliveryDataReviewJl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        fh_jl_review = process_data.get('fh_jl_review')
        fh_jl_remark = process_data.get('fh_jl_remark')
        if fh_jl_review == '驳回':
            if not fh_jl_remark:
                return {"code": -1, "msg": '监理驳回时，请填写驳回原因'}

        variables = {
            "fh_jl_review": fh_jl_review,
            "fh_jl_remark": fh_jl_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeliveryDataReviewXg(AjaxTodoBase):
    """
        发货资料审核（项管）
    """

    def __int__(self):
        super(DeliveryDataReviewXg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        fh_xg_review = process_data.get('fh_xg_review')
        fh_xg_remark = process_data.get('fh_xg_remark')
        if fh_xg_review == '驳回':
            if not fh_xg_remark:
                return {"code": -1, "msg": '项管驳回时，请填写驳回原因'}

        variables = {
            "fh_xg_review": fh_xg_review,
            "fh_xg_remark": fh_xg_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ArrivalDataReviewJl(AjaxTodoBase):
    """
        到货资料审核（监理）
    """

    def __int__(self):
        super(ArrivalDataReviewJl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        dh_jl_review = process_data.get('dh_jl_review')
        dh_jl_remark = process_data.get('dh_jl_remark')
        if dh_jl_review == '驳回':
            if not dh_jl_remark:
                return {"code": -1, "msg": '监理驳回时，请填写驳回原因'}

        variables = {
            "dh_jl_review": dh_jl_review,
            "dh_jl_remark": dh_jl_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ArrivalDataReviewXg(AjaxTodoBase):
    """
        到货资料审核（项管）
    """

    def __int__(self):
        super(ArrivalDataReviewXg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        dh_xg_review = process_data.get('dh_xg_review')
        dh_xg_remark = process_data.get('dh_xg_remark')
        if dh_xg_review == '驳回':
            if not dh_xg_remark:
                return {"code": -1, "msg": '项管驳回时，请填写驳回原因'}

        variables = {
            "dh_xg_review": dh_xg_review,
            "dh_xg_remark": dh_xg_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SignReceivedGoodsSiteJl(AjaxTodoBase):
    """
        现场签收到货资料-监理
    """

    def __int__(self):
        super(SignReceivedGoodsSiteJl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        xcqs_jl_review = process_data.get('xcqs_jl_review')
        xcqs_jl_remark = process_data.get('xcqs_jl_remark')
        if xcqs_jl_review == '驳回':
            if not xcqs_jl_remark:
                return {"code": -1, "msg": '监理驳回时，请填写驳回原因'}

        variables = {
            "xcqs_jl_review": xcqs_jl_review,
            "xcqs_jl_remark": xcqs_jl_remark
        }

        project_name = self.ctx.variables.get("project_name")
        # 刷新下一待办处理人
        xg = refresh_corresponding_account_of_role(self.ctx.variables.get("xg"), project_name)
        variables.update({"xg": xg})

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SignReceivedGoodsSiteXg(AjaxTodoBase):
    """
        现场签收到货资料-项管
    """

    def __int__(self):
        super(SignReceivedGoodsSiteXg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        xcqs_xg_review = process_data.get('xcqs_xg_review')
        xcqs_xg_remark = process_data.get('xcqs_xg_remark')

        if xcqs_xg_review == '驳回':
            if not xcqs_xg_remark:
                return {"code": -1, "msg": '项管驳回时，请填写驳回原因'}

        variables = {
            "xcqs_xg_review": xcqs_xg_review,
            "xcqs_xg_remark": xcqs_xg_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SignReceivedGoodsSitePM(AjaxTodoBase):
    """
        现场签收到货资料-PM
    """

    def __int__(self):
        super(SignReceivedGoodsSitePM, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        receipt_time = process_data.get('receipt_time')
        pm_review = process_data.get('pm_review')
        pm_remark = process_data.get('pm_remark')
        if pm_review == '驳回':
            if not pm_remark:
                return {"code": -1, "msg": 'PM驳回时，请填写驳回原因'}

        upload_receiving_information = []
        receipt_list = self.ctx.variables.get('receipt_list')
        for item in receipt_list:
            upload_receiving_information.append(
                item.get("receipt_information")
            )
        variables = {
            "pm_review": pm_review,
            "pm_remark": pm_remark,
            "acceptance_time": receipt_time,
            "upload_receiving_information": upload_receiving_information
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CallInterfaceReturnData(object):
    """
        接口返回数据
    """

    def __init__(self):
        super(CallInterfaceReturnData, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def call_interface_return_data(self, ticket_id_list):
        """
            接口返回数据
        """
        Facilitator = "德衡"
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        instance_id = ''
        for row in ticket_id_list:
            ticket_id = row
            # 通过工单号获取instance_id
            data = {
                "ResultColumns": {
                    "InstanceId": ""
                },
                "SearchCondition": {'TicketId': ticket_id}
            }

            extra_data = {"SchemaId": "ticket_base"}
            ticket_info = gnetops.request(
                action="QueryData", method="Run", data=data, ext_data=extra_data
            )
            res_list = ticket_info['List']

            for item in res_list:
                instance_id = item.get('InstanceId')
            if not instance_id:
                return {
                    "code": -1,
                    "msg": "查询失败,工单不存在"
                }
            # 通过instance_id获取工单信息
            if instance_id:
                data_instanceId = {
                    "InstanceId": instance_id
                }
                # 请求获取工作流日志
                res_list = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
                for res in res_list:
                    if res.get('TaskName') == '等待发货资料提交':
                        # 通过instance_id获取流程变量
                        if instance_id:
                            process_variable_data = {
                                "InstanceId": instance_id,
                                "Keylist": [
                                    "device_name", "project_name", "category_name", "equipment_type", "supplier",
                                    "testing_plan_list",
                                    "production_preparation_list",
                                    # 备料                         生产                      出厂
                                    "prepare_materials_list", "production_list", "factory_inspection_list"
                                ]
                            }
                            process_variable_info = gnetops.request(
                                action="Ticket", method="GetHistoryVariables", ext_data=process_variable_data
                            )
                            if process_variable_info:
                                device_name = process_variable_info['device_name']
                                project_name = process_variable_info['project_name']
                                category_name = process_variable_info['category_name']
                                supply_type = process_variable_info['equipment_type']
                                supplier = process_variable_info['supplier']
                                testing_plan_list = process_variable_info['testing_plan_list']
                                production_preparation_list = process_variable_info['production_preparation_list']
                                prepare_materials_list = process_variable_info['prepare_materials_list']  # 备料
                                production_list = process_variable_info['production_list']  # 生产
                                factory_inspection_list = process_variable_info['factory_inspection_list']  # 出厂

                                # 建单
                                jd_data = {
                                    "Action": "Ticket",  # 原样填写
                                    "Method": "Create",  # 原样填写
                                    "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                                    # 调用方系统标识
                                    "CorpId": corp_id,  # (*必填)
                                    # 服务商企业ID
                                    "Data": {  # 自定义流程变量
                                        "CustomVariables": {
                                            "DeviceName": device_name,
                                            "ProjectName": project_name,
                                            "SupplyType": supply_type,
                                            "CategoryName": category_name,
                                            "TencentTicketId": ticket_id
                                        },
                                        # 流程定义标识
                                        "ProcessDefinitionKey": "tb_dcs-create-order-A-copy",  # (*必填)
                                        # 工单来源
                                        "Source": f"外部系统（{Facilitator}）",
                                        # 工单描述
                                        "TicketDescription": f"{project_name}-{device_name}:合作版设备生产跟踪",
                                        # (*必填)
                                        # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                                        "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                                        # 工单标题
                                        "TicketTitle": f"{project_name}-{device_name}:合作版设备生产跟踪",  # (*必填)
                                        "UserInfo": {  # 用户信息
                                            "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                                            "Creator": "v_mmywang",  # (*必填)
                                            "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                                        }
                                    }
                                }
                                info = calling_external_interfaces(interface_info, jd_data)
                                PartnerTicketId = str(info['data']['TicketId'])

                                # 通过工单号修改流程变量数据
                                variables = {"PartnerTicketId": PartnerTicketId}
                                WorkflowVarUpdate(instance_id=instance_id,
                                                  variables=variables
                                                  ).call()

                                # 生产计划
                                passing_parameters = {
                                    "ProjectName": project_name,  # 项目名称
                                    "DeviceName": device_name,  # 设备名称
                                    "Supplier": supplier,  # 供应商
                                    "ProductionPreparationPlan": testing_plan_list[0].get("production_preparation"),
                                    # 生产准备完成时间
                                    "PrepareMaterialsPlan": testing_plan_list[0].get("prepare_materials"),  # 备料完成时间
                                    "ProductionPlan": testing_plan_list[0].get("production"),  # 生产完成时间
                                    "FactoryInspectionPlan": testing_plan_list[0].get("factory_inspection"),  # 出厂检验完成时间
                                    "ShippingApplicationPlan": testing_plan_list[0].get("shipping_application"),
                                    # 发货完成时间
                                    "ReceivingSignPlan": testing_plan_list[0].get("receiving_sign"),  # 到货完成时间
                                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                                }
                                # 构造请求数据
                                scjh_data = {
                                    "SystemId": "13",
                                    "Action": "Nbroker",
                                    "Method": "UrlProxy",
                                    "NbrokerData": {
                                        "context": {
                                            "service_name": "TCForward",  # (*必填) 原样填写
                                            "method_name": "index"  # (*必填) 原样填写
                                        },
                                        "args": {
                                            "function_name": "supply_equipment_plan",  # (*必填) 云函数名称
                                            "data": passing_parameters,  # 传递给云函数的入参
                                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                                        }
                                    },
                                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                                }
                                calling_external_interfaces(interface_info, scjh_data)

                                # 生产准备
                                passing_parameters = {
                                    "ProjectName": project_name,  # 项目名称
                                    "DeviceName": device_name,  # 设备名称
                                    "PoIssueTime": production_preparation_list[0].get("real_time"),  # PO下发时间
                                    "DesignConfirmationTime": production_preparation_list[0].get("confirm_time"),
                                    # 设备确认时间
                                    "ProductionPreparation": production_preparation_list[0].get(
                                        "production_preparation_time"),
                                    # 生产准备完成时间
                                    "Remark": production_preparation_list[0].get("remark"),  # 备注
                                    "Appendix": production_preparation_list[0].get("upload_info"),  # 附件
                                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                                }
                                # 构造请求数据
                                sczb_data = {
                                    "SystemId": "13",
                                    "Action": "Nbroker",
                                    "Method": "UrlProxy",
                                    "NbrokerData": {
                                        "context": {
                                            "service_name": "TCForward",  # (*必填) 原样填写
                                            "method_name": "index"  # (*必填) 原样填写
                                        },
                                        "args": {
                                            "function_name": "supply_production_preparation_progress",  # (*必填) 云函数名称
                                            "data": passing_parameters,  # 传递给云函数的入参
                                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                                        }
                                    },
                                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                                }
                                calling_external_interfaces(interface_info, sczb_data)

                                # 设备进度
                                # 备料
                                bl_passing_parameters = {
                                    "ProjectName": project_name,  # 项目名称
                                    "DeviceName": device_name,  # 设备名称
                                    "ProgressStage": 1,  # 阶段（备料）
                                    "ActualCompletion": prepare_materials_list[0].get("real_time"),  # 备料完成时间
                                    "Remark": prepare_materials_list[0].get("remark"),  # 备注
                                    "Appendix": prepare_materials_list[0].get("upload_info"),  # 附件
                                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                                }
                                # 生产
                                sc_passing_parameters = {
                                    "ProjectName": project_name,  # 项目名称
                                    "DeviceName": device_name,  # 设备名称
                                    "ProgressStage": 2,  # 阶段（生产）
                                    "ActualCompletion": production_list[0].get("real_time"),  # 产完成时间
                                    "Remark": production_list[0].get("remark"),  # 备注
                                    "Appendix": production_list[0].get("upload_info"),  # 附件
                                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                                }
                                # 出厂检验
                                cc_passing_parameters = {
                                    "ProjectName": project_name,  # 项目名称
                                    "DeviceName": device_name,  # 设备名称
                                    "ProgressStage": 3,  # 阶段（出场）
                                    "ActualCompletion": factory_inspection_list[0].get("real_time"),  # 出场完成时间
                                    "Remark": factory_inspection_list[0].get("remark"),  # 备注
                                    "Appendix": factory_inspection_list[0].get("upload_info"),  # 附件
                                    "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
                                }
                                # 备料构造请求数据
                                bl_data = {
                                    "SystemId": "13",
                                    "Action": "Nbroker",
                                    "Method": "UrlProxy",
                                    "NbrokerData": {
                                        "context": {
                                            "service_name": "TCForward",  # (*必填) 原样填写
                                            "method_name": "index"  # (*必填) 原样填写
                                        },
                                        "args": {
                                            "function_name": "supply_equipment_progress",  # (*必填) 云函数名称
                                            "data": bl_passing_parameters,  # 传递给云函数的入参
                                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                                        }
                                    },
                                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                                }
                                # 生产构造请求数据
                                sc_data = {
                                    "SystemId": "13",
                                    "Action": "Nbroker",
                                    "Method": "UrlProxy",
                                    "NbrokerData": {
                                        "context": {
                                            "service_name": "TCForward",  # (*必填) 原样填写
                                            "method_name": "index"  # (*必填) 原样填写
                                        },
                                        "args": {
                                            "function_name": "supply_equipment_progress",  # (*必填) 云函数名称
                                            "data": sc_passing_parameters,  # 传递给云函数的入参
                                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                                        }
                                    },
                                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                                }
                                # 出厂检验构造请求数据
                                cc_data = {
                                    "SystemId": "13",
                                    "Action": "Nbroker",
                                    "Method": "UrlProxy",
                                    "NbrokerData": {
                                        "context": {
                                            "service_name": "TCForward",  # (*必填) 原样填写
                                            "method_name": "index"  # (*必填) 原样填写
                                        },
                                        "args": {
                                            "function_name": "supply_equipment_progress",  # (*必填) 云函数名称
                                            "data": cc_passing_parameters,  # 传递给云函数的入参
                                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                                        }
                                    },
                                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                                }
                                calling_external_interfaces(interface_info, bl_data)
                                calling_external_interfaces(interface_info, sc_data)
                                calling_external_interfaces(interface_info, cc_data)
                    else:
                        continue
