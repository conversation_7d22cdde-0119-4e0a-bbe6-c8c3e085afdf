import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_application_process.template import TemplateAction


# 初验信息填写
class InitialAcceptanceInfoInput(AjaxTodoBase):
    def __init__(self):
        super(InitialAcceptanceInfoInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取数据
        info_dict = process_data.get("info_dict")
        initial_files = info_dict.get("initial_files")
        appendix_list = []
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
            initial_files, "附件上传"
        )
        if not file_exit_flag:
            return file_exit_msg
        for file in initial_files:
            appendix_list.append(file["response"]["FileList"][0]["url"])
        # 获取验收po数据
        selected_switch = process_data.get("selected_switch")
        po_category_dict = process_data.get("po_category_dict")
        # 取初验实际完成时间
        date = datetime.datetime.strptime(
            info_dict.get("actual_finish_time"), "%Y-%m-%d"
        )
        real_time = date.strftime("%Y-%m-%d %H:%M:%S")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "info_dict": info_dict,
            "appendix_list": appendix_list,
            "real_time": real_time,
            "selected_switch": selected_switch,
            "po_category_dict": po_category_dict,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 项目初验邮件推送
class InitialAcceptanceEmailPush(AjaxTodoBase):
    def __init__(self):
        super(InitialAcceptanceEmailPush, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取数据
        email_title = process_data.get("email_title")
        email_content = process_data.get("email_content")
        email_receiver = process_data.get("email_receiver")
        email_Cc = process_data.get("email_Cc")
        email_receiver_list = email_receiver.split(";")
        email_Cc_list = []
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        tof.send_email(
            sendTitle=email_title,
            msgContent=email_content,
            sendTo=email_receiver_list,
            sendCopy=email_Cc_list,
        )
        variables = {
            "email_title": email_title,
            "email_content": email_content,
            "email_receiver": email_receiver,
            "email_Cc": email_Cc,
            "email_receiver_list": email_receiver_list,
            "email_Cc_list": email_Cc_list,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
