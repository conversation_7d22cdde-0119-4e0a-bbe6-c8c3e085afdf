from datetime import datetime

from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class ConstructionIssueManagement(object):
    """
        项目管理：问题管理
    """

    def indicator_card_numbers(self, project_name):
        """
        指标卡数量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT problem_put_forward_time,problem_end_time" \
               f" FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.problem_put_forward_time, qtm.problem_end_time FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = "SELECT problem_put_forward_time,problem_end_time" \
               f" FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.problem_put_forward_time, em.problem_end_time FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql3 = "SELECT problem_put_forward_time,problem_end_time" \
               f" FROM safety_problem WHERE project_name='{project_name}'"
        sql6 = "SELECT sp.problem_put_forward_time, sp.problem_end_time FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        problem_list = (list(db.get_all(sql1)) + list(db.get_all(sql2)) + list(db.get_all(sql3))
                        + list(db.get_all(sql4)) + list(db.get_all(sql5)) + list(db.get_all(sql6)))
        solve = 0
        unsolved = 0
        for i in problem_list:
            if i.get("problem_end_time"):
                solve += 1
            else:
                unsolved += 1
        if solve == 0 and unsolved == 0:
            return {
                'solved_number': 0,
                'unsolved_number': 0,
                'total_number': 0,
                'percentage': '0.00%'
            }
        total_number = solve + unsolved
        data = {
            'solved_number': solve,
            'unsolved_number': unsolved,
            'total_number': total_number,
            'percentage': "{:.2f}%".format(round(float(solve) / float(total_number) * 100))
        }

        return data

    def question_total(self, project_name):
        """
            三种问题类型：总量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = f"SELECT problem_category FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.problem_category FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql3 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'"
        sql6 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        equipment_list = list(db.get_all(sql2)) + list(db.get_all(sql5))
        safety_list = list(db.get_all(sql3)) + list(db.get_all(sql6))
        total_number = len(quality_list) + len(equipment_list) + len(safety_list)
        if total_number == 0:
            return [{"value": 0, "name": '甲供设备管理', 'percentage': "0.00%"},
                    {"value": 0, "name": '质量工艺管理', 'percentage': "0.00%"},
                    {"value": 0, "name": '安全问题', 'percentage': "0.00%"}]
        data = []
        if quality_list:
            data.append({"value": len(quality_list), "name": '质量工艺管理',
                         'percentage': "{:.2f}%".format(round(float(len(quality_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '甲供设备管理', 'percentage': '0.00%'})
        if equipment_list:
            data.append({"value": len(equipment_list), "name": '甲供设备管理',
                         'percentage': "{:.2f}%".format(round(float(len(equipment_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '质量工艺管理', 'percentage': '0.00%'})
        if safety_list:
            data.append({"value": len(safety_list), "name": '安全问题',
                         'percentage': "{:.2f}%".format(round(float(len(safety_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '安全问题', 'percentage': '0.00%'})
        return data

    def question_unsolved_total(self, project_name):
        """
            各类未完成问题总量
        """

        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND qtm.problem_end_time IS NULL"

        sql2 = f"SELECT problem_category FROM equipment_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql5 = "SELECT em.problem_category FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND em.problem_end_time IS NULL"

        sql3 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql6 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND sp.problem_end_time IS NULL"

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        equipment_list = list(db.get_all(sql2)) + list(db.get_all(sql5))
        safety_list = list(db.get_all(sql3)) + list(db.get_all(sql6))
        total_number = len(quality_list) + len(equipment_list) + len(safety_list)
        if total_number == 0:
            return [{"value": 0, "name": '甲供设备管理', 'percentage': "0.00%"},
                    {"value": 0, "name": '质量工艺管理', 'percentage': "0.00%"},
                    {"value": 0, "name": '安全问题', 'percentage': "0.00%"}]
        data = []
        if quality_list:
            data.append({"value": len(quality_list), "name": '质量工艺管理',
                         'percentage': "{:.2f}%".format(round(float(len(quality_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '甲供设备管理', 'percentage': '0.00%'})
        if equipment_list:
            data.append({"value": len(equipment_list), "name": '甲供设备管理',
                         'percentage': "{:.2f}%".format(round(float(len(equipment_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '质量工艺管理', 'percentage': '0.00%'})
        if safety_list:
            data.append({"value": len(safety_list), "name": '安全问题',
                         'percentage': "{:.2f}%".format(round(float(len(safety_list)) / float(total_number) * 100))})
        else:
            data.append({"value": 0, "name": '安全问题', 'percentage': '0.00%'})
        return data

    def question_category_details(self, project_name):
        """
            下钻：各类问题细分类
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = f"SELECT problem_category FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.problem_category FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql3 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'"
        sql6 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        equipment_list = list(db.get_all(sql2)) + list(db.get_all(sql5))
        safety_list = list(db.get_all(sql3)) + list(db.get_all(sql6))
        total_quality = len(quality_list)
        total_equipment = len(equipment_list)
        total_safety = len(safety_list)
        # 甲供信息
        if total_equipment == 0:
            equipment_category_list = [{"value": 0, "name": '进度', 'percentage': '0.00%'},
                                       {"value": 0, "name": '质量', 'percentage': '0.00%'},
                                       {"value": 0, "name": '工艺', 'percentage': '0.00%'}, ]
        else:
            jd = 0
            zl = 0
            gy = 0
            for q in equipment_list:
                if q.get("problem_category") == '进度':
                    jd += 1
                elif q.get("problem_category") == '质量':
                    zl += 1
                elif q.get("problem_category") == '工艺':
                    gy += 1
            equipment_category_list = [{"value": jd, "name": '进度',
                                        'percentage': "{:.2f}%".format(
                                            round(float(jd) / float(total_equipment) * 100))},
                                       {"value": zl, "name": '质量',
                                        'percentage': "{:.2f}%".format(
                                            round(float(zl) / float(total_equipment) * 100))},
                                       {"value": gy, "name": '工艺',
                                        'percentage': "{:.2f}%".format(
                                            round(float(gy) / float(total_equipment) * 100))}]
        # 设备信息
        if total_quality == 0:
            quality_category_list = [{"value": 0, "name": '建设管理', 'percentage': '0.00%'},
                                     {"value": 0, "name": 'TB设计', 'percentage': '0.00%'},
                                     {"value": 0, "name": '乙供设备供货', 'percentage': '0.00%'},
                                     {"value": 0, "name": '乙供产品质量', 'percentage': '0.00%'},
                                     {"value": 0, "name": '土建建设', 'percentage': '0.00%'},
                                     {"value": 0, "name": '资源配套(政府)', 'percentage': '0.00%'}, ]
        else:
            js = 0
            tb = 0
            gh = 0
            cp = 0
            tj = 0
            zy = 0
            for e in quality_list:
                if e.get("problem_category") == '建设管理':
                    js += 1
                elif e.get("problem_category") == 'TB设计':
                    tb += 1
                elif e.get("problem_category") == '乙供设备供货':
                    gh += 1
                elif e.get("problem_category") == '乙供产品质量':
                    cp += 1
                elif e.get("problem_category") == '土建建设':
                    tj += 1
                elif e.get("problem_category") == '资源配套(政府)':
                    zy += 1
            quality_category_list = [{"value": js, "name": '建设管理',
                                      'percentage': "{:.2f}%".format(round(float(js) / float(total_quality) * 100))},
                                     {"value": tb, "name": 'TB设计',
                                      'percentage': "{:.2f}%".format(round(float(tb) / float(total_quality) * 100))},
                                     {"value": gh, "name": '乙供设备供货',
                                      'percentage': "{:.2f}%".format(round(float(gh) / float(total_quality) * 100))},
                                     {"value": cp, "name": '乙供产品质量',
                                      'percentage': "{:.2f}%".format(round(float(cp) / float(total_quality) * 100))},
                                     {"value": tj, "name": '土建建设',
                                      'percentage': "{:.2f}%".format(round(float(tj) / float(total_quality) * 100))},
                                     {"value": zy, "name": '资源配套(政府)',
                                      'percentage': "{:.2f}%".format(
                                          round(float(zy) / float(total_quality) * 100))}, ]

        # 安全问题
        if total_safety == 0:
            safety_category_list = [{"value": 0, "name": '三宝四口五临边', 'percentage': '0.00%'},
                                    {"value": 0, "name": '高处作业', 'percentage': '0.00%'},
                                    {"value": 0, "name": '安全用电', 'percentage': '0.00%'},
                                    {"value": 0, "name": '施工机具', 'percentage': '0.00%'},
                                    {"value": 0, "name": '防火', 'percentage': '0.00%'},
                                    {"value": 0, "name": '起重吊装', 'percentage': '0.00%'},
                                    {"value": 0, "name": '其他', 'percentage': '0.00%'}, ]
        else:
            lb = 0
            gc = 0
            aq = 0
            sg = 0
            fh = 0
            qz = 0
            qt = 0
            for s in safety_list:
                if s.get("problem_category") == '三宝四口五临边':
                    lb += 1
                elif s.get("problem_category") == '高处作业':
                    gc += 1
                elif s.get("problem_category") == '安全用电':
                    aq += 1
                elif s.get("problem_category") == '施工机具':
                    sg += 1
                elif s.get("problem_category") == '防火':
                    fh += 1
                elif s.get("problem_category") == '起重吊装':
                    qz += 1
                else:
                    qt += 1

            safety_category_list = [{"value": lb, "name": '三宝四口五临边',
                                     'percentage': "{:.2f}%".format(round(float(lb) / float(total_safety) * 100))},
                                    {"value": gc, "name": '高处作业',
                                     'percentage': "{:.2f}%".format(round(float(gc) / float(total_safety) * 100))},
                                    {"value": aq, "name": '安全用电',
                                     'percentage': "{:.2f}%".format(round(float(aq) / float(total_safety) * 100))},
                                    {"value": sg, "name": '施工机具',
                                     'percentage': "{:.2f}%".format(round(float(sg) / float(total_safety) * 100))},
                                    {"value": fh, "name": '防火',
                                     'percentage': "{:.2f}%".format(round(float(fh) / float(total_safety) * 100))},
                                    {"value": qz, "name": '起重吊装',
                                     'percentage': "{:.2f}%".format(round(float(qz) / float(total_safety) * 100))},
                                    {"value": qt, "name": '其他',
                                     'percentage': "{:.2f}%".format(round(float(qt) / float(total_safety) * 100))}
                                    ]
        data = {
            "quality_category_list": quality_category_list,
            "equipment_category_list": equipment_category_list,
            "safety_category_list": safety_category_list

        }
        return data

    def question_category_unsolved_details(self, project_name):
        """
            下钻：各类未解决问题细分类
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND qtm.problem_end_time IS NULL"

        sql2 = f"SELECT problem_category FROM equipment_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql5 = "SELECT em.problem_category FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND em.problem_end_time IS NULL"

        sql3 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql6 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND sp.problem_end_time IS NULL"

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        equipment_list = list(db.get_all(sql2)) + list(db.get_all(sql5))
        safety_list = list(db.get_all(sql3)) + list(db.get_all(sql6))
        total_quality = len(quality_list)
        total_equipment = len(equipment_list)
        total_safety = len(safety_list)
        # 甲供信息
        if total_equipment == 0:
            equipment_category_list = [{"value": 0, "name": '进度', 'percentage': '0.00%'},
                                       {"value": 0, "name": '质量', 'percentage': '0.00%'},
                                       {"value": 0, "name": '工艺', 'percentage': '0.00%'}, ]
        else:
            jd = 0
            zl = 0
            gy = 0
            for q in equipment_list:
                if q.get("problem_category") == '进度':
                    jd += 1
                elif q.get("problem_category") == '质量':
                    zl += 1
                elif q.get("problem_category") == '工艺':
                    gy += 1
            equipment_category_list = [{"value": jd, "name": '进度',
                                        'percentage': "{:.2f}%".format(
                                            round(float(jd) / float(total_equipment) * 100))},
                                       {"value": zl, "name": '质量',
                                        'percentage': "{:.2f}%".format(
                                            round(float(zl) / float(total_equipment) * 100))},
                                       {"value": gy, "name": '工艺',
                                        'percentage': "{:.2f}%".format(
                                            round(float(gy) / float(total_equipment) * 100))}]
        # 设备信息
        if total_quality == 0:
            quality_category_list = [{"value": 0, "name": '建设管理', 'percentage': '0.00%'},
                                     {"value": 0, "name": 'TB设计', 'percentage': '0.00%'},
                                     {"value": 0, "name": '乙供设备供货', 'percentage': '0.00%'},
                                     {"value": 0, "name": '乙供产品质量', 'percentage': '0.00%'},
                                     {"value": 0, "name": '土建建设', 'percentage': '0.00%'},
                                     {"value": 0, "name": '资源配套(政府)', 'percentage': '0.00%'}, ]
        else:
            js = 0
            tb = 0
            gh = 0
            cp = 0
            tj = 0
            zy = 0
            for e in quality_list:
                if e.get("problem_category") == '建设管理':
                    js += 1
                elif e.get("problem_category") == 'TB设计':
                    tb += 1
                elif e.get("problem_category") == '乙供设备供货':
                    gh += 1
                elif e.get("problem_category") == '乙供产品质量':
                    cp += 1
                elif e.get("problem_category") == '土建建设':
                    tj += 1
                elif e.get("problem_category") == '资源配套(政府)':
                    zy += 1
            quality_category_list = [{"value": js, "name": '建设管理',
                                      'percentage': "{:.2f}%".format(round(float(js) / float(total_quality) * 100))},
                                     {"value": tb, "name": 'TB设计',
                                      'percentage': "{:.2f}%".format(round(float(tb) / float(total_quality) * 100))},
                                     {"value": gh, "name": '乙供设备供货',
                                      'percentage': "{:.2f}%".format(round(float(gh) / float(total_quality) * 100))},
                                     {"value": cp, "name": '乙供产品质量',
                                      'percentage': "{:.2f}%".format(round(float(cp) / float(total_quality) * 100))},
                                     {"value": tj, "name": '土建建设',
                                      'percentage': "{:.2f}%".format(round(float(tj) / float(total_quality) * 100))},
                                     {"value": zy, "name": '资源配套(政府)',
                                      'percentage': "{:.2f}%".format(
                                          round(float(zy) / float(total_quality) * 100))}, ]

        # 安全问题
        if total_safety == 0:
            safety_category_list = [{"value": 0, "name": '三宝四口五临边', 'percentage': '0.00%'},
                                    {"value": 0, "name": '高处作业', 'percentage': '0.00%'},
                                    {"value": 0, "name": '安全用电', 'percentage': '0.00%'},
                                    {"value": 0, "name": '施工机具', 'percentage': '0.00%'},
                                    {"value": 0, "name": '防火', 'percentage': '0.00%'},
                                    {"value": 0, "name": '起重吊装', 'percentage': '0.00%'},
                                    {"value": 0, "name": '其他', 'percentage': '0.00%'}, ]
        else:
            lb = 0
            gc = 0
            aq = 0
            sg = 0
            fh = 0
            qz = 0
            qt = 0
            for s in safety_list:
                if s.get("problem_category") == '三宝四口五临边':
                    lb += 1
                elif s.get("problem_category") == '高处作业':
                    gc += 1
                elif s.get("problem_category") == '安全用电':
                    aq += 1
                elif s.get("problem_category") == '施工机具':
                    sg += 1
                elif s.get("problem_category") == '防火':
                    fh += 1
                elif s.get("problem_category") == '起重吊装':
                    qz += 1
                else:
                    qt += 1

            safety_category_list = [{"value": lb, "name": '三宝四口五临边',
                                     'percentage': "{:.2f}%".format(round(float(lb) / float(total_safety) * 100))},
                                    {"value": gc, "name": '高处作业',
                                     'percentage': "{:.2f}%".format(round(float(gc) / float(total_safety) * 100))},
                                    {"value": aq, "name": '安全用电',
                                     'percentage': "{:.2f}%".format(round(float(aq) / float(total_safety) * 100))},
                                    {"value": sg, "name": '施工机具',
                                     'percentage': "{:.2f}%".format(round(float(sg) / float(total_safety) * 100))},
                                    {"value": fh, "name": '防火',
                                     'percentage': "{:.2f}%".format(round(float(fh) / float(total_safety) * 100))},
                                    {"value": qz, "name": '起重吊装',
                                     'percentage': "{:.2f}%".format(round(float(qz) / float(total_safety) * 100))},
                                    {"value": qt, "name": '其他',
                                     'percentage': "{:.2f}%".format(round(float(qt) / float(total_safety) * 100))}
                                    ]
        data = {
            "quality_category_unsolved": quality_category_list,
            "equipment_category_unsolved": equipment_category_list,
            "safety_category_unsolved": safety_category_list

        }
        return data

    def problem_impact(self, project_name):
        """
            下钻：各类问题——影响分类
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.impact_classification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = f"SELECT impact_classification FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.impact_classification FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4)) + list(db.get_all(sql2)) + list(db.get_all(sql5))
        total = len(quality_list)
        if total == 0:
            quality_impact_list = [{"value": 0, "name": '进度', 'percentage': '0.00%'},
                                   {"value": 0, "name": '质量', 'percentage': '0.00%'},
                                   {"value": 0, "name": '合规', 'percentage': '0.00%'}]
        else:
            jd = 0
            zl = 0
            hg = 0
            for quality in quality_list:
                if quality.get("impact_classification") == "进度":
                    jd += 1
                elif quality.get("impact_classification") == "质量":
                    zl += 1
                elif quality.get("impact_classification") == "合规":
                    hg += 1
            quality_impact_list = [{"value": jd, "name": '进度',
                                    'percentage': "{:.2f}%".format(
                                        round(float(jd) / float(total) * 100))},
                                   {"value": zl, "name": '质量',
                                    'percentage': "{:.2f}%".format(
                                        round(float(zl) / float(total) * 100))},
                                   {"value": hg, "name": '合规',
                                    'percentage': "{:.2f}%".format(
                                        round(float(hg) / float(total) * 100))}]

        return quality_impact_list

    def problem_impact_unsolve(self, project_name):
        """
            下钻：各类未解决问题——影响分类
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM quality_technics_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT qtm.impact_classification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND qtm.problem_end_time IS NULL"

        sql2 = f"SELECT impact_classification FROM equipment_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql5 = "SELECT em.impact_classification FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND em.problem_end_time IS NULL"

        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4)) + list(db.get_all(sql2)) + list(db.get_all(sql5))
        total = len(quality_list)
        if total == 0:
            quality_impact_list = [{"value": 0, "name": '进度', 'percentage': '0.00%'},
                                   {"value": 0, "name": '质量', 'percentage': '0.00%'},
                                   {"value": 0, "name": '合规', 'percentage': '0.00%'}]
        else:
            jd = 0
            zl = 0
            hg = 0
            for quality in quality_list:
                if quality.get("impact_classification") == "进度":
                    jd += 1
                elif quality.get("impact_classification") == "质量":
                    zl += 1
                elif quality.get("impact_classification") == "合规":
                    hg += 1
            quality_impact_list = [{"value": jd, "name": '进度',
                                    'percentage': "{:.2f}%".format(
                                        round(float(jd) / float(total) * 100))},
                                   {"value": zl, "name": '质量',
                                    'percentage': "{:.2f}%".format(
                                        round(float(zl) / float(total) * 100))},
                                   {"value": hg, "name": '合规',
                                    'percentage': "{:.2f}%".format(
                                        round(float(hg) / float(total) * 100))}]

        return quality_impact_list

    def basic_logic(self, page_list):
        # 构造工单号列表
        ticket_id_list = []
        for page in page_list:
            put_forward_time = page.get('problem_put_forward_time')
            end_time = page.get('problem_end_time')
            # 转换日期数据格式
            if put_forward_time is not None:
                problem_put_forward_time = put_forward_time.strftime("%Y-%m-%d")
                page['problem_put_forward_time'] = problem_put_forward_time
            # 转换日期格式
            # 判断问题是否关闭
            if end_time is not None:
                page['state'] = '已关闭'
                page["deal_users"] = ''
                problem_end_time = end_time.strftime("%Y-%m-%d")
                page['problem_end_time'] = problem_end_time
                duration = (end_time - put_forward_time).days + 1
                page['duration'] = str(duration)
            else:
                t_id = page.get('t_id')
                page['state'] = '未关闭'
                now = datetime.now()
                duration = (now - put_forward_time).days + 1
                if 3 < duration <= 7:
                    page['duration'] = "大于3天"
                elif 7 < duration <= 15:
                    page['duration'] = "大于7天"
                elif duration > 15:
                    page['duration'] = "大于15天"
                if t_id:
                    ticket_id_list.append(t_id)
                else:
                    page["deal_users"] = ''

            if page.get('problem_photo'):
                if "," in page.get('problem_photo'):
                    problem_url_list = page.get('problem_photo').split(",")
                else:
                    problem_url_list = page.get('problem_photo').split(";")
                page['problem_photo'] = problem_url_list
            if page.get('photos_after_rectification'):
                if "," in page.get('photos_after_rectification'):
                    url_list = page.get('photos_after_rectification').split(",")
                else:
                    url_list = page.get('photos_after_rectification').split(";")
                page['photos_after_rectification'] = url_list
        # 通过工单号获取流程id
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "CurrentAllProcessUsers": "",
                    "TicketId": ""
                },
                "SearchCondition": {
                    "TicketId": ticket_id_list
                }
            }
        }
        query_result = gnetops.request(
            action="QueryData",
            method="Run",
            ext_data=query_data
        )
        # 节点代办人
        result_list = query_result.get("List")
        for q in page_list:
            for item in result_list:
                if item.get('TicketId') == q.get('t_id'):
                    q['deal_users'] = item.get('CurrentAllProcessUsers')

        return page_list

    def problem_list(self, project_name, problemCategory, problemImpact, impactLevel, rectificationStatus,
                     page_size, page_number, t_id, responsible_unit, problem_put_forward_time, problem_end_time):
        db = mysql.new_mysql_instance("tbconstruct")

        sql1 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.t_id,qtm.question_item,qtm.problem_category,qtm.problem_location," \
               "qtm.problem_description,qtm.problem_photo,qtm.rectification_reform_measures,qtm.problem_level," \
               "qtm.responsible_unit,qtm.problem_put_forward_time,qtm.problem_impact," \
               "qtm.problem_end_time,qtm.photos_after_rectification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.t_id,em.question_item,em.problem_category,em.problem_location," \
               "em.problem_description,em.problem_photo,em.rectification_reform_measures,em.problem_level," \
               "em.responsible_unit,em.problem_put_forward_time,em.problem_impact, " \
               "em.problem_end_time,em.photos_after_rectification FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql3 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM safety_problem WHERE project_name='{project_name}'"
        sql6 = "SELECT sp.t_id,sp.question_item,sp.problem_category,sp.problem_location," \
               "sp.problem_description,sp.problem_photo,sp.rectification_reform_measures,sp.problem_level," \
               "sp.responsible_unit,sp.problem_put_forward_time,sp.problem_impact, " \
               "sp.problem_end_time,sp.photos_after_rectification FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        if rectificationStatus == '已关闭':
            sql2 += " AND problem_end_time IS NOT NULL "
            sql5 += " AND em.problem_end_time IS NOT NULL "
            sql1 += " AND problem_end_time IS NOT NULL "
            sql4 += " AND qtm.problem_end_time IS NOT NULL "
            sql3 += " AND problem_end_time IS NOT NULL "
            sql6 += " AND sp.problem_end_time IS NOT NULL "
        elif rectificationStatus == '未关闭':
            sql2 += " AND problem_end_time IS NULL"
            sql5 += " AND em.problem_end_time IS NULL "
            sql1 += " AND problem_end_time IS NULL"
            sql4 += " AND qtm.problem_end_time IS NULL "
            sql3 += " AND problem_end_time IS NULL"
            sql6 += " AND sp.problem_end_time IS NULL "
        if problemImpact:
            problem_impact = problemImpact[0]
            sql2 += f" AND impact_classification='{problem_impact}'"
            sql5 += f" AND em.impact_classification='{problem_impact}'"
            sql1 += f" AND impact_classification='{problem_impact}'"
            sql4 += f" AND qtm.impact_classification='{problem_impact}'"
            sql3 += f" AND impact_classification='{problem_impact}'"
            sql6 += f" AND sp.impact_classification='{problem_impact}'"
        if t_id:
            sql2 += f" AND t_id = '{t_id}' "
            sql5 += f" AND em.t_id = '{t_id}' "
            sql1 += f" AND t_id = '{t_id}' "
            sql4 += f" AND qtm.t_id = '{t_id}' "
            sql3 += f" AND t_id = '{t_id}' "
            sql6 += f" AND sp.t_id = '{t_id}' "
        if responsible_unit:
            sql2 += f" AND responsible_unit = '{responsible_unit}' "
            sql5 += f" AND em.responsible_unit = '{responsible_unit}' "
            sql1 += f" AND responsible_unit = '{responsible_unit}' "
            sql4 += f" AND qtm.responsible_unit = '{responsible_unit}' "
            sql3 += f" AND responsible_unit = '{responsible_unit}' "
            sql6 += f" AND sp.responsible_unit = '{responsible_unit}' "
        if problem_end_time and rectificationStatus == '未关闭':
            return {'code': 400, "msg": "传参错误"}
        if problem_put_forward_time and problem_end_time:
            if (type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2
                    and type(problem_end_time) == list and len(problem_end_time) == 2):
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                sql1 += (f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                         f"'{problem_put_end}') AND (problem_put_forward_time BETWEEN "
                         f"'{problem_end_start}' AND '{problem_end_end}')")
                sql4 += (f"AND (qtm.problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                         f"'{problem_put_end}') AND (qtm.problem_put_forward_time BETWEEN "
                         f"'{problem_end_start}' AND '{problem_end_end}')")
        elif problem_put_forward_time or problem_end_time:
            if type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2:
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                sql1 += f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"
                sql4 += f"AND (qtm.problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"

            if type(problem_end_time) == list and len(problem_end_time) == 2:
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                sql1 += f"AND (problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"
                sql4 += f"AND (qtm.problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"

        problem_list = []
        if problemCategory or impactLevel:
            if '甲供设备管理' in problemCategory or '甲供设备管理' in impactLevel:
                if len(problemCategory) > 1:
                    problem_category = problemCategory[1]
                    sql2 += f" AND problem_category='{problem_category}'"
                    sql5 += f" AND em.problem_category='{problem_category}'"
                if impactLevel and len(impactLevel) > 1:
                    impact_level = impactLevel[1]
                    sql2 += f" AND problem_level='{impact_level}'"
                    sql5 += f" AND em.problem_level='{impact_level}'"
                problem_list = list(db.get_all(sql2)) + list(db.get_all(sql5))

            elif '质量工艺管理' in problemCategory or '质量工艺管理' in impactLevel:
                if len(problemCategory) > 1:
                    problem_category = problemCategory[1]
                    sql1 += f" AND problem_category='{problem_category}'"
                    sql4 += f" AND qtm.problem_category='{problem_category}'"
                if impactLevel and len(impactLevel) > 1:
                    impact_level = impactLevel[1]
                    sql1 += f" AND problem_level='{impact_level}'"
                    sql4 += f" AND qtm.problem_level='{impact_level}'"
                problem_list = list(db.get_all(sql1)) + list(db.get_all(sql4))

            elif '安全问题' in problemCategory or '安全问题' in impactLevel:
                if len(problemCategory) > 1:
                    problem_category = problemCategory[1]
                    sql3 += f" AND problem_category='{problem_category}'"
                    sql6 += f" AND sp.problem_category='{problem_category}'"
                if impactLevel and len(impactLevel) > 1:
                    impact_level = impactLevel[1]
                    sql3 += f" AND problem_level='{impact_level}'"
                    sql6 += f" AND sp.problem_level='{impact_level}'"
                problem_list = list(db.get_all(sql3)) + list(db.get_all(sql6))

        else:
            problem_list = (list(db.get_all(sql1)) + list(db.get_all(sql2)) + list(db.get_all(sql3))
                            + list(db.get_all(sql4)) + list(db.get_all(sql5)) + list(db.get_all(sql6)))
        # 计算总数
        total = len(problem_list)
        # 计算总页数
        total_pages = (len(problem_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = problem_list[start_index:end_index]
        problem_list = self.basic_logic(page_list)
        return {"total": total, "problem_list": problem_list}

    def construction_export(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")

        sql1 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.t_id,qtm.question_item,qtm.problem_category,qtm.problem_location," \
               "qtm.problem_description,qtm.problem_photo,qtm.rectification_reform_measures,qtm.problem_level," \
               "qtm.responsible_unit,qtm.problem_put_forward_time,qtm.problem_impact, " \
               "qtm.problem_end_time,qtm.photos_after_rectification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql2 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM equipment_management WHERE project_name='{project_name}'"
        sql5 = "SELECT em.t_id,em.question_item,em.problem_category,em.problem_location," \
               "em.problem_description,em.problem_photo,em.rectification_reform_measures,em.problem_level," \
               "em.responsible_unit,em.problem_put_forward_time,em.problem_impact, " \
               "em.problem_end_time,em.photos_after_rectification FROM week_create_info wci " \
               "JOIN equipment_management em ON wci.ticketid = em.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "

        sql3 = "SELECT t_id,question_item,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact" \
               f" FROM safety_problem WHERE project_name='{project_name}'"
        sql6 = "SELECT sp.t_id,sp.question_item,sp.problem_category,sp.problem_location," \
               "sp.problem_description,sp.problem_photo,sp.rectification_reform_measures,sp.problem_level," \
               "sp.responsible_unit,sp.problem_put_forward_time,sp.problem_impact, " \
               "sp.problem_end_time,sp.photos_after_rectification FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        problem_list = (list(db.get_all(sql1)) + list(db.get_all(sql2)) + list(db.get_all(sql3))
                        + list(db.get_all(sql4)) + list(db.get_all(sql5)) + list(db.get_all(sql6)))
        question_list = self.basic_logic(problem_list)

        return question_list


class ConstructionRisksManagement(object):
    """
        项目管理：风险数据接口
    """

    def risk_warning_list(self, project_name, page_size, page_number):
        offset = (page_number - 1) * page_size  # 计算偏移量
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(risk_item) FROM construction_risk_warning_data WHERE project_name='{project_name}'"

        sql1 = "SELECT risk_item,state,effect,details_reason" \
               f" FROM construction_risk_warning_data WHERE project_name='{project_name}'"
        sql1 += f" LIMIT {offset}, {page_size}"
        # 总数
        count_info = db.get_all(sql)
        total_results = 0
        if count_info:
            total_results = count_info[0].get('count(risk_item)')
        risk_warning_list = db.get_all(sql1)
        return {"risk_warning_list": risk_warning_list, "total": total_results}
