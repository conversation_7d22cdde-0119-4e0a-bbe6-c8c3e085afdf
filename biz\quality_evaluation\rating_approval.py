from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


# 商务leader审批
class QEBusinessRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        business_rater_leader_approval = process_data.get(
            "business_rater_leader_approval", 1
        )
        business_rater_leader_remark = process_data.get(
            "business_rater_leader_remark", ""
        )
        business_rater_leader_reject_reason = ""
        if not business_rater_leader_approval:
            business_rater_leader_reject_reason = business_rater_leader_remark
        variables = {
            "business_rater_leader_approval": business_rater_leader_approval,
            "business_rater_leader_remark": business_rater_leader_remark,
            "business_rater_leader_reject_reason": business_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 供应链leader审批
class QESupplyChainRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        supply_chain_rater_leader_approval = process_data.get(
            "supply_chain_rater_leader_approval", 1
        )
        supply_chain_rater_leader_remark = process_data.get(
            "supply_chain_rater_leader_remark", ""
        )
        supply_chain_rater_leader_reject_reason = ""
        if not supply_chain_rater_leader_approval:
            supply_chain_rater_leader_reject_reason = supply_chain_rater_leader_remark
        variables = {
            "supply_chain_rater_leader_approval": supply_chain_rater_leader_approval,
            "supply_chain_rater_leader_remark": supply_chain_rater_leader_remark,
            "supply_chain_rater_leader_reject_reason": supply_chain_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 规划leader审批
class QEPlanningRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        planning_rater_leader_approval = process_data.get(
            "planning_rater_leader_approval", 1
        )
        planning_rater_leader_remark = process_data.get(
            "planning_rater_leader_remark", ""
        )
        planning_rater_leader_reject_reason = ""
        if not planning_rater_leader_approval:
            planning_rater_leader_reject_reason = planning_rater_leader_remark
        variables = {
            "planning_rater_leader_approval": planning_rater_leader_approval,
            "planning_rater_leader_remark": planning_rater_leader_remark,
            "planning_rater_leader_reject_reason": planning_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 产品leader审批
class QEProductRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        product_rater_leader_approval = process_data.get(
            "product_rater_leader_approval", 1
        )
        product_rater_leader_remark = process_data.get(
            "product_rater_leader_remark", ""
        )
        product_rater_leader_reject_reason = ""
        if not product_rater_leader_approval:
            product_rater_leader_reject_reason = product_rater_leader_remark
        variables = {
            "product_rater_leader_approval": product_rater_leader_approval,
            "product_rater_leader_remark": product_rater_leader_remark,
            "product_rater_leader_reject_reason": product_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 项目leader审批
class QEProjectRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_rater_leader_approval = process_data.get(
            "project_rater_leader_approval", 1
        )
        project_rater_leader_remark = process_data.get(
            "project_rater_leader_remark", ""
        )
        project_rater_leader_reject_reason = ""
        if not project_rater_leader_approval:
            project_rater_leader_reject_reason = project_rater_leader_remark
        variables = {
            "project_rater_leader_approval": project_rater_leader_approval,
            "project_rater_leader_remark": project_rater_leader_remark,
            "project_rater_leader_reject_reason": project_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 优化leader审批
class QEOptimizeRaterLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        optimize_rater_leader_approval = process_data.get(
            "optimize_rater_leader_approval", 1
        )
        optimize_rater_leader_remark = process_data.get(
            "optimize_rater_leader_remark", ""
        )
        optimize_rater_leader_reject_reason = ""
        if not optimize_rater_leader_approval:
            optimize_rater_leader_reject_reason = optimize_rater_leader_remark
        variables = {
            "optimize_rater_leader_approval": optimize_rater_leader_approval,
            "optimize_rater_leader_remark": optimize_rater_leader_remark,
            "optimize_rater_leader_reject_reason": optimize_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 弱电leader审批
class QEWeakElectricityLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        weak_electricity_rater_leader_approval = process_data.get(
            "weak_electricity_rater_leader_approval", 1
        )
        weak_electricity_rater_leader_remark = process_data.get(
            "weak_electricity_rater_leader_remark", ""
        )
        weak_electricity_rater_leader_reject_reason = ""
        if not weak_electricity_rater_leader_approval:
            weak_electricity_rater_leader_reject_reason = (
                weak_electricity_rater_leader_remark
            )
        variables = {
            "weak_electricity_rater_leader_approval": weak_electricity_rater_leader_approval,
            "weak_electricity_rater_leader_remark": weak_electricity_rater_leader_remark,
            "weak_electricity_rater_leader_reject_reason": weak_electricity_rater_leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# leader审批
class QualityEvaluationLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval", 1)
        leader_remark = process_data.get("leader_remark", "")
        leader_reject_reason = ""
        if not leader_approval:
            leader_reject_reason = leader_remark
        variables = {
            "leader_approval": leader_approval,
            "leader_remark": leader_remark,
            "leader_reject_reason": leader_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 总监审批
class QualityEvaluationDirectorApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        director_approval = process_data.get("director_approval", 1)
        director_remark = process_data.get("director_remark", "")
        director_reject_reason = ""
        if not director_approval:
            director_reject_reason = director_remark
        variables = {
            "director_approval": director_approval,
            "director_remark": director_remark,
            "director_reject_reason": director_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# GM审批
class QualityEvaluationGMApproval(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # GM节点启用moa审批
        GM_approval = process_data.get("GM_approval", "同意")
        GM_remark = process_data.get("GM_remark", "")
        GM_reject_reason = ""
        if GM_approval == "驳回":
            GM_reject_reason = GM_remark
        variables = {
            "GM_approval": GM_approval,
            "GM_remark": GM_remark,
            "GM_reject_reason": GM_reject_reason,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
