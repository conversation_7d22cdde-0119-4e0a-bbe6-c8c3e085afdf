import os
import uuid
from datetime import datetime

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql,curl
from iBroker.lib.sdk import tof
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from biz.construction_process.cos_lib import COSLib


class ConstructionStatusFeedbackUpload(AjaxTodoBase):
    def __init__(self):
        super(ConstructionStatusFeedbackUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        deal_users = self.ctx.variables.get("PM")
        project_name = self.ctx.variables.get("project_name")
        real_time = process_data.get("real_time")
        remark = process_data.get("remark")
        appendix = process_data.get("appendix")
        if real_time:
            try:
                datetime.strptime(real_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                parsed_time = datetime.strptime(real_time, '%Y-%m-%d %H:%M')
                acceptance_time = parsed_time.strftime('%Y-%m-%d 00:00:00')
        else:
            return {"code": -1, "msg": "请填写时间"}

        appendix_list = []

        if appendix:
            try:
                for file in appendix:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"附件上传错误:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"附件上传错误:{e}"}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "appendix": str(appendix_list),
            "remark": remark,
            'real_time': real_time,
            'type': '人员入场'
        }
        # 存数据库
        tb_db.begin()
        tb_db.insert("construction_status_feedback_process", data)
        tb_db.commit()
        variables = {
            "real_time": real_time,
            "appendix": appendix,
            'remark': remark,
            'appendix_list': appendix_list,
            'deal_users': deal_users,
            # 'po_info': []
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionStatusFeedback(object):
    def __init__(self):
        super(ConstructionStatusFeedback).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def statu_feedback(self):
        flow.complete_task(self.ctx.task_id)

    def feedback(self, project_name, appendix_list, real_time, deal_users):
        # 获取需求单号
        db = mysql.new_mysql_instance('tbconstruct')
        sql = "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd " \
              "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode " \
              f"WHERE rewd.project_name = '{project_name}' "

        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table('获取需求id', {'message': code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 物料id
            material_id_list = []
            # 需求单号
            # 通过需求单号获取po信息
            for i in code_id_list:
                code_id = i.get('idcrmOrderCode')
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"})
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
            for j in po_material_info:
                material_id = j.get('materialId')
                if material_id not in material_id_list:
                    material_id_list.append(material_id)
            self.workflow_detail.add_kv_table('最终po信息', {'message': po_material_info})
            # 通过物料id获取品名信息
            material_list = gnetops.request(action="Tbconstruct",
                                            method="ObtainMaterialInfoById",
                                            ext_data={
                                                "MaterialCodeId": material_id_list
                                            }
                                            )
            self.workflow_detail.add_kv_table('物料信息', {'message': material_list})
            if material_list:
                material_info_list = material_list.get('materialList')
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    # 物料id
                    material_id = material.get('materialQueryDTO').get('materialId')
                    # 品名
                    material_category_name = material.get('materialQueryDTO').get('materialCategoryName')
                    # 品名编码
                    material_category_code = material.get('materialQueryDTO').get('materialCategoryCode')
                    if material_category_name == '集成工程' and material_category_code == '796350BIEP':
                        data.append(material_id)
                    elif material_category_name == '机电项目管理' and material_category_code == '796350CPRM':
                        data.append(material_id)
                    elif material_category_name == '项目监理' and material_category_code == '796350CPRS':
                        data.append(material_id)
                self.workflow_detail.add_kv_table('构造数据', {'message': data})
                # 将获取到的特定品名的物料id与po信息中的id对比，找出需要的数据
                for d in data:
                    for po in po_material_info:
                        if d == po.get('materialId'):
                            if str(po.get('orderItemId')) != "0":
                                data_list.append({"AssetCode": "",
                                                  "LineDetailId": str(po.get('orderItemId')),
                                                  "QuantityReceived": po.get('orderAmount'),
                                                  "SnCode": "",
                                                  "AttachmentList": appendix_list
                                                  })

            self.workflow_detail.add_kv_table('传参信息', {'message': data_list})
            # 获取企微id
            chat_info = gnetops.request(action="Tof",
                                        method="GetIDCStaffIDByEngName",
                                        ext_data={
                                            "EngName": str(deal_users),
                                        }
                                        )
            self.workflow_detail.add_kv_table('获取企微id', {'message': str(chat_info)})
            chat_id = ''
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table('获取企微id',
                                                  {"success": False, "data": f"获取失败:{str(chat_info)}"})
            info = gnetops.request(action="Tbconstruct",
                                   method="FeedbackAcceptanceStatusToStar",
                                   ext_data={
                                       "FeedbackType": 4,  # 0=到货,1=安装,2=初验,3=终,人员入场=4
                                       "ReceiveDate": str(real_time),  # 时间
                                       "ReceiveUserId": chat_id,  # id
                                       "Orderdetails": data_list,
                                   }
                                   )

            variables = {
                "po_material_info": po_material_info,
                "data_list": data_list,
                "info": str(info),
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table('获取po信息', {'message': '获取不到需求单号'})
            return {'code': -1, 'message': '获取不到需求单号'}

    def get_receiver(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT PM,architecture,beautification,tree_sutra,business,supply_chain,inform_a_person,weak_current " \
              "FROM project_team_information_data WHERE project_name='%s'" % project_name
        email_receiver = db.get_all(sql)
        # email_receiver_list = []
        receiver = ''
        if email_receiver:
            item = email_receiver[0]
            for key, value in item.items():
                name_list = value.split(';')
                for name in name_list:
                    receiver += name + ';'
                    # email_receiver_list.append(name)
        variables = {
            "email_receiver": receiver
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendEmailNotification(AjaxTodoBase):
    """
        发送邮件：附件
    """

    def __init__(self):
        super(SendEmailNotification, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        file_list = process_data.get('file_list')
        email_title = process_data.get('email_title')
        email_content = process_data.get('email_content')
        email_receiver = process_data.get("email_receiver")
        file_data = {}
        email_receiver_list = email_receiver.split(';')
        # 指定保存文件的完整路径
        save_directory = '/path/to/save'
        os.makedirs(save_directory, exist_ok=True)  # 创建保存路径
        try:
            for file in file_list:
                file_name = file.get("name")
                file_url = file["response"]["FileList"][0]["url"]
                relative_url = file_url.split("cosfile")[1]
                new_file_name = str(uuid.uuid4()) + file_name
                save_file_path = os.path.join(save_directory, new_file_name)
                COSLib.download_2(file_name=relative_url, save_to_file=save_file_path)
                file_data[save_file_path] = file_name
        except KeyError:
            return {"code": -1, "msg": f"文件上传错误"}
        except IndexError:
            return {"code": -1, "msg": f"文件上传错误"}
        # 邮件抄送人
        if email_receiver_list:
            for j in range(len(email_receiver_list)):
                email_receiver_list[j] += "@tencent.com"
        tof.send_attachment_email(filePathAndfileName=file_data,
                                  sendTitle=email_title,
                                  msgContent=email_content,
                                  sendTo=email_receiver_list)
        variables = {
            "email_receiver_list": email_receiver_list,
            "file_data": file_data
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
