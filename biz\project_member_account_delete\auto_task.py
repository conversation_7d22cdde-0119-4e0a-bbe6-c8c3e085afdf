from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql
from iBroker.lib import config
import datetime

from biz.project_member_account_delete.account_delete_api import (
    AccountDeleteApi,
)


# 账号删除
class AccountDeleteAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 查询项目所有成员
    def query_project_member(self):
        project_name = self.ctx.variables.get("project_name")
        member_list = AccountDeleteApi.query_project_member_info(project_name)
        variables = {
            "member_list": member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 删除建设平台账号权限
    def delete_account_permission(self, del_member_list):
        """
        删除建设平台账号权限
        """
        tb_db = mysql.new_mysql_instance("tbconstruct")
        for info in del_member_list:
            id = info.get("id")
            update_data = {"del_flag": "1"}
            tb_db.begin()
            tb_db.update_by_id("project_role_account", update_data, id)
            tb_db.commit()
            info["del_account_permission_status"] = "删除成功"
        variables = {
            "del_member_list": del_member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 判断是否需要起删除企微账号流程工单（删除权限后的判断，只需判断账号是否有权限，有：不需要，无：需要。）
    def delete_wework_account_flow_determine(self, project_name, del_member_list):
        """
        判断是否需要起删除企微账号流程工单
        (设计院和厂家不删; 只判断项管、监理、集成商)
        (删除权限后的判断，只需判断账号是否有权限，有：不需要，无：需要。)
        """
        delete_wework_account_flow_flag = 0
        db = mysql.new_mysql_instance("tbconstruct")
        for item in del_member_list:
            if "account" in item:
                account = item.get("account")
                role = item.get("role")
                if "厂家" in role or "设计院" in role:
                    item["is_truly_del_account"] = "否"
                elif "集成商" in role or "项管" in role or "监理" in role or "总包" in role or "合建方" in role:
                    sql = f"SELECT * FROM project_role_account WHERE account='{account}' and del_flag=0"
                    result = db.get_all(sql)
                    if result:
                        item["is_truly_del_account"] = "否"
                    else:
                        delete_wework_account_flow_flag = 1
                        item["is_truly_del_account"] = "是"
                        # 账号删除，需将账号下的待办都转派给上级
                        item["new_todo_user"] = AccountDeleteApi.get_new_todo_user(
                            project_name, role
                        )
                # 更新账号使用情况（可能其它工单完成修改过数据）
                account_use_info = AccountDeleteApi.query_account_use_info(account)
                item["account_use_info"] = account_use_info
        variables = {
            "delete_wework_account_flow_flag": delete_wework_account_flow_flag,
            # 删除权限后,会判断是否需要起单,会更新数据,更新的数据得新存一份数据
            # (避免查看已办历史记录时, 这之前的节点数据受影响, 后面节点参数也需更新)
            "del_member_list_confirm": del_member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 自动起单-删除账号流程（delete_wework_account）
    def delete_wework_account_automatic(self, project_name, del_member_list_confirm):
        """
        自动起单-删除账号流程（delete_wework_account）
        """
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        creator = self.ctx.variables.get("Creator")
        create_input = []
        create_output = []
        # 重复账号可直接复用工单，无需重新起单
        del_account_ticket_dict = {}
        # 起单
        for i in del_member_list_confirm:
            account = i.get("account")
            if i.get("is_truly_del_account") == "是":
                if account not in del_account_ticket_dict:
                    # 输入
                    processTitle = (
                        f"{project_name}项目成员企业微信账号删除: "
                        f"{i.get('name')} {i.get('account')} {i.get('phone')}"
                    )
                    data = {
                        "CustomVariables": {
                            "userid": account,
                            "company": "犀牛鸟有限责任公司",
                        },
                        "ProcessDefinitionKey": "delete_wework_account",
                        "Source": "",
                        "TicketDescription": processTitle,
                        "TicketLevel": "3",
                        "TicketTitle": processTitle,
                        "UserInfo": {
                            "Concern": f"{creator};{flow_responsible_person}",  # 关注人 可填多个
                            "Creator": creator,  # 建单人 只能填一个
                            "Deal": flow_responsible_person,  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
                        },
                    }
                    create_input.append(data)
                    self.workflow_detail.add_kv_table(
                        "1.企业微信账号删除流程起单输入", {"message": create_input}
                    )
                    # 输出
                    res = gnetops.request(action="Ticket", method="Create", data=data)
                    create_output.append(res)
                    self.workflow_detail.add_kv_table(
                        "2.企业微信账号删除流程起单输出", {"message": create_output}
                    )
                    # 起单工单号返回
                    i["ticket_id"] = res.get("TicketId")
                    del_account_ticket_dict[account] = res.get("TicketId")
                else:
                    i["ticket_id"] = del_account_ticket_dict[account]
        self.workflow_detail.add_kv_table(
            "3.删除账号信息", {"message": del_member_list_confirm}
        )
        query_ticket_status_args = {
            "del_member_list_confirm": del_member_list_confirm,
        }
        variables = {
            "create_input": create_input,
            "create_output": create_output,
            "del_member_list_confirm": del_member_list_confirm,
            "query_ticket_status_args": query_ticket_status_args,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 等企业微信账号删除流程所有工单结单(定时任务)
    def query_ticket_status(self, del_member_list_confirm):
        """
        等企业微信账号删除流程所有工单结单(定时任务)
        """
        # 判断是否都结单
        ticket_end_flag = True
        # 重复工单可直接复用结果，无需重新查询
        ticket_status_dict = {}
        for item in del_member_list_confirm:
            ticket_id = item.get("ticket_id")
            if ticket_id:
                if ticket_id not in ticket_status_dict:
                    query_data = {
                        "SchemaId": "ticket_base",
                        "Data": {
                            "ResultColumns": {"TicketStatus": "", "TicketId": ""},
                            "SearchCondition": {"TicketId": [str(ticket_id)]},
                        },
                    }
                    query_result = gnetops.request(
                        action="QueryData", method="Run", ext_data=query_data
                    )
                    result_list = query_result.get("List")
                    for res in result_list:
                        if res.get("TicketId") == str(ticket_id):
                            ticket_status = (
                                "运行中"
                                if res.get("TicketStatus") == "OPEN"
                                else "已结单"
                            )
                            item["ticket_status"] = ticket_status
                            ticket_status_dict[ticket_id] = ticket_status
                            # 可以认为除了OPEN是运行中外，其它的都是已结单
                            if res.get("TicketStatus") == "OPEN":
                                ticket_end_flag = False
                else:
                    item["ticket_status"] = ticket_status_dict[ticket_id]
        date = datetime.datetime.today()
        date = date.strftime("%Y-%m-%d %H:%M:%S")
        self.workflow_detail.add_kv_table(
            f"企业微信账号删除流程建单工单状态({date})",
            {"message": del_member_list_confirm},
        )
        if ticket_end_flag:
            variables = {
                "del_member_list_confirm": del_member_list_confirm,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
        else:
            return {"success": False, "data": "流程未结束"}

    # 获取账号删除结果
    def query_account_status(self, del_member_list_confirm):
        """
        获取账号删除结果
        """
        # 重复账号可直接复用结果，无需重新查询
        del_account_status_dict = {}
        for item in del_member_list_confirm:
            if item.get("ticket_id") and item.get("ticket_status") == "已结单":
                userid = item.get("account")
                if userid not in del_account_status_dict:
                    data = {
                        "userid": userid,
                        "company": "犀牛鸟有限责任公司",
                    }
                    result = gnetops.request(
                        action="WeWork", method="QueryWeWorkUser", data=data
                    )
                    if (
                        result["Result"]["errcode"] == 0
                        and result["Result"]["userid"] == userid
                    ):
                        item["del_account_status"] = "删除失败"
                        del_account_status_dict[userid] = "删除失败"
                    else:
                        item["del_account_status"] = "删除成功"
                        del_account_status_dict[userid] = "删除成功"
                else:
                    item["del_account_status"] = del_account_status_dict[userid]
        variables = {
            "del_member_list_confirm": del_member_list_confirm,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def to_tansfer_todo(self, del_member_list_confirm):
        """
        转派待办
        """
        data = {
            "Datalist": [
                # {
                #     "RawProcessUser": "[string]",  # 原处理人
                #     "TransProcessUser": "[string]",  # 转移处理人
                # },
            ]
        }
        datalist = []
        for item in del_member_list_confirm:
            if (
                item.get("is_truly_del_account")
                and item.get("del_account_status") == "删除成功"
            ):
                if item.get("account") and item.get("new_todo_user"):
                    datalist.append(
                        {
                            "RawProcessUser": item.get("account"),  # 原处理人
                            "TransProcessUser": item.get("new_todo_user"),  # 转移处理人
                        }
                    )
        if datalist:
            # 去重
            unique_users = set()
            temp = []
            for item in datalist:
                raw_process_user = item["RawProcessUser"]
                if raw_process_user not in unique_users:
                    unique_users.add(raw_process_user)
                    temp.append(item)
            data["Datalist"] = temp
            self.workflow_detail.add_kv_table("1.入参", {"message": data})
            # 调接口转派待办
            res = gnetops.request(
                action="TaskTodo", method="BatchUpdateProcessUseV1", data=data
            )
            self.workflow_detail.add_kv_table("2.出参", {"message": res})
        else:
            self.workflow_detail.add_kv_table(
                "1.待办转派信息为空", {"message": del_member_list_confirm}
            )
        flow.complete_task(self.ctx.task_id)

    # def get_new_todo_user(self, project_name, role):
    #     """
    #     获取转单信息（转给上级）
    #     往上：
    #         集成商-项目经理
    #         监理-总监理工程师
    #         项管-项目经理
    #     再往上：
    #         项目PM
    #     再往上：
    #         youngshi;yanjunchen;
    #     """
    #     # 查项目PM
    #     PM = AccountDeleteApi.get_PM(project_name)
    #     new_todo_user = PM
    #     # 查集成商-项目经理、监理-总监理工程师、项管-项目经理
    #     # role_leader = self.get_role_leader("项管-项目经理")
    #     # role_leader = self.get_role_leader("集成商-项目经理")
    #     # role_leader = self.get_role_leader("监理-总监理工程师")
    #     if "项管" in role:
    #         if role != "项管-项目经理":
    #             # 转给项管-项目经理
    #             new_todo_user = AccountDeleteApi.get_role_leader(project_name, "项管-项目经理")
    #     elif "集成商" in role:
    #         if role != "集成商-项目经理":
    #             # 转给集成商-项目经理
    #             new_todo_user = AccountDeleteApi.get_role_leader(project_name, "集成商-项目经理")
    #     elif "监理" in role:
    #         if role != "监理-总监理工程师":
    #             # 转给监理-总监理工程师
    #             new_todo_user = AccountDeleteApi.get_role_leader(project_name, "监理-总监理工程师")
    #     elif "合建方" in role:
    #         if role != "合建方-项目经理":
    #             # 转给合建方-项目经理
    #             new_todo_user = AccountDeleteApi.get_role_leader(project_name, "合建方-项目经理")
    #     elif "总包" in role:
    #         if role != "总包-项目经理":
    #             # 转给总包-项目经理
    #             new_todo_user = AccountDeleteApi.get_role_leader(project_name, "总包-项目经理")
    #     return new_todo_user

    # # 查询项目PM
    # def get_PM(self, project_name):
    #     PM = "youngshi"
    #     query_sql = f"SELECT PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
    #     tb_db = mysql.new_mysql_instance("tbconstruct")
    #     query_result = tb_db.query(query_sql)
    #     if query_result:
    #         PM = query_result[0].get("PM", "youngshi")
    #     return PM

    # # 查询项目role leader
    # def get_role_leader(self, project_name, role):
    #     # 查项目PM
    #     PM = self.get_PM(project_name)
    #     leader = PM
    #     query_sql = (
    #         f"select account from project_role_account "
    #         f"where project_name = '{project_name}' and role = '{role}' and del_flag=0"
    #     )
    #     tb_db = mysql.new_mysql_instance("tbconstruct")
    #     query_result = tb_db.query(query_sql)
    #     if query_result:
    #         leader = query_result[0].get("account", PM)
    #     return leader
