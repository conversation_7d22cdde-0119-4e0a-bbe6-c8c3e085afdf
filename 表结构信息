需求：

查询接口问题分析：
数据展示问题：之前因设备类型固定写死在代码里，更改时将名字合并，导致功能展示异常，数据查不出。
测试环境问题：测试环境信息不全，若查看正式数据需通过 ISOPS 点 WOA 域名查正式环境。
查询接口优化方案：
查询表调整：不再通过查两张表关联，使用平台所有功能的表进行查询。
查询条件确定：主要使用项目名称进行查询，状态等信息暂未使用。
数据筛选：区分甲供和乙供设备，表中有相关配置表明设备是甲供还是乙供。
SQL 查询优化：通过 project name 和 device time 等做 join 查询，避免多次查数据库。
开发注意事项：
强制结单处理：强制结单的工单在字段中有标识，查询时需排除。
表结构重构：考虑将相关表字段重构，放到一张表中。
开发进度要求：尽量在周四赶上发布，明天基本完成开发并测试。



 the extracted information from the provided images, translated into Chinese:

**图片 1: `delivery_schedule_management_table` (交付计划管理表)**

* **表名:** delivery_schedule_management_table
* **所在库:** idc_tb_dt_database
* **存储引擎:** InnoDB
* **字符集:** utf8
* **备注:** 交付管理表
* **列信息:**
    * **id:** int, 长度: 9, 默认值: (空), 备注: id, 主键: 是, 可空: (空), 自增: 是, 操作: Delete
    * **project_name:** varchar, 长度: 255, 默认值: (空), 备注: 项目名称, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **supplier:** varchar, 长度: 255, 默认值: (空), 备注: 供应商, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **project_required_delivery_time:** varchar, 长度: 255, 默认值: (空), 备注: 项目要求交付时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **device_type:** varchar, 长度: 255, 默认值: (空), 备注: 设备类型, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **equipment_sla:** varchar, 长度: 255, 默认值: (空), 备注: 设备SLA, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **expected_time_equipment:** varchar, 长度: 255, 默认值: (空), 备注: 设备预期时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **estimated_time_delivery:** varchar, 长度: 255, 默认值: (空), 备注: 预计交付时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **delivery_gap:** varchar, 长度: 255, 默认值: (空), 备注: 交付GAP, 主键: (空), 可空: 是, 自增: (空), 操作: Delete

**图片 2: `all_construction_ticket_data` (所有施工工单数据)**

* **表名:** all_construction_ticket_data
* **选择库:** idc_tb_dt_database
* **存储引擎:** InnoDB
* **字符集:** utf8
* **备注:** 全量拉取平台工单数据 (从平台数据库中拉取的数据)
* **列信息:**
    * **id:** int, 长度: 9, 默认值: (空), 备注: (空), 主键: 是, 可空: (空), 自增: (空), 操作: Delete
    * **TicketId:** varchar, 长度: 255, 默认值: (空), 备注: 工单号, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **InstanceId:** varchar, 长度: 255, 默认值: (空), 备注: 流程ID, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **ProcessDefinitionKey:** varchar, 长度: 255, 默认值: (空), 备注: 流程标识, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **SchemeNameCn:** varchar, 长度: 255, 默认值: (空), 备注: 工单名称, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **Description:** varchar, 长度: 255, 默认值: (空), 备注: 工单描述, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **Title:** varchar, 长度: 255, 默认值: (空), 备注: 工单标题, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **TicketStatus:** varchar, 长度: 9, 默认值: (空), 备注: 工单状态, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **CurrentTasks:** longtext, 长度: 0, 默认值: (空), 备注: 当前任务, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **CurrentTaskDetails:** longtext, 长度: 0, 默认值: (空), 备注: 当前任务详情, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **CurrentAllProcessUsers:** varchar, 长度: 255, 默认值: (空), 备注: 当前处理人, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **CreateTime:** varchar, 长度: 255, 默认值: (空), 备注: 创建时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **EndTime:** varchar, 长度: 255, 默认值: (空), 备注: 结束时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **CustomRequestFileKV:** longtext, 长度: 0, 默认值: (空), 备注: 流程请求自定义配置, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **IsForceEnd:** varchar, 长度: 255, 默认值: (空), 备注: 工单是否强制结束, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **ForceEndReason:** varchar, 长度: 255, 默认值: (空), 备注: 强制结束原因, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **ForceEndOperator:** varchar, 长度: 255, 默认值: (空), 备注: 强制结束人, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **Manager:** varchar, 长度: 255, 默认值: (空), 备注: 报障管理员, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **UserInfo:** longtext, 长度: 0, 默认值: (空), 备注: 相关人, 主键: (空), 可空: 是, 自增: (空), 操作: Delete

**图片 3: `equipment_production_racking_process` (设备生产上架流程)**

* **表名:** equipment_production_racking_process
* **所在库:** idc_tb_dt_database
* **存储引擎:** InnoDB
* **字符集:** utf8
* **备注:** 备注说明: 可不填
* **列信息:**
    * **id:** int, 长度: 9, 默认值: (空), 备注: (空), 主键: 是, 可空: (空), 自增: (空), 操作: Delete
    * **ticket_id:** varchar, 长度: 225, 默认值: (空), 备注: 工单号, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **project_name:** varchar, 长度: 225, 默认值: (空), 备注: 项目名称, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **campus:** varchar, 长度: 225, 默认值: (空), 备注: 园区, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **equipment_type:** varchar, 长度: 225, 默认值: (空), 备注: 设备类型, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **device_name:** varchar, 长度: 225, 默认值: (空), 备注: 设备名称, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **supplier:** varchar, 长度: 225, 默认值: (空), 备注: 供应商, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **state:** int, 长度: 9, 默认值: 0, 备注: 状态, 主键: (空), 可空: (空), 自增: (空), 操作: Delete
    * **pm_confirm:** varchar, 长度: 225, 默认值: (空), 备注: pm确认, 主键: (空), 可空: 是, 自增: (空), 操作: Delete

**图片 4: `equipment_arrival_data` (设备到货数据)**

* **表名:** equipment_arrival_data
* **所在库:** idc_tb_dt_database
* **存储引擎:** InnoDB
* **字符集:** utf8
* **备注:** 设备到货数据表
* **列信息:**
    * **id:** int, 长度: 9, 默认值: (空), 备注: id, 主键: 是, 可空: (空), 自增: (空), 操作: Delete
    * **campus:** varchar, 长度: 255, 默认值: (空), 备注: 园区, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **project_name:** varchar, 长度: 255, 默认值: (空), 备注: 项目名称, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **equipment_category:** varchar, 长度: 255, 默认值: (空), 备注: 设备品类, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **supplier:** varchar, 长度: 255, 默认值: (空), 备注: 供应商, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **planned_arrival_time:** datetime, 长度: 0, 默认值: (空), 备注: 计划到货时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **actual_arrival_time:** datetime, 长度: 0, 默认值: (空), 备注: 实际到货时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **upload_shipping_information:** longtext, 长度: 0, 默认值: (空), 备注: 上传发货信息, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **upload_receiving_information:** longtext, 长度: 0, 默认值: (空), 备注: 上传收货信息, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **ticket_id:** varchar, 长度: 255, 默认值: (空), 备注: 工单id, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **scheduled_delivery_time:** datetime, 长度: 0, 默认值: (空), 备注: 计划发货时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **actual_delivery_time:** datetime, 长度: 0, 默认值: (空), 备注: 实际发货时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **equipment_batch:** varchar, 长度: 225, 默认值: (空), 备注: 批次, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **fh_remark:** varchar, 长度: 225, 默认值: (空), 备注: 发货备注, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **slh_remark:** varchar, 长度: 225, 默认值: (空), 备注: 收货备注, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **receipt_time:** varchar, 长度: 255, 默认值: (空), 备注: 审核实际收货时间, 主键: (空), 可空: 是, 自增: (空), 操作: Delete
    * **receipt_information:** longtext, 长度: 0, 默认值: (空), 备注: 审核到货收货资料, 主键: (空), 可空: 是, 自增: (空), 操作: Delete