#!/usr/bin/env python3
# -*- coding: utf-8 -*-


import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iBroker.lib import mysql
import logging
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CorrectedEquipmentDataConsolidator:
    def __init__(self):
        self.db = mysql.new_mysql_instance("tbconstruct")
        self.start_time = time.time()

    def log_update_status(self, table_name, record_count, status='success', message=''):
        """记录更新状态，用于页面显示"""
        try:
            self.db.execute("""
                INSERT INTO consolidated_data_update_log (table_name, record_count, status, message) 
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                    update_time = CURRENT_TIMESTAMP,
                    record_count = VALUES(record_count),
                    status = VALUES(status),
                    message = VALUES(message)
            """, (table_name, record_count, status, message))
        except Exception as e:
            logger.warning(f"记录更新状态失败: {e}")

    def create_tables(self):
        """创建必要的表（包含所有字段）"""
        logger.info("🏗️ 创建数据表...")

        # 创建合并表
        create_main_table = """
        CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
            state VARCHAR(50) DEFAULT NULL,
            TicketId VARCHAR(100) NOT NULL,
            Title VARCHAR(255) DEFAULT NULL,
            SchemeNameCn VARCHAR(255) DEFAULT NULL,
            IsForceEnd VARCHAR(255) DEFAULT '0',
            project_name VARCHAR(255) DEFAULT NULL,
            device_name VARCHAR(255) DEFAULT NULL,
            supplier VARCHAR(255) DEFAULT NULL,
            device_type VARCHAR(100) DEFAULT NULL,
            equipment_sla VARCHAR(50) DEFAULT NULL,
            expected_time_equipment DATETIME DEFAULT NULL,
            estimated_time_delivery DATETIME DEFAULT NULL,
            delivery_gap INT DEFAULT NULL,
            completion_time DATETIME DEFAULT NULL,
            po_create_time DATETIME DEFAULT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (TicketId),
            INDEX idx_project_name (project_name),
            INDEX idx_state (state),
            INDEX idx_device_type (device_type),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """

        # 创建更新日志表
        create_log_table = """
        CREATE TABLE IF NOT EXISTS consolidated_data_update_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(100) NOT NULL,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            record_count INT DEFAULT 0,
            status VARCHAR(50) DEFAULT 'success',
            message TEXT,
            INDEX idx_table_name (table_name),
            INDEX idx_update_time (update_time)
        )
        """

        try:
            self.db.execute(create_main_table)
            self.db.execute(create_log_table)
            logger.info("✅ 数据表创建成功（已包含所有必要字段）")
            return True
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False

    def fetch_base_tickets(self):
        """获取基础工单数据"""
        logger.info("📋 第一步：获取基础工单数据...")
        query = """
        SELECT DISTINCT
            actd.TicketId,
            actd.Title,
            actd.SchemeNameCn,
            actd.IsForceEnd
        FROM all_construction_ticket_data actd
        WHERE actd.SchemeNameCn LIKE '%设备生产%'
            AND actd.IsForceEnd = 0
        ORDER BY actd.TicketId
        """
        try:
            results = self.db.get_all(query)
            logger.info(f"✅ 获取基础工单: {len(results)} 条")
            return {item['TicketId']: item for item in results}
        except Exception as e:
            logger.error(f"❌ 获取基础工单失败: {e}")
            return {}

    def fetch_production_data(self):
        """获取生产racking数据（eprp表）"""
        logger.info("🏭 第二步：获取生产racking数据...")
        query = """
        SELECT 
            eprp.ticket_id,
            eprp.project_name,
            eprp.device_name,
            eprp.equipment_type  -- 设备类型，用于device_type字段
        FROM equipment_production_racking_process eprp
        WHERE eprp.equipment_type = '甲供'
        """
        try:
            results = self.db.get_all(query)
            production_map = {}
            for item in results:
                ticket_id = item['ticket_id']
                if ticket_id not in production_map:
                    production_map[ticket_id] = []
                production_map[ticket_id].append(item)
            logger.info(f"✅ 获取生产数据: {len(production_map)} 个工单")
            return production_map
        except Exception as e:
            logger.error(f"❌ 获取生产数据失败: {e}")
            return {}

    def fetch_delivery_data(self):
        """获取交付计划数据（dsm表）- 按project+device_type建立映射（匹配eprp的device_name）"""
        logger.info("🚚 第三步：获取交付计划数据...")
        query = """
        SELECT 
            dsm.project_name,
            dsm.device_type,  -- 用于匹配eprp的device_name
            dsm.supplier,
            dsm.expected_time_equipment,
            dsm.estimated_time_delivery,
            dsm.delivery_gap
        FROM delivery_schedule_management_table dsm
        WHERE dsm.project_name IN (SELECT DISTINCT project_name FROM equipment_production_racking_process)
          AND dsm.device_type IN (SELECT DISTINCT device_name FROM equipment_production_racking_process)
        """
        try:
            results = self.db.get_all(query)
            delivery_map = {}
            for item in results:
                # 构建唯一键：project_name + device_type（基于dsm表实际存在的字段）
                key = f"{item['project_name']}_{item['device_type']}"
                delivery_map[key] = item
            logger.info(f"✅ 获取交付数据: {len(delivery_map)} 条记录")
            return delivery_map
        except Exception as e:
            logger.error(f"❌ 获取交付数据失败: {e}")
            return {}

    def fetch_sla_data(self):
        """获取设备SLA数据（ecmr表）"""
        logger.info("📊 第四步：获取设备SLA数据...")
        query = """
        SELECT 
            ecmr.device_name,
            ecmr.equipment_sla
        FROM equipment_category_mapping_relationship ecmr
        """
        try:
            results = self.db.get_all(query)
            # 按设备名称建立映射
            sla_map = {item['device_name']: item['equipment_sla'] for item in results}
            logger.info(f"✅ 获取SLA数据: {len(sla_map)} 条记录")
            return sla_map
        except Exception as e:
            logger.error(f"❌ 获取SLA数据失败: {e}")
            return {}

    def fetch_completion_data(self):
        """获取施工完成时间（cpd表）- 处理特殊映射"""
        logger.info("⏱️ 第五步：获取施工完成时间...")
        query = """
        SELECT 
            cpd.work_content,
            cpd.completion_time,
            cpd.project_name  -- 补充项目名称用于精准匹配
        FROM construct_plan_data cpd
        """
        try:
            results = self.db.get_all(query)
            completion_map = {}
            for item in results:
                # 构建唯一键：project_name + work_content（设备名称）
                key = f"{item['project_name']}_{item['work_content']}"
                completion_map[key] = item['completion_time']

                # 处理特殊映射：弹性一体柜(HVDC)对应HVDC的完成时间
                if item['work_content'] == 'HVDC':
                    special_key = f"{item['project_name']}_弹性一体柜(HVDC)"
                    completion_map[special_key] = item['completion_time']

            logger.info(f"✅ 获取完成时间数据: {len(completion_map)} 条记录")
            return completion_map
        except Exception as e:
            logger.error(f"❌ 获取完成时间数据失败: {e}")
            return {}

    def fetch_po_time_data(self):
        """获取PO创建时间（pi表）- 按project+material_name建立映射（匹配eprp的device_name）"""
        logger.info("💸 第六步：获取PO时间数据...")
        query = """
        SELECT 
            pi.project_name,
            pi.material_name,  -- 用于匹配eprp的device_name（替换原device_name）
            pi.po_create_time
        FROM payment_info pi
        -- 新增过滤：只保留与eprp表匹配的project和material_name
        WHERE pi.project_name IN (SELECT DISTINCT project_name FROM equipment_production_racking_process)
          AND pi.material_name IN (SELECT DISTINCT device_name FROM equipment_production_racking_process)
        """
        try:
            results = self.db.get_all(query)
            # 按project_name + material_name建立映射（匹配eprp的project_name + device_name）
            po_time_map = {}
            for item in results:
                key = f"{item['project_name']}_{item['material_name']}"
                po_time_map[key] = item['po_create_time']
            logger.info(f"✅ 获取PO时间数据: {len(po_time_map)} 条记录")
            return po_time_map
        except Exception as e:
            logger.error(f"❌ 获取PO时间数据失败: {e}")
            return {}

    def fetch_state_data(self):
        """获取区域数据（summary_data表）"""
        logger.info("🌍 第七步：获取区域数据...")
        query = """
        SELECT 
            sd.campus,
            sd.project_name,
            sd.state,
            CONCAT(sd.campus, sd.project_name) AS full_project_name
        FROM summary_data sd
        """
        try:
            results = self.db.get_all(query)
            state_map = {item['full_project_name']: item['state'] for item in results}
            logger.info(f"✅ 获取区域数据: {len(state_map)} 条记录")
            return state_map
        except Exception as e:
            logger.error(f"❌ 获取区域数据失败: {e}")
            return {}

    def consolidate_and_insert(self):
        """主要的数据合并流程（修正所有字段映射）"""
        logger.info("🚀 开始数据合并流程...")

        # 1. 清空目标表
        try:
            self.db.execute("DELETE FROM consolidated_equipment_data")
            logger.info("🗑️ 清空目标表成功")
        except Exception as e:
            logger.error(f"清空目标表失败: {e}")
            return False

        # 2. 分步获取各表数据
        base_tickets = self.fetch_base_tickets()
        if not base_tickets:
            logger.error("❌ 无基础工单数据，终止流程")
            return False

        production_data = self.fetch_production_data()
        delivery_data = self.fetch_delivery_data()
        sla_data = self.fetch_sla_data()
        completion_data = self.fetch_completion_data()
        po_time_data = self.fetch_po_time_data()
        state_data = self.fetch_state_data()

        # 3. 数据组装（修正所有字段映射）
        logger.info("🔧 开始数据组装...")
        combined_data = []

        for ticket_id, base_info in base_tickets.items():
            if ticket_id not in production_data:
                continue

            # 处理同一工单的多个设备
            for prod_item in production_data[ticket_id]:
                project_name = prod_item['project_name']
                device_name = prod_item['device_name']

                # 构建各表数据的查询键（统一使用project+device）
                delivery_key = f"{project_name}_{device_name}"
                completion_key = f"{project_name}_{device_name}"
                po_time_key = f"{project_name}_{device_name}"
                full_project_name = f"{project_name}"  # 用于匹配区域数据

                # 从各表获取对应数据
                delivery_info = delivery_data.get(delivery_key, {})
                completion_time = completion_data.get(completion_key)
                po_create_time = po_time_data.get(po_time_key)
                state = state_data.get(full_project_name)

                # 组装完整记录（修正所有字段来源）
                combined_record = {
                    # 基础字段
                    'state': state,
                    'TicketId': ticket_id,
                    'Title': base_info['Title'],
                    'SchemeNameCn': base_info['SchemeNameCn'],
                    'IsForceEnd': base_info['IsForceEnd'],
                    'project_name': project_name,
                    'device_name': device_name,

                    # 修正的字段映射
                    'supplier': delivery_info.get('supplier', '待确认'),
                    'device_type': prod_item['equipment_type'],  # 来自eprp的equipment_type
                    'equipment_sla': sla_data.get(device_name),  # 来自ecmr表
                    'expected_time_equipment': delivery_info.get('expected_time_equipment'),  # 来自dsm表
                    'estimated_time_delivery': delivery_info.get('estimated_time_delivery'),  # 来自dsm表
                    'delivery_gap': delivery_info.get('delivery_gap'),  # 来自dsm表
                    'completion_time': completion_time,  # 来自cpd表（精准匹配）
                    'po_create_time': po_create_time  # 来自pi表（精准匹配）
                }
                combined_data.append(combined_record)

        if not combined_data:
            logger.error("❌ 数据组装后无有效记录")
            return False

        # 4. 插入数据（确保字段顺序完全匹配）
        logger.info(f"📥 开始插入数据，共 {len(combined_data)} 条...")
        insert_count = 0

        for record in combined_data:
            try:
                insert_sql = """
                INSERT IGNORE INTO consolidated_equipment_data (
                    state, TicketId, Title, SchemeNameCn, IsForceEnd,
                    project_name, device_name, supplier, device_type,
                    equipment_sla, expected_time_equipment, estimated_time_delivery,
                    delivery_gap, completion_time, po_create_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                # 严格按SQL字段顺序传入参数，确保一一对应
                self.db.execute(insert_sql, (
                    record['state'],
                    record['TicketId'],
                    record['Title'],
                    record['SchemeNameCn'],
                    record['IsForceEnd'],
                    record['project_name'],
                    record['device_name'],
                    record['supplier'],
                    record['device_type'],
                    record['equipment_sla'],
                    record['expected_time_equipment'],
                    record['estimated_time_delivery'],
                    record['delivery_gap'],
                    record['completion_time'],
                    record['po_create_time']
                ))
                insert_count += 1

            except Exception as e:
                logger.warning(f"插入记录失败（TicketId: {record['TicketId']}）: {e}")
                continue

        # 5. 记录更新状态
        elapsed_time = time.time() - self.start_time
        message = f"数据合并完成，耗时 {elapsed_time:.2f} 秒"
        self.log_update_status('consolidated_equipment_data', insert_count, 'success', message)

        logger.info(f"✅ 数据合并完成！插入 {insert_count} 条记录，耗时 {elapsed_time:.2f} 秒")
        return True


def main():
    """主函数"""
    logger.info("🚀 启动修正版设备数据合并程序")

    consolidator = CorrectedEquipmentDataConsolidator()

    # 创建表
    if not consolidator.create_tables():
        return False

    # 执行数据合并
    if not consolidator.consolidate_and_insert():
        return False

    logger.info("🎉 修正版设备数据合并程序执行完成！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
