import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


class DeepeningDesignDcops(object):
    """
        深化设计
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(DeepeningDesignDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def in_depth_design_data_review(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            深化设计资料审核反馈
        """
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        in_depth_design_data_review_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "in_depth_design_data_review",  # (*必填) 云函数名称
                    "data": in_depth_design_data_review_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)

        variables = {
            'in_depth_design_data_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceShsjDcops(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceShsjDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_deepen_design_data_upload(self):
        """
            等待深化设计资料上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name, file_type, url, upload_time, is_need, file_remark " \
              "FROM maintenance_upload_data " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND node = '架构' "
        result_list = db.get_all(sql)
        url_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            file_name = record.get('file_name', "")  # 文件名称
            file_type = record.get('file_type', "")  # 文件类型
            file_url = record.get('url', "[]")  # 文件
            is_need = record.get('is_need', "")  # 文件上传人
            file_remark = record.get('file_remark', "")  # 文件上传人
            upload_time = record.get('upload_time', "")  # 文件上传时间

            file_url = file_url.replace("'", '"')
            file_url = json.loads(file_url)
            # 获取 plan_url 字符串
            if file_url is not None:
                file_url_table = []
                for doc in file_url:
                    if doc is not None and "response" in doc and "FileList" in doc["response"]:
                        file_list = doc["response"]["FileList"]
                        if len(file_list) > 0 and "url" in file_list[0]:
                            url = file_list[0]["url"]
                            name = file_list[0]['name']
                            file_url_table.append({
                                "file_url": url,
                                'file_url_name': name
                            })
            url_file_list.append({
                'file_name': file_name,
                'file_type': file_type,
                'url': file_url_table,
                'is_need': is_need,
                'file_remark': file_remark,
                'upload_time': upload_time
            })

        self.workflow_detail.add_kv_table('深化设计资料上传信息', {'message': result_list})
        if result_list:
            variables = {
                "final_file_list": url_file_list,
                "result_list": result_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
