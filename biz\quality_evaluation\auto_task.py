from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import (
    WorkflowDetailService,
)
from iBroker.lib import config

from biz.quality_evaluation.project_info import ProjectInfoQuery
from biz.quality_evaluation.standard import StandardQuery
from biz.quality_evaluation.category import CategoryQuery
from biz.quality_evaluation.rating_handle import TeamRatingHandle, ProjectRatingHandle


# 质量评估自动任务类
class QualityEvaluationAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_project_info_dict(self):
        # 数据库查询
        project_list = config.get_config_map("quality_evaluation_project_list")
        project_info_dict = ProjectInfoQuery.get_project_info_dict()
        # # 七彩石获取 目前人工维护
        # project_info_dict = ProjectInfoQuery.get_project_info_dict_temp()
        variables = {
            "project_list": project_list,
            "project_info_dict": project_info_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_project_category_basic_info(self, project_info_list):
        """
        获取项目品类基本信息
        根据实际项目品类供应商数量动态增加品类，同时生成对应字段key
        """
        # 获取项目
        project_category_basic_info_dict = {}
        for peoject in project_info_list:
            project_name = peoject["project_name"]
            project_category_basic_info = CategoryQuery.category_handle(project_name)
            project_category_basic_info_dict[project_name] = project_category_basic_info
        variables = {
            "project_category_basic_info_dict": project_category_basic_info_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_standard_class_list(self):
        """
        查询所有团队质量评价标准
        """
        business_standard_class_list = StandardQuery.get_standard_class_list("商务", 2)
        supply_chain_standard_class_list = StandardQuery.get_standard_class_list(
            "供应链", 2
        )
        planning_standard_class_list = StandardQuery.get_standard_class_list("规划", 2)
        product_standard_class_list = StandardQuery.get_standard_class_list("产品", 2)
        project_standard_class_list = StandardQuery.get_standard_class_list("项目", 2)
        optimize_standard_class_list = StandardQuery.get_standard_class_list("优化", 2)
        weak_electricity_standard_class_list = StandardQuery.get_standard_class_list(
            "弱电", 2
        )
        variables = {
            "business_standard_class_list": business_standard_class_list,
            "supply_chain_standard_class_list": supply_chain_standard_class_list,
            "planning_standard_class_list": planning_standard_class_list,
            "product_standard_class_list": product_standard_class_list,
            "project_standard_class_list": project_standard_class_list,
            "optimize_standard_class_list": optimize_standard_class_list,
            "weak_electricity_standard_class_list": weak_electricity_standard_class_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_team_rating_list_template(
        self, project_info_list, project_category_basic_info_dict
    ):
        """
        组装团队评分导入模板
        """
         # 获取团队对应标准分类的关键指标
        business_key_indicator = StandardQuery.get_key_indicator("商务")
        business_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "商务",
            project_info_list,
            business_key_indicator,
            project_category_basic_info_dict,
        )
        supply_chain_key_indicator = StandardQuery.get_key_indicator("供应链")
        supply_chain_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "供应链",
            project_info_list,
            supply_chain_key_indicator,
            project_category_basic_info_dict,
        )
        optimize_key_indicator = StandardQuery.get_key_indicator("优化")
        optimize_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "优化",
            project_info_list,
            optimize_key_indicator,
            project_category_basic_info_dict,
        )
        planning_indicator = StandardQuery.get_key_indicator("规划")
        planning_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "规划",
            project_info_list,
            planning_indicator,
            project_category_basic_info_dict,
        )
        product_key_indicator = StandardQuery.get_key_indicator("产品")
        product_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "产品",
            project_info_list,
            product_key_indicator,
            project_category_basic_info_dict,
        )
        project_indicator = StandardQuery.get_key_indicator("项目")
        project_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "项目",
            project_info_list,
            project_indicator,
            project_category_basic_info_dict,
        )
        weak_electricity_key_indicator = StandardQuery.get_key_indicator("弱电")
        weak_electricity_rating_list_template = TeamRatingHandle.get_team_rating_list_template(
            "弱电",
            project_info_list,
            weak_electricity_key_indicator,
            project_category_basic_info_dict,
        )
        variables = {
            "business_rating_list_template": business_rating_list_template,
            "supply_chain_rating_list_template": supply_chain_rating_list_template,
            "optimize_rating_list_template": optimize_rating_list_template,
            "planning_rating_list_template": planning_rating_list_template,
            "product_rating_list_template": product_rating_list_template,
            "project_rating_list_template": project_rating_list_template,
            "weak_electricity_rating_list_template": weak_electricity_rating_list_template
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_team_rating_list(
        self, project_info_list, project_category_basic_info_dict
    ):
        """
        组装团队需要评分的数据
        """
        # 获取团队对应标准分类的关键指标
        business_key_indicator = StandardQuery.get_key_indicator("商务")
        supply_chain_key_indicator = StandardQuery.get_key_indicator("供应链")
        planning_indicator = StandardQuery.get_key_indicator("规划")
        product_key_indicator = StandardQuery.get_key_indicator("产品")
        project_indicator = StandardQuery.get_key_indicator("项目")
        optimize_key_indicator = StandardQuery.get_key_indicator("优化")
        weak_electricity_key_indicator = StandardQuery.get_key_indicator("弱电")
        business_rating_list = TeamRatingHandle.get_team_rating_list(
            "商务",
            project_info_list,
            business_key_indicator,
            project_category_basic_info_dict,
        )
        supply_chain_rating_list = TeamRatingHandle.get_team_rating_list(
            "供应链",
            project_info_list,
            supply_chain_key_indicator,
            project_category_basic_info_dict,
        )
        planning_rating_list = TeamRatingHandle.get_team_rating_list(
            "规划",
            project_info_list,
            planning_indicator,
            project_category_basic_info_dict,
        )
        product_rating_list = TeamRatingHandle.get_team_rating_list(
            "产品",
            project_info_list,
            product_key_indicator,
            project_category_basic_info_dict,
        )
        project_rating_list = TeamRatingHandle.get_team_rating_list(
            "项目",
            project_info_list,
            project_indicator,
            project_category_basic_info_dict,
        )
        optimize_rating_list = TeamRatingHandle.get_team_rating_list(
            "优化",
            project_info_list,
            optimize_key_indicator,
            project_category_basic_info_dict,
        )
        weak_electricity_rating_list = TeamRatingHandle.get_team_rating_list(
            "弱电",
            project_info_list,
            weak_electricity_key_indicator,
            project_category_basic_info_dict,
        )
        variables = {
            "business_rating_list": business_rating_list,
            "supply_chain_rating_list": supply_chain_rating_list,
            "planning_rating_list": planning_rating_list,
            "product_rating_list": product_rating_list,
            "project_rating_list": project_rating_list,
            "optimize_rating_list": optimize_rating_list,
            "weak_electricity_rating_list": weak_electricity_rating_list,
            # 是否需要重新计算
            "is_recalculate": 0,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_project_supplier_and_team_standard(
        self,
        project_info_list,
        project_category_basic_info_dict,
        business_standard_class_list,
        supply_chain_standard_class_list,
        planning_standard_class_list,
        product_standard_class_list,
        project_standard_class_list,
        optimize_standard_class_list,
        weak_electricity_standard_class_list,
    ):
        """
        组装内控需要的数据
        项目品类供应商范围、各团队质量评价标准
        """
        # 项目品类供应商范围
        project_supplier_range_list = []
        for peoject in project_info_list:
            project_name = peoject["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            project_supplier_range_list.append(
                {
                    "project_name": project_name,
                    "supplier_list": [
                        {
                            "row_header": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
            )
        # 各团队质量评价标准
        business_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            business_standard_class_list
        )
        supply_chain_standard_list = StandardQuery.get_team_eq_standard_list(
            supply_chain_standard_class_list
        )
        planning_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            planning_standard_class_list
        )
        product_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            product_standard_class_list
        )
        project_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            project_standard_class_list
        )
        optimize_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            optimize_standard_class_list
        )
        weak_electricity_eq_standard_list = StandardQuery.get_team_eq_standard_list(
            weak_electricity_standard_class_list
        )
        all_team_eq_standard_list = [
            *business_eq_standard_list,
            *supply_chain_standard_list,
            *planning_eq_standard_list,
            *product_eq_standard_list,
            *project_eq_standard_list,
            *optimize_eq_standard_list,
            *weak_electricity_eq_standard_list,
        ]
        variables = {
            "project_supplier_range_list": project_supplier_range_list,
            "all_team_eq_standard_list": all_team_eq_standard_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def merge_all_team_rating_standard_list(self):
        """
        合并所有团队评分详情数据（评分汇总展示）
        """
        # 获取团队评分数据
        business_rating_standard_list = self.ctx.variables.get(
            "business_rating_standard_list"
        )
        supply_chain_rating_standard_list = self.ctx.variables.get(
            "supply_chain_rating_standard_list"
        )
        planning_rating_standard_list = self.ctx.variables.get(
            "planning_rating_standard_list"
        )
        product_rating_standard_list = self.ctx.variables.get(
            "product_rating_standard_list"
        )
        project_rating_standard_list = self.ctx.variables.get(
            "project_rating_standard_list"
        )
        optimize_rating_standard_list = self.ctx.variables.get(
            "optimize_rating_standard_list"
        )
        weak_electricity_rating_standard_list = self.ctx.variables.get(
            "weak_electricity_rating_standard_list"
        )
        all_team_rating_standard_list = (
            TeamRatingHandle.get_all_team_rating_standard_list(
                business_rating_standard_list,
                supply_chain_rating_standard_list,
                planning_rating_standard_list,
                product_rating_standard_list,
                project_rating_standard_list,
                optimize_rating_standard_list,
                weak_electricity_rating_standard_list,
            )
        )
        variables = {
            "all_team_rating_standard_list": all_team_rating_standard_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def handle_all_team_rating_standard_list(
        self, all_team_rating_standard_list, is_recalculate=0
    ):
        """
        处理团队评分的数据(评分汇总)
        打平团队评分详情数据+计算项目品类分数
        """
        if all_team_rating_standard_list is None:
            all_team_rating_standard_list = []
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 取出特殊处理数据
        special_data = TeamRatingHandle.get_special_data(
            all_team_rating_standard_list, project_category_basic_info_dict
        )
        # 打平团队评分详情数据
        all_flat_score_list = TeamRatingHandle.flat_all_team_rating_standard_list(
            all_team_rating_standard_list, project_category_basic_info_dict
        )
        # 计算项目品类分数
        project_category_score_list = (
            ProjectRatingHandle.get_project_category_score(
                all_flat_score_list, special_data, project_category_basic_info_dict
            )
            if all_flat_score_list
            else []
        )
        # 重新计算完成需恢复默认值
        is_recalculate = 0
        variables = {
            "special_data": special_data,
            "all_flat_score_list": all_flat_score_list,
            "project_category_score_list": project_category_score_list,
            "is_recalculate": is_recalculate,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def approval_export_data_handle(self, all_team_rating_standard_list):
        """
        审批节点导出数据处理(各团队评分数据 5类 1、2合并且去掉权重)
        """
        if all_team_rating_standard_list is None:
            all_team_rating_standard_list = []
        approval_export_data = {
            "class1_project_list": [],
            # "class2_project_list": [],
            # "class3_project_list": [],
            "class4_project_list": [],
            "class5_project_list": [],
            "class6_project_list": [],
            "class7_project_list": [],
        }
        for i in range(len(all_team_rating_standard_list["class1_project_list"])):
            project_info = all_team_rating_standard_list["class1_project_list"][i]
            # peoject_rating = []
            for j in range(len(project_info["rating_standard_list"])):
                temp = {
                    **project_info["rating_standard_list"][j],
                    **all_team_rating_standard_list["class2_project_list"][i][
                        "rating_standard_list"
                    ][j],
                    "project_name": project_info["project_name"],
                    "category_eq_class_name": "集采设备质量评价",
                }
                temp.pop("team_weight", None)
                approval_export_data["class1_project_list"].append(temp)
            # approval_export_data["class1_project_list"].append({
            #     "project_name": project_info["project_name"],
            #     "rating_standard_list": peoject_rating
            # })
            # approval_export_data["class1_project_list"].append(peoject_rating)
        # approval_export_data["class4_project_list"] = all_team_rating_standard_list["class4_project_list"]
        # approval_export_data["class5_project_list"] = all_team_rating_standard_list["class5_project_list"]
        # approval_export_data["class6_project_list"] = all_team_rating_standard_list["class6_project_list"]
        # approval_export_data["class7_project_list"] = all_team_rating_standard_list["class7_project_list"]
        for project_info in all_team_rating_standard_list["class4_project_list"]:
            for rating_data in project_info["rating_standard_list"]:
                temp = {
                    **rating_data,
                    "project_name": project_info["project_name"],
                }
                temp.pop("team_weight", None)
                approval_export_data["class4_project_list"].append(temp)
        for project_info in all_team_rating_standard_list["class5_project_list"]:
            for rating_data in project_info["rating_standard_list"]:
                temp = {
                    **rating_data,
                    "project_name": project_info["project_name"],
                }
                temp.pop("team_weight", None)
                approval_export_data["class5_project_list"].append(temp)
        for project_info in all_team_rating_standard_list["class6_project_list"]:
            for rating_data in project_info["rating_standard_list"]:
                temp = {
                    **rating_data,
                    "project_name": project_info["project_name"],
                }
                temp.pop("team_weight", None)
                approval_export_data["class6_project_list"].append(temp)
        for project_info in all_team_rating_standard_list["class7_project_list"]:
            for rating_data in project_info["rating_standard_list"]:
                temp = {
                    **rating_data,
                    "project_name": project_info["project_name"],
                }
                temp.pop("team_weight", None)
                approval_export_data["class7_project_list"].append(temp)
        variables = {
            "approval_export_data": approval_export_data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def merge_all_team_rating_list(
        self,
        all_flat_score_list,
        project_category_score_list,
        special_data,
    ):
        """
        合并团队给品类-关键指标打的分数（中间评分表）
        即：获取项目-维度-关键指标-品类分数(项目 维度 关键指标 品类 分数 评级)
        """
        if all_flat_score_list is None:
            all_flat_score_list = []
        if project_category_score_list is None:
            project_category_score_list = []
        if special_data is None:
            special_data = []
        merge_all_team_rating_dict = (
            ProjectRatingHandle.get_project_dimensions_indicator_category_score(
                all_flat_score_list, project_category_score_list, special_data
            )
        )
        # 提出 "项目" 用作页面展示
        project_all_team_rating_dict = (
            ProjectRatingHandle.get_project_all_team_rating_dict(
                merge_all_team_rating_dict
            )
        )
        variables = {
            "merge_all_team_rating_dict": merge_all_team_rating_dict,
            "project_all_team_rating_dict": project_all_team_rating_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
