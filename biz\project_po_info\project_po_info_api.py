from biz.project_po_info.tools import Tools


class ProjectPoInfo(object):
    # 项目po信息查询（集成商、时间）
    @staticmethod
    def query_project_po_info(project_name_list):
        # 校验项目名称、项目编号、需求单号是否配置齐全
        if project_name_list:
            project_cfg_err = Tools.check_project_cfg(project_name_list)
            if project_cfg_err:
                return {
                    "code": 400,
                    "msg": f"{(','.join(project_cfg_err))}项目编号/需求单号缺失",
                }
            else:
                insert_list = []
                for project_name in project_name_list:
                    # 获取匹配的po信息
                    po_info = Tools.get_po_info(project_name)
                    if po_info:
                        insert_list += po_info
                    else:
                        return {"code": 400, "msg": f"{project_name}未获取到需求单号"}
                if insert_list:
                    res = Tools.project_po_info_insert(insert_list)
                    if res:
                        return {
                            "code": 200,
                            "msg": "所有项目集成商品类po信息获取成功并成功写入库",
                        }
                    else:
                        return {
                            "code": 200,
                            "msg": "所有项目集成商品类po信息获取成功, 写入库失败",
                        }
                else:
                    return {"code": 200, "msg": "所有项目集成商品类po信息获取成功"}
        else:
            return {"code": 400, "msg": "参数不能为空"}
