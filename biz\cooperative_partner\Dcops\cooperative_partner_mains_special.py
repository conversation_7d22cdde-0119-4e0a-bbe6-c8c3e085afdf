import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class MainsSpecialDcops(object):
    """
        市电专项
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(MainsSpecialDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def mains_special_completed_confirmation_audit(self, project_name, task_name, examine_approve_result,
                                                   dismiss_role, remark):
        """
            市电专项子任务完工确认审批结果回传
        """
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        sdzx_data = {
            "ProjectName": project_name,  # 项目名称
            "TaskName": task_name,  # 子任务名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }

        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "new_electric_item_sub_confirmation_audit",  # (*必填) 云函数名称
                    "data": sdzx_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)
        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceSdzxDcops(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceSdzxDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_wdbz_completion_confirmation_data_submission(self):
        """
            等待外电报装完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word1_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9')"
        word2_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.1'')"
        word1_list = db.get_all(word1_sql)
        word2_list = db.get_all(word2_sql)
        wdbz_word_table = []
        for row in word1_list:
            for item in word2_list:
                serial_number = row.get('serial_number', '')

                # 根据 serial_number 选择需要的字段
                if serial_number == '2.9':
                    sdzx_work_content = row.get('work_content', '')
                    sdzx_start_time = row.get('start_time', '')
                    sdzx_completion_time = row.get('completion_time', '')
                    sdzx_responsible_person = row.get('responsible_person', '')

                if serial_number == '2.9.1':
                    work_content = item.get('work_content', '')
                    start_time = item.get('start_time', '')
                    completion_time = item.get('completion_time', '')
                    actual_start_time = item.get('actual_start_time', '')
                    actual_finish_time = item.get('actual_finish_time', '')
                    schedule_skewing_explain = item.get('schedule_skewing_explain', '')
                    appendix = item.get('appendix', '')
                    appendix_list = []
                    # 使用 ast.literal_eval 解析字符串
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    wdbz_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word2_list})
        if word2_list:
            variables = {
                'sdzx_work_content': sdzx_work_content,
                'sdzx_start_time': sdzx_start_time,
                'sdzx_completion_time': sdzx_completion_time,
                'sdzx_responsible_person': sdzx_responsible_person,
                'wdbz_word_table': wdbz_word_table,
                'word_list': word2_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_wdfa_completion_confirmation_data_submission(self):
        """
            等待外电方案完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.2'"
        word_list = db.get_all(word_sql)
        wdfa_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.2':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})

                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）
                wdfa_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })

        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'wdfa_word_table': wdfa_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_kkf_completion_confirmation_data_submission(self):
        """
            等待可靠费完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.3'"
        word_list = db.get_all(word_sql)
        kkf_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.3':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})
                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                kkf_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'kkf_word_table': kkf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gdht_completion_confirmation_data_submission(self):
        """
            等待供电合同完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.4'"
        word_list = db.get_all(word_sql)
        gdht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.4':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})
                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                gdht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gdht_word_table': gdht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ddxy_completion_confirmation_data_submission(self):
        """
            等待调度协议完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.5'"
        word_list = db.get_all(word_sql)
        ddxy_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.5':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})
                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                ddxy_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'ddxy_word_table': ddxy_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sdqjc_completion_confirmation_data_submission(self):
        """
            等待送电前检测完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.6'"
        word_list = db.get_all(word_sql)
        sdqjc_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.6':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})
                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                sdqjc_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sdqjc_word_table': sdqjc_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sd_completion_confirmation_data_submission(self):
        """
            等待送电完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.7'"
        word_list = db.get_all(word_sql)
        sd_word_table = []
        appendix_list = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.9.7':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix_list = []
                # 尝试解析 appendix 为 JSON 格式
                file_list = ast.literal_eval(appendix) if appendix else []
                # 处理文件列表
                for file_item in file_list:
                    # 从 response 中获取 FileList
                    if isinstance(file_item, dict) and 'response' in file_item:
                        file_list_data = file_item['response'].get('FileList', [])
                        # 遍历 FileList 获取 url
                        for file_data in file_list_data:
                            if 'url' in file_data:
                                appendix_list.append({"appendix": file_data['url']})
                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                sd_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "actual_construction_time": actual_construction_time,
                    "planned_project_duration": planned_project_duration,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sd_word_table': sd_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
