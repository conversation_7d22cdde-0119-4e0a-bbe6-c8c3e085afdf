import json

from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib.sdk import flow
from iBroker.lib import mysql

from biz.construction_before_eruipment.tools import Tools


class GroundApprovedPlan(AjaxTodoBase):
    """
        地面及放线审核通过的方案上传
    """

    def __init__(self):
        super(GroundApprovedPlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def ckeck_upload(self, ground_plan):
        # 捕获异常
        try:
            url, result = ground_plan["response"]["FileList"][0]["url"], False
        except KeyError:
            url, result = {"code": -1, "msg": "未上传地面及放线方案"}, True
        except IndexError:
            url, result = {"code": -1, "msg": "未上传地面及放线方案"}, True
        return url, result

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ground_list = self.ctx.variables.get("ground_list", {})

        # 获取信息(判断是否为空）
        ground_plan = process_data.get("ground_plan", None)
        plan_remark = ground_plan.get("remark")
        complete_report_review_report = ground_plan.get("complete_report_review_report")
        url_list = []
        for i in complete_report_review_report:
            url, result = self.ckeck_upload(i)
            if result:
                return url
            if len(i) > 0 and 'response' in i:
                url_list.append(url)
        url_str = ";".join(url_list)

        insert_data = {
            "ticket_id": ticket_id,
            'name': 'ground',
            "plan_url": url_str,
            "plan_remark": plan_remark
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_before_url", insert_data)
        tb_db.commit()
        ground_list["plan_remark"]: plan_remark
        # 流程变量回存
        variables = {
            "ground_plan": ground_plan,
            "ground_list": ground_list
        }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class GroundReport(AjaxTodoBase):
    """
        地面及放线审核通过的完工报告
    """

    def __init__(self):
        super(GroundReport, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取信息(判断是否为空）
        ticket_id = self.ctx.variables.get("ticket_id")
        ground_list = process_data.get("ground_list", None)
        all_report_url = []
        all_photo_url = []

        # 存数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        if ground_list:
            work_list = ground_list.get('work_list')

            # 适配老流程增加错误处理
            complete_report = ground_list.get('complete_report', None)
            if complete_report:
                report_url_list = []
                for i in complete_report:
                    report_url, report_result = Tools.check_report(i)
                    if report_result:
                        return report_url
                    if len(i) > 0 and 'response' in i:
                        report_url_list.append(report_url)
                report_url_str = ";".join(report_url_list)

            complete_photo = ground_list.get('complete_photo', None)
            if complete_photo:
                photo_url_list = []
                for i in complete_photo:
                    photo_url, photo_result = Tools.check_photo(i)
                    if photo_result:
                        return photo_url
                    if len(i) > 0 and 'response' in i:
                        photo_url_list.append(photo_url)
                photo_url_str = ";".join(photo_url_list)

            for i in work_list:
                complete_report = i.get('complete_report')
                complete_photo = i.get('complete_photo')
                # report_url_list = []
                if complete_report and complete_photo:
                    for report in complete_report:
                        report_url, report_result = Tools.check_report(report)
                        if report_result:
                            return report_url
                        if len(report) > 0 and 'response' in report:
                            # report_url_list.append(report_url)
                            all_report_url.append(report_url)

                    # photo_url_list = []
                    for photo in complete_photo:
                        photo_url, photo_result = Tools.check_photo(photo)
                        if photo_result:
                            return photo_url
                        if len(photo) > 0 and 'response' in photo:
                            # photo_url_list.append(photo_url)
                            all_photo_url.append(photo_url)

                    # i['report_url_list'] = report_url_list
                    # i['photo_url_list'] = photo_url_list
                    work_content = i.get('work_content', None)
                    update_data = {
                        'start_time': i.get('start_time', None),
                        'finish_time': i.get('finish_time', None),
                        "construction_period": i.get('construction_period', None),
                        "progress_deviation": i.get('progress_deviation', None),
                        "remark": i.get('remark', None),
                        "report": json.dumps(complete_report, ensure_ascii=False),
                        "photo": json.dumps(complete_photo, ensure_ascii=False),
                    }
                    conditions = {
                        'ticket_id': ticket_id,
                        'work_content': work_content
                    }
                    tb_db.begin()
                    tb_db.update("equipment_before_data", update_data, conditions)
                    tb_db.commit()
        if all_report_url and all_photo_url:
            report_url_str = ";".join(all_report_url)
            photo_url_str = ";".join(all_report_url)
        data = {
            'report_url': report_url_str,
            'photo_url': photo_url_str
        }
        # 查询条件
        cond = {
            'ticket_id': ticket_id,
            'name': 'ground',
        }

        tb_db.begin()
        tb_db.update("equipment_before_url", data, cond)
        tb_db.commit()

        variables = {
            'ground_list': ground_list,
        }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
