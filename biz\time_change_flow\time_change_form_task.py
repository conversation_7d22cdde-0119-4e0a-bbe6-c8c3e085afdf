from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService


# 时间变更需求提出
class TimeChangeForm(AjaxTodoBase):
    def __init__(self):
        super(TimeChangeForm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get("project_name")
        change_reason = process_data.get("change_reason")
        change_time = process_data.get("change_time")
        submit_file = process_data.get("submit_file")
        deliver_time = process_data.get("demand_delivery")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "project_name": project_name,
            "change_reason": change_reason,
            "change_time": change_time,
            "submit_file": submit_file,
            "deliver_time": deliver_time,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 运管审批
class TimeChangeOMApproval(AjaxTodoBase):
    def __init__(self):
        super(TimeChangeOMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        om_pass = process_data.get("om_pass")
        om_remark = process_data.get("om_remark")
        submit_file_om = process_data.get("submit_file_om")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "om_pass": om_pass,
            "om_remark": om_remark,
            "submit_file_om": submit_file_om,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# PM审批
class TimeChangePMApproval(AjaxTodoBase):
    def __init__(self):
        super(TimeChangePMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        pm_pass = process_data.get("pm_pass")
        pm_remark = process_data.get("pm_remark")
        submit_file_pm = process_data.get("submit_file_pm")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "pm_pass": pm_pass,
            "pm_remark": pm_remark,
            "submit_file_pm": submit_file_pm,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 商务审批
class TimeChangeBusinessApproval(AjaxTodoBase):
    def __init__(self):
        super(TimeChangeBusinessApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        business_pass = process_data.get("business_pass")
        submit_file_business = process_data.get("submit_file_business")
        business_remark = process_data.get("business_remark")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "business_pass": business_pass,
            "business_remark": business_remark,
            "submit_file_business": submit_file_business,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)