from iBroker.lib import mysql

from datetime import datetime
from dateutil.relativedelta import relativedelta


class DailyReportApi(object):

    # 获取上一天的日报-施工情况
    @staticmethod
    def get_yesterday_construction_situation(project_name, date):
        today = datetime.strptime(date, "%Y-%m-%d").date()
        yesterday = today - relativedelta(days=1)
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT zone, sub_item, specific_content, person_num, progress FROM construction_daily_situation "
            "WHERE project_report_id = "
            "(SELECT id FROM project_daily_report "
            f"WHERE project_name = '{project_name}' AND report_date = '{yesterday}' "
            "ORDER BY created_at desc)"
        )
        result = db.query(sql)
        return result
