import ast
import datetime
import hashlib
import hmac
import json
import time
import uuid
import numpy as np

import pandas as pd
from iBroker.lib.sdk import flow, tof, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql, config, curl


class GetEquipmentManager(object):
    """
        获取设备责任人、电话、设备PO、设备供应商、设备名称
    """

    def __init__(self):
        super(GetEquipmentManager, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_equipment_info(self):
        project_name = self.ctx.variables.get("project_name")
        PM = self.ctx.variables.get("PM")
        email_receiver_list_2 = self.ctx.variables.get("email_receiver_list_2")

        db = mysql.new_mysql_instance("tbconstruct")
        manager_sql = \
            f"""
            -- 外层查询，用于判断是否有有效的 po_code 并决定是否执行后续查询
            SELECT
              final.project_name,
              final.position,
              final.po_code,
              final.device_name,
              snm.js_supplier_name AS supplier,
              final.responsible_person,
              final.phone,
              final.po_create_time,
              -- 添加 po_create_time 字段
              final.account -- 添加 account 字段
            FROM
              (
                -- 子查询用于检查是否有有效的 po_code
                SELECT
                  CASE
                    WHEN EXISTS (
                      SELECT
                        1
                      FROM
                        payment_info pi
                      WHERE
                        pi.project_name = '{project_name}'
                        AND pi.po_code IS NOT NULL
                        AND TRIM(pi.po_code) != ''
                    ) THEN 1
                    ELSE 0
                  END AS has_valid_po_code
              ) check_po_code -- 当有有效的 po_code 时才进行后续的复杂查询
              JOIN (
                -- 子查询 1：尝试获取每个 po_code 对应的商务经理记录
                SELECT
                  pi.project_name,
                  pra.position,
                  pi.po_code,
                  ecm.device_name,
                  pi.supplier,
                  pra.name AS responsible_person,
                  pra.phone,
                  pi.po_create_time,
                  -- 添加 po_create_time 字段
                  pra.account -- 添加 account 字段
                FROM
                  payment_info pi
                  LEFT JOIN equipment_category_mapping_relationship ecm ON pi.material_name = ecm.material_category_name
                  LEFT JOIN supplier_name_mapping snm ON pi.supplier = snm.sw_supplier_name
                  LEFT JOIN project_role_account pra ON snm.js_supplier_name = pra.service_provider
                WHERE
                  pi.project_name = '{project_name}'
                  AND pra.position = '商务经理'
                UNION ALL
                  -- 子查询 2：如果没有商务经理记录，尝试获取每个 po_code 对应的项目经理记录
                SELECT
                  pi.project_name,
                  pra.position,
                  pi.po_code,
                  ecm.device_name,
                  pi.supplier,
                  pra.name AS responsible_person,
                  pra.phone,
                  pi.po_create_time,
                  -- 添加 po_create_time 字段
                  pra.account -- 添加 account 字段
                FROM
                  payment_info pi
                  LEFT JOIN equipment_category_mapping_relationship ecm ON pi.material_name = ecm.material_category_name
                  LEFT JOIN supplier_name_mapping snm ON pi.supplier = snm.sw_supplier_name
                  LEFT JOIN project_role_account pra ON snm.js_supplier_name = pra.service_provider
                WHERE
                  pi.project_name = '{project_name}'
                  AND pra.position = '项目经理'
                  AND pi.po_code NOT IN (
                    -- 排除已经有商务经理记录的 po_code
                    SELECT
                      pi2.po_code
                    FROM
                      payment_info pi2
                      LEFT JOIN equipment_category_mapping_relationship ecm2 ON pi2.material_name = ecm2.material_category_name
                      LEFT JOIN supplier_name_mapping snm2 ON pi2.supplier = snm2.sw_supplier_name
                      LEFT JOIN project_role_account pra2 ON snm2.js_supplier_name = pra2.service_provider
                    WHERE
                      pi2.project_name = '{project_name}'
                      AND pra2.position = '商务经理'
                  )
                UNION ALL
                  -- 子查询 3：如果既没有商务经理也没有项目经理记录，获取每个 po_code 的第一条记录
                SELECT
                  pi.project_name,
                  pra.position,
                  pi.po_code,
                  ecm.device_name,
                  pi.supplier,
                  pra.name AS responsible_person,
                  pra.phone,
                  pi.po_create_time,
                  -- 添加 po_create_time 字段
                  pra.account -- 添加 account 字段
                FROM
                  payment_info pi
                  LEFT JOIN equipment_category_mapping_relationship ecm ON pi.material_name = ecm.material_category_name
                  LEFT JOIN supplier_name_mapping snm ON pi.supplier = snm.sw_supplier_name
                  LEFT JOIN project_role_account pra ON snm.js_supplier_name = pra.service_provider
                WHERE
                  pi.project_name = '{project_name}'
                  AND pi.po_code NOT IN (
                    -- 排除已经有商务经理或项目经理记录的 po_code
                    SELECT
                      pi2.po_code
                    FROM
                      payment_info pi2
                      LEFT JOIN equipment_category_mapping_relationship ecm2 ON pi2.material_name = ecm2.material_category_name
                      LEFT JOIN supplier_name_mapping snm2 ON pi2.supplier = snm2.sw_supplier_name
                      LEFT JOIN project_role_account pra2 ON snm2.js_supplier_name = pra2.service_provider
                    WHERE
                      pi2.project_name = '{project_name}'
                      AND (
                        pra2.position = '商务经理'
                        OR pra2.position = '项目经理'
                      )
                  )
              ) final ON check_po_code.has_valid_po_code = 1
              LEFT JOIN supplier_name_mapping snm ON final.supplier = snm.sw_supplier_name
            GROUP BY
              final.po_code
            ORDER BY
              final.po_code;
            """
        issued_list = []
        unissued_list = []
        manager_list = db.get_all(manager_sql)
        for row in manager_list:
            if row.get('po_code'):
                issued_list.append(row)
            elif not row.get('po_code'):
                unissued_list.append(row)

        msg_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件通知</title>
                <style>
                    .title{{
                        margin-bottom: 10px;
                        color: #212529;
                        text-align: center;
                    }}
                    .info-title{{
                        display: inline-block;
                        width: 7rem;
                        text-align: right;
                        font-weight: bold;
                    }}
                    .table-head {{
                        color: #fff;
                        background-color: #343a40;
                        border-color: #454d55;
                        padding: 0.75rem;
                        font-size: 0;
                        font-weight: bold;
                        text-align: center;
                    }}
                    .table-head-item{{
                        display: inline-block;
                        font-size: 16px;
                    }}
                    .row-box{{
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                        font-size: 0;
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }}
                    .row-item{{
                        display: inline-block; font-size: 16px;
                    }}
                    
                    .approve-info-title01{{
                        display: inline-block;
                        width: 15%;
                        text-align: right; font-weight: bold;
                    }}
                    .approve-info-title02{{
                        display: inline-block; vertical-align: top; width: 15%; font-size: 16px; text-align: right; font-weight: bold;
                    }}
                    .approve-info-remark02{{
                        display: inline-block; vertical-align: top; width: 85%; font-size: 16px;
                    }}
                    .attachment-box{{
                        width: 15%;
                        text-align: right;
                        padding-left: 40px;
                    }}
                </style>
            </head>
            <body>
                <div style="font-size: 16px;">
                    <h2 class="title">{project_name}的po信息</h2>
                    <div>
                        <h3>1. 已下发po</h3>
                        <p><span class="info-title">项目名：</span>{project_name}</p>
                    </div>
                </div>
                
                <div class="table-head">
                    <span class="table-head-item" style="width: 10%;">序号</span>
                    <span class="table-head-item" style="width: 10%;">设备名称</span>
                    <span class="table-head-item" style="width: 20%;">PO单号</span>
                    <span class="table-head-item" style="width: 20%;">PO下发时间</span>
                    <span class="table-head-item" style="width: 10%;">供应商</span>
                    <span class="table-head-item" style="width: 10%;">负责人/联系人</span>
                    <span class="table-head-item" style="width: 10%;">企业账号</span>
                    <span class="table-head-item" style="width: 10%;">电话</span>
                </div>
            """
        for index, value in enumerate(issued_list):
            msg_content += f"""
                        <div class="row-box">
                            <span class="row-item" style="width: 10%;">{index + 1}</span>
                            <span class="row-item" style="width: 10%;">{value.get('device_name')}</span>
                            <span class="row-item" style="width: 20%;">{value.get('po_code')}</span>
                            <span class="row-item" style="width: 20%;">{value.get('po_create_time')}</span>
                            <span class="row-item" style="width: 10%;">{value.get('supplier')}</span>
                            <span class="row-item" style="width: 10%;">{value.get('responsible_person')}</span>
                            <span class="row-item" style="width: 10%;">{value.get('account')}</span>
                            <span class="row-item" style="width: 10%;">{value.get('phone')}</span>
                        </div>
                        """
        # 处理未下发的 PO
        msg_content += f"""
                    <div style="font-size: 16px;">
                        <div>
                            <h3>2. 未下发po</h3>
                            <p><span class="info-title">项目名：</span>{project_name}</p>
                        </div>
                    </div>
                    <div class="table-head">
                        <span class="table-head-item" style="width: 50%;">序号</span>
                        <span class="table-head-item" style="width: 50%;">设备名称</span>
                    </div>
        """
        for index, value in enumerate(unissued_list):
            msg_content += f"""
                        <div class="row-box">
                            <span class="row-item" style="width: 50%;">{index + 1}</span>
                            <span class="row-item" style="width: 50%;">{value.get('device_name')}</span>
                        </div>
        """
        # 结束 HTML 内容
        msg_content += """
            </body>
            </html>
        """
        email_receiver_list_2.append(PM + '@tencent.com')
        title = 'PO信息邮件发送'
        tof.send_email(sendTitle=title, msgContent=msg_content, sendTo=email_receiver_list_2)
        variables = {
            'manager_list': manager_list,
            'msg_content': msg_content
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
