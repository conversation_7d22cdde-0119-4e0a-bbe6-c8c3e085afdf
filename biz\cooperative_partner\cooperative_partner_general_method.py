import ast
import hashlib
import hmac
import json
import re
import time

from iBroker.lib import mysql, curl, config
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.utils.change_url import download_and_upload_file


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class CooperativePartnerGeneralMethod(AjaxTodoBase):
    """
        合作方通用方法
    """

    def abnormal_return(self, system_type, info):
        """
            异常返回
        """
        if system_type == "外部":
            if info.get('ErrorCode') != 0:
                return {
                    'code': -1,
                    'msg': f'报错信息为：{info.get("Message")}'
                }
            else:
                return {"code": 0, "msg": '推送成功'}
        elif system_type == "Dcops":
            data = info['data']

            # 获取 code 的值
            code = data.get('code')
            message = info.get('msg')

            if code != 0:
                return {
                    'code': -1,
                    'msg': f'报错信息为：{message}'
                }
            elif code == 0:
                result = data.get('result')
                result_dict = json.loads(result)
                code = result_dict.get('code')
                message = result_dict.get('message')
                if code == 0:
                    return {
                        'code': 0,
                        'msg': '推送成功'
                    }
                elif code == -1:
                    return {
                        'code': -1,
                        'msg': f'推送失败,报错信息为：{message}'
                    }

    def file_format_processing(self, system_type, file_table):
        """
            文件格式处理
        """
        # 处理传过来的数据格式
        file_list = []
        if system_type == "外部":
            file_table = ast.literal_eval(file_table)
            if isinstance(file_table, list):
                for i in file_table:
                    info = download_and_upload_file(i)
                    file_list.append({
                        "file": info
                    })
            return file_list
        elif system_type == "Dcops":
            file_table = ast.literal_eval(file_table)
            if isinstance(file_table, list):
                for item in file_table:
                    if isinstance(item, dict):
                        file = item.get('response', {}).get('FileList', [])[0].get('url', '')
                        info = download_and_upload_file(file)
                        file_list.append({
                            "file": info
                        })
            return file_list

    def photo_format_stitching(self, photo_table):
        """
            图片格式拼接
        """
        # 处理传过来的数据格式
        new_photo_result = []
        if photo_table:
            for row in photo_table:
                photo = row.get("photo")
                new_photo = {
                    "height": "150px",
                    "marginRight": "5px",
                    "url": photo,
                    "width": "150px",
                }
                new_photo_result.append(new_photo)
        return new_photo_result


class CheckProgressWorkOrder(object):
    """
        检查工单进度
    """

    def tencent_work_order_progress(self, TencentTicketId):
        """
            查询腾讯工单进度
        """
        if not TencentTicketId:
            return {
                "code": -1,
                "msg": "工单号不能为空"
            }
        instance_id = ''
        tencent_work_order_progress_data = []
        progress_count = 0
        data = {
            "ResultColumns": {
                "InstanceId": ""
            },
            "SearchCondition": {'TicketId': TencentTicketId}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        if not instance_id:
            return {
                "code": -1,
                "msg": "查询失败,工单不存在"
            }
        if instance_id:
            data_instanceId = {
                "InstanceId": instance_id
            }
            # 请求获取工作流日志
            res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
            for row in res:
                if row.get('TaskStatus') == '进行中':
                    tencent_work_order_progress_data.append({
                        "CurrentNode": row.get("TaskName"),
                        "Processor": row.get('Operator')
                    })
                    progress_count += 1
                elif row.get('TaskStatus') is not None:  # 处理其他非空状态的情况
                    pass
            if progress_count > 0:
                return {
                    "code": 0,
                    "msg": "查询成功",
                    "data": tencent_work_order_progress_data
                }
            elif progress_count == 0:
                return {
                    "code": -1,
                    "msg": "工单已关闭",
                    "data": [{"CurrentNode": "工单已结束", "Processor": ""}]
                }

    def cooperative_partner_work_order_progress(self, Facilitator, system_id, system_type, PartnerTicketId):
        """
            查询合作伙伴工单进度
        """
        cooperative_partner_work_order_progress_data = {
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "cooperative_partner_work_order_progress",  # (*必填) 云函数名称
                        "data": cooperative_partner_work_order_progress_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            return info
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": cooperative_partner_work_order_progress_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/cooperative_partner_work_order_progress"
                }
            )
            return info

    def test_work_order_progress(self):
        """
            测试工单进度
        """
        pass


class MasterControlPlanTemplate(object):
    def obtain_master_control_plan_template(self):
        """
            获取项目总控计划模板
        """
        master_control_plan_template = config.get_config_string(
            "master_control_plan_template"
        )
        data = {
            "MasterControlPlan": master_control_plan_template
        }

        return {'code': 200, 'msg': '成功', 'data': data}


class CooperativePartnerWorkOrderProgress(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            获取工单进度
        """


class IsItOrNotCooperativePartner(object):
    """
        是否为合作伙伴流程
        是的话是Dcops系统还是外部系统
    """

    def __init__(self):
        super(IsItOrNotCooperativePartner, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def is_it_partner_general_check(self, campus, project):
        """
        一般用判断是否为合作伙伴
        """
        Facilitator = ''
        # campus = self.ctx.variables.get("campus")
        # project = self.ctx.variables.get("project")
        # project_name = self.ctx.variables.get("project_name")
        # if not campus and not project:
        #     match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
        #     if match:
        #         campus = match.group(1)
        #         project = match.group(2)
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT integrators " \
              "FROM supplier_resources " \
              f"WHERE campus = '{campus}' " \
              f"AND project = '{project}' "
        result_list = db.get_all(sql)
        for row in result_list:
            Facilitator = row.get("integrators")
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map("cooperative_partner_process")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        # 首先判断是否为合作伙伴
        if Facilitator in cooperative_partner_process:
            is_or_not = '是'
        else:
            is_or_not = '否'

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        variables = {
            'Facilitator': Facilitator,
            'is_or_not': is_or_not,
            'system_type': system_type,
        }
        if Facilitator:
            system_id, corp_id = obtain_supplier_information('supplier_differentiation', Facilitator)
            variables.update({
                'system_id': system_id,
                'corp_id': corp_id,
            })
        return variables

    def is_it_cooperative_partner(self):
        """
            判断是否为合作伙伴
        """
        Facilitator = ''
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        project_name = self.ctx.variables.get("project_name")
        if not campus and not project:
            match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
            if match:
                campus = match.group(1)
                project = match.group(2)
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT integrators " \
              "FROM supplier_resources " \
              f"WHERE campus = '{campus}' " \
              f"AND project = '{project}' "
        result_list = db.get_all(sql)
        for row in result_list:
            Facilitator = row.get("integrators")
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map("cooperative_partner_process")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        # 首先判断是否为合作伙伴
        if Facilitator in cooperative_partner_process:
            is_or_not = '是'
        else:
            is_or_not = '否'

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        variables = {
            'Facilitator': Facilitator,
            'is_or_not': is_or_not,
            'system_type': system_type
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class IsPartnerProcessAvailable(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def is_partner_process_available(self):
        """
            流程节点-判断合作伙伴流程是否可用
        """
        Facilitator = ""
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        # if not campus or not project:
        #     project_name = self.ctx.variables.get("project_name")
        #     match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
        #     if match:
        #         campus = match.group(1)
        #         project = match.group(2)
        db = mysql.new_mysql_instance("tbconstruct")
        if not campus or not project:
            project_name = self.ctx.variables.get("project_name")
            sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                           f"WHERE project_name='{project_name}'")
            is_hire = False if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else True
            if not is_hire:
                variables = {
                    'Facilitator': Facilitator,
                    'is_or_not': "否",
                    'system_type': ""
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return None
        sql = "SELECT integrators " \
              "FROM supplier_resources " \
              f"WHERE campus = '{campus}' " \
              f"AND project = '{project}' "
        result_list = db.get_all(sql)
        for row in result_list:
            Facilitator = row.get("integrators")

        # 获取合作伙伴可用流程配置
        partner_process_available_service_provider = config.get_config_map("partner_process_available_service_provider")
        # 获取供应商系统名分类单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        process_definition_key = self.ctx.variables.get("_process_definition_key")
        available_service_provider_list = partner_process_available_service_provider.get(process_definition_key)

        if Facilitator in available_service_provider_list:
            is_or_not = '是'
        else:
            is_or_not = '否'
        self.workflow_detail.add_kv_table("信息",{'合作伙伴流程是否可用': is_or_not,
                                                  "配置":partner_process_available_service_provider})

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        variables = {
            'Facilitator': Facilitator,
            'is_or_not': is_or_not,
            'system_type': system_type
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
