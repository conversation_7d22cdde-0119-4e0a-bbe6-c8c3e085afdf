import functools
import os

from iBroker.lib.context import get_context
from iBroker.lib.sdk import gnetops, flow
from iBroker.lib.sdk.flow import is_task_end


def get_ticket_info(ticket_id_list: list):
    """
    根据工单号列表获取工单信息
    :param ticket_id_list: 工单号列表
    :return:
    """
    data = {
        "ResultColumns": {
            "CreateTime": "",
            "EndTime": "",
            "InstanceId": "",
            # 是否强制结单
            "IsForceEnd": "",
            # 流程定义标识
            "ProcessDefinitionKey": "",
            "ServiceRecoverTime": "",
            "StartProcessTime": "",
            # 单据状态[OPEN:运行中;END:已结束]
            "TicketStatus": "",
            "Title": "",
            "CustomRequestVarKV": "",
        },
        "SearchCondition": {'TicketId': ticket_id_list}
    }

    extra_data = {"SchemaId": "ticket_base"}
    ticket_info = gnetops.request(
        action="QueryData", method="Run", data=data, ext_data=extra_data
    )
    res = ticket_info['List']
    return res


def cycle_task_over(self):
    """判断流程或节点是否结束
    主要作用就是消除循环任务因为流程或任务节点结束而报错
    """
    # 判断当前流程是否结束，如果流程已结束，便结束定时任务
    if flow.is_instance_end(self.ctx.instance_id):
        return {"success": True, "data": "流程已结束"}
    # 判断节点是否结束，如果任务节点已经流转结束，便结束定时任务
    if flow.is_task_end(self.ctx.task_id):
        return {"success": True, "data": f"任务节点{self.ctx.task_id}已结束"}


def cycle_task_abort(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        env_flag = os.environ.get('ENVFLAG', None)
        ctx = get_context()
        if env_flag != 'devcloud' and is_task_end(task_id=ctx.task_id):
            return {'success': True}
        return func(*args, **kwargs)

    return wrapper


def ticket_is_over(ticket_id_list: list):
    """
    判断工单是否结束
    :param ticket_id_list: 工单号列表
    :return:
    """
    cnt = 0  # 用于记录有多少个工单正在运行中
    res_list = get_ticket_info(ticket_id_list)
    for item in res_list:
        if item.get('TicketStatus') == 'OPEN':
            cnt += 1
    return cnt
