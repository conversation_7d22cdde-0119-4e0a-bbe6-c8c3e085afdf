import uuid
import numpy as np
import pandas as pd

from biz.construction_process.cos_lib import COSLib


# 模板操作
class Tools(object):

    # 判断文件是否存在

    @staticmethod
    def check_report(complete_report):
        try:
            report_url, report_result = complete_report["response"]["FileList"][0]["url"], False
        except KeyError:
            report_url, report_result = {"code": -1,
                                         "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            report_url, report_result = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return report_url, report_result

    @staticmethod
    def check_photo(complete_photo):
        try:
            photo_url, photo_result = complete_photo["response"]["FileList"][0]["url"], False
        except KeyError:
            photo_url, photo_result = {"code": -1,
                                       "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            photo_url, photo_result = {"code": -1,
                                       "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return photo_url, photo_result

    @staticmethod
    def check_review_report(complete_review_report):
        try:
            review_report_url, review_report_result = complete_review_report["response"]["FileList"][0]["url"], False
        except KeyError:
            review_report_url, review_report_result = {"code": -1,
                                                       "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            review_report_url, review_report_result = {"code": -1,
                                                       "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return review_report_url, review_report_result
