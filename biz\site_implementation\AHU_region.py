import uuid
import numpy as np
import pandas as pd
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class AHURegion(AjaxTodoBase):
    """
        AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(AHURegion, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_itemized_plan_is_upload(self, ahu_sub_plan: list):
        try:
            datas, bools = ahu_sub_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        return datas, bools

    def verify_whether_the_upload_is_correct(self, ahu_sub_plan: list):
        url = ahu_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel ahu解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        for _, row in df.iterrows():
            serial_number = row['序号']
            task_name = row['工作内容']
            if serial_number == 3.7 and task_name == 'AHU区域 ':
                return {"code": 0, "msg": "分项计划上传正确"}, False
        return {"code": -1, "msg": "分项计划上传错误，请重新上传"}, True

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ahu_sub_plan = process_data.get('ahu_sub_plan', None)
        datas, bools = self.check_itemized_plan_is_upload(ahu_sub_plan)
        sub_plan_data, sub_plan_bool = self.verify_whether_the_upload_is_correct(ahu_sub_plan)
        if bools:
            return datas
        if sub_plan_bool:
            return sub_plan_data
        update_data = {}
        if ahu_sub_plan and len(ahu_sub_plan) > 0 and 'response' in ahu_sub_plan[0]:
            update_data = {
                'cfscl_sub_plan': ahu_sub_plan[0]["response"]["FileList"][0]["url"]
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_sub_plan': ahu_sub_plan
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ElectricalEngineeringAHU(AjaxTodoBase):
    """
        电气工程_AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(ElectricalEngineeringAHU, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ahu_dqgc_list = process_data.get("ahu_dqgc_list", {})
        work_list = ahu_dqgc_list.get("work_list", [])

        ahu_dqgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            ahu_dqgc_dict = {
                'work_content': '电气工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'ahu_dqgc_dict': ahu_dqgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'dqgc_actual_start_time': actual_start_time,
                'dqgc_actual_finish_time': actual_finish_time,
                'dqgc_actual_construction_time': actual_construction_time,
                'dqgc_schedule_deviation': schedule_deviation,
                'dqgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '电气工程',
                'dqgc_work_content': work_content
            }
            tb_db.update("ahu_data", update2_data, conditions2)
        complete_report = ahu_dqgc_list.get('complete_report', None)
        complete_photo = ahu_dqgc_list.get('complete_photo', None)
        complete_report_review_report = ahu_dqgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'dqgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'dqgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'dqgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '电气工程'
        }
        tb_db.update("ahu_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_dqgc_list': ahu_dqgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'ahu_dqgc_dict': ahu_dqgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WeakCurrentEngineeringAHU(AjaxTodoBase):
    """
        弱电工程_AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(WeakCurrentEngineeringAHU, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ahu_rdgc_list = process_data.get("ahu_rdgc_list", {})
        work_list = ahu_rdgc_list.get("work_list", [])

        ahu_rdgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            ahu_rdgc_dict = {
                'work_content': '弱电工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'ahu_rdgc_dict': ahu_rdgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'rdgc_actual_start_time': actual_start_time,
                'rdgc_actual_finish_time': actual_finish_time,
                'rdgc_actual_construction_time': actual_construction_time,
                'rdgc_schedule_deviation': schedule_deviation,
                'rdgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '弱电工程',
                'rdgc_work_content': work_content
            }
            tb_db.update("ahu_data", update2_data, conditions2)
        complete_report = ahu_rdgc_list.get('complete_report', None)
        complete_photo = ahu_rdgc_list.get('complete_photo', None)
        complete_report_review_report = ahu_rdgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'rdgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'rdgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'rdgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '弱电工程'
        }
        tb_db.update("ahu_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_rdgc_list': ahu_rdgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'ahu_rdgc_dict': ahu_rdgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RenovationProjectAHU(AjaxTodoBase):
    """
        装修工程_AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(RenovationProjectAHU, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ahu_zxgc_list = process_data.get("ahu_zxgc_list", {})
        work_list = ahu_zxgc_list.get("work_list", [])

        ahu_zxgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            ahu_zxgc_dict = {
                'work_content': '装修（结构工程）',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'ahu_zxgc_dict': ahu_zxgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'zx_actual_start_time': actual_start_time,
                'zx_actual_finish_time': actual_finish_time,
                'zx_actual_construction_time': actual_construction_time,
                'zx_schedule_deviation': schedule_deviation,
                'zx_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '装修（结构工程）',
                'zx_work_content': work_content
            }
            tb_db.update("ahu_data", update2_data, conditions2)
        complete_report = ahu_zxgc_list.get('complete_report', None)
        complete_photo = ahu_zxgc_list.get('complete_photo', None)
        complete_report_review_report = ahu_zxgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'zx_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'zx_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'zx_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '装修（结构工程）'
        }
        tb_db.update("ahu_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_zxgc_list': ahu_zxgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'ahu_zxgc_dict': ahu_zxgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class HeatingProjectAHU(AjaxTodoBase):
    """
        暖通工程_AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(HeatingProjectAHU, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ahu_ntgc_list = process_data.get("ahu_ntgc_list", {})
        work_list = ahu_ntgc_list.get("work_list", [])

        ahu_ntgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '跳过':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            ahu_ntgc_dict = {
                'work_content': '消防工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            ahu_dqgc_dict = self.ctx.variables.get('ahu_dqgc_dict')
            ahu_rdgc_dict = self.ctx.variables.get('ahu_rdgc_dict')
            ahu_zxgc_dict = self.ctx.variables.get('ahu_zxgc_dict')

            ahu_skip_list = []
            if ahu_dqgc_dict:
                ahu_skip_list.append(ahu_dqgc_dict)
            if ahu_rdgc_dict:
                ahu_skip_list.append(ahu_rdgc_dict)
            if ahu_zxgc_dict:
                ahu_skip_list.append(ahu_zxgc_dict)
            if ahu_ntgc_dict:
                ahu_skip_list.append(ahu_ntgc_dict)

            variables = {
                'ahu_ntgc_dict': ahu_ntgc_dict,
                'ahu_skip_list': ahu_skip_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            ahu_dqgc_dict = self.ctx.variables.get('ahu_dqgc_dict')
            ahu_rdgc_dict = self.ctx.variables.get('ahu_rdgc_dict')
            ahu_zxgc_dict = self.ctx.variables.get('ahu_zxgc_dict')

            ahu_skip_list = []
            if ahu_dqgc_dict:
                ahu_skip_list.append(ahu_dqgc_dict)
            if ahu_rdgc_dict:
                ahu_skip_list.append(ahu_rdgc_dict)
            if ahu_zxgc_dict:
                ahu_skip_list.append(ahu_zxgc_dict)
            if ahu_ntgc_dict:
                ahu_skip_list.append(ahu_ntgc_dict)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'ntgc_actual_start_time': actual_start_time,
                'ntgc_actual_finish_time': actual_finish_time,
                'ntgc_actual_construction_time': actual_construction_time,
                'ntgc_schedule_deviation': schedule_deviation,
                'ntgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '暖通工程',
                'ntgc_work_content': work_content
            }
            tb_db.update("ahu_data", update2_data, conditions2)
        complete_report = ahu_ntgc_list.get('complete_report', None)
        complete_photo = ahu_ntgc_list.get('complete_photo', None)
        complete_report_review_report = ahu_ntgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'ntgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'ntgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'ntgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '暖通工程'
        }
        tb_db.update("ahu_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_ntgc_list': ahu_ntgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'ahu_skip_list': ahu_skip_list,
            'ahu_ntgc_dict': ahu_ntgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AHUPMApproval(AjaxTodoBase):
    """
        AHU-PM审批
    """

    def __init__(self):
        super(AHUPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ahu_PM_approval = process_data.get('ahu_pm_approval')
        ahu_PM_remark = process_data.get('ahu_PM_remark')

        if ahu_PM_approval == '驳回':
            ahu_skip_list = []
            if not ahu_PM_remark:
                return {'code': -1, 'msg': '驳回时，未填写备注'}

        variables = {
            'ahu_PM_approval': ahu_PM_approval,
            'ahu_PM_remark': ahu_PM_remark,
            'ahu_skip_list': ahu_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DevicePowerDebuggingAHU(AjaxTodoBase):
    """
        设备上电调试_AHU区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(DevicePowerDebuggingAHU, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_power_debug_is_upload(self, power_debug: list):
        try:
            datas, bools = power_debug[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        ahu_sdts_list = process_data.get('ahu_sdts_list')
        ticket_id = self.ctx.variables.get("ticket_id")
        work_list = ahu_sdts_list.get("work_list", [])
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'sdts_actual_start_time': actual_start_time,
                'sdts_actual_finish_time': actual_finish_time,
                'sdts_actual_construction_time': actual_construction_time,
                'sdts_schedule_deviation': schedule_deviation,
                'sdts_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '设备上电调试',
                'sdts_work_content': work_content,
            }
            tb_db.update("ahu_data", update2_data, conditions2)
        power_debug = ahu_sdts_list.get('power_debug', None)
        update_data = {}
        datas, bools = self.check_power_debug_is_upload(power_debug)
        if bools:
            return datas
        if power_debug and len(power_debug) > 0 and 'response' in power_debug[0]:
            update_data = {
                'ahu_power_debug': power_debug[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '设备上电调试'
        }
        tb_db.update("ahu_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'ahu_sdts_list': ahu_sdts_list,
            'ahu_power_debug': power_debug
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
