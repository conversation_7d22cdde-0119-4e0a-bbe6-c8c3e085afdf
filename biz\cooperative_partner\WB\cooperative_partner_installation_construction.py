import ast
import datetime
import json

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config
from biz.cooperative_partner.cooperative_partner_general_method import (obtain_supplier_information,
                                                                        calling_external_interfaces)

class InstallationConstructionPreparationWB(object):
    """
        安装施工
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(InstallationConstructionPreparationWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_new_site_construction(self, project_name):
        """
            创建工单--安装施工
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        Facilitator = self.ctx.variables.get("Facilitator")

        construction_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name
        }
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map("cooperative_partner_process")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "create_new_site_construction",  # (*必填) 云函数名称
                        "data": construction_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']

            # 获取 code 的值
            code = result_str.get('code')
            message = result_str.get('msg')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": construction_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/technical_data_approval_results"
                }
            )
            if info.get('ErrorCode') == -1:
                return {'code': -1, 'msg': f'{info.get("Message")}'}
        PartnerTicketId = 0
        if 'Data' in info:
            PartnerTicketId = str(info['Data'])
        elif 'FlowID' in info:
            PartnerTicketId = str(info['FlowID'])

        data_dict = {
            'ticket_id': ticket_id,
            'project_name': project_name,
            'Facilitator': Facilitator,
            'partner_ticket_id': PartnerTicketId
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("installation_construction_partner", data_dict)
        tb_db.commit()
        variables = {
            'info': str(info),
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_level_four_plan_audit_results(self, project_name, scheme_supervision_approval,
                                               scheme_supervision_remark):
        """
            4级分项计划审核结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        if not scheme_supervision_remark:
            scheme_supervision_remark = '无'

        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        scheme_supervision = 0
        if scheme_supervision_approval == "通过":
            scheme_supervision = 1
        elif scheme_supervision_approval == "驳回":
            scheme_supervision = 2
        data = {
            "ProjectName": project_name,  # 项目名称
            "SchemeSupervisionApproval": scheme_supervision,  # 监理审批
            "SchemeSupervisionRemark": scheme_supervision_remark,  # 监理备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/feedback_level_four_plan_audit_results"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'feedback_level_four_plan_audit_results': str(info),
            'data': data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_construction_plan_audit_results(self, project_name, secondary_numbering, secondary_name,
                                                 scheme_supervision_approval, scheme_supervision_remark):
        """
            施工方案审核结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        if not scheme_supervision_remark:
            scheme_supervision_remark = '无'

        scheme_supervision = 0
        if scheme_supervision_approval == "通过":
            scheme_supervision = 1
        elif scheme_supervision_approval == "驳回":
            scheme_supervision = 2
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")

        data = {
            "ProjectName": project_name,  # 项目名称
            "SecondaryNumbering": secondary_numbering,  # 二级分项编号
            "SecondaryName": secondary_name,  # 二级分项名称
            "SchemeSupervisionApproval": scheme_supervision,  # 监理审批
            "SchemeSupervisionRemark": scheme_supervision_remark,  # 监理备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/feedback_construction_plan_audit_results"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'feedback_construction_plan_audit_results': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def completion_report_review_results_feedback(self, project_name, tertiary_numbering, tertiary_name,
                                                  examine_approve_result, dismiss_role, completion_audit_report,
                                                  remark):
        """
            完工报告审核结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'

        data = {
            "ProjectName": project_name,  # 项目名称
            "TertiaryNumbering": tertiary_numbering,  # 三级分项编号
            "TertiaryName": tertiary_name,  # 三级分项名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "CompletionAuditReport": completion_audit_report,  # 完工审核报告
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/completion_report_review_results_feedback"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'completion_report_review_results_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def report_audit_results_feedback(self, project_name, report_tale, item_approval, item_remark):
        """
            报告审核结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        if not item_remark:
            item_remark = '无'

        item = 0
        if item_approval == "同意":
            item = 1
        elif item_approval == "驳回":
            item = 2

        data = {
            "ProjectName": project_name,  # 项目名称
            "ReportTale": report_tale,  # 报告类型（检验、送电）
            "ItemApproval": item,  # 项管审批（1、同意，2、驳回）
            "ItemRemark": item_remark,  # 项管备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/report_audit_results_feedback"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'report_audit_results_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceAzsg(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceAzsg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_project_plan_upload(self):
        """
            等待项目计划上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT project_scheduling, remark " \
              "FROM installation_construction_itemized_plan " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' "
        result_list = db.get_all(sql)
        for row in result_list:
            project_scheduling = row.get('project_scheduling', '')
            remark = row.get('remark', '')
        self.workflow_detail.add_kv_table('项目计划信息', {'message': result_list})
        if result_list:
            variables = {
                "project_scheduling": project_scheduling,
                "remark": remark
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    """
        等待所有的施工计方案上传
    """

    def wait_it_block1_construction_plan_upload(self):
        """
            等待 it_block1 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.1' "
        result_list = db.get_all(sql)
        it_block1_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            it_block1_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "it_block1_construction_plan_list": it_block1_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_construction_plan_upload(self):
        """
            等待 it_block2 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.2' "
        result_list = db.get_all(sql)
        it_block2_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            it_block2_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "it_block2_construction_plan_list": it_block2_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_construction_plan_upload(self):
        """
            等待 it_block3 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.3' "
        result_list = db.get_all(sql)
        it_block3_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            it_block3_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "it_block3_construction_plan_list": it_block3_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_construction_plan_upload(self):
        """
            等待 it_block4 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.4' "
        result_list = db.get_all(sql)
        it_block4_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            it_block4_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "it_block4_construction_plan_list": it_block4_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_dlzl_construction_plan_upload(self):
        """
            等待 电力走廊 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.5' "
        result_list = db.get_all(sql)
        dlzl_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            dlzl_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "dlzl_construction_plan_list": dlzl_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_cf_and_scl_construction_plan_upload(self):
        """
            等待 柴发和水处理方仓 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.6' "
        result_list = db.get_all(sql)
        cf_and_scl_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            cf_and_scl_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "cf_and_scl_construction_plan_list": cf_and_scl_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_construction_plan_upload(self):
        """
            等待 AHU区域 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.7' "
        result_list = db.get_all(sql)
        AHU_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            AHU_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "AHU_construction_plan_list": AHU_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_construction_plan_upload(self):
        """
            等待 办公区域 施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT second_serial_number, second_task_name, second_construction_scheme " \
              "FROM installation_construction_construction_scheme " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND second_serial_number = '3.8' "
        result_list = db.get_all(sql)
        bgqy_construction_plan_list = []
        for record in result_list:
            second_serial_number = record.get('second_serial_number', '')
            second_task_name = record.get('second_task_name', '')
            second_construction_scheme = record.get('second_construction_scheme', '')

            # 尝试将 report_url 字符串解析为列表
            try:
                second_construction_schemes = ast.literal_eval(second_construction_scheme)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                second_construction_schemes = []  # 如果解析失败，设置为空列表
            construction_scheme_list = []
            # 确保 report_urls 是一个列表
            if isinstance(second_construction_schemes, list):
                # 为每个 URL 创建一个新的字典
                for url in second_construction_schemes:
                    construction_scheme_list.append({"second_construction_scheme": url})

            bgqy_construction_plan_list.append({
                "second_serial_number": second_serial_number,
                "second_task_name": second_task_name,
                "second_construction_scheme": construction_scheme_list
            })

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "bgqy_construction_plan_list": bgqy_construction_plan_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    """
        等待分项计划上传
    """

    def wait_it_block1_fckj_confirmation_of_completion(self):
        """
            等待it_block1 方仓及框架安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_fckj_list = []
        """
            IT Block1-方仓及框架安装（结构工程）
        """
        block1_fckj_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.1':
                block1_fckj_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_fckj_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.1.1' and row['serial_number'] <= '3.1.1.6':
                    block1_fckj_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_fckj_task['work_list'].append(block1_fckj_work_item)
        for item in wgqr_list:
            for block1_fckj_work_item in block1_fckj_task['work_list']:
                if item['fourthly_task_name'] == block1_fckj_work_item['work_content']:
                    block1_fckj_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_fckj_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_fckj_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_fckj_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_fckj_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_fckj_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_fckj_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_fckj_list.append(block1_fckj_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_fckj_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_fckj_list': block1_fckj_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block1_qdgc_confirmation_of_completion(self):
        """
            等待it_block1 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_qdgc_list = []

        """
            IT Block1-电气工程（强电工程）
        """
        block1_qdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.2':
                block1_qdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_qdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.2.1' and row['serial_number'] <= '3.1.2.6':
                    block1_qdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_qdgc_task['work_list'].append(block1_qdgc_work_item)
        for item in wgqr_list:
            for block1_qdgc_work_item in block1_qdgc_task['work_list']:
                if item['fourthly_task_name'] == block1_qdgc_work_item['work_content']:
                    block1_qdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_qdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_qdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_qdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_qdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_qdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_qdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_qdgc_list.append(block1_qdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_qdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_qdgc_list': block1_qdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block1_rdgc_confirmation_of_completion(self):
        """
            等待it_block1 弱电系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_rdgc_list = []

        """
            IT Block1-弱电系统（弱电工程）
        """
        block1_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.3':
                block1_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.3.1' and row['serial_number'] <= '3.1.3.10':
                    block1_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_rdgc_task['work_list'].append(block1_rdgc_work_item)
        for item in wgqr_list:
            for block1_rdgc_work_item in block1_rdgc_task['work_list']:
                if item['fourthly_task_name'] == block1_rdgc_work_item['work_content']:
                    block1_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_rdgc_list.append(block1_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_rdgc_list': block1_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block1_zxgc_confirmation_of_completion(self):
        """
            等待it_block1 装修工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_zxgc_list = []

        """
            IT Block1-装修工程
        """
        block1_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.4':
                block1_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.4.1' and row['serial_number'] <= '3.1.4.8':
                    block1_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_zxgc_task['work_list'].append(block1_zxgc_work_item)
        for item in wgqr_list:
            for block1_zxgc_work_item in block1_zxgc_task['work_list']:
                if item['fourthly_task_name'] == block1_zxgc_work_item['work_content']:
                    block1_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_zxgc_list.append(block1_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_zxgc_list': block1_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block1_xfgc_confirmation_of_completion(self):
        """
            等待it_block1 消防工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_xfgc_list = []
        """
            IT Block1-消防工程
        """
        block1_xfgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.5':
                block1_xfgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_xfgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.5.1' and row['serial_number'] <= '3.1.5.9':
                    block1_xfgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_xfgc_task['work_list'].append(block1_xfgc_work_item)
        for item in wgqr_list:
            for block1_xfgc_work_item in block1_xfgc_task['work_list']:
                if item['fourthly_task_name'] == block1_xfgc_work_item['work_content']:
                    block1_xfgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_xfgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_xfgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_xfgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_xfgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_xfgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_xfgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_xfgc_list.append(block1_xfgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_xfgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_xfgc_list': block1_xfgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block1_ntgc_confirmation_of_completion(self):
        """
            等待it_block1 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_ntgc_list = []

        """
            IT Block1-暖通工程
        """
        block1_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.6':
                block1_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.6.1' and row['serial_number'] <= '3.1.6.4':
                    block1_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_ntgc_task['work_list'].append(block1_ntgc_work_item)
        for item in wgqr_list:
            for block1_ntgc_work_item in block1_ntgc_task['work_list']:
                if item['fourthly_task_name'] == block1_ntgc_work_item['work_content']:
                    block1_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_ntgc_list.append(block1_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block1_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_ntgc_list': block1_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_fckj_confirmation_of_completion(self):
        """
            等待it_block2 方仓及框架安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_fckj_list = []
        """
            IT block2-方仓及框架安装（结构工程）
        """
        block2_fckj_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.1':
                block2_fckj_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_fckj_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.1.1' and row['serial_number'] <= '3.2.1.6':
                    block2_fckj_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_fckj_task['work_list'].append(block2_fckj_work_item)
        for item in wgqr_list:
            for block2_fckj_work_item in block2_fckj_task['work_list']:
                if item['fourthly_task_name'] == block2_fckj_work_item['work_content']:
                    block2_fckj_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_fckj_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_fckj_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_fckj_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_fckj_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_fckj_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_fckj_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_fckj_list.append(block2_fckj_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_fckj_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_fckj_list': block2_fckj_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_qdgc_confirmation_of_completion(self):
        """
            等待it_block2 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_qdgc_list = []

        """
            IT block2-电气工程（强电工程）
        """
        block2_qdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.2':
                block2_qdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_qdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.2.1' and row['serial_number'] <= '3.2.2.6':
                    block2_qdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_qdgc_task['work_list'].append(block2_qdgc_work_item)
        for item in wgqr_list:
            for block2_qdgc_work_item in block2_qdgc_task['work_list']:
                if item['fourthly_task_name'] == block2_qdgc_work_item['work_content']:
                    block2_qdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_qdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_qdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_qdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_qdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_qdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_qdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_qdgc_list.append(block2_qdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_qdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_qdgc_list': block2_qdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_rdgc_confirmation_of_completion(self):
        """
            等待it_block2 弱电系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_rdgc_list = []

        """
            IT block2-弱电系统（弱电工程）
        """
        block2_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.3':
                block2_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.3.1' and row['serial_number'] <= '3.2.3.10':
                    block2_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_rdgc_task['work_list'].append(block2_rdgc_work_item)
        for item in wgqr_list:
            for block2_rdgc_work_item in block2_rdgc_task['work_list']:
                if item['fourthly_task_name'] == block2_rdgc_work_item['work_content']:
                    block2_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_rdgc_list.append(block2_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_rdgc_list': block2_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_zxgc_confirmation_of_completion(self):
        """
            等待it_block2 装修工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_zxgc_list = []

        """
            IT block2-装修工程
        """
        block2_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.4':
                block2_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.4.1' and row['serial_number'] <= '3.2.4.8':
                    block2_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_zxgc_task['work_list'].append(block2_zxgc_work_item)
        for item in wgqr_list:
            for block2_zxgc_work_item in block2_zxgc_task['work_list']:
                if item['fourthly_task_name'] == block2_zxgc_work_item['work_content']:
                    block2_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_zxgc_list.append(block2_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_zxgc_list': block2_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_xfgc_confirmation_of_completion(self):
        """
            等待it_block2 消防工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_xfgc_list = []
        """
            IT block2-消防工程
        """
        block2_xfgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.5':
                block2_xfgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_xfgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.5.1' and row['serial_number'] <= '3.2.5.9':
                    block2_xfgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_xfgc_task['work_list'].append(block2_xfgc_work_item)
        for item in wgqr_list:
            for block2_xfgc_work_item in block2_xfgc_task['work_list']:
                if item['fourthly_task_name'] == block2_xfgc_work_item['work_content']:
                    block2_xfgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_xfgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_xfgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_xfgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_xfgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_xfgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_xfgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_xfgc_list.append(block2_xfgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_xfgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_xfgc_list': block2_xfgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_ntgc_confirmation_of_completion(self):
        """
            等待it_block2 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_ntgc_list = []

        """
            IT block2-暖通工程
        """
        block2_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.6':
                block2_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.6.1' and row['serial_number'] <= '3.2.6.4':
                    block2_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_ntgc_task['work_list'].append(block2_ntgc_work_item)
        for item in wgqr_list:
            for block2_ntgc_work_item in block2_ntgc_task['work_list']:
                if item['fourthly_task_name'] == block2_ntgc_work_item['work_content']:
                    block2_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_ntgc_list.append(block2_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block2_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_ntgc_list': block2_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_fckj_confirmation_of_completion(self):
        """
            等待it_block3 方仓及框架安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_fckj_list = []
        """
            IT block3-方仓及框架安装（结构工程）
        """
        block3_fckj_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.1':
                block3_fckj_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_fckj_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.1.1' and row['serial_number'] <= '3.3.1.6':
                    block3_fckj_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_fckj_task['work_list'].append(block3_fckj_work_item)
        for item in wgqr_list:
            for block3_fckj_work_item in block3_fckj_task['work_list']:
                if item['fourthly_task_name'] == block3_fckj_work_item['work_content']:
                    block3_fckj_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_fckj_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_fckj_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_fckj_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_fckj_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_fckj_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_fckj_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_fckj_list.append(block3_fckj_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_fckj_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_fckj_list': block3_fckj_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_qdgc_confirmation_of_completion(self):
        """
            等待it_block3 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_qdgc_list = []

        """
            IT block3-电气工程（强电工程）
        """
        block3_qdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.2':
                block3_qdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_qdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.2.1' and row['serial_number'] <= '3.3.2.6':
                    block3_qdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_qdgc_task['work_list'].append(block3_qdgc_work_item)
        for item in wgqr_list:
            for block3_qdgc_work_item in block3_qdgc_task['work_list']:
                if item['fourthly_task_name'] == block3_qdgc_work_item['work_content']:
                    block3_qdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_qdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_qdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_qdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_qdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_qdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_qdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_qdgc_list.append(block3_qdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_qdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_qdgc_list': block3_qdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_rdgc_confirmation_of_completion(self):
        """
            等待it_block3 弱电系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_rdgc_list = []

        """
            IT block3-弱电系统（弱电工程）
        """
        block3_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.3':
                block3_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.3.1' and row['serial_number'] <= '3.3.3.10':
                    block3_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_rdgc_task['work_list'].append(block3_rdgc_work_item)
        for item in wgqr_list:
            for block3_rdgc_work_item in block3_rdgc_task['work_list']:
                if item['fourthly_task_name'] == block3_rdgc_work_item['work_content']:
                    block3_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_rdgc_list.append(block3_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_rdgc_list': block3_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_zxgc_confirmation_of_completion(self):
        """
            等待it_block3 装修工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_zxgc_list = []

        """
            IT block3-装修工程
        """
        block3_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.4':
                block3_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.4.1' and row['serial_number'] <= '3.3.4.8':
                    block3_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_zxgc_task['work_list'].append(block3_zxgc_work_item)
        for item in wgqr_list:
            for block3_zxgc_work_item in block3_zxgc_task['work_list']:
                if item['fourthly_task_name'] == block3_zxgc_work_item['work_content']:
                    block3_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_zxgc_list.append(block3_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_zxgc_list': block3_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_xfgc_confirmation_of_completion(self):
        """
            等待it_block3 消防工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_xfgc_list = []
        """
            IT block3-消防工程
        """
        block3_xfgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.5':
                block3_xfgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_xfgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.5.1' and row['serial_number'] <= '3.3.5.9':
                    block3_xfgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_xfgc_task['work_list'].append(block3_xfgc_work_item)
        for item in wgqr_list:
            for block3_xfgc_work_item in block3_xfgc_task['work_list']:
                if item['fourthly_task_name'] == block3_xfgc_work_item['work_content']:
                    block3_xfgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_xfgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_xfgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_xfgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_xfgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_xfgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_xfgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_xfgc_list.append(block3_xfgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_xfgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_xfgc_list': block3_xfgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_ntgc_confirmation_of_completion(self):
        """
            等待it_block3 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_ntgc_list = []

        """
            IT block3-暖通工程
        """
        block3_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.6':
                block3_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.6.1' and row['serial_number'] <= '3.3.6.4':
                    block3_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_ntgc_task['work_list'].append(block3_ntgc_work_item)
        for item in wgqr_list:
            for block3_ntgc_work_item in block3_ntgc_task['work_list']:
                if item['fourthly_task_name'] == block3_ntgc_work_item['work_content']:
                    block3_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_ntgc_list.append(block3_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block3_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_ntgc_list': block3_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_fckj_confirmation_of_completion(self):
        """
            等待it_block4 方仓及框架安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_fckj_list = []
        """
            IT block4-方仓及框架安装（结构工程）
        """
        block4_fckj_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.1':
                block4_fckj_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_fckj_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.1.1' and row['serial_number'] <= '3.4.1.6':
                    block4_fckj_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_fckj_task['work_list'].append(block4_fckj_work_item)
        for item in wgqr_list:
            for block4_fckj_work_item in block4_fckj_task['work_list']:
                if item['fourthly_task_name'] == block4_fckj_work_item['work_content']:
                    block4_fckj_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_fckj_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_fckj_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_fckj_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_fckj_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_fckj_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_fckj_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_fckj_list.append(block4_fckj_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_fckj_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_fckj_list': block4_fckj_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_qdgc_confirmation_of_completion(self):
        """
            等待it_block4 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_qdgc_list = []

        """
            IT block4-电气工程（强电工程）
        """
        block4_qdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.2':
                block4_qdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_qdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.2.1' and row['serial_number'] <= '3.4.2.6':
                    block4_qdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_qdgc_task['work_list'].append(block4_qdgc_work_item)
        for item in wgqr_list:
            for block4_qdgc_work_item in block4_qdgc_task['work_list']:
                if item['fourthly_task_name'] == block4_qdgc_work_item['work_content']:
                    block4_qdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_qdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_qdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_qdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_qdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_qdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_qdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_qdgc_list.append(block4_qdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_qdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_qdgc_list': block4_qdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_rdgc_confirmation_of_completion(self):
        """
            等待it_block4 弱电系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_rdgc_list = []

        """
            IT block4-弱电系统（弱电工程）
        """
        block4_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.3':
                block4_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.3.1' and row['serial_number'] <= '3.4.3.10':
                    block4_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_rdgc_task['work_list'].append(block4_rdgc_work_item)
        for item in wgqr_list:
            for block4_rdgc_work_item in block4_rdgc_task['work_list']:
                if item['fourthly_task_name'] == block4_rdgc_work_item['work_content']:
                    block4_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_rdgc_list.append(block4_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_rdgc_list': block4_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_zxgc_confirmation_of_completion(self):
        """
            等待it_block4 装修工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_zxgc_list = []

        """
            IT block4-装修工程
        """
        block4_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.4':
                block4_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.4.1' and row['serial_number'] <= '3.4.4.8':
                    block4_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_zxgc_task['work_list'].append(block4_zxgc_work_item)
        for item in wgqr_list:
            for block4_zxgc_work_item in block4_zxgc_task['work_list']:
                if item['fourthly_task_name'] == block4_zxgc_work_item['work_content']:
                    block4_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_zxgc_list.append(block4_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_zxgc_list': block4_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_xfgc_confirmation_of_completion(self):
        """
            等待it_block4 消防工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_xfgc_list = []
        """
            IT block4-消防工程
        """
        block4_xfgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.5':
                block4_xfgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_xfgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.5.1' and row['serial_number'] <= '3.4.5.9':
                    block4_xfgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_xfgc_task['work_list'].append(block4_xfgc_work_item)
        for item in wgqr_list:
            for block4_xfgc_work_item in block4_xfgc_task['work_list']:
                if item['fourthly_task_name'] == block4_xfgc_work_item['work_content']:
                    block4_xfgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_xfgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_xfgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_xfgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_xfgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_xfgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_xfgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_xfgc_list.append(block4_xfgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_xfgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_xfgc_list': block4_xfgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_ntgc_confirmation_of_completion(self):
        """
            等待it_block4 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_ntgc_list = []

        """
            IT block4-暖通工程
        """
        block4_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.6':
                block4_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.6.1' and row['serial_number'] <= '3.4.6.4':
                    block4_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_ntgc_task['work_list'].append(block4_ntgc_work_item)
        for item in wgqr_list:
            for block4_ntgc_work_item in block4_ntgc_task['work_list']:
                if item['fourthly_task_name'] == block4_ntgc_work_item['work_content']:
                    block4_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_ntgc_list.append(block4_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if block4_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_ntgc_list': block4_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_dlzl_dlfc_confirmation_of_completion(self):
        """
            等待电力走廊 电力方仓安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        dlzl_dlfc_list = []
        """
            电力走廊-电力方仓安装
        """
        dlzl_dlfc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.5.1':
                dlzl_dlfc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if dlzl_dlfc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.5.1.1' and row['serial_number'] <= '3.5.1.9':
                    dlzl_dlfc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    dlzl_dlfc_task['work_list'].append(dlzl_dlfc_work_item)
        for item in wgqr_list:
            for dlzl_dlfc_work_item in dlzl_dlfc_task['work_list']:
                if item['fourthly_task_name'] == dlzl_dlfc_work_item['work_content']:
                    dlzl_dlfc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    dlzl_dlfc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    dlzl_dlfc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == dlzl_dlfc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    dlzl_dlfc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    dlzl_dlfc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    dlzl_dlfc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            dlzl_dlfc_list.append(dlzl_dlfc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if dlzl_dlfc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'dlzl_dlfc_list': dlzl_dlfc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_dlzl_bfxt_confirmation_of_completion(self):
        """
            等待电力走廊 补/排风系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        dlzl_bfxt_list = []

        """
            电力走廊-补/排风系统
        """
        dlzl_bfxt_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.5.2':
                dlzl_bfxt_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if dlzl_bfxt_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.5.2.1' and row['serial_number'] <= '3.5.2.6':
                    dlzl_bfxt_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    dlzl_bfxt_task['work_list'].append(dlzl_bfxt_work_item)
        for item in wgqr_list:
            for dlzl_bfxt_work_item in dlzl_bfxt_task['work_list']:
                if item['fourthly_task_name'] == dlzl_bfxt_work_item['work_content']:
                    dlzl_bfxt_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    dlzl_bfxt_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    dlzl_bfxt_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == dlzl_bfxt_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    dlzl_bfxt_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    dlzl_bfxt_task['tertiary_complete_report'] = tertiary_complete_report_list
                    dlzl_bfxt_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            dlzl_bfxt_list.append(dlzl_bfxt_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if dlzl_bfxt_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'dlzl_bfxt_list': dlzl_bfxt_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_dlzl_wsd_confirmation_of_completion(self):
        """
            等待电力走廊 外市电工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        dlzl_wsd_list = []
        """
            电力走廊-外市电工程(红线内)
        """
        dlzl_wsd_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.5.3':
                dlzl_wsd_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if dlzl_wsd_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.5.3.1' and row['serial_number'] <= '3.5.3.3':
                    dlzl_wsd_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    dlzl_wsd_task['work_list'].append(dlzl_wsd_work_item)
        for item in wgqr_list:
            for dlzl_wsd_work_item in dlzl_wsd_task['work_list']:
                if item['fourthly_task_name'] == dlzl_wsd_work_item['work_content']:
                    dlzl_wsd_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    dlzl_wsd_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    dlzl_wsd_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == dlzl_wsd_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    dlzl_wsd_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    dlzl_wsd_task['tertiary_complete_report'] = tertiary_complete_report_list
                    dlzl_wsd_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            dlzl_wsd_list.append(dlzl_wsd_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if dlzl_wsd_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'dlzl_wsd_list': dlzl_wsd_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_cfscl_cffc_confirmation_of_completion(self):
        """
            等待柴发和水处理区域 柴发方仓安装 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        cfscl_cffc_list = []
        """
            柴发和水处理区域-柴发方仓安装
        """
        cfscl_cffc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.6.1':
                cfscl_cffc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if cfscl_cffc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.6.1.1' and row['serial_number'] <= '3.6.1.12':
                    cfscl_cffc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    cfscl_cffc_task['work_list'].append(cfscl_cffc_work_item)
        for item in wgqr_list:
            for cfscl_cffc_work_item in cfscl_cffc_task['work_list']:
                if item['fourthly_task_name'] == cfscl_cffc_work_item['work_content']:
                    cfscl_cffc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    cfscl_cffc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    cfscl_cffc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == cfscl_cffc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    cfscl_cffc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    cfscl_cffc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    cfscl_cffc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            cfscl_cffc_list.append(cfscl_cffc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if cfscl_cffc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'cfscl_cffc_list': cfscl_cffc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_cfscl_scl_confirmation_of_completion(self):
        """
            等待柴发和水处理区域 水处理 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        cfscl_scl_list = []

        """
            柴发和水处理区域-水处理
        """
        cfscl_scl_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.6.2':
                cfscl_scl_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if cfscl_scl_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.6.2.1' and row['serial_number'] <= '3.6.2.12':
                    cfscl_scl_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    cfscl_scl_task['work_list'].append(cfscl_scl_work_item)
        for item in wgqr_list:
            for cfscl_scl_work_item in cfscl_scl_task['work_list']:
                if item['fourthly_task_name'] == cfscl_scl_work_item['work_content']:
                    cfscl_scl_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    cfscl_scl_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    cfscl_scl_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == cfscl_scl_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    cfscl_scl_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    cfscl_scl_task['tertiary_complete_report'] = tertiary_complete_report_list
                    cfscl_scl_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            cfscl_scl_list.append(cfscl_scl_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if cfscl_scl_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'cfscl_scl_list': cfscl_scl_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_cfscl_cfly_confirmation_of_completion(self):
        """
            等待柴发和水处理区域 柴发油路系统 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        cfscl_cfly_list = []
        """
            柴发和水处理区域-柴发油路系统
        """
        cfscl_cfly_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.6.3':
                cfscl_cfly_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if cfscl_cfly_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.6.3.1' and row['serial_number'] <= '3.6.3.9':
                    cfscl_cfly_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    cfscl_cfly_task['work_list'].append(cfscl_cfly_work_item)
        for item in wgqr_list:
            for cfscl_cfly_work_item in cfscl_cfly_task['work_list']:
                if item['fourthly_task_name'] == cfscl_cfly_work_item['work_content']:
                    cfscl_cfly_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    cfscl_cfly_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    cfscl_cfly_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == cfscl_cfly_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    cfscl_cfly_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    cfscl_cfly_task['tertiary_complete_report'] = tertiary_complete_report_list
                    cfscl_cfly_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            cfscl_cfly_list.append(cfscl_cfly_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if cfscl_cfly_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'cfscl_cfly_list': cfscl_cfly_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_zxgc_confirmation_of_completion(self):
        """
            等待AHU区域 装修 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        ahu_zxgc_list = []
        """
            AHU区域-装修(结构工程)
        """
        ahu_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.7.1':
                ahu_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if ahu_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.7.1.1' and row['serial_number'] <= '3.7.1.5':
                    ahu_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    ahu_zxgc_task['work_list'].append(ahu_zxgc_work_item)
        for item in wgqr_list:
            for ahu_zxgc_work_item in ahu_zxgc_task['work_list']:
                if item['fourthly_task_name'] == ahu_zxgc_work_item['work_content']:
                    ahu_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    ahu_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    ahu_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == ahu_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    ahu_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    ahu_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    ahu_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            ahu_zxgc_list.append(ahu_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if ahu_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'ahu_zxgc_list': ahu_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_ntgc_confirmation_of_completion(self):
        """
            等待AHU区域 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        ahu_ntgc_list = []

        """
            AHU区域-暖通工程
        """
        ahu_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.7.2':
                ahu_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if ahu_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.7.2.1' and row['serial_number'] <= '3.7.2.8':
                    ahu_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    ahu_ntgc_task['work_list'].append(ahu_ntgc_work_item)
        for item in wgqr_list:
            for ahu_ntgc_work_item in ahu_ntgc_task['work_list']:
                if item['fourthly_task_name'] == ahu_ntgc_work_item['work_content']:
                    ahu_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    ahu_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    ahu_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == ahu_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    ahu_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    ahu_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    ahu_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            ahu_ntgc_list.append(ahu_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if ahu_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'ahu_ntgc_list': ahu_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_dqgc_confirmation_of_completion(self):
        """
            等待AHU区域 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        ahu_dqgc_list = []
        """
            AHU区域-电气工程
        """
        ahu_dqgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.7.3':
                ahu_dqgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if ahu_dqgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.7.3.1' and row['serial_number'] <= '3.7.3.3':
                    ahu_dqgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    ahu_dqgc_task['work_list'].append(ahu_dqgc_work_item)
        for item in wgqr_list:
            for ahu_dqgc_work_item in ahu_dqgc_task['work_list']:
                if item['fourthly_task_name'] == ahu_dqgc_work_item['work_content']:
                    ahu_dqgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    ahu_dqgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    ahu_dqgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == ahu_dqgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    ahu_dqgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    ahu_dqgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    ahu_dqgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            ahu_dqgc_list.append(ahu_dqgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if ahu_dqgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'ahu_dqgc_list': ahu_dqgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_rdgc_confirmation_of_completion(self):
        """
            等待AHU区域分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        ahu_rdgc_list = []

        """
            AHU区域-弱电工程
        """
        ahu_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.7.4':
                ahu_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if ahu_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.7.4.1' and row['serial_number'] <= '3.7.4.1':
                    ahu_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    ahu_rdgc_task['work_list'].append(ahu_rdgc_work_item)
        for item in wgqr_list:
            for ahu_rdgc_work_item in ahu_rdgc_task['work_list']:
                if item['fourthly_task_name'] == ahu_rdgc_work_item['work_content']:
                    ahu_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    ahu_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    ahu_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == ahu_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    ahu_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    ahu_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    ahu_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            ahu_rdgc_list.append(ahu_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if ahu_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'ahu_rdgc_list': ahu_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_zxgc_confirmation_of_completion(self):
        """
            等待办公区域分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_zxgc_list = []
        """
            办公区域-装修(结构工程)
        """
        bg_zxgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.1':
                bg_zxgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_zxgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.1.1' and row['serial_number'] <= '3.8.1.2':
                    bg_zxgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_zxgc_task['work_list'].append(bg_zxgc_work_item)
        for item in wgqr_list:
            for bg_zxgc_work_item in bg_zxgc_task['work_list']:
                if item['fourthly_task_name'] == bg_zxgc_work_item['work_content']:
                    bg_zxgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_zxgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_zxgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_zxgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_zxgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_zxgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_zxgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_zxgc_list.append(bg_zxgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if bg_zxgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_zxgc_list': bg_zxgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_ntgc_confirmation_of_completion(self):
        """
            等待办公区域 暖通工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_ntgc_list = []

        """
            办公区域-暖通工程
        """
        bg_ntgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.2':
                bg_ntgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_ntgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.2.1' and row['serial_number'] <= '3.8.2.3':
                    bg_ntgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_ntgc_task['work_list'].append(bg_ntgc_work_item)
        for item in wgqr_list:
            for bg_ntgc_work_item in bg_ntgc_task['work_list']:
                if item['fourthly_task_name'] == bg_ntgc_work_item['work_content']:
                    bg_ntgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_ntgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_ntgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_ntgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_ntgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_ntgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_ntgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_ntgc_list.append(bg_ntgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if bg_ntgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_ntgc_list': bg_ntgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_dqgc_confirmation_of_completion(self):
        """
            等待办公区域 电气工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_dqgc_list = []
        """
            办公区域-电气工程
        """
        bg_dqgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.3':
                bg_dqgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_dqgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.3.1' and row['serial_number'] <= '3.8.3.2':
                    bg_dqgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_dqgc_task['work_list'].append(bg_dqgc_work_item)
        for item in wgqr_list:
            for bg_dqgc_work_item in bg_dqgc_task['work_list']:
                if item['fourthly_task_name'] == bg_dqgc_work_item['work_content']:
                    bg_dqgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_dqgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_dqgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_dqgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_dqgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_dqgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_dqgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_dqgc_list.append(bg_dqgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if bg_dqgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_dqgc_list': bg_dqgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_rdgc_confirmation_of_completion(self):
        """
            等待办公区域 弱电工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_rdgc_list = []

        """
            办公区域-弱电工程
        """
        bg_rdgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.4':
                bg_rdgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_rdgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.4.1' and row['serial_number'] <= '3.8.4.1':
                    bg_rdgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_rdgc_task['work_list'].append(bg_rdgc_work_item)
        for item in wgqr_list:
            for bg_rdgc_work_item in bg_rdgc_task['work_list']:
                if item['fourthly_task_name'] == bg_rdgc_work_item['work_content']:
                    bg_rdgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_rdgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_rdgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_rdgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_rdgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_rdgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_rdgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_rdgc_list.append(bg_rdgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if bg_rdgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_rdgc_list': bg_rdgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_xfgc_confirmation_of_completion(self):
        """
            等待办公区域 消防工程 分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_xfgc_list = []

        """
            办公区域-消防工程
        """
        bg_xfgc_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.5':
                bg_xfgc_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_xfgc_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.5.1' and row['serial_number'] <= '3.8.5.2':
                    bg_xfgc_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_xfgc_task['work_list'].append(bg_xfgc_work_item)
        for item in wgqr_list:
            for bg_xfgc_work_item in bg_xfgc_task['work_list']:
                if item['fourthly_task_name'] == bg_xfgc_work_item['work_content']:
                    bg_xfgc_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_xfgc_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_xfgc_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_xfgc_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_xfgc_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_xfgc_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_xfgc_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_xfgc_list.append(bg_xfgc_task)

        self.workflow_detail.add_kv_table('完工确认数据', {'message': zkjh_list})
        if bg_xfgc_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_xfgc_list': bg_xfgc_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    """
        等待所有设备上电调试
    """

    def wait_it_block1_sdts_confirmation_of_completion(self):
        """
            等待IT-Block1-设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block1_sdts_list = []

        """
            IT-Block1-设备上电调试
        """
        block1_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.1.7':
                block1_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block1_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.1.7.1' and row['serial_number'] <= '3.1.7.3':
                    block1_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block1_sdts_task['work_list'].append(block1_sdts_work_item)
        for item in wgqr_list:
            for block1_sdts_work_item in block1_sdts_task['work_list']:
                if item['fourthly_task_name'] == block1_sdts_work_item['work_content']:
                    block1_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block1_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block1_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block1_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block1_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block1_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block1_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block1_sdts_list.append(block1_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if block1_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block1_sdts_list': block1_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block2_sdts_confirmation_of_completion(self):
        """
            等待IT-Block2-设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block2_sdts_list = []

        """
            IT-Block2-设备上电调试
        """
        block2_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.2.7':
                block2_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block2_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.2.7.1' and row['serial_number'] <= '3.2.7.3':
                    block2_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block2_sdts_task['work_list'].append(block2_sdts_work_item)
        for item in wgqr_list:
            for block2_sdts_work_item in block2_sdts_task['work_list']:
                if item['fourthly_task_name'] == block2_sdts_work_item['work_content']:
                    block2_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block2_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block2_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block2_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block2_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block2_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block2_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block2_sdts_list.append(block2_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if block2_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block2_sdts_list': block2_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block3_sdts_confirmation_of_completion(self):
        """
            等待IT-Block3-设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block3_sdts_list = []
        """
            IT-Block3-设备上电调试
        """
        block3_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.3.7':
                block3_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block3_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.3.7.1' and row['serial_number'] <= '3.3.7.3':
                    block3_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block3_sdts_task['work_list'].append(block3_sdts_work_item)
        for item in wgqr_list:
            for block3_sdts_work_item in block3_sdts_task['work_list']:
                if item['fourthly_task_name'] == block3_sdts_work_item['work_content']:
                    block3_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block3_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block3_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block3_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block3_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block3_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block3_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block3_sdts_list.append(block3_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if block3_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block3_sdts_list': block3_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_it_block4_sdts_confirmation_of_completion(self):
        """
            等待所有设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        block4_sdts_list = []

        """
            IT-Block4-设备上电调试
        """
        block4_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.4.7':
                block4_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if block4_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.4.7.1' and row['serial_number'] <= '3.4.7.3':
                    block4_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    block4_sdts_task['work_list'].append(block4_sdts_work_item)
        for item in wgqr_list:
            for block4_sdts_work_item in block4_sdts_task['work_list']:
                if item['fourthly_task_name'] == block4_sdts_work_item['work_content']:
                    block4_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    block4_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    block4_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == block4_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    block4_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    block4_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    block4_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            block4_sdts_list.append(block4_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if block4_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'block4_sdts_list': block4_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_dlzl_sdts_confirmation_of_completion(self):
        """
            等待所有设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        dlzl_sdts_list = []

        """
            电力走廊-设备上电调试
        """
        dlzl_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.5.4':
                dlzl_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if dlzl_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.5.4.1' and row['serial_number'] <= '3.5.4.3':
                    dlzl_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    dlzl_sdts_task['work_list'].append(dlzl_sdts_work_item)
        for item in wgqr_list:
            for dlzl_sdts_work_item in dlzl_sdts_task['work_list']:
                if item['fourthly_task_name'] == dlzl_sdts_work_item['work_content']:
                    dlzl_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    dlzl_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    dlzl_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == dlzl_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    dlzl_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    dlzl_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    dlzl_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            dlzl_sdts_list.append(dlzl_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if dlzl_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'dlzl_sdts_list': dlzl_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_cfscl_sdts_confirmation_of_completion(self):
        """
            等待所有设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        cfscl_sdts_list = []

        """
            柴发和水处理区域-设备上电调试
        """
        cfscl_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.6.4':
                cfscl_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if cfscl_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.6.4.1' and row['serial_number'] <= '3.6.4.3':
                    cfscl_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    cfscl_sdts_task['work_list'].append(cfscl_sdts_work_item)
        for item in wgqr_list:
            for cfscl_sdts_work_item in cfscl_sdts_task['work_list']:
                if item['fourthly_task_name'] == cfscl_sdts_work_item['work_content']:
                    cfscl_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    cfscl_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    cfscl_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == cfscl_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    cfscl_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    cfscl_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    cfscl_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            cfscl_sdts_list.append(cfscl_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if cfscl_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'cfscl_sdts_list': cfscl_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ahu_sdts_confirmation_of_completion(self):
        """
            等待AHU区域设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")

        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'"

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'"

        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)

        ahu_sdts_list = []

        # AHU区域-设备上电调试
        ahu_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.7.5':
                ahu_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if ahu_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.7.5.1' and row['serial_number'] <= '3.7.5.3':
                    ahu_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    ahu_sdts_task['work_list'].append(ahu_sdts_work_item)

            for item in wgqr_list:
                for ahu_sdts_work_item in ahu_sdts_task['work_list']:
                    if item['fourthly_task_name'] == ahu_sdts_work_item['work_content']:
                        ahu_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                        ahu_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                        ahu_sdts_work_item['remark'] = item['fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                if wgbg_item['tertiary_serial_number'] == ahu_sdts_task['serial_number']:
                    ahu_sdts_task['tertiary_complete_photo'] = ast.literal_eval(
                        wgbg_item.get('tertiary_complete_photo', '[]'))
                    ahu_sdts_task['tertiary_complete_report'] = ast.literal_eval(
                        wgbg_item.get('tertiary_complete_report', '[]'))
                    ahu_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']

            ahu_sdts_list.append(ahu_sdts_task)

        self.workflow_detail.add_kv_table('AHU设备上电调试完工数据', {'message': zkjh_list})
        if ahu_sdts_list:
            variables = {
                'ahu_sdts_list': ahu_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_bgqy_sdts_confirmation_of_completion(self):
        """
            等待所有设备上电调试分项施工完工确认
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        zkjh_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"

        wgqr_sql = "SELECT tertiary_serial_number, tertiary_task_name, fourthly_serial_number, fourthly_task_name, " \
                   "fourthly_actual_start_time, fourthly_actual_finish_time, fourthly_schedule_skewing_explain, " \
                   "tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_data " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "

        wgbg_sql = "SELECT tertiary_complete_report, tertiary_complete_photo, tertiary_remark " \
                   "FROM installation_construction_document_completion " \
                   f"WHERE ticket_id = '{ticket_id}'" \
                   f"AND project_name = '{project_name}' "
        zkjh_list = db.get_all(zkjh_sql)
        wgqr_list = db.get_all(wgqr_sql)
        wgbg_list = db.get_all(wgbg_sql)
        bg_sdts_list = []

        """
            办公区域-设备上电调试
        """
        bg_sdts_task = None
        for row in zkjh_list:
            if row['serial_number'] == '3.8.6':
                bg_sdts_task = {
                    'serial_number': row['serial_number'],
                    'work_content': row['work_content'],
                    'start_time': row['start_time'],
                    'finish_time': row['completion_time'],
                    'work_list': []
                }
                break

        if bg_sdts_task:
            for row in zkjh_list:
                if row['serial_number'] >= '3.8.6.1' and row['serial_number'] <= '3.8.6.3':
                    bg_sdts_work_item = {
                        'serial_number': row['serial_number'],
                        'work_content': row['work_content'],
                        'start_time': row['start_time'],
                        'finish_time': row['completion_time']
                    }
                    bg_sdts_task['work_list'].append(bg_sdts_work_item)
        for item in wgqr_list:
            for bg_sdts_work_item in bg_sdts_task['work_list']:
                if item['fourthly_task_name'] == bg_sdts_work_item['work_content']:
                    bg_sdts_work_item['actual_start_time'] = item['fourthly_actual_start_time']
                    bg_sdts_work_item['actual_finish_time'] = item['fourthly_actual_finish_time']
                    bg_sdts_work_item['remark'] = item[
                        'fourthly_schedule_skewing_explain']

            for wgbg_item in wgbg_list:
                tertiary_complete_photo_list = []
                tertiary_complete_report_list = []
                if wgbg_item['tertiary_serial_number'] == bg_sdts_task['serial_number']:
                    tertiary_complete_photo_list.clear()
                    tertiary_complete_report_list.clear()

                    tertiary_complete_photo = wgbg_item.get('tertiary_complete_photo')
                    tertiary_complete_report = wgbg_item.get('tertiary_complete_report')
                    # 尝试将 tertiary_complete_photo 字符串解析为列表
                    try:
                        tertiary_complete_photos = ast.literal_eval(tertiary_complete_photo)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_photos = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_photos 是一个列表
                    if isinstance(tertiary_complete_photos, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_photos:
                            tertiary_complete_photo_list.append({"tertiary_complete_photo": url})
                    # 尝试将 tertiary_complete_report 字符串解析为列表
                    try:
                        tertiary_complete_reports = ast.literal_eval(tertiary_complete_report)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        tertiary_complete_reports = []  # 如果解析失败，设置为空列表
                    # 确保 tertiary_complete_reports 是一个列表
                    if isinstance(tertiary_complete_reports, list):
                        # 为每个 URL 创建一个新的字典
                        for url in tertiary_complete_reports:
                            tertiary_complete_report_list.append({"tertiary_complete_report": url})
                    bg_sdts_task['tertiary_complete_photo'] = tertiary_complete_photo_list
                    bg_sdts_task['tertiary_complete_report'] = tertiary_complete_report_list
                    bg_sdts_task['tertiary_remark'] = wgbg_item['tertiary_remark']
            bg_sdts_list.append(bg_sdts_task)

        self.workflow_detail.add_kv_table('设备上电调试完工数据', {'message': zkjh_list})
        if bg_sdts_list:
            variables = {
                'zkjh_list': zkjh_list,
                'wgqr_list': wgqr_list,
                'bg_sdts_list': bg_sdts_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    """
        等待检验、送电报告上传
    """

    def wait_inspection_report_upload(self):
        """
            等待检验报告上传
        """
        report_file_list = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT report_tale, report_file, remark " \
              "FROM installation_construction_report " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND report_tale = '检验' "
        result_list = db.get_all(sql)
        for row in result_list:
            report_file = row.get('report_file', '')
            # 尝试将 report_file 字符串解析为列表
            try:
                report_files = ast.literal_eval(report_file)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                report_files = []  # 如果解析失败，设置为空列表
            # 确保 report_files 是一个列表
            if isinstance(report_files, list):
                # 为每个 URL 创建一个新的字典
                for url in report_files:
                    report_file_list.append({"report_file": url})
            remark = row.get('remark', '')
        self.workflow_detail.add_kv_table('检验报告信息', {'message': result_list})
        if result_list:
            variables = {
                "jy_report_file_list": report_file_list,
                "jy_remark": remark
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_send_electricity_report_upload(self):
        """
            等待送电报告上传
        """
        report_file_list = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT report_tale, report_file, remark " \
              "FROM installation_construction_report " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND report_tale = '送电' "
        result_list = db.get_all(sql)
        for row in result_list:
            report_file = row.get('report_file', '')
            # 尝试将 report_file 字符串解析为列表
            try:
                report_files = ast.literal_eval(report_file)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                report_files = []  # 如果解析失败，设置为空列表
            # 确保 report_files 是一个列表
            if isinstance(report_files, list):
                # 为每个 URL 创建一个新的字典
                for url in report_files:
                    report_file_list.append({"report_file": url})
            remark = row.get('remark', '')
        self.workflow_detail.add_kv_table('送电报告信息', {'message': result_list})
        if result_list:
            variables = {
                "sd_report_file_list": report_file_list,
                "sd_remark": remark
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceAzsg(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceAzsg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def level_four_item_plan_submission(self, TencentTicketId, ProjectName, ProjectScheduling, Remark):
        """
            4级分项计划提交
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        insert_data = {
            "ticket_id": TencentTicketId,
            "project_name": ProjectName,
            "project_scheduling": str(ProjectScheduling),
            "remark": Remark,
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert("installation_construction_itemized_plan", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def construction_plan_upload(self, TencentTicketId, ProjectName, SecondaryNumbering, SecondaryName,
                                 ConstructionScheme, Remark):
        """
            施工方案上传
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号 或 项目名称 缺失'}
        # 校验 SecondaryNumbering 和 SecondaryName
        if not SecondaryNumbering or not SecondaryName:
            return {'code': -1, 'msg': '二级分项编号 或 二级分项名称 缺失'}

        insert_data = {
            "ticket_id": TencentTicketId,
            "project_name": ProjectName,
            "second_serial_number": SecondaryNumbering,
            "second_task_name": SecondaryName,
            "second_construction_scheme": str(ConstructionScheme),
            "second_remark": Remark
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert("installation_construction_construction_scheme", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def confirmation_of_completion(self, TencentTicketId, ProjectName, TertiaryNumbering, TertiaryName,
                                   LevelFourCompletionList, CompleteReport, CompletePhoto, Remark):
        """
            分项施工完工确认
        """
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号 或 项目名称 缺失'}
        # 校验 SecondaryNumbering 和 SecondaryName
        if not TertiaryNumbering or not TertiaryName:
            return {'code': -1, 'msg': '三级分项编号 或 三级分项名称 缺失'}

        if not Remark:
            Remark = '无'
        insert_data = []
        document_completion_data = []
        for row in LevelFourCompletionList:
            fourthly_numbering = row.get('FourthlyNumbering')
            fourthly_name = row.get('FourthlyName')
            start_time = row.get('StartTime')
            finish_time = row.get('FinishTime')
            schedule_skewing_explain = row.get('ScheduleSkewingExplain')

            insert_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'tertiary_serial_number': TertiaryNumbering,
                'tertiary_task_name': TertiaryName,
                'fourthly_serial_number': fourthly_numbering,
                'fourthly_task_name': fourthly_name,
                'fourthly_actual_start_time': start_time,
                'fourthly_actual_finish_time': finish_time,
                'fourthly_schedule_skewing_explain': schedule_skewing_explain
            })

            document_completion_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'tertiary_serial_number': TertiaryNumbering,
                'tertiary_task_name': TertiaryName,
                'tertiary_complete_report': str(CompleteReport),
                'tertiary_complete_photo': str(CompletePhoto),
                'tertiary_remark': Remark
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert_batch("installation_construction_data", insert_data)
            tb_db.insert_batch("installation_construction_document_completion", document_completion_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def report_upload(self, TencentTicketId, ProjectName, ReportTale, ReportFile, Remark):
        """
            报告上传
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号 或 项目名称 缺失'}
        # 校验 ReportTale
        if not ReportTale:
            return {'code': -1, 'msg': '报告类型 缺失'}

        insert_data = {
            'ticket_id': TencentTicketId,
            'project_name': ProjectName,
            'report_tale': ReportTale,
            'report_file': str(ReportFile),
            'remark': Remark
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert("installation_construction_report", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class ProjectPlanSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-项目计划-监理审批
    """

    def __init__(self):
        super(ProjectPlanSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        project_plan_supervision_approval = process_data.get('project_plan_supervision_approval')
        project_plan_supervision_remark = process_data.get('project_plan_supervision_remark')

        if project_plan_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_itemized_plan " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'project_plan_supervision_approval': project_plan_supervision_approval,
            'project_plan_supervision_remark': project_plan_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


"""
    专项施工方案审批
"""


class ItBloack1SupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-施工方案监理审批
    """

    def __init__(self):
        super(ItBloack1SupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block1_supervision_approval = process_data.get('it_block1_supervision_approval')
        it_block1_supervision_remark = process_data.get('it_block1_supervision_remark')

        if it_block1_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = 'IT Block1/电池间'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_supervision_approval': it_block1_supervision_approval,
            'it_block1_supervision_remark': it_block1_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2SupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-施工方案监理审批
    """

    def __init__(self):
        super(ItBloack2SupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_supervision_approval = process_data.get('it_block2_supervision_approval')
        it_block2_supervision_remark = process_data.get('it_block2_supervision_remark')

        if it_block2_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = 'IT Block2/电池间'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_supervision_approval': it_block2_supervision_approval,
            'it_block2_supervision_remark': it_block2_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3SupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-施工方案监理审批
    """

    def __init__(self):
        super(ItBloack3SupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_supervision_approval = process_data.get('it_block3_supervision_approval')
        it_block3_supervision_remark = process_data.get('it_block3_supervision_remark')

        if it_block3_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = 'IT Block3/电池间'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_supervision_approval': it_block3_supervision_approval,
            'it_block3_supervision_remark': it_block3_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4SupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-施工方案监理审批
    """

    def __init__(self):
        super(ItBloack4SupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_supervision_approval = process_data.get('it_block4_supervision_approval')
        it_block4_supervision_remark = process_data.get('it_block4_supervision_remark')

        if it_block4_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = 'IT Block4/电池间'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_supervision_approval': it_block4_supervision_approval,
            'it_block4_supervision_remark': it_block4_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-电力走廊-施工方案监理审批
    """

    def __init__(self):
        super(DlzlSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        dlzl_supervision_approval = process_data.get('dlzl_supervision_approval')
        dlzl_supervision_remark = process_data.get('dlzl_supervision_remark')

        if dlzl_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = '电力走廊'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'dlzl_supervision_approval': dlzl_supervision_approval,
            'dlzl_supervision_remark': dlzl_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-柴发方仓和水处理方仓-施工方案监理审批
    """

    def __init__(self):
        super(CfsclSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        cfscl_supervision_approval = process_data.get('cfscl_supervision_approval')
        cfscl_supervision_remark = process_data.get('cfscl_supervision_remark')

        if cfscl_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = '柴发和水处理区域'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'cfscl_supervision_approval': cfscl_supervision_approval,
            'cfscl_supervision_remark': cfscl_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU区域-施工方案监理审批
    """

    def __init__(self):
        super(AhuSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_supervision_approval = process_data.get('ahu_supervision_approval')
        ahu_supervision_remark = process_data.get('ahu_supervision_remark')

        if ahu_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = 'AHU区域'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_supervision_approval': ahu_supervision_approval,
            'ahu_supervision_remark': ahu_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqySupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-施工方案监理审批
    """

    def __init__(self):
        super(BgqySupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_supervision_approval = process_data.get('bgqy_supervision_approval')
        bgqy_supervision_remark = process_data.get('bgqy_supervision_remark')

        if bgqy_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_construction_scheme " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and second_task_name = '办公区域'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_supervision_approval': bgqy_supervision_approval,
            'bgqy_supervision_remark': bgqy_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


"""
    分项计划审批
"""


class ItBloack1FckjSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-方仓框架安装-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1FckjSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_qdgc_list = process_data.get('block1_qdgc_list')
        it_block1_fckj_supervision_approval = block1_qdgc_list.get('it_block1_fckj_supervision_approval')
        it_block1_fckj_supervision_remark = block1_qdgc_list.get('it_block1_fckj_supervision_remark')
        complete_report_review_report = block1_qdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_fckj_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='方仓及框架安装（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_fckj_supervision_approval': it_block1_fckj_supervision_approval,
            'it_block1_fckj_supervision_remark': it_block1_fckj_supervision_remark,
            'it_block1_fckj_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1DqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1DqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_dqgc_list = process_data.get('block1_dqgc_list')
        it_block1_dqgc_supervision_approval = block1_dqgc_list.get('it_block1_dqgc_supervision_approval')
        it_block1_dqgc_supervision_remark = block1_dqgc_list.get('it_block1_dqgc_supervision_remark')
        complete_report_review_report = block1_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='电气工程（强电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_dqgc_supervision_approval': it_block1_dqgc_supervision_approval,
            'it_block1_dqgc_supervision_remark': it_block1_dqgc_supervision_remark,
            'it_block1_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1RdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1RdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_rdgc_list = process_data.get('block1_rdgc_list')
        it_block1_rdgc_supervision_approval = block1_rdgc_list.get('it_block1_rdgc_supervision_approval')
        it_block1_rdgc_supervision_remark = block1_rdgc_list.get('it_block1_rdgc_supervision_remark')
        complete_report_review_report = block1_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='弱电系统（弱电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_rdgc_supervision_approval': it_block1_rdgc_supervision_approval,
            'it_block1_rdgc_supervision_remark': it_block1_rdgc_supervision_remark,
            'it_block1_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1ZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1ZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_zxgc_list = process_data.get('block1_zxgc_list')
        it_block1_zxgc_supervision_approval = block1_zxgc_list.get('it_block1_zxgc_supervision_approval')
        it_block1_zxgc_supervision_remark = block1_zxgc_list.get('it_block1_zxgc_supervision_remark')
        complete_report_review_report = block1_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='装修工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_zxgc_supervision_approval': it_block1_zxgc_supervision_approval,
            'it_block1_zxgc_supervision_remark': it_block1_zxgc_supervision_remark,
            'it_block1_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1XfgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-消防工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1XfgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_xfgc_list = process_data.get('block1_xfgc_list')
        it_block1_xfgc_supervision_approval = block1_xfgc_list.get('it_block1_xfgc_supervision_approval')
        it_block1_xfgc_supervision_remark = block1_xfgc_list.get('it_block1_xfgc_supervision_remark')
        complete_report_review_report = block1_xfgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_xfgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='消防工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_xfgc_supervision_approval': it_block1_xfgc_supervision_approval,
            'it_block1_xfgc_supervision_remark': it_block1_xfgc_supervision_remark,
            'it_block1_xfgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1NtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1NtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_ntgc_list = process_data.get('block1_ntgc_list')
        it_block1_ntgc_supervision_approval = block1_ntgc_list.get('it_block1_ntgc_supervision_approval')
        it_block1_ntgc_supervision_remark = block1_ntgc_list.get('it_block1_ntgc_supervision_remark')
        complete_report_review_report = block1_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_ntgc_supervision_approval': it_block1_ntgc_supervision_approval,
            'it_block1_ntgc_supervision_remark': it_block1_ntgc_supervision_remark,
            'it_block1_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack1SdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block1-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack1SdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block1_sdts_list = process_data.get('block1_sdts_list')
        it_block1_sdts_supervision_approval = block1_sdts_list.get('it_block1_sdts_supervision_approval')
        it_block1_sdts_supervision_remark = block1_sdts_list.get('it_block1_sdtsc_supervision_remark')
        complete_report_review_report = block1_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block1_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.1.7'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block1_sdts_supervision_approval': it_block1_sdts_supervision_approval,
            'it_block1_sdts_supervision_remark': it_block1_sdts_supervision_remark,
            'it_block1_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2FckjSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-方仓框架安装-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2FckjSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_fckj_list = process_data.get('it_block2_fckj_list')
        it_block2_fckj_supervision_approval = it_block2_fckj_list.get('it_block2_fckj_supervision_approval')
        it_block2_fckj_supervision_remark = it_block2_fckj_list.get('it_block2_fckj_supervision_remark')
        complete_report_review_report = it_block2_fckj_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_fckj_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='方仓及框架安装（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_fckj_supervision_approval': it_block2_fckj_supervision_approval,
            'it_block2_fckj_supervision_remark': it_block2_fckj_supervision_remark,
            'it_block2_fckj_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2DqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2DqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_dqgc_list = process_data.get('it_block2_dqgc_list')
        it_block2_dqgc_supervision_approval = it_block2_dqgc_list.get('it_block2_dqgc_supervision_approval')
        it_block2_dqgc_supervision_remark = it_block2_dqgc_list.get('it_block2_dqgc_supervision_remark')
        complete_report_review_report = it_block2_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='电气工程（强电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_dqgc_supervision_approval': it_block2_dqgc_supervision_approval,
            'it_block2_dqgc_supervision_remark': it_block2_dqgc_supervision_remark,
            'it_block2_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2RdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2RdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_rdgc_list = process_data.get('it_block2_rdgc_list')
        it_block2_rdgc_supervision_approval = it_block2_rdgc_list.get('it_block2_rdgc_supervision_approval')
        it_block2_rdgc_supervision_remark = it_block2_rdgc_list.get('it_block2_rdgc_supervision_remark')
        complete_report_review_report = it_block2_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='弱电系统（弱电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_rdgc_supervision_approval': it_block2_rdgc_supervision_approval,
            'it_block2_rdgc_supervision_remark': it_block2_rdgc_supervision_remark,
            'it_block2_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2ZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2ZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_zxgc_list = process_data.get('it_block2_zxgc_list')
        it_block2_zxgc_supervision_approval = it_block2_zxgc_list.get('it_block2_zxgc_supervision_approval')
        it_block2_zxgc_supervision_remark = it_block2_zxgc_list.get('it_block2_zxgc_supervision_remark')
        complete_report_review_report = it_block2_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='装修工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_zxgc_supervision_approval': it_block2_zxgc_supervision_approval,
            'it_block2_zxgc_supervision_remark': it_block2_zxgc_supervision_remark,
            'it_block2_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2XfgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-消防工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2XfgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_xfgc_list = process_data.get('it_block2_xfgc_list')
        it_block2_xfgc_supervision_approval = it_block2_xfgc_list.get('it_block2_xfgc_supervision_approval')
        it_block2_xfgc_supervision_remark = it_block2_xfgc_list.get('it_block2_xfgc_supervision_remark')
        complete_report_review_report = it_block2_xfgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_xfgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='消防工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_xfgc_supervision_approval': it_block2_xfgc_supervision_approval,
            'it_block2_xfgc_supervision_remark': it_block2_xfgc_supervision_remark,
            'it_block2_xfgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2NtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2NtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block2_ntgc_list = process_data.get('it_block2_ntgc_list')
        it_block2_ntgc_supervision_approval = it_block2_ntgc_list.get('it_block2_ntgc_supervision_approval')
        it_block2_ntgc_supervision_remark = it_block2_ntgc_list.get('it_block2_ntgc_supervision_remark')
        complete_report_review_report = it_block2_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_ntgc_supervision_approval': it_block2_ntgc_supervision_approval,
            'it_block2_ntgc_supervision_remark': it_block2_ntgc_supervision_remark,
            'it_block2_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack2SdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block2-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack2SdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block2_sdts_list = process_data.get('block2_sdts_list')
        it_block2_sdts_supervision_approval = block2_sdts_list.get('it_block2_sdts_supervision_approval')
        it_block2_sdts_supervision_remark = block2_sdts_list.get('it_block2_sdtsc_supervision_remark')
        complete_report_review_report = block2_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block2_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.2.7'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block2_sdts_supervision_approval': it_block2_sdts_supervision_approval,
            'it_block2_sdts_supervision_remark': it_block2_sdts_supervision_remark,
            'it_block2_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3FckjSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-方仓框架安装-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3FckjSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_fckj_list = process_data.get('it_block3_fckj_list')
        it_block3_fckj_supervision_approval = it_block3_fckj_list.get('it_block3_fckj_supervision_approval')
        it_block3_fckj_supervision_remark = it_block3_fckj_list.get('it_block3_fckj_supervision_remark')
        complete_report_review_report = it_block3_fckj_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_fckj_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='方仓及框架安装（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_fckj_supervision_approval': it_block3_fckj_supervision_approval,
            'it_block3_fckj_supervision_remark': it_block3_fckj_supervision_remark,
            'it_block3_fckj_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3DqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3DqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_dqgc_list = process_data.get('it_block3_dqgc_list')
        it_block3_dqgc_supervision_approval = it_block3_dqgc_list.get('it_block3_dqgc_supervision_approval')
        it_block3_dqgc_supervision_remark = it_block3_dqgc_list.get('it_block3_dqgc_supervision_remark')
        complete_report_review_report = it_block3_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='电气工程（强电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_dqgc_supervision_approval': it_block3_dqgc_supervision_approval,
            'it_block3_dqgc_supervision_remark': it_block3_dqgc_supervision_remark,
            'it_block3_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3RdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3RdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_rdgc_list = process_data.get('it_block3_rdgc_list')
        it_block3_rdgc_supervision_approval = it_block3_rdgc_list.get('it_block3_rdgc_supervision_approval')
        it_block3_rdgc_supervision_remark = it_block3_rdgc_list.get('it_block3_rdgc_supervision_remark')
        complete_report_review_report = it_block3_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='弱电系统（弱电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_rdgc_supervision_approval': it_block3_rdgc_supervision_approval,
            'it_block3_rdgc_supervision_remark': it_block3_rdgc_supervision_remark,
            'it_block3_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3ZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3ZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_zxgc_list = process_data.get('it_block3_zxgc_list')
        it_block3_zxgc_supervision_approval = it_block3_zxgc_list.get('it_block3_zxgc_supervision_approval')
        it_block3_zxgc_supervision_remark = it_block3_zxgc_list.get('it_block3_zxgc_supervision_remark')
        complete_report_review_report = it_block3_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='装修工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_zxgc_supervision_approval': it_block3_zxgc_supervision_approval,
            'it_block3_zxgc_supervision_remark': it_block3_zxgc_supervision_remark,
            'it_block3_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3XfgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-消防工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3XfgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_xfgc_list = process_data.get('it_block3_xfgc_list')
        it_block3_xfgc_supervision_approval = it_block3_xfgc_list.get('it_block3_xfgc_supervision_approval')
        it_block3_xfgc_supervision_remark = it_block3_xfgc_list.get('it_block3_xfgc_supervision_remark')
        complete_report_review_report = it_block3_xfgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_xfgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='消防工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_xfgc_supervision_approval': it_block3_xfgc_supervision_approval,
            'it_block3_xfgc_supervision_remark': it_block3_xfgc_supervision_remark,
            'it_block3_xfgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3NtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3NtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block3_ntgc_list = process_data.get('it_block3_ntgc_list')
        it_block3_ntgc_supervision_approval = it_block3_ntgc_list.get('it_block3_ntgc_supervision_approval')
        it_block3_ntgc_supervision_remark = it_block3_ntgc_list.get('it_block3_ntgc_supervision_remark')
        complete_report_review_report = it_block3_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        variables = {
            'it_block3_ntgc_supervision_approval': it_block3_ntgc_supervision_approval,
            'it_block3_ntgc_supervision_remark': it_block3_ntgc_supervision_remark,
            'it_block3_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack3SdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block3-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack3SdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block3_sdts_list = process_data.get('block3_sdts_list')
        it_block3_sdts_supervision_approval = block3_sdts_list.get('it_block3_sdts_supervision_approval')
        it_block3_sdts_supervision_remark = block3_sdts_list.get('it_block3_sdtsc_supervision_remark')
        complete_report_review_report = block3_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block3_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.3.7'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block3_sdts_supervision_approval': it_block3_sdts_supervision_approval,
            'it_block3_sdts_supervision_remark': it_block3_sdts_supervision_remark,
            'it_block3_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4FckjSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-方仓框架安装-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4FckjSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_fckj_list = process_data.get('it_block4_fckj_list')
        it_block4_fckj_supervision_approval = it_block4_fckj_list.get('it_block4_fckj_supervision_approval')
        it_block4_fckj_supervision_remark = it_block4_fckj_list.get('it_block4_fckj_supervision_remark')
        complete_report_review_report = it_block4_fckj_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_fckj_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='方仓及框架安装（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_fckj_supervision_approval': it_block4_fckj_supervision_approval,
            'it_block4_fckj_supervision_remark': it_block4_fckj_supervision_remark,
            'it_block4_fckj_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4DqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4DqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_dqgc_list = process_data.get('it_block4_dqgc_list')
        it_block4_dqgc_supervision_approval = it_block4_dqgc_list.get('it_block4_dqgc_supervision_approval')
        it_block4_dqgc_supervision_remark = it_block4_dqgc_list.get('it_block4_dqgc_supervision_remark')
        complete_report_review_report = it_block4_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='电气工程（强电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_dqgc_supervision_approval': it_block4_dqgc_supervision_approval,
            'it_block4_dqgc_supervision_remark': it_block4_dqgc_supervision_remark,
            'it_block4_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4RdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4RdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_rdgc_list = process_data.get('it_block4_rdgc_list')
        it_block4_rdgc_supervision_approval = it_block4_rdgc_list.get('it_block4_rdgc_supervision_approval')
        it_block4_rdgc_supervision_remark = it_block4_rdgc_list.get('it_block4_rdgc_supervision_remark')
        complete_report_review_report = it_block4_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='弱电系统（弱电工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_rdgc_supervision_approval': it_block4_rdgc_supervision_approval,
            'it_block4_rdgc_supervision_remark': it_block4_rdgc_supervision_remark,
            'it_block4_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4ZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4ZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_zxgc_list = process_data.get('it_block4_zxgc_list')
        it_block4_zxgc_supervision_approval = it_block4_zxgc_list.get('it_block4_zxgc_supervision_approval')
        it_block4_zxgc_supervision_remark = it_block4_zxgc_list.get('it_block4_zxgc_supervision_remark')
        complete_report_review_report = it_block4_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='装修工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_zxgc_supervision_approval': it_block4_zxgc_supervision_approval,
            'it_block4_zxgc_supervision_remark': it_block4_zxgc_supervision_remark,
            'it_block4_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4XfgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-消防工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4XfgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_xfgc_list = process_data.get('it_block4_xfgc_list')
        it_block4_xfgc_supervision_approval = it_block4_xfgc_list.get('it_block4_xfgc_supervision_approval')
        it_block4_xfgc_supervision_remark = it_block4_xfgc_list.get('it_block4_xfgc_supervision_remark')
        complete_report_review_report = it_block4_xfgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_xfgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='消防工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_xfgc_supervision_approval': it_block4_xfgc_supervision_approval,
            'it_block4_xfgc_supervision_remark': it_block4_xfgc_supervision_remark,
            'it_block4_xfgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4NtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4NtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        it_block4_ntgc_list = process_data.get('it_block4_ntgc_list')
        it_block4_ntgc_supervision_approval = it_block4_ntgc_list.get('it_block4_ntgc_supervision_approval')
        it_block4_ntgc_supervision_remark = it_block4_ntgc_list.get('it_block4_ntgc_supervision_remark')
        complete_report_review_report = it_block4_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name ='暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_ntgc_supervision_approval': it_block4_ntgc_supervision_approval,
            'it_block4_ntgc_supervision_remark': it_block4_ntgc_supervision_remark,
            'it_block4_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItBloack4SdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-it block4-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(ItBloack4SdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        block4_sdts_list = process_data.get('block4_sdts_list')
        it_block4_sdts_supervision_approval = block4_sdts_list.get('it_block4_sdts_supervision_approval')
        it_block4_sdts_supervision_remark = block4_sdts_list.get('it_block4_sdtsc_supervision_remark')
        complete_report_review_report = block4_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if it_block4_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.4.7'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'it_block4_sdts_supervision_approval': it_block4_sdts_supervision_approval,
            'it_block4_sdts_supervision_remark': it_block4_sdts_supervision_remark,
            'it_block4_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlDlfcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-电力走廊-电力方仓-分项计划监理审批
    """

    def __init__(self):
        super(DlzlDlfcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        dlzl_dlfc_list = process_data.get('dlzl_dlfc_list')
        dlzl_dlfc_supervision_approval = dlzl_dlfc_list.get(
            'dlzl_dlfc_supervision_approval')
        dlzl_dlfc_supervision_remark = dlzl_dlfc_list.get(
            'dlzl_dlfc_supervision_remark')
        complete_report_review_report = dlzl_dlfc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if dlzl_dlfc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '电力方仓安装'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'dlzl_dlfc_supervision_approval': dlzl_dlfc_supervision_approval,
            'dlzl_dlfc_supervision_remark': dlzl_dlfc_supervision_remark,
            'dlzl_dlfc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlBfxtSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-电力走廊-补/排风系统-分项计划监理审批
    """

    def __init__(self):
        super(DlzlBfxtSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        dlzl_bfxt_list = process_data.get('dlzl_bfxt_list')
        dlzl_bfxt_supervision_approval = dlzl_bfxt_list.get(
            'dlzl_bfxt_supervision_approval')
        dlzl_bfxt_supervision_remark = dlzl_bfxt_list.get(
            'dlzl_bfxt_supervision_remark')
        complete_report_review_report = dlzl_bfxt_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if dlzl_bfxt_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '外市电工程（红线内）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'dlzl_bfxt_supervision_approval': dlzl_bfxt_supervision_approval,
            'dlzl_bfxt_supervision_remark': dlzl_bfxt_supervision_remark,
            'dlzl_bfxt_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlWsdSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-电力走廊-外市电工程-分项计划监理审批
    """

    def __init__(self):
        super(DlzlWsdSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        dlzl_wsd_list = process_data.get('dlzl_wsd_list')
        dlzl_wsd_supervision_approval = dlzl_wsd_list.get(
            'dlzl_wsd_supervision_approval')
        dlzl_wsd_supervision_remark = dlzl_wsd_list.get(
            'dlzl_wsd_supervision_remark')
        complete_report_review_report = dlzl_wsd_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if dlzl_wsd_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '补/排风系统'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'dlzl_wsd_supervision_approval': dlzl_wsd_supervision_approval,
            'dlzl_wsd_supervision_remark': dlzl_wsd_supervision_remark,
            'dlzl_wsd_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlSdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-电力走廊-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(DlzlSdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        dlzl_sdts_list = process_data.get('dlzl_sdts_list')
        dlzl_sdts_supervision_approval = dlzl_sdts_list.get('dlzl_sdts_supervision_approval')
        dlzl_sdts_supervision_remark = dlzl_sdts_list.get('dlzl_sdtsc_supervision_remark')
        complete_report_review_report = dlzl_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if dlzl_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.5.4'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'dlzl_sdts_supervision_approval': dlzl_sdts_supervision_approval,
            'dlzl_sdts_supervision_remark': dlzl_sdts_supervision_remark,
            'dlzl_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclCffcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-柴发方仓和水处理方仓-柴发方仓安装-分项计划监理审批
    """

    def __init__(self):
        super(CfsclCffcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        cfscl_cffc_list = process_data.get('cfscl_cffc_list')
        cfscl_cffc_supervision_approval = cfscl_cffc_list.get(
            'cfscl_cffc_supervision_approval')
        cfscl_cffc_supervision_remark = cfscl_cffc_list.get(
            'cfscl_cffc_supervision_remark')
        complete_report_review_report = cfscl_cffc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if cfscl_cffc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '柴发方仓安装'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'cfscl_cffc_supervision_approval': cfscl_cffc_supervision_approval,
            'cfscl_cffc_supervision_remark': cfscl_cffc_supervision_remark,
            'cfscl_cffc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclSclSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-柴发方仓和水处理方仓-水处理-分项计划监理审批
    """

    def __init__(self):
        super(CfsclSclSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        cfscl_scl_list = process_data.get('cfscl_scl_list')
        cfscl_scl_supervision_approval = cfscl_scl_list.get(
            'cfscl_scl_supervision_approval')
        cfscl_scl_supervision_remark = cfscl_scl_list.get(
            'cfscl_scl_supervision_remark')
        complete_report_review_report = cfscl_scl_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if cfscl_scl_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '水处理'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'cfscl_scl_supervision_approval': cfscl_scl_supervision_approval,
            'cfscl_scl_supervision_remark': cfscl_scl_supervision_remark,
            'cfscl_scl_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclCflySupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-柴发方仓和水处理方仓-柴发油路系统-分项计划监理审批
    """

    def __init__(self):
        super(CfsclCflySupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        cfscl_cfly_list = process_data.get('cfscl_cfly_list')
        cfscl_cfly_supervision_approval = cfscl_cfly_list.get(
            'cfscl_cfly_supervision_approval')
        cfscl_cfly_supervision_remark = cfscl_cfly_list.get(
            'cfscl_cfly_supervision_remark')
        complete_report_review_report = cfscl_cfly_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if cfscl_cfly_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '柴发油路系统'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'cfscl_cfly_supervision_approval': cfscl_cfly_supervision_approval,
            'cfscl_cfly_supervision_remark': cfscl_cfly_supervision_remark,
            'cfscl_cfly_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclSdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-柴发方仓和水处理方仓-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(CfsclSdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        cfscl_sdts_list = process_data.get('cfscl_sdts_list')
        cfscl_sdts_supervision_approval = cfscl_sdts_list.get('cfscl_sdts_supervision_approval')
        cfscl_sdts_supervision_remark = cfscl_sdts_list.get('cfscl_sdtsc_supervision_remark')
        complete_report_review_report = cfscl_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if cfscl_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.6.4'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'cfscl_sdts_supervision_approval': cfscl_sdts_supervision_approval,
            'cfscl_sdts_supervision_remark': cfscl_sdts_supervision_remark,
            'cfscl_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU区域-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(AhuZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_zxgc_list = process_data.get('ahu_zxgc_list')
        ahu_zxgc_supervision_approval = ahu_zxgc_list.get(
            'ahu_zxgc_supervision_approval')
        ahu_zxgc_supervision_remark = ahu_zxgc_list.get(
            'ahu_zxgc_supervision_remark')
        complete_report_review_report = ahu_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if ahu_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '装修（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_zxgc_supervision_approval': ahu_zxgc_supervision_approval,
            'ahu_zxgc_supervision_remark': ahu_zxgc_supervision_remark,
            'ahu_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuNtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU区域-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(AhuNtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_ntgc_list = process_data.get('ahu_ntgc_list')
        ahu_ntgc_supervision_approval = ahu_ntgc_list.get(
            'ahu_ntgc_supervision_approval')
        ahu_ntgc_supervision_remark = ahu_ntgc_list.get(
            'ahu_ntgc_supervision_remark')
        complete_report_review_report = ahu_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if ahu_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_ntgc_supervision_approval': ahu_ntgc_supervision_approval,
            'ahu_ntgc_supervision_remark': ahu_ntgc_supervision_remark,
            'ahu_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuDqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU区域-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(AhuDqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_dqgc_list = process_data.get('ahu_dqgc_list')
        ahu_dqgc_supervision_approval = ahu_dqgc_list.get(
            'ahu_dqgc_supervision_approval')
        ahu_dqgc_supervision_remark = ahu_dqgc_list.get(
            'ahu_dqgc_supervision_remark')
        complete_report_review_report = ahu_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if ahu_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '电气工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_dqgc_supervision_approval': ahu_dqgc_supervision_approval,
            'ahu_dqgc_supervision_remark': ahu_dqgc_supervision_remark,
            'ahu_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuRdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU区域-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(AhuRdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_rdgc_list = process_data.get('ahu_rdgc_list')
        ahu_rdgc_supervision_approval = ahu_rdgc_list.get(
            'ahu_rdgc_supervision_approval')
        ahu_rdgc_supervision_remark = ahu_rdgc_list.get(
            'ahu_rdgc_supervision_remark')
        complete_report_review_report = ahu_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if ahu_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '弱电工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_rdgc_supervision_approval': ahu_rdgc_supervision_approval,
            'ahu_rdgc_supervision_remark': ahu_rdgc_supervision_remark,
            'ahu_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AhuSdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-AHU-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(AhuSdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        ahu_sdts_list = process_data.get('ahu_sdts_list')
        ahu_sdts_supervision_approval = ahu_sdts_list.get('ahu_sdts_supervision_approval')
        ahu_sdts_supervision_remark = ahu_sdts_list.get('ahu_sdtsc_supervision_remark')
        complete_report_review_report = ahu_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if ahu_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.7.5'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'ahu_sdts_supervision_approval': ahu_sdts_supervision_approval,
            'ahu_sdts_supervision_remark': ahu_sdts_supervision_remark,
            'ahu_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqyZxgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-装修工程-分项计划监理审批
    """

    def __init__(self):
        super(BgqyZxgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_zxgc_list = process_data.get('bgqy_zxgc_list')
        bgqy_zxgc_supervision_approval = bgqy_zxgc_list.get(
            'bgqy_zxgc_supervision_approval')
        bgqy_zxgc_supervision_remark = bgqy_zxgc_list.get(
            'bgqy_zxgc_supervision_remark')
        complete_report_review_report = bgqy_zxgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_zxgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '装修（结构工程）'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_zxgc_supervision_approval': bgqy_zxgc_supervision_approval,
            'bgqy_zxgc_supervision_remark': bgqy_zxgc_supervision_remark,
            'bgqy_zxgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqyNtgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-暖通工程-分项计划监理审批
    """

    def __init__(self):
        super(BgqyNtgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_ntgc_list = process_data.get('bgqy_ntgc_list')
        bgqy_ntgc_supervision_approval = bgqy_ntgc_list.get(
            'bgqy_ntgc_supervision_approval')
        bgqy_ntgc_supervision_remark = bgqy_ntgc_list.get(
            'bgqy_ntgc_supervision_remark')
        complete_report_review_report = bgqy_ntgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_ntgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '暖通工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_ntgc_supervision_approval': bgqy_ntgc_supervision_approval,
            'bgqy_ntgc_supervision_remark': bgqy_ntgc_supervision_remark,
            'bgqy_ntgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqyDqgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-电气工程-分项计划监理审批
    """

    def __init__(self):
        super(BgqyDqgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_dqgc_list = process_data.get('bgqy_dqgc_list')
        bgqy_dqgc_supervision_approval = bgqy_dqgc_list.get(
            'bgqy_dqgc_supervision_approval')
        bgqy_dqgc_supervision_remark = bgqy_dqgc_list.get(
            'bgqy_dqgc_supervision_remark')
        complete_report_review_report = bgqy_dqgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_dqgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '电气工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_dqgc_supervision_approval': bgqy_dqgc_supervision_approval,
            'bgqy_dqgc_supervision_remark': bgqy_dqgc_supervision_remark,
            'bgqy_dqgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqyRdgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-弱电工程-分项计划监理审批
    """

    def __init__(self):
        super(BgqyRdgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_rdgc_list = process_data.get('bgqy_rdgc_list')
        bgqy_rdgc_supervision_approval = bgqy_rdgc_list.get(
            'bgqy_rdgc_supervision_approval')
        bgqy_rdgc_supervision_remark = bgqy_rdgc_list.get(
            'bgqy_rdgc_supervision_remark')
        complete_report_review_report = bgqy_rdgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_rdgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '弱电工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_rdgc_supervision_approval': bgqy_rdgc_supervision_approval,
            'bgqy_rdgc_supervision_remark': bgqy_rdgc_supervision_remark,
            'bgqy_rdgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqyXfgcSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-消防工程-分项计划监理审批
    """

    def __init__(self):
        super(BgqyXfgcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_xfgc_list = process_data.get('bgqy_xfgc_list')
        bgqy_xfgc_supervision_approval = bgqy_xfgc_list.get(
            'bgqy_xfgc_supervision_approval')
        bgqy_xfgc_supervision_remark = bgqy_xfgc_list.get(
            'bgqy_xfgc_supervision_remark')
        complete_report_review_report = bgqy_xfgc_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_xfgc_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_task_name = '消防工程'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_xfgc_supervision_approval': bgqy_xfgc_supervision_approval,
            'bgqy_xfgc_supervision_remark': bgqy_xfgc_supervision_remark,
            'bgqy_xfgc_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgqySdtsSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-办公区域-上电调试-分项计划监理审批
    """

    def __init__(self):
        super(BgqySdtsSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        complete_report_review_report_url = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        bgqy_sdts_list = process_data.get('bgqy_sdts_list')
        bgqy_sdts_supervision_approval = bgqy_sdts_list.get('bgqy_sdts_supervision_approval')
        bgqy_sdts_supervision_remark = bgqy_sdts_list.get('bgqy_sdtsc_supervision_remark')
        complete_report_review_report = bgqy_sdts_list.get('complete_report_review_report')

        for report in complete_report_review_report:
            # 提取 FileList 中的 URL
            for file in report['response']['FileList']:
                complete_report_review_report_url.append(file['url'])

        if bgqy_sdts_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and tertiary_serial_number ='3.8.6'" \
                        f"and tertiary_task_name ='设备上电调试'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'bgqy_sdts_supervision_approval': bgqy_sdts_supervision_approval,
            'bgqy_sdts_supervision_remark': bgqy_sdts_supervision_remark,
            'bgqy_sdts_complete_report_review_report': complete_report_review_report_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgJyReportSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-检验报告审核-监理审批
    """

    def __init__(self):
        super(AzsgJyReportSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jy_report_supervision_approval = process_data.get('jy_report_supervision_approval')
        jy_report_supervision_remark = process_data.get('jy_report_supervision_remark')

        if jy_report_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '检验'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jy_report_supervision_approval': jy_report_supervision_approval,
            'jy_report_supervision_remark': jy_report_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgJyReportItemApproval(AjaxTodoBase):
    """
        安装施工-检验报告审核-项管审批
    """

    def __init__(self):
        super(AzsgJyReportItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jy_report_item_approval = process_data.get('jy_report_item_approval')
        jy_report_item_remark = process_data.get('jy_report_item_remark')

        if jy_report_item_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '检验'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jy_report_item_approval': jy_report_item_approval,
            'jy_report_item_remark': jy_report_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgJyReportPmApproval(AjaxTodoBase):
    """
        安装施工-检验报告审核-PM审批
    """

    def __init__(self):
        super(AzsgJyReportPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jy_report_PM_approval = process_data.get('jy_report_PM_approval')
        jy_report_PM_remark = process_data.get('jy_report_PM_remark')

        if jy_report_PM_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '检验'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jy_report_PM_approval': jy_report_PM_approval,
            'jy_report_PM_remark': jy_report_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgSdReportSupervisionExaminationApproval(AjaxTodoBase):
    """
        安装施工-送电报告审核-监理审批
    """

    def __init__(self):
        super(AzsgSdReportSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sd_report_supervision_approval = process_data.get('sd_report_supervision_approval')
        sd_report_supervision_remark = process_data.get('sd_report_supervision_remark')

        if sd_report_supervision_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '送电'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sd_report_supervision_approval': sd_report_supervision_approval,
            'sd_report_supervision_remark': sd_report_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgSdReportItemApproval(AjaxTodoBase):
    """
        安装施工-送电报告审核-项管审批
    """

    def __init__(self):
        super(AzsgSdReportItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sd_report_item_approval = process_data.get('sd_report_item_approval')
        sd_report_item_remark = process_data.get('sd_report_item_remark')

        if sd_report_item_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '送电'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sd_report_item_approval': sd_report_item_approval,
            'sd_report_item_remark': sd_report_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AzsgSdReportPmApproval(AjaxTodoBase):
    """
        安装施工-送电报告审核-PM审批
    """

    def __init__(self):
        super(AzsgSdReportPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sd_report_PM_approval = process_data.get('sd_report_PM_approval')
        sd_report_PM_remark = process_data.get('sd_report_PM_remark')

        if sd_report_PM_approval == '驳回':
            query_sql = f"DELETE FROM installation_construction_report " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND report_tale = '送电'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sd_report_PM_approval': sd_report_PM_approval,
            'sd_report_PM_remark': sd_report_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
