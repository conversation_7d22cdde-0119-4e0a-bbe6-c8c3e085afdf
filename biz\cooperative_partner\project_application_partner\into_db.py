import json

from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow


class ConstructionApplicationProcessPartnerIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_construction_application_into_db(self):
        """
        合作伙伴-项目报建 完工信息存库
        """
        project_name = self.ctx.variables.get("project_name")
        main_ticket_id = self.ctx.variables.get("main_ticket_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        work_list = self.ctx.variables.get("work_list")
        insert_data = []
        if work_list:
            info = work_list[0]
            appendix = info.get("appendix")
            file_json = [file["response"]["FileList"][0]["url"] for file in appendix]
            insert_data.append(
                {
                    "project_name": project_name,
                    "serial_number": info.get("serial_number"),
                    "work_content": info.get("work_content"),
                    "main_flow_id": main_ticket_id,
                    "ticket_id": ticket_id,
                    "start_time": info.get("start_time"),
                    "finish_time": info.get("finish_time"),
                    "construction_time": info.get("construction_time"),
                    "actual_start_time": info.get("actual_start_time"),
                    "actual_finish_time": info.get("actual_finish_time"),
                    "actual_construction_time": info.get("actual_construction_time"),
                    "construction_time_deviation": info.get(
                        "construction_time_deviation"
                    ),
                    "schedule_deviation": info.get("schedule_deviation"),
                    "schedule_skewing_explain": info.get("schedule_skewing_explain"),
                    "file_json": json.dumps(file_json, ensure_ascii=False),
                    "remark": info.get("remark"),
                }
            )
        if insert_data:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_application_completion_info", insert_data)
            tb_db.commit()
        variables = {"insert_data": insert_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def xfbg_into_db(self):
        """
        合作伙伴-项目报建 消防第三方测试报告存库
        """
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        xfbg_file = self.ctx.variables.get("xfbg_file")
        xfbg_remark = self.ctx.variables.get("xfbg_remark")
        file_json = [file["response"]["FileList"][0]["url"] for file in xfbg_file]
        sql = (
            "SELECT project_name, serial_number, work_content, start_time, completion_time AS finish_time "
            "FROM construct_plan_data "
            f"WHERE project_name = '{project_name}' AND serial_number = '2.4' "
        )
        db = mysql.new_mysql_instance("tbconstruct")
        info = db.get_row(sql)
        insert_data = []
        insert_data.append(
            {
                "project_name": project_name,
                "serial_number": info.get("serial_number"),
                "work_content": info.get("work_content"),
                "main_flow_id": ticket_id,
                "ticket_id": ticket_id,
                "start_time": info.get("start_time"),
                "finish_time": info.get("finish_time"),
                "file_json": json.dumps(file_json, ensure_ascii=False),
                "remark": xfbg_remark,
            }
        )
        if insert_data:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_application_completion_info", insert_data)
            tb_db.commit()
        variables = {"insert_data": insert_data}
        flow.complete_task(self.ctx.task_id, variables=variables)
