import datetime
import json

from iBroker.lib import mysql
from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class ConstructionMaintenanceDataIntegrators(AjaxTodoBase):
    """
        建设接维平台对接：集成商资料上传
    """

    def __init__(self):
        super(ConstructionMaintenanceDataIntegrators, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jcs_uploader = process_user
        jcs_file_list = process_data.get("jcs_file")
        jcs_upload_time = datetime.datetime.now()
        time_str = jcs_upload_time.strftime('%Y-%m-%d %H:%M:%S')
        logo = process_data.get("logo")
        file_list = []
        for file in jcs_file_list:
            url = file.get("url")
            file_type = file.get("file_type")
            file_name = file.get("file_name")
            file_remark = file.get("file_remark")
            is_need = file.get("is_need")
            if url:
                file_list.append({
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": url,
                    "uploader": jcs_uploader,
                    "upload_time": time_str,
                    "node": logo,
                    "file_remark": file_remark,
                })
            else:
                if not is_need:
                    return {"code": -1, "msg": f"{file_name}未上传且未选择文件状态"}
                else:
                    if len(is_need) > 1:
                        return {"code": -1, "msg": "文件状态只能选择一个，请检查"}
                    elif is_need[0] == "不涉及" and not file_remark:
                        return {"code": -1, "msg": "不涉及文件请写明理由"}
                    elif is_need[0] == "后补上传" and not file_remark:
                        return {"code": -1, "msg": "后补文件请写明理由"}
                    else:
                        file_list.append({
                            "file_type": file_type,
                            "file_name": file_name,
                            "url": url,
                            "uploader": jcs_uploader,
                            "upload_time": time_str,
                            "node": logo,
                            "file_remark": file_remark,
                            "is_need": is_need
                        })
        variables = {
            'jcs_file_list': file_list,
            'jcs_upload_time': time_str,
            'jcs_uploader': jcs_uploader,
            'jcs_file': jcs_file_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceDataSupervision(AjaxTodoBase):
    """
        建设接维平台对接：监理资料上传
    """

    def __init__(self):
        super(ConstructionMaintenanceDataSupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jl_uploader = process_user
        jl_file_list = process_data.get("jl_file")
        jl_upload_time = datetime.datetime.now()
        time_str = jl_upload_time.strftime('%Y-%m-%d %H:%M:%S')
        logo = process_data.get("logo")

        file_list = []
        for file in jl_file_list:
            url = file.get("url")
            file_type = file.get("file_type")
            file_name = file.get("file_name")
            if url:
                file_list.append({
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": url,
                    "uploader": jl_uploader,
                    "upload_time": time_str,
                    "node": logo
                })
        variables = {
            'jl_file_list': file_list,
            'jl_upload_time': time_str,
            'jl_uploader': jl_uploader,
            'jl_file': jl_file_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceSupervision(AjaxTodoBase):
    """
        监理审核
    """

    def __init__(self):
        super(ConstructionMaintenanceSupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get('logo')
        jl_review = process_data.get("jl_review")
        jl_rejection = process_data.get("jl_rejection")
        jl_remark = process_data.get("jl_remark")

        if jl_review:
            if logo == '集成商':
                jcs_file_list = process_data.get("jcs_file_list")
                if jl_review == '驳回':
                    if jl_rejection:
                        variables = {
                            'jcs_file_list': jcs_file_list,
                            'jcs_jl_review': jl_review,
                            'jcs_jl_rejection': jl_rejection,
                            'jcs_jl_remark': jl_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'jcs_file_list': jcs_file_list,
                        'jcs_jl_review': jl_review,
                        'jcs_jl_remark': jl_remark
                    }
            elif logo == '监理':
                jl_file_list = process_data.get("jl_file_list")
                if jl_review == '驳回':
                    if jl_rejection:
                        variables = {
                            'jl_file_list': jl_file_list,
                            'jl_jl_review': jl_review,
                            'jl_jl_rejection': jl_rejection,
                            'jl_jl_remark': jl_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'jl_file_list': jl_file_list,
                        'jl_jl_review': jl_review,
                        'jl_jl_remark': jl_remark
                    }
            else:
                return {"code": -1, "msg": "未标明类型"}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceProjectManagement(AjaxTodoBase):
    """
        项管审核
    """

    def __init__(self):
        super(ConstructionMaintenanceProjectManagement, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get("logo")
        xg_review = process_data.get("xg_review")
        xg_rejection = process_data.get("xg_rejection")
        xg_remark = process_data.get("xg_remark")
        if xg_review:
            if logo == '集成商':
                jcs_file_list = process_data.get("jcs_file_list")
                if xg_review == '驳回':
                    if xg_rejection:
                        variables = {
                            'jcs_xg_review': xg_review,
                            'jcs_xg_rejection': xg_rejection,
                            'jcs_xg_remark': xg_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    contract_file = self.ctx.variables.get("contract_file")
                    jcs_file_list = contract_file + jcs_file_list
                    variables = {
                        'jcs_file_list': jcs_file_list,
                        'jcs_xg_review': xg_review,
                        'jcs_xg_remark': xg_remark
                    }
            elif logo == '监理':
                jl_file_list = process_data.get("jl_file_list")
                if xg_review == '驳回':
                    if xg_rejection:
                        variables = {
                            'jl_file_list': jl_file_list,
                            'jl_xg_review': xg_review,
                            'jl_xg_rejection': xg_rejection,
                            'jl_xg_remark': xg_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'jl_file_list': jl_file_list,
                        'jl_xg_review': xg_review,
                        'jl_xg_remark': xg_remark
                    }
            else:
                return {"code": -1, "msg": "未标明类型"}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenancePM(AjaxTodoBase):
    """
        PM审核
    """

    def __init__(self):
        super(ConstructionMaintenancePM, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get("logo")
        pm_review = process_data.get("pm_review")
        pm_rejection = process_data.get("pm_rejection")
        pm_remark = process_data.get("pm_remark")
        if pm_review:
            if logo == '集成商':
                jcs_file_list = process_data.get("jcs_file_list")
                if pm_review == '驳回':
                    contract_file = self.ctx.variables.get("contract_file")
                    for item in contract_file:
                        for file in jcs_file_list:
                            if item['file_name'] == file['file_name']:
                                jcs_file_list.remove(file)
                    if pm_rejection:
                        variables = {
                            'jcs_file_list': jcs_file_list,
                            'jcs_pm_review': pm_review,
                            'jcs_pm_rejection': pm_rejection,
                            'jcs_pm_remark': pm_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'jcs_file_list': jcs_file_list,
                        'jcs_pm_review': pm_review,
                        'jcs_pm_remark': pm_remark
                    }
            elif logo == '监理':
                jl_file_list = process_data.get("jl_file_list")
                if pm_review == '驳回':
                    if pm_rejection:
                        variables = {
                            'jl_file_list': jl_file_list,
                            'jl_pm_review': pm_review,
                            'jl_pm_rejection': pm_rejection,
                            'jl_pm_remark': pm_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'jl_file_list': jl_file_list,
                        'jl_pm_review': pm_review,
                        'jl_pm_remark': pm_remark
                    }
            else:
                return {"code": -1, "msg": "未标明类型"}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceFileStorage(object):
    def __init__(self):
        super(ConstructionMaintenanceFileStorage, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def file_storage(self):
        """
            文件保存
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jcs_file_list = self.ctx.variables.get("jcs_file_list")
        jl_file_list = self.ctx.variables.get("jl_file_list")
        file_list = jcs_file_list + jl_file_list
        insert_data = []
        for file in file_list:
            url = file.get("url")
            insert_data.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                "url": json.dumps(url, ensure_ascii=False),
                "file_name": file.get("file_name"),
                "file_type": file.get("file_type"),
                "uploader": file.get("uploader"),
                "upload_time": file.get("upload_time"),
                "node": file.get("node"),
                "is_need": file.get("is_need")[0] if file.get("is_need") else "",
                "file_remark": file.get("file_remark")
            })
            # file["ticket_id"] = ticket_id
            # file["project_name"] = project_name
            # file["url"] = json.dumps(url, ensure_ascii=False)
            # file["file_name"] = file.get("file_name")
            # file["file_type"] = file.get("file_type")
            # file["uploader"] = file.get("uploader")
            # file["upload_time"] = file.get("upload_time")
            # file["node"] = file.get("node")
            # file["is_need"] = file.get("is_need")[0] if file.get("is_need") else ""
            # file["file_remark"] = file.get("file_remark")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("maintenance_upload_data", insert_data)
        tb_db.commit()
        variables = {
            # "file_list": file_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql_query = ("SELECT account, role FROM project_role_account "
                     "WHERE project_name = '%s' "
                     "AND role IN ('集成商-项目经理', '监理-总监理工程师', '项管-项目经理','合建方-项目经理','总包-项目经理') "
                     "AND del_flag = '0'") % project_name
        jg_sql = "SELECT architecture,PM FROM project_team_information_data " \
                 " WHERE project_name='%s'" % project_name

        sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                       f"WHERE project_name='{project_name}'")
        is_hire = "是" if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else "否"
        people_list_1 = db.get_all(sql_query)
        jl = ""
        jcs = ""
        xg = ""
        if people_list_1:
            for i in people_list_1:
                if i.get("role") == "监理-总监理工程师":
                    jl = i.get("account")
                if i.get("role") == "总包-项目经理":
                    jcs = i.get("account")
                if i.get("role") == "集成商-项目经理":
                    jcs = i.get("account")
                if i.get("role") == "项管-项目经理":
                    xg = i.get("account")
                if i.get("role") == "合建方-项目经理":
                    xg = i.get("account")
        else:
            return {"code": -1, "msg": "人员信息未录入"}
        people_list_2 = db.get_all(jg_sql)
        if people_list_2:
            pm = people_list_2[0].get("PM")
            jg = people_list_2[0].get("architecture")
        else:
            return {"code": -1, "msg": "人员信息未录入"}
        variables = {
            "jg": jg,
            "xg": xg,
            "jl": jl,
            "pm": pm,
            "jcs": jcs,
            "is_hire": is_hire
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionIntegratorStoragePersonCheck(object):
    """
    建设、接维对接:数据上传及审核人员确定
    """

    def __init__(self):
        super(ConstructionIntegratorStoragePersonCheck, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
                "SELECT construction_mode FROM risk_early_warning_data WHERE project_name='%s'"
                % project_name
        )
        query_data = db.get_row(sql)
        is_hire = False
        if query_data:
            construction_mode = query_data.get("construction_mode")
            if construction_mode != "自建":
                is_hire = True
        integrator_project_manager_sql = (
            "SELECT account FROM project_role_account"
            f" WHERE project_name='{project_name}' and role ='集成商-项目经理' and del_flag = '0'"
        )
        supervisor_engineer_sql = (
            "SELECT account FROM project_role_account"
            f" WHERE project_name='{project_name}' and role ='监理-总监理工程师' and del_flag = '0'"
        )
        project_management_manager_sql = (
            "SELECT account FROM project_role_account"
            f" WHERE project_name='{project_name}' and role ='项管-项目经理' and del_flag = '0'"
        )
        sql1 = "SELECT architecture FROM project_team_information_data " \
               " WHERE project_name='%s'" % project_name
        if is_hire:
            integrator_project_manager_sql = (
                "SELECT account FROM project_role_account"
                f" WHERE project_name='{project_name}' and role ='总包-项目经理' and del_flag = '0'"
            )
            project_management_manager_sql = (
                "SELECT account FROM project_role_account"
                f" WHERE project_name='{project_name}' and role ='合建方-项目经理' and del_flag = '0'"
            )
        PM_list = db.get_all(sql1)
        if PM_list:
            PM = PM_list[0].get("architecture")
        else:
            PM = ""
        integrator_project_manager = None
        supervisor_engineer = None
        project_management_manager = None
        integrator_project_manager_list = db.get_all(integrator_project_manager_sql)
        supervisor_engineer_list = db.get_all(supervisor_engineer_sql)
        project_management_manager_list = db.get_all(project_management_manager_sql)
        self.workflow_detail.add_kv_table(name='sql查询信息', kv_table={
            'integrator_project_manager': integrator_project_manager_list,
            'supervisor_engineer': supervisor_engineer_list,
            'project_management_manager': project_management_manager_list
        })
        if integrator_project_manager_list:
            integrator_project_manager = integrator_project_manager_list[0].get("account")
        if supervisor_engineer_list:
            supervisor_engineer = supervisor_engineer_list[0].get("account")
        if project_management_manager_list:
            project_management_manager = project_management_manager_list[0].get("account")

        variables = {
            "integrator_project_manager": integrator_project_manager,
            "supervisor_engineer": supervisor_engineer,
            "project_management_manager": project_management_manager,
            "project_name": project_name,
            "PM": PM
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionIntegratorFileUpload(AjaxTodoBase):
    """
        建设、接维对接:集成商资料上传
    """

    def __init__(self):
        super(ConstructionIntegratorFileUpload, self).__init__()
        self.ctx = get_context()

    def end(self, process_data, process_user):
        # 获取上传文件目录
        final_file_list = process_data.get("final_file_list")
        final_file_list_save = []
        # 定义存库的文件列表
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        logo = process_data.get("logo", "")
        if logo == "集成商":
            fail_list = []
            un_remark_list = []
            for file in final_file_list:
                url = file.get("url")
                file_type = file.get("file_type")
                file_name = file.get("file_name")
                is_need = file.get("is_need")
                file_remark = file.get("file_remark")
                if is_need:
                    if not file_remark:
                        un_remark_list.append(f"【{file_name}】")
                    else:
                        final_file_list_save.append({
                            "ticket_id": ticket_id,
                            "project_name": project_name,
                            "file_type": file_type,
                            "file_name": file_name,
                            "url": '',
                            "uploader": self.ctx.variables.get("integrator_project_manager"),
                            "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                            "node": "架构",
                            "file_remark": file_remark
                        })
                    continue
                if url:
                    for item in url:
                        if item["status"] == "fail":
                            fail_list.append(f"【{file_name}:{item['name']}】")
                    final_file_list_save.append({
                        "ticket_id": ticket_id,
                        "project_name": project_name,
                        "file_type": file_type,
                        "file_name": file_name,
                        "url": json.dumps(url),
                        "uploader": self.ctx.variables.get("integrator_project_manager"),
                        "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "node": "架构",
                        "file_remark": file_remark
                    })
                else:
                    fail_list.append(f"【{file_name}】")
            if un_remark_list:
                return {"code": -1, "msg": f"{un_remark_list}不涉及未填写备注"}
            if fail_list:
                return {"code": -1, "msg": f"{fail_list}上传失败"}
        elif logo == "终验文件上传":
            fail_list = []
            for file in final_file_list:
                url = file.get("url")
                file_type = file.get("file_type")
                file_name = file.get("file_name")
                if url:
                    for item in url:
                        if item["status"] == "fail":
                            fail_list.append(f"【{file_name}:{item['name']}】")
                    final_file_list_save.append({
                        "ticket_id": ticket_id,
                        "project_name": project_name,
                        "file_type": file_type,
                        "file_name": file_name,
                        "url": json.dumps(url) if url else '',
                        "uploader": self.ctx.variables.get("integrator_project_manager"),
                        "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                        "node": "集成商",
                    })
                else:
                    fail_list.append(f"【{file_name}】")
            if fail_list:
                return {"code": -1, "msg": f"{fail_list}上传失败"}
        # 将上传文件列表及存库列表加入流程变量供其他节点使用
        variables = {
            'final_file_list': final_file_list,
            'final_file_list_save': final_file_list_save,
        }

        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ContractInformationByProject(object):
    """
        合同信息
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_contract_information(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql_campus = ("select campus,project from risk_early_warning_data where project_name = '%s'" % project_name)
        campus_query = db.get_row(sql_campus)
        if campus_query:
            project = campus_query.get("project")
            campus = campus_query.get("campus")
        else:
            return {"code": -1, "msg": "未查询到对应项目信息"}
        sql1 = ("select "
                "integrators,AHU,low_voltage_cabinet,medium_voltage_cabinet,"
                "battery,chaifa,cabinet,transformer,headboard,PDU,tbox "
                f"from supplier_resources where campus = '{campus}' and project = '{project}'"
                )
        query1 = db.get_row(sql1)
        sql2 = ("select service_provider,equipment_name,characteristic,contract_url "
                "from service_provider_contract_documents")
        contract_list = db.get_all(sql2)
        equipment_url_list = []
        integrated_url_list = []
        if query1 and contract_list:
            for item in contract_list:
                equipment_name = item.get("equipment_name")
                if item["service_provider"] == query1.get("integrators") and item["characteristic"] == "集成":
                    integrated_url_list += json.loads(item["contract_url"])
                if equipment_name:
                    if item["service_provider"] == query1.get(equipment_name) and item["characteristic"] == "设备":
                        equipment_url_list += json.loads(item["contract_url"])
        contract_file = [
            {"file_type": "招采及合同文件",
             "file_name": "对应项目的腾讯集采设备的招标技术文件，包含技术应答文件、澄清应答",
             "url": equipment_url_list
             },
            {"file_type": "招采及合同文件",
             "file_name": "腾讯与腾讯集采设备厂家合同封面及合同中有关服务的条款及附件",
             "url": equipment_url_list
             },
            {"file_type": "招采及合同文件",
             "file_name": "对应项目的集成商招标技术文件，包含技术应答文件、澄清应答",
             "url": integrated_url_list
             },
            {"file_type": "招采及合同文件",
             "file_name": "腾讯与TB集成商合同封面及合同中有关售后条款及附件",
             "url": integrated_url_list
             }
        ]
        variables = {
            "contract_file": contract_file,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionIntegratorFileCheck(AjaxTodoBase):
    """
        建设、接维对接:监理资料审核
    """

    def __init__(self):
        super(ConstructionIntegratorFileCheck, self).__init__()
        self.ctx = get_context()

    def end(self, process_data, process_user):
        logo = process_data.get('logo')
        if logo == "监理审核":
            # 获取监理审核情况
            supervision_review = process_data.get("supervision_review")
            supervision_rejection = process_data.get("supervision_rejection")
            supervision_remark = process_data.get("supervision_remark")
            if supervision_review:
                if supervision_review == '驳回':
                    if supervision_rejection:
                        variables = {
                            'supervision_review': supervision_review,
                            'supervision_rejection': supervision_rejection,
                            'supervision_remark': supervision_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'supervision_review': supervision_review,
                        'supervision_remark': supervision_remark,
                    }

            else:
                return {"code": -1, "msg": "请选择审核结果"}
        elif logo == "项管审核":
            # 获取项管审核情况
            project_manager_review = process_data.get("project_manager_review")
            project_manager_rejection = process_data.get("project_manager_rejection")
            project_manager_remark = process_data.get("project_manager_remark")
            if project_manager_review:
                if project_manager_review == '驳回':
                    if project_manager_rejection:
                        variables = {
                            'project_manager_review': project_manager_review,
                            'project_manager_rejection': project_manager_rejection,
                            'jiewei': project_manager_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    variables = {
                        'project_manager_review': project_manager_review,
                        'project_manager_remark': project_manager_remark,
                    }
            else:
                return {"code": -1, "msg": "请选择审核结果"}
        elif logo == "设计PM审核":
            # 获取项管审核情况
            PM_review = process_data.get("PM_review")
            PM_rejection = process_data.get("PM_rejection")
            PM_remark = process_data.get("PM_remark")
            if PM_review:
                if PM_review == '驳回':
                    if PM_rejection:
                        variables = {
                            'PM_review': PM_review,
                            'PM_rejection': PM_rejection,
                            'PM_remark': PM_remark
                        }
                    else:
                        return {"code": -1, "msg": "请填写驳回原因"}
                else:
                    # 获取上传文件目录
                    PM_file_list = process_data.get("PM_file_list")
                    PM_file_list_save = []
                    # 定义存库的文件列表
                    ticket_id = self.ctx.variables.get("ticket_id")
                    project_name = self.ctx.variables.get("project_name")
                    fail_list = []
                    for file in PM_file_list:
                        url = file.get("url")
                        file_type = file.get("file_type", "")
                        file_name = file.get("file_name", "")
                        file_remark = file.get("file_remark", "")
                        if url:
                            for item in url:
                                if item["status"] == "fail":
                                    fail_list.append(f"【{file_name}:{item['name']}】")
                            PM_file_list_save.append({
                                "ticket_id": ticket_id,
                                "project_name": project_name,
                                "file_type": file_type,
                                "file_name": file_name,
                                "url": json.dumps(url),
                                "uploader": self.ctx.variables.get("integrator_project_manager"),
                                "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                                "node": "架构",
                                "file_remark": file_remark,
                            })
                    final_file_list = self.ctx.variables.get("final_file_list")
                    final_file_list_save = self.ctx.variables.get("final_file_list_save")
                    final_file_list.extend(PM_file_list)
                    final_file_list_save.extend(PM_file_list_save)
                    variables = {
                        'final_file_list': final_file_list,
                        'final_file_list_save': final_file_list_save,
                        'PM_file_list': PM_file_list,
                        'PM_file_list_save': PM_file_list_save,
                        'PM_review': PM_review,
                        'PM_remark': PM_remark,
                    }
                    if fail_list:
                        return {"code": -1, "msg": f"{fail_list}上传失败"}

            else:
                return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionIntegratorFileStore(object):
    """
        建设、接维对接:上传文件存库
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def file_store(self, final_file_list_save):
        # final_file_list_save = self.ctx.variables.get("final_file_list_save")
        for file in final_file_list_save:
            if not file.get("file_remark", ""):
                file["file_remark"] = "无"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("maintenance_upload_data", final_file_list_save)
        tb_db.commit()
        variables = {
            "final_file_list_save": final_file_list_save
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class Info(object):
    def __init__(self):
        super(Info, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def change(self):
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT account,role FROM project_role_account" \
               " WHERE project_name='%s'" % project_name
        account_list = db.get_all(sql1)
        variables = {
            "account_list": account_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class RoleCheck(AjaxTodoBase):
    def __init__(self):
        super(RoleCheck, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        account_list = process_data.get("account_list")
        list1 = []
        for i in account_list:
            list1.append(
                f"update `project_role_account` set `role` = '{i.get('new')}' where `role` = '{i.get('role')}' "
                f"and `project_name` = '{i.get('project_name')}';")
        variables = {
            'list1': list1
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RoleCreate(AjaxTodoBase):
    def __init__(self):
        super(RoleCreate, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        role_list = process_data.get("role_list")
        insert = []
        for i in role_list:
            if i.get("account"):
                insert.append({
                    "idcrmOrderCode": i.get("name"),
                    "idcrmProjectCode": i.get("account")
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("request_number", insert)
        tb_db.commit()
        variables = {
            "insert": insert
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
