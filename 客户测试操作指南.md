# 货期管理功能调试操作指南

## 🎯 目标
解决`dynamic_header`和`tabular_data_query`两个方法返回空数据的问题

## 📋 操作步骤

### 第一步：SQL测试验证
请在测试环境的数据库中执行以下SQL脚本，确认数据情况：

```bash
# 1. 连接到测试环境数据库
mysql -h your_host -u your_user -p your_database

# 2. 执行测试脚本
source /path/to/SQL测试脚本.sql
```

**重点关注以下输出结果：**

#### 1.1 基础数据检查
```sql
-- 期望结果：各表都应该有数据
SELECT 'all_construction_ticket_data' as table_name, COUNT(*) as total_count FROM all_construction_ticket_data;
SELECT 'equipment_production_racking_process' as table_name, COUNT(*) as total_count FROM equipment_production_racking_process;
SELECT 'delivery_schedule_management_table' as table_name, COUNT(*) as total_count FROM delivery_schedule_management_table;
```

#### 1.2 强制结单工单检查
```sql
-- 期望结果：有效工单数 > 0
SELECT 'valid_tickets' as type, COUNT(*) as count 
FROM all_construction_ticket_data 
WHERE (IsForceEnd IS NULL OR IsForceEnd != '1');
```

#### 1.3 表关联检查
```sql
-- 期望结果：关联后仍有数据
SELECT 'actd_eprp_dsm_join' as join_type, COUNT(*) as count
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
AND dsm.project_name IS NOT NULL;
```

### 第二步：根据SQL结果判断问题

#### 情况A：基础表无数据
如果某个表的count为0，说明：
- 数据还未同步到测试环境
- 表名可能不正确
- 需要检查数据导入流程

#### 情况B：有基础数据但关联后为空
如果基础表有数据但关联后为0，说明：
- 项目名称字段不匹配
- 设备类型字段不匹配  
- 工单ID关联有问题

#### 情况C：有关联数据但应用查询为空
如果SQL测试有结果但应用查询为空，说明：
- 代码中的WHERE条件过于严格
- 字段名称或表名有误
- 数据库连接配置问题

### 第三步：代码部署和测试

#### 3.1 备份原文件
```bash
cp campus_resources.py campus_resources.py.backup
```

#### 3.2 部署优化代码
将`优化后的方法实现.py`中的`dynamic_header_optimized`方法替换原来的`dynamic_header`方法。

#### 3.3 重启应用
```bash
# 根据您的部署方式选择
systemctl restart your-app-service
# 或
supervisorctl restart your-app
# 或
pkill -f python && nohup python app.py &
```

#### 3.4 查看调试日志
```bash
# 实时监控日志
tail -f /var/log/your-app/app.log | grep "DEBUG.*dynamic_header"

# 或者查看系统日志
journalctl -f -u your-service | grep "DEBUG"
```

### 第四步：功能测试

#### 4.1 访问功能页面
在浏览器中访问货期管理相关页面，触发`dynamic_header`方法调用。

#### 4.2 观察日志输出
应该能看到类似以下的调试信息：
```
[DEBUG] === dynamic_header 开始执行 ===
[DEBUG] 表 all_construction_ticket_data: 1234 条记录
[DEBUG] 表 equipment_production_racking_process: 567 条记录
[DEBUG] 表 delivery_schedule_management_table: 890 条记录
[DEBUG] 强制结单工单数量: 123
[DEBUG] 有效工单数量: 1111
[DEBUG] === 测试表关联 ===
[DEBUG] 工单表与设备生产表关联后: 456 条记录
[DEBUG] 三表关联（仅项目名称）后: 234 条记录
[DEBUG] 完整关联（项目名称+设备类型）后: 123 条记录
```

### 第五步：问题排查

#### 5.1 如果表数据量为0
```sql
-- 检查表是否存在
SHOW TABLES LIKE '%construction%';
SHOW TABLES LIKE '%equipment%';
SHOW TABLES LIKE '%delivery%';

-- 检查表结构
DESCRIBE all_construction_ticket_data;
DESCRIBE equipment_production_racking_process;
DESCRIBE delivery_schedule_management_table;
```

#### 5.2 如果关联后数据为0
```sql
-- 检查项目名称匹配
SELECT DISTINCT eprp.project_name as eprp_project
FROM equipment_production_racking_process eprp
WHERE eprp.project_name IS NOT NULL
LIMIT 10;

SELECT DISTINCT dsm.project_name as dsm_project  
FROM delivery_schedule_management_table dsm
WHERE dsm.project_name IS NOT NULL
LIMIT 10;

-- 检查是否有相同的项目名称
SELECT eprp.project_name
FROM equipment_production_racking_process eprp
INNER JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
GROUP BY eprp.project_name
LIMIT 10;
```

#### 5.3 如果设备类型不匹配
```sql
-- 检查设备类型字段
SELECT DISTINCT device_name FROM equipment_production_racking_process WHERE device_name IS NOT NULL LIMIT 10;
SELECT DISTINCT device_type FROM delivery_schedule_management_table WHERE device_type IS NOT NULL LIMIT 10;

-- 检查设备类型匹配情况
SELECT eprp.device_name, dsm.device_type, COUNT(*) as count
FROM equipment_production_racking_process eprp
INNER JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
GROUP BY eprp.device_name, dsm.device_type
ORDER BY count DESC
LIMIT 10;
```

## 📊 预期结果

成功后应该能看到：
1. **表头数据**：包含项目名称、供应商、交付时间等信息
2. **表格数据**：包含设备类型、SLA、预期时间等详细信息
3. **调试日志**：显示每个步骤的数据量和处理结果

## 🚨 注意事项

1. **生产环境谨慎操作**：建议先在测试环境验证
2. **备份重要数据**：修改前务必备份原文件
3. **移除调试日志**：功能正常后记得移除详细的调试日志
4. **性能考虑**：调试日志会影响性能，仅用于排查问题

## 📞 反馈信息

请将以下信息反馈给开发团队：

1. **SQL测试结果**：各个步骤的count数量
2. **应用日志**：包含DEBUG信息的完整日志
3. **错误信息**：如果有任何错误或异常
4. **数据示例**：如果有数据，提供几条示例记录

这样我们就能快速定位问题并提供解决方案。
