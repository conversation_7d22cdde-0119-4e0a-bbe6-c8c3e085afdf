import datetime

from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow, gnetops


# 项目供应商数据入库类
class SupplierShareAllocationIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_supplier_resources_into_db(self, data_list):
        """
        项目供应商资源修改
        """
        if data_list:
            data = data_list[0]
            campus = data.get("campus")
            project = data.get("project")
            # 判断是否有数据 有：修改 没有：新增
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = f"SELECT * FROM supplier_resources WHERE campus = '{campus}' AND project = '{project}'"
            result = db.get_all(query_sql)
            if result:
                id = data.get("id")
                update_data = {
                    "integrators": data.get("integrators"),
                    "project_manager": data.get("project_manager"),
                    "supervision": data.get("supervision"),
                    "examination": data.get("examination"),
                    "AHU": data.get("AHU"),
                    "chaifa": data.get("chaifa"),
                    "low_voltage_cabinet": data.get("low_voltage_cabinet"),
                    "medium_voltage_cabinet": data.get("medium_voltage_cabinet"),
                    "battery": data.get("battery"),
                    "cabinet": data.get("cabinet"),
                    "transformer": data.get("transformer"),
                    "headboard": data.get("headboard"),
                    "PDU": data.get("PDU"),
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update_by_id("supplier_resources", update_data, id)
                tb_db.commit()
            else:
                delivery_target = data.get("delivery_target")
                date_obj = datetime.datetime.strptime(delivery_target, '%Y-%m-%d')
                converted_date = date_obj.strftime('%Y年%m月%d日')
                temp = {
                    "area": data.get("area"),
                    "campus": campus,
                    "project": project,
                    "delivery_target": converted_date,
                    "state": data.get("state"),
                    "integrators": data.get("integrators"),
                    "project_manager": data.get("project_manager"),
                    "supervision": data.get("supervision"),
                    "examination": data.get("examination"),
                    "AHU": data.get("AHU"),
                    "chaifa": data.get("chaifa"),
                    "low_voltage_cabinet": data.get("low_voltage_cabinet"),
                    "medium_voltage_cabinet": data.get("medium_voltage_cabinet"),
                    "battery": data.get("battery"),
                    "cabinet": data.get("cabinet"),
                    "transformer": data.get("transformer"),
                    "headboard": data.get("headboard"),
                    "PDU": data.get("PDU"),
                }
                insert_list = [temp]
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("supplier_resources", insert_list)
                tb_db.commit()
        flow.complete_task(self.ctx.task_id)

    def project_supplier_resources_record_into_db(
            self, data_list, remark, handle_user_info
    ):
        """
        项目供应商份额分配操作记录保存
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        # 获取建单时间
        create_ticket_time = ""
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "CreateTime": "",
                },
                "SearchCondition": {"TicketId": [str(ticket_id)]},
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List")
        if result_list:
            create_ticket_time = result_list[0].get("CreateTime")
        # 操作记录保存
        insert_list = []
        if data_list:
            data = data_list[0]
            temp = {
                "area": data.get("area"),
                "campus": data.get("campus"),
                "project": data.get("project"),
                "delivery_target": data.get("delivery_target"),
                "state": data.get("state"),
                "integrators": data.get("integrators"),
                "project_manager": data.get("project_manager"),
                "supervision": data.get("supervision"),
                "examination": data.get("examination"),
                "AHU": data.get("AHU"),
                "chaifa": data.get("chaifa"),
                "low_voltage_cabinet": data.get("low_voltage_cabinet"),
                "medium_voltage_cabinet": data.get("medium_voltage_cabinet"),
                "battery": data.get("battery"),
                "cabinet": data.get("cabinet"),
                "transformer": data.get("transformer"),
                "headboard": data.get("headboard"),
                "PDU": data.get("PDU"),
                "ticket_id": ticket_id,
                "create_ticket_time": create_ticket_time,
                "update_time": handle_user_info.get("date"),
                "update_user": handle_user_info.get("user"),
                "remark": remark,
            }
            insert_list.append(temp)
        if insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("supplier_resources_record", insert_list)
            tb_db.commit()
        variables = {
            "create_ticket_time": create_ticket_time,
            "insert_list": insert_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 供应商份额分配信息更新
    def supplier_share_allocation_info_into_db(
        self, data_list, remark, handle_user_info
    ):
        """
        供应商份额分配信息更新(summary_data供应商份额分配时间、集成单位)
        """
        if data_list:
            data = data_list[0]
            campus = data.get("campus")
            project = data.get("project")
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = (
                "SELECT sn, supplier_share_allocation_time "
                f"FROM summary_data WHERE campus = '{campus}' AND project_name = '{project}'"
            )
            result = db.get_row(query_sql)
            if result:
                id = result.get("sn")
                exit_time = result.get("supplier_share_allocation_time")
                update_time = handle_user_info.get("date")
                jcs = data.get("integrators")
                min_time = update_time
                if exit_time:
                    exit_time_temp = datetime.datetime.strptime(
                        exit_time, "%Y-%m-%d %H:%M:%S"
                    )
                    update_time_temp = datetime.datetime.strptime(
                        update_time, "%Y-%m-%d %H:%M:%S"
                    )
                    min_time_temp = min(exit_time_temp, update_time_temp)
                    min_time = min_time_temp.strftime("%Y-%m-%d %H:%M:%S")
                update_data = {
                    "supplier_share_allocation_time": min_time,
                    "Integration_unit": jcs,
                }
                project_jcs_dict = {
                    "exit_time": exit_time,
                    "update_time": update_time,
                    "min_time": min_time,
                    "jcs": jcs,
                }
                db.update_by_id("summary_data", update_data, id)
                db.commit()
                variables = {
                    "summary_update_data": update_data,
                    "project_jcs_dict": project_jcs_dict,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
