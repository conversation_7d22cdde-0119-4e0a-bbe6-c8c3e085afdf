import datetime
import json
import logging
import os
import uuid
from collections import defaultdict

from iBroker.lib import mysql, config
from iBroker.lib.sdk import gnetops, flow
from iBroker.lib.sdk import tof
from iBroker.sdk.notification.chatops import Chat<PERSON>psSend
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib
from biz.cooperative_partner.cooperative_partner_general_method import IsItOrNotCooperativePartner, \
    CheckProgressWorkOrder


class ProcessProgressByProject(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_info_bak(self, project_name, page_size=None, page_number=None, ticket_id=None, ticket_name=None,
                     process_identification_name=None, responsible_person=None):
        if not page_size:
            page_size = 10
        if not page_number:
            page_number = 1
        db = mysql.new_mysql_instance("tbconstruct")
        SearchCondition = {}
        if ticket_id:
            SearchCondition["ticket_id"] = ticket_id
        elif ticket_name:
            SearchCondition["ticket_name"] = ticket_name
        elif process_identification_name:
            SearchCondition["process_identification_name"] = []
            for item in process_identification_name:
                if item == "设备生产跟踪":
                    SearchCondition["process_identification_name"].append("设备生产跟踪")
                elif item == "开办及设备进场前施工流程":
                    SearchCondition["process_identification_name"].append("开办及设备进场前施工流程")
                elif item == "项目建设-施工准备-项目报建":
                    SearchCondition["process_identification_name"].append("施工准备")
                elif item == "项目建设-现场实施":
                    SearchCondition["process_identification_name"].append("项目建设-现场实施")
                elif item == "项目建设流程":
                    SearchCondition["process_identification_name"].append("项目建设流程")
        elif responsible_person:
            SearchCondition["responsible_person"] = responsible_person
        table_list = ["equipment_production_racking_process",  # 设备生产跟踪
                      "equipment_before_process",  # 开办及设备进场前施工流程
                      "construction_application_second_level",  # 项目建设-施工准备-项目报建
                      "special_program_data",  # 项目建设-现场实施
                      "risk_early_warning_data",  # 项目建设流程
                      "construction_application_second_level",  # 项目建设-施工准备-市电专项
                      "maintenance_upload_data",  # 接维：设计相关资料清单上传
                      ]
        self.process_list = []
        self.ticket_id_list = []
        for table_name in table_list:
            if table_name == 'equipment_production_racking_process':
                process_identification_name = "设备生产跟踪"
                process_identification = "equipment_production_tracking_management"
            elif table_name == 'equipment_before_process':
                process_identification_name = "开办及设备进场前施工流程"
                process_identification = "construction_before_equipment_arrival"
            elif table_name == 'construction_application_second_level':
                process_identification_name = "项目建设-施工准备-项目报建"
                process_identification = "project_construction_application"
            elif table_name == 'special_program_data':
                process_identification_name = "项目建设-现场实施"
                process_identification = "project_site_implementation"
            elif table_name == 'risk_early_warning_data':
                process_identification_name = "项目建设流程"
                process_identification = "project_construct_test"
            elif table_name == 'construction_application_second_level':
                process_identification_name = "项目建设-施工准备-市电专项"
                process_identification = "mains_power_special"
            elif table_name == 'maintenance_upload_data':
                process_identification_name = "接维：设计相关资料清单上传"
                process_identification = "maintenance_design_transmittal_checklist"
            sql = "SELECT %(id)s FROM %(table)s WHERE project_name='%(name)s'" % {
                'id': 'ticket_id'
                if table_name != 'risk_early_warning_data' else 'dcopsTicketId',
                'table': table_name,
                'name': project_name
            }
            query = db.get_all(sql)
            if query:
                for row in query:
                    ticket_id = row.get('ticket_id', row.get('dcopsTicketId'))
                    if ticket_id not in self.ticket_id_list:
                        self.ticket_id_list.append(ticket_id)
                        self.process_list.append({
                            "process_identification_name": process_identification_name,
                            "process_identification": process_identification,
                            "ticket_id": ticket_id,
                            "ticket_name": "",
                            "agency_name": "",
                            "responsible_person": "",
                            "cost_time": "",
                        })
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "Title": "",
                    "CurrentAllProcessUsers": "",
                    "CurrentTasks": "",
                    "StartProcessTime": "",
                    "EndTime": "",
                    "TicketId": "",
                    "InstanceId": "",
                    "TicketStatus": ""
                },
                "SearchCondition": {
                    "TicketId": self.ticket_id_list
                }
            }
        }
        query_result = gnetops.request(
            action="QueryData",
            method="Run",
            ext_data=query_data
        )
        for item in self.process_list:
            detail = next((d for d in query_result.get("List") if d['TicketId'] == item['ticket_id']), None)
            if detail:
                item['ticket_name'] = detail['Title']
                item['agency_name'] = detail['CurrentTasks']
                item['responsible_person'] = detail['CurrentAllProcessUsers'].split(',')[-1].strip() if detail[
                    'CurrentAllProcessUsers'] else ""
                data_instanceId = {
                    "InstanceId": detail['InstanceId']
                }
                res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
                start = datetime.datetime.strptime(res[-1]["CreateTime"], "%Y-%m-%d %H:%M:%S")
                if res[-1]["EndTime"]:
                    end = datetime.datetime.strptime(res[-1]["EndTime"], "%Y-%m-%d %H:%M:%S")
                else:
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    end = datetime.datetime.strptime(current_time, "%Y-%m-%d %H:%M:%S")
                delta = end - start
                total_seconds = int(delta.total_seconds())
                # 计算天数
                days = total_seconds // (24 * 3600)
                # 去掉天数后的秒数
                remaining_seconds = total_seconds % (24 * 3600)
                # 计算小时数
                hours = remaining_seconds // 3600
                # 去掉小时后的秒数
                remaining_seconds %= 3600
                # 计算分钟数
                minutes = remaining_seconds // 60
                if days > 0:
                    item['cost_time'] = f"{days}天{hours}时"
                elif hours > 0:
                    item['cost_time'] = f"{hours}时{minutes}分"
                else:
                    item['cost_time'] = f"{minutes}分"
                if detail['TicketStatus']:
                    if detail["TicketStatus"] == "OPEN":
                        item["ticket_status"] = "进行中"
                    elif detail["TicketStatus"] == "END":
                        item["ticket_status"] = "已完成"
                else:
                    item["ticket_status"] = ""
        process_list_end = self.process_list
        if SearchCondition:
            process_list_end = [
                item for item in self.process_list
                if all(
                    (
                            (key == 'process_identification_name' and item['process_identification_name'] in
                             SearchCondition[key]) or
                            (key == 'ticket_name' and SearchCondition[key] in item.get(key, "")) or
                            (key == 'responsible_person' and SearchCondition[key] in item.get(key, "")) or
                            (item.get(key) is None or SearchCondition[key] is None or item.get(key) == SearchCondition[
                                key])
                    )
                    for key in SearchCondition.keys()
                )
            ]
        # 分页处理
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        data_list = process_list_end[start_index:end_index]

        return {"data_list": data_list, "total_results": len(process_list_end)}

    def get_info(self, project_name, page_size=None, page_number=None, ticket_id=None, ticket_name=None,
                 process_identification_name=None, responsible_person=None, status=None):
        # 设置默认分页参数
        if not page_size:
            page_size = 10
        if not page_number:
            page_number = 1

        db = mysql.new_mysql_instance("tbconstruct")

        # 构建动态 SQL 查询
        sql = """
                SELECT 
                    a.TicketId, ProcessDefinitionKey, SchemeNameCn, Title, TicketStatus, CurrentTasks,
                    CurrentAllProcessUsers, CreateTime, EndTime, t.PartnerTicketId 
                FROM 
                    all_construction_ticket_data a 
                LEFT JOIN 
                    tencent_partner_ticket_mapping t ON a.TicketId = t.TicketId
                WHERE 
                    Title LIKE %s
            """
        params = [f"%{project_name}%"]

        # 动态添加过滤条件
        if ticket_id:
            sql += " AND a.TicketId = %s"
            params.append(ticket_id)
        else:
            sql += " AND %s IS NULL"
            params.append(None)

        if ticket_name:
            sql += " AND Title LIKE CONCAT('%%', %s, '%%')"
            params.append(ticket_name)
        else:
            sql += " AND %s IS NULL"
            params.append(None)

        expanded_process_names = []
        if process_identification_name:
            # 获取部分多版本流程名称对应关系
            mapping_process_dict = config.get_config_map("multi_version_process_name_correspondence")
            # 将传入的流程名称映射为包含多版本的流程名称列表
            for item in process_identification_name:
                if item in mapping_process_dict:
                    # 如果流程名称在映射字典中，扩展为多版本流程名称
                    expanded_process_names.extend(mapping_process_dict[item])
                else:
                    # 如果不在映射字典中，保留原始流程名称
                    expanded_process_names.append(item)

            # 确保 expanded_process_names 是非空的，避免 SQL 查询错误
            if expanded_process_names:
                sql += " AND SchemeNameCn IN %s"
                params.append(tuple(expanded_process_names))
        else:
            sql += " AND %s IS NULL"
            params.append(None)

        if responsible_person:
            sql += " AND FIND_IN_SET(%s, CurrentAllProcessUsers)"
            params.append(responsible_person)
        else:
            sql += " AND %s IS NULL"
            params.append(None)

        # 添加 status 筛选条件
        if status == "已完成":
            sql += " AND TicketStatus = 'END'"
        elif status == "进行中":
            sql += " AND TicketStatus != 'END'"

        # 添加排序规则：进行中的优先返回，已完成的放在后面
        sql += """
                ORDER BY 
                    CASE 
                        WHEN a.TicketStatus = 'END' THEN 1 
                        ELSE 0 
                    END ASC
            """

        # 添加分页
        sql += " LIMIT %s OFFSET %s"
        params.extend([page_size, (page_number - 1) * page_size])

        # 执行查询
        query_data = db.get_all(sql, tuple(params))

        # 初始化结果列表
        process_list = []
        for item in query_data:
            create_time = item.get("CreateTime")
            end_time = item.get("EndTime")
            start = datetime.datetime.strptime(create_time, "%Y-%m-%d %H:%M:%S")
            if end_time != '0000-00-00 00:00:00':
                end = datetime.datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            else:
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                end = datetime.datetime.strptime(current_time, "%Y-%m-%d %H:%M:%S")

            delta = end - start
            total_seconds = int(delta.total_seconds())
            days = total_seconds // (24 * 3600)
            remaining_seconds = total_seconds % (24 * 3600)
            hours = remaining_seconds // 3600
            remaining_seconds %= 3600
            minutes = remaining_seconds // 60

            cost_time = (
                f"{days}天{hours}时" if days > 0 else
                f"{hours}时{minutes}分" if hours > 0 else
                f"{minutes}分"
            )

            process_list.append({
                "ticket_id": item.get("TicketId"),
                "ticket_name": item.get("Title"),
                "agency_name": item.get("CurrentTasks"),
                "ticket_status": "已完成" if item.get("TicketStatus") == "END" else "进行中",
                "process_identification_name": item.get("SchemeNameCn"),
                "process_identification": item.get("ProcessDefinitionKey"),
                "responsible_person": item.get("CurrentAllProcessUsers"),
                "cost_time": cost_time,
                "partner_ticket_id": str(item.get("PartnerTicketId")) if item.get("PartnerTicketId") is not None else ""
            })

        # 获取总记录数
        total_sql = """
                SELECT COUNT(*) AS total 
                FROM all_construction_ticket_data
                WHERE Title LIKE %s
            """
        total_params = [f"%{project_name}%"]
        if ticket_id:
            total_sql += " AND TicketId = %s"
            total_params.append(ticket_id)
        if ticket_name:
            total_sql += " AND Title LIKE CONCAT('%%', %s, '%%')"
            total_params.append(ticket_name)
        if process_identification_name:
            total_sql += " AND SchemeNameCn IN %s"
            total_params.append(tuple(expanded_process_names))
        if responsible_person:
            total_sql += " AND FIND_IN_SET(%s, CurrentAllProcessUsers)"
            total_params.append(responsible_person)
        # 添加 status 筛选条件
        if status == "已完成":
            total_sql += " AND TicketStatus = 'END'"
        elif status == "进行中":
            total_sql += " AND TicketStatus != 'END'"

        total_results = db.get_row(total_sql, tuple(total_params))["total"]

        return {"data_list": process_list, "total_results": total_results}

    def partner_work_order_status_query(self, partner_ticket_id, project_name):
        sql = "SELECT project,campus FROM risk_early_warning_data WHERE project_name = '%s'" % project_name
        db = mysql.new_mysql_instance("tbconstruct")
        project_info = db.get_row(sql)
        (project, campus) = (project_info.get('project'), project_info.get('campus'))
        check = IsItOrNotCooperativePartner()
        variables = check.is_it_partner_general_check(campus=campus, project=project)
        Facilitator = variables.get('Facilitator')
        system_type = variables.get('system_type')
        system_id = variables.get('system_id')
        query = CheckProgressWorkOrder()
        info = query.cooperative_partner_work_order_progress(Facilitator, system_id, system_type, partner_ticket_id)

        # return {"info": info, "variables": variables}

        # 默认值
        result_data = []
        if not info:
            return {
                "code": -1,
                "data": result_data,
                "msg": "接口调用失败"
            }

        # 情况一：dcops {"result": "{\"code\":...}"}
        if 'data' in info:
            data = info.get("data", {})
            result = json.loads(data.get("result", {}))
            code = result.get("code")
            message = result.get("message")
            if code == 0:
                inner_data = result.get("data")
                for item in inner_data:
                    current_task = item.get("CurrentNode")
                    process_user = item.get("Processor")
                    result_data.append({
                        "current_task": current_task,
                        "process_user": process_user
                    })

        # 情况二：外部接口 {"ErrorCode": 0, "Data": {...}}
        elif 'ErrorCode' in info:
            code = info.get("ErrorCode")
            message = info.get("Message") or "请求成功"

            if code == 0:
                data_content = info.get("Data", {})
                for item in data_content:
                    current_task = item.get("CurrentNode", "")
                    process_user = item.get("Processor", "")
                    result_data.append({
                        "current_task": current_task,
                        "process_user": process_user
                    })

        else:
            return {
                "code": -1,
                "data": result_data,
                "msg": "接口返回格式不支持"
            }

        # 根据 code 返回最终结果
        if code == 0:
            return {
                "code": 0,
                "data": result_data,
                "msg": "成功"
            }
        else:
            return {
                "code": -1,
                "data": result_data,
                "msg": message or "接口调用失败"
            }


class ProcessStatusUpdate(object):
    def __init__(self):
        self.logger = logging.getLogger('ConstructionWorkOrder')
        self.logger.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

    def status_update(self, work_order_number, work_order_heading, process_name, handler):
        """
            建设工单——工单升级
        """
        try:
            personnel_list = handler.split(';')  # 将字符串拆分为列表
            email_list = [email + "@tencent.com" for email in personnel_list]  # 添加后缀
            text = f'【流程名称】：{process_name}\n' \
                   f'【任务名称】：{work_order_heading}\n' \
                   '【待办详情内网链接】：https://dcops.woa.com/operateManage/business/' \
                   f'ToDoDetails?params={work_order_number}\n' \
                   '【待办详情外网链接】：https://dcops.idc.tencent.com/operateManage/business/' \
                   f'details?params={work_order_number}\n' \
                   '（注：如内网链接无权限访问，请点击外网链接查看待办）'
            rich_text_template = f'【流程名称】：{process_name}<br>' \
                                 f'【任务名称】：{work_order_heading}<br>' \
                                 '【待办详情内网链接】：https://dcops.woa.com/operateManage/business/' \
                                 f'ToDoDetails?params={work_order_number}<br>' \
                                 '【待办详情外网链接】：https://dcops.idc.tencent.com/operateManage/business/' \
                                 f'details?params={work_order_number}<br>' \
                                 '（注：如内网链接无权限访问，请点击外网链接查看待办）'
            tof.send_email(sendTitle=work_order_heading, msgContent=rich_text_template, sendTo=email_list)
            for i in personnel_list:
                ChatOpsSend(
                    user_name=i,
                    msg_content=text,
                    msg_type='text',
                    des_type='single'
                ).call()
            response = {
                'code': 200,
                'msg': '催办成功'
            }
        except ValueError as ve:
            response = {
                'code': 500,
                'msg': f'催办失败：{ve}'
            }

        except TypeError as te:
            response = {
                'code': 500,
                'msg': f'催办失败：{te}'
            }
        return response


class ProjectEquipmentInformationDeliveryQuery(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def equipment_information_query(self):
        db = mysql.new_mysql_instance("tbconstruct")

        # 获取所有项目编号及其对应的项目名称
        sql = "SELECT project_name, item_number FROM risk_early_warning_data"
        query_list = db.get_all(sql)
        item_number_list = [{'item_number': item['item_number'], 'project_name': item['project_name']}
                            for item in query_list if item.get('item_number')]
        item_number_list.append({"item_number": "B8-1", "project_name": "南京江宁B4-1"})

        # 查询product_query_info表的所有记录
        existing_records_sql = "SELECT * FROM product_query_info"
        existing_records = db.get_all(existing_records_sql)

        # 修改键为三个字段的组合
        existing_dict = {(record['project_name'], record['category_name'], record['instance_id']): record for record in
                         existing_records}

        insert_data = []
        update_data = []
        all_query_data = []

        for project in item_number_list:
            query_data = gnetops.request(
                action="Tbconstruct",
                method="QueryProductDeliveryInfo",
                ext_data={"ProjectCode": project["item_number"]},
                scheme="ifob-infrastructure"
            )
            if query_data:
                data_info = query_data.get("data", [])
                all_query_data.append(query_data)

                for item in data_info:
                    # 使用三个字段构造key
                    key = (str(project.get("project_name")),
                           str(item.get("category_name")),
                           str(item.get("instance_id")))

                    existing_record = existing_dict.get(key)

                    if not existing_record:
                        if item.get("state") == "完成":
                            insert_data.append({
                                "batch": str(item.get("batch", "")),
                                "category_name": str(item.get("category_name", "")),
                                "delivery_num": str(item.get("delivery_num", "")),
                                "file_name": str(item.get("file_name", "")),
                                "file_url": str(item.get("file_url", "")).replace(
                                    "https://idc-sc2-1258344706.cos-internal.ap-guangzhou.tencentcos.cn",
                                    "https://idc-sc2-1258344706.cos.ap-guangzhou.myqcloud.com"
                                ),
                                "instance_id": str(item.get("instance_id", "")),
                                "project_name": str(project.get("project_name", "")),
                                "review_time": str(item.get("review_time", "")),
                                "state": str(item.get("state", "")),
                                "submit_time": str(item.get("submit_time", "")),
                                "supplier": str(item.get("supplier", "")),
                                "ticket_id": str(item.get("ticket_id", "")),
                                "delivery_total": str(item.get("delivery_total", "")),
                            })
                    else:
                        # 仅检查batch和file_url的变化
                        new_batch = str(item.get("batch", ""))
                        old_batch = str(existing_record.get("batch", ""))

                        new_file_url = str(item.get("file_url", "")).replace(
                            "https://idc-sc2-1258344706.cos-internal.ap-guangzhou.tencentcos.cn",
                            "https://idc-sc2-1258344706.cos.ap-guangzhou.myqcloud.com"
                        )
                        old_file_url = str(existing_record.get("file_url", ""))

                        changes = {}
                        if new_batch != old_batch:
                            changes["batch"] = new_batch
                        if new_file_url != old_file_url:
                            changes["file_url"] = new_file_url

                        if changes:
                            # 添加三个条件字段
                            changes.update({
                                "project_name": str(project.get("project_name")),
                                "category_name": str(item.get("category_name")),
                                "instance_id": str(item.get("instance_id"))
                            })
                            update_data.append(changes)

        # 更新操作
        if update_data:
            for update_item in update_data:
                conditions = {
                    "project_name": update_item["project_name"],
                    "category_name": update_item["category_name"],
                    "instance_id": update_item["instance_id"]
                }
                data = {
                    "batch": update_item.get("batch"),
                    "file_url": update_item.get("file_url")
                }
                # 仅更新batch和file_url字段
                db.update("product_query_info", data=data, conditions=conditions)

        # 插入新记录
        if insert_data:
            db.begin()
            db.insert_batch("product_query_info", insert_data)
            db.commit()

        variables = {
            "insert_data": insert_data,
            "update_data": update_data,
            "all_query_data": all_query_data,
            "item_number_list": item_number_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def equipment_information_query_by_project(self, project_name, campus, page_size, page_number):
        db = mysql.new_mysql_instance("tbconstruct")
        project_name = campus + project_name
        sql = (f"SELECT batch,supplier,category_name,file_url,submit_time FROM product_query_info "
               f"WHERE project_name='{project_name}'")
        shipping_list = db.get_all(sql)
        # 初始化字典来存储分组后的数据
        grouped_data = defaultdict(lambda: defaultdict(list))

        for item in shipping_list:
            category_name = item.get("category_name")
            supplier = item.get('supplier')
            batch = item.get('batch')
            file_url = item.get('file_url')
            submit_time = item.get('submit_time')

            # 累积到相应的分组中
            grouped_data[category_name][supplier].append({
                'batch': batch,
                'upload_shipping_information': [file_url],
                'shipping_time': submit_time
            })

        # 构建最终的输出格式
        shipping_info = []

        for equipment_name, manufacturers in grouped_data.items():
            equipment_manufacturer_list = []

            for manufacturer, batches in manufacturers.items():
                batch_list = []

                # 合并相同批次的信息（如果有的话）
                batch_dict = defaultdict(list)
                for batch_info in batches:
                    batch_key = (batch_info['batch'], batch_info['shipping_time'])
                    batch_dict[batch_key].extend(batch_info['upload_shipping_information'])

                for (batch, shipping_time), urls in batch_dict.items():
                    batch_list.append({
                        'batch': batch,
                        'shipping_time': shipping_time,
                        'upload_shipping_information': urls
                    })

                equipment_manufacturer_list.append({
                    'equipment_manufacturer': manufacturer,
                    'batch_list': batch_list
                })

            shipping_info.append({
                'equipment_name': equipment_name,
                'equipment_manufacturer_list': equipment_manufacturer_list
            })
        # 计算总数
        total = len(shipping_info)
        # 计算总页数
        total_pages = (len(shipping_info) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = shipping_info[start_index:end_index]
        data = {
            # 发货数量
            'shipping_number': total,
            # 发货详细信息
            'shipping_list': page_list,
        }
        return data


class TestSendEmailTBBid(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 数据写入
        # # 生成导出表的表头
        # column_names = ['project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level', 'four_level']
        # supplier_cnt = self.variables.get('supplier_cnt', 0)
        # rater_cnt = self.variables.get('rater_cnt', 0)
        #
        # 邮件通知
        project_name = self.variables.get('project_name', '')
        project_desc = self.variables.get('project_desc', '')
        desc = self.variables.get('desc', '')
        yb_url = self.variables.get('yb_url', '')
        ms_url = self.variables.get('ms_url', '')

        msg_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件通知</title>
                <style>
                    .title{{
                        margin-bottom: 10px;
                        color: #212529;
                        text-align: center;
                    }}
                    .info-title{{
                        display: inline-block;
                        width: 7rem;
                        text-align: right;
                        font-weight: bold;
                    }}
                    .table-head {{
                        color: #fff;
                        background-color: #343a40;
                        border-color: #454d55;
                        padding: 0.75rem;
                        font-size: 0;
                        font-weight: bold;
                        text-align: center;
                    }}
                    .table-head-item{{
                        display: inline-block;
                        font-size: 16px;
                    }}
                    .row-box{{
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                        font-size: 0;
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }}
                    .row-item{{
                        display: inline-block; font-size: 16px;
                    }}
                    .approve-info-title01{{
                        display: inline-block;
                        width: 15%;
                        text-align: right; font-weight: bold;
                    }}
                    .approve-info-title02{{
                        display: inline-block; vertical-align: top; width: 15%; font-size: 16px; text-align: right; font-weight: bold;
                    }}
                    .approve-info-remark02{{
                        display: inline-block; vertical-align: top; width: 85%; font-size: 16px;
                    }}
                    .attachment-box{{
                        width: 15%;
                        text-align: right;
                        padding-left: 40px;
                    }}
                </style>
            </head>
            <body>
                <div style="font-size: 16px;">
                    <h2 class="title">{project_name}</h2>
                    <div>
                        <h3>1. 基础信息</h3>
                        <p><span class="info-title">项目名：</span>{project_name}</p>
                        <p><span class="info-title">项目概况：</span>{project_desc}</p>
                    </div>
                    <div>
                        <h3>2. {desc}</h3>
                    </div>
                </div>
            </body>
            </html>
            """
        title = process_data.get('title')
        receivers = process_data.get('receivers')
        self.workflow_detail.add_kv_table("renming", {"receivers": receivers})
        # receivers = ["<EMAIL>"]
        receivers_list = receivers.split(";")
        for i in receivers_list:
            i = i + "@tencent.com"

        clarification_document_table = [{
            "clarification_document_name": "应标评分表.xlsx",
            "clarification_document": f"{yb_url}"
        },
            {
                "clarification_document_name": "面试评分表.xlsx",
                "clarification_document": f"{ms_url}"
            }
        ]
        save_directory = '/path/to/save'
        os.makedirs(save_directory, exist_ok=True)  # 创建保存路径

        technical_dict = {}  # 定义空字典
        combined_dict = {}  # 定义空字典

        for clarification_document in clarification_document_table:
            clarification_document_name = clarification_document["clarification_document_name"]
            clarification_document_url = clarification_document["clarification_document"]

            relative_url = clarification_document_url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            save_file_path = os.path.join(save_directory, new_file_name)

            COSLib.download_2(file_name=relative_url, save_to_file=save_file_path)

            technical_dict[save_file_path] = clarification_document_name  # 将附件添加到字典中

        combined_dict.update(technical_dict)

        tof.send_attachment_email(
            filePathAndfileName=combined_dict,
            sendTitle=title,
            msgContent=msg_content,
            sendTo=receivers_list)
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(
            task_id=self.ctx.task_id,
            variables={
                'msg_content': msg_content,
                'title': title,
                "receivers": receivers,
            }
        )
