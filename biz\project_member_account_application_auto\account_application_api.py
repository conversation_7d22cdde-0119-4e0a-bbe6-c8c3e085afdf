from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class AutoAccountApplicationApi(object):
    # 判断是否有重复的账号（集成商、项管、监理） del_flag=1为删除的账号
    @staticmethod
    def DuplicateAccount(table_list):
        duplicate_account_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in table_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    name = ""
                    project_list = []
                    for account in result:
                        role = account.get("role")
                        # 只判断集成商、项管、监理
                        if "集成商" in role or "项管" in role or "监理" in role:
                            name = account.get("name")
                            project_list.append(account.get("project_name"))
                    duplicate_account_list.append(
                        f"{name}已在{(',').join(project_list)}中开通账号"
                    )
        return duplicate_account_list

    # 自建-判断是否有重复的账号（集成商、项管、监理） del_flag=1为删除的账号
    # 判断已存在的和新申请的姓名是否一致
    @staticmethod
    def account_validate(table_list):
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in table_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    name = info.get("name")
                    phone = info.get("phone")
                    for account in result:
                        # 重复账号只判断集成商、项管、监理
                        exist_role = account.get("role")
                        exist_project_name = account.get("project_name")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_account = account.get("account")
                        if (
                            "集成商" in exist_role
                            or "项管" in exist_role
                            or "监理" in exist_role
                        ):
                            # 判断是否已过初验
                            # 强校验：项目已初验就可以重复开通，未初验填了备注也不可重复开通
                            flag = AutoAccountApplicationApi.project_is_initial_inspection_flag(
                                exist_project_name
                            )
                            status = "已初验" if flag else "未初验"
                            account_validate_list.append(
                                f"【已开通账号】【{status}】"
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        if name != exist_name:
                            account_validate_list.append(
                                f"【同手机号不同姓名】({name}, {phone})对应已开通账号："
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        return account_validate_list

    # 合建-判断是否有重复的账号（总包、合建方、监理） del_flag=1为删除的账号
    # 判断已存在的和新申请的姓名是否一致
    @staticmethod
    def account_validate_joint(table_list):
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in table_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    name = info.get("name")
                    phone = info.get("phone")
                    for account in result:
                        # 重复账号只判断总包、合建方、监理
                        exist_role = account.get("role")
                        exist_project_name = account.get("project_name")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_account = account.get("account")
                        if (
                            "总包" in exist_role
                            or "合建方" in exist_role
                            or "监理" in exist_role
                        ):
                            flag = AutoAccountApplicationApi.project_is_initial_inspection_flag(
                                exist_project_name
                            )
                            status = "已初验" if flag else "未初验"
                            account_validate_list.append(
                                f"【已开通账号】【{status}】"
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        if name != exist_name:
                            account_validate_list.append(
                                f"【同手机号不同姓名】({name}, {phone})对应已开通账号："
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        return account_validate_list

    # 通过手机号查询账号
    @staticmethod
    def get_account_by_phone(table_list):
        phoneObj = {}
        for item in table_list:
            if item.get("phone"):
                phoneObj[item.get("phone")] = {}
        phoneList = list(phoneObj.keys())
        data = {"mobile": phoneList}
        query_result = gnetops.request(
            action="Human", method="BatchQueryWeWorkUserid", data=data
        )
        if query_result:
            for item in query_result:
                if item.get("telephone"):
                    phoneObj[item.get("telephone")] = item
        for item in table_list:
            if item.get("phone"):
                keji_account = phoneObj.get(item["phone"]).get("keji_account")
                keji_user_name = phoneObj.get(item["phone"]).get("keji_user_name")
                if keji_account and keji_user_name:
                    item["keji_account_flag"] = True
                    item["userid"] = keji_account
                    item["username"] = keji_user_name
                    item["nameFlag"] = (
                        True
                        if item.get("name") and item.get("name") in keji_user_name
                        else False
                    )
                    item["accountFlag"] = (
                        True
                        if item.get("application_account")
                        and str(item.get("application_account")) in keji_account
                        else False
                    )
                else:
                    item["keji_account_flag"] = False
                    item["nameFlag"] = False
                    item["accountFlag"] = False
        return table_list

    # 调接口生成userid
    @staticmethod
    def get_account(table_list):
        for item in table_list:
            if (
                not item.get("userid")
                and item.get("name")
                and item.get("service_provider")
                and item.get("position")
            ):
                data = {
                    "NbrokerData": {
                        "context": {
                            "service_name": "OutsourcingAccountCreate",
                            "method_name": "get_account",
                        },
                        "args": {
                            "full_name": item.get("name"),
                            "account_type": "IDC建设服务人员",
                            "supply_name": item.get("service_provider"),
                            "position": item.get("position"),
                            "company": "犀牛鸟有限责任公司",
                        },
                    },
                    "ServiceUrl": "http://ibroker-human:8080/nBroker/api/v1/task",
                }
                query_result = gnetops.request(
                    action="Nbroker", method="UrlProxy", ext_data=data
                )
                data = query_result.get("result").get("data")
                item["userid"] = data.get("userid")
                item["username"] = data.get("name")
                item["user_position"] = item.get("position")
        return table_list

    # 查询企业微信账号创建流程建单工单状态和创建结果
    @staticmethod
    def query_create_ticket_info(member_list):
        # 重复工单可直接复用结果，无需重新查询
        ticket_status_dict = {}
        # 重复账号可直接复用结果，无需重新查询
        create_account_status_dict = {}
        for item in member_list:
            ticket_id = item.get("ticket_id")
            if ticket_id:
                userid = item.get("userid")
                if ticket_id not in ticket_status_dict:
                    query_data = {
                        "SchemaId": "ticket_base",
                        "Data": {
                            "ResultColumns": {"TicketStatus": "", "TicketId": ""},
                            "SearchCondition": {"TicketId": [str(ticket_id)]},
                        },
                    }
                    query_result = gnetops.request(
                        action="QueryData", method="Run", ext_data=query_data
                    )
                    # item.setdefault("query_result", query_result)
                    result_list = query_result.get("List")
                    for res in result_list:
                        if res.get("TicketId") == str(ticket_id):
                            TicketStatus = res.get("TicketStatus")
                            ticket_status = (
                                "运行中"
                                if res.get("TicketStatus") == "OPEN"
                                else "已结单"
                            )
                            item["ticket_status"] = ticket_status
                            ticket_status_dict[ticket_id] = ticket_status
                            # 可以认为除了OPEN是运行中外，其它的都是已结单
                            # 已结单的需查询企微账号创建结果
                            if TicketStatus != "OPEN":
                                data = {
                                    "userid": userid,
                                    "company": "犀牛鸟有限责任公司",
                                }
                                result = gnetops.request(
                                    action="WeWork", method="QueryWeWorkUser", data=data
                                )
                                if (
                                    result["Result"]["errcode"] == 0
                                    and result["Result"]["userid"] == userid
                                ):
                                    item["create_account_status"] = "创建成功"
                                    create_account_status_dict[userid] = "创建成功"
                                else:
                                    item["create_account_status"] = "创建失败"
                                    create_account_status_dict[userid] = "创建失败"
                else:
                    item["ticket_status"] = ticket_status_dict.get(ticket_id)
                    item["create_account_status"] = create_account_status_dict.get(
                        userid
                    )
        return member_list

    @staticmethod
    def query_exist_account(member_list):
        """
        判断项目中是否开通过同手机号、同姓名、同角色的账号
        """
        for info in member_list:
            db = mysql.new_mysql_instance("tbconstruct")
            project_name = info.get("campus_name") + info.get("project_name")
            phone = info.get("phone")
            name = info.get("name")
            role = info.get("role")
            sql = (
                f"SELECT * FROM project_role_account WHERE "
                f"project_name='{project_name}' "
                f"and phone='{phone}' "
                f"and name='{name}' "
                f"and role='{role}' "
                f"and del_flag=0"
            )
            result = db.get_all(sql)
            info["existFlag"] = "是" if result else "否"
        return member_list

    @staticmethod
    def project_is_initial_inspection_flag(project_name):
        """
        判断重复账号所在项目是否初验
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            f"SELECT * FROM construction_feedback_to_xingchen "
            f"WHERE project_name = '{project_name}' AND type = '初验' "
        )
        result = db.get_all(sql)
        flag = bool(result)
        return flag
