import shutil
import threading
import time
import qrcode
import zipfile
import os
from PIL import Image
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from openpyxl import Workbook
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, tof, idcdb, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.decive_signage_information.my_coslib import Tool


class CabinetQRCodeSign(object):
    """
        机柜标牌：二维码标牌
    """

    def __init__(self):
        super(CabinetQRCodeSign, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_rack_info(self, project_name):

        puth_gen = os.path.dirname(os.path.abspath(__file__))
        file_path = f"{puth_gen}/TencentSans-W7.ttf"
        self.workflow_detail.add_kv_table('文件路径', {'message': file_path})

        # 获取模组名称
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship WHERE project_name ='%s'" % project_name
        mozu_name_list = db.get_all(sql)
        if mozu_name_list:
            mozu_name = mozu_name_list[0].get('module_name')
        else:
            return {'code': -1, 'mag': '未获取到模组名称'}
        # 获取PM和数经
        tree_sutra = ''
        PM = ''
        send_list = []
        sql1 = "SELECT tree_sutra,PM FROM project_team_information_data WHERE project_name='%s'" % project_name
        personnel_list = db.get_all(sql1)
        if personnel_list:
            tree_sutra = personnel_list[0].get('tree_sutra')
            PM = personnel_list[0].get('PM')
            if tree_sutra:
                send_list += tree_sutra.split(';')
            if PM:
                send_list += PM.split(';')

        """
            获取机架信息
        """
        rack_info = idcdb.get_list(table="rack", fields=["mozu_beginTime", "rack_id", "rack_belongtype", "rack_code"],
                                   search_condition={"mozu_name": [mozu_name]})
        self.workflow_detail.add_kv_table('机架信息',
                                          {'message': rack_info})
        """
        rack_info = [{
                "mozu_beginTime": "2020-09-10",
                "rack_belongtype": "R",
                "rack_id": "561231"
            },]
        """

        rack_id_list = []
        if rack_info:
            mozu_str = rack_info[0].get("mozu_beginTime")
            date_object = mozu_str.split('-')
            if len(date_object) > 2 and len(date_object[0]) == 4:
                data = date_object[0][2:4] + date_object[1]
            else:
                self.workflow_detail.add_kv_table('未获取到机架时间格式错误',
                                          {'message': "未获取到机架时间格式错误"})
            idcowner = rack_info[0].get("rack_belongtype")
            for rack in rack_info:
                rack_id_list.append(int(rack.get("rack_id")))

            info = gnetops.request(action="Tbconstruct",
                                   method="QueryRackResourceID",
                                   ext_data={
                                       "MozuDate": data,
                                       "IDCOwner": idcowner,
                                       "RackId": rack_id_list
                                   })
            self.workflow_detail.add_kv_table('机架标识',
                                              {'message': info})
            if info:
                info_result = info.get('result')
                # 处理数据
                num = 0
                for i in info_result:
                    num += 1
                    i['num'] = num
                    for j in rack_info:
                        if str(i.get('rack_id')) == j.get('rack_id'):
                            j['rack_res_id'] = i.get('rack_res_id')
                            i["rack_code"] = j.get('rack_code')

                n = len(info_result)
                part_size = n // 5  # 每份的大小
                remaining = n % 5  # 剩余的元素个数

                result = []
                start_index = 0

                for i in range(5):
                    end_index = start_index + part_size
                    # 如果剩余元素大于0，则在当前部分添加一个额外的元素
                    if remaining > 0:
                        end_index += 1
                        remaining -= 1  # 减少剩余元素的计数
                    part = info_result[start_index:end_index]
                    result.append(part)
                    start_index = end_index
                variables = {
                    "rack_info": rack_info,
                    "send_list": send_list,
                    # "info": result,
                    "file_path": file_path,
                    "info_result": info_result
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
            else:
                self.workflow_detail.add_kv_table('获取机架信息',
                                                  {"success": False, "data": "未获取到机架资源"})
        else:
            self.workflow_detail.add_kv_table('获取机架信息',
                                              {"success": False, "data": "未获取到机架id"})

    def square(self, content, code, image_path, logo_path):

        # 1、创建一个QRCode对象
        qr1 = qrcode.QRCode(
            version=None,  # 版本号，控制二维码的大小
            error_correction=qrcode.constants.ERROR_CORRECT_H,  # 纠错级别，控制二维码的容错能力
            box_size=10,  # 每个小格子的像素大小
            border=1,  # 边框的格子数
        )
        # 2、添加数据到QRCode对象
        qr1.add_data(content)
        # 3、生成QRCode矩阵
        qr1.make(fit=True)


        # 将二维码转换为字节流
        qr_img1 = qr1.make_image(fill_color="black", back_color="white").convert("RGBA")
        qr_img1 = qr_img1.resize((732, 732))  # 1*1cm
        # 打开中心图标
        logo = Image.open(logo_path)
        # 计算Logo图像的大小
        logo_size = qr_img1.size[0] // 4
        logo_img = logo.resize((logo_size, logo_size), Image.ANTIALIAS)

        # 计算Logo图像的位置
        logo_position = ((qr_img1.size[0] - logo_size) // 2, (qr_img1.size[1] - logo_size) // 2)

        # 将Logo图像粘贴到QRCode图像上 end
        qr_img1.paste(logo_img, logo_position, mask=logo_img)
        # 保存
        if not os.path.exists(image_path):
            os.makedirs(image_path)
        qr_img1.save(f"{image_path}" + "/" + content + "+" + code + ".png")
        self.workflow_detail.add_kv_table('图片保存',
                                          {'message': f"{image_path}" + "/" + content + "+" + code + ".png"})
        return "成功"

    def process_dataset(self, ws, info, image_path, logo_path):
        # 创建一个QRCode对象
        for i in info:
            num = i.get('num')
            name = i.get("rack_id")
            content = i.get("rack_res_id")
            code = i.get("rack_code")
            if content:
                self.square(content, code, image_path, logo_path)
            location = f'A{num}'
            location2 = f'B{num}'
            location3 = f'C{num}'
            ws[location].value = name
            ws[location2].value = content
            ws[location3].value = code
        return '成功'

    def generate_QR_square(self, info, project_name, send_list):
        puth_gen = os.path.dirname(os.path.abspath(__file__))
        logo_path = f"{puth_gen}/rack_qrcode_logo.png"
        # 存放图片的文件夹
        image_path = f"{puth_gen}/{project_name}-img"

        if not os.path.exists(image_path):
            os.makedirs(image_path)
        self.workflow_detail.add_kv_table('图片文件夹保存地址', {'message': image_path})
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        # 创建线程来处理数据集
        # # 创建线程来处理数据集
        threads = []
        for i in info:
            t = threading.Thread(target=self.process_dataset, args=(ws, i, image_path, logo_path))
            t.start()
            time.sleep(1)
            threads.append(t)
        # 等待所有线程完成
        for t in threads:
            t.join()

        with zipfile.ZipFile(f'{puth_gen}/{project_name}-img.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(image_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    zipf.write(file_path, os.path.relpath(file_path, image_path))
        # 保存工作簿
        wb.save(f'{puth_gen}/{project_name}-rack.xlsx')
        self.workflow_detail.add_kv_table('excel保存', {'message': 'excel保存'})
        for j in range(len(send_list)):
            send_list[j] += "@tencent.com"
        try:
            image = Tool.uploadFile(f'{puth_gen}/{project_name}-img.zip')
            image_url = "https://test.otob.dcops.qq.com/relay/cosfile/" + image
            self.workflow_detail.add_kv_table('图片压缩包COS链接', {'message': image_url})
            excel = Tool.uploadFile(f'{puth_gen}/{project_name}-rack.xlsx')
            excel_url = "https://test.otob.dcops.qq.com/relay/cosfile/" + excel
            self.workflow_detail.add_kv_table('excelCOS链接', {'message': excel_url})

            tof.send_attachment_email(filePathAndfileName={
                f'{puth_gen}/{project_name}-rack.xlsx': f"{project_name}rack.xlsx",
                f'{puth_gen}/{project_name}-img.zip': f"{project_name}-img.zip"
            },
                sendTitle=f"{project_name}机架信息",
                msgContent=f"机架信息链接：{excel_url}\n"
                           f"标牌图片压缩包：{image_url}\n",
                sendTo=send_list)
            try:
                os.remove(f'{puth_gen}/{project_name}-img.zip')
                shutil.rmtree(f'{puth_gen}/{project_name}-img')
                self.workflow_detail.add_kv_table(
                    '正方形删除',
                    {'message': '文件夹、压缩包已删除'
                     })

                variables = {
                    'excel_url': excel_url,
                    'image_url': image_url,
                    'puth_gen': puth_gen
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
            except OSError as e:
                self.workflow_detail.add_kv_table('excel上传错误',
                                                  {"success": False,
                                                   "data": f"删除文件夹时发生错误: {e.strerror}"})
        except FileNotFoundError:
            self.workflow_detail.add_kv_table('excel上传错误', {"success": False, "data": "文件未找到"})
        except IOError as e:
            self.workflow_detail.add_kv_table('excel上传错误', {"success": False, "data": str(e)})

    def data_storage(self, project_name, ticket_id, excel_url, image_url):

        insert_data = {
            "project_name": project_name,
            "ticket_id": ticket_id,
            "excel_url": excel_url,
            "img_url": image_url
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("cabinet_label_information", insert_data)
        tb_db.commit()
        flow.complete_task(self.ctx.task_id, variables={"insert_data": insert_data})


class CabinetQRCodeSignPM(AjaxTodoBase):
    """
            获取整改责任人信息
        """

    def __init__(self):
        super(CabinetQRCodeSignPM, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        pm = process_data.get("pm")
        variables = {
            "pm": pm
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
