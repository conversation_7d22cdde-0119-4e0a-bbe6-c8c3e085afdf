#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import copy
import datetime
import json
import os
import re
import time
import uuid

import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate

from biz.common.workflow_tools import cycle_task_abort
from biz.common.cycle_task_tools import ticket_is_over
from biz.construction_process.cos_lib import COSLib


def create_msg_content(project_name, pass_or_not='同意', operation_type='审核', reject_reason='') -> str:
    """
    生成通知信息的内容
    :param pass_or_not: 同意 or 驳回
    :param project_name: 项目名
    :param operation_type: 操作类型， 审核 or 评分 or 审批
    :param reject_reason: 驳回原因
    :return:
    """
    if pass_or_not == '同意':
        msg_content = '请尽快对 "' + project_name + '" ' + operation_type + ', ' + operation_type + \
                      '入口为：https://dcops.test.woa.com/appManage/tickets/needToDo'
    else:
        msg_content = (
                '项目："' + project_name + '"被驳回, 请重新评分， 评分入口为：https://dcops.test.woa.com/appManage/tickets/needToDo\n'
                + '驳回原因：' + reject_reason
        )
    return msg_content


class SelectReviewerTodoPass(AjaxTodoBase):
    # 指定评标小组

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 指定评标人、初审人、供应商
        variables_map, people_info = {}, {}  # people_info 保存 供应商名单和评标人名单
        project_name = process_data.get("project_name", '')  # 项目名称
        project_desc = process_data.get("project_desc", '')  # 项目概况
        rater_str = process_data.get("raters", '')  # 评标人
        rater_list = rater_str.split(';')
        rater_list = [item for item in rater_list if item]
        rater_leader = process_data.get("rater_leader", '')  # 评审组长 即初审人
        rater_list.append(rater_leader)
        # 防止用户在评分人的输入框中也将评审组长输入 对列表去重
        rater_list = list(set(rater_list))
        result = ';'.join(rater_list)
        suppliers = process_data.get('suppliers', '')
        # 创建新的供应商列表
        technical_score_summary = []
        for index, manufacturer in enumerate(suppliers):
            supplier_key = f"supplier_{chr(97 + index)}"  # chr(97) 是 'a'
            technical_score_summary.append({
                "supplier_key": supplier_key,
                "manufacturers": manufacturer,
            })
        supplier_list = process_data.get('suppliers', [])
        # 过滤用户输入  保证供应商名单 非空，前后不含空格
        supplier_list = [item.strip() for item in supplier_list if item]
        ybpf_table = process_data.get('ybpf_table')
        ybpf_table.append({
            "eval_content": "",
            "project": "厂家评审情况",
        })
        msg_content = create_msg_content(project_name=project_name, operation_type='评分')
        people_info.update({'rater_list': rater_list, 'supplier_list': supplier_list})
        variables_map.update({
            'people_info': people_info, 'msg_content': msg_content, 'project_name': project_name,
            'project_desc': project_desc, 'rater_leader': rater_leader, 'suppliers': suppliers, 'raters': rater_str,
            'ybpf_table': ybpf_table, 'technical_score_summary': technical_score_summary
        })
        variables_map.update({
            'result': result,
        })

        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidMarkTodoPass(AjaxTodoBase):
    # 子流程  评标人员对标进行打分

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        variables_map = {}
        ybpf_table = process_data.get('ybpf_table')
        variables_map.update({'ybpf_table': ybpf_table})
        technical_score_summary = []
        supplier_cnt = self.ctx.variables.get('supplier_cnt', 0)
        for i in range(supplier_cnt):
            supplier_key = f"supplier_{chr(ord('a') + i)}"
            access = "合格"
            for item in ybpf_table:
                if item.get(supplier_key) == "不合格":
                    access = "不合格"
            technical_score_summary.append({
                "supplier_key": supplier_key,
                "skill_score": access,
                "manufacturers": self.ctx.variables.get(f"supplier_{chr(ord('a') + i)}_name", ''),
            })


        cur_rater = self.ctx.get_var('cur_rater', '')
        # 获取当前时间
        current_time = datetime.datetime.now()
        # 格式化为 'yyyy-mm-dd hh:mm:ss'
        now = current_time.strftime('%Y-%m-%d %H:%M')
        # 移除项目为 "合计" 的条目
        ybpf_table = [item for item in ybpf_table if item.get('project') != '厂家评审情况']

        # 添加 cur_rater 字段
        for item in ybpf_table:
            if not item.get("rater_remark"):
                item['rater_remark'] = '无'
            item['cur_rater'] = cur_rater  # 添加 cur_rater 字段

        # 添加 cur_rater 字段
        for item in ybpf_table:
            item['stage'] = "评标阶段"  # 添加 stage 字段

        self.workflow_detail.add_kv_table(name='自增表格',
                                          kv_table={'ybpf_table': ybpf_table})

        # 检查废标
        people_info = self.ctx.get_var('people_info', {})
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        ybpf_invalid_idx_lt = list()  # 用于保存废标的下标
        for item in ybpf_table:
            for i in range(supplier_cnt):
                temp = item['supplier_' + chr(ord('a') + i)]
                if temp == "废标":
                    ybpf_invalid_idx_lt.append(i)
        ybpf_invalid_idx_lt = list(set(ybpf_invalid_idx_lt))

        # 将流程变量更新至 父流程
        f_instance_id = self.variables.get('f_instance_id', '')
        if f_instance_id and cur_rater:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    cur_rater + '_ybpf_table': [ybpf_table, ybpf_invalid_idx_lt],  # 使用列表  # 使用列表
                    cur_rater + '_technical_score_summary': technical_score_summary,
                    'ybpf_table': ybpf_table,  # 使用字符串键
                    f'{cur_rater}': now}  # 确保键是字符串
            ).call()
        GnetopsTodoEnd(self.ctx.task_id)
        variables_map.update({cur_rater + '_ybpf_table': [ybpf_table, ybpf_invalid_idx_lt],  # 使用列表  # 使用列表
                              cur_rater + '_technical_score_summary': technical_score_summary, }  # 确保键是字符串
                             )
        WorkflowContinue(
            task_id=self.ctx.task_id,
            variables_map=variables_map
        ).call()


class FilesGenerationPass(object):
    """
        评分模板文件生成
    """

    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def loop_item(self, people_info: dict, flow_key: str):
        """
        flow_key 传入创建的子流程的 流程标识
        title 子流程的标题
        """
        project_name = self.ctx.get_var('project_name', '')
        project_desc = self.ctx.get_var('project_desc', '')
        rater_list = people_info.get('rater_list', [])
        rater_cnt = len(rater_list)
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        msg_content = self.ctx.get_var('msg_content')
        ybpf_table = self.ctx.get_var('ybpf_table')
        ybpf_first_file_url = self.ctx.get_var('ybpf_first_file_url')
        technical_score_summary = self.ctx.get_var('technical_score_summary')
        import_table_head = self.ctx.get_var('import_table_head')
        f_instance_id = self.ctx.instance_id
        variables_map = {
            'project_name': project_name,
            'project_desc': project_desc,
            'ybpf_table': ybpf_table,
            'technical_score_summary': technical_score_summary,
            'people_info': people_info,
            'msg_content': msg_content,
            'supplier_cnt': supplier_cnt,
            'rater_cnt': rater_cnt,
            'f_instance_id': f_instance_id,
            "ybpf_first_file_url": ybpf_first_file_url,
            "import_table_head": import_table_head
        }
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})

        for i in range(supplier_cnt):
            # 生成流程变量
            variables_map.update({'supplier_' + chr(ord('a') + i) + "_name": supplier_list[i]})
        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_zongxiyu')
        # 用于保存创建的工单的单号
        ticket_id_list = []

        for rater in rater_list:
            variables_map.update({'cur_rater': rater})
            people_table = self.ctx.get_var(f"{rater}_ybpf_table")
            if people_table:
                ybpf_table = people_table[0]
                ybpf_table.append({"project": "厂家评审情况"})
                variables_map.update({'ybpf_table': ybpf_table})
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '的具体的评分功能"服务',
                ticket_level=3,
                title=project_name + ' - 评分',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=rater,  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list.append(ticket_info.get('TicketId'))

        # 门户页面库数据添加
        db = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT tender_evaluation FROM procurement_process_information "
               f"WHERE project_name='{project_name}'")
        conditions = {"project_name": project_name}
        procurement_portal_error_message = ''
        query = db.get_row(sql)
        if query:
            all_ticket_id = json.loads(query.get('tender_evaluation', ''))
            if all_ticket_id.get("children_ticket_id", []):
                all_ticket_id["children_ticket_id"].extend(ticket_id_list)
            else:
                all_ticket_id["children_ticket_id"] = ticket_id_list
            update_data = {"tender_evaluation": json.dumps(all_ticket_id)}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()
        else:
            procurement_portal_error_message = "招采门户数据更新失败，请手动补充"

        variables_map.update({'ticket_id_list': ticket_id_list,
                              'procurement_portal_error_message': procurement_portal_error_message})
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    def file_uploaded_to_cos(self, people_info: dict):
        """
            文件上传至cos
        """
        ybpf_table = self.ctx.variables.get('ybpf_table')
        supplier_list = people_info.get('supplier_list')
        supplier_cnt = len(supplier_list)
        # rater_cnt = self.ctx.variables.get('rater_cnt')
        # supplier_list = people_info.get('supplier_list')
        # whether_bid_suppliers = self.ctx.variables.get('whether_bid_suppliers')
        # if whether_bid_suppliers is not None:
        #     whether_bid_suppliers_len = len(whether_bid_suppliers)
        #     supplier_cnt = supplier_cnt - whether_bid_suppliers_len
        project_name = self.ctx.variables.get('project_name')

        # 创建表头
        import_table_head = {
            "project": "项目",
            "eval_content": "评审内容",
            'first_level': '合格',
            'second_level': '不合格',
            'remark': '评审内容备注'
        }
        for i in range(supplier_cnt):
            import_table_head[f'supplier_{chr(ord("a") + i)}'] = supplier_list[i]
        import_table_head['rater_remark'] = '备注'

        # for i in range(supplier_cnt):
        #     str01 = chr(ord('a') + i)  # 'a', 'b', ...
        #     for j in range(rater_cnt):
        #         str02 = str(j + 1)
        #         import_table_head[f"rater_{str02}{str01}"] = f"评委{str02}{str01}"
        #         import_table_head[f"rater_{str02}_remark"] = f"评委{str02}备注"
        #     import_table_head[f"avg_{str01}"] = f"平均分{str01}"
        #     import_table_head[f"dif_{str01}"] = f"偏差{str01}(%)"

        # 将数据转换为 DataFrame
        # ybpf_data = json.loads(ybpf_table)
        ybpf_df = pd.DataFrame(ybpf_table)

        # 重新排列列顺序
        ybpf_df = ybpf_df.reindex(columns=import_table_head.keys())

        # 设置 rater_remark 列的默认值为 "无"
        ybpf_df['rater_remark'].fillna('无', inplace=True)

        for i in range(supplier_cnt):
            ybpf_df[f'supplier_{chr(ord("a") + i)}'] = None

        ybpf_output_file = os.path.join(f'{project_name}评分模板.xlsx')

        # 保存为 Excel 文件
        ybpf_df.to_excel(ybpf_output_file, index=False, encoding='utf-8', engine='openpyxl',
                         header=list(import_table_head.values()))

        cos_path = '/data_json/'
        COSLib.uploadFile(file_name=ybpf_output_file, cos_path=cos_path)
        ybpf_file_url = COSLib.get_file_url(file_name=ybpf_output_file, cos_path=cos_path)

        variables = {
            "ybpf_first_file_url": ybpf_file_url,
            "import_table_head": import_table_head,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def sort_to_score(self):
        """
        对各个供应商的得分 进行排序
        :return:
        """
        people_info = self.ctx.get_var('people_info', {})
        rater_list = people_info.get('rater_list', [])
        rater_cnt = len(rater_list)
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        raters = []
        for i in rater_list:
            rater = self.ctx.get_var(f'{i}')

            raters.append({
                f"{i}": rater
            })

        # 提取时间并排序
        sorted_a = sorted(raters, key=lambda x: datetime.datetime.strptime(list(x.values())[0], '%Y-%m-%d %H:%M'))

        # 根据排序后的评委数据更新 rater_list
        rater_list = [list(sorted_a[i].keys())[0] for i in range(len(sorted_a))]

        # 存放结果的列表
        result = []

        # 为每个字典分配顺序编号
        for key, item in enumerate(sorted_a, start=1):
            # 获取字典的键
            value = list(item.keys())[0]
            # 将结果添加到列表中
            result.append({f"rater_{key}": value})

        # 修改_ybpf_table的key
        # 遍历 result 列表
        ybpf_gather = []
        for rater in result:
            # 获取 rater 的值
            rater_key = list(rater.values())[0]
            new_remark_key = f"{list(rater.keys())[0]}_remark"  # 'rater_1_remark'
            ybpf_name = self.ctx.get_var(rater_key + '_ybpf_table')
            # 遍历 v_mmywang_ybpf_table
            for table in ybpf_name[0]:  # 只处理第一个子列表
                if table['cur_rater'] == rater_key:
                    # 修改 rater_remark 为新的键
                    if table.get('rater_remark'):
                        table[new_remark_key] = table.pop('rater_remark')

            ybpf_gather.append({
                rater_key + '_ybpf_table': ybpf_name
            })
        # 初始化一个字典来存储每个项目的评分汇总
        project_summary = {}
        ybpf_test_table = []

        # 汇总数据 ybpf_table 的评分数据
        ybpf_table = [[] for _ in range(supplier_cnt)]  # 第i个下标，保存供应商（A+i）的评分情况
        invalid_bid_idx1 = []
        for item in rater_list:
            for j, row in enumerate(ybpf_gather):
                ybpf_info = row.get(item + '_ybpf_table')
                if ybpf_info and isinstance(ybpf_info, list) and len(ybpf_info) > 0:
                    table = ybpf_info[0]
                    ybpf_test_table.extend(table)
                    ybpf_temp_table = []  # 保存该评分人的打分情况
                    invalid_idx = ybpf_info[1]  # 获取废标的下标
                    invalid_bid_idx1 += invalid_idx
                    for entry in table:
                        row = []  # 保存某个评分表中的一行
                        for i in range(supplier_cnt):
                            supplier_key = f'supplier_{chr(ord("a") + i)}'
                            score = entry[supplier_key]
                            row.append(1 if score == "合格" else 0)  # 使用 -1 表示废标
                        ybpf_temp_table.append(row)
                    ybpf_temp_table = list(map(list, zip(*ybpf_temp_table)))  # 行列转置，[[1, 2],[3, 4]] -> [[1, 3], [2, 4]]
                    # print(ybpf_temp_table)
                    for i in range(supplier_cnt):
                        ybpf_table[i].append(ybpf_temp_table[i])
                    # print(ybpf_table)
                else:
                    # 如果 ybpf_info 为空或不是列表，或者列表为空，则处理这种情况
                    pass
                invalid_bid_idx1 = list(set(invalid_bid_idx1))

        # 汇总technical_score_summary数据
        # 创建一个字典来存储共同的 manufacturers 的数据
        combined_scores = {}
        technical_score_summary = []

        # 遍历第一个列表
        for item in rater_list:
            hz_info = self.ctx.variables.get(item + '_technical_score_summary')
            for row in hz_info:
                manufacturers = row.get("manufacturers")
                skill_score = 1 if row.get("skill_score") == "合格" else 0
                supplier_key = row.get("supplier_key")

                if manufacturers not in combined_scores:
                    combined_scores[manufacturers] = {
                        "skill_score": skill_score,
                        "count": 1,  # 计数器，用于计算平均值
                        "supplier_key": supplier_key
                    }
                else:
                    combined_scores[manufacturers]["skill_score"] += skill_score
                    combined_scores[manufacturers]["count"] += 1

        # 计算平均通过情况并生成新的列表
        for manufacturers, scores in combined_scores.items():
            supplier_key = scores.get("supplier_key")
            if scores["skill_score"] == scores["count"]:  # 只考虑共同的 manufacturers
                skill_score = "合格"
            else:
                skill_score = "不合格"
            technical_score_summary.append({
                "manufacturers": manufacturers,
                "skill_score": skill_score,  # 根据公式计算的 skill_score
                "supplier_key": supplier_key
            })
        # 提取所有技术评分
        skill_scores = [entry['skill_score'] for entry in technical_score_summary]

        # print(skill_scores)

        # 定义比较函数
        def sort_by_skill_score(item):
            # 返回一个元组，第一个元素是布尔值（False 表示“通过”，True 表示“不通过”）
            return item['skill_score'] == '合格'

        technical_score_summary = sorted(technical_score_summary, key=sort_by_skill_score, reverse=True)

        # 计算表格中的平均值
        ybpf_temp_list = []
        # 遍历每个供应商
        for i in range(supplier_cnt):
            # 过滤掉空列表
            filtered_data = [item for item in ybpf_table[i] if item]
            print(filtered_data)

            # 创建一个新的列表来存储替换后的评分
            replaced_data = []

            # 遍历每个评分项
            for row in filtered_data:
                replaced_row = []
                for score in row:
                    # 将评分替换为 "合格" 或 "不合格"
                    replaced_row.append("合格" if score == 1 else "不合格")
                replaced_data.append(replaced_row)
            print(replaced_data)

            # 将替换后的数据转置并添加到 ybpf_temp_list 中
            ybpf_temp_list.append(list(map(list, zip(*replaced_data))))

        # 获取 ybpf_table
        ybpf_list = self.ctx.variables.get('ybpf_table')
        ybpf_table_length = len(ybpf_list)
        # 从 ybpf_table 中提取数据并填充 import_table
        ybpf_import_table = [[] for _ in range(ybpf_table_length)]
        #
        for j in range(ybpf_table_length):
            ybpf_temp_item = [
                ybpf_list[j].get('stage'),
                ybpf_list[j].get('project'),
                ybpf_list[j].get('eval_content'),
                ybpf_list[j].get('first_level'),
                ybpf_list[j].get('second_level'),
                ybpf_list[j].get('rater_remark')
            ]
            ybpf_import_table[j].extend(ybpf_temp_item)
        print(ybpf_import_table)

        # 填充每个供应商的评分
        for i in range(supplier_cnt):
            for j in range(ybpf_table_length):
                ybpf_import_table[j].extend(ybpf_temp_list[i][j])

        # 添加三个空行
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        # 表格中的最后两行特别处理
        ybpf_import_table[-1].append('**说明信息**')
        ybpf_supplier_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        # ybpf_rater_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        for i in range(supplier_cnt):
            ybpf_supplier_str = ybpf_supplier_str + chr(ord('a') + i) + '是' + supplier_list[i] + '； '
        ybpf_import_table[-1].append(ybpf_supplier_str)

        # 生成json数据格式 用于表单导出
        keys = ['stage', 'project', 'eval_content', 'first_level', 'second_level', 'rater_remark']
        keys_bak = copy.deepcopy(keys)
        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                keys.append('rater_' + str(j + 1) + chr(ord('a') + i))  # rater_1a

        ybpf_data_json = []
        for i in range(ybpf_table_length + 4):
            ybpf_data_json.append(dict(zip(keys, ybpf_import_table[i])))
        # 正则匹配备注字段名
        pattern = re.compile(r'^rater_\d*_remark$')
        # 循环应标汇总评分结果表
        for item in rater_list:
            for table in ybpf_gather:
                ybpf_info = table.get(item + '_ybpf_table')
                if ybpf_info:
                    person_table = ybpf_info[0]
                    for index, row in enumerate(person_table):
                        if ybpf_data_json[index].get("rater_remark"):
                            ybpf_data_json[index].pop("rater_remark")
                        # 将对应行备注信息刷入结果表
                        ybpf_data_json[index].update({
                            key: value for key, value in row.items() if pattern.match(key)
                        })
        project_summary = {"project": "厂家评审汇总"}
        for key in keys:
            key_add = 0
            if key not in keys_bak:
                update = 0
                for row in ybpf_data_json:
                    if row.get(key) == "合格":
                        key_add += 1
                        if key_add == len(ybpf_data_json[:-4]):
                            update = 1
                        else:
                            update = 0
                if update == 1:
                    project_summary.update({key: "合格"})
                else:
                    project_summary.update({key: "不合格"})
        ybpf_data_json.insert(-4, project_summary)

        # 初始化流程变量
        variables = {
            'ybpf_data_json': ybpf_data_json,
            # 'ybpf_increment_table': ybpf_increment_table,
            'technical_score_summary': technical_score_summary,
            'result': result,
            # 'ybpf_dif_json': ybpf_dif_json,
            # 'import_table_head': str(import_table_head)
        }

        deviations = {}
        deviation_information_count = 0
        for supplier in people_info["supplier_list"]:
            scores_list = []
            for rater in people_info["rater_list"]:
                score_summary = self.ctx.variables.get(f"{rater}_technical_score_summary", [])
                score_item = next((item for item in score_summary if item.get("manufacturers") == supplier), None)
                if score_item:
                    scores_list.append({"score": score_item.get('skill_score'), "rater": rater})

            # Check for deviations
            unique_scores = set([s['score'] for s in scores_list])
            if len(unique_scores) > 1:  # If there are different scores
                deviation_key = f"deviation_information_{deviation_information_count + 1}"
                deviations[deviation_key] = [
                    {"deviation_value": s.get('score'), "lowest_score": s.get('rater'), "supplier": supplier}
                    for s in scores_list]
                deviation_information_count += 1
            elif len(unique_scores) == 1 and "不合格" in unique_scores:
                deviation_key = f"deviation_information_{deviation_information_count + 1}"
                deviations[deviation_key] = [
                    {"deviation_value": s.get('score'), "lowest_score": s.get('rater'), "supplier": supplier}
                    for s in scores_list]
                deviation_information_count += 1
        deviations["deviation_information_count"] = deviation_information_count

        variables.update(deviations)

        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables).call()

    def file_uploaded_to_cos_2(self):
        """
            文件上传至cos
        """
        time.sleep(5)
        process_tile = self.ctx.variables.get('ProcessTitle')
        ybpf_data_json = self.ctx.variables.get('ybpf_data_json')
        supplier_cnt = self.ctx.variables.get('supplier_cnt')
        rater_cnt = self.ctx.variables.get('rater_cnt')
        technical_score_summary = self.ctx.variables.get('technical_score_summary')

        url_table = []

        # 创建表头
        import_table_head = {
            "project": "项目",
            "eval_content": "评审内容",
            "first_level": "合格",
            "second_level": "不合格"
        }
        for i in range(supplier_cnt):
            str01 = chr(ord('a') + i)  # 'a', 'b', ...
            for j in range(rater_cnt):
                str02 = str(j + 1)
                import_table_head[f"rater_{str02}{str01}"] = f"评委{str02}{str01}"

        for i in range(supplier_cnt):
            str01 = chr(ord('a') + i)  # 'a', 'b', ...
            for j in range(rater_cnt):
                str02 = str(j + 1)
                import_table_head[f"rater_{str02}_remark"] = f"评委{str02}备注"
        # 评分汇总表格按列名重构
        all_scores_list = [{
            "project": "厂家",
            "eval_content": "通过评审汇总",
        }]
        for row in technical_score_summary:
            all_scores_list.append({
                "project": row.get("manufacturers"),
                "eval_content": row.get("skill_score"),
            })

        ybpf_data_json = (ybpf_data_json[:-4] + [{}, {}, {}] + all_scores_list + [{}, {}, {}] + ybpf_data_json[-4:])
        final_data_json = ybpf_data_json
        # 将数据转换为 DataFrame
        ybpf_df = pd.DataFrame(ybpf_data_json)

        # 重新排列列顺序
        ybpf_df = ybpf_df.reindex(columns=import_table_head.keys())
        ybpf_output_file = os.path.join(f'{process_tile}评分汇总.xlsx')

        # 保存为 Excel 文件
        ybpf_df.to_excel(ybpf_output_file, index=False, encoding='utf-8', engine='openpyxl',
                         header=list(import_table_head.values()))

        cos_path = '/data_json/'
        COSLib.uploadFile(file_name=ybpf_output_file, cos_path=cos_path)
        ybpf_file_url = COSLib.get_file_url(file_name=ybpf_output_file, cos_path=cos_path)

        url_table.append({
            'yppf_url': ybpf_file_url,
        })

        variables = {
            "ybpf_file_url": ybpf_file_url,
            "url_table": url_table,
            "final_data_json": final_data_json
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WhetherBidNotarizePass(AjaxTodoBase):
    """
        差别评审信息确认
    """

    def __init__(self):
        super(WhetherBidNotarizePass, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        variables = {}
        whether_bid_file = process_data.get('whether_bid_file', [])
        whether_bid_remark = process_data.get('whether_bid_remark', "")
        technical_score_summary = self.ctx.variables.get('technical_score_summary', '')
        deviation_information_count = self.ctx.variables.get('deviation_information_count', '')
        ybpf_data_json = self.ctx.variables.get('ybpf_data_json', '')
        people_info = self.ctx.variables.get('people_info', '')
        supplier_list = people_info.get('supplier_list', '')
        supplier_cnt = self.ctx.variables.get('supplier_cnt', '')
        rater_cnt = self.ctx.variables.get('rater_cnt', '')
        whether_bid_suppliers = []
        whether_bid_suppliers_set = set()
        for count in range(1, deviation_information_count + 1):
            deviation_information_bak = process_data.get(f'deviation_information_{count}', [])
            if deviation_information_bak:
                is_update = 0
                for item in deviation_information_bak:
                    if item.get('is_pass') == '不合格':
                        is_update = -1
                    else:
                        is_update = 1
                whether_bid_remark_bak = process_data.get(f'whether_bid_remark_{count}', '')
                whether_bid_file_bak = process_data.get(f'whether_bid_file_{count}', '')
                variables.update({
                    f'deviation_information_{count}': deviation_information_bak,
                    f'whether_bid_remark_{count}': whether_bid_remark_bak,
                    f'whether_bid_file_{count}': whether_bid_file_bak,
                })
                for row in whether_bid_file_bak:
                    FileList = row.get('response', {}).get('FileList', [])
                    if FileList:
                        if FileList[0].get('status') != "success":
                            return {"code": -1, "msg": "文件上传失败"}
                if is_update == -1:
                    whether_bid_remark += f"{whether_bid_remark_bak}\n"
                    whether_bid_file.extend(whether_bid_file_bak)
                    whether_bid_suppliers_set.add(deviation_information_bak[0].get('supplier'))
                    for row in technical_score_summary:
                        if deviation_information_bak[0].get('supplier') == row.get('manufacturers'):
                            row['skill_score'] = '不合格'
                else:
                    for row in technical_score_summary:
                        if deviation_information_bak[0].get('supplier') == row.get('manufacturers'):
                            row['skill_score'] = '合格'
        if whether_bid_suppliers_set:
            whether_bid_suppliers = list(whether_bid_suppliers_set)


        whether_bid_file_url = []
        whether_bid_file_table = []

        if whether_bid_file is not None:
            for doc in whether_bid_file:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        whether_bid_file_url.append(url)
                        whether_bid_file_table.append({
                            "whether_bid_file": url,
                            'whether_bid_file_name': name
                        })
                    else:
                        return {"code": -1, 'msg': "文件上传失败或未上传完毕，请稍后再提交"}

        # 假设 technical_score_smary 是一个字典列表，每个字典都有一个 'manufacturers' 字段
        if isinstance(technical_score_summary, list):
            # 使用列表推导式过滤掉不需要的条目
            technical_score_summary = [
                item for item in technical_score_summary
                if not any(supplier in item.get('manufacturers', []) for supplier in whether_bid_suppliers)
            ]
        # 假设 deviation_information 是一个字典列表，每个字典都有一个 'supplier' 字段
        # if isinstance(deviation_information, list):
        #     # 使用列表推导式过滤掉不需要的条目
        #     deviation_information = [
        #         item for item in deviation_information
        #         if not any(supplier in item.get('supplier', []) for supplier in whether_bid_suppliers)
        #     ]

        variables.update({
            "whether_bid_file": whether_bid_file,
            "whether_bid_file_table": whether_bid_file_table,
            "whether_bid_suppliers": whether_bid_suppliers,
            "whether_bid_remark": whether_bid_remark,
            "technical_score_summary": technical_score_summary,
            "ybpf_data_json": ybpf_data_json,
            "supplier_cnt": supplier_cnt
        })

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BidEmailNoticeTodoPass(AjaxTodoBase):
    """
    数据写入， 和 邮件通知
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()
        self.my_db = mysql.new_mysql_instance("tbconstruct")

    def end(self, process_data, process_user):
        # 数据写入
        # # 生成导出表的表头
        # column_names = ['project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level', 'four_level']
        # supplier_cnt = self.variables.get('supplier_cnt', 0)
        # rater_cnt = self.variables.get('rater_cnt', 0)
        #
        # 邮件通知
        project_name = self.variables.get('project_name', '')
        project_desc = self.variables.get('project_desc', '')
        whether_bid_suppliers = self.variables.get('whether_bid_suppliers', [])
        whether_bid_remark = self.variables.get('whether_bid_remark', '')

        # 将列表转换为以逗号隔开的字符串
        suppliers_string = ', '.join(whether_bid_suppliers)

        msg_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件通知</title>
                <style>
                    .title{{
                        margin-bottom: 10px;
                        color: #212529;
                        text-align: center;
                    }}
                    .info-title{{
                        display: inline-block;
                        width: 7rem;
                        text-align: right;
                        font-weight: bold;
                    }}
                    .table-head {{
                        color: #fff;
                        background-color: #343a40;
                        border-color: #454d55;
                        padding: 0.75rem;
                        font-size: 0;
                        font-weight: bold;
                        text-align: center;
                    }}
                    .table-head-item{{
                        display: inline-block;
                        font-size: 16px;
                    }}
                    .row-box{{
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                        font-size: 0;
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }}
                    .row-item{{
                        display: inline-block; font-size: 16px;
                    }}
                    .approve-info-title01{{
                        display: inline-block;
                        width: 15%;
                        text-align: right; font-weight: bold;
                    }}
                    .approve-info-title02{{
                        display: inline-block; vertical-align: top; width: 15%; font-size: 16px; text-align: right; font-weight: bold;
                    }}
                    .approve-info-remark02{{
                        display: inline-block; vertical-align: top; width: 85%; font-size: 16px;
                    }}
                    .attachment-box{{
                        width: 15%;
                        text-align: right;
                        padding-left: 40px;
                    }}
                </style>
            </head>
            <body>
                <div style="font-size: 16px;">
                    <h2 class="title">{project_name}</h2>
                    <div>
                        <h3>1. 基础信息</h3>
                        <p><span class="info-title">项目名：</span>{project_name}</p>
                        <p><span class="info-title">项目概况：</span>{project_desc}</p>
                    </div>
                    <div>
                        <h3>2. 汇总表</h3>
                    </div>
                </div>

                <div class="table-head">
                    <span class="table-head-item" style="width: 30%;">序号</span>
                    <span class="table-head-item" style="width: 35%;">厂家</span>
                    <span class="table-head-item" style="width: 35%;">评标情况</span>
                </div>
            """

        technical_score_summary = self.variables.get('technical_score_summary', '')
        for index, value in enumerate(technical_score_summary):
            msg_content += f"""
                <div class="row-box">
                    <span class="row-item" style="width: 30%;">{index + 1}</span>
                    <span class="row-item" style="width: 35%;">{value.get('manufacturers')}</span>
                    <span class="row-item" style="width: 35%;">{value.get('skill_score')}</span>
                </div>
                """
        msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">不合格供应商： </span>
                        <span>{suppliers_string}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">组长备注： </span>
                        <span class="approve-info-remark02">{whether_bid_remark}</span>
                    </div>
                </div>
                """
        first_check = self.variables.get('first_check', '')
        check_remark = self.variables.get('check_remark', '')
        if first_check and check_remark:
            msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">评分组长审核： </span>
                        <span>{first_check}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">备注： </span>
                        <span class="approve-info-remark02">{check_remark}</span>
                    </div>
                </div>
                """

        leader = self.variables.get('leader', '')
        leader_approval_standard = self.variables.get('leader_approval_standard', '')  # 同意 or 驳回
        leader_remarks_standard = self.variables.get('leader_remarks_standard', '')  # 备注
        msg_content += f"""
            <div style="margin: 1rem 0 1rem 1rem;">
                <div style="font-size: 16px;">
                    <span class="approve-info-title01">{leader}审核： </span>
                    <span>{leader_approval_standard}</span>
                </div>
                <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                    <span class="approve-info-title02">备注： </span>
                    <span class="approve-info-remark02">{leader_remarks_standard}</span>
                </div>
            </div>
            """

        center = self.variables.get('center', '')  # 审批人
        director_approval_standard = self.variables.get('director_approval_standard', '')  # 同意 or 驳回
        director_remarks_standard = self.variables.get('director_remarks_standard', '')  # 备注
        msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">{center}审核： </span>
                        <span>{director_approval_standard}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">备注： </span>
                        <span class="approve-info-remark02">{director_remarks_standard}</span>
                    </div>
                </div>
                """

        next_op = self.variables.get('next_op', '')  # 审批人
        GM = self.variables.get('GM', '')  # 审批人
        GM_approval_standard = self.variables.get('GM_approval_standard', '')  # 同意 or 驳回
        GM_remarks_standard = self.variables.get('GM_remarks_standard', '')  # 备注
        if next_op == 0:
            msg_content += f"""
                    <div style="margin: 1rem 0 1rem 1rem;">
                        <div style="font-size: 16px;">
                            <span class="approve-info-title01">{GM}审核： </span>
                            <span>{GM_approval_standard}</span>
                        </div>
                        <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                            <span class="approve-info-title02">备注： </span>
                            <span class="approve-info-remark02">{GM_remarks_standard}</span>
                        </div>
                    </div>
                    """
        elif next_op == 1:
            msg_content += f"""
                    <div style="margin: 1rem 0 1rem 1rem;">
                        <div style="font-size: 16px;">
                            <span class="approve-info-title01">{center}审核： </span>
                            <span>{director_approval_standard}</span>
                        </div>
                        <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                            <span class="approve-info-title02">备注： </span>
                            <span class="approve-info-remark02">{director_remarks_standard}</span>
                        </div>
                    </div>
                    """

        other_desc = process_data.get('other_desc', '')
        if other_desc:
            msg_content += f"""
                        </span>
                    </div>
                </div>

                <div>
                    <h3>3. 其他说明</h3>
                    <div>
                        &nbsp;&nbsp;&nbsp;&nbsp;{other_desc}
                    </div>
                </div>
            </body>
            </html>
            """
        else:
            msg_content += """
                        </span>
                    </div>
                </div>
            </body>
            </html>
            """

        title = process_data.get('title', '')
        receivers = process_data.get('receivers', [])
        transmits = process_data.get('transmits', [])
        # tof.send_email(sendTitle=title, msgContent=msg_content, sendTo=receivers, sendCopy=transmits)
        #
        # for i in range(supplier_cnt):
        #     for j in range(rater_cnt):
        #         column_names.append('rater_' + str(j + 1) + chr(ord('a') + i))  # 形如rater_1a
        #     column_names.append('avg_' + chr(ord('a') + i))  # 形如avg_e
        #     column_names.append('dif_' + chr(ord('a') + i))  # 形如dif_e
        # try:
        #     ybpf_data_json = self.ctx.get_var('ybpf_data_json', '')
        #     mspf_data_json = self.ctx.get_var('mspf_data_json', '')
        #     insert_datas = []
        #     # 导出表的数据data_json中，最后三行为 - 两个空行，一个说明信息，故需要截掉
        #     for item in ybpf_data_json[0:-4]:
        #         ybpf_data = {}
        #         for i in range(len(column_names)):
        #             ybpf_data.update({column_names[i]: item.get(column_names[i], '')})
        #         insert_datas.append(ybpf_data)
        #
        #     for item in mspf_data_json[0:-4]:
        #         mspf_data = {}
        #         for i in range(len(column_names)):
        #             mspf_data.update({column_names[i]: item.get(column_names[i], '')})
        #         insert_datas.append(mspf_data)
        #
        #     self.workflow_detail.add_kv_table(
        #         name="流程变量",
        #         kv_table={
        #             "数据表的插入数据": insert_datas
        #         }
        #     )
        #
        #     self.my_db.insert_batch(table='idc_tb_procurement_evaluation', insertdatas=insert_datas)
        #
        # except ValueError as ve:
        #     print(f'捕获到异常 {ve}')
        #
        # except TypeError as te:
        #     print(f'捕获到异常 {te}')

        self.workflow_detail.add_kv_table(
            name="邮件内容",
            kv_table={
                "邮件内容": msg_content,
                "title": title,
                "receivers": receivers,
                "transmits": transmits,
            }
        )
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(
            task_id=self.ctx.task_id,
            variables={
                'msg_content': msg_content,
                'title': title,
                "receivers": receivers,
                "transmits": transmits,
                "other_desc": other_desc
            }
        )


class BidEmailNoticeShowPass(AjaxTodoBase):
    """
    数据写入， 和 邮件通知
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()
        self.my_db = mysql.new_mysql_instance("tbconstruct")

    def end(self, process_data, process_user):
        # 生成导出表的表头
        is_send = process_data.get('is_send')
        if is_send == "否":
            GnetopsTodoEnd(self.ctx.task_id)
            flow.complete_task(
                task_id=self.ctx.task_id,
                variables={
                    "is_send": is_send
                })
            return None
        column_names = ['project', 'eval_content', 'first_level', 'second_level', 'third_level']
        supplier_cnt = self.variables.get('supplier_cnt', 0)
        rater_cnt = self.variables.get('rater_cnt', 0)
        title = process_data.get('title', '')
        receivers = process_data.get('receivers', [])
        receivers = receivers.split(';')
        transmits = self.variables.get('transmits', [])
        if receivers and isinstance(receivers, str):
            receivers = [receivers + "@tencent.com"]
        if receivers and isinstance(receivers, list):
            for i in range(len(receivers)):
                receivers[i] = receivers[i] + "@tencent.com"
        if transmits:
            transmits = transmits.split(';')
            for i in range(len(transmits)):
                transmits[i] = transmits[i] + "@tencent.com"
        msg_content = process_data.get('msg_content', '')
        tof.send_email(sendTitle=title, msgContent=msg_content, sendTo=receivers, sendCopy=transmits)

        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                column_names.append('rater_' + str(j + 1) + chr(ord('a') + i))  # 形如rater_1a
            column_names.append('avg_' + chr(ord('a') + i))  # 形如avg_e
            column_names.append('dif_' + chr(ord('a') + i))  # 形如dif_e
        try:
            ybpf_data_json = self.ctx.get_var('ybpf_data_json', '')
            mspf_data_json = self.ctx.get_var('mspf_data_json', '')
            insert_datas = []
            # 导出表的数据data_json中，最后三行为 - 两个空行，一个说明信息，故需要截掉
            for item in ybpf_data_json[0:-4]:
                ybpf_data = {}
                for i in range(len(column_names)):
                    ybpf_data.update({column_names[i]: item.get(column_names[i], '')})
                insert_datas.append(ybpf_data)

            for item in mspf_data_json[0:-4]:
                mspf_data = {}
                for i in range(len(column_names)):
                    mspf_data.update({column_names[i]: item.get(column_names[i], '')})
                insert_datas.append(mspf_data)

            self.workflow_detail.add_kv_table(
                name="流程变量",
                kv_table={
                    "数据表的插入数据": insert_datas
                }
            )

            self.my_db.insert_batch(table='idc_tb_procurement_evaluation', insertdatas=insert_datas)

        except ValueError as ve:
            print(f'捕获到异常 {ve}')

        except TypeError as te:
            print(f'捕获到异常 {te}')
        ticket_id = self.ctx.variables.get('ticket_id')
        rater_list = self.ctx.variables.get('people_info')["rater_list"]
        rater_leader = self.ctx.variables.get("rater_leader")
        project_name = self.variables.get('project_name', '')
        email_list = []
        for i in rater_list:
            i += "@tencent.com"
            email_list.append(i)
        msg_to_rater = (f"【{project_name}】技术评标审批已完成\n\n"
                        f"感谢大家的支持，谢谢！")
        tof.send_email(sendTitle=title, msgContent=msg_to_rater, sendTo=email_list, sendCopy=[rater_leader])
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(
            task_id=self.ctx.task_id,
            variables={
                'msg_content': msg_content,
                'title': title,
                "receivers": receivers,
                "transmits": transmits,
                "is_send": is_send
            }
        )
