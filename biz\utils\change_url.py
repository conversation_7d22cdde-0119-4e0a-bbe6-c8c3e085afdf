import json
import mimetypes
import os
import re
import time
import uuid
from urllib.parse import unquote

import requests
from iBroker.lib import exception,mysql
from iBroker.lib.sdk import tof

from biz.construction_process.cos_lib import COSLib


def transform_url_list(url_list_str):
    """
    将URL列表字符串转换为指定格式的列表（保留原始URL不做修改）

    参数:
        url_list_str: 包含多个URL的列表字符串

    返回:
        list: 转换后的列表结构
    """
    try:
        # 将字符串形式的列表转换为实际列表
        url_list = eval(url_list_str)

        # 处理每个URL，构建结果列表
        result = []
        for url in url_list:
            # 提取文件名（从URL最后一个'/'之后的部分）
            file_name = url.split('/')[-1]

            # 构建每个URL对应的数据结构（不再替换'test'）
            item = {
                'name': file_name,
                'response': {
                    'FileList': [{
                        'name': file_name,
                        'status': 0,
                        'url': url  # 直接使用原始URL
                    }]
                }
            }
            result.append(item)

        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        return url_list_str


def test_download_link(url):
    """
    测试下载链接是否可用

    参数:
        url (str): 要测试的下载链接

    返回:
        tuple: (是否可用, 详细信息)
    """
    try:
        # 第一次请求获取真实响应
        response = requests.get(
            url,
            stream=True,
            timeout=30,
            headers={'User-Agent': 'Mozilla/5.0'},
            allow_redirects=True
        )

        # 检查实际状态码
        if response.status_code != 200:
            return False, f"HTTP状态码异常: {response.status_code}"

        # 检查是否有实际数据
        content = next(response.iter_content(1024))  # 读取前1KB
        if not content:
            return False, "无实际内容返回"

        # 检查内容类型
        content_type = response.headers.get('Content-Type', '')
        if ('text/html' in content_type or 'application/json' in content_type) and len(content) < 1024:
            return False, "返回的是错误页面而非文件"

        return True, "链接真正可用"

    except requests.exceptions.RequestException as e:
        return False, f"请求失败: {str(e)}"


def download_and_upload_file(url, save_dir="./relay/construct"):
    """
    通用文件下载函数（自动识别文件类型）
    :param url: 文件下载地址
    :param save_dir: 保存目录（默认./downloads）
    :return: (文件保存路径) 或 (None) 如果失败
    """
    try:
        # 创建目录（如果不存在）
        os.makedirs(save_dir, exist_ok=True)

        # 第一次请求获取文件信息（HEAD方法）
        headers = {"User-Agent": "Mozilla/5.0"}
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()

        # 解析文件类型（优先从Content-Type获取）
        content_type = response.headers.get("Content-Type", "").split(";")[0]
        file_ext = (
                mimetypes.guess_extension(content_type) or ".bin"
        )  # 默认用.bin表示未知类型

        # 解析文件名（优先从Content-Disposition获取）
        content_disp = response.headers.get("Content-Disposition", "")
        filename_match = re.search(
            r'filename\*?=["\']?(?:UTF-\d["\']?)?([^"\'\s;]+)', content_disp, re.I
        )

        if filename_match:
            filename = unquote(filename_match.group(1))  # 解码URL编码

            if isinstance(filename, bytes):
                filename = filename.decode('utf-8')
            # 分离文件名和扩展名
            name, ext = os.path.splitext(filename)
        else:
            # 从URL最后部分生成文件名（带识别出的后缀）
            base_name = url.split('/')[-1].split('?')[0]
            name, ext = os.path.splitext(base_name)
            if not ext:
                ext = file_ext

        short_uuid = str(uuid.uuid4())[:8]
        name = f"{name}_{short_uuid}"
        filename = f"{name}{ext}"

        name, ext = os.path.splitext(filename)

        # 完整保存路径
        save_path = os.path.join(save_dir, filename)

        # 检查是否已有同名文件，如果有则添加短UUID
        counter = 0
        while os.path.exists(save_path):
            # 只取 UUID 的前8位，避免文件名过长
            short_uuid = str(uuid.uuid4())[:8]
            new_filename = f"{name}_{short_uuid}{ext}"
            save_path = os.path.join(save_dir, new_filename)
            counter += 1
            if counter > 10:
                raise FileExistsError(f"无法生成唯一文件名：{filename}")

        # 写入文件
        with open(save_path, "wb") as f:
            for chunk in response.iter_content(8192):
                f.write(chunk)

        print(f"文件已保存至: {save_path}")
        cos_path = "relay/construct/"
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                COSLib.uploadFile(file_name=save_path, cos_path=cos_path)
                ybpf_file_url = COSLib.get_file_url(file_name=os.path.basename(save_path), cos_path=cos_path)
                return ybpf_file_url
            except (
                    exception.LogicException,
                    IOError,
                    requests.exceptions.RequestException,
            ) as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"上传COS失败，第 {retry_count} 次重试: {e}")
                    time.sleep(2 ** retry_count)  # 指数退避
    except (
            exception.LogicException,
            IOError,
            requests.exceptions.RequestException,
    ) as e:
        tof.send_company_wechat_message(
            receivers=["v_zongxiyu"],
            title="附件下载失败",
            message=f"附件下载失败（已达最大重试次数）: {url}, 错误: {e}"
        )
        return url


def problem_rectification_file_url_change(url, save_dir="./cooperative_files"):
    """
    通用文件下载函数（自动识别文件类型）用于测试问题整改雷达链接照片替换
    :param url: 文件下载地址
    :param save_dir: 保存目录（默认./downloads）
    :return: (文件保存路径, 实际文件类型) 或 (None, None) 如果失败
    """
    try:
        # 创建目录（如果不存在）
        os.makedirs(save_dir, exist_ok=True)

        # 第一次请求获取文件信息（HEAD方法）
        headers = {"User-Agent": "Mozilla/5.0"}
        response = requests.get(url, headers=headers, stream=True)
        response.raise_for_status()

        # 解析文件类型（优先从Content-Type获取）
        content_type = response.headers.get("Content-Type", "").split(";")[0]
        file_ext = (
                mimetypes.guess_extension(content_type) or ".jpg"
        )  # 默认用.jpg表示未知类型

        # 如果文件后缀为.bin则替换为.jpg
        if file_ext == '.bin':
            file_ext = '.jpg'

        # 解析文件名（优先从Content-Disposition获取）
        content_disp = response.headers.get("Content-Disposition", "")
        filename_match = re.search(
            r'filename\*?=["\']?(?:UTF-\d["\']?)?([^"\'\s;]+)', content_disp, re.I
        )

        if filename_match:
            filename = unquote(filename_match.group(1))  # 解码URL编码
            # 分离文件名和扩展名
            name, ext = os.path.splitext(filename)
            if not ext:
                ext = file_ext
            else:
                file_ext = ext  # 更新file_ext为真实扩展名
            filename = f"{name}{ext}"
        else:
            # 从URL最后部分生成文件名（带识别出的后缀）
            base_name = url.split('/')[-1].split('?')[0]
            name, ext = os.path.splitext(base_name)
            if not ext:
                ext = file_ext
            filename = f"{name}{ext}"

        name, ext = os.path.splitext(filename)

        # 完整保存路径
        save_path = os.path.join(save_dir, filename)

        # 检查是否已有同名文件，如果有则添加短UUID
        counter = 0
        while os.path.exists(save_path):
            # 只取 UUID 的前8位，避免文件名过长
            short_uuid = str(uuid.uuid4())[:8]
            new_filename = f"{name}_{short_uuid}{ext}"
            save_path = os.path.join(save_dir, new_filename)
            counter += 1
            if counter > 10:
                raise FileExistsError(f"无法生成唯一文件名：{filename}")

        # 写入文件
        with open(save_path, "wb") as f:
            for chunk in response.iter_content(8192):
                f.write(chunk)

        print(f"文件已保存至: {save_path}")
        cos_path = "cooperative_files/"
        COSLib.uploadFile(file_name=save_path, cos_path=cos_path)
        ybpf_file_url = COSLib.get_file_url(file_name=os.path.basename(save_path), cos_path=cos_path)
        return ybpf_file_url
        # return save_path, content_type

        # return save_path, content_type

    except (
            exception.LogicException,
            IOError,
            requests.exceptions.RequestException,
    ) as e:
        print(f"下载上传文件失败: {str(e)}")
        return url

def get_accout_by_role(role:str, project_name:str):
    """
    根据角色获取账号
    :param role: 角色
    :param project_name: 项目
    :return: 账号
    """
    db = mysql.new_mysql_instance("tbconstruct")
    construct_account = db.get_dictionary("project_role_account", ["account"], {"role": role, "project_name": project_name,"del_flag": 0})
    return construct_account["account"]

# if __name__ == "__main__":
#     url = "https://openapi.kehua.com:8643/papi/openapi/api/file/v2/common/download/1148680705577140226?userid=1116443153403330777&access_name=khuser&access_pwd=asdasjda165156&corpid=ff21f8103e669a6714af661f1b6ecbde"
#     print(download_and_upload_file(url))