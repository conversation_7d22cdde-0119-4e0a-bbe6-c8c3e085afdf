from iBroker.lib import mysql, config


class PermissionControl(object):
    """
        权限管理
    """

    def construction_issues_control(self, account):
        # 先从白名单配置中获取数据
        config_result = config.get_config_map("construction_issues_control")
        if account in config_result:
            return True
        # 再从数据库中获取数据
        db = mysql.new_mysql_instance("tbconstruct")
        # 合并 SQL 语句，使用 OR 条件一次性查询
        sql = f"""
                SELECT account 
                FROM project_role_account 
                WHERE (role LIKE '%监理%' OR role LIKE '%项管%' OR role LIKE '%合建方%') 
                AND (del_flag = 0 OR del_flag IS NULL)
            """
        # 执行一次查询
        result = db.get_all(sql)

        account_list = [i.get('account') for i in result]

        if account in account_list:
            return True
        return False
