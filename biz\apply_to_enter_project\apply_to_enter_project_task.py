from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


# 已有账号申请入项目
class ApplyToEnterProject(AjaxTodoBase):
    def __init__(self):
        super(ApplyToEnterProject, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("member_list")
        if not member_list:
            return {"code": -1, "msg": "请添加已有账号人员申请入项目信息！"}
        application_remark = process_data.get("application_remark")
        # 判断账号是否都已开通
        no_wework_account_list = []
        # 判断输入姓名和账号绑定姓名是否一致
        name_validate_list = []
        # 判断是否有重复开通的账号 （有的话“application_remark”为必填）
        account_repeat_list = []
        # 校验是否存在【同手机号不同姓名】的账号
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in member_list:
            if (
                not info.get("exist_account")
                or not info.get("exist_account_label")
                or not info.get("exist_name")
            ):
                no_wework_account_list.append(info.get("phone"))
            if info.get("name") != info.get("exist_name"):
                name_validate_list.append(
                    f"{info.get('phone')} {info.get('name')} {info.get('exist_name')}"
                )
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    append_flag = False
                    for account in result:
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        name = info.get("name")
                        phone = info.get("phone")
                        application_account = info.get("application_account")
                        project_name = info.get("campus_name") + info.get(
                            "project_name"
                        )
                        role = info.get("role")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_project_name = account.get("project_name")
                        exist_role = account.get("role")
                        exist_account = account.get("account")
                        if (
                            "集成商" in role or "项管" in role or "监理" in role
                        ) and project_name not in account_repeat_list:
                            account_repeat_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        if name != exist_name:
                            # 新申请的展示在已存在的前面
                            if not append_flag:
                                account_validate_list.append(
                                    f"{name} {project_name} {role} {phone} {application_account}"
                                )
                            append_flag = True
                            account_validate_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        if no_wework_account_list:
            tips = ("<br>").join(no_wework_account_list)
            return {
                "code": -1,
                "msg": f"以下手机号【未查询到账号】，请确认手机号是否正确或走账号开通流程！<br>{tips}",
            }
        if name_validate_list:
            tips = ("<br>").join(name_validate_list)
            return {
                "code": -1,
                "msg": f"以下手机号【输入姓名与已有账号绑定姓名不一致】，请确认输入姓名是否输入正确！<br>{tips}",
            }
        if account_repeat_list:
            tips = ("<br>").join(account_repeat_list)
            if not application_remark:
                return {
                    "code": -1,
                    "msg": f"存在【已开通账号】，请在备注填写使用理由！<br>{tips}",
                }
        if account_validate_list:
            tips = ("<br>").join(account_validate_list)
            return {
                "code": -1,
                "msg": "【请核对】姓名电话是否对应！"
                # "<br>若是已开通的账号信息有误请先删除已开通的账号信息再继续开通；"
                # "若是本次开通的账号信息有误请先修改正确再继续开通。"
                f"<br>{tips}",
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "member_list": member_list,
            "application_remark": application_remark,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
