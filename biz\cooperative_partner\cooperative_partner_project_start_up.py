import ast
import datetime
import hashlib
import hmac
import json
import time
import uuid
import numpy as np

import pandas as pd
from iBroker.lib.sdk import flow, tof, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id




class ProjectStartUpSupplier(object):
    """
        项目启动
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(ProjectStartUpSupplier, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_start_up_process_initiation(self, project_name):
        """
            创建工单--项目启动
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        system_type = self.ctx.variables.get("system_type")

        DemandDelivery = self.ctx.variables.get("demand_delivery")
        ConstructionMode = self.ctx.variables.get("construction_mode")
        PM = self.ctx.variables.get("PM")
        Architecture = self.ctx.variables.get("architecture")
        Beautification = self.ctx.variables.get("beautification")
        TreeSutra = self.ctx.variables.get("tree_sutra")
        WeakCurrent = self.ctx.variables.get("weak_current")
        Business = self.ctx.variables.get("business")
        SupplyChain = self.ctx.variables.get("supply_chain")
        InformPerson = self.ctx.variables.get("inform_a_person")
        AHU = self.ctx.variables.get("AHU")
        FirewoodHair = self.ctx.variables.get("firewood_hair")
        LowPressureCabinet = self.ctx.variables.get("low_pressure_cabinet")
        MediumPressureCabinet = self.ctx.variables.get("medium_pressure_cabinet")
        Transformers = self.ctx.variables.get("transformers")
        SynthesisDistributionCabinet = self.ctx.variables.get("synthesis_distribution_cabinet")
        HVDC = self.ctx.variables.get("HVDC")
        Battery = self.ctx.variables.get("battery")
        PDU = self.ctx.variables.get("PDU")
        Cabinet = self.ctx.variables.get("cabinet")
        Integrator = self.ctx.variables.get("integrator")
        ItemTube = self.ctx.variables.get("item_tube")
        SupervisionManagement = self.ctx.variables.get("supervision_and_management")
        ThirdPartyTesting = self.ctx.variables.get("third_party_testing")

        personnel_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name,
            "DemandDelivery": DemandDelivery,
            "ConstructionMode": ConstructionMode,
            "PM": PM,
            "Architecture": Architecture,
            "Beautification": Beautification,
            "TreeSutra": TreeSutra,
            "WeakCurrent": WeakCurrent,
            "Business": Business,
            "SupplyChain": SupplyChain,
            "InformPerson": InformPerson,
            "AHU": AHU,
            "FirewoodHair": FirewoodHair,
            "LowPressureCabinet": LowPressureCabinet,
            "MediumPressureCabinet": MediumPressureCabinet,
            "Transformers": Transformers,
            "SynthesisDistributionCabinet": SynthesisDistributionCabinet,
            "HVDC": HVDC,
            "Battery": Battery,
            "PDU": PDU,
            "Cabinet": Cabinet,
            "Integrator": Integrator,
            "ItemTube": ItemTube,
            "SupervisionManagement": SupervisionManagement,
            "ThirdPartyTesting": ThirdPartyTesting
        }

        if system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": personnel_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/project_start_up_process_initiation"
                }
            )

            PartnerTicketId = str(info['Data'])
            variables = {
                'info': str(info),
                'system_type': system_type,
                'PartnerTicketId': PartnerTicketId
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            supplier_info = 'supplier_differentiation'
            system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": personnel_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": "project_initiation",  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:项目启动",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:项目启动",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)

            PartnerInstanceId = str(info['data']['InstanceId'])
            PartnerTicketId = str(info['data']['TicketId'])

            variables = {
                'project_start_up_process_initiation': str(info),
                'system_id': system_id,
                'system_type': system_type,
                'PartnerInstanceId': PartnerInstanceId,
                'PartnerTicketId': PartnerTicketId
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

    def analyze_control_plan(self, project_name):
        system_type = self.ctx.variables.get("system_type")
        if system_type == '外部':
            progress = self.ctx.variables.get("progress")
        elif system_type == 'Dcops':
            progress_table = self.ctx.variables.get("progress_table")
            for row in progress_table:
                progress = row.get("progress")
        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        if progress:
            ticket_id = self.ctx.variables.get("ticket_id")
            """
                excel解析总控计划
            """
            df = pd.read_excel(progress, engine='openpyxl', sheet_name='总控计划模版')
            df = df.replace({np.nan: None})
            """
                所有数据存库
            """
            insert_data = []
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                plan_duration_construction = row['工期(日历日）']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                completion_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                insert_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'serial_number': serial_number,
                    'work_content': work_content,
                    'plan_duration_construction': plan_duration_construction,
                    'start_time': start_time,
                    'completion_time': completion_time,
                    'responsible_person': responsible_person,
                    'output_object': output_object,
                    'input_object': input_object,
                    'construction_attribute': construction_attribute,
                    'now_time': now_time
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("excel_data", insert_data)
            tb_db.commit()
        else:
            self.workflow_detail.add_kv_table('解析总控计划', {"success": False, "data": "未获取到总控计划"})
        flow.complete_task(self.ctx.task_id)


class WaitPartnerCallInterfaceXmqd(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceXmqd, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_master_control_plan_upload(self):
        """
            等待总控计划上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")
        if system_type == "外部":
            progress_table = []
            db = mysql.new_mysql_instance("tbconstruct")
            sql = "SELECT progress " \
                  "FROM file_storage_data " \
                  f"WHERE dcopsTicketId = '{ticket_id}'" \
                  f"AND project_name = '{project_name}' "
            result_list = db.get_all(sql)

            for record in result_list:
                # 获取 plan_url 字符串
                progress = record.get('progress', "")  # 总控计划文档

                progress_table.append({
                    "progress": progress,
                    'progress_name': "总控计划"
                })

            self.workflow_detail.add_kv_table('总控计划文档', {'message': result_list})
            if result_list:
                variables = {
                    "zkjh_result_list": result_list,
                    "progress_table": progress_table
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == "Dcops":
            progress_table = []
            db = mysql.new_mysql_instance("tbconstruct")
            sql = "SELECT progress " \
                  "FROM file_storage_data " \
                  f"WHERE dcopsTicketId = '{ticket_id}'" \
                  f"AND project_name = '{project_name}' "
            result_list = db.get_all(sql)

            for record in result_list:
                progress = record.get('progress')
                progress = progress.replace("'", '"')
                progress = json.loads(progress)
                # 获取 plan_url 字符串
                if progress is not None:
                    for doc in progress:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                progress_table.append({
                                    "progress": url,
                                    'progress_name': name
                                })

            self.workflow_detail.add_kv_table('总控计划文档', {'message': result_list})
            if result_list:
                variables = {
                    "zkjh_result_list": result_list,
                    "progress_table": progress_table
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceXmqd(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceXmqd, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def master_control_plan_upload(self, TencentTicketId, ProjectName, MasterControlPlan):
        """
            总控计划上传
        """
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        insert_data = {
            'dcopsTicketId': TencentTicketId,
            'project_name': ProjectName,
            'progress': str(MasterControlPlan)
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert("file_storage_data", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class ZkjhSupervisionExaminationApproval(AjaxTodoBase):
    """
        总控计划-监理审批
    """

    def __init__(self):
        super(ZkjhSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_supervision_approval = process_data.get('zkjh_supervision_approval')
        zkjh_supervision_remark = process_data.get('zkjh_supervision_remark')

        if zkjh_supervision_approval == '驳回':
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 1

            zkjh_jl = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_supervision_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }

            if system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": zkjh_jl,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

                variables = {
                    'zkjh_supervision_approval': zkjh_supervision_approval,
                    'zkjh_supervision_remark': zkjh_supervision_remark,
                    'zkjh_supervision_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
            elif system_type == "Dcops":
                system_id = self.ctx.variables.get("system_id")
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                            "data": zkjh_jl,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']['result']

                # 将 result 字符串解析为字典
                result_dict = json.loads(result_str)

                # 获取 code 的值
                code = result_dict.get('code')
                message = result_dict.get('message')
                if code != 0:
                    return {'code': -1, 'msg': f'{message}'}

                variables = {
                    'zkjh_supervision_approval': zkjh_supervision_approval,
                    'zkjh_supervision_remark': zkjh_supervision_remark,
                    'zkjh_supervision_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_supervision_approval == '同意':
            if not zkjh_supervision_remark:
                zkjh_supervision_remark = '无'

            variables = {
                'zkjh_supervision_approval': zkjh_supervision_approval,
                'zkjh_supervision_remark': zkjh_supervision_remark
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhItemApproval(AjaxTodoBase):
    """
        总控计划-项管审批
    """

    def __init__(self):
        super(ZkjhItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_item_approval = process_data.get('zkjh_item_approval')
        zkjh_item_remark = process_data.get('zkjh_item_remark')

        if zkjh_item_approval == '驳回':
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 2

            zkjh_xg = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_item_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": zkjh_xg,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

                variables = {
                    'zkjh_item_approval': zkjh_item_approval,
                    'zkjh_item_remark': zkjh_item_remark,
                    'zkjh_item_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
            elif system_type == "Dcops":
                system_id = self.ctx.variables.get("system_id")
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                            "data": zkjh_xg,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)
                result_str = info['data']['result']

                # 将 result 字符串解析为字典
                result_dict = json.loads(result_str)

                # 获取 code 的值
                code = result_dict.get('code')
                message = result_dict.get('message')
                if code != 0:
                    return {'code': -1, 'msg': f'{message}'}

                variables = {
                    'zkjh_item_approval': zkjh_item_approval,
                    'zkjh_item_remark': zkjh_item_remark,
                    'zkjh_item_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_item_approval == '同意':
            if not zkjh_item_remark:
                zkjh_item_remark = '无'

            variables = {
                'zkjh_item_approval': zkjh_item_approval,
                'zkjh_item_remark': zkjh_item_remark
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhPMApproval(AjaxTodoBase):
    """
        总控计划-PM审批
    """

    def __init__(self):
        super(ZkjhPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        system_type = self.ctx.variables.get("system_type")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_PM_approval = process_data.get('zkjh_PM_approval')
        zkjh_PM_remark = process_data.get('zkjh_PM_remark')

        if zkjh_PM_approval == '驳回':
            if not zkjh_PM_remark:
                zkjh_PM_remark = '无'
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 3

            zkjh_PM = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": zkjh_PM,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

                variables = {
                    'zkjh_PM_approval': zkjh_PM_approval,
                    'zkjh_PM_remark': zkjh_PM_remark,
                    'zkjh_PM_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
            elif system_type == "Dcops":
                system_id = self.ctx.variables.get("system_id")
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                            "data": zkjh_PM,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)
                result_str = info['data']['result']

                # 将 result 字符串解析为字典
                result_dict = json.loads(result_str)

                # 获取 code 的值
                code = result_dict.get('code')
                message = result_dict.get('message')
                if code != 0:
                    return {'code': -1, 'msg': f'{message}'}

                variables = {
                    'zkjh_PM_approval': zkjh_PM_approval,
                    'zkjh_PM_remark': zkjh_PM_remark,
                    'zkjh_PM_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_PM_approval == '同意':
            if not zkjh_PM_remark:
                zkjh_PM_remark = '无'
            examine_approve = 1
            role = 4

            zkjh_PM = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": zkjh_PM,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

                variables = {
                    'zkjh_PM_approval': zkjh_PM_approval,
                    'zkjh_PM_remark': zkjh_PM_remark,
                    'zkjh_PM_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)
            elif system_type == "Dcops":
                system_id = self.ctx.variables.get("system_id")
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                            "data": zkjh_PM,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                info = calling_external_interfaces(interface_info, data)
                result_str = info['data']['result']

                # 将 result 字符串解析为字典
                result_dict = json.loads(result_str)

                # 获取 code 的值
                code = result_dict.get('code')
                message = result_dict.get('message')
                if code != 0:
                    return {'code': -1, 'msg': f'{message}'}

                variables = {
                    'zkjh_PM_approval': zkjh_PM_approval,
                    'zkjh_PM_remark': zkjh_PM_remark,
                    'zkjh_PM_info': info
                }
                GnetopsTodoEnd(self.ctx.task_id, process_user)
                flow.complete_task(self.ctx.task_id, variables=variables)


class QdwdPMApproval(AjaxTodoBase):
    """
        启动文档-PM审批
    """

    def __init__(self):
        super(QdwdPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        qdwd_PM_approval = process_data.get('qdwd_PM_approval')
        qdwd_PM_remark = process_data.get('qdwd_PM_remark')

        if qdwd_PM_approval == '驳回':
            conditions = {
                'dcopsTicketId': ticket_id,
                'project_name': project_name,
            }
            update_data = {
                'minutes_of_start_up_meeting': None,
                'safety': None,
                'quality_and_other': None
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("file_storage_data", update_data, conditions)
            tb_db.commit()

        variables = {
            'qdwd_PM_approval': qdwd_PM_approval,
            'qdwd_PM_remark': qdwd_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ExternalInterfacePersonInput(AjaxTodoBase):
    """
        外部接口人输入
    """

    def __init__(self):
        super(ExternalInterfacePersonInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        team_info_insert_data = self.ctx.variables.get("team_info_insert_data")
        minutes_of_start_up_meeting = process_data.get("minutes_of_start_up_meeting", None)
        progress = process_data.get("progress", None)
        safety = process_data.get("safety", None)
        quality_and_other = process_data.get("quality_and_other", None)

        # 需求交付时间
        demand_delivery = self.ctx.variables.get("demand_delivery")
        PM = self.ctx.variables.get("PM")
        architecture = self.ctx.variables.get('architecture')
        beautification = self.ctx.variables.get('beautification')
        tree_sutra = self.ctx.variables.get('tree_sutra')
        weak_current = self.ctx.variables.get('weak_current')
        business = self.ctx.variables.get('business')
        supply_chain = self.ctx.variables.get('supply_chain')
        inform_a_person = self.ctx.variables.get('inform_a_person')
        if PM:
            PM_list = PM.split(';')
        else:
            PM_list = []
        if architecture:
            architecture_list = architecture.split(';')
        else:
            architecture_list = []
        if beautification:
            beautification_list = beautification.split(';')
        else:
            beautification_list = []
        if tree_sutra:
            tree_sutra_list = tree_sutra.split(';')
        else:
            tree_sutra_list = []
        if weak_current:
            weak_current_list = weak_current.split(';')
        else:
            weak_current_list = []
        if business:
            business_list = business.split(';')
        else:
            business_list = []
        if supply_chain:
            supply_chain_list = supply_chain.split(';')
        else:
            supply_chain_list = []
        if inform_a_person:
            inform_a_person_list = inform_a_person.split(';')
        else:
            inform_a_person_list = []

        receivers_list = (PM_list + architecture_list + beautification_list + tree_sutra_list + supply_chain_list +
                          weak_current_list + business_list + inform_a_person_list)
        # 企微发送人
        new_list = list(set(filter(None, receivers_list)))
        # 邮件发送人
        email_list = []
        for i in new_list:
            i += "@tencent.com"
            email_list.append(i)

        # 企微内容
        chat_data = f"{project_name}已启动，交付目标为{demand_delivery},请各团队知悉，谢谢！"
        # 邮件内容
        email_data = ''
        email_data += f"<p><strong>【项目名称】:</strong> {project_name}</p>"
        email_data += f"<p><strong>【阶段】:</strong> 项目启动阶段</p>"
        email_data += f"<p><strong>【交付时间】:</strong> {demand_delivery}</p>"
        email_data += (f"<p><strong>【团队成员】:</strong>"
                       f" PM：{PM}，架构：{architecture}，优化：{beautification}，数经：{tree_sutra}，弱电：{weak_current}，"
                       f"商务：{business}，供应链：{supply_chain}，知会人：{inform_a_person}</p>")
        email_data += (f"<p><strong>【工单链接】:</strong> "
                       f"<a href='https://dcops.woa.com/operateManage/business/ToDoDetails?params={ticket_id}"
                       f"&isHistory=1'>点击此处查看工单详情</a></p>")

        title = f"{project_name} 项目启动已完成"
        # 企微
        tof.send_company_wechat_message(receivers=new_list, title=title, message=chat_data)
        # 邮件
        tof.send_email(sendTitle=title, msgContent=email_data, sendTo=email_list)

        team_info_insert_data['AHU'] = process_data.get('AHU')
        team_info_insert_data['firewood_hair'] = process_data.get('firewood_hair')
        team_info_insert_data['low_pressure_cabinet'] = process_data.get('low_pressure_cabinet')
        team_info_insert_data['medium_pressure_cabinet'] = process_data.get('medium_pressure_cabinet')
        team_info_insert_data['transformers'] = process_data.get('transformers')
        team_info_insert_data['synthesis_distribution_cabinet'] = process_data.get('synthesis_distribution_cabinet')
        team_info_insert_data['HVDC'] = process_data.get('HVDC')
        team_info_insert_data['battery'] = process_data.get('battery')
        team_info_insert_data['PDU'] = process_data.get('transformers')
        team_info_insert_data['cabinet'] = process_data.get('cabinet')
        team_info_insert_data['integrator'] = process_data.get('integrator')
        team_info_insert_data['item_tube'] = process_data.get('item_tube')
        team_info_insert_data['supervision_and_management'] = process_data.get('supervision_and_management')
        team_info_insert_data['third_party_testing'] = process_data.get('third_party_testing')

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("project_team_information_data", team_info_insert_data)
        tb_db.commit()

        variables = {
            'AHU': process_data.get('AHU', ""),
            'firewood_hair': process_data.get('firewood_hair', ""),
            'low_pressure_cabinet': process_data.get('low_pressure_cabinet', ""),
            'medium_pressure_cabinet': process_data.get('medium_pressure_cabinet', ""),
            'transformers': process_data.get('transformers', ""),
            'synthesis_distribution_cabinet': process_data.get('synthesis_distribution_cabinet', ""),
            'HVDC': process_data.get('HVDC', ""),
            'battery': process_data.get('battery', ""),
            'PDU': process_data.get('PDU', ""),
            'cabinet': process_data.get('cabinet', ""),
            'integrator': process_data.get('integrator', ""),
            'item_tube': process_data.get('item_tube', ""),
            'supervision_and_management': process_data.get('supervision_and_management', ""),
            'third_party_testing': process_data.get('third_party_testing', ""),
            "minutes_of_start_up_meeting": minutes_of_start_up_meeting,
            "progress": progress,
            "safety": safety,
            "quality_and_other": quality_and_other
        }

        # 结束dcops代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class StartFileUpload(AjaxTodoBase):
    """
        启动文件上传
    """

    def __init__(self):
        super(StartFileUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        minutes_of_start_up_meeting = process_data.get("minutes_of_start_up_meeting", None)
        safety = process_data.get("safety", None)
        quality_and_other = process_data.get("quality_and_other", None)

        minutes_of_start_up_meeting_url = []
        minutes_of_start_up_meeting_table = []
        if minutes_of_start_up_meeting is not None:
            for doc in minutes_of_start_up_meeting:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        minutes_of_start_up_meeting_url.append(url)
                        minutes_of_start_up_meeting_table.append({
                            "minutes_of_start_up_meeting": url,
                            'minutes_of_start_up_meeting_name': name
                        })

        safety_url = []
        safety_table = []
        if safety is not None:
            for doc in safety:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        safety_url.append(url)
                        safety_table.append({
                            "safety": url,
                            'safety_name': name
                        })

        quality_and_other_url = []
        quality_and_other_table = []
        if quality_and_other is not None:
            for doc in quality_and_other:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        quality_and_other_url.append(url)
                        quality_and_other_table.append({
                            "quality_and_other": url,
                            'quality_and_other_name': name
                        })
        minutes_of_start_up_meeting_join = ', '.join(minutes_of_start_up_meeting_url)
        safety_join = ', '.join(safety_url)
        quality_and_other_join = ', '.join(quality_and_other_url)
        conditions = {
            'dcopsTicketId': ticket_id,
            'project_name': project_name,
        }
        update_data = {
            'minutes_of_start_up_meeting': minutes_of_start_up_meeting_join,
            'safety': safety_join,
            'quality_and_other': quality_and_other_join
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("file_storage_data", update_data, conditions)
        tb_db.commit()

        variables = {
            "minutes_of_start_up_meeting": minutes_of_start_up_meeting_table,
            "safety": safety_table,
            "quality_and_other": quality_and_other_table
        }

        # 结束dcops代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)
