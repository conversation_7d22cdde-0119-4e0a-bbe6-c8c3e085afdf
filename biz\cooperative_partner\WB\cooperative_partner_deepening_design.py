import ast
import datetime
import json

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


class DeepeningDesignWB(object):
    """
        深化设计
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(DeepeningDesignWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def in_depth_design_data_review(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            深化设计资料审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/in_depth_design_data_review"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'in_depth_design_data_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceShsj(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceShsj, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_deepen_design_data_upload(self):
        """
            等待深化设计资料上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name, file_type, url, upload_time, is_need, file_remark " \
              "FROM maintenance_upload_data " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND node = '架构' "
        result_list = db.get_all(sql)
        url_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            file_name = record.get('file_name', "")  # 文件名称
            file_type = record.get('file_type', "")  # 文件类型
            url = record.get('url', "[]")  # 文件
            is_need = record.get('is_need', "")  # 文件上传人
            file_remark = record.get('file_remark', "")  # 文件上传人
            upload_time = record.get('upload_time', "")  # 文件上传时间

            # 尝试将 url 字符串解析为列表
            try:
                urls = ast.literal_eval(url)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                urls = []  # 如果解析失败，设置为空列表

            url_list = []
            # 确保 urls 是一个列表
            if isinstance(urls, list):
                # 为每个 URL 创建一个新的字典
                for url in urls:
                    url_list.append({"url": url})

            url_file_list.append({
                'file_name': file_name,
                'file_type': file_type,
                'url': url_list,
                'is_need': is_need,
                'file_remark': file_remark,
                'upload_time': upload_time
            })

        self.workflow_detail.add_kv_table('深化设计资料上传信息', {'message': result_list})
        if result_list:
            variables = {
                "final_file_list": url_file_list,
                "result_list": result_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceShsj(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceShsj, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_ticket(self, ProjectName, PartnerTicketId, Facilitator):
        """
            创建深化设计工单（起腾讯侧工单）
        """
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        res_list = []
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号{ProjectName}"
        processTitle = date_string + "深化设计"
        if system_type == '外部':
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'Facilitator': Facilitator,
                        'project_name': ProjectName,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_partner_deepen_design",
                    "Source": "",
                    "TicketDescription": "深化设计流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)
        elif system_type == 'Dcops':
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'Facilitator': Facilitator,
                        'project_name': ProjectName,
                        'system_id': system_id,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_partner_deepen_design_Dcops",
                    "Source": "",
                    "TicketDescription": "深化设计流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)

        for row in res_list:
            TicketId = row.get('TicketId')

        return {
            'code': 200,
            'msg': '成功',
            'data': TicketId
        }

    def deepen_design_data_upload(self, TencentTicketId, ProjectName, FinalFileList):
        """
            深化设计资料上传
        """
        insert_data = []
        un_remark_list = []
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}

        for row in FinalFileList:
            file_type = row.get('FileType')
            file_name = row.get('FileName')
            file = row.get('File')
            is_need = row.get('IsNeed')
            remark = row.get('FileRemark')

            # 检查是否需要上传
            if is_need == '不涉及':
                if not remark:
                    un_remark_list.append(f"【{file_name}】")

            insert_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'file_type': file_type,
                'file_name': file_name,
                'url': str(file),
                'is_need': is_need,
                'file_remark': remark,
                "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                'node': "架构"
            })

        if un_remark_list:
            return {"code": -1, "msg": f"{un_remark_list}不涉及未填写备注"}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert_batch("maintenance_upload_data", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class ShsjSupervisionExaminationApproval(AjaxTodoBase):
    """
        深化设计资料-监理审批
    """

    def __init__(self):
        super(ShsjSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        shsj_supervision_approval = process_data.get('shsj_supervision_approval')
        shsj_supervision_remark = process_data.get('shsj_supervision_remark')

        if shsj_supervision_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_upload_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND node = '架构' "
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'shsj_supervision_approval': shsj_supervision_approval,
            'shsj_supervision_remark': shsj_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ShsjItemApproval(AjaxTodoBase):
    """
        深化设计资料-项管审批
    """

    def __init__(self):
        super(ShsjItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        shsj_item_approval = process_data.get('shsj_item_approval')
        shsj_item_remark = process_data.get('shsj_item_remark')

        if shsj_item_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_upload_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND node = '架构' "
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'shsj_item_approval': shsj_item_approval,
            'shsj_item_remark': shsj_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ShsjPmApproval(AjaxTodoBase):
    """
        深化设计资料-PM审批
    """

    def __init__(self):
        super(ShsjPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        shsj_PM_approval = process_data.get('shsj_PM_approval')
        shsj_PM_remark = process_data.get('shsj_PM_remark')

        if shsj_PM_approval == '驳回':
            query_sql = "DELETE FROM maintenance_upload_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND node = '架构' "
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        # 获取上传文件目录
        PM_file_list = process_data.get("PM_file_list")
        PM_file_list_save = []
        # 定义存库的文件列表
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        fail_list = []
        for file in PM_file_list:
            url = file.get("url")
            file_type = file.get("file_type")
            file_name = file.get("file_name")
            if url:
                for item in url:
                    if item["status"] == "fail":
                        fail_list.append(f"【{file_name}:{item['name']}】")
                PM_file_list_save.append({
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": json.dumps(url),
                    "uploader": self.ctx.variables.get("integrator_project_manager"),
                    "upload_time": datetime.datetime.now().strftime('%Y-%m-%d'),
                    "node": "架构",
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("maintenance_upload_data", PM_file_list_save)
        tb_db.commit()

        variables = {
            'shsj_PM_approval': shsj_PM_approval,
            'shsj_PM_remark': shsj_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
