import re
from datetime import datetime

from iBroker.lib import mysql, config


class ProgressManagement(object):
    def first_level_node(self, campus):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = \
            f"""
            SELECT serial_number, work_content, week_progress FROM construct_plan_data WHERE project_name = '{campus}'
            """

        # 执行查询语句
        result = db.get_all(query_sql)
        first_list = []
        for row in result:
            serial_number = row.get('serial_number', None)
            if serial_number is not None and serial_number.count('.') == 0:
                first_list.append(row)

        data = []
        for first_row in first_list:
            first_serial_number = first_row['serial_number']
            first_title = first_row['work_content']
            # 直接使用 week_progress 作为进度值
            first_progress = first_row.get('week_progress', 0)

            # 根据新逻辑判断 first_progress_state
            if first_progress / 100 == 1:
                first_progress_state = 2
            elif first_progress == 0:
                first_progress_state = 0
            else:
                first_progress_state = 1

            data.append({
                'serial_number': first_serial_number,
                'label': first_title,
                'stageWorkState': first_progress_state
            })
        return data

    def second_level_title(self, campus):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = \
            f"""
            SELECT serial_number, work_content, week_progress FROM construct_plan_data WHERE project_name = '{campus}'
            """

        # 执行查询语句
        result = db.get_all(query_sql)
        first_list = []
        second_list = []
        third_list = []

        # 按层级分离数据
        for row in result:
            serial_number = row.get('serial_number', None)
            if serial_number is not None and serial_number.count('.') == 0:
                first_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 1:
                second_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 2:
                third_list.append(row)

        # 必须展示的二级标题的serial_number列表
        required_serial_numbers = {
            "1.1", "2.1", "2.2", "2.3", "2.4", "2.5", "2.6", "2.7", "2.8", "3.1", "3.2", "3.3", "3.4", "3.5",
            "3.6", "3.7", "3.8", "4.1", "4.2", "4.3", "5.1", "5.2"
        }

        # 根据serial_number过滤二级列表
        second_list = [row for row in second_list if row.get('serial_number') in required_serial_numbers]

        # 构建二级和三级标题的映射关系
        second_third_mapping = {}
        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            second_title = second_row['work_content']  # 使用 work_content 作为标题
            second_third_mapping[second_serial_number] = {'title': second_title, 'third_list': []}

        for third_row in third_list:
            third_serial_number = third_row['serial_number']
            second_serial_number = '.'.join(third_serial_number.split('.')[:-1])
            if second_serial_number in second_third_mapping:
                second_third_mapping[second_serial_number]['third_list'].append(third_row)

        # 构建返回数据
        data = []
        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            second_data = second_third_mapping.get(second_serial_number)

            if second_data:
                third_list = second_data['third_list']

                # 直接使用 week_progress 作为进度值并除以100
                second_progress = second_row.get('week_progress', 0) / 100 if second_row.get(
                    'week_progress') is not None else 0

                # 确保进度值在 [0, 1] 范围内
                if second_progress > 1:
                    second_progress = 1
                elif second_progress < 0:
                    second_progress = 0

                # 确定进度状态
                if second_progress == 1:
                    second_progress_state = 2
                elif second_progress == 0:
                    second_progress_state = 0
                else:
                    second_progress_state = 1

                # 构建详情数据并将进度数据除以100
                details = {sub_row['work_content']: sub_row.get('week_progress', 0) / 100 if sub_row.get(
                    'week_progress') is not None else 0
                           for sub_row in result if
                           sub_row and sub_row.get('serial_number') and
                           sub_row['serial_number'].startswith(second_serial_number) and
                           sub_row['serial_number'].count('.') == 2}

                # 如果没有三级标题，将二级标题的进度添加到详情中
                if not details and second_progress is not None:
                    details[second_row['work_content']] = second_progress

                data.append({
                    'serial_number': second_serial_number,
                    'label': second_row['work_content'],  # 使用 work_content 作为标签
                    'specialWorkState': second_progress_state,
                    'rate': second_progress,
                    'details': details
                })

        return data

    def second_level_title_hj(self, campus):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = \
            f"""
            SELECT serial_number, work_content, week_progress FROM construct_plan_data WHERE project_name = '{campus}'
            """

        # 执行查询语句
        result = db.get_all(query_sql)
        first_list = []
        second_list = []
        third_list = []

        # 按层级分离数据
        for row in result:
            serial_number = row.get('serial_number', None)
            if serial_number is not None and serial_number.count('.') == 0:
                first_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 1:
                second_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 2:
                third_list.append(row)

        # 构建二级和三级标题的映射关系
        second_third_mapping = {}
        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            second_title = second_row['work_content']  # 使用 work_content 作为标题
            second_third_mapping[second_serial_number] = {'title': second_title, 'third_list': []}

        for third_row in third_list:
            third_serial_number = third_row['serial_number']
            second_serial_number = '.'.join(third_serial_number.split('.')[:-1])
            if second_serial_number in second_third_mapping:
                second_third_mapping[second_serial_number]['third_list'].append(third_row)

        # 构建返回数据
        data = []
        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            second_data = second_third_mapping.get(second_serial_number)

            if second_data:
                third_list = second_data['third_list']

                # 直接使用 week_progress 作为进度值并除以100，保留两位小数
                second_progress = round(second_row.get('week_progress', 0) / 100, 2) if second_row.get(
                    'week_progress') is not None else 0

                # 确保进度值在 [0, 1] 范围内
                if second_progress > 1:
                    second_progress = 1
                elif second_progress < 0:
                    second_progress = 0

                # 确定进度状态
                if second_progress == 1:
                    second_progress_state = 2
                elif second_progress == 0:
                    second_progress_state = 0
                else:
                    second_progress_state = 1

                # 构建详情数据并将进度数据除以100，保留两位小数
                details = {sub_row['work_content']: round(sub_row.get('week_progress', 0) / 100, 2) if sub_row.get(
                    'week_progress') is not None else 0
                           for sub_row in result if
                           sub_row and sub_row.get('serial_number') and
                           sub_row['serial_number'].startswith(second_serial_number) and
                           sub_row['serial_number'].count('.') == 2}

                # 如果没有三级标题，将二级标题的进度添加到详情中
                if not details and second_progress is not None:
                    details[second_row['work_content']] = second_progress

                data.append({
                    'serial_number': second_serial_number,
                    'label': second_row['work_content'],  # 使用 work_content 作为标签
                    'specialWorkState': second_progress_state,
                    'rate': second_progress,
                    'details': details
                })
        # 根据 serial_number 的开头分类到不同的 id 中
        nested_data = {}
        for item in data:
            serial_number = item['serial_number']
            if serial_number.startswith('1'):
                key = 's1'
            elif serial_number.startswith('2'):
                key = 's2'
            elif serial_number.startswith('3'):
                key = 's3'
            elif serial_number.startswith('4'):
                key = 's4'
            elif serial_number.startswith('5'):
                key = 's5'
            else:
                continue  # 如果 serial_number 不以 1-5 开头，则跳过
            if key not in nested_data:
                nested_data[key] = []
            nested_data[key].append(item)
        # 将 nested_data 转换为列表格式
        final_data = []
        for key, items in nested_data.items():
            final_data.append({
                'id': key,
                'meta': items
            })
        return final_data

    def project_under_construction(self, project_state=None):
        def sort_key(item):
            if not item['progress'] and not item['state'] and not item['currentProgress'] \
                    and not item['risksIssues'] and not item['helpSolve']:
                return 1  # 没有数据的项目放到最后面
            else:
                return 0  # 有数据的项目放到前面

        db = mysql.new_mysql_instance("tbconstruct")
        project_sql = "SELECT project_name,demand_delivery,campus,project FROM risk_early_warning_data"
        project_state_sql = f"SELECT campus,project_name FROM summary_data WHERE state='{project_state}'"
        project_result = db.get_all(project_sql)
        project_state_result = db.get_all(project_state_sql)

        project_data = {}
        for item in project_result:
            project_name = item.get('project_name', None)
            campus = item.get('campus', None)
            project = item.get('project', None)
            demand_delivery = item.get('demand_delivery', None)
            match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
            if match:
                campus = match.group(1)
                project = match.group(2)
            else:
                campus = campus
                project = project

            project_data[project_name] = {
                'campus': campus,
                'project': project,
                'deliveryTarget': demand_delivery,
                'progress': '',
                'state': '',
                'currentProgress': '',
                'risksIssues': '',
                'helpSolve': '',
            }

            query_sql = f"SELECT wrr.details, wci.project_name, wrr.table_of_contents " \
                        f"FROM week_create_info wci " \
                        f"JOIN weekly_rief_report wrr ON wci.ticketid = wrr.ticketid " \
                        f"WHERE wci.project_name = '{project_name}'"

            query_result = db.get_all(query_sql)

            if not query_result:
                query_sql = f"SELECT wrr.details, rewd.project_name, wrr.table_of_contents " \
                            f"FROM risk_early_warning_data rewd " \
                            f"JOIN weekly_rief_report wrr ON rewd.dcopsTicketId = wrr.ticketid " \
                            f"WHERE rewd.project_name = '{project_name}'"
                query_result = db.get_all(query_sql)
            for row in query_result:
                details = row.get('details', None)
                table_of_contents = row.get('table_of_contents', None)

                if project_name in project_data:
                    if table_of_contents == '整体形象进度':
                        project_data[project_name]['progress'] = details
                    elif table_of_contents == '状态':
                        project_data[project_name]['state'] = details
                    elif table_of_contents == '工程进度':
                        project_data[project_name]['currentProgress'] = details
                    elif table_of_contents == '本周问题及风险':
                        project_data[project_name]['risksIssues'] = details
                    elif table_of_contents == '解决措施/求助':
                        project_data[project_name]['helpSolve'] = details
        # 没有数据的项目往后面排
        data = sorted(list(project_data.values()), key=sort_key)
        # 按 deliveryTarget 从小到大排序
        data = sorted(data, key=lambda x: x['deliveryTarget'])

        # # 屏蔽已交付状态的项目
        # state_sql = "SELECT project_name, final_conclusion " \
        #             "FROM maintenance_main_process"
        # state_result = db.get_all(state_sql)
        # for row in state_result:
        #     project_name = row.get('project_name')
        #     final_conclusion = row.get('final_conclusion')
        #     for item in data:
        #         if item.get('campus') + item.get('project') == project_name and final_conclusion == '确认':
        #             data.remove(item)

        # 获取七彩石配置
        delivered_project = config.get_config_map(
            "delivered_project"
        )
        for row in delivered_project:
            campus = row.get('campus')
            project = row.get('project')

            data = [item for item in data if item.get('campus') != campus or item.get('project') != project]

        # 根据 project_state_result 进行过滤
        if project_state_result:
            # 提取 campus 和 project_name
            valid_projects = {(row['campus'], row['project_name']) for row in project_state_result}
            data = [item for item in data if (item['campus'], item['project']) in valid_projects]
        return data

    def new_project_under_construction(self, project_state=None):
        def sort_key(item):
            if not item['progress'] and not item['state'] and not item['currentProgress'] \
                    and not item['risksIssues'] and not item['helpSolve']:
                return 1  # 没有数据的项目放到最后面
            else:
                return 0  # 有数据的项目放到前面

        db = mysql.new_mysql_instance("tbconstruct")
        project_sql = "SELECT project_name,demand_delivery,campus,project FROM risk_early_warning_data"
        project_state_sql = f"SELECT campus,project_name FROM summary_data WHERE state='{project_state}'"
        project_result = db.get_all(project_sql)
        project_state_result = db.get_all(project_state_sql)

        project_data = {}
        for item in project_result:
            project_name = item.get('project_name', None)
            campus = item.get('campus', None)
            project = item.get('project', None)
            demand_delivery = item.get('demand_delivery', None)
            match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
            if match:
                campus = match.group(1)
                project = match.group(2)
            else:
                campus = campus
                project = project

            project_data[project_name] = {
                'campus': campus,
                'project': project,
                'deliveryTarget': demand_delivery,
                'progress': '',
                'state': '',
                'currentProgress': '',
                'risksIssues': '',
                'helpSolve': '',
            }

            query_sql = f"""
                    SELECT weekly_report_cycle, current_stage, overall_image_progress,state,arrival_equipment,
                    project_progress,week_completion,next_week_work_plan,safety_management,
                    quality_control,construction_personnel_input,follow_last_week_key_issues,issues_risks_week,
                    solution_measure
                    FROM weekly_report_brief
                    WHERE project_name = '{project_name}'
                    AND now_time = (SELECT MAX(now_time) 
                    FROM weekly_report_brief 
                    WHERE project_name = '{project_name}')
            """

            query_result = db.get_all(query_sql)
            for row in query_result:
                overall_image_progress = row.get('overall_image_progress', None)
                state = row.get('state', None)
                project_progress = row.get('project_progress', None)
                issues_risks_week = row.get('issues_risks_week', None)
                solution_measure = row.get('solution_measure', None)

                if overall_image_progress:
                    # 尝试提取字符串中的数字信息
                    match = re.search(r'(\d+\.?\d*)%?', overall_image_progress)
                    if match:
                        try:
                            # 去掉 % 符号并转换为浮点数
                            overall_image_progress = float(match.group(1))
                        except ValueError:
                            overall_image_progress = 0
                    else:
                        overall_image_progress = 0

                if project_name in project_data:
                    project_data[project_name]['progress'] = overall_image_progress
                    project_data[project_name]['state'] = state
                    project_data[project_name]['currentProgress'] = project_progress
                    project_data[project_name]['risksIssues'] = issues_risks_week
                    project_data[project_name]['helpSolve'] = solution_measure

        # 按 deliveryTarget 从小到大排序
        data = sorted(list(project_data.values()), key=lambda x: x['deliveryTarget'])
        # 没有数据的项目往后面排
        data = sorted(data, key=sort_key)

        # 根据 project_state_result 进行过滤
        if project_state_result:
            # 提取 campus 和 project_name
            valid_projects = {(row['campus'], row['project_name']) for row in project_state_result}
            data = [item for item in data if (item['campus'], item['project']) in valid_projects]
        return data

    def state_filtrate(self, state):
        state_data = self.project_under_construction()
        data = []
        for row in state_data:
            project_name = row['project']
            campus = row['campus']
            delivery_target = row['deliveryTarget']
            progress = row['progress']
            state_value = row['state']
            current_progress = row['currentProgress']
            risks_issues = row['risksIssues']
            help_solve = row['helpSolve']

            # 提取括号外的数据
            state_value_without_parentheses = state_value.split('(', 1)[0].strip()

            if state in state_value_without_parentheses:  # 判断 state 是否在 state_value 中
                state_filtrate = {
                    'project': project_name,
                    'campus': campus,
                    'deliveryTarget': delivery_target,
                    'progress': progress,
                    'state': state_value,
                    'currentProgress': current_progress,
                    'risksIssues': risks_issues,
                    'helpSolve': help_solve,
                }
                data.append(state_filtrate)

        return data

    def data_migration(self):
        db = mysql.new_mysql_instance("tbconstruct")
        project_name_sql = "SELECT project_name FROM risk_early_warning_data"
        project_name_result = db.get_all(project_name_sql)
        content_mapping = {
            '周报周期': 'weekly_report_cycle',
            '当前阶段': 'current_stage',
            '整体形象进度': 'overall_image_progress',
            '状态': 'state',
            '设备到货': 'arrival_equipment',
            '工程进度': 'project_progress',
            '本周完成情况': 'week_completion',
            '下周工作计划': 'next_week_work_plan',
            '安全管理': 'safety_management',
            '质量管理': 'quality_control',
            '施工人员投入': 'construction_personnel_input',
            '上周重点问题跟踪情况': 'follow_last_week_key_issues',
            '本周问题及风险': 'issues_risks_week',
            '解决措施/求助': 'solution_measure'
        }

        all_insert_data = []
        for item in project_name_result:
            project_name = item.get('project_name', None)
            queries = [
                f"""
                    SELECT wrr.details, wci.project_name, wrr.table_of_contents, wrr.ticket_id, wrr.now_time 
                    FROM week_create_info wci 
                    JOIN weekly_rief_report wrr ON wci.ticketid = wrr.ticketid 
                    WHERE wci.project_name = '{project_name}'
                    AND wrr.now_time = (
                        SELECT MAX(now_time) 
                        FROM weekly_rief_report 
                        WHERE ticketid 
                        IN ( 
                            SELECT ticketid 
                            FROM week_create_info 
                            WHERE project_name = '{project_name}'
                        )
                    )
                    """,
                f"""
                    SELECT wrr.details, rewd.project_name, wrr.table_of_contents, wrr.ticket_id, wrr.now_time 
                    FROM risk_early_warning_data rewd 
                    JOIN weekly_rief_report wrr ON rewd.dcopsTicketId = wrr.ticketid 
                    WHERE rewd.project_name = '{project_name}'
                    AND wrr.now_time = (
                        SELECT MAX(now_time) 
                        FROM weekly_rief_report 
                        WHERE ticketid 
                        IN ( 
                            SELECT dcopsTicketId 
                            FROM risk_early_warning_data 
                            WHERE project_name = '{project_name}'
                        )
                    )
                    """
            ]

            query_result = []
            for query in queries:
                result = db.get_all(query)
                if result:
                    query_result = result
                    break

            insert_data = {
                'project_name': project_name,
                'ticket_id': None,
                'now_time': None,
                'weekly_report_cycle': None,
                'current_stage': None,
                'overall_image_progress': None,
                'state': None,
                'arrival_equipment': None,
                'project_progress': None,
                'week_completion': None,
                'next_week_work_plan': None,
                'safety_management': None,
                'quality_control': None,
                'construction_personnel_input': None,
                'follow_last_week_key_issues': None,
                'issues_risks_week': None,
                'solution_measure': None
            }

            for row in query_result:
                details = row.get('details', None)
                table_of_contents = row.get('table_of_contents', None)
                insert_data['ticket_id'] = row.get('ticket_id', None)
                insert_data['now_time'] = row.get('now_time', None)

                if table_of_contents in content_mapping:
                    if table_of_contents == '整体形象进度':
                        try:
                            if details:
                                progress = float(details)
                                insert_data[content_mapping[table_of_contents]] = f"{progress * 100:.2f}%"
                        except ValueError:
                            insert_data[content_mapping[table_of_contents]] = details
                    else:
                        insert_data[content_mapping[table_of_contents]] = details

            if any(insert_data.values()):
                all_insert_data.append(insert_data)
        db.insert_batch("weekly_report_brief", all_insert_data)
        return all_insert_data
