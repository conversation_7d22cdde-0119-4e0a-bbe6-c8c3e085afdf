import datetime

from iBroker.lib import mysql
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_application_process.template import TemplateAction
from biz.construction_application_process.field_handle import (
    ConstructionApplicationFieldHandle,
)
from biz.construction_application_process.construction_application_info import (
    ConstructionApplicationInfoQuery,
)


# 项目报建 分项计划 上传
class ProjectConstructionApplicationUpload(AjaxTodoBase):
    def __init__(self):
        super(ProjectConstructionApplicationUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取上传文件url
        xmbj_sub_plan = process_data.get("xmbj_sub_plan")
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_file_exit_flag(
            xmbj_sub_plan, "项目报建计划"
        )
        if not file_exit_flag:
            return file_exit_msg
        # 模板字段
        template_field = {
            "2.4.1": "项目立项",
            "2.4.2": "设计合同签订上传",
            "2.4.3": "图纸送审",
            "2.4.4": "监理直接发包、上传",
            "2.4.5": "工程直接发包、上传",
            "2.4.6": "监理合同签订上传",
            "2.4.7": "工程合同签订上传",
            "2.4.8": "农民工保证金监管合同签订上传",
            "2.4.9": "工伤意外险",
            "2.4.10": "施工许可证下发",
            "2.4.11": "消防备案",
            "2.4.12": "竣工备案",
            # "2.4.13": "档案馆资料归档",
        }
        # 判断模板是否正确
        template_true_msg, template_true_flag = TemplateAction.get_template_flag(
            xmbj_sub_plan, "项目报建计划", template_field
        )
        if not template_true_flag:
            return template_true_msg
        file_write_msg, file_write_flag = TemplateAction.get_file_input_flag(
            xmbj_sub_plan, "项目报建计划"
        )
        if not file_write_flag:
            return file_write_msg
        xmbj_sub_plan_file_json = TemplateAction.get_file_json(
            xmbj_sub_plan, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "xmbj_sub_plan": xmbj_sub_plan,
            "xmbj_sub_plan_file_json": xmbj_sub_plan_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 项目立项
class ProjecEstablishment(AjaxTodoBase):
    def __init__(self):
        super(ProjecEstablishment, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xmlx_dict = process_data.get("xmlx_dict")
        upload_file_json = {"项目立项报告": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "project_establishment_report_file" in xmlx_dict:
            upload_file_field = upload_file_field_map[
                "project_establishment_report_file"
            ]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                xmlx_dict["project_establishment_report_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                xmlx_dict["project_establishment_report_file"], process_user
            )
        xmlx_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "xmlx_dict": xmlx_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 设计合同签订上传
class DesignContractSigning(AjaxTodoBase):
    def __init__(self):
        super(DesignContractSigning, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sjhtqt_dict = process_data.get("sjhtqt_dict")
        upload_file_json = {"设计合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "design_contract_file" in sjhtqt_dict:
            upload_file_field = upload_file_field_map["design_contract_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                sjhtqt_dict["design_contract_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                sjhtqt_dict["design_contract_file"], process_user
            )
        sjhtqt_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sjhtqt_dict": sjhtqt_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 图纸送审（第一版）：无PM确认、状态回传星环节点；附件只有"送审图纸"、"审图报告"；
class DrawingsSubmittedReview(AjaxTodoBase):
    def __init__(self):
        super(DrawingsSubmittedReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        tzss_dict = process_data.get("tzss_dict")
        upload_file_json = {
            "送审图纸": [],
            "审图报告": [],
        }
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "approval_drawings_file" in tzss_dict:
            upload_file_field = upload_file_field_map["approval_drawings_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                tzss_dict["approval_drawings_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                tzss_dict["approval_drawings_file"], process_user
            )
        if "drawing_review_report_file" in tzss_dict:
            upload_file_field = upload_file_field_map["drawing_review_report_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                tzss_dict["drawing_review_report_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                tzss_dict["drawing_review_report_file"], process_user
            )
        tzss_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "tzss_dict": tzss_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 图纸送审（第二版）：有PM确认、状态回传星环节点。"审图报告"改为"审图合格证"、新增"结构力学书"附件上传。
class NewDrawingsSubmittedReview(AjaxTodoBase):
    def __init__(self):
        super(NewDrawingsSubmittedReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        tzss_dict = process_data.get("tzss_dict")
        upload_file_json = {
            "送审图纸": [],
            "审图合格证": [],
            "结构力学书": [],
        }
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        tzss_PM_file = []
        if "approval_drawings_file" in tzss_dict:
            upload_file_field = upload_file_field_map["approval_drawings_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                tzss_dict["approval_drawings_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                tzss_dict["approval_drawings_file"], process_user
            )
        if "drawing_review_certificate_file" in tzss_dict:
            upload_file_field = upload_file_field_map["drawing_review_certificate_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                tzss_dict["drawing_review_certificate_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                tzss_dict["drawing_review_certificate_file"], process_user
            )
            tzss_PM_file = [
                *tzss_PM_file,
                *tzss_dict["drawing_review_certificate_file"],
            ]
        if "structural_mechanics_book_file" in tzss_dict:
            upload_file_field = upload_file_field_map["structural_mechanics_book_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                tzss_dict["structural_mechanics_book_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                tzss_dict["structural_mechanics_book_file"], process_user
            )
            tzss_PM_file = [*tzss_PM_file, *tzss_dict["structural_mechanics_book_file"]]
        tzss_dict.setdefault("upload_file_json", upload_file_json)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "tzss_dict": tzss_dict,
            "tzss_PM_approval": 1,
            "tzss_PM_remark": "",
            "tzss_PM_file": tzss_PM_file,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 图纸送审-PM确认
class DrawingsSubmittedReviewPMCheck(AjaxTodoBase):
    def __init__(self):
        super(DrawingsSubmittedReviewPMCheck).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        tzss_PM_approval = process_data.get("tzss_PM_approval", 1)
        tzss_PM_remark = process_data.get("tzss_PM_remark", "")
        tzss_PM_reject_reason = ""
        if not tzss_PM_approval:
            tzss_PM_reject_reason = tzss_PM_remark
        tzss_PM_file = process_data.get("tzss_PM_file", [])
        tzss_PM_appendix_list = []
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
            tzss_PM_file, "附件上传"
        )
        if not file_exit_flag:
            return file_exit_msg
        for file in tzss_PM_file:
            tzss_PM_appendix_list.append(file["response"]["FileList"][0]["url"])
        # 取图纸送审实际完成时间
        tzss_dict = process_data.get("tzss_dict")
        date = datetime.datetime.strptime(
            tzss_dict.get("actual_finish_time"), "%Y-%m-%d"
        )
        tzss_PM_check_date = date.strftime("%Y-%m-%d %H:%M:%S")
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        temp = {}
        if tzss_PM_approval == 0:
            temp = {
                "tzss_responsible_person": todo_handler,
            }
        else:
            temp = {
                "jlfb_responsible_person": todo_handler,
                "gcfb_responsible_person": todo_handler,
                "jlhtqd_responsible_person": todo_handler,
                "gchtqj_responsible_person": todo_handler,
                "nmgbzjjghtqd_responsible_person": todo_handler,
                "gsywx_responsible_person": todo_handler,
            }
        variables = {
            "tzss_PM_approval": tzss_PM_approval,
            "tzss_PM_remark": tzss_PM_remark,
            "tzss_PM_reject_reason": tzss_PM_reject_reason,
            # 取图纸送审实际完成时间
            # "tzss_PM_check_date": str(
            #     datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # ),
            "tzss_PM_check_date": tzss_PM_check_date,
            "tzss_PM_file": tzss_PM_file,
            "tzss_PM_appendix_list": tzss_PM_appendix_list,
            **temp,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 监理直接发包、上传
class SupervisionContractAwarding(AjaxTodoBase):
    def __init__(self):
        super(SupervisionContractAwarding, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jlfb_dict = process_data.get("jlfb_dict")
        upload_file_json = {"监理直接发包、上传-上传文件(如有)": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "jlfb_other_file" in jlfb_dict:
            upload_file_field = upload_file_field_map["jlfb_other_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                jlfb_dict["jlfb_other_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                jlfb_dict["jlfb_other_file"], process_user
            )
        jlfb_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "jlfb_dict": jlfb_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 工程直接发包、上传
class EngineeringContractAwarding(AjaxTodoBase):
    def __init__(self):
        super(EngineeringContractAwarding, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gcfb_dict = process_data.get("gcfb_dict")
        upload_file_json = {"工程直接发包、上传-上传文件(如有)": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "gcfb_other_file" in gcfb_dict:
            upload_file_field = upload_file_field_map["gcfb_other_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                gcfb_dict["gcfb_other_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                gcfb_dict["gcfb_other_file"], process_user
            )
        gcfb_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gcfb_dict": gcfb_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 监理合同签订上传
class SupervisionContractSigning(AjaxTodoBase):
    def __init__(self):
        super(SupervisionContractSigning, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jlhtqd_dict = process_data.get("jlhtqd_dict")
        upload_file_json = {"监理合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "supervision_contract_file" in jlhtqd_dict:
            upload_file_field = upload_file_field_map["supervision_contract_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                jlhtqd_dict["supervision_contract_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                jlhtqd_dict["supervision_contract_file"], process_user
            )
        jlhtqd_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "jlhtqd_dict": jlhtqd_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 工程合同签订上传
class EngineeringContractSigning(AjaxTodoBase):
    def __init__(self):
        super(EngineeringContractSigning, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gchtqj_dict = process_data.get("gchtqj_dict")
        upload_file_json = {"工程合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "engineering_contract_file" in gchtqj_dict:
            upload_file_field = upload_file_field_map["engineering_contract_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                gchtqj_dict["engineering_contract_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                gchtqj_dict["engineering_contract_file"], process_user
            )
        gchtqj_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gchtqj_dict": gchtqj_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 农民工保证金监管合同签订上传
class MigrantWorkersSecurityDepositSupervisionContractSigning(AjaxTodoBase):
    def __init__(self):
        super(MigrantWorkersSecurityDepositSupervisionContractSigning, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        nmgbzjjghtqd_dict = process_data.get("nmgbzjjghtqd_dict")
        upload_file_json = {"农民工保证金监管合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "nmgbzjjght_file" in nmgbzjjghtqd_dict:
            upload_file_field = upload_file_field_map["nmgbzjjght_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                nmgbzjjghtqd_dict["nmgbzjjght_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                nmgbzjjghtqd_dict["nmgbzjjght_file"], process_user
            )
        nmgbzjjghtqd_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "nmgbzjjghtqd_dict": nmgbzjjghtqd_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 工伤意外险
class WorkRelatedAccidentInsurance(AjaxTodoBase):
    def __init__(self):
        super(WorkRelatedAccidentInsurance, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gsywx_dict = process_data.get("gsywx_dict")
        upload_file_json = {"保险合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "insurance_contract_file" in gsywx_dict:
            upload_file_field = upload_file_field_map["insurance_contract_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                gsywx_dict["insurance_contract_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                gsywx_dict["insurance_contract_file"], process_user
            )
        gsywx_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gsywx_dict": gsywx_dict,
            "sgxkz_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 施工许可证下发
class ConstructionPermitIssuance(AjaxTodoBase):
    def __init__(self):
        super(ConstructionPermitIssuance, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sgxkz_dict = process_data.get("sgxkz_dict")
        upload_file_json = {"施工证": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "construction_certificate_file" in sgxkz_dict:
            upload_file_field = upload_file_field_map["construction_certificate_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                sgxkz_dict["construction_certificate_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                sgxkz_dict["construction_certificate_file"], process_user
            )
        sgxkz_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（竣工备案、消防备案）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sgxkz_dict": sgxkz_dict,
            "xfba_responsible_person": todo_handler,
            "jgba_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 竣工备案
class CompletionRecord(AjaxTodoBase):
    def __init__(self):
        super(CompletionRecord, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jgba_dict = process_data.get("jgba_dict")
        upload_file_json = {"竣工备案报告": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "completion_filing_report_file" in jgba_dict:
            upload_file_field = upload_file_field_map["completion_filing_report_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                jgba_dict["completion_filing_report_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                jgba_dict["completion_filing_report_file"], process_user
            )
        jgba_dict.setdefault("upload_file_json", upload_file_json)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "jgba_dict": jgba_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 消防备案
class FireProtectionRecord(AjaxTodoBase):
    def __init__(self):
        super(FireProtectionRecord, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xfba_dict = process_data.get("xfba_dict")
        upload_file_json = {"消防备案报告": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("项目报建")
        )
        if "fire_protection_filing_report_file" in xfba_dict:
            upload_file_field = upload_file_field_map[
                "fire_protection_filing_report_file"
            ]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                xfba_dict["fire_protection_filing_report_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                xfba_dict["fire_protection_filing_report_file"], process_user
            )
        xfba_dict.setdefault("upload_file_json", upload_file_json)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "xfba_dict": xfba_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 消防工程第三方检测报告
class FireEngineeringThirdPartyInspectionReport(AjaxTodoBase):
    def __init__(self):
        super(FireEngineeringThirdPartyInspectionReport, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fire_engineering_inspection_report = process_data.get(
            "fire_engineering_inspection_report"
        )
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
            fire_engineering_inspection_report, "消防工程第三方检测报告"
        )
        if not file_exit_flag:
            return file_exit_msg
        fire_engineering_inspection_report_file_json = TemplateAction.get_file_json(
            fire_engineering_inspection_report, process_user
        )
        # 获取待办处理人（主要是下一个节点:  竣工备案、消防备案）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "fire_engineering_inspection_report": fire_engineering_inspection_report,
            "fire_engineering_inspection_report_file_json": fire_engineering_inspection_report_file_json,
            "xfba_responsible_person": todo_handler,
            "jgba_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointProjecEstablishment(AjaxTodoBase):
    """
    合建项目报建-项目立项，设计合同签订，图纸送审
    """
    def __init__(self):
        super(JointProjecEstablishment, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xmlx_dict = process_data.get("xmlx_dict")
        for current_content in xmlx_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "xmlx_dict": xmlx_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

class JointSupervisionContractAwarding(AjaxTodoBase):
    """
    合建项目报建-监理合同签订等
    """
    def __init__(self):
        super(JointSupervisionContractAwarding, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jlfb_dict = process_data.get("jlfb_dict")
        for current_content in jlfb_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人（施工许可证）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "jlfb_dict": jlfb_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

class JointConstructionPermitIssuance(AjaxTodoBase):
    """
    合建项目报建-施工许可证下发
    """
    def __init__(self):
        super(JointConstructionPermitIssuance, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sgxkz_dict = process_data.get("sgxkz_dict")
        for current_content in sgxkz_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sgxkz_dict": sgxkz_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

class JointCompletionRecord(AjaxTodoBase):
    """
    合建项目报建-竣工备案
    """
    def __init__(self):
        super(JointCompletionRecord, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jgba_dict = process_data.get("jgba_dict")
        for current_content in jgba_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
            if current_content.get("work_content") == "竣工备案":
                complete_time = current_content.get("actual_finish_time")
                project_name = self.ctx.variables.get("project_name")
                db = mysql.new_mysql_instance("tbconstruct")
                sql = (f"SELECT campus,project,dcopsTicketId FROM risk_early_warning_data "
                       f"WHERE project_name = '{project_name}'")
                query_data = db.get_row(sql)
                if query_data:
                    campus = query_data.get("campus")
                    ticket_id = query_data.get("dcopsTicketId")
                    project = query_data.get("project")
                    response_data = gnetops.request(
                        action="Project",
                        method="UpdateSummaryData",
                        ext_data={
                            "campus": campus,
                            "project_name": project,
                            "ticket_id": ticket_id,
                            "update_filed": "completion_government",
                            "update_value": complete_time
                        },
                        scheme="ifob-infrastructure",
                    )
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "jgba_dict": jgba_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

class JointFireProtectionRecord(AjaxTodoBase):
    """
    合建项目报建-消防备案
    """
    def __init__(self):
        super(JointFireProtectionRecord, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xfba_dict = process_data.get("xfba_dict")
        for current_content in xfba_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
            if current_content.get("work_content") == "消防备案":
                complete_time = current_content.get("actual_finish_time")
                project_name = self.ctx.variables.get("project_name")
                db = mysql.new_mysql_instance("tbconstruct")
                sql = (f"SELECT campus,project,dcopsTicketId FROM risk_early_warning_data "
                       f"WHERE project_name = '{project_name}'")
                query_data = db.get_row(sql)
                if query_data:
                    campus = query_data.get("campus")
                    ticket_id = query_data.get("dcopsTicketId")
                    project = query_data.get("project")
                    response_data = gnetops.request(
                        action="Project",
                        method="UpdateSummaryData",
                        ext_data={
                            "campus": campus,
                            "project_name": project,
                            "ticket_id": ticket_id,
                            "update_filed": "fire_filing_government",
                            "update_value": complete_time
                        },
                        scheme="ifob-infrastructure",
                    )
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "xfba_dict": xfba_dict,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
