#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试campus_resources.py修复效果的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from biz.construction_big_disk.campus_resources import ProjectsUnderConstructionInPark

def test_data_query():
    """测试data_query方法"""
    print("🔍 测试 data_query 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.data_query()
        print(f"✅ data_query 成功，返回 {len(result)} 条记录")
        return True
    except Exception as e:
        print(f"❌ data_query 失败: {e}")
        return False

def test_tabular_data():
    """测试tabular_data方法"""
    print("🔍 测试 tabular_data 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.tabular_data()
        print(f"✅ tabular_data 成功，返回 {len(result)} 条记录")
        return True
    except Exception as e:
        print(f"❌ tabular_data 失败: {e}")
        return False

def test_supplier_resource_inquiry():
    """测试supplier_resource_inquiry方法"""
    print("🔍 测试 supplier_resource_inquiry 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.supplier_resource_inquiry()
        print(f"✅ supplier_resource_inquiry 成功，返回 {len(result)} 条记录")
        return True
    except Exception as e:
        print(f"❌ supplier_resource_inquiry 失败: {e}")
        return False

def test_supplier_inquiry():
    """测试supplier_inquiry方法"""
    print("🔍 测试 supplier_inquiry 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.supplier_inquiry()
        print(f"✅ supplier_inquiry 成功，返回 {len(result)} 条记录")
        return True
    except Exception as e:
        print(f"❌ supplier_inquiry 失败: {e}")
        return False

def test_dynamic_table_header_generation():
    """测试dynamic_table_header_generation方法"""
    print("🔍 测试 dynamic_table_header_generation 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.dynamic_table_header_generation()
        if result is None:
            print("⚠️ dynamic_table_header_generation 返回 None（可能是数据问题）")
        else:
            print(f"✅ dynamic_table_header_generation 成功，返回 {len(result)} 个表头")
        return True
    except Exception as e:
        print(f"❌ dynamic_table_header_generation 失败: {e}")
        return False

def test_construction_plan():
    """测试construction_plan方法"""
    print("🔍 测试 construction_plan 方法...")
    try:
        instance = ProjectsUnderConstructionInPark()
        result = instance.construction_plan()
        print(f"✅ construction_plan 成功，返回 {len(result)} 条记录")
        return True
    except Exception as e:
        print(f"❌ construction_plan 失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 campus_resources.py 修复效果")
    print("=" * 50)
    
    tests = [
        test_data_query,
        test_tabular_data,
        test_supplier_resource_inquiry,
        test_supplier_inquiry,
        test_dynamic_table_header_generation,
        test_construction_plan
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
        print("-" * 30)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！HTTP 500错误应该已经修复")
    else:
        print("⚠️ 部分测试失败，可能还需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
