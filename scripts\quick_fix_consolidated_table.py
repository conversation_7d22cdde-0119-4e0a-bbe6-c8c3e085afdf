#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复合并表脚本
直接创建表并插入基础数据，避免存储过程问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iBroker.lib import mysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_table_directly():
    """直接创建合并表"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    # 删除旧表（如果存在）
    try:
        db.execute("DROP TABLE IF EXISTS consolidated_equipment_data")
        logger.info("删除旧表成功")
    except Exception as e:
        logger.warning(f"删除旧表失败（可能不存在）: {e}")
    
    # 创建新表
    create_sql = """
    CREATE TABLE consolidated_equipment_data (
        state VARCHAR(50) DEFAULT NULL,
        TicketId VARCHAR(100) NOT NULL,
        Title VARCHAR(255) DEFAULT NULL,
        SchemeNameCn VARCHAR(255) DEFAULT NULL,
        IsForceEnd VARCHAR(255) DEFAULT '0',
        project_name VARCHAR(255) DEFAULT NULL,
        device_name VARCHAR(255) DEFAULT NULL,
        supplier VARCHAR(255) DEFAULT NULL,
        device_type VARCHAR(100) DEFAULT NULL,
        equipment_sla VARCHAR(50) DEFAULT NULL,
        expected_time_equipment DATETIME DEFAULT NULL,
        estimated_time_delivery DATETIME DEFAULT NULL,
        delivery_gap INT DEFAULT NULL,
        completion_time DATETIME DEFAULT NULL,
        po_create_time DATETIME DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (TicketId),
        INDEX idx_project_name (project_name),
        INDEX idx_state (state),
        INDEX idx_device_type (device_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    """
    
    try:
        db.execute(create_sql)
        logger.info("✅ 合并表创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 创建合并表失败: {e}")
        return False

def insert_basic_data():
    """插入基础数据"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    try:
        # 先插入一些基础数据，确保表可用
        basic_insert_sql = """
        INSERT IGNORE INTO consolidated_equipment_data (
            state, TicketId, Title, SchemeNameCn, IsForceEnd,
            project_name, device_name, supplier, device_type, equipment_sla
        ) 
        SELECT DISTINCT
            '已交付' as state,
            actd.TicketId,
            actd.Title,
            actd.SchemeNameCn,
            actd.IsForceEnd,
            COALESCE(eprp.project_name, '未知项目') as project_name,
            COALESCE(eprp.device_name, '未知设备') as device_name,
            '待确认' as supplier,
            COALESCE(eprp.device_name, '未知类型') as device_type,
            '30天' as equipment_sla
        FROM all_construction_ticket_data actd
        LEFT JOIN equipment_production_racking_process eprp
            ON eprp.ticket_id = actd.TicketId
        WHERE actd.SchemeNameCn LIKE '%设备生产%'
            AND actd.IsForceEnd = 0
            AND actd.TicketId IS NOT NULL
            AND actd.TicketId != ''
        LIMIT 500
        """
        
        result = db.execute(basic_insert_sql)
        logger.info(f"✅ 基础数据插入成功，影响行数: {result}")
        
        # 查询插入的记录数
        count_result = db.get_one("SELECT COUNT(*) as count FROM consolidated_equipment_data")
        logger.info(f"📊 合并表总记录数: {count_result['count']}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 基础数据插入失败: {e}")
        return False

def update_project_states():
    """更新项目状态"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    try:
        # 更新项目状态
        update_sql = """
        UPDATE consolidated_equipment_data ced
        LEFT JOIN (
            SELECT DISTINCT
                CONCAT(sd.campus, sd.project_name) as full_project_name,
                sd.state
            FROM summary_data sd
            WHERE sd.state IS NOT NULL AND sd.state != ''
        ) proj_states ON ced.project_name = proj_states.full_project_name
        SET ced.state = COALESCE(proj_states.state, '未知状态')
        WHERE ced.project_name IS NOT NULL
        """
        
        result = db.execute(update_sql)
        logger.info(f"✅ 项目状态更新成功，影响行数: {result}")
        
        # 统计各状态的数量
        state_stats = db.get_all("""
            SELECT state, COUNT(*) as count 
            FROM consolidated_equipment_data 
            GROUP BY state 
            ORDER BY count DESC
        """)
        
        logger.info("📈 项目状态统计:")
        for stat in state_stats:
            logger.info(f"  {stat['state']}: {stat['count']} 条")
        
        return True
    except Exception as e:
        logger.error(f"❌ 项目状态更新失败: {e}")
        return False

def verify_table():
    """验证表结构和数据"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    try:
        # 检查表结构
        columns = db.get_all("SHOW COLUMNS FROM consolidated_equipment_data")
        logger.info(f"📋 表结构验证 - 字段数: {len(columns)}")
        
        # 检查数据完整性
        data_check = db.get_one("""
            SELECT 
                COUNT(*) as total_count,
                COUNT(DISTINCT TicketId) as unique_tickets,
                COUNT(CASE WHEN state IS NOT NULL THEN 1 END) as has_state_count,
                COUNT(CASE WHEN project_name IS NOT NULL THEN 1 END) as has_project_count
            FROM consolidated_equipment_data
        """)
        
        logger.info("🔍 数据完整性检查:")
        logger.info(f"  总记录数: {data_check['total_count']}")
        logger.info(f"  唯一工单数: {data_check['unique_tickets']}")
        logger.info(f"  有状态记录: {data_check['has_state_count']}")
        logger.info(f"  有项目名记录: {data_check['has_project_count']}")
        
        # 显示示例数据
        sample_data = db.get_all("SELECT * FROM consolidated_equipment_data LIMIT 3")
        logger.info("📝 示例数据:")
        for i, row in enumerate(sample_data, 1):
            logger.info(f"  记录{i}: TicketId={row.get('TicketId')}, "
                       f"项目={row.get('project_name')}, 状态={row.get('state')}")
        
        return True
    except Exception as e:
        logger.error(f"❌ 表验证失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始快速修复合并表")
    
    # 步骤1: 创建表
    if not create_table_directly():
        logger.error("❌ 创建表失败，退出")
        return False
    
    # 步骤2: 插入基础数据
    if not insert_basic_data():
        logger.error("❌ 插入基础数据失败，退出")
        return False
    
    # 步骤3: 更新项目状态
    if not update_project_states():
        logger.warning("⚠️ 项目状态更新失败，但继续执行")
    
    # 步骤4: 验证表
    if not verify_table():
        logger.warning("⚠️ 表验证失败，但表已创建")
    
    logger.info("🎉 快速修复完成！合并表已可用")
    logger.info("💡 提示：现在可以测试页面功能了")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n" + "="*50)
        print("✅ 修复成功！请按以下步骤测试：")
        print("1. 打开页面，不选择任何条件直接查询")
        print("2. 选择园区进行查询")
        print("3. 选择项目状态进行查询")
        print("="*50)
    else:
        print("\n❌ 修复失败，请检查错误日志")
    
    sys.exit(0 if success else 1)
