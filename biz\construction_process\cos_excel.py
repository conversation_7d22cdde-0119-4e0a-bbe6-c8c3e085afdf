from openpyxl import load_workbook
from cos_lib import COSLib


def upload_url_to_cos(url, cos_path):
    COSLib.uploadFile(url, cos_path)


# 下载 Excel 文件
def download_excel_file(cos_path, save_to_file):
    COSLib.download(file_name=cos_path, save_to_file=save_to_file)


# 处理 Excel 文件
def process_excel_file(file_path):
    wb = load_workbook(filename=file_path)
    # 在这里可以执行其他操作，如读取、修改或提取数据
    # 例如，获取工作表对象
    sheet = wb.active
    # 读取单元格数据
    cell_value = sheet['A1'].value
    print(f"单元格 A1 的值为: {cell_value}")
    # 修改单元格数据
    sheet['A1'] = "New Value"
    # 保存修改后的文件
    modified_file_path = f"modified_{file_path}"
    wb.save(modified_file_path)
    print(f"文件处理完成，保存为：{modified_file_path}")


# 测试代码
def main():
    # 假设已上传的 Excel 文件在 COS 中的 URL
    excel_url = "https://test.otob.dcops.qq.com/relay/cosfile/relay/20240201/" \
                "新建 Microsoft Excel 工作表_7e04da009966bbc81fc390f8a3a8d535.xlsx"
    # 上传URL到COS
    upload_url_to_cos(excel_url, cos_path='wmytest.xlsx')

    # 下载 Excel 文件到本地
    save_to_file = "downloaded_excel.xlsx"
    download_excel_file(excel_url, save_to_file)

    # 处理已下载的 Excel 文件
    process_excel_file(save_to_file)


# 执行测试代码
if __name__ == "__main__":
    main()
