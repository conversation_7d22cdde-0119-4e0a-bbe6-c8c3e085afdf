# Docker本地操作指南 - 中文版

## 🎯 Docker已启动，接下来怎么操作？

### 1. 📊 检查Docker容器状态

```bash
# 查看正在运行的容器
docker ps

# 应该看到类似输出：
# CONTAINER ID   IMAGE               COMMAND                  CREATED         STATUS         PORTS                                   NAMES
# abc123def456   ibroker-construct   "supervisord -c /etc…"   2 minutes ago   Up 2 minutes   0.0.0.0:80->80/tcp, 0.0.0.0:8000->8000/tcp   ibroker-construct
```

### 2. 🔍 查看服务日志

```bash
# 查看容器启动日志
docker logs ibroker-construct

# 实时查看日志（按Ctrl+C退出）
docker logs -f ibroker-construct
```

### 3. 🚪 进入Docker容器内部

```bash
# 进入容器的命令行界面
docker exec -it ibroker-construct bash

# 进入后你会看到类似：
# root@abc123def456:/data/nbroker#
```

### 4. 📁 在容器内部查看项目文件

```bash
# 进入容器后执行：
ls -la                          # 查看当前目录文件
ls -la biz/construction_big_disk/   # 查看我们修改的文件目录
cat trpc_python.yaml           # 查看配置文件
```

### 5. 🔧 检查服务运行状态

```bash
# 在容器内检查进程状态
supervisorctl status

# 应该看到：
# python-trpc-server               RUNNING   pid 123, uptime 0:05:30
# zylog                           RUNNING   pid 124, uptime 0:05:30
```

### 6. 🌐 测试服务是否可访问

**在Windows命令行或浏览器中测试：**

```bash
# 测试HTTP服务（端口80）
curl http://localhost:80

# 测试tRPC服务（端口8000）
curl http://localhost:8000

# 或者直接在浏览器打开：
# http://localhost:80
# http://localhost:8000
```

### 7. 🐛 运行我们修改的代码

**进入容器后，测试我们的调试代码：**

```bash
# 进入容器
docker exec -it ibroker-construct bash

# 进入Python环境
python3

# 在Python中测试我们的代码
>>> import sys
>>> sys.path.append('/data/nbroker')
>>> from biz.construction_big_disk.campus_resources import DeliveryScheduleManagement
>>> 
>>> # 创建实例
>>> dsm = DeliveryScheduleManagement()
>>> 
>>> # 测试动态表头功能（会显示我们添加的调试信息）
>>> result = dsm.dynamic_header()
>>> print(result)
```

### 8. 📋 查看详细的应用日志

```bash
# 在容器内查看应用日志
tail -f /data/wwwlogs/message.log      # 主服务日志
tail -f /data/wwwlogs/supervisord.log  # 进程管理日志
tail -f /data/wwwlogs/stdout.log       # 标准输出日志
tail -f /data/wwwlogs/stderr.log       # 错误日志
```

### 9. 🔄 重启服务（如果需要）

```bash
# 在容器内重启服务
supervisorctl restart python-trpc-server

# 或者重启整个容器
docker restart ibroker-construct
```

### 10. 📝 修改代码后重新加载

**如果您修改了代码，需要重新构建：**

```bash
# 停止当前容器
docker stop ibroker-construct
docker rm ibroker-construct

# 重新构建并启动
docker build -t ibroker-construct .
docker run -d --name ibroker-construct -p 8000:8000 -p 80:80 ibroker-construct
```

## 🎯 测试我们修复的功能

### 方法1: 在容器内直接测试

```bash
# 进入容器
docker exec -it ibroker-construct bash

# 运行Python测试
cd /data/nbroker
python3 -c "
from biz.construction_big_disk.campus_resources import DeliveryScheduleManagement
dsm = DeliveryScheduleManagement()
print('=== 测试动态表头功能 ===')
result = dsm.dynamic_header()
print('结果:', result)
"
```

### 方法2: 通过HTTP接口测试

```bash
# 如果项目有HTTP接口，可以这样测试：
curl -X POST http://localhost:80/api/delivery/dynamic_header \
  -H "Content-Type: application/json" \
  -d '{"project_name": "", "state": "", "PM": "", "demand_delivery": []}'
```

## 🚨 常见问题解决

### 问题1: 容器启动失败
```bash
# 查看错误信息
docker logs ibroker-construct

# 检查端口是否被占用
netstat -ano | findstr :80
netstat -ano | findstr :8000
```

### 问题2: 无法访问服务
```bash
# 检查容器是否在运行
docker ps

# 检查端口映射
docker port ibroker-construct
```

### 问题3: 数据库连接失败
```bash
# 在容器内测试数据库连接
docker exec -it ibroker-construct bash
python3 -c "
from iBroker.lib import mysql
try:
    db = mysql.new_mysql_instance('tbconstruct')
    print('✅ 数据库连接成功')
except Exception as e:
    print('❌ 数据库连接失败:', e)
"
```

## 📞 快速命令参考

```bash
# 查看容器状态
docker ps

# 查看日志
docker logs -f ibroker-construct

# 进入容器
docker exec -it ibroker-construct bash

# 重启容器
docker restart ibroker-construct

# 停止容器
docker stop ibroker-construct

# 删除容器
docker rm ibroker-construct

# 重新构建
docker build -t ibroker-construct .
```

## 🎉 成功标志

如果看到以下内容，说明服务启动成功：

1. ✅ `docker ps` 显示容器状态为 "Up"
2. ✅ `supervisorctl status` 显示服务为 "RUNNING"
3. ✅ 浏览器能访问 http://localhost:80
4. ✅ 我们的调试信息正常输出

**现在可以开始测试我们修复的代码了！** 🚀
