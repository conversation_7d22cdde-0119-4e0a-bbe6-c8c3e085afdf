from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase


class Approve(AjaxTodoBase):
    """
    审批节点
    """

    PROTOCOL_KEY = "ibroker.protocol.approve"

    def __init__(self):
        super(Approve, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        next_op = process_data.get("next_op", None)

        self.workflow_detail.add_kv_table(name="用户选择内容",
                                          kv_table={"选择": next_op})
        # 结束待办，如果业务逻辑需要不结束待办，则不添加该语句
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables={"next_op": next_op})
