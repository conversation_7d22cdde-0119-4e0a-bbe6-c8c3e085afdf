# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: after_audit.py
# @time: 2023/3/8 14:31
# @描述: 【请添加描述】
import time

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.logone import logger
from iBroker.lib import mysql, config

# 形成审批结论
from biz.tbbg.instruct_export import InstructDataExport


class OutpufAuditResult:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("OutpufAuditResult-ServiceMethod-" + str(time.time()))

        # 审批信息入库
        audit_list = self.ctx.variables.get("audit_list")
        idc_audit_list = self.ctx.variables.get("idc_audit_list")
        requirement_id = self.ctx.variables.get("requirement_id")

        batch_insert_list = []
        for audit in audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": "RequirementAudit",
                "AuditUser": audit.get("audit_user"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_reason")
            })

        for audit in idc_audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": audit.get("todo_key"),
                "AuditUser": audit.get("audit_user"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_opinion")
            })
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert_batch("tb_change_audit_history", batch_insert_list)
        # 同步审批信息
        result = gnetops.request(action="TbChangeJoint", method="SendAuditResultToStars", data={
            "RequirementId": self.ctx.variables.get("requirement_id"),
            "AuditResult": 1
        })

        self.workflow_detail.add_kv_table("同步审批信息至星辰", {
            "TbComplete": result
        })

        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 发变更需求
class SendChangeRequire:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("SendChangePlan-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 变更指令（DCOPS向合作方发送）
class SendChangeOrder(AjaxTodoBase):
    def __init__(self):
        super(SendChangeOrder, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取inside_list和outside_list
        inside_list_str = process_data.get("inside_list")
        outside_list_str = process_data.get("outside_list") if process_data.get("outside_list") else ""
        hand_time = process_data.get("hand_time")
        target_time = process_data.get("target_time")
        instruct_change_content = process_data.get("instruct_change_content")
        other_content = process_data.get("other_content")

        # 将inside_list_str根据;分割成一个list
        inside_list = []
        if inside_list_str:
            inside_list = inside_list_str.split(";")

        # 遍历inside_list，给每个元素拼接上@tencent.com
        for i in range(len(inside_list)):
            inside_list[i] = inside_list[i] + "@tencent.com"

        # 将outside_list_str根据;分割成一个list
        outside_list = []
        if outside_list_str:
            outside_list = outside_list_str.split(";")
        # 将inside_list和outside_list拼接成一个list
        to_user = inside_list + outside_list

        tb_db = mysql.new_mysql_instance("tb_change")
        requirement_id = self.ctx.variables.get("requirement_id")

        ret_list = InstructDataExport().export_and_upload(int(requirement_id), to_user, hand_time, target_time,
                                                          instruct_change_content,
                                                          other_content)

        tb_db.update_by_id("requirement", {"InstructSendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())},
                           requirement_id)

        # 遍历ret_list
        file_batch_insert_list = []
        for ret in ret_list:
            insert_obj = {
                "RequirementId": requirement_id,
                "CosPath": ret.get("cosurl"),
                "FileName": ret.get("filename"),
                "Creator": process_user,
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "Type": "instruct"
            }
            file_batch_insert_list.append(insert_obj)

        if len(file_batch_insert_list) > 0:
            tb_db.insert_batch("requirement_file", file_batch_insert_list)

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 接收po单发送信息
class ReceivePoInfoAndSendMsg:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("ReceivePoInfoAndSendMsg-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 变更完成确认
class ChangeCompleteAffirm(AjaxTodoBase):
    def __init__(self):
        super(ChangeCompleteAffirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):

        # 审批信息入库
        audit_list = self.ctx.variables.get("audit_list")
        idc_audit_list = self.ctx.variables.get("idc_audit_list")
        requirement_id = self.ctx.variables.get("requirement_id")

        batch_insert_list = []
        for audit in audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": "RequirementAudit",
                "AuditUser": audit.get("audit_people"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_reason")
            })

        for audit in idc_audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": audit.get("todo_key"),
                "AuditUser": audit.get("audit_user"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_opinion")
            })
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert_batch("tb_change_audit_history", batch_insert_list)

        # get cos_url from rainbow_config
        cos_url = config.get_config_string("cos.url")

        # get data named pm_file_list from process_data
        complete_file_list = process_data["complete_file_list"]
        # create a array named insert_data_list
        insert_data_list = []
        for pm_file in complete_file_list:
            insert_obj = {
                "RequirementId": requirement_id,
                "CosPath": pm_file["response"]["FileList"][0]["url"].split(cos_url)[1],
                "FileName": pm_file["name"],
                "Creator": process_user,
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "Type": "complete"
            }
            insert_data_list.append(insert_obj)

        # if insert_data_list is not empty, batch insert into requirement_file table
        if len(insert_data_list) > 0:
            tb_db.insert_batch("requirement_file", insert_data_list)

        result = gnetops.request(action="TbChangeJoint", method="TbComplete", data={
            "RequirementId": self.ctx.variables.get("requirement_id"),
        })

        self.workflow_detail.add_kv_table("TB变更完成，同步星辰", {
            "TbComplete": result
        })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 接收结算完成
class ReceiveSettleComplete:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("ReceiveSettleComplete-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)


if __name__ == '__main__':
    # excel_file = pathconst.TMEP_DIR + "/" + "mail_title" + ".xlsx"
    # file_name = "mail_title" + ".xlsx"
    # wb = Workbook()
    # ws = wb.create_sheet("变更指令", 0)
    #
    # ws.merge_cells('B1:G2')
    # ws["B1:G2"]= "变更指令"
    # ws.save(excel_file)
    pass
