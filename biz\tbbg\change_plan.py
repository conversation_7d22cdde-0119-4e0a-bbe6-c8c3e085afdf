# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: change_plan.py
# @time: 2023/3/6 14:24
# @描述: 【请添加描述】
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.lib import mysql


# 传递变更方案
class SendChangePlan(AjaxTodoBase):
    def __init__(self):
        super(SendChangePlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):

        # 从process_data中获取file_data的outsideList数据
        chooseList = process_data["file_data"]["outsideList"]

        # 遍历chooseList，将push_able为True的数据记录到数组中，push_able有可能不存在，所以需要判断
        chooseFileIds = []
        for choose in chooseList:
            if choose.get("push_able"):
                chooseFileIds.append(choose.get("Id"))

        # 请求gnetops接口，传递变更方案，处理返回结果
        result = gnetops.request(action="TbChangeJoint", method="SendRequirToStars",
                                 data={"RequirementId": self.ctx.variables.get("requirement_id"),
                                       "FileIds": chooseFileIds})

        # if not result.get("Data").get("statusCode") or result.get("Data").get("statusCode") != 1:
        #     raise exception.LogicException("传递变更方案失败")

        self.workflow_detail.add_kv_table("传递变更方案", {
            "SendRequirToStars": result
        })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 确认接收变更方案
class AffirmReceiveChangePlan:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def WaitMethod(self):
        # 记录相关数据到数据库中
        ticket_id = self.ctx.variables.get("TicketId")
        task_id = self.ctx.task_id
        requirement_id = self.ctx.variables.get("requirement_id")
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert("joint_temp", {
            "TicketId": ticket_id,
            "TaskId": task_id,
            "RequirementId": requirement_id,
            "JoinKey": "BudgetAbout"
        })
        pass

    def ServiceMethod(self, task_id: str, end_able: bool):
        variables = {
            "stars_reject": 1 if end_able else 0
        }
        # 流程流转
        flow.complete_task(task_id, variables=variables)


# 接收商务Gm审批信息
class ReceiveGmInfo:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def WaitMethod(self):
        # 记录相关数据到数据库中
        ticket_id = self.ctx.variables.get("TicketId")
        task_id = self.ctx.task_id
        requirement_id = self.ctx.variables.get("requirement_id")
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert("joint_temp", {
            "TicketId": ticket_id,
            "TaskId": task_id,
            "RequirementId": requirement_id,
            "JoinKey": "GmAudit"
        })
        pass

    def ServiceMethod(self, task_id: str, end_able: bool):
        variables = {
            "stars_gm_audit": 1 if end_able else 0
        }
        # 流程流转
        flow.complete_task(task_id, variables=variables)


# 接收变更估算以及供应商
class ReceiveDetails(AjaxTodoBase):
    def __init__(self):
        super(ReceiveDetails, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id)
