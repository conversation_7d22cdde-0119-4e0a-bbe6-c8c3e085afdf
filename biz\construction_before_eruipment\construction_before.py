import json
import uuid
import numpy as np
import pandas as pd

from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib.sdk import flow, gnetops
from iBroker.lib import mysql, curl

from biz.construction_process.cos_lib import COSLib
from biz.construction_before_eruipment.tools import Tools


class GetProjectData(object):
    """
        获取风险预警所需数据
    """

    def __init__(self):
        super(GetProjectData, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_risk_need(self, project_name):
        ticket_id = self.ctx.variables.get("ticket_id")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT progress FROM file_storage_data WHERE project_name = '{project_name}' "
        sql1 = "SELECT demand_distribution,demand_delivery,item_number FROM risk_early_warning_data " \
               f"WHERE project_name = '{project_name}' "
        sql2 = "SELECT integrator,item_tube,supervision_and_management,PM FROM project_team_information_data " \
               f"WHERE project_name = '{project_name}'"
        overall_control_list = db.get_all(sql)
        risk_need_list = db.get_all(sql1)
        personnel_list = db.get_all(sql2)
        overall_control_plan = ''
        demand_distribution = ''
        demand_delivery = ''
        # pm
        PM = ''
        # 集成商
        integrator = ''
        # 项管
        item_tube = ''
        # 监理
        supervision_and_management = ''
        if overall_control_list:
            overall_control_plan = overall_control_list[0].get('progress')
        if risk_need_list:
            demand_distribution_str = str(risk_need_list[0].get('demand_distribution'))
            demand_delivery_str = str(risk_need_list[0].get('demand_delivery'))

            if '00:00:00' in demand_distribution_str:
                demand_distribution = demand_distribution_str.split()[0]
            else:
                demand_distribution = demand_distribution_str

            if '00:00:00' in demand_delivery_str:
                demand_delivery = demand_delivery_str.split()[0]
            else:
                demand_delivery = demand_delivery_str

        if personnel_list:
            PM = personnel_list[0].get('PM')
            integrator = personnel_list[0].get('integrator')
            item_tube = personnel_list[0].get('item_tube')
            supervision_and_management = personnel_list[0].get('supervision_and_management')
        insert_data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_before_process", insert_data)
        tb_db.commit()
        variables = {
            "demand_distribution": demand_distribution,
            "demand_delivery": demand_delivery,
            'PM': PM,
            'stage': '项目施工准备阶段',
            'risk_need_list': risk_need_list,
            # 集成商
            'integrator': integrator,
            # 项管
            'project_manager': item_tube,
            'deal_with_human': item_tube,
            # 监理
            'supervision': supervision_and_management,
            "overall_control_url": overall_control_plan
        }
        if demand_distribution is None or demand_delivery is None:
            self.workflow_detail.add_kv_table('风险预警数据', {"success": False, "data": "下发或交付时间为空"})
        else:
            flow.complete_task(self.ctx.task_id, variables=variables)


class UploadWeeklyReportInformation(AjaxTodoBase):
    """

    """

    def __init__(self):
        super(UploadWeeklyReportInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        weekly_conditions = {}
        weekly_update_data = {}
        weekly_insert_data = {}
        daily_insert_data = {}
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        PM = self.ctx.variables.get("PM")

        # 指定处理人和周期
        project_phase = process_data.get('project_phase', None)
        week_date = process_data.get('week_date', None)
        deal_with_human = process_data.get('deal_with_human', None)
        daily_processor = process_data.get('daily_processor', None)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        # 周报 project_name
        weekly_project_name_sql = """
            SELECT project_name FROM week_create_info
        """
        # 日报 project_name
        daily_project_name_sql = """
            SELECT project_name FROM daily_create_info
        """
        weekly_project_name_list = tb_db.get_all(weekly_project_name_sql)
        daily_project_name_list = tb_db.get_all(daily_project_name_sql)
        # 判断周报信息表中如果有 此项目，那么就修改，如果没有此项目，那么就插入
        if any(item.get('project_name') == project_name for item in weekly_project_name_list):
            weekly_conditions = {
                'project_name': project_name
            }

            weekly_update_data = {
                'project_phase': project_phase,
                'week_date': week_date
            }
        else:
            weekly_insert_data = {
                "ticketid": ticket_id,
                "week_date": week_date,
                "deal_with_human": deal_with_human,
                "project_name": project_name,
                "project_phase": project_phase,
                'handlers': PM
            }
        # 判断日报信息表中如果有 此项目，那么就修改，如果没有此项目，那么就插入
        if any(item.get('project_name') == project_name for item in daily_project_name_list):
            pass
        else:
            daily_insert_data = {
                'ticketid': ticket_id,
                'daily_processor': daily_processor,
                'project_name': project_name,
                'handlers': PM
            }

        tb_db.begin()
        if weekly_update_data:
            tb_db.update("week_create_info", weekly_update_data, weekly_conditions)
        else:
            tb_db.insert("week_create_info", weekly_insert_data)
        if daily_insert_data:
            tb_db.insert("daily_create_info", daily_insert_data)
        tb_db.commit()
        variables = {
            'week_date': week_date,
            'deal_with_human': deal_with_human,
            'daily_processor': daily_processor
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionPlan(AjaxTodoBase):
    """
        上传施工计划
    """

    def __init__(self):
        super(ConstructionPlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def update(self, process_data, process_user):
        pass

    # 检查计划是否上传成功

    def ckeck_upload(self, construction_plan):
        # 捕获异常
        try:
            url, result = construction_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            url, result = {"code": -1, "msg": "未上传开办及设备进场前施工计划"}, True
        except IndexError:
            url, result = {"code": -1, "msg": "未上传开办及设备进场前施工计划"}, True
        return url, result

    def ckeck_is_correct(self, construction_plan):
        url = construction_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        for _, row in df.iterrows():
            serial_number = str(row['序号'])
            task_name = row['工作内容']
            if serial_number == '2.7' and task_name == '项目开办':
                return {"code": 0, "msg": "计划上传正确"}, False
        return {"code": -1, "msg": "计划上传错误，请重新上传"}, True

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        handler = self.ctx.variables.get("PM")
        # 获取上传的计划信息
        construction_plan = process_data.get("construction_plan")
        # 检查是否上传文件
        url, result = self.ckeck_upload(construction_plan)
        if result:
            return url
        upload_plan, plan_result = self.ckeck_is_correct(construction_plan)
        if plan_result:
            return upload_plan
        if construction_plan and 'response' in construction_plan[0]:
            insert_data = {
                "handler": handler,
                "url": url
            }
            con = {"ticket_id": ticket_id,
                   "project_name": project_name, }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_before_process", insert_data, con)
            tb_db.commit()

        variables = {
            "construction_plan": construction_plan,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class FileParsing(object):
    """
        解析施工计划
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_data(self, row):

        data = {}
        number = str(row['序号'])
        if '2.7' <= number <= '2.9':
            work_content = row['工作内容']
            plan_construction_period = row['工期（日历日）']
            plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['开始时间（年/月/日）']) else None
            plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['完成时间（年/月/日）']) else None
            responsible_person = row['责任人（开通账号人员）']
            output = row['输出物']
            input = row['输入物']
            construction_properties = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
            data = {
                'number': number,
                'work_content': work_content,
                'plan_construction_period': plan_construction_period,
                'plan_start_time': plan_start_time,
                'plan_finish_time': plan_finish_time,
                'responsible_person': responsible_person,
                'output': output,
                'input': input,
                'construction_properties': construction_properties
            }
        return data

    def title_data(self, row, list):
        data = self.get_data(row)
        list['time'] = data.get('plan_start_time')
        list['completion_time'] = data.get('plan_finish_time')
        list['responsible_person'] = data.get('responsible_person')
        return list

    def analytical_data(self, construction_plan):
        """
            下载计划，解析数据
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        # 下载计划
        url = construction_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        # 解析数据
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        # 设备进场前
        project_start_list = {'work_list': []}
        ground_list = {'work_list': []}
        AHU_list = {'work_list': []}
        water_treatment_list = {'work_list': []}
        other_list = {'work_list': []}
        # 判断其他方案是否存在
        if '2.8.4' in df['序号'].values:
            next_op = 1
        else:
            next_op = 2
        # 截取设备进场前的计划
        if '2.9' in df['序号'].values:
            filtered_df = df[(df['序号'].apply(str) >= '2.7') & (df['序号'].apply(str) < '2.9')]
        else:
            filtered_df = df[df['序号'].apply(str) >= '2.7']
        # 存数据库的数据
        insert_data = []
        for _, row in filtered_df.iterrows():
            data = self.get_data(row)
            data['ticket_id'] = ticket_id
            insert_data.append(data)
            number = str(row['序号'])
            if number == '2.7':
                self.title_data(row, project_start_list)
            elif '2.7' < number < '2.8':
                data = self.get_data(row)
                project_start_list['work_list'].append(data)
            elif number == '2.8':
                self.title_data(row, ground_list)
                self.title_data(row, AHU_list)
                self.title_data(row, water_treatment_list)
                self.title_data(row, other_list)
            elif '2.8.1' <= number < '2.8.2':
                data = self.get_data(row)
                ground_list['work_list'].append(data)
            elif '2.8.2' <= number < '2.8.3':
                data = self.get_data(row)
                AHU_list['work_list'].append(data)
            if next_op == 1:
                if '2.8.3' <= number < '2.8.4':
                    data = self.get_data(row)
                    water_treatment_list['work_list'].append(data)
                elif '2.8.4' <= number < '2.9':
                    data = self.get_data(row)
                    other_list['work_list'].append(data)
            else:
                if '2.8.3' <= number < '2.9':
                    data = self.get_data(row)
                    water_treatment_list['work_list'].append(data)

        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        # 插入数据
        tb_db.insert_batch("equipment_before_data", insert_data)
        tb_db.commit()
        variables = {
            "next_op": next_op,
            'project_start_list': project_start_list,
            'ground_list': ground_list,
            'AHU_list': AHU_list,
            'water_treatment_list': water_treatment_list,
            'other_list': other_list,
            'insert_data': insert_data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectStartPlan(AjaxTodoBase):
    """
        项目开办方案上传
    """

    def __init__(self):
        super(ProjectStartPlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def ckeck_upload(self, project_start_plan):
        # 捕获异常
        try:
            url, result = project_start_plan["response"]["FileList"][0]["url"], False
        except KeyError:
            url, result = {"code": -1, "msg": "未开办方案"}, True
        except IndexError:
            url, result = {"code": -1, "msg": "未开办方案"}, True
        return url, result

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_start_list = self.ctx.variables.get("project_start_list")
        project_start_plan = process_data.get("project_start_plan")
        plan_remark = project_start_plan.get("remark")
        complete_report_review_report = project_start_plan.get("complete_report_review_report")
        url_list = []
        for i in complete_report_review_report:
            url, result = self.ckeck_upload(i)
            if result:
                return url
            if len(i) > 0 and 'response' in i:
                url_list.append(url)
        # 存数据
        url_str = ";".join(url_list)
        insert_data = {
            "ticket_id": ticket_id,
            'name': 'start',
            "plan_url": url_str,
            "plan_remark": plan_remark
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_before_url", insert_data)
        tb_db.commit()
        project_start_list["plan_remark"]: plan_remark
        variables = {
            "project_start_plan": project_start_plan,
            "complete_report_review_report": complete_report_review_report,
            "project_start_list": project_start_list
        }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class AcquisitionOfJointConstructionProjectPlan(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_data(self, project_name):
        sql = ("SELECT serial_number,work_content,start_time,completion_time,project_progress FROM construct_plan_data "
               f"WHERE project_name='{project_name}'")
        db = mysql.new_mysql_instance("tbconstruct")
        query_data = db.get_all(sql)
        project_start_list = []
        equipment_construction_list = []

        current_section = ""
        current_work = ""
        if query_data:
            for row in query_data:
                serial_number = row.get("serial_number", "")
                work_content = row.get('work_content', "")
                start_time = row.get('start_time', "")
                completion_time = row.get('completion_time', "")
                project_progress = row.get('project_progress', "")
                # 检查是否是2级标题
                if '.' not in serial_number or serial_number.count('.') == 1:
                    if work_content == "项目开办" or work_content == "设备进场前施工":
                        current_work = work_content
                        current_section = serial_number
                    continue
                # 获取三级标题内容
                if current_work == "项目开办":
                    if serial_number.startswith(current_section) and serial_number.count('.') == 2:
                        project_start_list.append({
                            "serial_number": serial_number,
                            "work_content": work_content,
                            "plan_start_time": start_time,
                            "plan_finish_time": completion_time,
                            "plan_construction_period": project_progress
                        })
                elif current_work == '设备进场前施工':
                    if serial_number.startswith(current_section) and serial_number.count('.') == 2:
                        equipment_construction_list.append({
                            "serial_number": serial_number,
                            "work_content": work_content,
                            "plan_start_time": start_time,
                            "plan_finish_time": completion_time,
                            "plan_construction_period": project_progress
                        })
        variables = {
            "project_start_list": project_start_list,
            "equipment_construction_list": equipment_construction_list,
            "query_data": query_data,
            "current_section": current_section
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectStart(AjaxTodoBase):
    """
        项目开办完工
    """

    def __init__(self):
        super(ProjectStart, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_start_list = process_data.get("project_start_list")
        complete_report = project_start_list.get('complete_report', None)
        report_url_list = []
        real_time = process_data.get('real_time')
        other_file = process_data.get('other_file', None)
        remark = process_data.get('remark')
        for i in complete_report:
            report_url, report_result = Tools.check_report(i)
            if report_result:
                return report_url
            if len(i) > 0 and 'response' in i:
                report_url_list.append(report_url)
        if other_file:
            for i in other_file:
                report_url, report_result = Tools.check_report(i)
                if report_result:
                    return report_url
                if len(i) > 0 and 'response' in i:
                    report_url_list.append(report_url)
        report_url_str = ";".join(report_url_list)

        complete_photo = project_start_list.get('complete_photo', None)
        photo_url_list = []
        for i in complete_photo:
            photo_url, photo_result = Tools.check_photo(i)
            if photo_result:
                return photo_url
            if len(i) > 0 and 'response' in i:
                photo_url_list.append(photo_url)
        photo_url_str = ";".join(photo_url_list)
        appendix_list = []
        if complete_report:
            try:
                for file in complete_report:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"开工令上传错误:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"开工令上传错误:{e}"}
        # 链接数据库
        # 修改数据/实际开始时间、实际结束时间、实际工期、进度偏离、备注
        tb_db = mysql.new_mysql_instance("tbconstruct")
        # project_start_list是字典
        if project_start_list:
            for i in project_start_list.get('work_list'):
                remark = i.get('remark', None)
                i['report_url_list'] = report_url_list
                i['photo_url_list'] = photo_url_list
                work_content = i.get('work_content', None)
                update_data = {
                    'start_time': i.get('start_time', None),
                    'finish_time': i.get('finish_time', None),
                    "construction_period": i.get('construction_period', None),
                    "progress_deviation": i.get('progress_deviation', None),
                    "remark": remark,
                }
                conditions = {
                    'ticket_id': ticket_id,
                    'work_content': work_content
                }
                tb_db.begin()
                tb_db.update("equipment_before_data", update_data, conditions)
                tb_db.commit()

        # 存数据库
        data = {
            'report_url': report_url_str,
            'photo_url': photo_url_str
        }
        cond = {
            'ticket_id': ticket_id,
            'name': 'start',
        }
        tb_db.begin()
        tb_db.update("equipment_before_url", data, cond)
        tb_db.commit()

        variables = {
            'project_start_list': project_start_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'real_time': real_time,
            'appendix_list': appendix_list,
            'other_file': other_file,
            'remark': remark
        }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointProjectStart(AjaxTodoBase):
    """
        合建项目开办总包信息填写
    """

    def __init__(self):
        super(JointProjectStart, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_start_list = process_data.get("project_start_list")
        complete_report = process_data.get('complete_report', None)
        real_time = process_data.get('real_time')
        remark = process_data.get('remark')
        startup_plan_to_start = process_data.get('startup_plan_to_start', "")
        startup_plan_to_end = process_data.get('startup_plan_to_end', "")
        startup_other_file = process_data.get('startup_other_file', None)
        variables = {
            'project_start_list': project_start_list,
            'complete_report': complete_report,
            'real_time': real_time,
            'remark': remark,
            'startup_plan_to_start': startup_plan_to_start,
            'startup_plan_to_end': startup_plan_to_end,
            'startup_other_file': startup_other_file
        }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class UpdateConstructionProcessStatus(object):
    def __init__(self):
        super(UpdateConstructionProcessStatus, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def change(self):
        ticket_id = self.ctx.variables.get("ticket_id")

        update_data3 = {'process_status': 1}
        con = {'ticket_id': ticket_id}
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("equipment_before_process", update_data3, con)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def data_format(self, data_list):
        for i in data_list:
            photo_url_list = []
            report_url_list = []
            for j in i.get("photo_url_list"):
                photo_url_list.append(
                    {
                        "url": j,
                        "height": "200px",
                        "width": "200px",
                        "marginRight": "5px"
                    }
                )
            i["photo_url_list"] = photo_url_list
            for k in i.get("report_url_list"):
                report_url_list.append(
                    {
                        "url": k
                    }
                )
            i["report_url_list"] = report_url_list

        return data_list

    def start_integration(self):
        project_start_list = self.ctx.variables.get("project_start_list")

        project_start = {
            "task_name": project_start_list.get('task_name'),
            "time": project_start_list.get('time'),
            "completion_time": project_start_list.get('completion_time'),
            "responsible_person": project_start_list.get('responsible_person'),
            "work_list": self.data_format(project_start_list.get("work_list"))
        }
        variables = {
            'project_start': project_start
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def sg_integration(self):
        ground_list = self.ctx.variables.get("ground_list")
        AHU_list = self.ctx.variables.get("AHU_list")
        water_treatment_list = self.ctx.variables.get("water_treatment_list")
        other_list = self.ctx.variables.get("other_list")
        # 构造数据格式
        work_list = ground_list.get('work_list') + other_list.get('work_list') + water_treatment_list.get(
            'work_list') + AHU_list.get('work_list')
        sg = {
            "task_name": "设备进场前施工",
            "time": ground_list.get('time'),
            "completion_time": ground_list.get('completion_time'),
            "responsible_person": ground_list.get('responsible_person'),
            "work_list": work_list
        }
        variables = {
            'sg': sg
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointConstructionBeforeEquipmentArrives(AjaxTodoBase):
    def __init__(self):
        super(JointConstructionBeforeEquipmentArrives, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
        合建项目设备进场前施工总包填写信息
        """
        equipment_construction_list = process_data.get("equipment_construction_list")
        equipment_construction_plan_to_start = process_data.get("equipment_construction_plan_to_start")
        equipment_construction_plan_to_end = process_data.get("equipment_construction_plan_to_end")
        equipment_construction_other_file = process_data.get("equipment_construction_other_file", [])
        variables = {
            'equipment_construction_list': equipment_construction_list,
            'equipment_construction_plan_to_start': equipment_construction_plan_to_start,
            'equipment_construction_plan_to_end': equipment_construction_plan_to_end,
            'equipment_construction_other_file': equipment_construction_other_file
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class StartPMAuditConfirmation(AjaxTodoBase):
    """
        开办流程：pm审核
    """

    def __init__(self):
        super(StartPMAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        start_review = process_data.get("start_review")
        start_rejection = process_data.get("start_rejection")
        if start_review:
            if start_review == '驳回':
                if start_rejection:
                    variables = {
                        'start_review': start_review,
                        'start_rejection': start_rejection,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                con = {
                    'ticket_id': ticket_id
                }
                data = {
                    'start_confirm': start_review
                }
                tb_db.begin()
                tb_db.update("equipment_before_process", data, con)
                tb_db.commit()
                variables = {
                    'start_review': start_review,
                }
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionPMAuditConfirmation(AjaxTodoBase):
    """
        开办流程：pm审核
    """

    def __init__(self):
        super(ConstructionPMAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        sg_review = process_data.get("sg_review")
        sg_rejection = process_data.get("sg_rejection")
        if sg_review:
            if sg_review == '驳回':
                if sg_rejection:
                    variables = {
                        'sg_review': sg_review,
                        'sg_rejection': sg_rejection,
                        'modify': '是'
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                con = {
                    'ticket_id': ticket_id
                }
                data = {
                    'sg_confirm': sg_review
                }
                tb_db.begin()
                tb_db.update("equipment_before_process", data, con)
                tb_db.commit()
                variables = {
                    'sg_review': sg_review,
                }
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionSupervisionAuditConfirmation(AjaxTodoBase):
    """
        开办流程：项管确认
    """

    def __init__(self):
        super(ConstructionSupervisionAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sg = self.ctx.variables.get("sg")
        modify = process_data.get("modify")
        modify_list = process_data.get("modify_list")
        ticket_id = self.ctx.variables.get('ticket_id')
        if modify == '是':
            change_list = []
            for i in modify_list:
                new_name = ''
                change_report_url_list = []
                change_photo_url_list = []
                name = i.get("name")
                if name == 'ground':
                    new_name = '地面处理及放线'
                elif name == 'water_treatment':
                    new_name = '水处理基础施工'
                elif name == 'AHU':
                    new_name = 'AHU基础施工'
                elif name == 'other':
                    new_name = '其他基础施工'
                update_data = {}
                if i.get("report"):
                    report_url_list = []

                    for j in i.get("report"):
                        report_url, report_result = Tools.check_photo(j)
                        if report_result:
                            return report_url
                        if len(j) > 0 and 'response' in j:
                            report_url_list.append(report_url)
                            change_report_url_list.append(
                                {
                                    'url': report_url
                                }
                            )
                    report_url_str = ";".join(report_url_list)
                    update_data["report_url"] = report_url_str

                if i.get("photo"):
                    photo_url_list = []

                    for k in i.get("photo"):
                        photo_url, photo_result = Tools.check_photo(k)
                        if photo_result:
                            return photo_url
                        if len(k) > 0 and 'response' in k:
                            photo_url_list.append(photo_url)
                            change_photo_url_list.append(
                                {
                                    "url": photo_url,
                                    "height": "200px",
                                    "width": "200px",
                                    "marginRight": "5px"
                                }
                            )
                    photo_url_str = ";".join(photo_url_list)
                    update_data["photo_url"] = photo_url_str
                tb_db = mysql.new_mysql_instance("tbconstruct")
                con = {
                    'ticket_id': ticket_id,
                    "name": name
                }
                tb_db.begin()
                tb_db.update("equipment_before_url", update_data, con)
                tb_db.commit()
                change_list.append(
                    {
                        'name': new_name,
                        'report': change_report_url_list,
                        'photo': change_photo_url_list
                    }
                )
            for work in sg.get('work_list'):
                for i in change_list:
                    if work['work_content'] == i['name']:
                        work['report'] = i.get('report')
                        work['photo'] = i.get('photo')
                    elif ('地面' not in work['work_content'] and 'AHU' not in work['work_content'] and '水处理' not in
                          work['work_content']) and i['name'] == '其他基础施工':
                        work['report'] = i.get('report')
                        work['photo'] = i.get('photo')

            variables = {
                "change_list": change_list,
                "modify": modify,
                "modify_list": modify_list,
                'sg': sg
            }
        else:
            variables = {
                "modify": modify
            }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointConstructionSupervisionAuditConfirmation(AjaxTodoBase):
    """
        合建项目开办流程：监理确认
    """

    def __init__(self):
        super(JointConstructionSupervisionAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        start_jl_review = process_data.get("start_jl_review")
        sg_jl_review = process_data.get("sg_jl_review")
        if start_jl_review:
            if start_jl_review == '确认':
                start_jl_remark = process_data.get("start_jl_remark")
                variables = {
                    'start_jl_review': start_jl_review,
                    'start_jl_remark': start_jl_remark,
                }
            else:
                start_jl_rejection = process_data.get("start_jl_rejection")
                if start_jl_rejection:
                    variables = {
                        'start_jl_review': start_jl_review,
                        'start_jl_rejection': start_jl_rejection,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
        elif sg_jl_review:
            if sg_jl_review == '确认':
                sg_jl_remark = process_data.get("sg_jl_remark")
                variables = {
                    'sg_jl_review': sg_jl_review,
                    'sg_jl_remark': sg_jl_remark,
                }
            else:
                sg_jl_rejection = process_data.get("sg_jl_rejection")
                if sg_jl_rejection:
                    variables = {
                        'sg_jl_review': sg_jl_review,
                        'sg_jl_rejection': sg_jl_rejection,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
        else:
            return {"code": -1, "msg": "请选择审批结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointStartPMAuditConfirmation(AjaxTodoBase):
    """
        合建项目开办：pm审核
    """

    def __init__(self):
        super(JointStartPMAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        start_review = process_data.get("start_review")
        start_rejection = process_data.get("start_rejection")
        start_remark = process_data.get("start_remark", "")
        if start_review:
            if start_review == '驳回':
                if start_rejection:
                    variables = {
                        'start_review': start_review,
                        'start_rejection': start_rejection,
                        "start_remark": start_remark
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                con = {
                    'ticket_id': ticket_id
                }
                data = {
                    'start_confirm': start_review
                }
                tb_db.begin()
                tb_db.update("equipment_before_process", data, con)
                tb_db.commit()
                project_start_list = process_data.get("project_start_list")
                complete_report = process_data.get('complete_report', None)
                # other_file = process_data.get('other_file', None)
                # complete_photo = process_data.get('complete_photo', None)
                report_url_list = []
                photo_url_list = []
                report_url_str = ''
                photo_url_str = ''
                variables = {
                    'start_review': start_review,
                    "start_remark": start_remark
                }
                for i in complete_report:
                    report_url, report_result = Tools.check_report(i)
                    if report_result:
                        return report_url
                    if len(i) > 0 and 'response' in i:
                        report_url_list.append(report_url)
                appendix_list = []
                for row in project_start_list:
                    other_file = row.get('other_file', [])
                    if other_file:
                        for i in other_file:
                            report_url, report_result = Tools.check_report(i)
                            if report_result:
                                return report_url
                            if len(i) > 0 and 'response' in i:
                                report_url_list.append(report_url)
                    complete_photo = row.get('complete_photo', [])
                    if complete_photo:
                        for i in complete_photo:
                            photo_url, photo_result = Tools.check_photo(i)
                            if photo_result:
                                return photo_url
                            if len(i) > 0 and 'response' in i:
                                photo_url_list.append(photo_url)
                    else:
                        return {"code": -1, "msg": "完工照片上传失败，请检查文件"}
                if report_url_list:
                    report_url_str = ";".join(report_url_list)
                if photo_url_list:
                    photo_url_str = ";".join(photo_url_list)
                if complete_report:
                    try:
                        for file in complete_report:
                            file_url = file["response"]["FileList"][0]["url"]
                            appendix_list.append(file_url)
                    except KeyError as e:
                        return {"code": -1, "msg": f"开工令上传错误:{e}"}
                    except IndexError as e:
                        return {"code": -1, "msg": f"开工令上传错误:{e}"}
                # 链接数据库
                # 修改数据/实际开始时间、实际结束时间、实际工期、进度偏离、备注
                tb_db = mysql.new_mysql_instance("tbconstruct")
                insert_data = []
                if project_start_list:
                    for i in project_start_list:
                        remark_1 = i.get('remark', None)
                        work_content = i.get('work_content', None)
                        insert_data.append({
                            'ticket_id': ticket_id,
                            'work_content': work_content,
                            'start_time': i.get('start_time', None),
                            'finish_time': i.get('finish_time', None),
                            "construction_period": i.get('construction_period', None),
                            "progress_deviation": i.get('progress_deviation', None),
                            "remark": remark_1,
                            "plan_construction_period": i.get('plan_construction_period', None),
                            "plan_start_time": i.get('plan_start_time', None),
                            "plan_finish_time": i.get('plan_finish_time', None),
                            "report": json.dumps(i.get('report')) if i.get('report') else None,
                            "photo": json.dumps(i.get('photo')) if i.get('photo') else None,
                            "special_program": json.dumps(i.get('special_program')) if i.get(
                                'special_program') else None,
                        })
                        tb_db.begin()
                        tb_db.insert_batch("equipment_before_data", insert_data)
                        tb_db.commit()

                # 存数据库
                data = [{
                    'report_url': report_url_str,
                    'photo_url': photo_url_str,
                    'ticket_id': ticket_id,
                    'name': 'start',
                }]
                tb_db.begin()
                tb_db.insert_batch("equipment_before_url", data)
                tb_db.commit()
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointConstructionProjectManagerAuditConfirmation(AjaxTodoBase):
    """
        合建项目开办流程：项管确认
    """

    def __init__(self):
        super(JointConstructionProjectManagerAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        start_xg_review = process_data.get("start_xg_review")
        sg_xg_review = process_data.get("sg_xg_review")
        if start_xg_review:
            if start_xg_review == '确认':
                start_xg_remark = process_data.get("start_xg_remark")
                variables = {
                    'start_xg_review': start_xg_review,
                    'start_xg_remark': start_xg_remark,
                }
            else:
                start_xg_rejection = process_data.get("start_xg_rejection")
                if start_xg_rejection:
                    variables = {
                        'start_xg_review': start_xg_review,
                        'start_xg_rejection': start_xg_rejection,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
        elif sg_xg_review:
            if sg_xg_review == '确认':
                sg_xg_remark = process_data.get("sg_xg_remark")
                variables = {
                    'sg_xg_review': sg_xg_review,
                    'sg_xg_remark': sg_xg_remark,
                }
            else:
                sg_xg_rejection = process_data.get("sg_xg_rejection")
                if sg_xg_rejection:
                    variables = {
                        'sg_xg_review': sg_xg_review,
                        'sg_xg_rejection': sg_xg_rejection,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
        else:
            return {"code": -1, "msg": "请选择审批结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointConstructionPMAuditConfirmation(AjaxTodoBase):
    """
        合建项目开办流程设备进场前施工：项目PM确认
    """

    def __init__(self):
        super(JointConstructionPMAuditConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        equipment_construction_list = process_data.get("equipment_construction_list")
        ticket_id = self.ctx.variables.get('ticket_id')
        sg_review = process_data.get("sg_review")
        sg_rejection = process_data.get("sg_rejection")
        sg_remark = process_data.get("sg_remark", "")
        if sg_review:
            if sg_review == '驳回':
                if sg_rejection:
                    variables = {
                        'sg_review': sg_review,
                        'sg_rejection': sg_rejection,
                        "sg_remark": sg_remark,
                        'modify': '是'
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                con = {
                    'ticket_id': ticket_id
                }
                data = {
                    'sg_confirm': sg_review
                }
                tb_db.begin()
                tb_db.update("equipment_before_process", data, con)
                tb_db.commit()
                report_url_list = []
                photo_url_list = []
                insert_data = []
                url_insert_data = []
                for item in equipment_construction_list:
                    report = item.get('report')
                    photo = item.get('photo')
                    # report_url_list = []
                    for report in report:
                        report_url, report_result = Tools.check_report(report)
                        if report_result:
                            return report_url
                        if len(report) > 0 and 'response' in report:
                            # report_url_list.append(report_url)
                            report_url_list.append(report_url)

                    # photo_url_list = []
                    for photo in photo:
                        photo_url, photo_result = Tools.check_photo(photo)
                        if photo_result:
                            return photo_url
                        if len(photo) > 0 and 'response' in photo:
                            # photo_url_list.append(photo_url)
                            photo_url_list.append(photo_url)

                    work_content = item.get('work_content', None)
                    insert_data.append({
                        'ticket_id': ticket_id,
                        'work_content': work_content,
                        'start_time': item.get('start_time', None),
                        'finish_time': item.get('finish_time', None),
                        "construction_period": item.get('construction_period', None),
                        "progress_deviation": item.get('progress_deviation', None),
                        "plan_construction_period": item.get('plan_construction_period', None),
                        "plan_start_time": item.get('plan_start_time', None),
                        "plan_finish_time": item.get('plan_finish_time', None),
                        "report": json.dumps(item.get('report')) if item.get('report') else None,
                        "photo": json.dumps(item.get('photo')) if item.get('photo') else None,
                        "special_program": json.dumps(item.get('special_program')) if item.get(
                            'special_program') else None,
                    })
                    report_url_str = ";".join(report_url_list)
                    photo_url_str = ";".join(photo_url_list)
                    url_insert_data.append({
                        "ticket_id": ticket_id,
                        'name': work_content,
                        'report_url': report_url_str,
                        'photo_url': photo_url_str,
                    })
                    tb_db.begin()
                    tb_db.insert_batch("equipment_before_url", url_insert_data)
                    tb_db.commit()
                tb_db.begin()
                tb_db.insert_batch("equipment_before_data", insert_data)
                tb_db.commit()
                variables = {
                    'sg_review': sg_review,
                    "sg_remark": sg_remark
                }
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转
        flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")


class ConstructionStatusFeedbackBefore(object):
    def __init__(self):
        super(ConstructionStatusFeedbackBefore, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def statu_feedback(self, project_name, appendix_list, real_time, PM):
        ticket_id = self.ctx.variables.get('ticket_id')
        remark = self.ctx.variables.get('remark')
        deal_users = PM
        if not remark:
            remark = ''
        db = mysql.new_mysql_instance("tbconstruct")
        data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "appendix": str(appendix_list),
            "remark": remark,
            'real_time': real_time,
            'type': '人员入场'
        }
        # 存数据库
        db.begin()
        db.insert("construction_status_feedback_process", data)
        db.commit()
        sql = "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd " \
              "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode " \
              f"WHERE rewd.project_name = '{project_name}' "

        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table('获取需求id', {'message': code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 物料id
            material_id_list = []
            # 需求单号
            # 通过需求单号获取po信息
            for i in code_id_list:
                code_id = i.get('idcrmOrderCode')
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"})
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
            for j in po_material_info:
                material_id = j.get('materialId')
                if material_id not in material_id_list:
                    material_id_list.append(material_id)
            self.workflow_detail.add_kv_table('最终po信息', {'message': po_material_info})
            # 通过物料id获取品名信息
            material_list = gnetops.request(action="Tbconstruct",
                                            method="ObtainMaterialInfoById",
                                            ext_data={
                                                "MaterialCodeId": material_id_list
                                            }
                                            )
            self.workflow_detail.add_kv_table('物料信息', {'message': material_list})
            if material_list:
                material_info_list = material_list.get('materialList')
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    # 物料id
                    material_id = material.get('materialQueryDTO').get('materialId')
                    # 品名
                    material_category_name = material.get('materialQueryDTO').get('materialCategoryName')
                    # 品名编码
                    material_category_code = material.get('materialQueryDTO').get('materialCategoryCode')
                    if material_category_name == '集成工程' and material_category_code == '796350BIEP':
                        data.append(material_id)
                    elif material_category_name == '机电项目管理' and material_category_code == '796350CPRM':
                        data.append(material_id)
                    elif material_category_name == '项目监理' and material_category_code == '796350CPRS':
                        data.append(material_id)
                self.workflow_detail.add_kv_table('构造数据', {'message': data})
                # 将获取到的特定品名的物料id与po信息中的id对比，找出需要的数据
                for d in data:
                    for po in po_material_info:
                        if d == po.get('materialId'):
                            data_list.append({"AssetCode": "",
                                              "LineDetailId": str(po.get('orderItemId')),
                                              "QuantityReceived": po.get('orderAmount'),
                                              "SnCode": "",
                                              "AttachmentList": appendix_list
                                              })

            self.workflow_detail.add_kv_table('传参信息', {'message': data_list})
            # 获取企微id
            chat_info = gnetops.request(action="Tof",
                                        method="GetIDCStaffIDByEngName",
                                        ext_data={
                                            "EngName": str(deal_users),
                                        }
                                        )
            self.workflow_detail.add_kv_table('获取企微id', {'message': str(chat_info)})
            chat_id = ''
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table('获取企微id',
                                                  {"success": False, "data": f"获取失败:{str(chat_info)}"})
            info = gnetops.request(action="Tbconstruct",
                                   method="FeedbackAcceptanceStatusToStar",
                                   ext_data={
                                       "FeedbackType": 4,  # 0=到货,1=安装,2=初验,3=终,人员入场=4
                                       "ReceiveDate": str(real_time),  # 时间
                                       "ReceiveUserId": chat_id,  # id
                                       "Orderdetails": data_list,
                                   }
                                   )

            variables = {
                "po_material_info": po_material_info,
                "data_list": data_list,
                "info": str(info),
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table('获取po信息', {'message': '获取不到需求单号'})
            return {'code': -1, 'message': '获取不到需求单号'}
