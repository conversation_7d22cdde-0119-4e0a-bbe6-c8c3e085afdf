from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql
from biz.project_name import Database


class TraversingDatabase(object):
    def __init__(self):
        super(TraversingDatabase, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def ExtractProjectName(self):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, project FROM risk_early_warning_data"
        project_result = db.get_all(query_sql)
        data = []
        for row in project_result:
            campus = row["campus"]
            project = row["project"]
            data.append({'campus': campus, 'project_name': project})
        variables = {
            'data': data
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def score_calculation(self, aqxj_table):
        """
            巡检分数计算
        """
        data = []
        for row in aqxj_table:
            assessment_team = row.get('assessment_team')
            project_name = row.get('project_name')
            section_name = row.get('section_name')
            construction_phase = row.get('construction_phase')
            prime_contractor = row.get('prime_contractor')
            supervising_unit = row.get('supervising_unit')
            security_management_state = row.get('security_management_state')
            supervision_unit_management_behavior = row.get('supervision_unit_management_behavior')
            main_contractor_unit_management_behavior = row.get('main_contractor_unit_management_behavior')
            one_two_types_reverse_buckle = row.get('one_two_types_reverse_buckle')
            correct_reverse_penalty_points = row.get('correct_reverse_penalty_points')
            bdaqwm_overall_pass_rate = row.get('bdaqwm_overall_pass_rate')
            fireproofing = row.get('fireproofing')
            material_fire_protection = row.get('material_fire_protection')
            site_fire_prevention = row.get('site_fire_prevention')
            operational_fire_prevention = row.get('operational_fire_prevention')
            gas_cylinder = row.get('gas_cylinder')
            sb_sk_wlb_jsj = row.get('sb_sk_wlb_jsj')
            helmet = row.get('helmet')
            safety_belt = row.get('safety_belt')
            frontier_guard = row.get('frontier_guard')
            bdzy_ydsczpt = row.get('bdzy_ydsczpt')
            opening_protection = row.get('opening_protection')
            safety_utilization_electric_power = row.get('safety_utilization_electric_power')
            distribution_box_and_switch_box = row.get('distribution_box_and_switch_box')
            field_lighting = row.get('field_lighting')
            distributing_line = row.get('distributing_line')
            construction_equipment = row.get('construction_equipment')
            lifting_erection = row.get('lifting_erection')
            field_enclosure = row.get('field_enclosure')
            enclosed_management = row.get('enclosed_management')
            workyard = row.get('workyard')
            field_material = row.get('field_material')
            construction_site_sign = row.get('construction_site_sign')
            living_and_office_area = row.get('living_and_office_area')
            civilized_construction = row.get('civilized_construction')
            supervision_department_log = row.get('supervision_department_log')
            supervision_instruction = row.get('supervision_instruction')
            review_and_acceptance = row.get('review_and_acceptance')
            side_log = row.get('side_log')
            security_check = row.get('security_check')
            personnel_allocation = row.get('personnel_allocation')
            supervision_plan = row.get('supervision_plan')
            technical_disclosure = row.get('technical_disclosure')
            security_conference = row.get('security_conference')
            system_responsibility_safe_production = row.get('system_responsibility_safe_production')
            special_construction_scheme = row.get('special_construction_scheme')
            field_acceptance_test = row.get('field_acceptance_test')
            safety_technology_disclosure = row.get('safety_technology_disclosure')
            safety_inspection = row.get('safety_inspection')
            safety_education = row.get('safety_education')
            emergency_rescue = row.get('emergency_rescue')
            safety_management_subcontractor = row.get('safety_management_subcontractor')
            take_appointment_with_certificate = row.get('take_appointment_with_certificate')
            handling_production_safety_accidents = row.get('handling_production_safety_accidents')
            safety_sign = row.get('safety_sign')
            safety_civilized_quality_assurance_system = row.get('safety_civilized_quality_assurance_system')
            hazard_control = row.get('hazard_control')
            management_behavior_total_contracting_unit = row.get('management_behavior_total_contracting_unit')

            if '/' in str(security_management_state):
                security_management_state = 1
            if security_management_state is not None:
                security_management_state = round(float(security_management_state) * 100, 2)

            if '/' in str(supervision_unit_management_behavior):
                supervision_unit_management_behavior = 1
            if supervision_unit_management_behavior is not None:
                supervision_unit_management_behavior = round(float(supervision_unit_management_behavior) * 100, 2)

            if '/' in str(main_contractor_unit_management_behavior):
                main_contractor_unit_management_behavior = 1
            if main_contractor_unit_management_behavior is not None:
                main_contractor_unit_management_behavior = round(float(main_contractor_unit_management_behavior) * 100,
                                                                 2)

            if '/' in str(one_two_types_reverse_buckle):
                one_two_types_reverse_buckle = 1
            if one_two_types_reverse_buckle is not None:
                one_two_types_reverse_buckle = round(float(one_two_types_reverse_buckle) * 100, 2)

            if '/' in str(correct_reverse_penalty_points):
                correct_reverse_penalty_points = 1
            if correct_reverse_penalty_points is not None:
                correct_reverse_penalty_points = round(float(correct_reverse_penalty_points) * 100, 2)

            if '/' in str(bdaqwm_overall_pass_rate):
                bdaqwm_overall_pass_rate = 1
            if bdaqwm_overall_pass_rate is not None:
                bdaqwm_overall_pass_rate = round(float(bdaqwm_overall_pass_rate) * 100, 2)

            if '/' in str(fireproofing):
                fireproofing = 1
            if fireproofing is not None:
                fireproofing = round(float(fireproofing) * 100, 2)

            if '/' in str(material_fire_protection):
                material_fire_protection = 1
            if material_fire_protection is not None:
                material_fire_protection = round(float(material_fire_protection) * 100, 2)

            if '/' in str(site_fire_prevention):
                site_fire_prevention = 1
            if site_fire_prevention is not None:
                site_fire_prevention = round(float(site_fire_prevention) * 100, 2)

            if '/' in str(operational_fire_prevention):
                operational_fire_prevention = 1
            if operational_fire_prevention is not None:
                operational_fire_prevention = round(float(operational_fire_prevention) * 100, 2)

            if '/' in str(gas_cylinder):
                gas_cylinder = 1
            if gas_cylinder is not None:
                gas_cylinder = round(float(gas_cylinder) * 100, 2)

            if '/' in str(sb_sk_wlb_jsj):
                sb_sk_wlb_jsj = 1
            if sb_sk_wlb_jsj is not None:
                sb_sk_wlb_jsj = round(float(sb_sk_wlb_jsj) * 100, 2)

            if '/' in str(helmet):
                helmet = 1
            if helmet is not None:
                helmet = round(float(helmet) * 100, 2)

            if '/' in str(safety_belt):
                safety_belt = 1
            if safety_belt is not None:
                safety_belt = round(float(safety_belt) * 100, 2)

            if '/' in str(frontier_guard):
                frontier_guard = 1
            if frontier_guard is not None:
                frontier_guard = round(float(frontier_guard) * 100, 2)

            if '/' in str(bdzy_ydsczpt):
                bdzy_ydsczpt = 1
            if bdzy_ydsczpt is not None:
                bdzy_ydsczpt = round(float(bdzy_ydsczpt) * 100, 2)

            if '/' in str(opening_protection):
                opening_protection = 1
            if opening_protection is not None:
                opening_protection = round(float(opening_protection) * 100, 2)

            if '/' in str(safety_utilization_electric_power):
                safety_utilization_electric_power = 1
            if safety_utilization_electric_power is not None:
                safety_utilization_electric_power = round(float(safety_utilization_electric_power) * 100, 2)

            if '/' in str(distribution_box_and_switch_box):
                distribution_box_and_switch_box = 1
            if distribution_box_and_switch_box is not None:
                distribution_box_and_switch_box = round(float(distribution_box_and_switch_box) * 100, 2)

            if '/' in str(field_lighting):
                field_lighting = 1
            if field_lighting is not None:
                field_lighting = round(float(field_lighting) * 100, 2)

            if '/' in str(distributing_line):
                distributing_line = 1
            if distributing_line is not None:
                distributing_line = round(float(distributing_line) * 100, 2)

            if '/' in str(construction_equipment):
                construction_equipment = 1
            if construction_equipment is not None:
                construction_equipment = round(float(construction_equipment) * 100, 2)

            if '/' in str(lifting_erection):
                lifting_erection = 1
            if lifting_erection is not None:
                lifting_erection = round(float(lifting_erection) * 100, 2)

            if '/' in str(field_enclosure):
                field_enclosure = 1
            if field_enclosure is not None:
                field_enclosure = round(float(field_enclosure) * 100, 2)

            if '/' in str(enclosed_management):
                enclosed_management = 1
            if enclosed_management is not None:
                enclosed_management = round(float(enclosed_management) * 100, 2)

            if '/' in str(workyard):
                workyard = 1
            if workyard is not None:
                workyard = round(float(workyard) * 100, 2)

            if '/' in str(field_material):
                field_material = 1
            if field_material is not None:
                field_material = round(float(field_material) * 100, 2)

            if '/' in str(construction_site_sign):
                construction_site_sign = 1
            if construction_site_sign is not None:
                construction_site_sign = round(float(construction_site_sign) * 100, 2)

            if '/' in str(living_and_office_area):
                living_and_office_area = 1
            if living_and_office_area is not None:
                living_and_office_area = round(float(living_and_office_area) * 100, 2)

            if '/' in str(civilized_construction):
                civilized_construction = 1
            if civilized_construction is not None:
                civilized_construction = round(float(civilized_construction) * 100, 2)

            if '/' in str(supervision_department_log):
                supervision_department_log = 1
            if supervision_department_log is not None:
                supervision_department_log = round(float(supervision_department_log) * 100, 2)

            if '/' in str(supervision_instruction):
                supervision_instruction = 1
            if supervision_instruction is not None:
                supervision_instruction = round(float(supervision_instruction) * 100, 2)

            if '/' in str(review_and_acceptance):
                review_and_acceptance = 1
            if review_and_acceptance is not None:
                review_and_acceptance = round(float(review_and_acceptance) * 100, 2)

            if '/' in str(side_log):
                side_log = 1
            if side_log is not None:
                side_log = round(float(side_log) * 100, 2)

            if '/' in str(security_check):
                security_check = 1
            if security_check is not None:
                security_check = round(float(security_check) * 100, 2)

            if '/' in str(personnel_allocation):
                personnel_allocation = 1
            if personnel_allocation is not None:
                personnel_allocation = round(float(personnel_allocation) * 100, 2)

            if '/' in str(supervision_plan):
                supervision_plan = 1
            if supervision_plan is not None:
                supervision_plan = round(float(supervision_plan) * 100, 2)

            if '/' in str(technical_disclosure):
                technical_disclosure = 1
            if technical_disclosure is not None:
                technical_disclosure = round(float(technical_disclosure) * 100, 2)

            if '/' in str(security_conference):
                security_conference = 1
            if security_conference is not None:
                security_conference = round(float(security_conference) * 100, 2)

            if '/' in str(system_responsibility_safe_production):
                system_responsibility_safe_production = 1
            if system_responsibility_safe_production is not None:
                system_responsibility_safe_production = round(float(system_responsibility_safe_production) * 100, 2)

            if '/' in str(special_construction_scheme):
                special_construction_scheme = 1
            if special_construction_scheme is not None:
                special_construction_scheme = round(float(special_construction_scheme) * 100, 2)

            if '/' in str(field_acceptance_test):
                field_acceptance_test = 1
            if field_acceptance_test is not None:
                field_acceptance_test = round(float(field_acceptance_test) * 100, 2)

            if '/' in str(safety_technology_disclosure):
                safety_technology_disclosure = 1
            if safety_technology_disclosure is not None:
                safety_technology_disclosure = round(float(safety_technology_disclosure) * 100, 2)

            if '/' in str(safety_inspection):
                safety_inspection = 1
            if safety_inspection is not None:
                safety_inspection = round(float(safety_inspection) * 100, 2)

            if '/' in str(safety_education):
                safety_education = 1
            if safety_education is not None:
                safety_education = round(float(safety_education) * 100, 2)

            if '/' in str(emergency_rescue):
                emergency_rescue = 1
            if emergency_rescue is not None:
                emergency_rescue = round(float(emergency_rescue) * 100, 2)

            if '/' in str(safety_management_subcontractor):
                safety_management_subcontractor = 1
            if safety_management_subcontractor is not None:
                safety_management_subcontractor = round(float(safety_management_subcontractor) * 100, 2)

            if '/' in str(take_appointment_with_certificate):
                take_appointment_with_certificate = 1
            if take_appointment_with_certificate is not None:
                take_appointment_with_certificate = round(float(take_appointment_with_certificate) * 100, 2)

            if '/' in str(handling_production_safety_accidents):
                handling_production_safety_accidents = 1
            if handling_production_safety_accidents is not None:
                handling_production_safety_accidents = round(float(handling_production_safety_accidents) * 100, 2)

            if '/' in str(safety_sign):
                safety_sign = 1
            if safety_sign is not None:
                safety_sign = round(float(safety_sign) * 100, 2)

            if '/' in str(safety_civilized_quality_assurance_system):
                safety_civilized_quality_assurance_system = 1
            if safety_civilized_quality_assurance_system is not None:
                safety_civilized_quality_assurance_system = round(
                    float(safety_civilized_quality_assurance_system) * 100, 2)

            if '/' in str(hazard_control):
                hazard_control = 1
            if hazard_control is not None:
                hazard_control = round(float(hazard_control) * 100, 2)

            if '/' in str(management_behavior_total_contracting_unit):
                management_behavior_total_contracting_unit = 1
            if management_behavior_total_contracting_unit is not None:
                management_behavior_total_contracting_unit = round(
                    float(management_behavior_total_contracting_unit) * 100, 2)
            data.append({
                'assessment_team': assessment_team,
                'project_name': project_name,
                'section_name': section_name,
                'construction_phase': construction_phase,
                'prime_contractor': prime_contractor,
                'supervising_unit': supervising_unit,
                'security_management_state': security_management_state,
                'supervision_unit_management_behavior': supervision_unit_management_behavior,
                'main_contractor_unit_management_behavior': main_contractor_unit_management_behavior,
                'one_two_types_reverse_buckle': one_two_types_reverse_buckle,
                'correct_reverse_penalty_points': correct_reverse_penalty_points,
                'bdaqwm_overall_pass_rate': bdaqwm_overall_pass_rate,
                'fireproofing': fireproofing,
                'material_fire_protection': material_fire_protection,
                'site_fire_prevention': site_fire_prevention,
                'operational_fire_prevention': operational_fire_prevention,
                'gas_cylinder': gas_cylinder,
                'sb_sk_wlb_jsj': sb_sk_wlb_jsj,
                'helmet': helmet,
                'safety_belt': safety_belt,
                'frontier_guard': frontier_guard,
                'bdzy_ydsczpt': bdzy_ydsczpt,
                'opening_protection': opening_protection,
                'safety_utilization_electric_power': safety_utilization_electric_power,
                'distribution_box_and_switch_box': distribution_box_and_switch_box,
                'field_lighting': field_lighting,
                'distributing_line': distributing_line,
                'construction_equipment': construction_equipment,
                'lifting_erection': lifting_erection,
                'field_enclosure': field_enclosure,
                'enclosed_management': enclosed_management,
                'workyard': workyard,
                'field_material': field_material,
                'construction_site_sign': construction_site_sign,
                'living_and_office_area': living_and_office_area,
                'civilized_construction': civilized_construction,
                'supervision_department_log': supervision_department_log,
                'supervision_instruction': supervision_instruction,
                'review_and_acceptance': review_and_acceptance,
                'side_log': side_log,
                'security_check': security_check,
                'personnel_allocation': personnel_allocation,
                'supervision_plan': supervision_plan,
                'technical_disclosure': technical_disclosure,
                'security_conference': security_conference,
                'system_responsibility_safe_production': system_responsibility_safe_production,
                'special_construction_scheme': special_construction_scheme,
                'field_acceptance_test': field_acceptance_test,
                'safety_technology_disclosure': safety_technology_disclosure,
                'safety_inspection': safety_inspection,
                'safety_education': safety_education,
                'emergency_rescue': emergency_rescue,
                'safety_management_subcontractor': safety_management_subcontractor,
                'take_appointment_with_certificate': take_appointment_with_certificate,
                'handling_production_safety_accidents': handling_production_safety_accidents,
                'safety_sign': safety_sign,
                'safety_civilized_quality_assurance_system': safety_civilized_quality_assurance_system,
                'hazard_control': hazard_control,
                'management_behavior_total_contracting_unit': management_behavior_total_contracting_unit
            })
        return data


class FileInformation(AjaxTodoBase):
    """
        第三方文件上传
    """

    def __init__(self):
        super(FileInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        insert_data = {}
        insert_data2 = []
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = process_data.get('campus', None)
        project = process_data.get('project', None)
        inspection_time = process_data.get('inspection_time', None)
        inspection_score = process_data.get('inspection_score', None)
        main_problem = process_data.get('main_problem', None)
        time_completion_demand_rectification = process_data.get('time_completion_demand_rectification', None)
        problem_report = process_data.get('problem_report', None)
        aqxj_table = process_data.get('aqxj_table', None)
        if problem_report and len(problem_report) > 0 and 'response' in problem_report[0]:
            insert_data = {
                'problem_report': problem_report[0]["response"]["FileList"][0]["url"],
                'campus': campus,
                'project_name': project,
                'inspection_time': inspection_time,
                'inspection_score': inspection_score,
                'main_problem': main_problem,
                'time_completion_demand_rectification': time_completion_demand_rectification,
                'ticket_id': ticket_id
            }
        problem_report_url = []
        problem_report_table = []
        if problem_report is not None:
            for doc in problem_report:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        problem_report_url.append(url)
                        problem_report_table.append({
                            "problem_report": url,
                            'problem_report_name': name
                        })
        project_name = campus + project
        for row in aqxj_table:
            insert_data2.append({
                'ticket_id': ticket_id,
                'assessment_team': row.get('assessment_team', 100),
                'project_name': project_name,
                'section_name': row.get('section_name', 100),
                'construction_phase': row.get('construction_phase', 100),
                'prime_contractor': row.get('prime_contractor', 100),
                'supervising_unit': row.get('supervising_unit', 100),
                'security_management_state': row.get('security_management_state', 100),
                'supervision_unit_management_behavior': row.get('supervision_unit_management_behavior', 100),
                'main_contractor_unit_management_behavior': row.get('main_contractor_unit_management_behavior', 100),
                'one_two_types_reverse_buckle': row.get('one_two_types_reverse_buckle', 100),
                'correct_reverse_penalty_points': row.get('correct_reverse_penalty_points', 100),
                'bdaqwm_overall_pass_rate': row.get('bdaqwm_overall_pass_rate', 100),
                'fireproofing': row.get('fireproofing', 100),
                'material_fire_protection': row.get('material_fire_protection', 100),
                'site_fire_prevention': row.get('site_fire_prevention', 100),
                'operational_fire_prevention': row.get('operational_fire_prevention', 100),
                'gas_cylinder': row.get('gas_cylinder', 100),
                'sb_sk_wlb_jsj': row.get('sb_sk_wlb_jsj', 100),
                'helmet': row.get('helmet', 100),
                'safety_belt': row.get('safety_belt', 100),
                'frontier_guard': row.get('frontier_guard', 100),
                'bdzy_ydsczpt': row.get('bdzy_ydsczpt', 100),
                'opening_protection': row.get('opening_protection', 100),
                'safety_utilization_electric_power': row.get('safety_utilization_electric_power', 100),
                'distribution_box_and_switch_box': row.get('distribution_box_and_switch_box', 100),
                'field_lighting': row.get('field_lighting', 100),
                'distributing_line': row.get('distributing_line', 100),
                'construction_equipment': row.get('construction_equipment', 100),
                'lifting_erection': row.get('lifting_erection', 100),
                'field_enclosure': row.get('field_enclosure', 100),
                'enclosed_management': row.get('enclosed_management', 100),
                'workyard': row.get('workyard', 100),
                'field_material': row.get('field_material', 100),
                'construction_site_sign': row.get('construction_site_sign', 100),
                'living_and_office_area': row.get('living_and_office_area', 100),
                'civilized_construction': row.get('civilized_construction', 100),
                'supervision_department_log': row.get('supervision_department_log', 100),
                'supervision_instruction': row.get('supervision_instruction', 100),
                'review_and_acceptance': row.get('review_and_acceptance', 100),
                'side_log': row.get('side_log', 100),
                'security_check': row.get('security_check', 100),
                'personnel_allocation': row.get('personnel_allocation', 100),
                'supervision_plan': row.get('supervision_plan', 100),
                'technical_disclosure': row.get('technical_disclosure', 100),
                'security_conference': row.get('security_conference', 100),
                'system_responsibility_safe_production': row.get('system_responsibility_safe_production', 100),
                'special_construction_scheme': row.get('special_construction_scheme', 100),
                'field_acceptance_test': row.get('field_acceptance_test', 100),
                'safety_technology_disclosure': row.get('safety_technology_disclosure', 100),
                'safety_inspection': row.get('safety_inspection', 100),
                'safety_education': row.get('safety_education', 100),
                'emergency_rescue': row.get('emergency_rescue', 100),
                'safety_management_subcontractor': row.get('safety_management_subcontractor', 100),
                'take_appointment_with_certificate': row.get('take_appointment_with_certificate', 100),
                'handling_production_safety_accidents': row.get('handling_production_safety_accidents', 100),
                'safety_sign': row.get('safety_sign', 100),
                'safety_civilized_quality_assurance_system': row.get('safety_civilized_quality_assurance_system', 100),
                'hazard_control': row.get('hazard_control', 100),
                'management_behavior_total_contracting_unit': row.get('management_behavior_total_contracting_unit', 100)
            })

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("third_party_report_data", insert_data)
        tb_db.insert_batch("security_inspection_problem_classification", insert_data2)
        tb_db.commit()
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}'" \
                    f"and del_flag = 0"
        project_result = db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        integrator = account_dict.get('集成商-项目经理')
        item_tube = account_dict.get('项管-项目经理')
        supervision_management = account_dict.get('监理-总监理工程师')
        variables = {
            'campus': campus,
            'project': project,
            'problem_report': problem_report,
            'problem_report_table': problem_report_table,
            'inspection_time': inspection_time,
            'inspection_score': inspection_score,
            'main_problem': main_problem,
            'time_completion_demand_rectification': time_completion_demand_rectification,
            'insert_data2': insert_data2,
            'integrator': integrator,
            'supervision_management': supervision_management,
            'item_tube': item_tube,
            'aqxj_table': aqxj_table
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RectifyingCorrectingDocuments(AjaxTodoBase):
    """
        整改文件上传
    """

    def __init__(self):
        super(RectifyingCorrectingDocuments, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        update_data = {}
        rectification_report = process_data.get('rectification_report', None)
        ticket_id = self.ctx.variables.get("ticket_id")
        if rectification_report and len(rectification_report) > 0 and 'response' in rectification_report[0]:
            update_data = {
                'rectification_report': rectification_report[0]["response"]["FileList"][0]["url"]
            }
        conditions = {
            'ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("third_party_report_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'rectification_report': rectification_report
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
