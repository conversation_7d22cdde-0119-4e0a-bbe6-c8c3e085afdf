import datetime
import json
import re

from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_before_eruipment.tools import Tools
from biz.utils.change_url import problem_rectification_file_url_change


def transform_url_list(url_list_str):
    """
    将URL列表字符串转换为指定格式的列表（保留原始URL不做修改）
    
    参数:
        url_list_str: 包含多个URL的列表字符串
        
    返回:
        list: 转换后的列表结构
    """
    try:
        # 将字符串形式的列表转换为实际列表
        url_list = eval(url_list_str)

        # 处理每个URL，构建结果列表
        result = []
        for url in url_list:
            # 提取文件名（从URL最后一个'/'之后的部分）
            file_name = url.split('/')[-1]

            # 构建每个URL对应的数据结构（不再替换'test'）
            item = {
                'name': file_name,
                'response': {
                    'FileList': [{
                        'name': file_name,
                        'status': 0,
                        'url': url  # 直接使用原始URL
                    }]
                }
            }
            result.append(item)

        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        return url_list_str


def update_rectifier_information(person_name, project_name):
    role = ""
    account_new = ""
    db = mysql.new_mysql_instance("tbconstruct")
    sql = (
            "SELECT account,del_flag,role FROM project_role_account"
            " WHERE project_name='%s' AND account = '%s' "
            % (project_name, person_name)
    )
    account_dict = db.get_row(sql)
    if account_dict:
        del_flag = account_dict.get("del_flag")
        role = account_dict.get("role")
        if del_flag == "0":
            account_new = person_name
        else:
            sql_1 = (
                "SELECT account FROM project_role_account"
                " WHERE project_name='%s' AND role='%s' AND del_flag = '0'"
                % (project_name, role)
            )
            account_dict_new = db.get_row(sql_1)
            if account_dict_new:
                account_new = account_dict_new.get("account")
            else:
                sql_2 = (
                    "SELECT account FROM project_role_account"
                    " WHERE project_name='%s' AND role in('合建方-项目经理','项管-项目经理') AND del_flag = '0'"
                    % (project_name)
                )
                xg_dict = db.get_row(sql_2)
                account_new = xg_dict.get("account")
    if not account_new:
        account_new = "v_zongxiyu"
    return account_new


class GetRectifierInformation(object):
    def __init__(self):
        super(GetRectifierInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def hire_project_get_rectifier_information(self, problem_list, project_name, db, current_time, ticket_id):
        zb_account = ""
        xg_account = ""
        jl_account = ""
        zb_service_provider = ""
        zb_list = []
        xg_list = []
        jl_list = []
        major = problem_list[0].get("major")
        module_name = problem_list[0].get("project_name")
        question_ticket = problem_list[0].get("question_ticket")
        rectifier_list = []
        rectifier_data = []
        # 通过工单号获取流程标题
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "Title": "",
                },
                "SearchCondition": {"TicketId": [question_ticket]},
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        if query_result:
            title = query_result.get("List")[0].get("Title")
            # 获取提出人信息
            proposer = ""
            if title:
                proposer_list = title.split("-")
                if len(proposer_list) == 2:
                    proposer = proposer_list[1]
        else:
            proposer = ""

        sql_all = (
                "SELECT account,name,role,service_provider FROM project_role_account"
                " WHERE project_name='%s' and del_flag = '0'" % project_name
        )
        all_person_list = db.get_all(sql_all)
        rectifier = ''
        for row in all_person_list:
            role = row.get("role", "")
            account = row.get("account", "")
            if role == "总包-项目经理":
                zb_account = account
                zb_service_provider = row.get("service_provider")
        for row in all_person_list:
            role = row.get("role", "")
            account = row.get("account", "")
            name = row.get("name", "")
            if major in role and "总包" in role:
                rectifier = account
                zb_list.append(rectifier)
            if role == "总包-项目经理":
                zb_account = account
                zb_service_provider = row.get("service_provider")
                zb_list.append(zb_account)
            if "厂家" in role:
                rectifier_data.append({"role": role,
                                       "account": account,
                                       "service_provider": row.get("service_provider")})
                rectifier_list.append({"label": f"{role}（{account}）", "value": account})
            if "总包" in role:
                rectifier_data.append({"role": role,
                                       "account": account,
                                       "service_provider": row.get("service_provider")})
                rectifier_list.append({"label": f"{role}（{account}）", "value": account})
            elif role == "监理-总监理工程师":
                jl_account = account
            elif role == "合建方-项目经理":
                xg_account = account
            elif major in role and "监理" in role:
                jl_list.append(account)
            elif major in role and "合建方" in role:
                zb_list.append(account)
                xg_list.append(account)
        if not rectifier:
            rectifier = zb_account
            zb_list.append(xg_account)
        else:
            zb_list.append(xg_account)
        if jl_list:
            jl_list.append(xg_account)
        else:
            jl_list.append(jl_account)
            jl_list.append(xg_account)
        xg_list.append(xg_account)
        data = {}
        subordinate_work_order_id = ""
        for i in problem_list:
            subordinate_work_order_id = i.get("subordinate_work_order_id")
            if subordinate_work_order_id:
                if "http" in subordinate_work_order_id:
                    data["radar"] = subordinate_work_order_id
                else:
                    subordinate_work_order_id = ""

            problem_photo = i.get("problem_photo")
            i["proposer"] = proposer
            i["problem_put_forward_time"] = str(current_time)
            problem_photo_url_list = []
            if problem_photo:
                for j in problem_photo:
                    if j.get("url") is not None:
                        problem_photo_url_list.append(j.get("url"))
            problem_photo_url = ";".join(problem_photo_url_list)
            data["question_ticket"] = i.get("question_ticket")
            data["ticket_id"] = ticket_id
            data["project_name"] = project_name
            data["device_name"] = i.get("device_name")
            data["device_id"] = i.get("device_id")
            data["problem_location_and_description"] = i.get(
                "problem_location_and_description"
            )
            data["problem_level"] = i.get("problem_level")
            data["problem_photo"] = problem_photo_url
            data["major"] = i.get("major")
            data["module_name"] = module_name
            data["problem_type"] = i.get("problem_type")
            data["proposer"] = proposer
            data["room_id"] = i.get("room_id")
            data["problem_put_forward_time"] = str(current_time)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        # 存数据库
        tb_db.begin()
        tb_db.insert("problem_rectification", data)
        tb_db.commit()

        variables = {
            "problem_list": problem_list,
            "project_name": project_name,
            "module_name": module_name,
            "zb_account": zb_account,
            "jl_account": jl_account,
            "xg_account": xg_account,
            "rectifier_data": rectifier_data,
            "rectifier_list": rectifier_list,
            "zb_service_provider": zb_service_provider,
            "data": data,
            "subordinate_work_order_id": subordinate_work_order_id,
            "zb_list": zb_list,
            "xg_list": xg_list,
            "jl_list": jl_list,
            "rectifier": rectifier
        }
        return variables

    def get_rectifier_information(self):
        current_time = datetime.date.today()
        ticket_id = self.ctx.variables.get("ticket_id")
        problem_list = self.ctx.variables.get("problem_list")
        rectifier_list = []
        if problem_list:
            zb_account = ""
            xg_account = ""
            jl_account = ""
            zb_service_provider = ""
            zb_list = []
            xg_list = []
            jl_list = []
            major = problem_list[0].get("major")
            module_name = problem_list[0].get("project_name")
            question_ticket = problem_list[0].get("question_ticket")

            # 部分外网照片链接处理
            problem_photo = problem_list[0].get("problem_photo")
            for photo in problem_photo:
                if photo.get("url", "").startswith("https://idc-sc2-**********.cos"):
                    photo["url"] = problem_rectification_file_url_change(photo.get("url"))

                self.workflow_detail.add_kv_table("修改的照片", {"url": photo.get("url"), "name": photo.get("name")})

            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                    "SELECT project_name FROM project_module_mapping_relationship "
                    " WHERE module_name='%s'" % module_name
            )
            project_name_list = db.get_all(sql)
            if project_name_list:
                project_name = project_name_list[0].get("project_name")
                sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                               f"WHERE project_name='{project_name}'")
                is_hire = False if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else True
                if is_hire:
                    variables = self.hire_project_get_rectifier_information(problem_list, project_name, db,
                                                                            current_time, ticket_id)
                    variables.update({"is_hire": "1"})
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return
                sql_all = (
                        "SELECT account,name FROM project_role_account"
                        " WHERE project_name='%s' and del_flag = '0'" % project_name
                )
                name_all = db.get_all(sql_all)
                count_all = []
                name_list = []
                if name_all:
                    for name in name_all:
                        value = name.get("account")
                        if value not in name_list:
                            count_all.append(
                                {
                                    "label": name.get("account")
                                             + "("
                                             + name.get("name")
                                             + ")",
                                    "value": value,
                                }
                            )
                            name_list.append(value)
                # 通过工单号获取流程标题
                query_data = {
                    "SchemaId": "ticket_base",
                    "Data": {
                        "ResultColumns": {
                            "Title": "",
                        },
                        "SearchCondition": {"TicketId": [question_ticket]},
                    },
                }
                query_result = gnetops.request(
                    action="QueryData", method="Run", ext_data=query_data
                )
                if query_result:
                    title = query_result.get("List")[0].get("Title")
                    # 获取提出人信息
                    proposer = ""
                    if title:
                        proposer_list = title.split("-")
                        if len(proposer_list) == 2:
                            proposer = proposer_list[1]
                else:
                    proposer = ""
                # 获取厂家信息
                sql = (
                        "SELECT role,account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' AND role LIKE '厂家%%' and del_flag = '0'"
                        % project_name
                )
                rectifier_data = db.get_all(sql)
                if rectifier_data:
                    for i in rectifier_data:
                        rectifier_list.append(
                            {"label": f'{i.get("role")}（{i.get("account")}）', "value": i.get("account")}
                        )
                # 获取集成商、监理、项管总接口
                zb_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '集成商-项目经理' and del_flag = '0'"
                        % project_name
                )
                jl_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '监理-总监理工程师' and del_flag = '0'"
                        % project_name
                )
                xg_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '项管-项目经理' and del_flag = '0'"
                        % project_name
                )
                zb_boss_sql = db.get_all(zb_sql)
                jl_boss_sql = db.get_all(jl_sql)
                xg_boss_sql = db.get_all(xg_sql)
                # 节点人都加入总接口
                if zb_boss_sql:
                    zb_account = zb_boss_sql[0].get("account")
                    zb_service_provider = zb_boss_sql[0].get("service_provider")
                    rectifier_data.append({
                        "role": "集成商-项目经理",
                        "account": zb_account,
                        "service_provider": zb_service_provider
                    })
                    zb_list.append(zb_account)
                    rectifier_list.append({"label": f"集成商-项目经理（{zb_account}）", "value": zb_account})
                if jl_boss_sql:
                    jl_account = jl_boss_sql[0].get("account")
                    jl_list.append(jl_account)
                if xg_boss_sql:
                    xg_account = xg_boss_sql[0].get("account")
                    xg_list.append(xg_account)

                if major:
                    sql1 = (
                            "SELECT role,account,service_provider FROM project_role_account"
                            " WHERE project_name='%s' AND role LIKE '%%%s%%' And del_flag = '0'"
                            % (project_name, major)
                    )
                    account_list = db.get_all(sql1)
                    if account_list:
                        for i in account_list:
                            if "集成商" in i.get("role") or "总包" in i.get("role"):
                                zb_account = i.get("account")
                                zb_list.append(zb_account)
                                zb_service_provider = i.get("service_provider")
                                rectifier_data.append({
                                    "role": i.get("role"),
                                    "account": zb_account,
                                    "service_provider": i.get("service_provider")
                                })
                                rectifier_list.append(
                                    {"label": f'{i.get("role")}（{i.get("account")}）', "value": i.get("account")}
                                )
                            elif "监理" in i.get("role"):
                                jl_list.append(i.get("account"))
                            elif "项管" in i.get("role"):
                                xg_list.append(i.get("account"))
                data = {}
                subordinate_work_order_id = ""
                for i in problem_list:
                    subordinate_work_order_id = i.get("subordinate_work_order_id")
                    if subordinate_work_order_id:
                        if "http" in subordinate_work_order_id:
                            data["radar"] = subordinate_work_order_id
                        else:
                            subordinate_work_order_id = ""

                    problem_photo = i.get("problem_photo")
                    i["proposer"] = proposer
                    i["problem_put_forward_time"] = str(current_time)
                    problem_photo_url_list = []
                    if problem_photo:
                        for j in problem_photo:
                            if j.get("url") is not None:
                                problem_photo_url_list.append(j.get("url"))
                    problem_photo_url = ";".join(problem_photo_url_list)
                    data["question_ticket"] = i.get("question_ticket")
                    data["ticket_id"] = ticket_id
                    data["project_name"] = project_name
                    data["device_name"] = i.get("device_name")
                    data["device_id"] = i.get("device_id")
                    data["problem_location_and_description"] = i.get(
                        "problem_location_and_description"
                    )
                    data["problem_level"] = i.get("problem_level")
                    data["problem_photo"] = problem_photo_url
                    data["major"] = i.get("major")
                    data["module_name"] = module_name
                    data["problem_type"] = i.get("problem_type")
                    data["proposer"] = proposer
                    data["room_id"] = i.get("room_id")
                    data["problem_put_forward_time"] = str(current_time)

                tb_db = mysql.new_mysql_instance("tbconstruct")
                # 存数据库
                tb_db.begin()
                tb_db.insert("problem_rectification", data)
                tb_db.commit()

                match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
                if match:
                    campus = match.group(1)
                    project = match.group(2)
                else:
                    campus = project_name
                    project = project_name

                variables = {
                    "problem_list": problem_list,
                    "project_name": project_name,
                    "campus": campus,
                    "project": project,
                    "module_name": module_name,
                    "zb_account": zb_account,
                    "jl_account": jl_account,
                    "xg_account": xg_account,
                    "rectifier_data": rectifier_data,
                    "rectifier_list": rectifier_list,
                    "zb_service_provider": zb_service_provider,
                    "data": data,
                    "subordinate_work_order_id": subordinate_work_order_id,
                    "zb_list": zb_list,
                    "xg_list": xg_list,
                    "jl_list": jl_list,
                    "count_all": count_all,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)

            else:
                self.workflow_detail.add_kv_table(
                    "自动获取数据节点", {"success": False, "data": "没有获取到项目名称"}
                )
        else:
            self.workflow_detail.add_kv_table(
                "自动获取数据节点", {"success": False, "data": "没有传问题信息"}
            )


class ChooseRectifier(AjaxTodoBase):
    """
    获取整改责任人信息
    """

    def __init__(self):
        super(ChooseRectifier, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        current_time = datetime.date.today()
        project_name = self.ctx.variables.get("project_name")
        zb_service_provider = self.ctx.variables.get("zb_service_provider")
        zb_account = self.ctx.variables.get("zb_account")
        ticket_id = self.ctx.variables.get("ticket_id")
        rectifier_data = self.ctx.variables.get("rectifier_data")
        module_name = self.ctx.variables.get("module_name")
        problem_list = process_data.get("problem_list")
        question_ticket = problem_list[0].get("question_ticket")
        person_name = process_data.get("rectifier")
        responsible = update_rectifier_information(project_name=project_name,
                                                   person_name=person_name)
        project_name = self.ctx.variables.get("project_name")
        responsible_unit_list = []
        is_reject = process_data.get("is_reject")
        reject_reason = process_data.get("reject_reason")
        # 获取责任单位数据源
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT supplier FROM category_supplier_info"
        field_value_query = db.get_all(sql)
        responsible_unit_set = set()
        if field_value_query:
            for record in field_value_query:
                field_value = record.get("supplier")
                if field_value:
                    field_value_list = field_value.split(";")
                    for i in field_value_list:
                        if i.strip():  # 确保不添加空字符串
                            responsible_unit_set.add(i.strip())

            # 将去重后的供应商信息加入 responsible_unit_list
        for unit in responsible_unit_set:
            responsible_unit_list.append({
                "label": unit,
                "value": unit,
            })

        responsible_unit = ""
        if responsible == zb_account:
            responsible_unit = zb_service_provider
        elif rectifier_data and responsible:
            for i in rectifier_data:
                if responsible == i.get("account"):
                    responsible_unit = i.get("service_provider")
        if problem_list:
            for i in problem_list:
                i["responsible_unit"] = responsible_unit

        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = (
            "SELECT question_ticket FROM problem_rectification "
            f"WHERE ticket_id = '{ticket_id}' "
        )
        result_list = db.get_all(sql1)
        if not result_list:
            data = {}
            for i in problem_list:
                problem_photo = i.get("problem_photo")
                problem_photo_url_list = []
                if problem_photo:
                    for j in problem_photo:
                        if j.get("url") is not None:
                            problem_photo_url_list.append(j.get("url"))
                problem_photo_url = ";".join(problem_photo_url_list)
                data["question_ticket"] = i.get("question_ticket")
                data["ticket_id"] = ticket_id
                data["project_name"] = project_name
                data["device_name"] = i.get("device_name")
                data["device_id"] = i.get("device_id")
                data["problem_location_and_description"] = i.get(
                    "problem_location_and_description"
                )
                data["problem_level"] = i.get("problem_level")
                data["problem_photo"] = problem_photo_url
                data["major"] = i.get("major")
                data["module_name"] = module_name
                data["responsible_unit"] = i.get("responsible_unit")
                data["problem_type"] = i.get("problem_type")
                data["room_id"] = i.get("room_id")
                data["problem_put_forward_time"] = str(current_time)
                data['cause_rejection'] = reject_reason
            tb_db = mysql.new_mysql_instance("tbconstruct")
            # 存数据库
            tb_db.begin()
            tb_db.insert("problem_rectification", data)
            tb_db.commit()
        else:
            # 存数据库
            data = {"responsible_unit": responsible_unit,
                    'cause_rejection': reject_reason}
            cond = {
                "ticket_id": ticket_id,
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("problem_rectification", data, cond)
            tb_db.commit()
        if is_reject == "驳回":
            if not reject_reason:
                return {"code": -1, "msg": "请输入驳回原因"}
            resp = gnetops.request(
                action="Verificationrectificationofresponsibleunit",
                method="QuestionSubmitCheckItem",
                ext_data={
                    "ProcessUser": process_user,
                    "IsReject": "驳回",
                    "RejectReason": reject_reason,
                    "TicketId": question_ticket
                },
                scheme="ifob-fre"
            )
            self.workflow_detail.add_kv_table("回传信息", {"resp": resp})
            if resp != "提交成功":
                return {"code": -1, "msg": "驳回状态传递失败，请稍后重试或联系开发人员"}
        variables = {
            "responsible": responsible,
            "rectifier": responsible,
            "responsible_unit": responsible_unit,
            "problem_list": problem_list,
            "responsible_unit_list": responsible_unit_list,
            "is_reject": is_reject
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ModifyProblemUpload(AjaxTodoBase):
    """
    问题整改上传
    """

    def __init__(self):
        super(ModifyProblemUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        personnel_selection = process_data.get("personnel_selection")
        problem_list = process_data.get("problem_list")
        project_name = self.ctx.variables.get("project_name")
        person_name = self.ctx.variables.get("responsible")
        responsible = update_rectifier_information(project_name=project_name,
                                                   person_name=person_name)
        if personnel_selection:
            if personnel_selection == "通过" and problem_list:
                if problem_list[0].get("plan_resolution_date") and problem_list[0].get(
                        "responsible_unit"
                ):
                    variables = {
                        "problem_list": problem_list,
                        "solve_list": problem_list,
                        "personnel_selection": personnel_selection,
                        "responsible": responsible,
                    }
                else:
                    return {"code": -1, "msg": "责任单位和计划解决日期为必填项"}
            else:
                variables = {"personnel_selection": personnel_selection}
        else:
            return {"code": -1, "msg": "请进行选择"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ModifyProblemSolve(AjaxTodoBase):
    """
    问题整改确认
    """

    def __init__(self):
        super(ModifyProblemSolve, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        solve_list = process_data.get("solve_list")
        reason_rejection = self.ctx.variables.get("cause_rejection")
        appendix = process_data.get("appendix")
        no_photos = process_data.get("no_photos")
        appendix_list = []
        if appendix:
            for app in appendix:
                url, result = Tools.check_photo(app)
                if result:
                    return url
                appendix_list.append(url)
        appendix_url = ";".join(appendix_list)
        fix_photo_response = ""
        solve_type = ""
        causes_and_solutions = ""
        question_ticket = ""
        responsible_unit = ""
        remark = ""
        problem_type = ""
        product_owner = ""
        show = 1
        data = {"appendix_url": appendix_url}
        if solve_list and len(solve_list) > 0:
            for i in solve_list:
                fix_photo = i.get("fix_photo")
                fix_photo_response = str(fix_photo)
                url_list = []
                show_photo = []
                if fix_photo:
                    for j in fix_photo:
                        url, result = Tools.check_photo(j)
                        if result:
                            return url
                        url_list.append(url)
                        show_photo.append(
                            {
                                "url": url,
                                "height": "150px",
                                "width": "150px",
                                "marginRight": "5px",
                            }
                        )
                    i["fix_photo_url"] = ";".join(url_list)
                else:
                    show = 2
                    if no_photos:
                        i["fix_photo_url"] = no_photos
                    else:
                        return {"code": -1, "msg": "请填写无照片备注"}

                i["photo"] = show_photo
                solve_type = i.get("solve_type")
                causes_and_solutions = i.get("causes_and_solutions")
                question_ticket = i.get("question_ticket")
                responsible_unit = i.get("responsible_unit")
                remark = i.get("remark")
                problem_type = i.get("problem_type")
                product_owner = i.get("product_owner")
                data["problem_type"] = i.get("problem_type")
                data["causes_and_solutions"] = i.get("causes_and_solutions")
                data["solve_type"] = i.get("solve_type")
                data["responsible_unit"] = i.get("responsible_unit")
                data["plan_resolution_date"] = i.get("plan_resolution_date")
                data["fix_photo_url"] = i.get("fix_photo_url")
                data["problem_end_time"] = i.get("problem_end_time")

        db = mysql.new_mysql_instance("tbconstruct")
        project_name = self.ctx.variables.get("project_name")
        problem_list = self.ctx.variables.get("problem_list")
        if problem_list:
            major = problem_list[0].get("major")
            jl_list = []
            jl_sql = (
                    "SELECT account,service_provider FROM project_role_account"
                    " WHERE project_name='%s' and role = '监理-总监理工程师' and del_flag = '0'"
                    % project_name
            )
            jl_boss_sql = db.get_all(jl_sql)
            if jl_boss_sql:
                jl_account = jl_boss_sql[0].get("account")
                jl_list.append(jl_account)
            if major:
                sql1 = (
                        "SELECT role,account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' AND role LIKE '%%%s%%' and del_flag = '0'"
                        % (project_name, major)
                )
                account_list = db.get_all(sql1)
                if account_list:
                    for i in account_list:
                        if "监理" in i.get("role"):
                            jl_list.append(i.get("account"))
            variables = {
                "solve_list": solve_list,
                "insert_data": data,
                "process_user": process_user,
                "fix_photo_response": fix_photo_response,
                "solve_type": solve_type,
                "causes_and_solutions": causes_and_solutions,
                "question_ticket": question_ticket,
                "reason_rejection": reason_rejection,
                "responsible_unit": responsible_unit,
                "remark": remark,
                "problem_type": problem_type,
                "show": show,
                "no_photos": no_photos,
                "product_owner": product_owner,
                "jl_list": jl_list
            }
        else:
            variables = {
                "solve_list": solve_list,
                "insert_data": data,
                "process_user": process_user,
                "fix_photo_response": fix_photo_response,
                "solve_type": solve_type,
                "causes_and_solutions": causes_and_solutions,
                "question_ticket": question_ticket,
                "reason_rejection": reason_rejection,
                "responsible_unit": responsible_unit,
                "remark": remark,
                "problem_type": problem_type,
                "show": show,
                "no_photos": no_photos,
                "product_owner": product_owner
            }
        person_name = self.ctx.variables.get("responsible")
        responsible = update_rectifier_information(project_name=project_name,
                                                   person_name=person_name)
        variables.update({"responsible": responsible})
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SupervisionAndReview(AjaxTodoBase):
    """
    监理审核
    """

    def __init__(self):
        super(SupervisionAndReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jl_remark = process_data.get("jl_remark")
        jl_review = process_data.get("jl_review")
        jl_rejection = process_data.get("jl_rejection")
        if jl_review:
            if jl_review == "驳回":
                if jl_rejection:
                    variables = {
                        "jl_review": jl_review,
                        "jl_rejection": jl_rejection,
                        "jl_remark": jl_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {"jl_review": jl_review, "jl_remark": jl_remark}
        else:
            return {"code": -1, "msg": "请选择审核结果"}

        db = mysql.new_mysql_instance("tbconstruct")
        project_name = self.ctx.variables.get("project_name")
        problem_list = self.ctx.variables.get("problem_list")
        if problem_list:
            major = problem_list[0].get("major")
            xg_list = []
            xg_sql = (
                    "SELECT account,service_provider FROM project_role_account"
                    " WHERE project_name='%s' and role = '项管-项目经理' and del_flag = '0'"
                    % project_name
            )
            is_hire = self.ctx.variables.get("is_hire", "")
            if is_hire == "1":
                xg_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '合建方-项目经理' and del_flag = '0'"
                        % project_name
                )
            xg_boss_sql = db.get_all(xg_sql)
            if xg_boss_sql:
                xg_account = xg_boss_sql[0].get("account")
                xg_list.append(xg_account)
            if major:
                sql1 = (
                        "SELECT role,account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' AND role LIKE '%%%s%%' and del_flag = '0'"
                        % (project_name, major)
                )
                account_list = db.get_all(sql1)
                if account_list:
                    for i in account_list:
                        if "项管" in i.get("role"):
                            xg_list.append(i.get("account"))
                        if "合建方" in i.get("role"):
                            xg_list.append(i.get("account"))
            variables.update({"xg_list": xg_list})
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectManagementReview(AjaxTodoBase):
    """
    项管审核
    """

    def __init__(self):
        super(ProjectManagementReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        xg_remark = process_data.get("xg_remark")
        xg_review = process_data.get("xg_review")
        xg_rejection = process_data.get("xg_rejection")
        if xg_review:
            if xg_review == "驳回":
                if xg_rejection:
                    variables = {
                        "xg_review": xg_review,
                        "xg_rejection": xg_rejection,
                        "xg_remark": xg_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {"xg_review": xg_review, "xg_remark": xg_remark}
        else:
            return {"code": -1, "msg": "请选择审核结果"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DataCallback(object):
    """
    数据回传给测试方
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def deliver_data(self):
        process_user = self.ctx.variables.get("process_user")
        question_ticket = self.ctx.variables.get("question_ticket")
        fix_photo_response = self.ctx.variables.get("fix_photo_response")
        solve_type = self.ctx.variables.get("solve_type")
        causes_and_solutions = self.ctx.variables.get("causes_and_solutions")
        responsible_unit = self.ctx.variables.get("responsible_unit")
        remark = self.ctx.variables.get("remark")
        problem_type = self.ctx.variables.get("problem_type")
        product_owner = self.ctx.variables.get("product_owner")
        # ticket_id = self.ctx.variables.get("ticket_id")
        # project_name = self.ctx.variables.get("project_name")
        is_or_not = self.ctx.variables.get("is_or_not")
        # whether_total_liability = self.ctx.variables.get("whether_total_liability")

        # if is_or_not == '是':
        #     if whether_total_liability == 2:
        #         is_or_not = '否'
        #     elif whether_total_liability == 1:
        #         is_or_not = '是'
        #     if whether_total_liability == 1:
        #         db = mysql.new_mysql_instance("tbconstruct")
        #         sql = f"""
        #                 SELECT question_ticket,fix_photo_url,causes_and_solutions,
        #                 corrector,solve_type,responsible_unit,remark,
        #                 problem_type,product_owner
        #                 FROM problem_rectification
        #                 WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'
        #             """
        #         result_list = db.get_all(sql)
        #         problem_data = db.get_dictionary(
        #             table="problem_rectification",
        #             conditions={"ticket_id": ticket_id, "project_name": project_name},
        #         )
        #         if not problem_data:
        #             return {"code": -1, "msg": "未找到问题数据"}
        #         process_user = problem_data["corrector"]
        #         if process_user is not None and process_user != '':
        #             accout = db.get_dictionary(
        #                 table="project_role_account",
        #                 fields=["account"],
        #                 conditions={"name": process_user, "project_name": project_name},
        #             )
        #             if accout and accout["account"] != '':
        #                 process_user = accout["account"]
        #         question_ticket = problem_data["question_ticket"]
        #         # if not fix_photo_response:
        #         #     for row in result_list:
        #         #         fix_photo_response = row.get("fix_photo_url")
        #         fix_photo_response = transform_url_list(problem_data["fix_photo_url"])
        #         solve_type = problem_data["solve_type"]
        #         causes_and_solutions = problem_data["causes_and_solutions"]
        #         responsible_unit = problem_data["responsible_unit"]
        #
        #         remark = problem_data["remark"]
        #         problem_type = problem_data["problem_type"]
        #         product_owner = problem_data["product_owner"]
        fix_photo_url = self.ctx.variables.get("fix_photo_url")
        if not fix_photo_response:
            fix_photo_response = transform_url_list(fix_photo_url)

        if not process_user:
            corrector = self.ctx.variables.get("corrector")
            responsible = self.ctx.variables.get("responsible")
            if responsible:
                process_user = responsible
            elif corrector:
                process_user = corrector



        info = gnetops.request(
            action="Verificationrectificationofresponsibleunit",
            method="QuestionSubmitCheckItem",
            ext_data={
                "CauseSolutionProcess": causes_and_solutions,
                "NeatenPhoto": fix_photo_response,
                "ProcessUser": process_user,
                "SolveType": solve_type,
                "TicketId": question_ticket,
                "ResponsibleUnit": responsible_unit,
                "ProblemNote": remark,
                "ProblemType": problem_type,
                "ProductOwner": product_owner,
            },
            scheme="ifob-fre"
        )

        self.workflow_detail.add_kv_table("回传数据", {"data": info})
        variables = {"test_info": info, "is_or_not": is_or_not}
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitingReviewResults(object):
    """
    等待测试平台审核结果
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def audit_results(self):
        pass


class StoreProblemRectificationData(object):
    """
    审核通过存储数据到数据库
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def storing_data(self):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        insert_data = self.ctx.variables.get("insert_data")
        reason_rejection = self.ctx.variables.get("reason_rejection")
        remark = self.ctx.variables.get("remark")
        jl_rejection = self.ctx.variables.get("jl_rejection")
        xg_rejection = self.ctx.variables.get("xg_rejection")
        problem_type = self.ctx.variables.get("problem_type")
        jl_remark = self.ctx.variables.get("jl_remark")
        xg_remark = self.ctx.variables.get("xg_remark")
        product_owner = self.ctx.variables.get("product_owner")

        insert_data["problem_type"] = problem_type
        insert_data["reason_rejection"] = reason_rejection
        insert_data["remark"] = remark
        insert_data["jl_rejection"] = jl_rejection
        insert_data["xg_rejection"] = xg_rejection
        insert_data["jl_remark"] = jl_remark
        insert_data["xg_remark"] = xg_remark
        insert_data["product_owner"] = product_owner
        tb_db = mysql.new_mysql_instance("tbconstruct")
        cond = {
            "ticket_id": ticket_id,
            "project_name": project_name,
        }
        # 存数据库
        tb_db.begin()
        tb_db.update("problem_rectification", insert_data, cond)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id)

# if __name__ == "__main__":
#     url = '["https://test.otob.dcops.qq.com/relay/cosfile/cooperative_files/101T方仓A列热通道综合配电柜侧门内部线缆与柜体之间无防护.jpg"]'
#     print(transform_url_list(url))
