from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class WeworkDeptApi(object):
    # 查询IDC项目建设组织架构
    @staticmethod
    def query_dept():
        res = gnetops.request(
            action="Human",
            method="QueryWeworkAllDept",
            data={
                "company": "犀牛鸟有限责任公司",
            },
        )
        dept_list = WeworkDeptApi.dept_filter(
            res, "犀牛鸟有限责任公司/IDC项目建设", "IDC项目建设"
        )
        return dept_list

    # 过滤
    @staticmethod
    def dept_filter(data, path, target):
        for item in data:
            if item.get("label") == target:
                return item.get("children")
            if item.get("label") in path:
                return WeworkDeptApi.dept_filter(item.get("children"), path, target)
