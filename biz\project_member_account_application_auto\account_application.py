from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql
from biz.project_member_account_application_auto.account_application_api import (
    AutoAccountApplicationApi,
)


# 自建账号申请
class AutoAccountApplication(AjaxTodoBase):
    def __init__(self):
        super(AutoAccountApplication, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("add_member_list")
        if not member_list:
            return {"code": -1, "msg": "请添加项目成员科技账号申请信息！"}
        application_remark = process_data.get("application_remark")
        # 判断是否有重复开通的账号
        account_repeat_list = []
        # 校验是否存在【同手机号不同姓名】的账号
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in member_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    append_flag = False
                    for account in result:
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        name = info.get("name")
                        phone = info.get("phone")
                        application_account = info.get("application_account")
                        project_name = info.get("campus_name") + info.get(
                            "project_name"
                        )
                        role = info.get("role")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_project_name = account.get("project_name")
                        exist_role = account.get("role")
                        exist_account = account.get("account")
                        if (
                            exist_project_name == project_name
                            and exist_name == name
                            and exist_role == role
                        ):
                            continue
                        if (
                            "集成商" in role or "项管" in role or "监理" in role
                        ) and project_name not in account_repeat_list:
                            # 判断是否已过初验
                            # 强校验：项目已初验就可以重复开通，未初验填了备注也不可重复开通
                            flag = AutoAccountApplicationApi.project_is_initial_inspection_flag(
                                exist_project_name
                            )
                            if not flag:
                                account_repeat_list.append(
                                    f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                                )
                                # 【已开通账号】为在建项目【项目名称】的【角色】，
                                # account_repeat_list.append(
                                #     f"【{exist_name} {exist_account}】为在建项目【{exist_project_name}】的【{exist_role}】"
                                # )
                        if name != exist_name:
                            # 新申请的展示在已存在的前面
                            if not append_flag:
                                account_validate_list.append(
                                    f"{name} {project_name} {role} {phone} {application_account}"
                                )
                            append_flag = True
                            account_validate_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        if account_repeat_list:
            tips = ("<br>").join(account_repeat_list)
            # 强校验：项目已初验就可以重复开通，未初验填了备注也不可重复开通
            # if not application_remark:
            return {
                "code": -1,
                # "msg": f"存在【已开通账号】，请在备注填写使用理由！<br>{tips}",
                "msg": f"存在【已开通账号】人员所在项目未完成初验，请联系服务商更换人员！<br>{tips}",
            }
        if account_validate_list:
            tips = ("<br>").join(account_validate_list)
            return {
                "code": -1,
                "msg": "【请核对】姓名电话是否对应！"
                # "<br>若是已开通的账号信息有误请先删除已开通的账号信息再继续开通；"
                # "若是本次开通的账号信息有误请先修改正确再继续开通。"
                f"<br>{tips}",
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "add_member_list": member_list,
            "application_remark": application_remark,
            "leader_approval": 1,
            "leader_remark": "",
            "leader_reject_reason": "",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 合建账号申请
class AutoAccountApplicationJoint(AjaxTodoBase):
    def __init__(self):
        super(AutoAccountApplicationJoint, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("add_member_list")
        if not member_list:
            return {"code": -1, "msg": "请添加项目成员科技账号申请信息！"}
        application_remark = process_data.get("application_remark")
        # 判断是否有重复开通的账号 （有的话“application_remark”为必填）
        account_repeat_list = []
        # 校验是否存在【同手机号不同姓名】的账号
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in member_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    append_flag = False
                    for account in result:
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        name = info.get("name")
                        phone = info.get("phone")
                        application_account = info.get("application_account")
                        project_name = info.get("campus_name") + info.get(
                            "project_name"
                        )
                        role = info.get("role")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_project_name = account.get("project_name")
                        exist_role = account.get("role")
                        exist_account = account.get("account")
                        if (
                            exist_project_name == project_name
                            and exist_name == name
                            and exist_role == role
                        ):
                            continue
                        if (
                            "总包" in role or "合建方" in role or "监理" in role
                        ) and project_name not in account_repeat_list:
                            account_repeat_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        if name != exist_name:
                            # 新申请的展示在已存在的前面
                            if not append_flag:
                                account_validate_list.append(
                                    f"{name} {project_name} {role} {phone} {application_account}"
                                )
                            append_flag = True
                            account_validate_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        if account_repeat_list:
            tips = ("<br>").join(account_repeat_list)
            if not application_remark:
                return {
                    "code": -1,
                    "msg": f"存在【已开通账号】，请在备注填写使用理由！<br>{tips}",
                }
        if account_validate_list:
            tips = ("<br>").join(account_validate_list)
            return {
                "code": -1,
                "msg": "【请核对】姓名电话是否对应！"
                # "<br>若是已开通的账号信息有误请先删除已开通的账号信息再继续开通；"
                # "若是本次开通的账号信息有误请先修改正确再继续开通。"
                f"<br>{tips}",
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "add_member_list": member_list,
            "application_remark": application_remark,
            "leader_approval": 1,
            "leader_remark": "",
            "leader_reject_reason": "",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号申请审批
class AutoAccountApplicationApproval(AjaxTodoBase):
    def __init__(self):
        super(AutoAccountApplicationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval", 1)
        leader_remark = process_data.get("leader_remark", "")
        leader_reject_reason = ""
        if not leader_approval:
            leader_reject_reason = leader_remark
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "leader_approval": leader_approval,
            "leader_remark": leader_remark,
            "leader_reject_reason": leader_reject_reason,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号申请完成信息确认
class AutoAccountInfoConfirm(AjaxTodoBase):
    def __init__(self):
        super(AutoAccountInfoConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        create_wework_account_flow_flag = self.ctx.variables.get(
            "create_wework_account_flow_flag"
        )
        if create_wework_account_flow_flag == 1:
            member_list = process_data.get("add_member_list")
            for item in member_list:
                if item.get("ticket_id"):
                    if (
                        not item.get("ticket_status")
                        or item.get("ticket_status") != "已结单"
                    ):
                        return {
                            "code": -1,
                            "msg": "请等待企微账号创建流程工单全部结单后再提交",
                        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id)
