import json

from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops

from biz.project_interface.maintenance_question_display import MaintenanceQuestionDisplay


class BasicInformationQuery(object):
    """
        项目信息、项目团队、项目外部接口人
    """

    def people_and_information(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        # 项目信息sql查询
        project_information_sql = "SELECT campus,project,region,province,demand_distribution,demand_delivery," \
                                  "estimation_period,construction_mode,item_number,number_of_rack," \
                                  "stand_alone_work,rack_capacity,PM,deputy_PM " \
                                  "FROM risk_early_warning_data " \
                                  f"WHERE project_name = '{project_name}'"
        # 人员信息sql查询
        personal_information_sql = "SELECT architecture,beautification,tree_sutra,weak_current," \
                                   "business,supply_chain,inform_a_person " \
                                   "FROM project_team_information_data " \
                                   f"WHERE project_name = '{project_name}'"
        # 项目信息查询
        summary_data_sql = "SELECT building_name,building_standards,framework " \
                           "FROM summary_data " \
                           f"WHERE CONCAT(campus, project_name) = '{project_name}'"
        # 项目信息数据
        project_information_result = db.get_all(project_information_sql)
        # 人员信息数据
        personal_information_result = db.get_all(personal_information_sql)
        # 项目信息查询
        summary_data_result = db.get_all(summary_data_sql)
        data = []
        if project_information_result:
            for row1 in project_information_result:
                temp_dict = {
                    'campus': row1.get('campus'),
                    'project': row1.get('project'),
                    'region': row1.get('region'),
                    'province': row1.get('province'),
                    'demand_distribution': row1.get('demand_distribution'),
                    'demand_delivery': row1.get('demand_delivery'),
                    'estimation_period': row1.get('estimation_period'),
                    'construction_mode': row1.get('construction_mode'),
                    'item_number': row1.get('item_number'),
                    'number_of_rack': row1.get('number_of_rack'),
                    'stand_alone_work': row1.get('stand_alone_work'),
                    'rack_capacity': row1.get('rack_capacity'),
                    'PM': row1.get('PM'),
                    'deputy_PM': row1.get('deputy_PM'),
                }
                if personal_information_result:
                    for row2 in personal_information_result:
                        temp_dict.update({
                            'architecture': row2.get('architecture'),
                            'beautification': row2.get('beautification'),
                            'tree_sutra': row2.get('tree_sutra'),
                            'weak_current': row2.get('weak_current'),
                            'business': row2.get('business'),
                            'supply_chain': row2.get('supply_chain'),
                            'inform_a_person': row2.get('inform_a_person'),
                        })
                if summary_data_result:
                    for row3 in summary_data_result:
                        temp_dict.update({
                            'building_name': row3.get('building_name'),
                            'building_standards': row3.get('building_standards'),
                            'framework': row3.get('framework'),
                        })
                data.append(temp_dict)
        return data


"""
    交付验收接口
"""


def process_site_inspection(data, project_name):
    """处理现场查验"""
    dimension_problem = MaintenanceQuestionDisplay()
    rate_of_completion = dimension_problem.test_overview(project_name)
    completion_rate = rate_of_completion.get('completion_rate', '0%')

    try:
        completion_rate_numeric = float(completion_rate.replace('%', '')) / 100
        completion_rate_numeric = round(completion_rate_numeric, 2)
        performance = completion_rate_numeric == 1
    except (ValueError, AttributeError):
        completion_rate_numeric = 0
        performance = False

    data.append({
        "performance": performance,
        "backlog": "现场查验",
        "schedule": completion_rate_numeric,
        "planned_completion_time": '',
        "actual_finish_time": '',
        "state": '',
        "work_order_number": ''
    })


def get_ticket_id(db, project_name, work_content):
    """获取指定工作内容的工单ID"""
    sql = """
        SELECT ticket_id 
        FROM construction_application_second_level 
        WHERE project_name = %s AND work_content = %s
    """
    result = db.get_all(sql, (project_name, work_content))
    return result[0]['ticket_id'] if result else ''


def query_construct_plan(db, project_name):
    """查询建设计划数据"""
    sql = """
        SELECT serial_number, work_content, week_progress, start_time, actual_finish_time, state
        FROM construct_plan_data 
        WHERE project_name = %s
    """
    return db.get_all(sql, (project_name,))


def process_base_items(data, zb_result, bj_ticket_id, sd_ticket_id):
    """处理基础验收项（报建、送电等）"""
    # 定义基础验收项配置
    base_items = [
        {"serial": "2.4", "work": "项目报建（含消防）", "backlog": "报建", "ticket": bj_ticket_id},
        {"serial": "2.9.7", "work": "送电", "backlog": "送电", "ticket": sd_ticket_id},
        {"serial": "2.5", "work": "集采设备生产到货", "backlog": "甲供设备到货", "ticket": ""},
        {"serial": "3", "work": "安装施工", "backlog": "安装施工", "ticket": ""},
        {"serial": "4", "work": "测试验证", "backlog": "第三方测试", "ticket": ""}
    ]
    if not zb_result:
        # 无数据时添加默认项
        for item in base_items:
            data.append(create_default_item(item["backlog"], item["ticket"]))
        return
    # 处理查询到的数据
    item_mapping = {f"{item['serial']}_{item['work']}": item for item in base_items}
    for row in zb_result:
        key = f"{row['serial_number']}_{row['work_content']}"
        if key in item_mapping:
            item = item_mapping[key]
            ticket_id = row.get('ticket_id', item["ticket"])
            data.append(create_item_from_row(row, item["backlog"], ticket_id))


def create_default_item(backlog, ticket_id):
    """创建默认项"""
    return {
        "performance": False,
        "backlog": backlog,
        "schedule": 0,
        "planned_completion_time": '',
        "actual_finish_time": '',
        "state": '',
        "work_order_number": ticket_id
    }


def create_item_from_row(row, backlog, ticket_id):
    """从查询结果创建项"""
    # 获取原始进度值，默认为0
    week_progress = row.get("week_progress", 0)

    # 处理进度值：除以100并保留两位小数
    progress_percentage = round(float(week_progress) / 100, 2)
    return {
        "performance": progress_percentage == 1.0,
        "backlog": backlog,
        "schedule": progress_percentage,
        "planned_completion_time": row["start_time"],
        "actual_finish_time": row["actual_finish_time"],
        "state": row["state"],
        "work_order_number": ticket_id
    }


def process_training_and_materials(data, db, project_name, zb_result):
    """处理培训和物资移交"""
    # 获取培训工单ID
    px_ticket_id = get_ticket_id_from_table(db, project_name, "maintenance_training")
    # 获取物资移交工单ID
    wz_ticket_id = get_ticket_id_from_table(db, project_name, "maintenance_material_acceptance")

    # 处理培训项
    px_performance, px_schedule = check_workflow_status(px_ticket_id, "培训材料上传")
    px_item = find_item_by_criteria(zb_result, "5.1.3", "培训")
    if px_item:
        data.append({
            "performance": px_performance,
            "backlog": "培训",
            "schedule": px_schedule,
            "planned_completion_time": px_item["start_time"],
            "actual_finish_time": px_item["actual_finish_time"],
            "state": px_item["state"],
            "work_order_number": px_ticket_id
        })
    elif not zb_result:
        data.append(create_default_item("培训", px_ticket_id))

    # 处理物资移交项
    wz_performance, wz_schedule = check_workflow_status(wz_ticket_id, "物资信息上传")
    wz_item = find_item_by_criteria(zb_result, "5.1.1", "资料审核移交")
    if wz_item:
        data.append({
            "performance": wz_performance,
            "backlog": "物资移交",
            "schedule": wz_schedule,
            "planned_completion_time": wz_item["start_time"],
            "actual_finish_time": wz_item["actual_finish_time"],
            "state": wz_item["state"],
            "work_order_number": wz_ticket_id
        })
    elif not zb_result:
        data.append(create_default_item("物资移交", wz_ticket_id))


def get_ticket_id_from_table(db, project_name, table_name):
    """从指定表获取工单ID"""
    sql = f"SELECT ticket_id FROM {table_name} WHERE project_name = %s"
    result = db.get_all(sql, (project_name,))
    return result[0]['ticket_id'] if result else ''


def check_workflow_status(ticket_id, task_name):
    """检查工作流状态"""
    if not ticket_id:
        return False, 0

    query_data = {
        "SchemaId": "ticket_base",
        "Data": {
            "ResultColumns": {"TicketStatus": "", "TicketId": "", "InstanceId": ""},
            "SearchCondition": {"TicketId": [str(ticket_id)]}
        }
    }
    result = gnetops.request(action="QueryData", method="Run", ext_data=query_data)
    instance_id = result.get("List", [{}])[0].get('InstanceId')

    if not instance_id:
        return False, 0

    workflow_data = {
        "IgnoreDetail": True,
        "InstanceId": instance_id,
        "IsFetchChild": True
    }
    workflow_log = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=workflow_data)

    for log in workflow_log:
        if log.get('TaskName') == task_name and log.get('TaskStatus') == '已完成':
            return True, 1
    return False, 0


def find_item_by_criteria(zb_result, serial, work_content):
    """根据条件查找项目"""
    if not zb_result:
        return None
    for item in zb_result:
        if item["serial_number"] == serial and item["work_content"] == work_content:
            return item
    return None


class ProjectDeliveryAcceptanceItem(object):
    """
        项目交付验收项
    """

    def check_completion_of_items(self, project_name):
        """
        验收项完成情况检查
        """
        data = []
        db = mysql.new_mysql_instance("tbconstruct")

        # 获取工单ID
        bj_ticket_id = get_ticket_id(db, project_name, "项目报建（含消防）")
        sd_ticket_id = get_ticket_id(db, project_name, "市电专项")

        # 查询建设计划数据
        zb_result = query_construct_plan(db, project_name)

        # 处理基础验收项
        process_base_items(data, zb_result, bj_ticket_id, sd_ticket_id)

        # 处理培训和物资移交
        process_training_and_materials(data, db, project_name, zb_result)

        # 处理现场查验
        process_site_inspection(data, project_name)

        return data

    def time_change_flow_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT demand_delivery_old,demand_delivery_new,created_time,"
               "creator,change_reason,submit_file_urls,pm_remark,om_remark "
               "FROM risk_early_warning_data_log WHERE project_name='%s'" % project_name)
        result = db.get_all(sql)
        data = []
        if result:
            for row in result:
                creator = row.get('creator', "")
                change_reason = row.get('change_reason', "")
                demand_delivery_old = row.get('demand_delivery_old', "")
                demand_delivery_new = row.get('demand_delivery_new', "")
                created_time = row.get('created_time', "")
                submit_file_urls = row.get('submit_file_urls', "")
                pm_remark = row.get('pm_remark', "无")
                om_remark = row.get('om_remark', "无")
                if created_time is not None:
                    created_time = created_time.strftime("%Y-%m-%d")
                url_list = []
                if submit_file_urls and submit_file_urls != "None" and submit_file_urls != "[]":
                    url_info = json.loads(submit_file_urls)
                    if isinstance(url_info, list) and url_info:
                        for item in url_info:
                            file_name = item.get("name")
                            url = item.get("response", {}).get("FileList", [])
                            if url:
                                file_url = url[0].get("url")
                                url_list.append({
                                    "fileName": file_name,
                                    "fileUrl": file_url
                                })
                data.append({
                    "creator": creator,
                    "change_reason": change_reason,
                    "demand_delivery_old": demand_delivery_old,
                    "demand_delivery_new": demand_delivery_new,
                    "created_time": created_time,
                    "submit_file_urls": url_list,
                    "pm_remark": pm_remark,
                    "om_remark": om_remark
                })
        return data
