class Tools(object):
    # 校验今日施工情况表格
    @staticmethod
    def validate_today_construction(data, option):
        for item in data:
            zone = item.get("zone")
            sub_item = item.get("sub_item")
            specific_content = item.get("specific_content")
            person_num = item.get("zone")
            progress = item.get("zone")
            if not zone:
                return False, f"施工区域不能为空"
            if zone not in option:
                return False, f"{zone}, 请选择正确的施工区域"
            if option.get(zone):
                if not sub_item:
                    return False, f"{zone}, 施工分项不能为空"
                if sub_item not in option.get(zone):
                    return False, f"{zone}, 请选择正确的施工分项"
                if option.get(zone).get(sub_item):
                    if not specific_content:
                        return False, f"{zone} {sub_item}, 具体内容不能为空"
                    if specific_content not in option.get(zone).get(sub_item):
                        return False, f"{zone}, 请选择正确的施工具体内容"
            if not person_num:
                return False, f"施工人数不能为空"
            if not progress:
                return False, f"进度状况不能为空"
        return True, ""
