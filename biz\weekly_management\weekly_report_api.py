import ast
from iBroker.lib import mysql


class WeeklyReportApi(object):
    """
    周报数据查询
    """

    def weekly_calendar_data(self, project_name):
        """
        周报日历组件展示数据（周报、日报）
        :param project_name: 项目名称
        :return 周报日历组件展示数据
        """
        db = mysql.new_mysql_instance("tbconstruct")
        new_weekly_report_sql = (
            "SELECT now_time, file_url FROM weekly_report_brief "
            f"WHERE project_name = '{project_name}' "
        )
        new_daily_report_sql = (
            "SELECT DATE_FORMAT(report_date, '%Y-%m-%d') AS report_date FROM project_daily_report "
            f"WHERE project_name = '{project_name}' "
        )
        weekly_result = db.query(new_weekly_report_sql)
        new_daily_report_result = db.query(new_daily_report_sql)
        data = []
        for item in weekly_result:
            date = item.get("now_time")
            url = item.get("file_url")
            data.append(
                {
                    "date": date,
                    "type": "weekly_newspaper",
                    "weekly_newspaper_name": f"{date}{project_name}建设周报",
                    "weekly_newspaper_url": url,
                }
            )
        for item in new_daily_report_result:
            date = item.get("report_date")
            data.append(
                {
                    "date": date,
                    "type": "daily_newspaper",
                    "weekly_newspaper_name": f"{date}{project_name}建设日报",
                    "weekly_newspaper_url": "",
                }
            )
        return data

    def weekly_report_upload_date(self, project_name):
        """
        周报上传星期X、最新周报上传日期
        :param
            project_name: 项目名称
        :return
            {
                latestWeeklyDate: 周报上传星期X
                weeklyReportUploadTime: 最新周报上传日期
            }
        """
        db = mysql.new_mysql_instance("tbconstruct")
        week_day_sql = f"SELECT week_date FROM week_create_info WHERE project_name = '{project_name}'"
        last_upload_sql = (
            "SELECT Max(now_time) as now_time FROM weekly_report_brief "
            f"WHERE project_name = '{project_name}'"
        )
        week_day_result = db.get_row(week_day_sql)
        last_upload_result = db.get_row(last_upload_sql)
        data = {
            "latestWeeklyDate": (
                last_upload_result.get("now_time", "") if last_upload_result else ""
            ),
            "weeklyReportUploadTime": (
                week_day_result.get("week_date", "") if week_day_result else ""
            ),
        }
        return data

    def weekly_report_preview(self, project_name, date):
        """
        周报预览
        :param
            project_name: 项目名称
            date: 周报日期
        :return
            {
                briefingData: 周报简报
                picturesData: 周报图片
                progressLog: 进度日志
            }
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = (
            "SELECT wrb.*, cpdl.operate_log FROM weekly_report_brief wrb "
            "LEFT JOIN construct_plan_data_log cpdl ON cpdl.ticket_id = wrb.ticket_id "
            "WHERE wrb.id = "
            f"(SELECT Max(id) FROM weekly_report_brief WHERE project_name = '{project_name}' AND now_time = '{date}')"
        )
        result = db.get_row(query_sql)
        pictures_submit = (
            ast.literal_eval(result.get("pictures_submit"))
            if result and result.get("pictures_submit")
            else []
        )
        pictures_data = []
        if pictures_submit:
            for item in pictures_submit:
                if item:
                    url = ""
                    try:
                        url = item.get("picture_url")[0]["response"]["FileList"][0]["url"]
                    except KeyError:
                        pass
                    except IndexError:
                        pass
                    except TypeError:
                        pass
                    finally:
                        pictures_data.append(
                            {
                                "picture_title": item.get("picture_title", ""),
                                "picture_url": url,
                            }
                        )
        operate_log = result.get("operate_log") if result else ""
        data = {
            "briefingData": result,
            "picturesData": pictures_data,
            "progressLog": operate_log,
        }
        return data

    def latest_weekly_daily_report(self, project_name):
        """
        最新周报、日报工单
        """
        db = mysql.new_mysql_instance("tbconstruct")
        weekly_sql = (
            "SELECT TicketId FROM all_construction_ticket_data "
            "WHERE ProcessDefinitionKey in ('project_construct_week', 'weekly_report_process') "
            f"AND Title LIKE '%{project_name}%' "
            "AND IsForceEnd = 0 "
            "ORDER BY id DESC LIMIT 1"
        )
        daily_sql = (
            "SELECT TicketId FROM all_construction_ticket_data "
            "WHERE ProcessDefinitionKey in ('construction_project_daily_report') "
            f"AND Title LIKE '%{project_name}%' "
            "AND IsForceEnd = 0 "
            "ORDER BY id DESC LIMIT 1"
        )
        weekly_result = db.query(weekly_sql)
        daily_result = db.query(daily_sql)
        data = {
            "weekly_report_ticket_id": (
                weekly_result[0].get("TicketId") if weekly_result else ""
            ),
            "daily_report_ticket_id": (
                daily_result[0].get("TicketId") if daily_result else ""
            ),
        }
        return data
