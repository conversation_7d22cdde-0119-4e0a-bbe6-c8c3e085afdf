import json
import re
from datetime import datetime
import datetime
import uuid

import pandas as pd
from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from dateutil import parser

from biz.construction_process.cos_lib import COSLib
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class ProjectHumanResourceManagement(AjaxTodoBase):
    def __init__(self):
        super(ProjectHumanResourceManagement, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def check_whether_manpower_file_uploaded(self, human_resource_document: list):
        try:
            data, bool = human_resource_document[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            data, bool = {"code": -1, "msg": "人力资源文件未上传完成，请等待文件上传完成后再做结单处理"}, True
        except IndexError:
            data, bool = {"code": -1, "msg": "人力资源文件未上传完成，请等待文件上传完成后再做结单处理"}, True
        return data, bool

    def end(self, process_data, process_user):
        """
            解析存储文件url以及文件数据
        """
        # 获取文件
        global url_data
        human_resource_document = process_data.get('human_resource_document')
        ticket_id = self.ctx.variables.get("ticket_id")
        now_time = self.ctx.variables.get("now_time")
        project_name = self.ctx.variables.get("project_name")
        url = human_resource_document[0]["response"]["FileList"][0]["url"]
        if human_resource_document and len(human_resource_document) > 0 and 'response' in human_resource_document[0]:
            url_data = {
                'project_name': project_name,
                'ticket_id': ticket_id,
                'human_resource_document': "url：" + url + '；' + "now_time：" + str(now_time)
            }

        # 校验文件是否上传成功
        data, bool = self.check_whether_manpower_file_uploaded(human_resource_document)
        if bool:
            return data
        # 下载并读取 Excel 文件
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath)
        df = df.replace({pd.np.nan: None})

        # 获取表头
        headers = df.columns.tolist()

        # 定义空字典用于存储解析后的数据
        data_one = {}
        data_two = {}
        # 处理每一行的数据
        for _, row in df.iterrows():
            # 获取序号和工作内容
            serial_number = row["序号"]  # 将序号转换为字符串类型
            job_content = row["工作内容"]

            # 遍历每一列的数据
            for header, data in zip(headers[2:], row[2:]):
                if header is None:
                    continue
                if isinstance(header, str):
                    try:
                        header = datetime.datetime.strptime(header, '%Y-%m-%d %H:%M:%S')  # 尝试解析为日期时间对象
                    except ValueError:
                        continue  # 解析失败，跳过该列的处理

                date_time = header.strftime('%Y-%m-%d')  # 将 header 格式化为字符串形式的日期

                number_people = None
                if pd.notnull(row[header]):
                    # 确保 row[header] 是字符串类型
                    data = str(row[header]).strip()  # 去除空格或其他不可见字符
                    if data != '\u3000' and data != '':  # 排除'\u3000'和空字符串的情况
                        # 使用正则表达式检查是否是有效的数字格式
                        if re.match(r'^-?\d+(\.\d+)?$', data):  # 匹配整数或小数
                            if '.' in data and data != '.':  # 检查是否包含小数点且不只是一个点号
                                number_people = int(float(data))  # 将浮点数转换为整数
                            else:
                                number_people = int(data)
                # 如果人数不为空值且序号有效，则将数据添加到字典中
                if number_people is not None and serial_number is not None and serial_number != '\u3000':
                    if date_time not in data_one:
                        data_one[date_time] = []
                    data_one[date_time].append({
                        "serial_number": serial_number,
                        "job_content": job_content,
                        "date_time": date_time,
                        "number_people": number_people,
                        "project_name": project_name,
                        "ticket_id": ticket_id
                    })
                if number_people is not None and serial_number is None or serial_number == '\u3000':
                    if date_time not in data_two:
                        data_two[date_time] = []
                    data_two[date_time].append({
                        "job_content": job_content,
                        "date_time": date_time,
                        "number_people": number_people,
                        "project_name": project_name,
                        "ticket_id": ticket_id
                    })
        # 将数据按日期排序
        sorted_data_one = sorted(data_one.items())
        sorted_data_two = sorted(data_two.items())

        variables = {
            'url_data': url_data,
            'human_resource_document': human_resource_document,
            'sorted_data_one': sorted_data_one,
            'sorted_data_two': sorted_data_two
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ManpowerDataStorage(object):
    """
        数据存库
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_repository(self):
        sorted_data_one = self.ctx.variables.get("sorted_data_one")
        sorted_data_two = self.ctx.variables.get("sorted_data_two")
        url_data = self.ctx.variables.get("url_data")
        project_name = self.ctx.variables.get("project_name")

        project_manpower_one_data = []
        project_manpower_two_data = []

        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")

        # 查询数据库中已存在的记录
        project_manpower_one_sql = "SELECT serial_number, job_content, date_time, number_people, project_name " \
                                   "FROM project_manpower_one " \
                                   f"WHERE project_name='{project_name}'"
        project_manpower_two_sql = "SELECT job_content, date_time, number_people, project_name " \
                                   "FROM project_manpower_two " \
                                   f"WHERE project_name='{project_name}'"
        result_one = tb_db.get_all(project_manpower_one_sql)
        result_two = tb_db.get_all(project_manpower_two_sql)

        # 遍历排序后的数据
        if sorted_data_one is not None:
            for date_one, items in sorted_data_one:
                for item in items:
                    exists = False
                    for record in result_one:
                        db_date_time = record['date_time'].strftime('%Y-%m-%d')
                        # 使用直接比较
                        if project_name == record.get('project_name', ''):
                            if (str(item['serial_number']), item['job_content'], item['date_time'],
                                item['number_people']) == \
                                    (record['serial_number'], record['job_content'], db_date_time,
                                     record['number_people']):
                                exists = True
                                break
                    if not exists:
                        project_manpower_one_data.append(item)
        # 遍历排序后的数据
        if sorted_data_two is not None:
            for date_two, items in sorted_data_two:
                for item in items:
                    exists = False
                    for record in result_two:
                        db_date_time = record['date_time'].strftime('%Y-%m-%d')
                        if (item['job_content'], item['date_time'], item['number_people']) == \
                                (record['job_content'], record['date_time'], db_date_time):
                            exists = True
                            break
                    if not exists:
                        project_manpower_two_data.append(item)
        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if url_data:
            tb_db.insert("project_manpower_url", url_data)
        if project_manpower_one_data:
            tb_db.insert_batch("project_manpower_one", project_manpower_one_data)
        if project_manpower_two_data:
            tb_db.insert_batch("project_manpower_two", project_manpower_two_data)
        tb_db.commit()
        variables = {
            'project_manpower_one_data': project_manpower_one_data,
            'project_manpower_two_data': project_manpower_two_data
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectHumanResourceAutomaticallyPlaceOrders(object):
    """
        项目人力日报自动起单
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def order_logic(self):
        global project_name, item_tube
        project_table = config.get_config_map(
            "project_manpower_shutdown"
        )
        db = mysql.new_mysql_instance("tbconstruct")
        res_list = []
        project_name_sql = "SELECT DISTINCT project_name FROM equipment_before_process"
        item_tube_sql = "SELECT DISTINCT pra.role, pra.account, pra.project_name " \
                        "FROM project_role_account pra " \
                        "JOIN equipment_before_process ebp " \
                        "ON pra.project_name = ebp.project_name " \
                        "WHERE pra.del_flag = 0 "
        item_tube_result = db.get_all(item_tube_sql)
        project_name_result = db.get_all(project_name_sql)
        project_name_to_item_tube = []  # 存储每个项目名称对应的项管

        for project in project_name_result:
            project_name = project['project_name']
            filtered_accounts = [row['account'] for row in item_tube_result if
                                 row['project_name'] == project_name and '项管-项目经理' in row['role']]
            if filtered_accounts:
                item_tube = filtered_accounts[0]  # 获取第一个项管
                project_name_to_item_tube.append({
                    'project_name': project_name,
                    'item_tube': item_tube,
                })
        for row in project_name_to_item_tube:
            project_name = row.get('project_name')
            item_tube = row.get('item_tube')

            # 屏蔽掉特定的 project_name
            if project_name in project_table:
                continue

            now = datetime.datetime.now()
            year = now.year
            month = now.month
            day = now.day
            date_string = f"{year}年{month}月{day}号{project_name}"
            processTitle = date_string + "项目人力文件上传"
            data = {
                "CustomVariables": {
                    "item_tube": item_tube,
                    'project_name': project_name
                },
                "ProcessDefinitionKey": "manpower_model_control",
                "Source": "",
                "TicketDescription": "项目人力文件上传流程",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan;v_mmywang",
                    "Creator": "keketan",
                    "Deal": "keketan"
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Ticket", method="Create", data=data)
            res_list.append(res)

        variables = {
            'res_list': res_list,
            'item_tube_result': item_tube_result,
            'project_name_result': project_name_result
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectManpowerInterface(object):
    """
        项目人力接口
    """

    def calendar_form_not_time(self, project_name):
        """
            日历表格查询--非时间列表格
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT serial_number, job_content, number_people " \
                    "FROM project_manpower_one " \
                    f"WHERE project_name = '{project_name}'"
        zkjh_sql = "SELECT serial_number, work_content " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')"
        result = db.get_all(query_sql)
        zkjh_result = db.get_all(zkjh_sql)
        data = []
        final_data = []

        accumulation_dict = {}

        for item in result:
            job_content = item["job_content"]
            number_people = item["number_people"]
            serial_number = item["serial_number"]

            key = (serial_number, job_content)  # Create a tuple key with both serial_number and job_content

            if key not in accumulation_dict:
                accumulation_dict[key] = number_people
            else:
                accumulation_dict[key] += number_people

        for (serial_number, job_content), number_people in accumulation_dict.items():
            data.append({
                "serial_number": serial_number,
                "job_content": job_content,
                "number_people": number_people
            })

        for item in zkjh_result:
            serial_number = item["serial_number"]
            work_content = item["work_content"]

            corresponding_data = next((d for d in data if d["serial_number"] == serial_number), None)

            final_entry = {
                "id": serial_number,
                "seqNum": serial_number,
                "workContent": work_content,
                "totalManpower": corresponding_data["number_people"] if corresponding_data else None
            }

            final_data.append(final_entry)

        return final_data

    def calendar_form_time(self, project_name):
        """
            日历表格查询--时间列表格
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT serial_number, date_time, number_people " \
                    "FROM project_manpower_one " \
                    f"WHERE project_name = '{project_name}'"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            serial_number = row.get("serial_number")
            date_time = row.get("date_time")
            number_people = row.get("number_people")

            date_time = date_time.strftime("%Y-%m-%d")

            data.append({
                'start': date_time,
                'title': number_people,
                'resourceId': serial_number,
            })

        return data

    def weekly_chart(self, project_name):
        """
            周统计图
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT job_content, date_time, number_people " \
                    "FROM project_manpower_two " \
                    f"WHERE project_name = '{project_name}'"
        result = db.get_all(query_sql)
        data = []
        xAxisData = []
        planManpower = []
        actualManpower = []

        if not result:
            # 处理没有找到数据的情况
            # 可以返回空列表或者抛出异常
            return {
                "xAxisData": xAxisData,
                "planManpower": planManpower,
                "actualManpower": actualManpower
            }

        # 从结果中获取数据并转换为日期对象
        for row in result:
            number_people = row.get("number_people")
            date_time = row.get("date_time")
            date_time = date_time.strftime("%Y-%m-%d")
            data.append({
                "date_time": date_time,
                "job_content": row.get("job_content"),
                "number_people": number_people
            })

        if not data:
            # 处理没有找到数据的情况
            # 可以返回空列表或者抛出异常
            return {
                "xAxisData": xAxisData,
                "planManpower": planManpower,
                "actualManpower": actualManpower
            }

        # 计算每周的数据
        start_date = datetime.datetime.strptime(data[0]["date_time"], "%Y-%m-%d")
        end_date = datetime.datetime.strptime(data[-1]["date_time"], "%Y-%m-%d")
        current_date = start_date
        week_number = 1
        while current_date <= end_date:
            week_start_date = current_date
            week_end_date = current_date + datetime.timedelta(days=6)
            week_range = f"{week_start_date.strftime('%Y-%m-%d')}~{week_end_date.strftime('%Y-%m-%d')}"
            weekly_actual_total = 0
            weekly_planned_total = 0
            days_count = 0
            for item in data:
                item_date = datetime.datetime.strptime(item["date_time"], "%Y-%m-%d")
                if week_start_date <= item_date <= week_end_date:
                    job_content = item["job_content"]
                    number_people = item["number_people"]
                    if job_content == "实际投入人员":
                        weekly_actual_total += number_people
                        days_count += 1
                    elif job_content == "计划投入人员":
                        weekly_planned_total += number_people
            xAxisData.append(f"第{week_number}周 ({week_range})")
            planManpower.append(weekly_planned_total)
            if days_count >= 7:
                actualManpower.append(weekly_actual_total)
            current_date += datetime.timedelta(days=7)
            week_number += 1
            # 移除实际投入人力中的0
            actualManpower = [x for x in actualManpower if x != 0]
        return {
            "xAxisData": xAxisData,
            "planManpower": planManpower,
            "actualManpower": actualManpower
        }

    def get_date(self, project_name):
        """
            提取最大最小日期
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT date_time " \
                    "FROM project_manpower_one " \
                    f"WHERE project_name = '{project_name}'"
        result = db.get_all(query_sql)
        data = []

        for row in result:
            date_time = row.get("date_time")
            date_time = date_time.strftime("%Y-%m-%d")
            data.append(date_time)

        if not data:
            # 处理没有找到日期数据的情况
            # 可以返回默认的日期范围或者抛出异常
            return []

        start_date = min(data)
        end_date = max(data)
        data_time = [{
            "start_date": start_date,
            "end_date": end_date
        }]

        return data_time


class DeleteDuplicateData():
    def delete_duplicate_data(self, batch_size=1000):
        """
            分段删除重复数据
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = """
            SELECT * FROM project_manpower_one
        """
        result = db.get_all(sql)
        unique_dict = {}

        # 构建唯一键字典，记录需要保留的ID
        for row in result:
            date_time = row['date_time'].strftime("%Y-%m-%d")
            key = (
                date_time, row['number_people'], row['job_content'],
                row['serial_number'], row['project_name']
            )
            if key not in unique_dict:
                unique_dict[key] = row['id']

        keep_id_list = list(unique_dict.values())
        total_deleted = len(result) - len(keep_id_list)

        if total_deleted == 0:
            return {
                'code': 0,
                'message': '没有发现重复数据'
            }

        # 获取需要删除的ID列表
        all_ids = [str(row['id']) for row in result]
        delete_ids = [id for id in all_ids if id not in map(str, keep_id_list)]

        # 分段删除
        batches = [delete_ids[i:i + batch_size] for i in range(0, len(delete_ids), batch_size)]
        deleted_count = 0

        for batch in batches:
            if not batch:
                continue

            delete_sql = f"DELETE FROM project_manpower_one WHERE id IN ({','.join(batch)})"
            try:
                db.query(delete_sql)
                db.commit()
                deleted_count += len(batch)
            except Exception as e:
                # 发生错误时回滚
                db.rollback()
                return {
                    'code': 1,
                    'message': f'删除过程中发生错误: {str(e)}，已删除 {deleted_count} 条记录'
                }

        return {
            'code': 0,
            'message': f'成功分段删除 {deleted_count} 条重复数据'
        }
