import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class WhetherThereAnyOtherConstruction(object):
    """
        判断是否有其他施工
    """

    def __init__(self):
        super(WhetherThereAnyOtherConstruction, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def look_for_data(self, ticket_id, project_name):
        """
            查询是否有其他施工
        """
        result = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                 "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                 "FROM excel_data " \
                 f"WHERE project_name = '{project_name}' " \
                 "AND now_time = " \
                 "(SELECT MAX(now_time) " \
                 "FROM excel_data " \
                 f"WHERE project_name = '{project_name}')" \
                 f"AND serial_number LIKE '2.8.%' AND serial_number > '2.8.3'"
        db = mysql.new_mysql_instance("tbconstruct")
        result_list = db.get_all(result)
        # 判断其他方案是否存在
        if result_list:
            next_op = 1
        else:
            next_op = 2

        variables = {
            "next_op": next_op
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionPreparation(object):
    """
        设备进场前施工准备
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(ConstructionPreparation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_construction_preparation(self, project_name):
        """
            创建工单--设备进场前施工准备
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")

        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        create_construction_preparation_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": create_construction_preparation_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/Create_new_construction_preparation"
                }
            )
            PartnerTicketId = str(info['Data'])
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            supplier_info = 'supplier_differentiation'
            system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": create_construction_preparation_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": "construction_preparation_plan",  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:设备进场前施工准备",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:设备进场前施工准备",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_mmywang",  # (*必填)
                        "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)

            PartnerTicketId = str(info['data']['TicketId'])

        data_dict = {
            'ticket_id': ticket_id,
            'system_id': system_id,
            'system_type': system_type,
            'project_name': project_name
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("equipment_before_process", data_dict)
        tb_db.commit()
        variables = {
            'info': str(info),
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def construction_preparation_plan_review(self, project_name, task_name, sgzb_scheme_supervision_approval,
                                             sgzb_scheme_supervision_remark):
        """
            设备进场前施工准备子项方案审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")

        scheme_supervision_approval = 0
        if sgzb_scheme_supervision_approval == "同意":
            scheme_supervision_approval = 1
        elif sgzb_scheme_supervision_approval == "驳回":
            scheme_supervision_approval = 2
        construction_preparation_plan_review_data = {
            "ProjectName": project_name,  # 项目名称
            "TaskName": task_name,  # 子任务名称
            "SchemeSupervisionApproval": scheme_supervision_approval,  # 监理审批
            "SchemeSupervisionRemark": sgzb_scheme_supervision_remark,  # 监理备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": construction_preparation_plan_review_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/construction_preparation_plan_review"
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "construction_preparation_plan_review",  # (*必填) 云函数名称
                        "data": construction_preparation_plan_review_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        variables = {
            'construction_preparation_plan_review': str(info),
            'kbfa_data': data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def construction_ready_completed_confirmation_audit(self, project_name, task_name, examine_approve_result,
                                                        dismiss_role, remark):
        """
            设备进场前施工准备子项完工确认审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")

        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        construction_ready_completed_confirmation_audit_data = {
            "ProjectName": project_name,  # 项目名称
            "TaskName": task_name,  # 子任务名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": construction_ready_completed_confirmation_audit_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                  "construction_ready_completed_confirmation_audit",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "construction_ready_completed_confirmation_audit",  # (*必填) 云函数名称
                        "data": construction_ready_completed_confirmation_audit_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceSbjc(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceSbjc, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_ground_construction_plan_submission(self):
        """
            等待地面处理及放线施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT plan_url, construction_plan_remark " \
              "FROM equipment_before_url " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and name = 'ground' " \
              f"and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if system_type == '外部':
            for record in result_list:
                # 获取 construction_plan_remark
                ground_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串

                # 尝试将 plan_url 字符串解析为列表
                try:
                    plan_urls = ast.literal_eval(plan_url_str)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    plan_urls = []  # 如果解析失败，设置为空列表

                new_result_list = []
                # 确保 plan_urls 是一个列表
                if isinstance(plan_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in plan_urls:
                        new_result_list.append({"plan_url": url})
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_ground_construction_plan_submission": new_result_list,
                    "ground_construction_plan_remark": ground_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            for record in result_list:
                # 获取 construction_plan_remark
                ground_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串
                if plan_url_str is not None:
                    plan_url_str = plan_url_str.replace("'", '"')
                    plan_url_str = json.loads(plan_url_str)
                    plan_url_str_table = []
                    for doc in plan_url_str:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                plan_url_str_table.append({
                                    "plan_url_str": url,
                                    'plan_url_str_name': name
                                })
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_ground_construction_plan_submission": plan_url_str_table,
                    "ground_construction_plan_remark": ground_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_AHU_construction_plan_submission(self):
        """
            等待AHU基础施工施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT plan_url, construction_plan_remark " \
              "FROM equipment_before_url " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and name = 'AHU' " \
              f"and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if system_type == '外部':
            for record in result_list:
                # 获取 construction_plan_remark
                AHU_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串

                # 尝试将 plan_url 字符串解析为列表
                try:
                    plan_urls = ast.literal_eval(plan_url_str)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    plan_urls = []  # 如果解析失败，设置为空列表

                new_result_list = []
                # 确保 plan_urls 是一个列表
                if isinstance(plan_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in plan_urls:
                        new_result_list.append({"plan_url": url})
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_AHU_construction_plan_submission": new_result_list,
                    "AHU_construction_plan_remark": AHU_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            for record in result_list:
                # 获取 construction_plan_remark
                AHU_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串
                if plan_url_str is not None:
                    plan_url_str = plan_url_str.replace("'", '"')
                    plan_url_str = json.loads(plan_url_str)
                    plan_url_str_table = []
                    for doc in plan_url_str:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                plan_url_str_table.append({
                                    "plan_url_str": url,
                                    'plan_url_str_name': name
                                })
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_AHU_construction_plan_submission": plan_url_str_table,
                    "AHU_construction_plan_remark": AHU_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_water_treatment_construction_plan_submission(self):
        """
            等待水处理基础施工施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT plan_url, construction_plan_remark " \
              "FROM equipment_before_url " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and name = 'water_treatment' " \
              f"and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if system_type == '外部':
            for record in result_list:
                # 获取 construction_plan_remark
                water_treatment_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串

                # 尝试将 plan_url 字符串解析为列表
                try:
                    plan_urls = ast.literal_eval(plan_url_str)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    plan_urls = []  # 如果解析失败，设置为空列表

                new_result_list = []
                # 确保 plan_urls 是一个列表
                if isinstance(plan_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in plan_urls:
                        new_result_list.append({"plan_url": url})
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_water_treatment_construction_plan_submission": new_result_list,
                    "water_treatment_construction_plan_remark": water_treatment_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            for record in result_list:
                # 获取 construction_plan_remark
                water_treatment_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串
                if plan_url_str is not None:
                    plan_url_str = plan_url_str.replace("'", '"')
                    plan_url_str = json.loads(plan_url_str)
                    plan_url_str_table = []
                    for doc in plan_url_str:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                plan_url_str_table.append({
                                    "plan_url_str": url,
                                    'plan_url_str_name': name
                                })
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_water_treatment_construction_plan_submission": plan_url_str_table,
                    "water_treatment_construction_plan_remark": water_treatment_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_other_construction_plan_submission(self):
        """
            等待其它基础施工方案施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT plan_url, construction_plan_remark " \
              "FROM equipment_before_url " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and name = 'other' " \
              f"and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if system_type == '外部':
            for record in result_list:
                # 获取 construction_plan_remark
                other_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串

                # 尝试将 plan_url 字符串解析为列表
                try:
                    plan_urls = ast.literal_eval(plan_url_str)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    plan_urls = []  # 如果解析失败，设置为空列表

                new_result_list = []
                # 确保 plan_urls 是一个列表
                if isinstance(plan_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in plan_urls:
                        new_result_list.append({"plan_url": url})
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_other_construction_plan_submission": new_result_list,
                    "other_construction_plan_remark": other_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            for record in result_list:
                # 获取 construction_plan_remark
                other_construction_plan_remark = record.get('construction_plan_remark', '')

                # 获取 plan_url 字符串
                plan_url_str = record.get('plan_url', "[]")  # 默认值为一个空的列表字符串

                if plan_url_str is not None:
                    plan_url_str = plan_url_str.replace("'", '"')
                    plan_url_str = json.loads(plan_url_str)
                    plan_url_str_table = []
                    for doc in plan_url_str:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                plan_url_str_table.append({
                                    "plan_url_str": url,
                                    'plan_url_str_name': name
                                })
            if result_list:
                variables = {
                    "result_list": result_list,
                    "wait_other_construction_plan_submission": plan_url_str_table,
                    "other_construction_plan_remark": other_construction_plan_remark,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_ground_completion_confirmation_data_submission(self):
        """
            等待地面处理及放线完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND (serial_number = '2.8' OR serial_number = '2.8.1')"
        file_sql = "SELECT report_url, photo_url " \
                   "FROM equipment_before_url " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'" \
                   f"AND name = 'ground'"
        word_list = db.get_all(word_sql)
        ground_file_list = db.get_all(file_sql)
        if system_type == '外部':
            ground_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')

                # 根据 serial_number 选择需要的字段
                if serial_number == '2.8':
                    sbjc_work_content = row.get('work_content', '')
                    sbjc_start_time = row.get('start_time', '')
                    sbjc_completion_time = row.get('completion_time', '')
                    sbjc_responsible_person = row.get('responsible_person', '')

                if serial_number == '2.8.1':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    ground_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain
                    })
            # 遍历原始列表
            for record in ground_file_list:
                # 获取 plan_url 列表
                report_url = record.get('report_url', [])
                photo_url = record.get('photo_url', [])
                # 尝试将 report_url 字符串解析为列表
                try:
                    report_urls = ast.literal_eval(report_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    report_urls = []  # 如果解析失败，设置为空列表
                ground_report_url_list = []
                # 确保 report_urls 是一个列表
                if isinstance(report_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in report_urls:
                        ground_report_url_list.append({"report_url": url})

                # 尝试将 photo_url 字符串解析为列表
                try:
                    photo_urls = ast.literal_eval(photo_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    photo_urls = []  # 如果解析失败，设置为空列表
                ground_photo_url_list = []
                # 确保 photo_urls 是一个列表
                if isinstance(photo_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in photo_urls:
                        ground_photo_url_list.append({"photo_url": url})
            if word_list and ground_file_list:
                variables = {
                    'sbjc_work_content': sbjc_work_content,
                    'sbjc_start_time': sbjc_start_time,
                    'sbjc_completion_time': sbjc_completion_time,
                    'sbjc_responsible_person': sbjc_responsible_person,
                    'ground_word_table': ground_word_table,
                    'ground_report_url_list': ground_report_url_list,
                    'ground_photo_url_list': ground_photo_url_list,
                    'word_list': word_list,
                    'AHU_file_list': ground_file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            ground_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')

                # 根据 serial_number 选择需要的字段
                if serial_number == '2.8':
                    sbjc_work_content = row.get('work_content', '')
                    sbjc_start_time = row.get('start_time', '')
                    sbjc_completion_time = row.get('completion_time', '')
                    sbjc_responsible_person = row.get('responsible_person', '')

                if serial_number == '2.8.1':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                    ground_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain
                    })
            # 遍历原始列表
            for record in ground_file_list:
                # 获取 plan_url 列表
                report_url = record.get('report_url', [])
                photo_url = record.get('photo_url', [])

                if report_url is not None:
                    report_url = report_url.replace("'", '"')
                    report_url = json.loads(report_url)
                    report_url_table = []
                    for doc in report_url:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                report_url_table.append({
                                    "report_url": url,
                                    'report_url_name': name
                                })

                if photo_url is not None:
                    photo_url = photo_url.replace("'", '"')
                    photo_url = json.loads(photo_url)
                    photo_url_table = []
                    for doc in photo_url:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                photo_url_table.append({
                                    "photo_url": url,
                                    'photo_url_name': name
                                })
            if word_list and ground_file_list:
                variables = {
                    'sbjc_work_content': sbjc_work_content,
                    'sbjc_start_time': sbjc_start_time,
                    'sbjc_completion_time': sbjc_completion_time,
                    'sbjc_responsible_person': sbjc_responsible_person,
                    'ground_word_table': ground_word_table,
                    'ground_report_url_list': report_url_table,
                    'ground_photo_url_list': photo_url_table,
                    'word_list': word_list,
                    'file_list': file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_AHU_completion_confirmation_data_submission(self):
        """
            等待AHU完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND serial_number = '2.8.2'"
        file_sql = "SELECT report_url, photo_url " \
                   "FROM equipment_before_url " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'" \
                   f"AND name = 'AHU'"
        word_list = db.get_all(word_sql)
        AHU_file_list = db.get_all(file_sql)
        if system_type == '外部':
            AHU_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）
                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）
                AHU_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "planned_project_duration": planned_project_duration,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "actual_construction_time": actual_construction_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
            # 遍历原始列表
            for record in AHU_file_list:
                # 获取 plan_url 列表
                report_url = record.get('report_url', [])
                photo_url = record.get('photo_url', [])
                # 尝试将 report_url 字符串解析为列表
                try:
                    report_urls = ast.literal_eval(report_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    report_urls = []  # 如果解析失败，设置为空列表
                AHU_report_url_list = []
                # 确保 report_urls 是一个列表
                if isinstance(report_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in report_urls:
                        AHU_report_url_list.append({"report_url": url})

                # 尝试将 photo_url 字符串解析为列表
                try:
                    photo_urls = ast.literal_eval(photo_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    photo_urls = []  # 如果解析失败，设置为空列表
                AHU_photo_url_list = []
                # 确保 photo_urls 是一个列表
                if isinstance(photo_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in photo_urls:
                        AHU_photo_url_list.append({"photo_url": url})
            if word_list and AHU_file_list:
                variables = {
                    'AHU_word_table': AHU_word_table,
                    'AHU_report_url_list': AHU_report_url_list,
                    'AHU_photo_url_list': AHU_photo_url_list,
                    'word_list': word_list,
                    'AHU_file_list': AHU_file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            AHU_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                AHU_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
                # 遍历原始列表
                for record in AHU_file_list:
                    # 获取 plan_url 列表
                    report_url = record.get('report_url', [])
                    photo_url = record.get('photo_url', [])

                    if report_url is not None:
                        report_url = report_url.replace("'", '"')
                        report_url = json.loads(report_url)
                        report_url_table = []
                        for doc in report_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    report_url_table.append({
                                        "report_url": url,
                                        'report_url_name': name
                                    })

                    if photo_url is not None:
                        photo_url = photo_url.replace("'", '"')
                        photo_url = json.loads(photo_url)
                        photo_url_table = []
                        for doc in photo_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    photo_url_table.append({
                                        "photo_url": url,
                                        'photo_url_name': name
                                    })
            if word_list and AHU_file_list:
                variables = {
                    'AHU_word_table': AHU_word_table,
                    'AHU_report_url_list': report_url_table,
                    'AHU_photo_url_list': photo_url_table,
                    'word_list': word_list,
                    'file_list': file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_water_treatment_completion_confirmation_data_submission(self):
        """
            等待水处理基础施工完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND serial_number = '2.8.3'"
        file_sql = "SELECT report_url, photo_url " \
                   "FROM equipment_before_url " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'" \
                   f"AND name = 'water_treatment'"
        word_list = db.get_all(word_sql)
        water_treatment_file_list = db.get_all(file_sql)
        if system_type == '外部':
            water_treatment_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）
                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）
                water_treatment_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "planned_project_duration": planned_project_duration,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "actual_construction_time": actual_construction_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
            # 遍历原始列表
            for record in water_treatment_file_list:
                # 获取 plan_url 列表
                report_url = record.get('report_url', [])
                photo_url = record.get('photo_url', [])
                # 尝试将 report_url 字符串解析为列表
                try:
                    report_urls = ast.literal_eval(report_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    report_urls = []  # 如果解析失败，设置为空列表
                water_treatment_report_url_list = []
                # 确保 report_urls 是一个列表
                if isinstance(report_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in report_urls:
                        water_treatment_report_url_list.append({"report_url": url})

                # 尝试将 photo_url 字符串解析为列表
                try:
                    photo_urls = ast.literal_eval(photo_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    photo_urls = []  # 如果解析失败，设置为空列表
                water_treatment_photo_url_list = []
                # 确保 photo_urls 是一个列表
                if isinstance(photo_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in photo_urls:
                        water_treatment_photo_url_list.append({"photo_url": url})
            if word_list and water_treatment_file_list:
                variables = {
                    'water_treatment_word_table': water_treatment_word_table,
                    'water_treatment_report_url_list': water_treatment_report_url_list,
                    'water_treatment_photo_url_list': water_treatment_photo_url_list,
                    'word_list': word_list,
                    'water_treatment_file_list': water_treatment_file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            water_treatment_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                water_treatment_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
                # 遍历原始列表
                for record in water_treatment_file_list:
                    # 获取 plan_url 列表
                    report_url = record.get('report_url', [])
                    photo_url = record.get('photo_url', [])

                    if report_url is not None:
                        report_url = report_url.replace("'", '"')
                        report_url = json.loads(report_url)
                        report_url_table = []
                        for doc in report_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    report_url_table.append({
                                        "report_url": url,
                                        'report_url_name': name
                                    })

                    if photo_url is not None:
                        photo_url = photo_url.replace("'", '"')
                        photo_url = json.loads(photo_url)
                        photo_url_table = []
                        for doc in photo_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    photo_url_table.append({
                                        "photo_url": url,
                                        'photo_url_name': name
                                    })
            if word_list and water_treatment_file_list:
                variables = {
                    'water_treatment_word_table': water_treatment_word_table,
                    'water_treatment_report_url_list': report_url_table,
                    'water_treatment_photo_url_list': photo_url_table,
                    'word_list': word_list,
                    'file_list': file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def wait_other_completion_confirmation_data_submission(self):
        """
            等待它基础施工完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        system_type = self.ctx.variables.get("system_type")

        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND serial_number LIKE '2.8.%' AND serial_number > '2.8.3'"
        file_sql = "SELECT report_url, photo_url " \
                   "FROM equipment_before_url " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'" \
                   f"AND name = 'other'"
        word_list = db.get_all(word_sql)
        other_file_list = db.get_all(file_sql)
        if system_type == '外部':
            other_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                # 计算计划完成天数
                start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）
                # 计算实际完成天数
                actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）
                other_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "planned_project_duration": planned_project_duration,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "actual_construction_time": actual_construction_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
            # 遍历原始列表
            for record in other_file_list:
                # 获取 plan_url 列表
                report_url = record.get('report_url', [])
                photo_url = record.get('photo_url', [])
                # 尝试将 report_url 字符串解析为列表
                try:
                    report_urls = ast.literal_eval(report_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    report_urls = []  # 如果解析失败，设置为空列表
                other_report_url_list = []
                # 确保 report_urls 是一个列表
                if isinstance(report_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in report_urls:
                        other_report_url_list.append({"report_url": url})

                # 尝试将 photo_url 字符串解析为列表
                try:
                    photo_urls = ast.literal_eval(photo_url)  # 将字符串解析为列表
                except (ValueError, SyntaxError):
                    photo_urls = []  # 如果解析失败，设置为空列表
                other_photo_url_list = []
                # 确保 photo_urls 是一个列表
                if isinstance(photo_urls, list):
                    # 为每个 URL 创建一个新的字典
                    for url in photo_urls:
                        other_photo_url_list.append({"photo_url": url})
            if word_list and other_file_list:
                variables = {
                    'other_word_table': other_word_table,
                    'other_report_url_list': other_report_url_list,
                    'other_photo_url_list': other_photo_url_list,
                    'word_list': word_list,
                    'other_file_list': other_file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
        elif system_type == 'Dcops':
            other_word_table = []
            for row in word_list:
                serial_number = row.get('serial_number', '')
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')

                other_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain
                })
                # 遍历原始列表
                for record in other_file_list:
                    # 获取 plan_url 列表
                    report_url = record.get('report_url', [])
                    photo_url = record.get('photo_url', [])

                    if report_url is not None:
                        report_url = report_url.replace("'", '"')
                        report_url = json.loads(report_url)
                        report_url_table = []
                        for doc in report_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    report_url_table.append({
                                        "report_url": url,
                                        'report_url_name': name
                                    })

                    if photo_url is not None:
                        photo_url = photo_url.replace("'", '"')
                        photo_url = json.loads(photo_url)
                        photo_url_table = []
                        for doc in photo_url:
                            if doc is not None and "response" in doc and "FileList" in doc["response"]:
                                file_list = doc["response"]["FileList"]
                                if len(file_list) > 0 and "url" in file_list[0]:
                                    url = file_list[0]["url"]
                                    name = file_list[0]['name']
                                    photo_url_table.append({
                                        "photo_url": url,
                                        'photo_url_name': name
                                    })
            if word_list and other_file_list:
                variables = {
                    'other_word_table': other_word_table,
                    'other_report_url_list': report_url_table,
                    'other_photo_url_list': photo_url_table,
                    'word_list': word_list,
                    'file_list': file_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceSbjc(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceSbjc, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_basic_info(self, project_name, TencentTicketId):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT ticket_id FROM equipment_before_process " \
              f"WHERE project_name = '{project_name}' " \
              f"AND ticket_id = '{TencentTicketId}'"
        result_list = db.get_all(sql)
        if result_list:
            project_name = result_list[0].get("ticket_id")
            return project_name
        else:
            return 0

    def construction_preparation_plan_submitted(self, TencentTicketId, ProjectName, SubitemName, SubitemStartPlan,
                                                Remark):
        """
            设备进场前施工准备子项方案提交
        """
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName or not SubitemName:
            return {'code': -1, 'msg': '工单号或项目名称或子项名称缺失'}
        info = self.get_basic_info(project_name=ProjectName, TencentTicketId=TencentTicketId)
        if info == 0:
            return {'code': -1, 'msg': '参数错误'}
        if SubitemName == '地面处理及放线':
            data = {
                "ticket_id": TencentTicketId,
                'name': 'ground',
                'project_name': ProjectName,
                'construction_plan_remark': Remark,
                "plan_url": str(SubitemStartPlan)
            }
        elif SubitemName == 'AHU基础施工':
            data = {
                "ticket_id": TencentTicketId,
                'name': 'AHU',
                'project_name': ProjectName,
                'construction_plan_remark': Remark,
                "plan_url": str(SubitemStartPlan)
            }
        elif SubitemName == '水处理基础施工':
            data = {
                "ticket_id": TencentTicketId,
                'name': 'water_treatment',
                'project_name': ProjectName,
                'construction_plan_remark': Remark,
                "plan_url": str(SubitemStartPlan)
            }
        else:
            data = {
                "ticket_id": TencentTicketId,
                'name': 'other',
                'project_name': ProjectName,
                'construction_plan_remark': Remark,
                "plan_url": str(SubitemStartPlan)
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if data:
            tb_db.insert("equipment_before_url", data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def construction_readiness_confirmed(self, TencentTicketId, ProjectName, TaskName, StartTime, FinishTime,
                                         ScheduleSkewingExplain, CompleteReport, CompletePhoto, Remark):
        """
            设备进场前施工准备子项施工完工确认
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        # 校验 TaskName
        if not TaskName:
            return {'code': -1, 'msg': '子任务名称缺失'}
        # 校验 StartTime 和 FinishTime
        if not StartTime or not FinishTime:
            return {'code': -1, 'msg': '实际开始时间和实际完成时间缺失'}
        # 校验 ScheduleSkewingExplain
        if not ScheduleSkewingExplain:
            return {'code': -1, 'msg': '进度偏移说明'}
        # 获取基本信息
        info = self.get_basic_info(project_name=ProjectName, TencentTicketId=TencentTicketId)
        if info == 0:
            return {'code': -1, 'msg': '参数错误'}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()

        sbjc_conditions = {
            'project_name': ProjectName,
            'work_content': '设备进场前施工',
        }
        sbjc_update_data = {
            "dcops_ticket_id": TencentTicketId,
            "project_name": ProjectName,
        }
        tb_db.update("excel_data", sbjc_update_data, sbjc_conditions)
        if TaskName == "地面处理及放线":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "AHU基础施工":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "水处理基础施工":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        else:
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务

        if TaskName == '地面处理及放线':
            file_conditions = {
                'ticket_id': TencentTicketId,
                'name': 'ground',
                'project_name': ProjectName
            }
        elif TaskName == 'AHU基础施工':
            file_conditions = {
                'ticket_id': TencentTicketId,
                'name': 'AHU',
                'project_name': ProjectName
            }
        elif TaskName == '水处理基础施工':
            file_conditions = {
                'ticket_id': TencentTicketId,
                'name': 'water_treatment',
                'project_name': ProjectName
            }
        else:
            file_conditions = {
                'ticket_id': TencentTicketId,
                'name': 'other',
                'project_name': ProjectName
            }
        file_update_data = {
            'report_url': str(CompleteReport),
            'photo_url': str(CompletePhoto)
        }
        tb_db.update("equipment_before_url", file_update_data, file_conditions)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class SbjcSgfaGroundSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SbjcSgfaGroundSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sgfa_ground_scheme_supervision_approval = process_data.get('sgfa_ground_scheme_supervision_approval')
        sgfa_ground_scheme_supervision_remark = process_data.get('sgfa_ground_scheme_supervision_remark')

        if sgfa_ground_scheme_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and name = 'ground'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sgfa_ground_scheme_supervision_approval': sgfa_ground_scheme_supervision_approval,
            'sgfa_ground_scheme_supervision_remark': sgfa_ground_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcSgfaAHUSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-AHU-施工方案监理审批
    """

    def __init__(self):
        super(SbjcSgfaAHUSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sgfa_AHU_scheme_supervision_approval = process_data.get('sgfa_AHU_scheme_supervision_approval')
        sgfa_AHU_scheme_supervision_remark = process_data.get('sgfa_AHU_scheme_supervision_remark')

        if sgfa_AHU_scheme_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and name = 'AHU'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sgfa_AHU_scheme_supervision_approval': sgfa_AHU_scheme_supervision_approval,
            'sgfa_AHU_scheme_supervision_remark': sgfa_AHU_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcSgfaWaterTreatmentSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-水处理基础施工-施工方案监理审批
    """

    def __init__(self):
        super(SbjcSgfaWaterTreatmentSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sgfa_water_treatment_scheme_supervision_approval = process_data.get(
            'sgfa_water_treatment_scheme_supervision_approval')
        sgfa_water_treatment_scheme_supervision_remark = process_data.get(
            'sgfa_water_treatment_scheme_supervision_remark')

        if sgfa_water_treatment_scheme_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and name = 'water_treatment'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sgfa_water_treatment_scheme_supervision_approval': sgfa_water_treatment_scheme_supervision_approval,
            'sgfa_water_treatment_scheme_supervision_remark': sgfa_water_treatment_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcSgfaOtherSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-其它基础施工-施工方案监理审批
    """

    def __init__(self):
        super(SbjcSgfaOtherSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sgfa_other_scheme_supervision_approval = process_data.get('sgfa_other_scheme_supervision_approval')
        sgfa_other_scheme_supervision_remark = process_data.get('sgfa_other_scheme_supervision_remark')

        if sgfa_other_scheme_supervision_approval == '驳回':
            query_sql = f"DELETE FROM equipment_before_url " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"and project_name ='{project_name}'" \
                        f"and name = 'other'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'sgfa_other_scheme_supervision_approval': sgfa_other_scheme_supervision_approval,
            'sgfa_other_scheme_supervision_remark': sgfa_other_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrGroundSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-完工确认监理审批
    """

    def __init__(self):
        super(SbjcWgqrGroundSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_groubd_scheme_supervision_approval = process_data.get('wgqr_groubd_scheme_supervision_approval')
        wgqr_groubd_scheme_supervision_remark = process_data.get('wgqr_groubd_scheme_supervision_remark')

        variables = {
            'wgqr_groubd_scheme_supervision_approval': wgqr_groubd_scheme_supervision_approval,
            'wgqr_groubd_scheme_supervision_remark': wgqr_groubd_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrGroundItemApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-完工确认项管审批
    """

    def __init__(self):
        super(SbjcWgqrGroundItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_groubd_item_approval = process_data.get('wgqr_groubd_item_approval')
        wgqr_groubd_item_remark = process_data.get('wgqr_groubd_item_remark')

        variables = {
            'wgqr_groubd_item_approval': wgqr_groubd_item_approval,
            'wgqr_groubd_item_remark': wgqr_groubd_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrGroundPmApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-完工确认PM审批
    """

    def __init__(self):
        super(SbjcWgqrGroundPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_groubd_PM_approval = process_data.get('wgqr_groubd_PM_approval')
        wgqr_groubd_PM_remark = process_data.get('wgqr_groubd_PM_remark')

        variables = {
            'wgqr_groubd_PM_approval': wgqr_groubd_PM_approval,
            'wgqr_groubd_PM_remark': wgqr_groubd_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrAHUSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-AHU-完工确认监理审批
    """

    def __init__(self):
        super(SbjcWgqrAHUSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_AHU_scheme_supervision_approval = process_data.get('wgqr_AHU_scheme_supervision_approval')
        wgqr_AHU_scheme_supervision_remark = process_data.get('wgqr_AHU_scheme_supervision_remark')

        variables = {
            'wgqr_AHU_scheme_supervision_approval': wgqr_AHU_scheme_supervision_approval,
            'wgqr_AHU_scheme_supervision_remark': wgqr_AHU_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrAHUItemApproval(AjaxTodoBase):
    """
        设备进场-AHU-完工确认项管审批
    """

    def __init__(self):
        super(SbjcWgqrAHUItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_AHU_item_approval = process_data.get('wgqr_AHU_item_approval')
        wgqr_AHU_item_remark = process_data.get('wgqr_AHU_item_remark')

        variables = {
            'wgqr_AHU_item_approval': wgqr_AHU_item_approval,
            'wgqr_AHU_item_remark': wgqr_AHU_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrAHUPmApproval(AjaxTodoBase):
    """
        设备进场-AHU-完工确认PM审批
    """

    def __init__(self):
        super(SbjcWgqrAHUPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_AHU_PM_approval = process_data.get('wgqr_AHU_PM_approval')
        wgqr_AHU_PM_remark = process_data.get('wgqr_AHU_PM_remark')

        variables = {
            'wgqr_AHU_PM_approval': wgqr_AHU_PM_approval,
            'wgqr_AHU_PM_remark': wgqr_AHU_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrWaterTreatmentSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-水处理基础施工-完工确认监理审批
    """

    def __init__(self):
        super(SbjcWgqrWaterTreatmentSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_water_treatment_scheme_supervision_approval = process_data.get(
            'wgqr_water_treatment_scheme_supervision_approval')
        wgqr_water_treatment_scheme_supervision_remark = process_data.get(
            'wgqr_water_treatment_scheme_supervision_remark')

        variables = {
            'wgqr_water_treatment_scheme_supervision_approval': wgqr_water_treatment_scheme_supervision_approval,
            'wgqr_water_treatment_scheme_supervision_remark': wgqr_water_treatment_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrWaterTreatmentItemApproval(AjaxTodoBase):
    """
        设备进场-水处理基础施工-完工确认项管审批
    """

    def __init__(self):
        super(SbjcWgqrWaterTreatmentItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_water_treatment_item_approval = process_data.get('wgqr_water_treatment_item_approval')
        wgqr_water_treatment_item_remark = process_data.get('wgqr_water_treatment_item_remark')

        variables = {
            'wgqr_water_treatment_item_approval': wgqr_water_treatment_item_approval,
            'wgqr_water_treatment_item_remark': wgqr_water_treatment_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrWaterTreatmentPmApproval(AjaxTodoBase):
    """
        设备进场-水处理基础施工-完工确认PM审批
    """

    def __init__(self):
        super(SbjcWgqrWaterTreatmentPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_water_treatment_PM_approval = process_data.get('wgqr_water_treatment_PM_approval')
        wgqr_water_treatment_PM_remark = process_data.get('wgqr_water_treatment_PM_remark')

        variables = {
            'wgqr_water_treatment_PM_approval': wgqr_water_treatment_PM_approval,
            'wgqr_water_treatment_PM_remark': wgqr_water_treatment_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrOtherSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-其它基础施工-完工确认监理审批
    """

    def __init__(self):
        super(SbjcWgqrOtherSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_other_scheme_supervision_approval = process_data.get('wgqr_other_scheme_supervision_approval')
        wgqr_other_scheme_supervision_remark = process_data.get('wgqr_other_scheme_supervision_remark')

        variables = {
            'wgqr_other_scheme_supervision_approval': wgqr_other_scheme_supervision_approval,
            'wgqr_other_scheme_supervision_remark': wgqr_other_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrOtherItemApproval(AjaxTodoBase):
    """
        设备进场-其它基础施工-完工确认项管审批
    """

    def __init__(self):
        super(SbjcWgqrOtherItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_other_item_approval = process_data.get('wgqr_other_item_approval')
        wgqr_other_item_remark = process_data.get('wgqr_other_item_remark')

        variables = {
            'wgqr_other_item_approval': wgqr_other_item_approval,
            'wgqr_other_item_remark': wgqr_other_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SbjcWgqrOtherPmApproval(AjaxTodoBase):
    """
        设备进场-其它基础施工-完工确认PM审批
    """

    def __init__(self):
        super(SbjcWgqrOtherPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_other_PM_approval = process_data.get('wgqr_other_PM_approval')
        wgqr_other_PM_remark = process_data.get('wgqr_other_PM_remark')

        variables = {
            'wgqr_other_PM_approval': wgqr_other_PM_approval,
            'wgqr_other_PM_remark': wgqr_other_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
