import json

from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow
from biz.construction_application_process.construction_application_info import (
    ConstructionApplicationInfoQuery,
)
from biz.construction_final_acceptance.tools import Tools


# 施工准备阶段-报建流程 数据入库类
class ConstructionApplicationProcessIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_construction_application_second_level_into_db_new(self):
        """
        建设流程-施工准备阶段-2.4项目报建-二级标题（新）
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        xmbj_list = self.ctx.variables.get("xmbj_list")
        # xmbj_sub_plan_file_json = self.ctx.variables.get("xmbj_sub_plan_file_json")
        # # 消防第三方测试报告
        # fire_engineering_inspection_report_file_json = self.ctx.variables.get(
        #     "fire_engineering_inspection_report_file_json"
        # )
        second_level_into_insert_new = []
        for list in xmbj_list:
            second_level_into_insert_new.append(
                {
                    "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                    "project_name": project_name,
                    "ticket_id": ticket_id,
                    "seq_no": list.get("seq_no"),
                    "work_content": list.get("work_content"),
                    "construction_time": list.get("construction_time"),
                    "start_time": list.get("start_time"),
                    "finish_time": list.get("finish_time"),
                    "responsible_person": list.get("responsible_person"),
                    "input": list.get("input"),
                    "output": list.get("output"),
                    "construction_attribute": list.get("construction_attribute"),
                    # "sub_plan": json.dumps(xmbj_sub_plan_file_json, ensure_ascii=False),
                    # "fire_test_report": json.dumps(
                    #     fire_engineering_inspection_report_file_json, ensure_ascii=False
                    # ),
                }
            )
        if second_level_into_insert_new:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_second_level", second_level_into_insert_new
            )
            tb_db.commit()
        variables = {"second_level_into_insert_new": second_level_into_insert_new}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def mains_power_plan_second_level_into_db_new(self):
        """
        建设流程-施工准备阶段-2.9市电专项-二级标题（新）
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        sdzx_list = self.ctx.variables.get("sdzx_list")
        # sdzx_sub_plan_file_json = self.ctx.variables.get("sdzx_sub_plan_file_json")
        second_level_into_insert_new = []
        for list in sdzx_list:
            second_level_into_insert_new.append(
                {
                    "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                    "project_name": project_name,
                    "ticket_id": ticket_id,
                    "seq_no": list.get("seq_no"),
                    "work_content": list.get("work_content"),
                    "construction_time": list.get("construction_time"),
                    "start_time": list.get("start_time"),
                    "finish_time": list.get("finish_time"),
                    "responsible_person": list.get("responsible_person"),
                    "input": list.get("input"),
                    "output": list.get("output"),
                    "construction_attribute": list.get("construction_attribute"),
                    # "sub_plan": json.dumps(sdzx_sub_plan_file_json, ensure_ascii=False),
                }
            )
        if second_level_into_insert_new:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_second_level", second_level_into_insert_new
            )
            tb_db.commit()
        variables = {"second_level_into_insert_new": second_level_into_insert_new}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def project_construction_application_second_level_into_db(self):
        """
        建设流程-施工准备阶段-2.4项目报建-二级标题
        """
        project_name = self.ctx.variables.get("project_name")
        # 兼容处理：旧版最后才存库，新版建单就要存库（门户页面需要根据单号跟踪流程情况），完成更新库
        info = ConstructionApplicationInfoQuery.get_construction_application_second_level_info(
            project_name, "2.4"
        )
        second_level_into_insert = []
        if info:
            # 消防第三方测试报告
            fire_engineering_inspection_report_file_json = self.ctx.variables.get(
                "fire_engineering_inspection_report_file_json"
            )
            xmbj_sub_plan_file_json = self.ctx.variables.get("xmbj_sub_plan_file_json")
            tb_db = mysql.new_mysql_instance("tbconstruct")
            # 三级内容
            update_data = {
                "sub_plan": json.dumps(xmbj_sub_plan_file_json, ensure_ascii=False),
                "fire_test_report": json.dumps(
                    fire_engineering_inspection_report_file_json, ensure_ascii=False
                ),
            }
            if info[0].get("id"):
                tb_db.begin()
                tb_db.update_by_id(
                    "construction_application_second_level",
                    update_data,
                    info[0].get("id"),
                )
                tb_db.commit()
        else:
            overall_control_plan_ticket_id = self.ctx.variables.get(
                "overall_control_plan_ticket_id"
            )
            ticket_id = self.ctx.variables.get("ticket_id")
            xmbj_list = self.ctx.variables.get("xmbj_list")
            xmbj_sub_plan_file_json = self.ctx.variables.get("xmbj_sub_plan_file_json")
            # 消防第三方测试报告
            fire_engineering_inspection_report_file_json = self.ctx.variables.get(
                "fire_engineering_inspection_report_file_json"
            )
            for list in xmbj_list:
                second_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "project_name": project_name,
                        "ticket_id": ticket_id,
                        "seq_no": list.get("seq_no"),
                        "work_content": list.get("work_content"),
                        "construction_time": list.get("construction_time"),
                        "start_time": list.get("start_time"),
                        "finish_time": list.get("finish_time"),
                        "responsible_person": list.get("responsible_person"),
                        "input": list.get("input"),
                        "output": list.get("output"),
                        "construction_attribute": list.get("construction_attribute"),
                        "sub_plan": json.dumps(
                            xmbj_sub_plan_file_json, ensure_ascii=False
                        ),
                        "fire_test_report": json.dumps(
                            fire_engineering_inspection_report_file_json,
                            ensure_ascii=False,
                        ),
                    }
                )
            if second_level_into_insert:
                # 连接数据库
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                # 插入数据
                tb_db.insert_batch(
                    "construction_application_second_level", second_level_into_insert
                )
                tb_db.commit()
        variables = {"second_level_into_insert": second_level_into_insert}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def mains_power_plan_second_level_into_db(self):
        """
        建设流程-施工准备阶段-2.9市电专项-二级标题
        """
        project_name = self.ctx.variables.get("project_name")
        # 兼容处理：旧版最后才存库，新版建单就要存库（门户页面需要根据单号跟踪流程情况），完成更新库
        info = ConstructionApplicationInfoQuery.get_construction_application_second_level_info(
            project_name, "2.9"
        )
        second_level_into_insert = []
        if info:
            sdzx_sub_plan_file_json = self.ctx.variables.get("sdzx_sub_plan_file_json")
            tb_db = mysql.new_mysql_instance("tbconstruct")
            # 三级内容
            update_data = {
                "sub_plan": json.dumps(sdzx_sub_plan_file_json, ensure_ascii=False),
            }
            if info[0].get("id"):
                tb_db.begin()
                tb_db.update_by_id(
                    "construction_application_second_level",
                    update_data,
                    info[0].get("id"),
                )
                tb_db.commit()
        else:
            overall_control_plan_ticket_id = self.ctx.variables.get(
                "overall_control_plan_ticket_id"
            )
            ticket_id = self.ctx.variables.get("ticket_id")
            sdzx_list = self.ctx.variables.get("sdzx_list")
            sdzx_sub_plan_file_json = self.ctx.variables.get("sdzx_sub_plan_file_json")
            for list in sdzx_list:
                second_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "project_name": project_name,
                        "ticket_id": ticket_id,
                        "seq_no": list.get("seq_no"),
                        "work_content": list.get("work_content"),
                        "construction_time": list.get("construction_time"),
                        "start_time": list.get("start_time"),
                        "finish_time": list.get("finish_time"),
                        "responsible_person": list.get("responsible_person"),
                        "input": list.get("input"),
                        "output": list.get("output"),
                        "construction_attribute": list.get("construction_attribute"),
                        "sub_plan": json.dumps(
                            sdzx_sub_plan_file_json, ensure_ascii=False
                        ),
                    }
                )
            if second_level_into_insert:
                # 连接数据库
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                # 插入数据
                tb_db.insert_batch(
                    "construction_application_second_level", second_level_into_insert
                )
                tb_db.commit()
        variables = {"second_level_into_insert": second_level_into_insert}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def project_construction_application_third_level_into_db(self):
        """
        建设流程-施工准备阶段-报建管理2.4项目报建-三级标题、四级标题
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        # 项目报建-二级
        xmbj_list = self.ctx.variables.get("xmbj_list")
        # 项目立项
        xmlx_dict = self.ctx.variables.get("xmlx_dict")
        # 设计合同签订上传
        sjhtqt_dict = self.ctx.variables.get("sjhtqt_dict")
        # 图纸送审
        tzss_dict = self.ctx.variables.get("tzss_dict")
        # 监理直接发包、上传
        jlfb_dict = self.ctx.variables.get("jlfb_dict")
        # 工程直接发包、上传
        gcfb_dict = self.ctx.variables.get("gcfb_dict")
        # 监理合同签订上传
        jlhtqd_dict = self.ctx.variables.get("jlhtqd_dict")
        # 工程合同签订上传
        gchtqj_dict = self.ctx.variables.get("gchtqj_dict")
        # 农民工保证金监管合同签订上传
        nmgbzjjghtqd_dict = self.ctx.variables.get("nmgbzjjghtqd_dict")
        # 工伤意外险
        gsywx_dict = self.ctx.variables.get("gsywx_dict")
        # 施工许可证下发
        sgxkz_dict = self.ctx.variables.get("sgxkz_dict")
        # 竣工备案
        jgba_dict = self.ctx.variables.get("jgba_dict")
        # 消防备案
        xfba_dict = self.ctx.variables.get("xfba_dict")
        # #档案馆资料归档
        # dagzlgd_dict = self.ctx.variables.get("dagzlgd_dict")

        # 上传消防报验计划 消防工程第三方检测报告

        third_level_list_temp = [
            xmlx_dict,
            sjhtqt_dict,
            tzss_dict,
            jlfb_dict,
            gcfb_dict,
            jlhtqd_dict,
            gchtqj_dict,
            nmgbzjjghtqd_dict,
            gsywx_dict,
            sgxkz_dict,
            jgba_dict,
            xfba_dict,
        ]

        third_level_into_insert = []
        fourth_level_into_insert = []
        for third_info in third_level_list_temp:
            third_level_into_insert.append(
                {
                    "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                    "construction_application_process_ticket_id": ticket_id,
                    "project_name": project_name,
                    "second_seq_no": xmbj_list[0]["seq_no"] if xmbj_list else None,
                    "second_work_content": (
                        xmbj_list[0]["work_content"] if xmbj_list else None
                    ),
                    "seq_no": third_info.get("seq_no"),
                    "work_content": third_info.get("work_content"),
                    "construction_time": third_info.get("construction_time"),
                    "start_time": third_info.get("start_time"),
                    "finish_time": third_info.get("finish_time"),
                    "responsible_person": third_info.get("responsible_person"),
                    "input": third_info.get("input"),
                    "output": third_info.get("output"),
                    "construction_attribute": third_info.get("construction_attribute"),
                    "upload_file_json": json.dumps(
                        third_info.get("upload_file_json"), ensure_ascii=False
                    ),
                    "remark": third_info.get("remark"),
                    "actual_start_time": third_info.get("actual_start_time"),
                    "actual_finish_time": third_info.get("actual_finish_time"),
                }
            )
            for fourth_info in third_info.get("work_list"):
                fourth_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "construction_application_process_ticket_id": ticket_id,
                        "project_name": project_name,
                        "second_seq_no": xmbj_list[0]["seq_no"] if xmbj_list else None,
                        "second_work_content": (
                            xmbj_list[0]["work_content"] if xmbj_list else None
                        ),
                        "third_seq_no": third_info.get("seq_no"),
                        "third_work_content": third_info.get("work_content"),
                        "seq_no": fourth_info.get("seq_no"),
                        "work_content": fourth_info.get("work_content"),
                        "construction_time": fourth_info.get("construction_time"),
                        "start_time": fourth_info.get("start_time"),
                        "finish_time": fourth_info.get("finish_time"),
                        "responsible_person": fourth_info.get("responsible_person"),
                        "input": fourth_info.get("input"),
                        "output": fourth_info.get("output"),
                        "construction_attribute": fourth_info.get(
                            "construction_attribute"
                        ),
                        "actual_start_time": fourth_info.get("actual_start_time"),
                        "actual_finish_time": fourth_info.get("actual_finish_time"),
                        "actual_construction_time": fourth_info.get(
                            "actual_construction_time"
                        ),
                        "construction_time_deviation": fourth_info.get(
                            "construction_time_deviation"
                        ),
                        "schedule_deviation": fourth_info.get("schedule_deviation"),
                        "remark": fourth_info.get("remark"),
                    }
                )
        if third_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_third_level", third_level_into_insert
            )
            tb_db.commit()
        if fourth_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_fourth_level", fourth_level_into_insert
            )
            tb_db.commit()
        variables = {
            "third_level_into_insert": third_level_into_insert,
            "fourth_level_into_insert": fourth_level_into_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def mains_power_plan_third_level_into_db(self):
        """
        建设流程-施工准备阶段-2.9市电专项-三级标题、四级标题
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        # 市电专项-二级
        sdzx_list = self.ctx.variables.get("sdzx_list")
        # 外电报装
        wdbz_dict = self.ctx.variables.get("wdbz_dict")
        # 外电方案
        wdfa_dict = self.ctx.variables.get("wdfa_dict")
        # 高可靠费缴费
        gkkf_dict = self.ctx.variables.get("gkkf_dict")
        # 供电合同签订
        gdht_dict = self.ctx.variables.get("gdht_dict")
        # 调度协议签订
        ddxy_dict = self.ctx.variables.get("ddxy_dict")
        # 送电前检测
        sdqjc_dict = self.ctx.variables.get("sdqjc_dict")
        # 送电
        sd_dict = self.ctx.variables.get("sd_dict")

        third_level_list_temp = [
            wdbz_dict,
            wdfa_dict,
            gkkf_dict,
            gdht_dict,
            ddxy_dict,
            sdqjc_dict,
            sd_dict,
        ]

        third_level_into_insert = []
        fourth_level_into_insert = []
        for third_info in third_level_list_temp:
            third_level_into_insert.append(
                {
                    "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                    "construction_application_process_ticket_id": ticket_id,
                    "project_name": project_name,
                    "second_seq_no": sdzx_list[0]["seq_no"] if sdzx_list else None,
                    "second_work_content": (
                        sdzx_list[0]["work_content"] if sdzx_list else None
                    ),
                    "seq_no": third_info.get("seq_no"),
                    "work_content": third_info.get("work_content"),
                    "construction_time": third_info.get("construction_time"),
                    "start_time": third_info.get("start_time"),
                    "finish_time": third_info.get("finish_time"),
                    "responsible_person": third_info.get("responsible_person"),
                    "input": third_info.get("input"),
                    "output": third_info.get("output"),
                    "construction_attribute": third_info.get("construction_attribute"),
                    "upload_file_json": json.dumps(
                        third_info.get("upload_file_json"), ensure_ascii=False
                    ),
                    "remark": third_info.get("remark"),
                    "actual_start_time": third_info.get("actual_start_time"),
                    "actual_finish_time": third_info.get("actual_finish_time"),
                }
            )
            for fourth_info in third_info.get("work_list"):
                fourth_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "construction_application_process_ticket_id": ticket_id,
                        "project_name": project_name,
                        "second_seq_no": sdzx_list[0]["seq_no"] if sdzx_list else None,
                        "second_work_content": (
                            sdzx_list[0]["work_content"] if sdzx_list else None
                        ),
                        "third_seq_no": third_info.get("seq_no"),
                        "third_work_content": third_info.get("work_content"),
                        "seq_no": fourth_info.get("seq_no"),
                        "work_content": fourth_info.get("work_content"),
                        "construction_time": fourth_info.get("construction_time"),
                        "start_time": fourth_info.get("start_time"),
                        "finish_time": fourth_info.get("finish_time"),
                        "responsible_person": fourth_info.get("responsible_person"),
                        "input": fourth_info.get("input"),
                        "output": fourth_info.get("output"),
                        "construction_attribute": fourth_info.get(
                            "construction_attribute"
                        ),
                        "actual_start_time": fourth_info.get("actual_start_time"),
                        "actual_finish_time": fourth_info.get("actual_finish_time"),
                        "actual_construction_time": fourth_info.get(
                            "actual_construction_time"
                        ),
                        "construction_time_deviation": fourth_info.get(
                            "construction_time_deviation"
                        ),
                        "schedule_deviation": fourth_info.get("schedule_deviation"),
                        "remark": fourth_info.get("remark"),
                    }
                )
        if third_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_third_level", third_level_into_insert
            )
            tb_db.commit()
        if fourth_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_fourth_level", fourth_level_into_insert
            )
            tb_db.commit()
        variables = {
            "third_level_into_insert": third_level_into_insert,
            "fourth_level_into_insert": fourth_level_into_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def tzss_info_update(self):
        """
        存量项目图纸送审节点信息修改
        """
        # 图纸送审
        tzss_dict = self.ctx.variables.get("tzss_dict")
        if tzss_dict:
            tb_db = mysql.new_mysql_instance("tbconstruct")
            # 三级内容
            update_data = {
                "upload_file_json": json.dumps(
                    tzss_dict.get("upload_file_json"), ensure_ascii=False
                ),
                "remark": tzss_dict.get("remark"),
            }
            tb_db.begin()
            tb_db.update_by_id("construction_application_third_level", update_data, id)
            tb_db.commit()
            # 四级内容
            work_list = tzss_dict.get("work_list")
            for item in work_list:
                f_id = item.get("id")
                if f_id:
                    f_update_data = {
                        "actual_start_time": item.get("actual_start_time"),
                        "actual_finish_time": item.get("actual_finish_time"),
                        "actual_construction_time": item.get(
                            "actual_construction_time"
                        ),
                        "construction_time_deviation": item.get(
                            "construction_time_deviation"
                        ),
                        "schedule_deviation": item.get("schedule_deviation"),
                        "remark": item.get("remark"),
                    }
                    tb_db.begin()
                    tb_db.update_by_id(
                        "construction_application_fourth_level", f_update_data, f_id
                    )
                    tb_db.commit()
        variables = {
            "tzss_third_level_update_data": update_data,
            "tzss_four_level_update_data": f_update_data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def tzss_info_insert(self):
        """
        存量项目图纸送审节点信息修改（新存库）
        """
        # 总控计划工单号
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        # 项目报建流程工单号
        construction_application_process_ticket_id = self.ctx.variables.get(
            "construction_application_process_ticket_id"
        )
        # 图纸送审补单流程工单号
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        # 项目报建
        xmbj_list = self.ctx.variables.get("xmbj_list")
        # 图纸送审
        tzss_dict = self.ctx.variables.get("tzss_dict")
        third_level_into_insert = []
        fourth_level_into_insert = []
        third_level_into_insert.append(
            {
                "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                "construction_application_process_ticket_id": construction_application_process_ticket_id,
                "ticket_id": ticket_id,
                "project_name": project_name,
                "second_seq_no": xmbj_list[0]["seq_no"] if xmbj_list else None,
                "second_work_content": (
                    xmbj_list[0]["work_content"] if xmbj_list else None
                ),
                "seq_no": tzss_dict.get("seq_no"),
                "work_content": tzss_dict.get("work_content"),
                "construction_time": tzss_dict.get("construction_time"),
                "start_time": tzss_dict.get("start_time"),
                "finish_time": tzss_dict.get("finish_time"),
                "responsible_person": tzss_dict.get("responsible_person"),
                "input": tzss_dict.get("input"),
                "output": tzss_dict.get("output"),
                "construction_attribute": tzss_dict.get("construction_attribute"),
                "upload_file_json": json.dumps(
                    tzss_dict.get("upload_file_json"), ensure_ascii=False
                ),
                "remark": tzss_dict.get("remark"),
                "actual_start_time": tzss_dict.get("actual_start_time"),
                "actual_finish_time": tzss_dict.get("actual_finish_time"),
            }
        )
        for fourth_info in tzss_dict.get("work_list"):
            fourth_level_into_insert.append(
                {
                    "overall_control_plan_ticket_id": xmbj_list[0][
                        "overall_control_plan_ticket_id"
                    ],
                    "construction_application_process_ticket_id": construction_application_process_ticket_id,
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "second_seq_no": xmbj_list[0]["seq_no"] if xmbj_list else None,
                    "second_work_content": (
                        xmbj_list[0]["work_content"] if xmbj_list else None
                    ),
                    "third_seq_no": tzss_dict.get("seq_no"),
                    "third_work_content": tzss_dict.get("work_content"),
                    "seq_no": fourth_info.get("seq_no"),
                    "work_content": fourth_info.get("work_content"),
                    "construction_time": fourth_info.get("construction_time"),
                    "start_time": fourth_info.get("start_time"),
                    "finish_time": fourth_info.get("finish_time"),
                    "responsible_person": fourth_info.get("responsible_person"),
                    "input": fourth_info.get("input"),
                    "output": fourth_info.get("output"),
                    "construction_attribute": fourth_info.get("construction_attribute"),
                    "actual_start_time": fourth_info.get("actual_start_time"),
                    "actual_finish_time": fourth_info.get("actual_finish_time"),
                    "actual_construction_time": fourth_info.get(
                        "actual_construction_time"
                    ),
                    "construction_time_deviation": fourth_info.get(
                        "construction_time_deviation"
                    ),
                    "schedule_deviation": fourth_info.get("schedule_deviation"),
                    "remark": fourth_info.get("remark"),
                }
            )
        if third_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_third_level_append", third_level_into_insert
            )
            tb_db.commit()
        if fourth_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_fourth_level_append", fourth_level_into_insert
            )
            tb_db.commit()
        variables = {
            "third_level_into_insert": third_level_into_insert,
            "fourth_level_into_insert": fourth_level_into_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def tzss_feedback_into_db(
            self, project_name, project_code, tzss_dict, tzss_PM_file, tzss_PM_remark
    ):
        """
        图纸送审状态反馈数据入库(construction_feedback_to_xingchen)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account = self.ctx.variables.get("PM")
        files_json = Tools.get_files_json(tzss_PM_file)
        insert_list = []
        insert_list.append(
            {
                "project_name": project_name,
                "project_code": project_code,
                "ticket_id": ticket_id,
                "type": "图纸已送审",
                "actual_date": tzss_dict.get("actual_finish_time"),
                "account": account,
                "files_json": json.dumps(files_json, ensure_ascii=False),
                "remark": tzss_PM_remark,
            }
        )
        if insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_feedback_to_xingchen", insert_list)
            tb_db.commit()
        # 获取待办处理人（竣工备案、消防备案）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, "项管-项目经理"
        )
        variables = {
            "insert_list": insert_list,
            "xfba_responsible_person": todo_handler,
            "jgba_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def joint_project_construction_application_third_level_into_db(self):
        """
        合建项目报建管理2.4项目报建-三级标题、四级标题
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        # 项目报建-二级
        current_section = self.ctx.variables.get("current_section")
        current_work = self.ctx.variables.get("current_work")
        # 项目立项,设计合同签订上传,图纸送审
        xmlx_dict = self.ctx.variables.get("xmlx_dict")
        # 监理直接发包、上传
        jlfb_dict = self.ctx.variables.get("jlfb_dict")
        # 施工许可证下发
        sgxkz_dict = self.ctx.variables.get("sgxkz_dict")
        # 竣工备案
        jgba_dict = self.ctx.variables.get("jgba_dict")
        # 消防备案
        xfba_dict = self.ctx.variables.get("xfba_dict")

        # 上传消防报验计划 消防工程第三方检测报告

        todo_handler = self.ctx.variables.get("todo_handler")

        third_level_list_temp = [
            xmlx_dict,
            jlfb_dict,
            sgxkz_dict,
            jgba_dict,
            xfba_dict,
        ]

        third_level_into_insert = []
        for third_info in third_level_list_temp:
            for current_content in third_info.get("work_list"):
                third_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "construction_application_process_ticket_id": ticket_id,
                        "project_name": project_name,
                        "second_seq_no": current_section if current_section else "",
                        "second_work_content": current_work if current_work else "",
                        "seq_no": current_content.get("seq_no"),
                        "work_content": current_content.get("work_content"),
                        "construction_time": current_content.get("construction_time"),
                        "start_time": current_content.get("start_time"),
                        "finish_time": current_content.get("finish_time"),
                        "responsible_person": todo_handler if todo_handler else "",
                        "input": current_content.get("input"),
                        "output": current_content.get("output"),
                        "construction_attribute": current_content.get("construction_attribute"),
                        "upload_file_json": json.dumps(
                            current_content.get("other_file"), ensure_ascii=False
                        ) if current_content.get("other_file") else "",
                        "remark": current_content.get("remark"),
                        "actual_start_time": current_content.get("actual_start_time"),
                        "actual_finish_time": current_content.get("actual_finish_time"),
                    }
                )
        if third_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_third_level", third_level_into_insert
            )
            tb_db.commit()
        variables = {
            "third_level_into_insert": third_level_into_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def joint_mains_power_plan_third_level_into_db(self):
        """
        合建项目市电专项2.9市电专项-三级标题、四级标题
        """
        overall_control_plan_ticket_id = self.ctx.variables.get(
            "overall_control_plan_ticket_id"
        )
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        # 项目报建-二级
        current_section = self.ctx.variables.get("current_section")
        current_work = self.ctx.variables.get("current_work")
        wdbz_dict = self.ctx.variables.get("wdbz_dict")

        # 上传消防报验计划 消防工程第三方检测报告

        todo_handler = self.ctx.variables.get("todo_handler")

        third_level_list_temp = [
            wdbz_dict,
        ]

        third_level_into_insert = []
        for third_info in third_level_list_temp:
            for current_content in third_info.get("work_list"):
                third_level_into_insert.append(
                    {
                        "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
                        "construction_application_process_ticket_id": ticket_id,
                        "project_name": project_name,
                        "second_seq_no": current_section if current_section else "",
                        "second_work_content": current_work if current_work else "",
                        "seq_no": current_content.get("seq_no"),
                        "work_content": current_content.get("work_content"),
                        "construction_time": current_content.get("construction_time"),
                        "start_time": current_content.get("start_time"),
                        "finish_time": current_content.get("finish_time"),
                        "responsible_person": todo_handler if todo_handler else "",
                        "input": current_content.get("input"),
                        "output": current_content.get("output"),
                        "construction_attribute": current_content.get("construction_attribute"),
                        "upload_file_json": json.dumps(
                            current_content.get("other_file"), ensure_ascii=False
                        ) if current_content.get("other_file") else "",
                        "remark": current_content.get("remark"),
                        "actual_start_time": current_content.get("actual_start_time"),
                        "actual_finish_time": current_content.get("actual_finish_time"),
                    }
                )
        if third_level_into_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "construction_application_third_level", third_level_into_insert
            )
            tb_db.commit()
        variables = {
            "third_level_into_insert": third_level_into_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
