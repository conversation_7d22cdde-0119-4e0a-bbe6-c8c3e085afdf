


import json

import uuid
import pandas as pd
import requests
import time

import openpyxl
import sseclient

import pandas as pd

from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

class HunyuanQueryApi(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def process(self,query, k, forward_service,messages):
        # ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:1081/openapi/app_platform/app_create"
        ss_url = "http://stream-server-online-hyaide-app.turbotke.production.polaris:81/openapi/app_platform/app_create"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer 7auGXNATFSKl7dF",
        }

        json_data = {
            "query_id": "test_query_id_" + str(uuid.uuid4()),
            "query": query,   # modlity_query 用modality_query替换query将触发多模态，
            "forward_service": forward_service,
            "messages": messages,
            "temperature": 0.9,
            "top_p": 0.95,
            "top_k": k,
            "repetition_penalty": 1,
            "output_seq_len": 5102,
            "max_input_seq_len": 2048,
            "stream": True,
            "compatible_with_openai": True,
        }
        resp = requests.post(ss_url, headers=headers, json=json_data, stream=True)
        client = sseclient.SSEClient(resp)
        think_data = ""
        result_data = ""
        for event in client.events():
            if event.data != '':
                data_js = json.loads(event.data)
                try:
                    think_data += data_js['choices'][0]['delta']['reasoning_content']
                    result_data += data_js['choices'][0]['delta']['content']
                    #   print(data_js['choices'][0]['delta']['content'], end='', flush=True)
                except Exception as e:
                    if 'event' in data_js:
                        if data_js['event'].get('name', '') == 'thinking' and data_js['event'].get('state', -1) == 0:
                            print('开始思考')
                            continue
                        elif data_js['event'].get('name', '') == 'thinking' and data_js['event'].get('state', -1) == 2:
                            print('\n结束思考')
                            continue

        return think_data,result_data

    def query_procurement_ai(self,question,history):
        k = 3
        forward_service = "hyaide-application-12757"
        think_data,result_data = self.process(question,k,forward_service,history)
        return {
            "think_data": think_data,
            "result_data": result_data
        }
    
if __name__ == "__main__":
    api = HunyuanQueryApi()
    question = "德衡的IT方舱处理方案是什么？"
    history = []
    think_data,result_data = api.query_procurement_ai(question,history)
    print(think_data)
    print(result_data)
