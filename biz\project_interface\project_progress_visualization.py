from iBroker.lib import mysql


class ProgressVisualization(object):
    def regional_progress_calculation(self, project_name):
        """
            区域进度计算
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT sm.serial_number, sm.task_name, " \
                    "sm.first_level_proportion, sm.second_level_proportion, sm.third_level_proportion, " \
                    "sm.first_progress, sm.second_progress, sm.third_progress, sm.now_time " \
                    "FROM week_create_info wci " \
                    "JOIN schedule_management sm ON wci.ticketid = sm.ticketid " \
                    f"WHERE wci.project_name = '{project_name}' " \
                    "AND sm.now_time = (" \
                    "SELECT MAX(now_time) " \
                    "FROM schedule_management " \
                    "WHERE ticketid " \
                    "IN ( " \
                    "SELECT ticketid " \
                    "FROM week_create_info " \
                    f"WHERE project_name = '{project_name}'))"
        result = db.get_all(query_sql)
        first_list = []
        second_list = []
        third_list = []
        for row in result:
            serial_number = row.get('serial_number', None)
            if serial_number is not None and serial_number.count('.') == 0:
                first_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 1:
                second_list.append(row)
            elif serial_number is not None and serial_number.count('.') == 2:
                third_list.append(row)
        second_third_mapping = {}
        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            second_title = second_row['task_name']
            second_third_mapping[second_serial_number] = {'title': second_title, 'third_list': []}
        for third_row in third_list:
            third_serial_number = third_row['serial_number']
            second_serial_number = '.'.join(third_serial_number.split('.')[:-1])
            if second_serial_number in second_third_mapping:
                second_third_mapping[second_serial_number]['third_list'].append(third_row)
        previous_second_progress = None
        data = {}
        label_mapping = {
            '柴发和水处理区域': '柴发和水处理区域',
            'AHU区域': 'AHU区域',
            'IT Block1/电池间': 'IT Block1/电池间',
            'IT Block2/电池间': 'IT Block2/电池间',
            'IT Block3/电池间': 'IT Block3/电池间',
            'IT Block4/电池间': 'IT Block4/电池间',
            '电力走廊': '电力走廊',
            '办公区域': '办公区域'
        }

        for second_row in second_list:
            second_serial_number = second_row['serial_number']
            if second_serial_number.startswith('3'):  # 限制只返回序号以3开头的数据
                second_data = second_third_mapping.get(second_serial_number)
                if second_data:
                    third_list = second_data['third_list']
                    second_progress = 0  # 将二级标题进度的初始值设置为 0
                    total_third_proportion = 0
                    valid_third_count = 0
                    for third_row in third_list:
                        third_progress = third_row.get('third_progress', 0)
                        third_proportion = third_row.get('third_level_proportion', None)
                        if isinstance(third_progress, (int, float)):
                            if third_proportion is not None:
                                if third_proportion != 0:
                                    second_progress += third_progress * third_proportion
                                    total_third_proportion += third_proportion
                                else:
                                    second_progress += third_progress
                            else:
                                second_progress += third_progress
                            valid_third_count += 1
                    if valid_third_count == 0:
                        second_progress = second_row.get('third_progress', 0)  # 如果没有有效的三级标题，直接取二级标题的进度，初始值为 0
                    else:
                        if total_third_proportion != 0:
                            second_progress /= total_third_proportion
                        else:
                            second_progress /= valid_third_count
                    if previous_second_progress is not None and second_progress is not None:
                        if second_progress < previous_second_progress:
                            second_progress = previous_second_progress
                        else:
                            previous_second_progress = second_progress
                    if second_progress > 1.0:
                        second_progress = 1.0
                    label = second_row['task_name']
                    if second_serial_number.startswith('3'):  # 限制只返回二级标题序号为3开头的label
                        for key in label_mapping:
                            if key[:9] == label[:9]:
                                label = label_mapping[key]
                                break
                        data[label] = {
                            'seq': second_serial_number,
                            'name': label,
                            'progress': f'{second_progress * 100:.2f}%'
                        }
        return data
