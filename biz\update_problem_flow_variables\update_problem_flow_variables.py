from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowVarUpdate


class UpdateProblemFlowVariables(object):
    """
    刷新流程变量
    """

    def update_jl_account(self, ticket_id_list):
        """
        南京江宁B4-1, 将监理周洪聪，刷成丰贵刚
        """
        # 获取工单号
        if ticket_id_list:
            if type(ticket_id_list) is list:
                # 获取InstanceId
                query_data = {
                    "SchemaId": "ticket_base",
                    "Data": {
                        "ResultColumns": {"InstanceId": "", "TicketId": ""},
                        "SearchCondition": {"TicketId": ticket_id_list},
                    },
                }
                query_result = gnetops.request(
                    action="QueryData", method="Run", ext_data=query_data
                )
                result_list = query_result.get("List")
                if result_list:
                    success_list = []
                    # 获取流程变量
                    for result in result_list:
                        ticket_id = result.get("TicketId")
                        instance_id = result.get("InstanceId")
                        main_flow_vars = flow.get_variables(
                            instance_id,
                            ["jl_account", "jl_list"],
                        )
                        # 处理流程变量
                        new_jl_account = main_flow_vars.get("jl_account", "")
                        new_jl_list = main_flow_vars.get("jl_list", [])
                        if new_jl_account == "Js_zhouhongcong":
                            new_jl_account = "Js_fengguigang"
                        for i in range(len(new_jl_list)):
                            if new_jl_list[i] == "Js_zhouhongcong":
                                new_jl_list[i] = "Js_fengguigang"
                        variables = {
                            "jl_account": new_jl_account,
                            "jl_list": new_jl_list,
                        }
                        # 修改流程变量
                        WorkflowVarUpdate(
                            instance_id=instance_id, variables=variables
                        ).call()
                        success_list.append(ticket_id)
                    return {"code": 200, "msg": f"流程变量：{success_list}修改成功"}
                else:
                    return {"code": 400, "msg": "未查询到instance_id"}
            else:
                return {"code": 400, "msg": "工单号格式错误"}
        else:
            return {"code": 400, "msg": "工单号为空"}
