import base64
import os
import uuid

from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class EditApprovalPage(AjaxTodoBase):
    """
        编辑审批页面
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        table = process_data.get('table')
        remark = process_data.get('remark')
        supplier_list = process_data.get('supplier_list')
        file_name = None
        adjustment_part = None
        attachment_uploading_url = []
        technical_document_url = []
        technical_document_table = []
        attachment_uploading_table = []
        if table:
            for row in table:
                if 'file_name' in row:
                    file_name = row['file_name']
                if 'adjustment_part' in row:
                    adjustment_part = row['adjustment_part']
        attachment_uploading = process_data.get("attachment_uploading", None)
        technical_document = process_data.get("technical_document", None)
        if technical_document is not None:
            for doc in technical_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        technical_document_url.append(url)
                        technical_document_table.append({
                            "technical_document": url,
                            'technical_document_name': name
                        })
        if attachment_uploading is not None:
            for doc in attachment_uploading:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        attachment_uploading_url.append(url)
                        attachment_uploading_table.append({
                            "attachment_uploading": url,
                            'attachment_uploading_name': name
                        })
        technical_document_join = ', '.join(attachment_uploading_url)
        bid_evaluation_standard_join = ', '.join(technical_document_url)
        insert_data = {}
        if attachment_uploading and len(attachment_uploading) > 0 and 'response' in \
                attachment_uploading[0]:
            insert_data = {
                'file_name': file_name,
                'adjustment_part': adjustment_part,
                'coverage_area': process_data['coverage_area'],
                'supplier_list': ','.join(supplier_list),
                'bid_evaluation_standard': bid_evaluation_standard_join,
                'technical_document': technical_document_join,
                'evaluation_weight': process_data['evaluation_weight'],
                'attachment_uploading_url': attachment_uploading_url,
                'dcops_ticket_id': ticket_id
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")

        tb_db.begin()
        tb_db.insert("approval_information", insert_data)
        tb_db.commit()
        variables = {
            'file_name': file_name,
            'adjustment_part': adjustment_part,
            'table': table,
            'remark': remark,
            'coverage_area': process_data['coverage_area'],
            'supplier_list': supplier_list,
            'attachment_uploading_table': attachment_uploading_table,
            'technical_document_table': technical_document_table,
            'evaluation_weight': process_data['evaluation_weight']
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToEddiewu(AjaxTodoBase):
    """
        发送信息给eddiewu
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        leader_approval = process_data.get("leader_approval", None)
        leader_remarks = process_data.get("leader_remarks", '无')
        update_data = {
            'leader_approval': leader_approval,
            'leader_remarks': leader_remarks
        }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")

        tb_db.begin()
        tb_db.update("approval_information", update_data, conditions)
        tb_db.commit()
        variables = {
            'leader_approval': leader_approval,
            'leader_remarks': leader_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToDirector(AjaxTodoBase):
    """
        发送信息给总监
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        director_approval = process_data.get("director_approval", '')
        director_remarks = process_data.get("director_remarks", '无')
        update_data = {
            'director_approval': director_approval,
            'director_remarks': director_remarks
        }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")

        tb_db.begin()
        tb_db.update("approval_information", update_data, conditions)
        tb_db.commit()
        variables = {
            'director_approval': director_approval,
            'director_remarks': director_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToGM(AjaxTodoBase):
    """
        发送信息给GM
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        GM_approval = process_data.get("GM_approval", '')
        GM_remarks = process_data.get("GM_remarks", '无')
        update_data = {
            'GM_approval': GM_approval,
            'GM_remarks': GM_remarks
        }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")

        tb_db.begin()
        tb_db.update("approval_information", update_data, conditions)
        tb_db.commit()
        variables = {
            'GM_approval': GM_approval,
            'GM_remarks': GM_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EmailInformationEditor(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        email_Cc = process_data.get("email_Cc")
        email_receiver = process_data.get("email_receiver")
        email_title = process_data.get("email_title")
        rich_text_template = process_data.get("rich_text_template")
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"

        variables = {
            'email_Cc': email_Cc,
            'email_receiver': email_receiver,
            'rich_text_template': rich_text_template,
            'email_Cc_list': email_Cc_list,
            'email_receiver_list': email_receiver_list,
            'email_title': email_title
        }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class EmailPushing(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def send_email(self):
        """
            邮件发送
        """
        attachment_uploading_table = self.ctx.variables.get('attachment_uploading_table')
        technical_document_table = self.ctx.variables.get('technical_document_table')
        email_Cc_list = self.ctx.variables.get("email_Cc_list")
        email_receiver_list = self.ctx.variables.get("email_receiver_list")
        rich_text_template = self.ctx.variables.get("rich_text_template")
        email_title = self.ctx.variables.get("email_title")
        # 指定保存文件的完整路径
        save_directory = '/path/to/save'
        os.makedirs(save_directory, exist_ok=True)  # 创建保存路径

        attachment_dict = {}  # 定义空字典
        technical_dict = {}  # 定义空字典
        combined_dict = {}  # 定义空字典

        for technical in technical_document_table:
            technical_name = technical["technical_document_name"]
            technical_url = technical["technical_document"]

            relative_url = technical_url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            save_file_path = os.path.join(save_directory, new_file_name)

            COSLib.download_2(file_name=relative_url, save_to_file=save_file_path)

            technical_dict[save_file_path] = technical_name  # 将附件添加到字典中

        for attachment in attachment_uploading_table:
            attachment_name = attachment["attachment_uploading_name"]
            attachment_url = attachment["attachment_uploading"]

            relative_url = attachment_url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            save_file_path = os.path.join(save_directory, new_file_name)

            COSLib.download_2(file_name=relative_url, save_to_file=save_file_path)

            attachment_dict[save_file_path] = attachment_name  # 将附件添加到字典中

        combined_dict.update(technical_dict)
        combined_dict.update(attachment_dict)

        tof.send_attachment_email(filePathAndfileName=combined_dict, sendTitle=email_title,
                                  msgContent=rich_text_template, sendTo=email_receiver_list, sendCopy=email_Cc_list)

        variables = {
            'attachment_dict': attachment_dict,
        }

        flow.complete_task(self.ctx.task_id, variables=variables)
