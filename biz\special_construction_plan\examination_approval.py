import datetime
import re

from iBroker.lib.sdk import gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql
from biz.project_name import Database


class DlzlApprovalFlow(AjaxTodoBase):
    """
        电力走廊专项方案上传
    """

    def __init__(self):
        super(DlzlApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dlzl_special_construction_plan_document_url = []
        dlzl_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        dlzl_task_name = process_data.get("dlzl_task_name", None)
        dlzl_plan_start_time = process_data.get("dlzl_plan_start_time", None)
        dlzl_plan_finish_time = process_data.get("dlzl_plan_finish_time", None)
        dlzl_responsible_person = process_data.get("dlzl_responsible_person", None)
        # 文件
        dlzl_special_construction_plan_document = process_data.get("dlzl_special_construction_plan_document", None)
        # 电力走廊专项方案文件
        if dlzl_special_construction_plan_document is not None:
            for doc in dlzl_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        dlzl_special_construction_plan_document_url.append(url)
                        dlzl_special_construction_plan_document_table.append({
                            "dlzl_special_construction_plan_document": url,
                            'dlzl_special_construction_plan_document_name': name
                        })
        dlzl_special_construction_plan_document_join = ', '.join(dlzl_special_construction_plan_document_url)
        dlzl_insert_data = {
            'project_name': project_name,
            'task_name': dlzl_task_name,
            'start_time': dlzl_plan_start_time,
            'finish_time': dlzl_plan_finish_time,
            'responsible_person': dlzl_responsible_person,
            'special_construction_plan_document': dlzl_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'dlzl_special_construction_plan_document_table': dlzl_special_construction_plan_document_table,
            'dlzl_insert_data': dlzl_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItOneApprovalFlow(AjaxTodoBase):
    """
        it 1 专项方案上传
    """

    def __init__(self):
        super(ItOneApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        IT_one_special_construction_plan_document_url = []
        IT_one_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        IT_one_task_name = process_data.get("IT_one_task_name", None)
        IT_one_plan_start_time = process_data.get("IT_one_plan_start_time", None)
        IT_one_plan_finish_time = process_data.get("IT_one_plan_finish_time", None)
        block1_responsible_person = process_data.get("block1_responsible_person", None)
        IT_one_special_construction_plan_document = process_data.get("IT_one_special_construction_plan_document", None)
        # it 1 专项方案文件
        if IT_one_special_construction_plan_document is not None:
            for doc in IT_one_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        IT_one_special_construction_plan_document_url.append(url)
                        IT_one_special_construction_plan_document_table.append({
                            "IT_one_special_construction_plan_document": url,
                            'IT_one_special_construction_plan_document_name': name
                        })
        IT_one_special_construction_plan_document_join = ', '.join(IT_one_special_construction_plan_document_url)

        IT_one_insert_data = {
            'project_name': project_name,
            'task_name': IT_one_task_name,
            'start_time': IT_one_plan_start_time,
            'finish_time': IT_one_plan_finish_time,
            'responsible_person': block1_responsible_person,
            'special_construction_plan_document': IT_one_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'IT_one_special_construction_plan_document_table': IT_one_special_construction_plan_document_table,
            'IT_one_insert_data': IT_one_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItTwoApprovalFlow(AjaxTodoBase):
    """
        it 2 专项方案上传
    """

    def __init__(self):
        super(ItTwoApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        IT_two_special_construction_plan_document_url = []
        IT_two_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        IT_two_task_name = process_data.get("IT_two_task_name", None)
        IT_two_plan_start_time = process_data.get("IT_two_plan_start_time", None)
        IT_two_plan_finish_time = process_data.get("IT_two_plan_finish_time", None)
        block2_responsible_person = process_data.get("block2_responsible_person", None)
        IT_two_special_construction_plan_document = process_data.get("IT_two_special_construction_plan_document", None)
        # it 2 方案文件
        if IT_two_special_construction_plan_document is not None:
            for doc in IT_two_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        IT_two_special_construction_plan_document_url.append(url)
                        IT_two_special_construction_plan_document_table.append({
                            "IT_two_special_construction_plan_document": url,
                            'IT_two_special_construction_plan_document_name': name
                        })
        IT_two_special_construction_plan_document_join = ', '.join(IT_two_special_construction_plan_document_url)

        IT_two_insert_data = {
            'project_name': project_name,
            'task_name': IT_two_task_name,
            'start_time': IT_two_plan_start_time,
            'finish_time': IT_two_plan_finish_time,
            'responsible_person': block2_responsible_person,
            'special_construction_plan_document': IT_two_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'IT_two_special_construction_plan_document_table': IT_two_special_construction_plan_document_table,
            'IT_two_insert_data': IT_two_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItThreeApprovalFlow(AjaxTodoBase):
    """
        it 3 专项方案上传
    """

    def __init__(self):
        super(ItThreeApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        IT_three_special_construction_plan_document_url = []
        IT_three_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        IT_three_task_name = process_data.get("IT_three_task_name", None)
        IT_three_plan_start_time = process_data.get("IT_three_plan_start_time", None)
        IT_three_plan_finish_time = process_data.get("IT_three_plan_finish_time", None)
        block3_responsible_person = process_data.get("block3_responsible_person", None)
        IT_three_special_construction_plan_document = process_data.get("IT_three_special_construction_plan_document",
                                                                       None)
        # it 3 方案文件
        if IT_three_special_construction_plan_document is not None:
            for doc in IT_three_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        IT_three_special_construction_plan_document_url.append(url)
                        IT_three_special_construction_plan_document_table.append({
                            "IT_three_special_construction_plan_document": url,
                            'IT_three_special_construction_plan_document_name': name
                        })
        IT_three_special_construction_plan_document_join = ', '.join(IT_three_special_construction_plan_document_url)

        IT_three_insert_data = {
            'project_name': project_name,
            'task_name': IT_three_task_name,
            'start_time': IT_three_plan_start_time,
            'finish_time': IT_three_plan_finish_time,
            'responsible_person': block3_responsible_person,
            'special_construction_plan_document': IT_three_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'IT_three_special_construction_plan_document_table': IT_three_special_construction_plan_document_table,
            'IT_three_insert_data': IT_three_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItFourApprovalFlow(AjaxTodoBase):
    """
        it 4 专项方案上传
    """

    def __init__(self):
        super(ItFourApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        IT_four_special_construction_plan_document_url = []
        IT_four_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        IT_four_task_name = process_data.get("IT_four_task_name", None)
        IT_four_plan_start_time = process_data.get("IT_four_plan_start_time", None)
        IT_four_plan_finish_time = process_data.get("IT_four_plan_finish_time", None)
        block4_responsible_person = process_data.get("block4_responsible_person", None)
        IT_four_special_construction_plan_document = process_data.get("IT_four_special_construction_plan_document",
                                                                      None)
        # it 4 方案文件
        if IT_four_special_construction_plan_document is not None:
            for doc in IT_four_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        IT_four_special_construction_plan_document_url.append(url)
                        IT_four_special_construction_plan_document_table.append({
                            "IT_four_special_construction_plan_document": url,
                            'IT_four_special_construction_plan_document_name': name
                        })
        IT_four_special_construction_plan_document_join = ', '.join(IT_four_special_construction_plan_document_url)

        IT_four_insert_data = {
            'project_name': project_name,
            'task_name': IT_four_task_name,
            'start_time': IT_four_plan_start_time,
            'finish_time': IT_four_plan_finish_time,
            'responsible_person': block4_responsible_person,
            'special_construction_plan_document': IT_four_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'IT_four_special_construction_plan_document_table': IT_four_special_construction_plan_document_table,
            'IT_four_insert_data': IT_four_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfApprovalFlow(AjaxTodoBase):
    """
        柴发 专项方案上传
    """

    def __init__(self):
        super(CfApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        cf_special_construction_plan_document_url = []
        cf_special_construction_plan_document_table = []
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        cf_task_name = process_data.get("cf_task_name", None)
        cf_plan_start_time = process_data.get("cf_plan_start_time", None)
        cf_plan_finish_time = process_data.get("cf_plan_finish_time", None)
        cfscl_responsible_person = process_data.get("cfscl_responsible_person", None)
        cf_special_construction_plan_document = process_data.get("cf_special_construction_plan_document", None)

        # 柴发专项方案文件
        if cf_special_construction_plan_document is not None:
            for doc in cf_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        cf_special_construction_plan_document_url.append(url)
                        cf_special_construction_plan_document_table.append({
                            "cf_special_construction_plan_document": url,
                            'cf_special_construction_plan_document_name': name
                        })
        cf_special_construction_plan_document_join = ', '.join(cf_special_construction_plan_document_url)

        cf_insert_data = {
            'project_name': project_name,
            'task_name': cf_task_name,
            'start_time': cf_plan_start_time,
            'finish_time': cf_plan_finish_time,
            'responsible_person': cfscl_responsible_person,
            'special_construction_plan_document': cf_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'cf_special_construction_plan_document_table': cf_special_construction_plan_document_table,
            'cf_insert_data': cf_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AHUApprovalFlow(AjaxTodoBase):
    """
        AHU 专项方案上传
    """

    def __init__(self):
        super(AHUApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        AHU_special_construction_plan_document_url = []
        AHU_special_construction_plan_document_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        AHU_task_name = process_data.get("AHU_task_name", None)
        AHU_plan_start_time = process_data.get("AHU_plan_start_time", None)
        AHU_plan_finish_time = process_data.get("AHU_plan_finish_time", None)
        ahu_responsible_person = process_data.get("ahu_responsible_person", None)
        AHU_special_construction_plan_document = process_data.get("AHU_special_construction_plan_document", None)

        # AHU专项方案文件
        if AHU_special_construction_plan_document is not None:
            for doc in AHU_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        AHU_special_construction_plan_document_url.append(url)
                        AHU_special_construction_plan_document_table.append({
                            "AHU_special_construction_plan_document": url,
                            'AHU_special_construction_plan_document_name': name
                        })
        AHU_special_construction_plan_document_join = ', '.join(AHU_special_construction_plan_document_url)

        AHU_insert_data = {
            'task_name': AHU_task_name,
            'start_time': AHU_plan_start_time,
            'finish_time': AHU_plan_finish_time,
            'responsible_person': ahu_responsible_person,
            'special_construction_plan_document': AHU_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'AHU_special_construction_plan_document_table': AHU_special_construction_plan_document_table,
            'AHU_insert_data': AHU_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgApprovalFlow(AjaxTodoBase):
    """
        办公 专项方案上传
    """

    def __init__(self):
        super(BgApprovalFlow, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        bg_special_construction_plan_document_url = []
        bg_special_construction_plan_document_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        bg_task_name = process_data.get("bg_task_name", None)
        bg_plan_start_time = process_data.get("bg_plan_start_time", None)
        bg_plan_finish_time = process_data.get("bg_plan_finish_time", None)
        bg_responsible_person = process_data.get("bg_responsible_person", None)
        bg_special_construction_plan_document = process_data.get("bg_special_construction_plan_document", None)
        # 办公区域专项方案文件
        if bg_special_construction_plan_document is not None:
            for doc in bg_special_construction_plan_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        bg_special_construction_plan_document_url.append(url)
                        bg_special_construction_plan_document_table.append({
                            "bg_special_construction_plan_document": url,
                            'bg_special_construction_plan_document_name': name
                        })
        bg_special_construction_plan_document_join = ', '.join(bg_special_construction_plan_document_url)

        bg_insert_data = {
            'task_name': bg_task_name,
            'start_time': bg_plan_start_time,
            'finish_time': bg_plan_finish_time,
            'responsible_person': bg_responsible_person,
            'special_construction_plan_document': bg_special_construction_plan_document_join,
            'ticket_id': ticket_id
        }

        variables = {
            'bg_special_construction_plan_document_table': bg_special_construction_plan_document_table,
            'bg_insert_data': bg_insert_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DlzlSupervisionApproval(AjaxTodoBase):
    """
        电力走廊专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        dlzl_supervision_management_approval = process_data.get("dlzl_supervision_management_approval", None)
        dlzl_supervision_management_remark = process_data.get("dlzl_supervision_management_remark", None)

        variables = {
            'dlzl_supervision_management_approval': dlzl_supervision_management_approval,
            'dlzl_supervision_management_remark': dlzl_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItOneSupervisionApproval(AjaxTodoBase):
    """
        IT1 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        IT_one_supervision_management_approval = process_data.get("IT_one_supervision_management_approval", None)
        IT_one_supervision_management_remark = process_data.get("IT_one_supervision_management_remark", None)

        variables = {
            'IT_one_supervision_management_approval': IT_one_supervision_management_approval,
            'IT_one_supervision_management_remark': IT_one_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItTwoSupervisionApproval(AjaxTodoBase):
    """
        IT2 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        IT_two_supervision_management_approval = process_data.get("IT_two_supervision_management_approval", None)
        IT_two_supervision_management_remark = process_data.get("IT_two_supervision_management_remark", None)

        variables = {
            'IT_two_supervision_management_approval': IT_two_supervision_management_approval,
            'IT_two_supervision_management_remark': IT_two_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItThreeSupervisionApproval(AjaxTodoBase):
    """
        IT3 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        IT_three_supervision_management_approval = process_data.get("IT_three_supervision_management_approval", None)
        IT_three_supervision_management_remark = process_data.get("IT_three_supervision_management_remark", None)

        variables = {
            'IT_three_supervision_management_approval': IT_three_supervision_management_approval,
            'IT_three_supervision_management_remark': IT_three_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ItFourSupervisionApproval(AjaxTodoBase):
    """
        IT4 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        IT_four_supervision_management_approval = process_data.get("IT_four_supervision_management_approval", None)
        IT_four_supervision_management_remark = process_data.get("IT_four_supervision_management_remark", None)

        variables = {
            'IT_four_supervision_management_approval': IT_four_supervision_management_approval,
            'IT_four_supervision_management_remark': IT_four_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfSupervisionApproval(AjaxTodoBase):
    """
        柴发和水处理 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        cf_supervision_management_approval = process_data.get("cf_supervision_management_approval", None)
        cf_supervision_management_remark = process_data.get("cf_supervision_management_remark", None)

        variables = {
            'cf_supervision_management_approval': cf_supervision_management_approval,
            'cf_supervision_management_remark': cf_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AHUSupervisionApproval(AjaxTodoBase):
    """
        AHU 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        AHU_supervision_management_approval = process_data.get("AHU_supervision_management_approval", None)
        AHU_supervision_management_remark = process_data.get("AHU_supervision_management_remark", None)

        variables = {
            'AHU_supervision_management_approval': AHU_supervision_management_approval,
            'AHU_supervision_management_remark': AHU_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BgSupervisionApproval(AjaxTodoBase):
    """
        办公区域 专项方案监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        bg_supervision_management_approval = process_data.get("bg_supervision_management_approval", None)
        bg_supervision_management_remark = process_data.get("bg_supervision_management_remark", None)

        variables = {
            'bg_supervision_management_approval': bg_supervision_management_approval,
            'bg_supervision_management_remark': bg_supervision_management_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SpecialWorkPlanStorage(object):
    """
        专项方案存储
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def dlzl_data_repository(self):
        """
            电力走廊数据存库
        """
        dlzl_insert_data = self.ctx.variables.get("dlzl_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", dlzl_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def it_one_data_repository(self):
        """
            it1数据存库
        """
        IT_one_insert_data = self.ctx.variables.get("IT_one_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", IT_one_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def it_two_data_repository(self):
        """
            it2数据存库
        """
        IT_two_insert_data = self.ctx.variables.get("IT_two_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", IT_two_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})


    def it_three_data_repository(self):
        """
            it3数据存库
        """
        IT_three_insert_data = self.ctx.variables.get("IT_three_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", IT_three_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def it_four_data_repository(self):
        """
            it4数据存库
        """
        IT_four_insert_data = self.ctx.variables.get("IT_four_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", IT_four_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def cf_data_repository(self):
        """
            柴发和水处理数据存库
        """
        cf_insert_data = self.ctx.variables.get("cf_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", cf_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def ahu_data_repository(self):
        """
            ahu数据存库
        """
        AHU_insert_data = self.ctx.variables.get("AHU_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", AHU_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})

    def bg_data_repository(self):
        """
            办公区域数据存库
        """
        bg_insert_data = self.ctx.variables.get("bg_insert_data")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("special_program_data", bg_insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={})


class SpecialExaminationApproval(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def examination_approval_generate(self):
        db = mysql.new_mysql_instance("tbconstruct")
        dlzl_info = db.get_list("power_corridor_data",
                                fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                        'dcops_ticket_id'],
                                conditions={
                                    'task_name': ['电力方仓安装', '补/排风系统', '外市电工程（红线内）', '设备上电调试']
                                })
        it1_info = db.get_list("it_block1",
                               fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                       'dcops_ticket_id'],
                               conditions={
                                   'task_name': ['方仓及框架安装（结构工程）', '电气工程（强电工程）', '弱电系统（弱电工程）',
                                                 '装修工程', '消防工程', '暖通工程', '设备上电调试']
                               })
        it2_info = db.get_list("it_block2",
                               fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                       'dcops_ticket_id'],
                               conditions={
                                   'task_name': ['方仓及框架安装（结构工程）', '电气工程（强电工程）', '弱电系统（弱电工程）',
                                                 '装修工程', '消防工程', '暖通工程', '设备上电调试']
                               })
        it3_info = db.get_list("it_block3",
                               fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                       'dcops_ticket_id'],
                               conditions={
                                   'task_name': ['方仓及框架安装（结构工程）', '电气工程（强电工程）', '弱电系统（弱电工程）',
                                                 '装修工程', '消防工程', '暖通工程', '设备上电调试']
                               })
        it4_info = db.get_list("it_block4",
                               fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                       'dcops_ticket_id'],
                               conditions={
                                   'task_name': ['方仓及框架安装（结构工程）', '电气工程（强电工程）', '弱电系统（弱电工程）',
                                                 '装修工程', '消防工程', '暖通工程', '设备上电调试']
                               })
        bg_info = db.get_list("administrative_data",
                              fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                      'dcops_ticket_id'],
                              conditions={
                                  'task_name': ['装修（结构工程）', '暖通工程', '电气工程', '弱电工程', '消防工程',
                                                '设备上电调试']
                              })
        ahu_info = db.get_list("ahu_data",
                               fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                       'dcops_ticket_id'],
                               conditions={
                                   'task_name': ['装修（结构工程）', '暖通工程', '电气工程', '弱电工程', '设备上电调试']
                               })
        cf_info = db.get_list("firewoodhair_water_data",
                              fields=['task_name', 'plan_start_time', 'plan_finish_time', 'responsible_person',
                                      'dcops_ticket_id'],
                              conditions={
                                  'task_name': ['柴发方仓安装', '柴发油路系统', '水处理', '设备上电调试']
                              })
        """
            电力走廊起单逻辑
        """
        dlzl_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in dlzl_info:
            # if task_name not in unique_task_names:
            #     unique_task_names.append(task_name)

            processTitle = '电力走廊——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                #     three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": '电力走廊——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            dlzl = gnetops.request(action="Ticket", method="Create", data=data)
            dlzl_list.append(dlzl)
        """
            IT方仓1起单逻辑
        """
        it1_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in it1_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            #     unique_task_names.append(task_name)

            processTitle = 'IT Block1/电池间——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": 'IT Block1/电池间——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            it1 = gnetops.request(action="Ticket", method="Create", data=data)
            it1_list.append(it1)
        """
            IT方仓2起单逻辑
        """
        it2_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in it2_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = 'IT Block2/电池间——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": 'IT Block2/电池间——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            it2 = gnetops.request(action="Ticket", method="Create", data=data)
            it2_list.append(it2)
        """
            IT方仓3起单逻辑
        """
        it3_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in it3_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = 'IT Block3/电池间——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": 'IT Block3/电池间——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            it3 = gnetops.request(action="Ticket", method="Create", data=data)
            it3_list.append(it3)
        """
            IT方仓4起单逻辑
        """
        it4_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in it4_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = 'IT Block4/电池间——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": 'IT Block4/电池间——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            it4 = gnetops.request(action="Ticket", method="Create", data=data)
            it4_list.append(it4)
        """
            办公区域起单逻辑
        """
        bg_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in bg_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = '办公区域——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": '办公区域——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            bg = gnetops.request(action="Ticket", method="Create", data=data)
            bg_list.append(bg)
        """
            ahu区域起单逻辑
        """
        ahu_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in ahu_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = 'AHU区域——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": 'AHU区域——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            ahu = gnetops.request(action="Ticket", method="Create", data=data)
            ahu_list.append(ahu)
        """
            柴发和水处理区域起单逻辑
        """
        cf_list = []
        unique_task_names = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in cf_info:
            # task_name = i.get('task_name', None)
            # if task_name not in unique_task_names:
            # unique_task_names.append(task_name)

            processTitle = '柴发和水处理区域——' + f'{i.get("task_name")}' + '专项方案上传'
            plan_start_time = i.get('plan_start_time')
            plan_finish_time = i.get('plan_finish_time')
            if isinstance(plan_start_time, str):
                if plan_start_time != '0000-00-00 00:00:00':
                    plan_start_time = datetime.datetime.strptime(plan_start_time, "%Y-%m-%d")
                else:
                    plan_start_time = None  # 或者设置为其他默认值
            if isinstance(plan_finish_time, str):
                if plan_finish_time != '0000-00-00 00:00:00':
                    plan_finish_time = datetime.datetime.strptime(plan_finish_time, "%Y-%m-%d")
                else:
                    plan_finish_time = None  # 或者设置为其他默认值
                # if plan_start_time is not None:
                # three_days_before = (plan_start_time - datetime.timedelta(days=3)).date()
                # if current_date == three_days_before:
            data = {
                "CustomVariables": {
                    "task_name": '柴发和水处理区域——' + i.get('task_name', None),
                    'plan_start_time': plan_start_time.strftime("%Y-%m-%d") if plan_start_time else None,
                    'plan_finish_time': plan_finish_time.strftime("%Y-%m-%d") if plan_finish_time else None,
                    'responsible_person': i.get('responsible_person', None),
                    'dcops_ticket_id': i.get('dcops_ticket_id', None),
                },
                "ProcessDefinitionKey": "upload_special_plan",
                "Source": "",
                "TicketDescription": "专项方案上传",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "keketan,youngshi,v_mmywang",
                    "Creator": "keketan,youngshi,v_mmywang",
                    "Deal": "keketan,youngshi,v_mmywang"
                }
            }
            # 起单，并抛入data
            cf = gnetops.request(action="Ticket", method="Create", data=data)
            cf_list.append(cf)

        variables = {
            'dlzl_list': dlzl_list,
            'it1_list': it1_list,
            'it2_list': it2_list,
            'it3_list': it3_list,
            'it4_list': it4_list,
            'bg_list': bg_list,
            'ahu_list': ahu_list,
            'cf_list': cf_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
