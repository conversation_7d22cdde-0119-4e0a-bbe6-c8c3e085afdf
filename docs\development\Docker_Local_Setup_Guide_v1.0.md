# iBroker-construct Docker本地运行指南

## 📋 Docker配置分析

### 🔍 Dockerfile解析

**基础镜像**: `mirrors.tencent.com/reachyang/trpc-python-runtime:0.0.1`
- 这是腾讯内部的tRPC Python运行时镜像
- 包含Python环境和tRPC框架

**端口暴露**:
- `8000` - tRPC服务端口
- `80` - HTTP服务端口

**关键组件**:
- **supervisord** - 进程管理器
- **iBroker-service** - 主服务启动器
- **zylog** - 日志收集服务

## 🚀 本地Docker运行步骤

### 方案1: 直接使用原始Dockerfile (推荐)

```bash
# 1. 构建Docker镜像
docker build -t ibroker-construct .

# 2. 运行容器
docker run -d \
  --name ibroker-construct \
  -p 8000:8000 \
  -p 80:80 \
  -e BRANCH=dev \
  ibroker-construct
```

### 方案2: 修改Dockerfile适配本地环境

如果无法访问腾讯内部镜像，创建本地版本：

```dockerfile
# 创建 Dockerfile.local
FROM python:3.7-slim

EXPOSE 8000
EXPOSE 80
WORKDIR /data/nbroker

ENV PROJECTFLAG=ibroker-construct
ENV BRANCH=dev

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 创建必要目录
RUN mkdir -p /data/{wwwlogs,nbroker/var}

# 复制并安装Python依赖
COPY requirements.txt /tmp/
RUN pip3 install -r /tmp/requirements.txt

# 复制项目文件
COPY . .

# 设置启动脚本权限
RUN chmod +x docker/start_trpc_server.sh && \
    chmod +x docker/start_zylog.sh && \
    chmod 0777 var

# 启动命令
CMD ["supervisord", "-c", "docker/supervisord.conf"]
```

### 方案3: Docker Compose (最简单)

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  ibroker-construct:
    build: .
    ports:
      - "8000:8000"
      - "80:80"
    environment:
      - BRANCH=dev
      - PROJECTFLAG=ibroker-construct
    volumes:
      - ./logs:/data/wwwlogs
      - ./data:/data/nbroker/var
    restart: unless-stopped

  # 如果需要本地MySQL
  mysql:
    image: mysql:5.7
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: tbconstruct
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

## 🔧 本地运行命令

### 使用原始Dockerfile

```bash
# 构建镜像
docker build -t ibroker-construct .

# 运行容器
docker run -d \
  --name ibroker-construct \
  -p 8000:8000 \
  -p 80:80 \
  -v $(pwd)/logs:/data/wwwlogs \
  ibroker-construct

# 查看日志
docker logs -f ibroker-construct

# 进入容器调试
docker exec -it ibroker-construct bash
```

### 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🔍 调试和验证

### 检查服务状态

```bash
# 检查容器状态
docker ps

# 查看服务日志
docker logs ibroker-construct

# 进入容器
docker exec -it ibroker-construct bash

# 在容器内检查进程
supervisorctl status
```

### 验证服务可用性

```bash
# 检查HTTP服务
curl http://localhost:80/health

# 检查tRPC服务
curl http://localhost:8000/health

# 检查端口监听
netstat -tlnp | grep -E "(80|8000)"
```

### 查看应用日志

```bash
# 主服务日志
docker exec ibroker-construct tail -f /data/wwwlogs/message.log

# supervisord日志
docker exec ibroker-construct tail -f /data/wwwlogs/supervisord.log

# zylog日志
docker exec ibroker-construct tail -f /data/nbroker/var/zylog_out.log
```

## ⚠️ 注意事项

### 1. 网络访问问题

如果无法访问腾讯内部镜像源：
```bash
# 修改Dockerfile，使用公共镜像
FROM python:3.7-slim
# 然后手动安装tRPC相关依赖
```

### 2. 依赖包问题

如果某些包无法安装：
```bash
# 进入容器手动安装
docker exec -it ibroker-construct bash
pip install 包名
```

### 3. 配置问题

如果需要修改配置：
```bash
# 挂载配置文件
docker run -v $(pwd)/trpc_python.yaml:/data/nbroker/trpc_python.yaml ibroker-construct
```

## 🎯 快速启动脚本

创建 `start_local.sh`:

```bash
#!/bin/bash

echo "🚀 启动iBroker-construct本地环境..."

# 构建镜像
echo "📦 构建Docker镜像..."
docker build -t ibroker-construct .

# 停止已存在的容器
echo "🛑 停止已存在的容器..."
docker stop ibroker-construct 2>/dev/null || true
docker rm ibroker-construct 2>/dev/null || true

# 启动新容器
echo "🎯 启动新容器..."
docker run -d \
  --name ibroker-construct \
  -p 8000:8000 \
  -p 80:80 \
  -v $(pwd)/logs:/data/wwwlogs \
  -e BRANCH=dev \
  ibroker-construct

echo "✅ 容器启动完成！"
echo "🌐 HTTP服务: http://localhost:80"
echo "🔧 tRPC服务: http://localhost:8000"
echo "📋 查看日志: docker logs -f ibroker-construct"
```

## 📞 故障排除

### 常见问题

1. **镜像拉取失败**
   - 使用本地Dockerfile.local
   - 或配置Docker代理

2. **端口占用**
   - 修改端口映射: `-p 8001:8000`

3. **权限问题**
   - 确保Docker有足够权限
   - 检查文件权限设置

**现在可以使用Docker快速启动项目了！** 🚀
