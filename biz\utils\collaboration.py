
import hashlib
import hmac
import json
import time
from iBroker.lib import config, curl
from iBroker.lib.sdk import gnetops

def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


# 合作伙伴统一调用入口
def collaboration_api_calling(facilitator: str = None, request_data:dict = None,
                              method_name:str = None, error_proces_data: dict = None):
    '''
    :param 
    facilitator: 合作伙伴名称（德衡、维谛...)
    request_data: 请求数据
    method_name: 方法名
    error_proces_data: 错误日志额外数据(InstanceId,TaskId,TicketId,有InstanceId,TaskId会自动重启报错节点)
    '''
    system_type_map = config.get_config_map("system_type_map")
    system_type = system_type_map.get(facilitator)
    if system_type == "外部":
        rsp = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": facilitator,
                "ReqData": request_data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/" + method_name
            }
        )
        code, message, data = rsp.get("ErrorCode"), rsp.get("Message"), rsp.get("Data")
    elif system_type == "Dcops":
        code, message, data = calling_external_interfaces(request_data,facilitator,method_name)
    if code != 0 and code != 200:
        error_data_call = {
            "Facilitator": facilitator,
            "RequestData": request_data,
            "ResponstData": {
                "code": code,
                "message": message,
                "data": data
            }
        }
        if error_proces_data:
            error_data_call.update(error_proces_data)
        gnetops.request(
            action="Tbconstruct",
            method="CooperationCallbackError",
            ext_data=error_data_call,
            scheme="ifob-infrastructure",
        )
    return code, message, data

# 私有化部署统一接口调用入口
def calling_external_interfaces(data,facilitator,method_name):
    
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map('create_order')
    url = info.get("Url")
    # system_id = info.get("SystemId")
    system_id, corp_id = obtain_supplier_information('supplier_differentiation', facilitator)
    secret_key = info.get("secretKey")
    
    # 调用云函数
    request_data = {
        "SystemId": "13",
        "Action": "Nbroker",
        "Method": "UrlProxy",
        "NbrokerData": {
            "context": {
                "service_name": "TCForward",  # (*必填) 原样填写
                "method_name": "index"  # (*必填) 原样填写
            },
            "args": {
                "function_name": method_name,  # (*必填) 云函数名称
                "data": data,  # 传递给云函数的入参
                "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
            }
        },
        "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
    }
    # 生成http头部
    not_default_namespace = config.get_config_map("partner_method_not_default_namespace")
    if not_default_namespace.get(facilitator):
        for key, value in not_default_namespace[facilitator].items():
            if request_data["NbrokerData"]["args"]["function_name"] in value:
                request_data["NbrokerData"]["args"]["namespace"] = key
    str_data = json.dumps(request_data)
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    resp = curl.post(title=f"Dcops请求外部接口-{facilitator}",
                    system_name="Dcops",
                    url=url,
                    postdata=str_data,
                    append_header=headers)
    result = resp.json()
    # 云函数转发层是否报错
    status = result.get("status")
    if status != 0:
        return status, result.get("call_stack"), {}
    else:
        # 获取实际返回数据
        resp_actual = result.get("data").get("result")
        code = resp_actual.get("code")
        if code:
            return code, result.get("message"), result.get("data")
        else:
            push_data = resp_actual.get("push")
            if push_data:
                return 0, result.get("message"), result.get("data")
            else:
                return -1, result.get("message"), result.get("data")

