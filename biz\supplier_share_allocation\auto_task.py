from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue
from iBroker.lib import mysql, config
from biz.supplier_share_allocation.tools import (
    Tools,
)


class SupplierShareAllocationAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_project_supplier_resources(self, campus, project):
        """
        获取项目供应商
        """
        f_instance_id = self.ctx.instance_id
        # 获取项目供应商
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT * FROM supplier_resources WHERE campus = '{campus}' AND project = '{project}'"
        result = db.get_all(query_sql)
        data_list = []
        # 供应商有数据：取供应商表的；供应商无数据：取项目启动表的
        if result:
            data = result[len(result) - 1]
            data_list.append(data)
        else:
            data = {
                "area": "",
                "campus": campus,
                "project": project,
                "delivery_target": "",
                "state": "建设中",
            }
            query_sql2 = (
                f"SELECT region, demand_delivery FROM risk_early_warning_data "
                f"WHERE campus = '{campus}' AND project = '{project}'"
            )
            result2 = db.get_all(query_sql2)
            if result2:
                data2 = result2[len(result2) - 1]
                data = {
                    **data,
                    "delivery_target": data2.get("demand_delivery"),
                    "area": data2.get("region"),
                }
            data_list.append(data)
        # 获取对应品类供应商下拉
        category_supplier_dict = Tools.get_category_supplier_info()
        # 获取待办处理人
        supplier_share_allocation_handler = config.get_config_map(
            "supplier_share_allocation_handler"
        )
        supplier_chain = ";".join(supplier_share_allocation_handler)
        variables = {
            "data_list": data_list,
            "f_instance_id": f_instance_id,
            "category_supplier_dict": category_supplier_dict,
            "supplier_share_allocation_handler": supplier_share_allocation_handler,
            "supplier_chain": supplier_chain,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 供应商份额分配-自动起单
    def create_supplier_share_allocation_flow(self, campus, project, ticket_creator):
        # 项目全称
        project_name = campus + project
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        # 输入
        processTitle = f"{project_name}项目供应商份额分配"
        data = {
            "CustomVariables": {
                "campus": campus,
                "project": project,
            },
            "ProcessDefinitionKey": "supplier_share_allocation_by_project",
            "Source": "",
            "TicketDescription": processTitle,
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": flow_responsible_person,  # 关注人 可填多个
                "Creator": ticket_creator,  # 建单人 只能填一个
                "Deal": flow_responsible_person,  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
            },
        }
        # 输出
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {"supplier_share_allocation_ticket": res}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def wait_supplier_share_allocation_ends(self):
        """
            等待供应商份额分配工单结束
        """
        supplier_share_allocation_ticket = self.ctx.variables.get("supplier_share_allocation_ticket")
        ticket_id = supplier_share_allocation_ticket.get('TicketId')
        data = {
            "ResultColumns": {
                "CreateTime": "",
                "EndTime": "",
                "InstanceId": "",
                # 是否强制结单
                "IsForceEnd": "",
                # 流程定义标识
                "ProcessDefinitionKey": "",
                "ServiceRecoverTime": "",
                "StartProcessTime": "",
                # 单据状态[OPEN:运行中;END:已结束]
                "TicketStatus": "",
                "Title": "",
                "CustomRequestVarKV": "",
            },
            "SearchCondition": {'TicketId': ticket_id}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']
        cnt = 0  # 用于记录有多少个工单正在运行中
        for item in res_list:
            if item.get('TicketStatus') == 'OPEN':
                cnt += 1
        if cnt:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}
