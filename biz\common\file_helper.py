#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2024/3/20 11:27
# <AUTHOR> v_hx<PERSON>liu
# @File    : file_helper.py
#   该模块 是一些处理word， excel文件的辅助方法
from openpyxl import load_workbook, styles, utils


def excel_col_row_adjust(file_path):
    """
    对excel文件的 行高，列宽实现自适应
    @param file_path: 文件路径
    @return:
    """
    wb = load_workbook(file_path)
    for sheet in wb.sheetnames:  # 对Excel工作簿里面每个sheet表循环
        ws = wb[sheet]
        lks = []  # 用来存储列宽数值
        for i in range(1, ws.max_column + 1):
            lk = 1  # 给一个初始列宽数值1
            for j in range(1, ws.max_row + 1):  # 对每一列进行循环
                sz = ws.cell(row=j, column=i).value  # 获取每一列单元格的内容
                if isinstance(sz, (int, float)):  # 判断单元格是不是数字格式，其他特殊格式都需要单独判断，否则都是按照文本格式统计长度
                    lk1 = len(str(format(sz, ',')))  # 考虑到数字千分位的情况
                elif sz is None:
                    lk1 = 0
                else:
                    lk1 = len(str(sz).encode('gbk'))  # 如果不是gbk格式，两个中文字的长度为2，实际我们需要长度为4
                if lk < lk1:  # 判断当前单元格长度是不是大于上一个单元格长度，如果大于，则将新的长度赋值到列宽参数上，筛选出长度最大的单元格，并作为列宽数值
                    lk = lk1
            lks.append(lk)  # 存储每一个列宽数值

        # 设置列宽
        for i in range(1, ws.max_column + 1):  # 对每一列循环
            k = utils.get_column_letter(i)
            ws.column_dimensions[k].width = min(lks[i - 1], 30) + 3  # 最大长度为30，避免列宽太大，加4是为了显示内容给人的感觉更宽松一点。

        for r in ws:
            for c in r:
                c.alignment = styles.Alignment(wrapText=True)  # 自动换行

        # 设置行高自适应
        max_heights = []
        for row in ws.rows:
            max_height = 0
            for cell in row:
                cell_width = len(str(cell.value))
                max_height = max(max_height, cell_width)
            max_heights.append(max_height)
        for i, max_height in enumerate(max_heights):
            row = ws.row_dimensions[i + 1]
            # row.auto_size = True
            adjust_height = max_height * 1.1 + 8
            row.height = adjust_height

    wb.save(file_path)  # 保存Excel
    wb.close()  # 关闭Excel对象


# excel_col_row_adjust('./../facility/test1.xlsx')
