#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的dynamic_header和tabular_data_query方法实现
解决查询返回空数据的问题
"""

def dynamic_header_optimized(self, project_name=None, state=None, PM=None, demand_delivery=None):
    """
    优化版动态表头（甲供）
    主要改进:
    1. 分步查询，逐步排查问题
    2. 更灵活的设备类型匹配
    3. 详细的调试日志
    """
    db = mysql.new_mysql_instance("tbconstruct")
    
    # 步骤1: 检查基础数据
    print("[DEBUG] === dynamic_header 开始执行 ===")
    try:
        # 检查各表数据量
        tables_info = {}
        tables = [
            'all_construction_ticket_data',
            'equipment_production_racking_process', 
            'delivery_schedule_management_table'
        ]
        
        for table in tables:
            count_sql = f"SELECT COUNT(*) as count FROM {table}"
            result = db.get_all(count_sql)
            tables_info[table] = result[0]['count'] if result else 0
            print(f"[DEBUG] 表 {table}: {tables_info[table]} 条记录")
        
        # 检查强制结单工单
        force_end_sql = "SELECT COUNT(*) as count FROM all_construction_ticket_data WHERE IsForceEnd = '1'"
        force_end_result = db.get_all(force_end_sql)
        force_end_count = force_end_result[0]['count'] if force_end_result else 0
        print(f"[DEBUG] 强制结单工单数量: {force_end_count}")
        
        valid_tickets_sql = "SELECT COUNT(*) as count FROM all_construction_ticket_data WHERE (IsForceEnd IS NULL OR IsForceEnd != '1')"
        valid_result = db.get_all(valid_tickets_sql)
        valid_count = valid_result[0]['count'] if valid_result else 0
        print(f"[DEBUG] 有效工单数量: {valid_count}")
        
    except Exception as e:
        print(f"[ERROR] 基础数据检查失败: {e}")
        return []
    
    # 步骤2: 测试表关联
    print("[DEBUG] === 测试表关联 ===")
    try:
        # 测试两表关联
        join_test_sql = """
            SELECT COUNT(*) as count
            FROM all_construction_ticket_data actd
            LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
            WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
        """
        join_result = db.get_all(join_test_sql)
        join_count = join_result[0]['count'] if join_result else 0
        print(f"[DEBUG] 工单表与设备生产表关联后: {join_count} 条记录")
        
        # 测试三表关联（项目名称匹配）
        three_join_sql = """
            SELECT COUNT(*) as count
            FROM all_construction_ticket_data actd
            LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
            LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
            WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
            AND dsm.project_name IS NOT NULL
        """
        three_join_result = db.get_all(three_join_sql)
        three_join_count = three_join_result[0]['count'] if three_join_result else 0
        print(f"[DEBUG] 三表关联（仅项目名称）后: {three_join_count} 条记录")
        
        # 测试完整关联（项目名称+设备类型）
        full_join_sql = """
            SELECT COUNT(*) as count
            FROM all_construction_ticket_data actd
            LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
            LEFT JOIN delivery_schedule_management_table dsm 
                ON dsm.project_name = eprp.project_name 
                AND dsm.device_type = eprp.device_name
            WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
            AND dsm.project_name IS NOT NULL
        """
        full_join_result = db.get_all(full_join_sql)
        full_join_count = full_join_result[0]['count'] if full_join_result else 0
        print(f"[DEBUG] 完整关联（项目名称+设备类型）后: {full_join_count} 条记录")
        
    except Exception as e:
        print(f"[ERROR] 表关联测试失败: {e}")
    
    # 步骤3: 构建查询条件
    conditions = ["(actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')"]
    
    if project_name:
        conditions.append(f"dsm.project_name LIKE '%{project_name}%'")
        print(f"[DEBUG] 添加项目名称过滤: {project_name}")
    
    if state:
        conditions.append(f"sd.state = '{state}'")
        print(f"[DEBUG] 添加状态过滤: {state}")
    
    if PM:
        conditions.append(f"rewd.PM = '{PM}'")
        print(f"[DEBUG] 添加PM过滤: {PM}")
    
    if demand_delivery and len(demand_delivery) == 2:
        start_date, end_date = demand_delivery
        conditions.append(
            f"STR_TO_DATE(rewd.demand_delivery, '%Y年%m月%d日') BETWEEN "
            f"STR_TO_DATE('{start_date}', '%Y年%m月%d日') AND "
            f"STR_TO_DATE('{end_date}', '%Y年%m月%d日')"
        )
        print(f"[DEBUG] 添加交付时间过滤: {start_date} 到 {end_date}")
    
    where_clause = " AND ".join(conditions)
    print(f"[DEBUG] 最终WHERE条件: {where_clause}")
    
    # 步骤4: 尝试简化查询
    print("[DEBUG] === 尝试简化查询 ===")
    
    # 先尝试最简单的查询
    simple_query = f"""
        SELECT DISTINCT
            dsm.project_name,
            dsm.supplier,
            dsm.project_required_delivery_time,
            dsm.device_type
        FROM all_construction_ticket_data actd
        LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
        LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
        WHERE {where_clause}
        AND dsm.project_name IS NOT NULL
        ORDER BY dsm.project_name, dsm.supplier
        LIMIT 10
    """
    
    try:
        print(f"[DEBUG] 执行简化查询: {simple_query}")
        simple_result = db.get_all(simple_query)
        print(f"[DEBUG] 简化查询结果数量: {len(simple_result) if simple_result else 0}")
        
        if simple_result:
            print(f"[DEBUG] 简化查询第一条数据: {simple_result[0]}")
            
            # 如果简化查询有结果，再尝试完整查询
            full_query = f"""
                SELECT DISTINCT
                    dsm.project_name,
                    dsm.supplier,
                    dsm.project_required_delivery_time,
                    dsm.device_type,
                    dsm.equipment_sla,
                    dsm.expected_time_equipment,
                    dsm.estimated_time_delivery,
                    dsm.delivery_gap,
                    eprp.ticket_id as production_work_order
                FROM all_construction_ticket_data actd
                LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
                LEFT JOIN delivery_schedule_management_table dsm 
                    ON dsm.project_name = eprp.project_name
                WHERE {where_clause}
                AND dsm.project_name IS NOT NULL
                ORDER BY dsm.project_name, dsm.supplier
            """
            
            print(f"[DEBUG] 执行完整查询")
            result = db.get_all(full_query)
            print(f"[DEBUG] 完整查询结果数量: {len(result) if result else 0}")
            
        else:
            print("[DEBUG] 简化查询无结果，检查数据匹配问题...")
            
            # 检查项目名称匹配问题
            mismatch_query = """
                SELECT 'eprp_projects' as type, eprp.project_name, COUNT(*) as count
                FROM equipment_production_racking_process eprp
                WHERE eprp.project_name IS NOT NULL
                GROUP BY eprp.project_name
                ORDER BY count DESC
                LIMIT 5
            """
            mismatch_result = db.get_all(mismatch_query)
            print(f"[DEBUG] 设备生产表项目名称示例: {mismatch_result}")
            
            dsm_projects_query = """
                SELECT 'dsm_projects' as type, dsm.project_name, COUNT(*) as count
                FROM delivery_schedule_management_table dsm
                WHERE dsm.project_name IS NOT NULL
                GROUP BY dsm.project_name
                ORDER BY count DESC
                LIMIT 5
            """
            dsm_projects_result = db.get_all(dsm_projects_query)
            print(f"[DEBUG] 交付计划表项目名称示例: {dsm_projects_result}")
            
            result = []
            
    except Exception as e:
        print(f"[ERROR] 查询执行失败: {e}")
        result = []
    
    # 步骤5: 处理结果
    if not result:
        print("[DEBUG] 查询无结果，返回空列表")
        return []
    
    # 数据去重和处理
    unique_data = []
    seen_records = set()
    
    for row in result:
        # 处理项目交付时间格式
        delivery_time = row.get('project_required_delivery_time', '')
        if '→' in delivery_time:
            delivery_time = delivery_time.split('→')[1].strip()
        
        record_key = (
            row.get('project_name'),
            row.get('supplier'),
            delivery_time
        )
        
        if record_key not in seen_records:
            unique_data.append({
                'project_name': row.get('project_name'),
                'supplier': row.get('supplier'),
                'project_required_delivery_time': delivery_time,
                'required_delivery_time': row.get('project_required_delivery_time')
            })
            seen_records.add(record_key)
    
    print(f"[DEBUG] 最终返回数据数量: {len(unique_data)}")
    print(f"[DEBUG] === dynamic_header 执行完成 ===")
    
    return unique_data
