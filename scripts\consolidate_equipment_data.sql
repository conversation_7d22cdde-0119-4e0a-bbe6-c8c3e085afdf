-- ========================================
-- 设备数据合并原生SQL查询
-- 基于实际数据库表结构编写
-- ========================================

-- 创建合并表
CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
    state VARCHAR(50) DEFAULT NULL COMMENT '项目状态',
    TicketId VARCHAR(100) NOT NULL COMMENT '工单ID',
    Title VARCHAR(255) DEFAULT NULL COMMENT '工单标题',
    SchemeNameCn VARCHAR(255) DEFAULT NULL COMMENT '方案名称',
    IsForceEnd VARCHAR(255) DEFAULT '0' COMMENT '是否强制结束',
    project_name VARCHAR(255) DEFAULT NULL COMMENT '项目名称',
    device_name VARCHAR(255) DEFAULT NULL COMMENT '设备名称',
    supplier VARCHAR(255) DEFAULT NULL COMMENT '供应商',
    device_type VARCHAR(100) DEFAULT NULL COMMENT '设备类型',
    equipment_sla VARCHAR(50) DEFAULT NULL COMMENT '设备SLA',
    expected_time_equipment DATETIME DEFAULT NULL COMMENT '预期设备时间',
    estimated_time_delivery DATETIME DEFAULT NULL COMMENT '预计交付时间',
    delivery_gap INT DEFAULT NULL COMMENT '交付间隔',
    completion_time DATETIME DEFAULT NULL COMMENT '完成时间',
    po_create_time DATETIME DEFAULT NULL COMMENT 'PO创建时间',
    PRIMARY KEY (TicketId)
) COMMENT='设备数据合并表';

-- ========================================
-- 方案1: 简化的单次查询（推荐用于小数据量）
-- ========================================
INSERT IGNORE INTO consolidated_equipment_data (
    state, TicketId, Title, SchemeNameCn, IsForceEnd,
    project_name, device_name, supplier, device_type, equipment_sla,
    expected_time_equipment, estimated_time_delivery, delivery_gap,
    completion_time, po_create_time
)
SELECT DISTINCT
    COALESCE(sd.state, '未知状态') as state,
    actd.TicketId,
    actd.Title,
    actd.SchemeNameCn,
    COALESCE(actd.IsForceEnd, '0') as IsForceEnd,
    eprp.project_name,
    eprp.device_name,
    COALESCE(dsm.supplier, '待确认') as supplier,
    COALESCE(dsm.device_type, eprp.device_name, '未知设备') as device_type,
    COALESCE(dsm.equipment_sla, ecmr.equipment_sla, '待确认') as equipment_sla,
    dsm.expected_time_equipment,
    dsm.estimated_time_delivery,
    dsm.delivery_gap,
    cpd.completion_time,
    pi.po_create_time
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp
    ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm
    ON dsm.project_name = eprp.project_name
    AND (dsm.device_type = eprp.device_name OR dsm.device_type LIKE CONCAT('%', eprp.device_name, '%'))
LEFT JOIN equipment_category_mapping_relationship ecmr
    ON ecmr.device_name = eprp.device_name
LEFT JOIN summary_data sd
    ON CONCAT(sd.campus, sd.project_name) = eprp.project_name
LEFT JOIN construct_plan_data cpd
    ON cpd.project_name = eprp.project_name
    AND cpd.work_content = eprp.device_name
LEFT JOIN payment_info pi
    ON pi.project_name = eprp.project_name
WHERE actd.SchemeNameCn LIKE '%设备生产%'
    AND (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
    AND eprp.equipment_type = '甲供'
    AND actd.TicketId IS NOT NULL
    AND actd.TicketId != ''
    AND eprp.project_name IS NOT NULL
    AND eprp.device_name IS NOT NULL
ORDER BY actd.TicketId
LIMIT 5000;

-- ========================================
-- 方案2: 分步查询SQL（推荐用于大数据量）
-- ========================================

-- 步骤1: 获取基础工单数据
SELECT '步骤1: 获取基础工单数据' as step;
SELECT DISTINCT
    actd.TicketId,
    actd.Title,
    actd.SchemeNameCn,
    actd.IsForceEnd,
    eprp.project_name,
    eprp.device_name,
    eprp.equipment_type
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp
    ON eprp.ticket_id = actd.TicketId
WHERE actd.SchemeNameCn LIKE '%设备生产%'
    AND (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
    AND eprp.equipment_type = '甲供'
    AND actd.TicketId IS NOT NULL
    AND actd.TicketId != ''
    AND eprp.project_name IS NOT NULL
    AND eprp.device_name IS NOT NULL
ORDER BY actd.TicketId
LIMIT 10;

-- 步骤2: 获取交付计划管理数据
SELECT '步骤2: 获取交付计划管理数据' as step;
SELECT project_name, device_type, supplier, equipment_sla, 
       expected_time_equipment, estimated_time_delivery, delivery_gap
FROM delivery_schedule_management_table
WHERE project_name IS NOT NULL
LIMIT 10;

-- 步骤3: 获取设备分类映射数据
SELECT '步骤3: 获取设备分类映射数据' as step;
SELECT device_name, equipment_sla, material_category_name
FROM equipment_category_mapping_relationship
WHERE device_name IS NOT NULL
LIMIT 10;

-- 步骤4: 获取项目状态数据
SELECT '步骤4: 获取项目状态数据' as step;
SELECT campus, project_name, state,
       CONCAT(campus, project_name) as full_project_name
FROM summary_data
WHERE campus IS NOT NULL AND project_name IS NOT NULL AND state IS NOT NULL
LIMIT 10;

-- 步骤5: 获取建设计划数据
SELECT '步骤5: 获取建设计划数据' as step;
SELECT project_name, work_content, completion_time
FROM construct_plan_data
WHERE project_name IS NOT NULL AND work_content IS NOT NULL
LIMIT 10;

-- 步骤6: 获取付款信息数据
SELECT '步骤6: 获取付款信息数据' as step;
SELECT project_name, po_create_time
FROM payment_info
WHERE project_name IS NOT NULL
LIMIT 10;

-- ========================================
-- 数据质量检查SQL
-- ========================================

-- 检查合并表数据质量
SELECT '数据质量检查' as step;
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN supplier IS NULL OR supplier = '' OR supplier = 'NULL' THEN 1 ELSE 0 END) as null_supplier,
    SUM(CASE WHEN device_type IS NULL OR device_type = '' OR device_type = 'NULL' THEN 1 ELSE 0 END) as null_device_type,
    SUM(CASE WHEN equipment_sla IS NULL OR equipment_sla = '' OR equipment_sla = 'NULL' THEN 1 ELSE 0 END) as null_sla,
    SUM(CASE WHEN state IS NULL OR state = '' OR state = 'NULL' THEN 1 ELSE 0 END) as null_state,
    SUM(CASE WHEN supplier = '待确认' THEN 1 ELSE 0 END) as pending_supplier,
    SUM(CASE WHEN equipment_sla = '待确认' THEN 1 ELSE 0 END) as pending_sla,
    SUM(CASE WHEN state = '未知状态' THEN 1 ELSE 0 END) as unknown_state
FROM consolidated_equipment_data;

-- 显示数据样本
SELECT '数据样本' as step;
SELECT TicketId, project_name, device_name, supplier, device_type, equipment_sla, state
FROM consolidated_equipment_data 
ORDER BY TicketId
LIMIT 10;

-- 检查异常数据
SELECT '异常数据检查' as step;
SELECT TicketId, supplier, device_type, equipment_sla
FROM consolidated_equipment_data 
WHERE supplier IN ('错了', '尖没有') 
   OR device_type IN ('错了', '尖没有')
   OR equipment_sla IN ('错了', '尖没有')
LIMIT 5;

-- ========================================
-- 数据清理SQL
-- ========================================

-- 修复异常的供应商数据
UPDATE consolidated_equipment_data 
SET supplier = '待确认'
WHERE supplier IN ('错了', '尖没有', 'NULL', '') 
   OR supplier IS NULL;

-- 修复异常的设备类型数据
UPDATE consolidated_equipment_data 
SET device_type = device_name
WHERE device_type IN ('NULL', '') 
   OR device_type IS NULL;

-- 修复异常的equipment_sla数据
UPDATE consolidated_equipment_data 
SET equipment_sla = '待确认'
WHERE equipment_sla IN ('NULL', '') 
   OR equipment_sla IS NULL;

-- 修复异常的状态数据
UPDATE consolidated_equipment_data 
SET state = '未知状态'
WHERE state IN ('NULL', '') 
   OR state IS NULL;

-- ========================================
-- 表结构查看
-- ========================================
DESCRIBE consolidated_equipment_data;

-- 查看表记录数
SELECT COUNT(*) as total_records FROM consolidated_equipment_data;
