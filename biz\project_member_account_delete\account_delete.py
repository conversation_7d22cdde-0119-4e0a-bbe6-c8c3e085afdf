from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService


# 账号删除申请
class AccountDelete(AjaxTodoBase):
    def __init__(self):
        super(AccountDelete, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("member_list")
        del_member_list = process_data.get("del_member_list")
        if not del_member_list:
            return {"code": -1, "msg": "请勾选需要删除的账号"}
        else:
            for item in del_member_list:
                if "is_truly_del_account" in item:
                    if "错误" in item["is_truly_del_account"]:
                        return {"code": -1, "msg": "请更新账号信息"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "member_list": member_list,
            "del_member_list": del_member_list,
            "leader_approval": 1,
            "leader_remark": "",
            "leader_reject_reason": "",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号删除审批
class AccountDeleteApproval(AjaxTodoBase):
    def __init__(self):
        super(AccountDeleteApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval", 1)
        leader_remark = process_data.get("leader_remark", "")
        leader_reject_reason = ""
        if not leader_approval:
            leader_reject_reason = leader_remark
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "leader_approval": leader_approval,
            "leader_remark": leader_remark,
            "leader_reject_reason": leader_reject_reason,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号删除完成信息确认
class AccountDeleteInfoConfirm(AjaxTodoBase):
    def __init__(self):
        super(AccountDeleteInfoConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        delete_wework_account_flow_flag = self.ctx.variables.get(
            "delete_wework_account_flow_flag"
        )
        if delete_wework_account_flow_flag == 1:
            del_member_list_confirm = process_data.get("del_member_list_confirm")
            for item in del_member_list_confirm:
                if item.get("ticket_id") and item.get("ticket_status") != "已结单":
                    return {
                        "code": -1,
                        "msg": "请等待企微账号删除流程工单全部结单后再提交",
                    }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id)
