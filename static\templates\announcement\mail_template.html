<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>公告</title>
    <script type="text/javascript">
        document.domain = 'oa.com';
    </script>

    <style type="text/css">
        ul li {
            list-style-type: none;
        }

        a {
            text-decoration: none;
        }

        h1 {
            text-align: center;
            font-size: 16px;
        }

        .title {
            font-family: "幼圆", "宋体", san-serif;

            font-size: 14px;
        }

        .foot1 {
            font-family: "幼圆", "宋体", san-serif;
            text-align: center;
            font-size: 13px;
            width: 100%;
            height: 30px;
            color: #000000;
            vertical-align: bottom;
        }

        .foot2 {
            font-family: "幼圆", "宋体", san-serif;
            text-align: center;
            font-size: 13px;
            line-height: 18px;
            width: 100%;
            height: 30px;
            color: #000000;
            vertical-align: bottom;
            padding: 100px 0px 10px 0px;
        }

        .foot {
            font-family: "幼圆", "宋体", san-serif;
            text-align: center;
            font-size: 14px;
            line-height: 28px;
            padding: 20px 0px;
            width: 100%;
            height: 60px;
            background-color: #333333;
            color: white;
            vertical-align: bottom;

        }

        .person {
            font-family: "幼圆", "宋体", san-serif;
            text-align: right;
            font-size: 14px;
            line-height: 18px;
            width: 100%;
            vertical-align: bottom;
        }

        ul li {
            float: left;
            margin: 0 0 5px;
            overflow-y: visible;
            padding: 0;
            width: 30%;
            list-style-type: none;
        }

        table {
            border: 1px;
            border-collapse: collapse;
            border-spacing: 0;
        }
    </style>

</head>
<body>
<div class="container" style="width:800px">
    <div class="content">
        <div class="datalist">
                    <span style="  font-size: medium;">
                        <div class="person" style="color:red; font-size: 12px;">
                            ***内部使用，禁止外传***
                        </div></span>

            <div>各位同事：</div>
        </div>
        {% for announcement_entry in announcement_entries|reverse %}
            <div>{{ announcement_entry }}</div>
        {% endfor %}

        NETOPS工单号：<a href="#" target="_blank"> {{ instance_id }}</a> <br><br>开始时间:
        <font style="color:red ; font-weight:bolder;"> {{ start_time }}</font>
        &nbsp;&nbsp;
        &nbsp;&nbsp; 结束时间: <span id="span_end_time" style="color:red ; font-weight:bolder;">{{ end_time }}</span>
        <br><br> 故障原因: <span>光缆故障导致</span>

        <br><br> 影响描述: 专线流量完全冗余；<br>

        <br><br>
        影响module:
        <table style="border:0px; width:100%;margin-left:20px;" border="0">

        </table>
        <br><br>影响部门:
        <table style="border:0px; width:100%;margin-left:20px;" border="0">

        </table>
        </span>
    </div>
</div>
<div class="person">
    <table style="float:right">
        <tr>
            <td style=" text-align: right"> 发布人 ：</td>
            <td>{{ publisher }}</td>
        </tr>
        <tr>
            <td style=" text-align: right">发布日期 ：</td>
            <td> {{ publish_time }}</td>
        </tr>
    </table>
</div>
<br><br><br><br>
<div class="foot2"><p>请各业务部门运维接口人通知相关同事进行业务侧恢复及确认工作。</p></div>
<br>
<div>
    <hr>
</div>
<table style="border:0px; width:100%" border="0">
    <tr class="title">
        <td><span> 公告编号: <a href="#"
                            target="_blank">  {{ instance_id }}</a> </span></td>
        <td style=" text-align: center;"><span> 公告类型:运营商网络线路类-专线线路中断   </span></td>
        <td style=" text-align: right;"><span> </span>
        </td>
    </tr>
</table>

<div class="foot"> 网络平台部提供7*24小时技术支持，如有问题请拨打NOC值班热线：6000<br>此邮件由系统自动发出，请勿回复<br>
    最终解释权归网络平台部网络架构中心所有
</div>

</div>
</body>
</html>


