#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试campus_resources修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from biz.construction_big_disk.campus_resources import ProjectsUnderConstructionInPark
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_no_conditions():
    """测试无条件查询 - 应该不再报device_record错误"""
    logger.info("=== 测试1: 无条件查询 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        result = park_resources.tabular_data_query()
        
        if isinstance(result, dict) and '13yue' in result:
            logger.info(f"✓ 无条件查询成功，返回设备数量: {len(result['13yue'])}")
            return True
        elif isinstance(result, list):
            logger.info(f"✓ 无条件查询成功，返回记录数量: {len(result)}")
            return True
        else:
            logger.error(f"✗ 无条件查询返回格式异常: {type(result)}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 无条件查询失败: {e}")
        return False

def test_project_name_filter():
    """测试项目名称筛选"""
    logger.info("=== 测试2: 项目名称筛选 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        # 使用一个常见的项目名称进行测试
        result = park_resources.tabular_data_query(project_name="清远清新")
        
        if isinstance(result, list):
            logger.info(f"✓ 项目名称筛选成功，返回记录数量: {len(result)}")
            # 验证返回的数据是否包含项目名称
            if result:
                sample_record = result[0]
                logger.info(f"  示例记录: {sample_record.get('project_name', 'N/A')}")
            return True
        else:
            logger.error(f"✗ 项目名称筛选返回格式异常: {type(result)}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 项目名称筛选失败: {e}")
        return False

def test_state_filter():
    """测试项目状态筛选"""
    logger.info("=== 测试3: 项目状态筛选 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        # 测试已交付状态
        result = park_resources.tabular_data_query(state="已交付")
        
        if isinstance(result, dict) and '13yue' in result:
            logger.info(f"✓ 项目状态筛选成功，返回设备数量: {len(result['13yue'])}")
            return True
        elif isinstance(result, list):
            logger.info(f"✓ 项目状态筛选成功，返回记录数量: {len(result)}")
            return True
        else:
            logger.info(f"✓ 项目状态筛选成功，但无匹配数据")
            return True
            
    except Exception as e:
        logger.error(f"✗ 项目状态筛选失败: {e}")
        return False

def test_combined_filters():
    """测试组合条件筛选"""
    logger.info("=== 测试4: 组合条件筛选 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        # 测试项目名称 + 状态组合筛选
        result = park_resources.tabular_data_query(
            project_name="清远清新", 
            state="已交付"
        )
        
        if isinstance(result, (dict, list)):
            logger.info(f"✓ 组合条件筛选成功，返回数据类型: {type(result)}")
            if isinstance(result, dict) and '13yue' in result:
                logger.info(f"  设备数量: {len(result['13yue'])}")
            elif isinstance(result, list):
                logger.info(f"  记录数量: {len(result)}")
            return True
        else:
            logger.error(f"✗ 组合条件筛选返回格式异常: {type(result)}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 组合条件筛选失败: {e}")
        return False

def test_data_structure():
    """测试返回数据结构的完整性"""
    logger.info("=== 测试5: 数据结构完整性 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        result = park_resources.tabular_data_query(project_name="清远清新")
        
        if isinstance(result, list) and result:
            sample_record = result[0]
            required_fields = [
                'project_name', 'device_type', 'equipment_SLA',
                'expected_time_equipment', 'estimated_time_delivery',
                'delivery_GAP', 'po_create_time', 'production_work_order',
                'actual_time_delivery'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in sample_record:
                    missing_fields.append(field)
            
            if missing_fields:
                logger.warning(f"⚠ 缺少字段: {missing_fields}")
            else:
                logger.info("✓ 数据结构完整，包含所有必需字段")
            
            logger.info(f"  示例记录字段: {list(sample_record.keys())}")
            return True
        else:
            logger.info("✓ 数据结构测试完成（无数据返回）")
            return True
            
    except Exception as e:
        logger.error(f"✗ 数据结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试campus_resources修复效果")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("无条件查询", test_no_conditions()))
    test_results.append(("项目名称筛选", test_project_name_filter()))
    test_results.append(("项目状态筛选", test_state_filter()))
    test_results.append(("组合条件筛选", test_combined_filters()))
    test_results.append(("数据结构完整性", test_data_structure()))
    
    # 统计测试结果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    logger.info(f"\n=== 测试结果汇总 ===")
    for test_name, result in test_results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！修复成功！")
        return True
    else:
        logger.error(f"❌ {total - passed} 个测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
