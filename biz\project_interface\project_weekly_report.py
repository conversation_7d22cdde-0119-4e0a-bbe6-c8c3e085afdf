import datetime
import os
import uuid

import pandas as pd
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class ProjectWeeklyReportQuery(object):
    """
    项目周报查询类
    """

    def data_query(self, campus):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT wciu.weekly_newspaper " \
                    "FROM week_create_info wci " \
                    "JOIN week_create_info_url wciu ON wci.ticketid = wciu.ticketid " \
                    f"WHERE wci.project_name = '{campus}' "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            weekly_newspaper_url = row.get('weekly_newspaper', None)
            # 截断url
            if "url：" in weekly_newspaper_url:
                weekly_newspaper_url = weekly_newspaper_url.split("url：")[1]
            if "；now_time：" in weekly_newspaper_url:
                weekly_newspaper_url = weekly_newspaper_url.split("；now_time：")[0]
            if "url：" in weekly_newspaper_url and "；now_time：" in weekly_newspaper_url:
                weekly_newspaper_url = weekly_newspaper_url.split("url：")[1].split("；now_time：")[0]
            filename_with_extension = os.path.basename(weekly_newspaper_url)
            filename_without_extension = os.path.splitext(filename_with_extension)[0]
            weekly_newspaper_name = filename_without_extension.split("_")[0]
            data.append({
                "weekly_newspaper_url": weekly_newspaper_url,
                "weekly_newspaper_name": weekly_newspaper_name,
            })
        return data

    def weekly_daily_calendar_inquiry(self, campus):
        """
            周报、日报
        """
        db = mysql.new_mysql_instance("tbconstruct")
        weekly_sql = "SELECT wciu.weekly_newspaper " \
                     "FROM week_create_info wci " \
                     "JOIN week_create_info_url wciu ON wci.ticketid = wciu.ticketid " \
                     f"WHERE wci.project_name = '{campus}' "
        weekly_sql_risk = "SELECT wciu.weekly_newspaper " \
                          "FROM risk_early_warning_data rewd " \
                          "JOIN week_create_info_url wciu ON rewd.dcopsTicketId = wciu.ticketid " \
                          f"WHERE rewd.project_name = '{campus}' "
        daily_sql = "SELECT dciu.daily_newspaper " \
                    "FROM daily_create_info dci " \
                    "JOIN daily_create_info_url dciu ON dci.ticketid = dciu.ticketid " \
                    f"WHERE dci.project_name = '{campus}' "
        new_daily_report_sql = (
            "SELECT DATE_FORMAT(report_date, '%Y-%m-%d') AS report_date FROM project_daily_report "
            f"WHERE project_name = '{campus}' "
        )
        weekly_result = db.get_all(weekly_sql)
        if not weekly_result:
            weekly_result = db.get_all(weekly_sql_risk)
        daily_result = db.get_all(daily_sql)
        new_daily_report_result = db.query(new_daily_report_sql)
        combined_result = []

        for item in weekly_result:
            # 提取文件名
            url = item["weekly_newspaper"]
            start_index = url.rfind("/") + 1
            end_index = url.rfind("_")
            name = url[start_index:end_index]
            # 提取时间
            date_start_index = url.rfind("now_time：") + len("now_time：")
            date = url[date_start_index:]
            # 截断url
            if "url：" in url:
                url = url.split("url：")[1]
            if "；now_time：" in url:
                url = url.split("；now_time：")[0]
            if "url：" in url and "；now_time：" in url:
                url = url.split("url：")[1].split("；now_time：")[0]
            # 截断名称
            name = name.split("；now")[0]

            combined_result.append({
                "date": date,
                "type": "weekly_newspaper",
                "weekly_newspaper_name": name,
                "weekly_newspaper_url": url
            })

        for item in daily_result:
            # 提取文件名
            url = item["daily_newspaper"]
            start_index = url.rfind("/") + 1
            end_index = url.rfind("_")
            name = url[start_index:end_index]
            # 提取时间
            date_start_index = url.rfind("now_time：") + len("now_time：")
            date = url[date_start_index:]
            # 截断url
            if "url：" in url:
                url = url.split("url：")[1]
            if "；now_time：" in url:
                url = url.split("；now_time：")[0]
            if "url：" in url and "；now_time：" in url:
                url = url.split("url：")[1].split("；now_time：")[0]
            # 截断名称
            name = name.split("；now")[0]
            combined_result.append({
                "date": date,
                "type": "daily_newspaper",
                "weekly_newspaper_name": name,
                "weekly_newspaper_url": url
            })
        for item in new_daily_report_result:
            date = item.get("report_date")
            combined_result.append(
                {
                    "date": date,
                    "type": "daily_newspaper",
                    "weekly_newspaper_name": f"{date}{campus}建设日报",
                    "weekly_newspaper_url": "",
                }
            )
        return combined_result

    def weekly_newspaper_upload_date(self, campus):
        """
            周报上传日期
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT wci.week_date,wciu.weekly_newspaper " \
                    "FROM week_create_info wci " \
                    "JOIN week_create_info_url wciu ON wci.ticketid = wciu.ticketid " \
                    f"WHERE wci.project_name = '{campus}' "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            week_date = row.get('week_date')
            # 文件url
            url = row.get("weekly_newspaper")
            if url:
                # 提取时间
                date_start_index = url.rfind("now_time：") + len("now_time：")
                date = url[date_start_index:]
                data = [{
                    "latestWeeklyDate": date,
                    "WeeklyNewspaperUploadTime": week_date,
                }]
        return data

    def data_details_query(self, url, sheet_name):
        """
        获取周报文件解析
        :param url:
        :param sheet_name:
        :return:
        """
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        data = []
        if sheet_name == '周报简报':
            """
                excel周报-周报简报解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                table_of_contents = row.get('目录')
                details = row.get('具体内容')
                if any([table_of_contents, details]):
                    data.append({
                        'contents': table_of_contents,
                        'details': details
                    })
        elif sheet_name == '进度管理':
            """
                excel周报-进度管理解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                serial_number = row.get('序号')
                task_name = row.get('工作内容')
                plan_start_time = row.get('开始时间（年/月/日）')
                plan_finish_time = row.get('完成时间（年/月/日）')
                actual_start_time = row.get('实际开始时间')
                actual_finish_time = row.get('实际完成时间')
                third_progress = row.get('当前进展')
                completion_week = row.get('本周完成情况')
                status = row.get('状态（正常/预警/滞后）')
                existing_problems = row.get('存在问题')
                solution = row.get('解决措施')
                work_plan_next_week = row.get('下周工作计划')
                first_level_proportion = row.get('一级占比')
                second_level_proportion = row.get('二级占比')
                third_level_proportion = row.get('三级占比')

                if not isinstance(first_level_proportion, (float, int)):
                    first_level_proportion = 0
                if not isinstance(second_level_proportion, (float, int)):
                    second_level_proportion = 0
                if not isinstance(third_level_proportion, (float, int)):
                    third_level_proportion = 0
                if not isinstance(third_progress, (float, int)):
                    third_progress = 0

                if isinstance(plan_start_time, datetime.datetime):
                    plan_start_time = plan_start_time.strftime("%Y-%m-%d")
                else:
                    plan_start_time = None

                if isinstance(plan_finish_time, datetime.datetime):
                    plan_finish_time = plan_finish_time.strftime("%Y-%m-%d")
                else:
                    plan_finish_time = None

                if isinstance(actual_start_time, datetime.datetime):
                    actual_start_time = actual_start_time.strftime("%Y-%m-%d")
                else:
                    actual_start_time = None

                if isinstance(actual_finish_time, datetime.datetime):
                    actual_finish_time = actual_finish_time.strftime("%Y-%m-%d")
                else:
                    actual_finish_time = None

                if any([task_name, serial_number, plan_start_time, plan_finish_time, actual_start_time,
                        actual_finish_time, completion_week, existing_problems, solution, work_plan_next_week,
                        first_level_proportion, third_level_proportion]):
                    data.append({
                        'task_name': task_name,
                        'serial_number': serial_number,
                        'plan_start_time': plan_start_time,
                        'plan_finish_time': plan_finish_time,
                        'actual_start_time': actual_start_time,
                        'actual_finish_time': actual_finish_time,
                        'third_progress': third_progress,
                        'completion_week': completion_week,
                        'status': status,
                        'existing_problems': existing_problems,
                        'solution': solution,
                        'work_plan_next_week': work_plan_next_week,
                        'first_level_proportion': first_level_proportion,
                        'second_level_proportion': second_level_proportion,
                        'third_level_proportion': third_level_proportion
                    })
        elif sheet_name == '甲供设备管理':
            """
                excel周报-甲供设备管理解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                serial_number = row.get('序号')
                belong_professional = row.get('所属专业')
                device_name = row.get('设备名称')
                problem_category = row.get('问题类别')
                problem_location = row.get('问题位置')
                problem_description = row.get('问题描述/原因')
                problem_photo = row.get('问题照片')
                rectification_reform_measures = row.get('整改措施')
                problem_level = row.get('问题等级')
                responsible_unit = row.get('责任单位')
                problem_put_forward_time = row.get('问题提出时间')
                problem_end_time = row.get('问题关闭时间')
                photos_after_rectification = row.get('整改后照片')
                problem_impact = row.get('问题影响')
                if any([serial_number, belong_professional, device_name, problem_category, problem_location,
                        problem_description, problem_photo, rectification_reform_measures, problem_level,
                        responsible_unit, problem_put_forward_time, problem_end_time, photos_after_rectification,
                        problem_impact]):
                    data.append({
                        'serial_number': serial_number,
                        'belong_professional': belong_professional,
                        'device_name': device_name,
                        'problem_category': problem_category,
                        'problem_location': problem_location,
                        'problem_description': problem_description,
                        'problem_photo': problem_photo,
                        'rectification_reform_measures': rectification_reform_measures,
                        'problem_level': problem_level,
                        'responsible_unit': responsible_unit,
                        'problem_put_forward_time': problem_put_forward_time,
                        'problem_end_time': problem_end_time,
                        'photos_after_rectification': photos_after_rectification,
                        'problem_impact': problem_impact
                    })
        elif sheet_name == '质量工艺管理':
            """
                excel周报-质量工艺管理解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                serial_number = row.get('序号')
                belong_professional = row.get('所属专业')
                problem_category = row.get('问题类别')
                problem_location = row.get('问题位置')
                problem_description = row.get('问题描述/原因')
                problem_photo = row.get('问题照片')
                rectification_reform_measures = row.get('整改措施')
                problem_level = row.get('问题等级')
                responsible_unit = row.get('责任单位')
                problem_put_forward_time = row.get('问题提出时间')
                problem_end_time = row.get('问题关闭时间')
                photos_after_rectification = row.get('整改后照片')
                problem_impact = row.get('问题影响')
                if any([serial_number, belong_professional, problem_category, problem_location, problem_description,
                        problem_photo, rectification_reform_measures, problem_level, responsible_unit,
                        problem_put_forward_time, problem_end_time, photos_after_rectification, problem_impact]):
                    data.append({
                        'serial_number': serial_number,
                        'belong_professional': belong_professional,
                        'problem_category': problem_category,
                        'problem_location': problem_location,
                        'problem_description': problem_description,
                        'problem_photo': problem_photo,
                        'rectification_reform_measures': rectification_reform_measures,
                        'problem_level': problem_level,
                        'responsible_unit': responsible_unit,
                        'problem_put_forward_time': problem_put_forward_time,
                        'problem_end_time': problem_end_time,
                        'photos_after_rectification': photos_after_rectification,
                        'problem_impact': problem_impact,
                    })
        elif sheet_name == '安全问题':
            """
                excel周报-安全问题解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                serial_number = row.get('序号')
                construction_unit = row.get('施工单位')
                problem_category = row.get('问题类型')
                problem_location = row.get('问题位置')
                problem_description = row.get('问题描述/原因')
                problem_photo = row.get('问题照片')
                problem_impact = row.get('问题影响')
                problem_level = row.get('问题等级')
                rectification_reform_measures = row.get('整改措施')
                responsible_unit = row.get('责任单位')
                problem_put_forward_time = row.get('问题提出时间')
                problem_end_time = row.get('问题关闭时间')
                photos_after_rectification = row.get('整改后照片')
                if any([serial_number, construction_unit, problem_category, problem_location, problem_description,
                        problem_photo, rectification_reform_measures, problem_level, responsible_unit,
                        problem_put_forward_time, problem_end_time, photos_after_rectification, problem_impact]):
                    data.append({
                        'serial_number': serial_number,
                        'construction_unit': construction_unit,
                        'problem_category': problem_category,
                        'problem_location': problem_location,
                        'problem_description': problem_description,
                        'problem_photo': problem_photo,
                        'problem_impact': problem_impact,
                        'problem_level': problem_level,
                        'rectification_reform_measures': rectification_reform_measures,
                        'responsible_unit': responsible_unit,
                        'problem_put_forward_time': problem_put_forward_time,
                        'problem_end_time': problem_end_time,
                        'photos_after_rectification': photos_after_rectification,
                    })
        elif sheet_name == '资料管理':
            """
                excel周报-资料管理解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                stage = row.get('阶段')
                serial_number = row.get('序号')
                two_directory = row.get('二级目录')
                three_directory = row.get('三级目录')
                archiving_situation = row.get('本周归档情况说明')
                if any([serial_number, stage, two_directory, three_directory, archiving_situation]):
                    data.append({
                        'stage': stage,
                        'serial_number': serial_number,
                        'two_directory': two_directory,
                        'three_directory': three_directory,
                        'archiving_situation': archiving_situation
                    })
        elif sheet_name == '人力资源管理':
            """
                excel周报-人力资源管理解析并存储数据
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name=f'{sheet_name}', parse_dates=False)
            df = df.replace({pd.np.nan: None})
            for _, row in df.iterrows():
                serial_number = row.get('序号')
                construction_unit = row.get('施工单位')
                engineering_major = row.get('工程专业')
                this_week_actual_input_days = row.get('本周实际投入人天')
                this_week_planned_input_days = row.get('本周计划投入人天')
                manpower_deviation = row.get('人力偏差')
                progress_influence = row.get('进度影响')
                responsible_person = row.get('责任人')
                solving_measures = row.get('解决措施')
                processing_result = row.get('处理结果')
                next_week_planned_input_days = row.get('下周计划投入人天')
                if any([serial_number, construction_unit, engineering_major, this_week_actual_input_days,
                        this_week_planned_input_days, manpower_deviation, progress_influence, responsible_person,
                        solving_measures, processing_result, next_week_planned_input_days]):
                    data.append({
                        'serial_number': serial_number,
                        'construction_unit': construction_unit,
                        'engineering_major': engineering_major,
                        'this_week_actual_input_days': this_week_actual_input_days,
                        'this_week_planned_input_days': this_week_planned_input_days,
                        'manpower_deviation': manpower_deviation,
                        'progress_influence': progress_influence,
                        'responsible_person': responsible_person,
                        'solving_measures': solving_measures,
                        'processing_result': processing_result,
                        'next_week_planned_input_days': next_week_planned_input_days
                    })
        return data

    def latest_weekly_daily_report(self, campus):
        """
        最新周报、日报工单
        """
        db = mysql.new_mysql_instance("tbconstruct")
        weekly_sql = (
            "SELECT TicketId FROM all_construction_ticket_data "
            "WHERE ProcessDefinitionKey in ('project_construct_week', 'weekly_report_process') "
            f"AND Title LIKE '%{campus}%' ORDER BY id DESC LIMIT 1"
        )
        daily_sql = (
            "SELECT TicketId FROM all_construction_ticket_data "
            "WHERE ProcessDefinitionKey in ('construction_project_daily_report') "
            f"AND Title LIKE '%{campus}%' ORDER BY id DESC LIMIT 1"
        )
        weekly_result = db.query(weekly_sql)
        daily_result = db.query(daily_sql)
        data = {
            "weekly_report_ticket_id": (
                weekly_result[0].get("TicketId") if weekly_result else ""
            ),
            "daily_report_ticket_id": (
                daily_result[0].get("TicketId") if daily_result else ""
            ),
        }
        return data
