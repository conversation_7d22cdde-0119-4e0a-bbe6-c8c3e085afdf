import uuid
import numpy as np
import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops
from biz.construction_process.cos_lib import COSLib
from iBroker.sdk.workflow.workflow import WorkflowVarUpdate


class InterfaceMasterControlPlan(object):
    def analyze_control_plan(self, overall_control_url, project_name, ticket_id):
        if overall_control_url and 'cosfile' in overall_control_url:
            relative_url = overall_control_url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            print(new_file_name)
            COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
            """
                excel解析总控计划
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl')
            df = df.replace({np.nan: None})
            """
                所有数据存库
            """
            insert_data = []
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['开始时间（年/月/日）']) else None
                completion_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                insert_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'serial_number': serial_number,
                    'work_content': work_content,
                    'start_time': start_time,
                    'completion_time': completion_time,
                    'responsible_person': responsible_person,
                    'output_object': output_object,
                    'input_object': input_object,
                    'construction_attribute': construction_attribute
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("excel_data", insert_data)
            tb_db.commit()
            return {"code": 200, "msg": insert_data}
        else:
            return {"code": 500, "msg": "url错误"}

    def get_data(self, row):
        number = str(row['序号'])
        work_content = row['工作内容']
        plan_construction_period = row['工期（日历日）']
        plan_start_time = row['开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
            row['开始时间（年/月/日）']) else None
        plan_finish_time = row['完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
            row['完成时间（年/月/日）']) else None
        responsible_person = row['责任人（开通账号人员）']
        output = row['输出物']
        input = row['输入物']
        construction_properties = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
        data = {
            'number': number,
            'work_content': work_content,
            'plan_construction_period': plan_construction_period,
            'plan_start_time': plan_start_time,
            'plan_finish_time': plan_finish_time,
            'responsible_person': responsible_person,
            'output': output,
            'input': input,
            'construction_properties': construction_properties
        }
        return data

    def title_data(self, row, list1):
        data = self.get_data(row)
        list1['time'] = data.get('plan_start_time')
        list1['completion_time'] = data.get('plan_finish_time')
        list1['responsible_person'] = data.get('responsible_person')
        return list1

    def analytical_opening_plan(self, construction_plan, ticket_id):
        """
                   下载计划，解析数据
        """
        # 下载计划
        url = construction_plan
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        # 解析数据
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        # 设备进场前
        project_start_list = {'work_list': []}
        ground_list = {'work_list': []}
        AHU_list = {'work_list': []}
        water_treatment_list = {'work_list': []}
        fangcang_list = {'work_list': []}
        other_list = {'work_list': []}
        # 判断其他方案是否存在
        if '2.8.5' in df['序号'].values:
            next_op = 1
        else:
            next_op = 2
        # 截取设备进场前的计划
        if '2.9' in df['序号'].values:
            filtered_df = df[(df['序号'].apply(str) >= '2.7') & (df['序号'].apply(str) < '2.9')]
        else:
            filtered_df = df[df['序号'].apply(str) >= '2.7']
        # 存数据库的数据
        insert_data = []
        for _, row in filtered_df.iterrows():
            data = self.get_data(row)
            data['ticket_id'] = ticket_id
            insert_data.append(data)
            number = str(row['序号'])
            if number == '2.7':
                self.title_data(row, project_start_list)
            elif number == '2.8.1':
                self.title_data(row, ground_list)
            elif number == '2.8.2':
                self.title_data(row, AHU_list)
            elif number == '2.8.3':
                self.title_data(row, water_treatment_list)
            elif number == '2.8.4':
                self.title_data(row, fangcang_list)
            elif number == '2.8.5':
                self.title_data(row, other_list)
            elif '2.7' < number < '2.8':
                data = self.get_data(row)
                project_start_list['work_list'].append(data)
            elif '2.8.1' < number < '2.8.2':
                data = self.get_data(row)
                ground_list['work_list'].append(data)
            elif '2.8.2' < number < '2.8.3':
                data = self.get_data(row)
                AHU_list['work_list'].append(data)
            elif '2.8.3' < number < '2.8.4':
                data = self.get_data(row)
                water_treatment_list['work_list'].append(data)
            if next_op == 1:
                if '2.8.4' < number < '2.8.5':
                    data = self.get_data(row)
                    fangcang_list['work_list'].append(data)
                elif '2.8.5' < number < '2.9':
                    data = self.get_data(row)
                    other_list['work_list'].append(data)
            else:
                if '2.8.4' < number < '2.9':
                    data = self.get_data(row)
                    fangcang_list['work_list'].append(data)

        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        # 插入数据
        tb_db.insert_batch("equipment_before_data", insert_data)
        tb_db.commit()
        variables = {
            "next_op": next_op,
            'project_start_list': project_start_list,
            'ground_list': ground_list,
            'AHU_list': AHU_list,
            'water_treatment_list': water_treatment_list,
            'fangcang_list': fangcang_list,
            'other_list': other_list,
            'insert_data': insert_data,
        }
        return {"code": 200, "msg": variables}

class RefreshVariables(object):
    """
    刷新流程变量
    """
    def refresh_variables(self, ticket_id_list, variables, process_definition_key):
        if ticket_id_list and variables:
            if type(variables) is dict:
                query_data = {
                    "SchemaId": "ticket_base",
                    "Data": {
                        "ResultColumns": {
                            "InstanceId": "",
                            "TicketId": "",
                            "ProcessDefinitionKey":""
                        },
                        "SearchCondition": {
                            "TicketId": ticket_id_list
                        }
                    }
                }
                query_result = gnetops.request(
                    action="QueryData",
                    method="Run",
                    ext_data=query_data
                )
                result_list = query_result.get("List")
                success_list = []
                if result_list:
                    for result in result_list:
                        ticket_id = result.get("TicketId")
                        instance_id = result.get('InstanceId')
                        key = result.get('ProcessDefinitionKey')
                        if process_definition_key == key:
                            success_list.append(ticket_id)
                            WorkflowVarUpdate(instance_id=instance_id,
                                              variables=variables
                                              ).call()
                    return {'code': 200, 'msg': f'流程变量：{success_list}修改成功'}
                else:
                    return {'code': 400, 'msg': '未查询到instance_id'}
            else:
                return {'code': 400, 'msg': '流程变量格式错误'}
        else:
            return {'code': 400, 'msg': '输入参数为空'}
