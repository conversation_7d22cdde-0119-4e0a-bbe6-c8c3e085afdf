from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_application_process.construction_application_info import (
    ConstructionApplicationInfoQuery,
)
from biz.construction_application_process.field_handle import (
    ConstructionApplicationFieldHandle,
)
from biz.construction_application_process.template import TemplateAction


class MainsPowerPlanUpload(AjaxTodoBase):
    # 市电专项 分项计划 上传
    def __init__(self):
        super(MainsPowerPlanUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取上传文件url
        sdzx_sub_plan = process_data.get("sdzx_sub_plan")
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_file_exit_flag(
            sdzx_sub_plan, "市电专项计划"
        )
        if not file_exit_flag:
            return file_exit_msg
        # 模板字段
        template_field = {
            "2.9.1": "外电报装",
            "2.9.2": "外电方案",
            "2.9.3": "高可靠费缴费",
            "2.9.4": "供电合同签订",
            "2.9.5": "调度协议签订",
            "2.9.6": "送电前检测",
            "2.9.7": "送电",
        }
        # 判断模板是否正确
        template_true_msg, template_true_flag = TemplateAction.get_template_flag(
            sdzx_sub_plan, "市电专项计划", template_field
        )
        if not template_true_flag:
            return template_true_msg
        file_write_msg, file_write_flag = TemplateAction.get_file_input_flag(
            sdzx_sub_plan, "市电专项计划"
        )
        if not file_write_flag:
            return file_write_msg
        sdzx_sub_plan_file_json = TemplateAction.get_file_json(
            sdzx_sub_plan, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "sdzx_sub_plan": sdzx_sub_plan,
            "sdzx_sub_plan_file_json": sdzx_sub_plan_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 外电报装
class ExternalPowerReport(AjaxTodoBase):
    def __init__(self):
        super(ExternalPowerReport, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        wdbz_dict = process_data.get("wdbz_dict")
        upload_file_json = {"供电报装申请表": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "power_supply_installation_application_file" in wdbz_dict:
            upload_file_field = upload_file_field_map[
                "power_supply_installation_application_file"
            ]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                wdbz_dict["power_supply_installation_application_file"],
                upload_file_field,
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                wdbz_dict["power_supply_installation_application_file"], process_user
            )
        wdbz_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（供电合同签订、调度协议签订、高可靠费缴费）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "wdbz_dict": wdbz_dict,
            "gdht_responsible_person": todo_handler,
            "ddxy_responsible_person": todo_handler,
            "gkkf_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 外电方案
class ExternalPowerPlan(AjaxTodoBase):
    def __init__(self):
        super(ExternalPowerPlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        wdfa_dict = process_data.get("wdfa_dict")
        upload_file_json = {"供电局批复方案": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "power_supply_bureau_approval_plan_file" in wdfa_dict:
            upload_file_field = upload_file_field_map[
                "power_supply_bureau_approval_plan_file"
            ]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                wdfa_dict["power_supply_bureau_approval_plan_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                wdfa_dict["power_supply_bureau_approval_plan_file"], process_user
            )
        wdfa_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（供电合同签订、调度协议签订、高可靠费缴费）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "wdfa_dict": wdfa_dict,
            "gdht_responsible_person": todo_handler,
            "ddxy_responsible_person": todo_handler,
            "gkkf_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 高可靠费缴费
class ReliabilityFee(AjaxTodoBase):
    def __init__(self):
        super(ReliabilityFee, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gkkf_dict = process_data.get("gkkf_dict")
        upload_file_json = {"高可靠费缴费凭证": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "highly_reliable_fee_payment_voucher_file" in gkkf_dict:
            upload_file_field = upload_file_field_map[
                "highly_reliable_fee_payment_voucher_file"
            ]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                gkkf_dict["highly_reliable_fee_payment_voucher_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                gkkf_dict["highly_reliable_fee_payment_voucher_file"], process_user
            )
        gkkf_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（送电）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gkkf_dict": gkkf_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 供电合同签订
class PowerSupplyContract(AjaxTodoBase):
    def __init__(self):
        super(PowerSupplyContract, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gdht_dict = process_data.get("gdht_dict")
        upload_file_json = {"供电合同": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "power_supply_contract_file" in gdht_dict:
            upload_file_field = upload_file_field_map["power_supply_contract_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                gdht_dict["power_supply_contract_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                gdht_dict["power_supply_contract_file"], process_user
            )
        gdht_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（送电）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gdht_dict": gdht_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 调度协议签订
class DispatchProtocol(AjaxTodoBase):
    def __init__(self):
        super(DispatchProtocol, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ddxy_dict = process_data.get("ddxy_dict")
        upload_file_json = {"调度协议": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "dispatch_protocol_file" in ddxy_dict:
            upload_file_field = upload_file_field_map["dispatch_protocol_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                ddxy_dict["dispatch_protocol_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                ddxy_dict["dispatch_protocol_file"], process_user
            )
        ddxy_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（送电）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "ddxy_dict": ddxy_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 送电前检测(设备试验报告)
class DetectionBeforePowerTransmission(AjaxTodoBase):
    def __init__(self):
        super(DetectionBeforePowerTransmission, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sdqjc_dict = process_data.get("sdqjc_dict")
        upload_file_json = {"设备试验报告": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "equipment_test_report" in sdqjc_dict:
            upload_file_field = upload_file_field_map["equipment_test_report"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                sdqjc_dict["equipment_test_report"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                sdqjc_dict["equipment_test_report"], process_user
            )
        sdqjc_dict.setdefault("upload_file_json", upload_file_json)
        # 获取待办处理人（送电）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sdqjc_dict": sdqjc_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 送电
class PowerTransmission(AjaxTodoBase):
    def __init__(self):
        super(PowerTransmission, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sd_dict = process_data.get("sd_dict")
        upload_file_json = {"送电-上传文件(如有)": []}
        upload_file_field_map = (
            ConstructionApplicationFieldHandle.get_upload_file_field_map("市电专项")
        )
        if "sd_other_file" in sd_dict:
            upload_file_field = upload_file_field_map["sd_other_file"]
            # 判断文件是否上传成功
            file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                sd_dict["sd_other_file"], upload_file_field
            )
            if not file_exit_flag:
                return file_exit_msg
            upload_file_json[upload_file_field] = TemplateAction.get_file_json(
                sd_dict["sd_other_file"], process_user
            )
        sd_dict.setdefault("upload_file_json", upload_file_json)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sd_dict": sd_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointExternalPowerReport(AjaxTodoBase):
    """
        合建项目报建-外电报装
    """

    def __init__(self):
        super(JointExternalPowerReport, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        wdbz_dict = process_data.get("wdbz_dict")
        for current_content in wdbz_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "wdbz_dict": wdbz_dict,
            "gdht_responsible_person": todo_handler,
            "ddxy_responsible_person": todo_handler,
            "gkkf_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointPowerSupplyContract(AjaxTodoBase):
    """
        合建项目报建-外电报装
    """

    def __init__(self):
        super(JointPowerSupplyContract, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gdht_dict = process_data.get("gdht_dict")
        for current_content in gdht_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "gdht_dict": gdht_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointDetectionBeforePowerTransmission(AjaxTodoBase):
    """
            合建项目报建-外电报装
        """

    def __init__(self):
        super(JointDetectionBeforePowerTransmission, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sdqjc_dict = process_data.get("sdqjc_dict")
        for current_content in sdqjc_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sdqjc_dict": sdqjc_dict,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class JointPowerTransmission(AjaxTodoBase):
    """
        合建项目报建-外电报装
    """

    def __init__(self):
        super(JointPowerTransmission, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sd_dict = process_data.get("sd_dict")
        for current_content in sd_dict.get("work_list"):
            if current_content.get("other_file", []):
                # 判断文件是否上传成功
                file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
                    current_content.get("other_file", []), current_content.get("work_content")
                )
                if not file_exit_flag:
                    return file_exit_msg
        # 获取待办处理人
        # （监理直接发包、工程直接发包、监理合同签订上传、工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(
            project_name, '合建方-项目经理'
        )
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "sd_dict": sd_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
