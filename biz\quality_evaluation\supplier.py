from iBroker.lib import mysql


class SupplierQuery(object):
    # 根据项目名称查项目品类供应商
    @staticmethod
    def query_project_category_supplier(project_name):
        # 本应动态获取再存到该表。
        # 但因数据来源未确定，故由阳阳姐人工提供数据，再由开发人员录入到该表中，流程中项目供应商从改表查询。
        # query_sql = (
        #     "SELECT * FROM quality_evaluation_project_supplier WHERE project_name = '%s' "
        #     % project_name
        # )
        # 改为从供应商资源表获取
        query_sql = f"SELECT * FROM supplier_resources WHERE CONCAT(campus, project) = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        supplier_dict = {}
        if query_result:
            category_name_key_map = {
                "AHU": "ahu",
                "chaifa": "cf",
                "transformer": "byq",
                "medium_voltage_cabinet": "zyg",
                "low_voltage_cabinet": "dyg",
                "headboard": "hvdc",
                "cabinet": "jg",
                "PDU": "pdu",
                "battery": "dc",
                "integrators": "jcs",
                "project_manager": "xg",
                "supervision": "jl",
                "examination": "cs",
            }
            for key, val in query_result[0].items():
                if category_name_key_map.get(key):
                    supplier_dict[category_name_key_map.get(key)] = val
        return supplier_dict

    # 根据项目名称查项目品类供应商
    @staticmethod
    def get_project_category_supplier(project_name):
        supplier_map = {
            "项目一": {
                "ahu": "ahu供应商",
                "cf": "cf供应商",
                "byq": "byq供应商",
                "zyg": "zyg供应商",
                "dyg": "dyg供应商",
                "hvdc": "hvdc供应商",
                "jg": "jg供应商",
                "pdu": "pdu供应商",
                "dc": "dc供应商",
                "jcs": "jcs供应商",
                "xg": "xg供应商",
                "jl": "jl供应商",
                "cs": "cs供应商",
            },
            "项目二": {
                "ahu": "ahu供应商",
                "cf": "cf供应商",
                "byq": "byq供应商",
                "zyg": "zyg供应商",
                "dyg": "dyg供应商",
                "hvdc": "hvdc供应商",
                "jg": "jg供应商",
                "pdu": "pdu供应商",
                "dc": "dc供应商",
                "jcs": "jcs供应商",
                "xg": "xg供应商",
                "jl": "jl供应商",
                "cs": "cs供应商",
            },
            "default": {
                "ahu": "AHU供应商",
                "cf": "柴发供应商",
                "byq": "变压器供应商",
                "zyg": "中压柜供应商",
                "dyg": "低压柜供应商",
                "hvdc": "综合配电柜供应商",
                "jg": "机柜供应商",
                "pdu": "PDU供应商",
                "dc": "电池供应商",
                "jcs": "集成商供应商",
                "xg": "项管供应商",
                "jl": "监理供应商",
                "cs": "测试供应商",
            },
            "南京江宁B2-1": {
                "ahu": "申菱",
                "cf": "科勒",
                "byq": "AEG",
                "zyg": "施耐德",
                "dyg": "任达",
                "hvdc": "中恒",
                "jg": "盛道",
                "pdu": "突破",
                "dc": "西恩迪",
                "jcs": "本贸",
                "xg": "华建",
                "jl": "中邮通",
                "cs": "创意银河",
            },
            "南京江宁B2-2": {
                "ahu": "申菱",
                "cf": "卡特",
                "byq": "AEG",
                "zyg": "施耐德",
                "dyg": "任达",
                "hvdc": "科华",
                "jg": "朗威",
                "pdu": "突破",
                "dc": "GNB",
                "jcs": "本贸",
                "xg": "华建",
                "jl": "汇源",
                "cs": "创意银河",
            },
            "贵安七星B2-1": {
                "ahu": "申菱",
                "cf": "科勒",
                "byq": "AEG",
                "zyg": "施耐德",
                "dyg": "任达",
                "hvdc": "科华",
                "jg": "朗威",
                "pdu": "盛道",
                "dc": "C&D",
                "jcs": "中兴",
                "xg": "华建",
                "jl": "广州汇源",
                "cs": "睿建智维",
            },
        }
        supplier_dict = (
            supplier_map[project_name]
            if project_name in supplier_map
            else supplier_map["default"]
        )
        return supplier_dict
