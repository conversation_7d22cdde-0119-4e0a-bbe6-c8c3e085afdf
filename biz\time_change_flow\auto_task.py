import json
import os
import uuid
from datetime import datetime

from iBroker.lib import config, mysql
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib


# 发送邮件
def send_email(
        email_title="",
        email_content="",
        email_receiver=None,
        submit_file_urls=None,
):
    """
    发送邮件
    :param email_title: 邮件标题
    :param email_content: 邮件内容
    :param email_receiver: 邮件接收人列表
    :param submit_file_urls: 提交文件列表
    """

    # 指定保存文件的完整路径
    save_directory = "/path/to/save"
    os.makedirs(save_directory, exist_ok=True)  # 创建保存路径
    file_data = {}
    try:
        if submit_file_urls:
            for file in submit_file_urls:
                file_name = file.get("name")
                file_url = file["response"]["FileList"][0]["url"]
                relative_url = file_url.split("cosfile")[1]
                new_file_name = str(uuid.uuid4()) + file_name
                save_file_path = os.path.join(save_directory, new_file_name)
                COSLib.download_2(file_name=relative_url, save_to_file=save_file_path)
                file_data[save_file_path] = file_name
    except KeyError:
        return {"code": -1, "msg": f"文件上传错误"}
    except IndexError:
        return {"code": -1, "msg": f"文件上传错误"}
    tof.send_attachment_email(
        filePathAndfileName=file_data,
        sendTitle=email_title,
        msgContent=email_content,
        sendTo=email_receiver,
    )


# 项目自动任务
class TimeChangeAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 获取项目信息
    def get_project_data(self, project_name):
        tb_db = mysql.new_mysql_instance("tbconstruct")
        team_rater_leader = config.get_config_map(
            "quality_evaluation_team_rater_leader"
        )
        business_rater = team_rater_leader["business_rater_leader"]
        operations_management_rater = config.get_config_string(
            "operations_management_rater"
        )
        select_sql = (
            f"select project_name,demand_delivery,PM,demand_distribution from risk_early_warning_data "
            f"where project_name = '{project_name}'"
        )
        result = tb_db.get_row(select_sql)
        demand_delivery = result["demand_delivery"]
        pm_rater = result["PM"]
        demand_distribution = result["demand_distribution"]
        variables = {
            "project_name": project_name,
            "demand_delivery": demand_delivery,
            "business_rater": business_rater,
            "operations_management_rater": operations_management_rater,
            "Pm_rater": pm_rater,
            "demand_distribution": demand_distribution,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 判断是否需要审批
    def need_to_approved(self, pm_rater, operations_management_rater, business_rater):
        creator = self.ctx.variables.get("Creator")
        variables = {}
        if creator == pm_rater:
            variables["need_pm_rater"] = 0
            variables["pm_pass"] = 1
        else:
            variables["need_pm_rater"] = 1
        if operations_management_rater == creator:
            variables["need_om_rater"] = 0
            variables["om_pass"] = 1
        else:
            variables["need_om_rater"] = 1
        if business_rater == creator:
            variables["need_business_rater"] = 0
            variables["business_pass"] = 1
        else:
            variables["need_business_rater"] = 1
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 判断是否全部通过
    def all_pass(self, om_pass, pm_pass):
        if om_pass == 1 and pm_pass == 1:
            variables = {"all_pass": 1}
        else:
            variables = {"all_pass": 0}
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 审批通过
    def pass_process(self, demand_distribution):
        tb_db = mysql.new_mysql_instance("tbconstruct")
        project_name = self.ctx.variables.get("project_name")
        change_time = self.ctx.variables.get("change_time")
        deliver_time = self.ctx.variables.get("demand_delivery")
        submit_file = self.ctx.variables.get("submit_file")
        pm_remark = self.ctx.variables.get("pm_remark")
        om_remark = self.ctx.variables.get("om_remark")
        submit_file_pm = self.ctx.variables.get("submit_file_pm")
        submit_file_om = self.ctx.variables.get("submit_file_om")
        submit_file_business = self.ctx.variables.get("submit_file_business")
        change_reason = self.ctx.variables.get("change_reason")
        ticket_id = self.ctx.variables.get("TicketId")
        operations_management_rater = self.ctx.variables.get(
            "operations_management_rater"
        )
        pm_rater = self.ctx.variables.get("Pm_rater")
        business_rater = self.ctx.variables.get("business_rater")
        creator = self.ctx.variables.get("Creator")
        date_start = datetime.strptime(demand_distribution, "%Y-%m-%d")
        date_now = datetime.strptime(change_time, "%Y-%m-%d")
        if submit_file:
            submit_file_all = submit_file.copy()
        else:
            submit_file_all = []

        if submit_file_pm:
            submit_file_all.extend(submit_file_pm)
            if submit_file:
                submit_file_pm.extend(submit_file)
        else:
            submit_file_pm = [] if not submit_file else submit_file

        if submit_file_om:
            submit_file_all.extend(submit_file_om)
            if submit_file:
                submit_file_om.extend(submit_file)
        else:
            submit_file_om = [] if not submit_file else submit_file

        if submit_file_business:
            submit_file_all.extend(submit_file_business)
            if submit_file:
                submit_file_business.extend(submit_file)
        else:
            submit_file_business = [] if not submit_file else submit_file
        month_diff = (
                (date_now.year - date_start.year) * 12 + date_now.month - date_start.month
        )
        condition = {
            "project_name": project_name,
        }
        update_data = {
            "demand_delivery": change_time,
            "estimation_period": month_diff,
        }
        creator = self.ctx.variables.get("Creator")
        insert_data_log = {
            "change_reason": change_reason,
            "creator": creator,
            "project_name": project_name,
            "demand_delivery_old": deliver_time,
            "demand_delivery_new": change_time,
            "pm_remark": pm_remark,
            "om_remark": om_remark,
            "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "submit_file_urls": json.dumps(submit_file_all),
        }
        tb_db.begin()
        tb_db.update("risk_early_warning_data", update_data, condition)
        tb_db.insert("risk_early_warning_data_log", insert_data_log)
        tb_db.commit()
        email_title = f"{project_name}项目交付时间变更审批结果通知"
        email_content = (f"<br>【工单号】{ticket_id}<br>【申请人】 {creator}<br>"
                         f"【初始时间】 {deliver_time}<br>"
                         f"【变更后时间】 {change_time}<br>"
                         f"【变更原因】 {change_reason}<br>【审批结果】同意变更<br>")
        send_email(
            email_title,
            email_content,
            email_receiver=[creator + "@tencent.com"],
            submit_file_urls=submit_file,
        )
        # send_email(
        #     email_title,
        #     email_content,
        #     email_receiver=[pm_rater + "@tencent.com"],
        #     submit_file_urls=submit_file_pm,
        # )
        # send_email(
        #     email_title,
        #     email_content,
        #     email_receiver=[business_rater + "@tencent.com"],
        #     submit_file_urls=submit_file_business,
        # )
        # send_email(
        #     email_title,
        #     email_content,
        #     email_receiver=[operations_management_rater + "@tencent.com"],
        #     submit_file_urls=submit_file_pm,
        # )
        # 发起总控计划修改流程
        data = {
            "CustomVariables": {
                "project_name": project_name,
            },
            "ProcessDefinitionKey": "general_control_plan_modification_process",
            "Source": "",
            "TicketDescription": "交付时间变更流程出发总控计划修改流程",
            "TicketLevel": "3",
            "TicketTitle": "交付时间变更流程出发总控计划修改流程",
            "UserInfo": {"Concern": "v_mmywang", "Creator": creator},
        }
        gnetops.request(action="Ticket", method="Create", data=data)
        flow.complete_task(self.ctx.task_id, variables={"email_title": email_title, "email_content": email_content,
                                                        "submit_file_all": submit_file_all})

    # 审批拒绝
    def reject_process(self, pm_rater):
        project_name = self.ctx.variables.get("project_name")
        change_reason = self.ctx.variables.get("change_reason")
        operations_management_rater = self.ctx.variables.get(
            "operations_management_rater"
        )
        business_rater = self.ctx.variables.get("business_rater")
        creator = self.ctx.variables.get("Creator")
        submit_file = self.ctx.variables.get("submit_file")
        submit_file_om = self.ctx.variables.get("submit_file_om")
        submit_file_business = self.ctx.variables.get("submit_file_business")
        ticket_id = self.ctx.variables.get("TicketId")
        email_title = f"{project_name}项目交付时间变更审批结果通知"
        submit_file_pm = self.ctx.variables.get("submit_file_pm")
        submit_file_all = submit_file.copy()
        if submit_file_pm:
            submit_file_all.append(submit_file_pm)
        if submit_file_om:
            submit_file_all.append(submit_file_om)
        if submit_file_business:
            submit_file_all.append(submit_file_business)
        submit_file_pm.append(submit_file)
        submit_file_om.append(submit_file)
        submit_file_business.append(submit_file)
        email_content = f"<br>【工单号】{ticket_id}<br>【申请人】 {creator}<br>【变更原因】 {change_reason}<br>【审批结果】维持原交付目标附件<br>"
        send_email(
            email_title,
            email_content,
            email_receiver=[creator + "@tencent.com"],
            submit_file_urls=submit_file,
        )
        send_email(
            email_title,
            email_content,
            email_receiver=[pm_rater + "@tencent.com"],
            submit_file_urls=submit_file_pm,
        )
        send_email(
            email_title,
            email_content,
            email_receiver=[business_rater + "@tencent.com"],
            submit_file_urls=submit_file_business,
        )
        send_email(
            email_title,
            email_content,
            email_receiver=[operations_management_rater + "@tencent.com"],
            submit_file_urls=submit_file_pm,
        )
        flow.complete_task(self.ctx.task_id)
