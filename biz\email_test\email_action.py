
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate


class EditEmail(AjaxTodoBase):
    """
        邮件编辑
    """
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        email_title = "模板标题"
        choose_list = [
            {
               'risk_item':"交付延期",
                'state': '有风险',
                'effect': '进度',
                'details_reason': '无'
            },
            
            {
               'risk_item':"供应商资源",
                'state': '有风险',
                'effect': '进度',
                'details_reason': '无'
            }
        ]
        rich_text_template = f"<h1>{email_title}</h1>\n"
        rich_text_template += "<ol>\n"
        for item in choose_list:
            rich_text_template += f"<li>风险项：{item['risk_item']}；状态：{item['state']}；影响(进度/质量/合规)：{item['effect']}；具体情况及原因：{item['details_reason']}</li>\n"
        rich_text_template += "</ol>"
        WorkflowVarUpdate(
            instance_id = self.ctx.instance_id,
            variables={
                "rich_text_template": rich_text_template,
            }
        ).call()

    def end(self, process_data, process_user):
        email_title = process_data.get("email_title")
        email_content = process_data.get("email_content")
        email_receiver = process_data.get("email_receiver")
        email_Cc = process_data.get("email_Cc")
        rich_text = process_data.get("rich_text")
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        variables = {
            'email_title': email_title,
            'email_content': email_content,
            'email_receiver': email_receiver,
            'email_Cc': email_Cc,
            'rich_text': rich_text,
            'email_Cc_list': email_Cc_list,
            'email_receiver_list': email_receiver_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendEmail(object):
    """
        邮件发送
    """
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def email_sending(self):
        email_title = self.ctx.variables.get("email_title")
        email_receiver_list = self.ctx.variables.get("email_receiver_list")
        email_Cc_list = self.ctx.variables.get("email_Cc_list")
        rich_text = self.ctx.variables.get("rich_text")
        # rich_text_template = self.ctx.variables.get("rich_text_template")

        choose_list = [
            {
               'risk_item':"交付延期",
                'state': '有风险',
                'effect': '进度',
                'details_reason': '无'
            },
            
            {
               'risk_item':"供应商资源",
                'state': '有风险',
                'effect': '进度',
                'details_reason': '无'
            }
        ]
        # rich_text_template = f"""
        #                         <h1>{email_title}</h1>
        #                         <h2>1.基础信息</h2>
        #                         <p class='ql-indent-1'>项目名称：项目管理技术评标</p>
        #                         <p class='ql-indent-1'>项目概况:4.12_TB项目管理技术评标评分</p><h2>
        #                         <h2>2.汇总表</h2>
        #                         <table border='1' cellspacing='0'>
        #                         <tr>
        #                             <th>风险项</th>
        #                             <th>状态</th>
        #                             <th>影响(进度/质量/合规)</th>
        #                             <th>具体情况及原因</th>
        #                         </tr>
        #                     """
        # for item in choose_list:
        #     rich_text_template += "<tr>"
        #     rich_text_template += f"<td>{item['risk_item']}</td>"
        #     rich_text_template += f"<td>{item['state']}</td>"
        #     rich_text_template += f"<td>{item['effect']}</td>"
        #     rich_text_template += f"<td>{item['details_reason']}</td>"
        #     rich_text_template += "</tr>\n"
        # rich_text_template += "</table>"
        # rich_text_template += """
        #                         <p class="ql-indent-1">审核：同意</p>
        #                         <p class="ql-indent-1">备注：无</p>
        #                         """

        msg_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件通知</title>
                <style>
                    .title{{
                        margin-bottom: 10px;
                        color: #212529;
                        text-align: center;
                    }}
                    .info-title{{
                        display: inline-block;
                        width: 7rem;
                        text-align: right;
                        font-weight: bold;
                    }}
                    .table-head {{
                        color: #fff;
                        background-color: #343a40;
                        border-color: #454d55;
                        padding: 0.75rem;
                        font-size: 0;
                        font-weight: bold;
                        text-align: center;
                    }}
                    .table-head-item{{
                        display: inline-block;
                        font-size: 16px;
                    }}
                    .row-box{{
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                        font-size: 0;
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }}
                    .row-item{{
                        display: inline-block; font-size: 16px;
                    }}
                </style>
            </head>
            <body>
                <div style="font-size: 16px;">
                    <h2 class="title">{email_title}</h2>
                    <div>
                        <h3>1. 基础信息</h3>
                        <p><span class="info-title">项目名称：</span>项目名称</p>
                        <p><span class="info-title">项目概况：</span>项目概况</p>
                    </div>
                    <div>
                        <h3>2. 汇总表</h3>
                    </div>
                </div>

                <div class="table-head">
                    <span class="table-head-item" style="width: 5%;">序号</span>
                    <span class="table-head-item" style="width: 10%;">风险项</span>
                    <span class="table-head-item" style="width: 10%;">状态</span>
                    <span class="table-head-item" style="width: 10%;">影响(进度/质量/合规)</span>
                    <span class="table-head-item" style="width: 65%;">具体情况及原因</span>
                </div>
            """
        for index, value in enumerate(choose_list):
            msg_content += f"""
                <div class="row-box">
                    <span class="row-item" style="width: 5%;">{index + 1}</span>
                    <span class="row-item" style="width: 10%;">{value.get('risk_item')}</span>
                    <span class="row-item" style="width: 10%;">{value.get('state')}</span>
                    <span class="row-item" style="width: 10%;">{value.get('effect')}</span>
                    <span class="row-item" style="width: 65%;text-align: center;">{value.get('details_reason')}</span>
                </div>
                """
        msg_content += """
            </body>
            </html>
        """
        tof.send_email(sendTitle=email_title, msgContent=msg_content,
                       sendTo=email_receiver_list,
                       sendCopy=email_Cc_list)
        variables = {
            'table_html': msg_content,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
