import uuid
import numpy as np
import pandas as pd
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, curl, config
from iBroker.sdk.notification.chatops import ChatOpsSend
from biz.construction_application_process.construction_application_info import (
    ConstructionApplicationInfoQuery,
)
from biz.construction_process.cos_lib import COSLib
from biz.construction_final_acceptance.tools import Tools


# 施工准备阶段-报建流程 自动任务类
class ConstructionApplicationProcessAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query_project_construction_application_process_info(self):
        """
        从总控计划中取出 施工准备阶段-项目报建 基本信息
        """
        project_name = self.ctx.variables.get("project_name")
        project_code = Tools.get_project_code(project_name)
        # overall_control_plan_ticket_id = (
        #     ConstructionApplicationInfoQuery.get_project_overall_control_plan_ticket_id(
        #         project_name
        #     )
        # )
        # 获取PM,找不到责任人的待办都给PM
        PM = ConstructionApplicationInfoQuery.get_PM(project_name)
        overall_control_plan_ticket_id = ""
        # xmbj_responsible_person = PM
        project_construction_application_info = (
            ConstructionApplicationInfoQuery.get_special_work_info(project_name, "2.4")
        )
        if project_construction_application_info:
            overall_control_plan_ticket_id = project_construction_application_info[
                0
            ].get("overall_control_plan_ticket_id")
            # if (
            #     project_construction_application_info[0].get("responsible_person")
            #     and project_construction_application_info[0].get("responsible_person")
            #     != "Js_xxx"
            # ):
            #     xmbj_responsible_person = project_construction_application_info[0].get(
            #         "responsible_person",
            #     )
        # "消防第三方测试报告"待办处理人获取
        # fire_report_handler = ConstructionApplicationInfoQuery.get_fire_report_handler(
        #     project_name
        # )
        # 获取待办处理人（消防第三方测试报告、上传报建计划）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        is_hire = ConstructionApplicationInfoQuery.whether_is_rental_project(project_name)
        if is_hire:
            todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        # if not fire_report_handler:
        #     fire_report_handler = PM
        variables = {
            "project_name": project_name,
            "project_code": project_code,
            "PM": PM,
            "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
            "xmbj_list": project_construction_application_info,
            "xmbj_responsible_person": todo_handler,
            "fire_report_handler": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_mains_power_plan_process_info(self):
        """
        从总控计划中取出 施工准备阶段-市电专项 基本信息
        """
        project_name = self.ctx.variables.get("project_name")
        # overall_control_plan_ticket_id = (
        #     ConstructionApplicationInfoQuery.get_project_overall_control_plan_ticket_id(
        #         project_name
        #     )
        # )
        # 获取PM,找不到责任人的待办都给PM
        PM = ConstructionApplicationInfoQuery.get_PM(project_name)
        overall_control_plan_ticket_id = ""
        # sdzx_responsible_person = PM
        mains_power_plan_info = ConstructionApplicationInfoQuery.get_special_work_info(
            project_name, "2.9"
        )
        if mains_power_plan_info:
            overall_control_plan_ticket_id = mains_power_plan_info[0].get(
                "overall_control_plan_ticket_id"
            )
            # if (
            #     mains_power_plan_info[0].get("responsible_person")
            #     and mains_power_plan_info[0].get("responsible_person") != "Js_xxx"
            # ):
            #     sdzx_responsible_person = mains_power_plan_info[0].get(
            #         "responsible_person"
            #     )
        # 获取待办处理人（上传市电计划）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        is_hire = ConstructionApplicationInfoQuery.whether_is_rental_project(project_name)
        if is_hire:
            todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "project_name": project_name,
            "PM": PM,
            "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
            "sdzx_list": mains_power_plan_info,
            "sdzx_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def analysis_project_construction_application(self, xmbj_sub_plan):
        """
        解析项目报建分项计划
        """
        # 获取url
        url = xmbj_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl")
        df = df.replace({np.nan: None})
        df = df.loc[df["序号"].str.startswith("2.4.", na=False), :]
        # 对应字段
        all_filed_dict = {
            "xmlx_dict": {},
            "sjhtqt_dict": {},
            "tzss_dict": {},
            "jlfb_dict": {},
            "gcfb_dict": {},
            "jlhtqd_dict": {},
            "gchtqj_dict": {},
            "nmgbzjjghtqd_dict": {},
            "gsywx_dict": {},
            "sgxkz_dict": {},
            "xfba_dict": {},
            "jgba_dict": {},
            # "dagzlgd_dict": {},
        }
        filed_map = {
            "2.4.1": "xmlx_dict",
            "2.4.2": "sjhtqt_dict",
            "2.4.3": "tzss_dict",
            "2.4.4": "jlfb_dict",
            "2.4.5": "gcfb_dict",
            "2.4.6": "jlhtqd_dict",
            "2.4.7": "gchtqj_dict",
            "2.4.8": "nmgbzjjghtqd_dict",
            "2.4.9": "gsywx_dict",
            "2.4.10": "sgxkz_dict",
            "2.4.11": "xfba_dict",
            "2.4.12": "jgba_dict",
            # "2.4.13": "dagzlgd_dict",
        }
        # 解析
        for _, row in df.iterrows():
            seq_no = str(row["序号"])
            work_content = row["工作内容"]
            construction_time = row["工期(日历日）"]
            start_time = row["开始时间（年/月/日）"].strftime("%Y-%m-%d")
            finish_time = row["完成时间（年/月/日）"].strftime("%Y-%m-%d")
            responsible_person = row["责任人（开通账号人员）"]
            input = row["输入物"]
            output = row["输出物"]
            construction_attribute = row["施工属性（电气/暖通/弱电/装修（结构）/消防）"]
            # 三级
            if seq_no in filed_map:
                all_filed_dict[filed_map[seq_no]].setdefault("seq_no", seq_no)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "work_content", work_content
                )
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "construction_time", construction_time
                )
                all_filed_dict[filed_map[seq_no]].setdefault("start_time", start_time)
                all_filed_dict[filed_map[seq_no]].setdefault("finish_time", finish_time)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "responsible_person", responsible_person
                )
                all_filed_dict[filed_map[seq_no]].setdefault("input", input)
                all_filed_dict[filed_map[seq_no]].setdefault("output", output)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "construction_attribute", construction_attribute
                )
            elif seq_no[: seq_no.rfind(".")] in filed_map:
                if (
                        "work_list"
                        not in all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]]
                ):
                    all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]][
                        "work_list"
                    ] = []
                all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]][
                    "work_list"
                ].append(
                    {
                        "seq_no": seq_no,
                        "work_content": work_content,
                        "construction_time": construction_time,
                        "start_time": start_time,
                        "finish_time": finish_time,
                        "responsible_person": responsible_person,
                        "input": input,
                        "output": output,
                        "construction_attribute": construction_attribute,
                    }
                )
        # 获取待办处理人（主要是下一个节点: 项目立项、设计合同签订上传、图纸送审）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        is_hire = ConstructionApplicationInfoQuery.whether_is_rental_project(project_name)
        if is_hire:
            todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "xmlx_dict": all_filed_dict["xmlx_dict"],
            "sjhtqt_dict": all_filed_dict["sjhtqt_dict"],
            "tzss_dict": all_filed_dict["tzss_dict"],
            "jlfb_dict": all_filed_dict["jlfb_dict"],
            "gcfb_dict": all_filed_dict["gcfb_dict"],
            "jlhtqd_dict": all_filed_dict["jlhtqd_dict"],
            "gchtqj_dict": all_filed_dict["gchtqj_dict"],
            "nmgbzjjghtqd_dict": all_filed_dict["nmgbzjjghtqd_dict"],
            "gsywx_dict": all_filed_dict["gsywx_dict"],
            "sgxkz_dict": all_filed_dict["sgxkz_dict"],
            "xfba_dict": all_filed_dict["xfba_dict"],
            "jgba_dict": all_filed_dict["jgba_dict"],
            # "dagzlgd_dict": all_filed_dict["dagzlgd_dict"],
            "xmlx_responsible_person": todo_handler,
            "sjhtqt_responsible_person": todo_handler,
            "tzss_responsible_person": todo_handler,
            "jlfb_responsible_person": todo_handler,
            "gcfb_responsible_person": todo_handler,
            "jlhtqd_responsible_person": todo_handler,
            "gchtqj_responsible_person": todo_handler,
            "nmgbzjjghtqd_responsible_person": todo_handler,
            "gsywx_responsible_person": todo_handler,
            "sgxkz_responsible_person": todo_handler,
            "xfba_responsible_person": todo_handler,
            "jgba_responsible_person": todo_handler,
            # "dagzlgd_responsible_person": all_filed_dict["dagzlgd_dict"]["responsible_person"],
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def analysis_mains_power_plan(self, sdzx_sub_plan):
        """
        解析市电分项计划
        "2.9.1": "外电报装",
        "2.9.2": "外电方案",
        "2.9.3": "高可靠费缴费",
        "2.9.4": "供电合同签订",
        "2.9.5": "调度协议签订",
        "2.9.6": "送电前检测",
        "2.9.7": "送电",
        """
        # 获取url
        url = sdzx_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl")
        df = df.replace({np.nan: None})
        df = df.loc[df["序号"].str.startswith("2.9.", na=False), :]
        # 对应字段
        all_filed_dict = {
            "wdbz_dict": {},
            "wdfa_dict": {},
            "gkkf_dict": {},
            "gdht_dict": {},
            "ddxy_dict": {},
            "sdqjc_dict": {},
            "sd_dict": {},
        }
        filed_map = {
            "2.9.1": "wdbz_dict",
            "2.9.2": "wdfa_dict",
            "2.9.3": "gkkf_dict",
            "2.9.4": "gdht_dict",
            "2.9.5": "ddxy_dict",
            "2.9.6": "sdqjc_dict",
            "2.9.7": "sd_dict",
        }
        # 解析
        for _, row in df.iterrows():
            seq_no = str(row["序号"])
            work_content = row["工作内容"]
            construction_time = row["工期(日历日）"]
            start_time = row["开始时间（年/月/日）"].strftime("%Y-%m-%d")
            finish_time = row["完成时间（年/月/日）"].strftime("%Y-%m-%d")
            responsible_person = row["责任人（开通账号人员）"]
            input = row["输入物"]
            output = row["输出物"]
            construction_attribute = row["施工属性（电气/暖通/弱电/装修（结构）/消防）"]
            # 三级
            if seq_no in filed_map:
                all_filed_dict[filed_map[seq_no]].setdefault("seq_no", seq_no)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "work_content", work_content
                )
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "construction_time", construction_time
                )
                all_filed_dict[filed_map[seq_no]].setdefault("start_time", start_time)
                all_filed_dict[filed_map[seq_no]].setdefault("finish_time", finish_time)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "responsible_person", responsible_person
                )
                all_filed_dict[filed_map[seq_no]].setdefault("input", input)
                all_filed_dict[filed_map[seq_no]].setdefault("output", output)
                all_filed_dict[filed_map[seq_no]].setdefault(
                    "construction_attribute", construction_attribute
                )
            elif seq_no[: seq_no.rfind(".")] in filed_map:
                if (
                        "work_list"
                        not in all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]]
                ):
                    all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]][
                        "work_list"
                    ] = []
                all_filed_dict[filed_map[seq_no[: seq_no.rfind(".")]]][
                    "work_list"
                ].append(
                    {
                        "seq_no": seq_no,
                        "work_content": work_content,
                        "construction_time": construction_time,
                        "start_time": start_time,
                        "finish_time": finish_time,
                        "responsible_person": responsible_person,
                        "input": input,
                        "output": output,
                        "construction_attribute": construction_attribute,
                    }
                )
        # 获取待办处理人（主要是下一个节点: 送电前检测、外电报装、外电方案）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        is_hire = ConstructionApplicationInfoQuery.whether_is_rental_project(project_name)
        if is_hire:
            todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "wdbz_dict": all_filed_dict["wdbz_dict"],
            "wdfa_dict": all_filed_dict["wdfa_dict"],
            "gkkf_dict": all_filed_dict["gkkf_dict"],
            "gdht_dict": all_filed_dict["gdht_dict"],
            "ddxy_dict": all_filed_dict["ddxy_dict"],
            "sdqjc_dict": all_filed_dict["sdqjc_dict"],
            "sd_dict": all_filed_dict["sd_dict"],
            "wdbz_responsible_person": todo_handler,
            "wdfa_responsible_person": todo_handler,
            "gkkf_responsible_person": todo_handler,
            "gdht_responsible_person": todo_handler,
            "ddxy_responsible_person": todo_handler,
            "sdqjc_responsible_person": todo_handler,
            "sd_responsible_person": todo_handler,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_tzss_info(self, project_name, construction_application_process_ticket_id):
        """
        获取存量项目图纸送审节点信息。（项目报建-图纸送审存量问题）
        """
        project_code = Tools.get_project_code(project_name)
        tzss_dict = {}
        xmbj_list = []
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {"InstanceId": "", "TicketId": ""},
                "SearchCondition": {
                    "TicketId": [str(construction_application_process_ticket_id)]
                },
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List")
        if result_list:
            instance_id = result_list[0].get("InstanceId")
        else:
            return {"code": 400, "msg": "未获取到流程id"}
        # 从流程变量获取数据
        main_flow_vars = flow.get_variables(
            instance_id,
            ["tzss_dict", "xmbj_list", "PM", "overall_control_plan_ticket_id"],
        )
        tzss_dict = main_flow_vars.get("tzss_dict", {})
        xmbj_list = main_flow_vars.get("xmbj_list", [])
        PM = main_flow_vars.get("PM", None)
        overall_control_plan_ticket_id = main_flow_vars.get(
            "overall_control_plan_ticket_id", None
        )
        # 审图合格证（旧：审图报告）
        tzss_dict.setdefault(
            "drawing_review_certificate_file",
            tzss_dict.get("drawing_review_report_file", []),
        )
        # 结构力学图（新增）
        tzss_dict.setdefault("structural_mechanics_book_file", [])
        # 删除审图报告
        tzss_dict.pop("drawing_review_report_file")
        # 获取待办处理人（图纸送审）
        project_name = self.ctx.variables.get("project_name")
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '项管-项目经理')
        is_hire = ConstructionApplicationInfoQuery.whether_is_rental_project(project_name)
        if is_hire:
            todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "project_name": project_name,
            "project_code": project_code,
            "construction_application_process_ticket_id": construction_application_process_ticket_id,
            "overall_control_plan_ticket_id": overall_control_plan_ticket_id,
            "PM": PM,
            "xmbj_list": xmbj_list,
            "tzss_dict": tzss_dict,
            "tzss_responsible_person": todo_handler,
        }
        self.workflow_detail.add_kv_table("info", {"message": variables})
        flow.complete_task(self.ctx.task_id, variables=variables)

    def tzss_PM_check_feedback(
            self, project_name, appendix_list, real_time, deal_users
    ):
        """
        图纸送审-PM确认，确认后状态回传给星环
        """
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )

        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            data_po_list = []
            # 物料id
            material_id_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"},
                )
                po_info = resp.json()
                # self.workflow_detail.add_kv_table("2.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    # 所有的物料id
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}2.获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "3.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "4.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "5.物料信息", {"message": len(material_list)}
            )
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    # 物料id
                    material_id = material.get("materialQueryDTO").get("materialId")
                    # 品名
                    material_category_name = material.get("materialQueryDTO").get(
                        "materialCategoryName"
                    )
                    # 品名编码
                    material_category_code = material.get("materialQueryDTO").get(
                        "materialCategoryCode"
                    )
                    if (
                            material_category_name == "报审报建服务"
                            and material_category_code == "796350CACS"
                    ):
                        data.append(material_id)
                self.workflow_detail.add_kv_table("6.匹配的物料id", {"message": data})
                for d in data:
                    for po in po_material_info:
                        if po.get("orderCode") and d == po.get("materialId"):
                            data_po_list.append(po.get("orderCode"))
                            data_list.append(
                                {
                                    "AssetCode": "",
                                    "LineDetailId": str(po.get("orderItemId")),
                                    "QuantityReceived": po.get("orderAmount"),
                                    "SnCode": "",
                                    "AttachmentList": appendix_list,
                                }
                            )

            self.workflow_detail.add_kv_table("7.传参信息", {"message": data_list})
            # 获取企微id
            chat_info = gnetops.request(
                action="Tof",
                method="GetIDCStaffIDByEngName",
                ext_data={
                    "EngName": str(deal_users),
                },
            )
            self.workflow_detail.add_kv_table(
                "8.获取企微id", {"message": str(chat_info)}
            )
            chat_id = ""
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table(
                    "8.获取企微id",
                    {"success": False, "data": f"获取失败:{str(chat_info)}"},
                )
            # 分批次调用回传接口
            chunk_size = 500
            sub_lists = [
                data_list[i: i + chunk_size]
                for i in range(0, len(data_list), chunk_size)
            ]
            info_list = []
            info_failed_list = []
            for i in range(len(sub_lists)):
                info = gnetops.request(
                    action="Tbconstruct",
                    method="FeedbackAcceptanceStatusToStar",
                    ext_data={
                        "FeedbackType": 5,  # 0=到货,1=安装,2=初验,3=终,人员入场=4,图纸已送审=5
                        "ReceiveDate": str(real_time),  # 时间
                        "ReceiveUserId": chat_id,  # id
                        "Orderdetails": sub_lists[i],
                    },
                )
                self.workflow_detail.add_kv_table(
                    f"9.第{i}次回传数据接口返回数据", {"message": info}
                )
                info_list.append(str(info))
                # 判断是否验收成功
                if info.get("status") != "success":
                    info_failed_list.append(info)
            # 推送消息
            acceptance_result_receivers = config.get_config_map(
                "acceptance_result_receivers"
            )
            receivers = [deal_users, *acceptance_result_receivers]
            title = f"{project_name}图纸送审状态确认"
            if info_failed_list:
                message = f"{project_name}图纸送审状态传递失败"
                for info in info_failed_list:
                    message += f"\n{info.get('message')}"
            else:
                message = f"{project_name}图纸送审状态传递成功"
            # 应用消息
            tof.send_company_wechat_message(
                receivers=receivers, title=title, message=message
            )
            # dcops消息
            for account in receivers:
                ChatOpsSend(
                    user_name=account,
                    msg_content=message,
                    msg_type="text",
                    des_type="single",
                ).call()
            variables = {
                "po_material_info": len(po_material_info),
                "material_id_list": len(material_id_list),
                "material_list": material_list,
                "data_list": data_list,
                "data_po_list": data_po_list,
                "chat_id": chat_id,
                "info_list": info_list,
                "info_failed_list": info_failed_list,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table(
                "2.获取po信息", {"message": "获取不到需求单号"}
            )
            return {"code": -1, "message": "获取不到需求单号"}

    def tzss_PM_check_feedback_test(
            self, project_name, appendix_list, real_time, deal_users, po_info
    ):
        """
        图纸送审-PM确认，确认后状态回传给星环
        测试环境 模拟数据处理
        """
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )

        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            data_po_list = []
            # 物料id
            material_id_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                # resp = curl.get(
                #         title="Dcops获取po信息",
                #         system_name="Dcops",
                #         url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/"
                #             f"{code_id}",
                #         postdata="",
                #         append_header={"Content-Type": "application/json"})
                # po_info = resp.json()
                po_info = self.ctx.variables.get("po_info")
                # self.workflow_detail.add_kv_table("2.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    # 所有的物料id
                    po_material_info += po_info.get("data")
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}2.获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "3.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "4.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "5.物料信息", {"message": len(material_list)}
            )
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    # 物料id
                    material_id = material.get("materialQueryDTO").get("materialId")
                    # 品名
                    material_category_name = material.get("materialQueryDTO").get(
                        "materialCategoryName"
                    )
                    # 品名编码
                    material_category_code = material.get("materialQueryDTO").get(
                        "materialCategoryCode"
                    )
                    if (
                            material_category_name == "报审报建服务"
                            and material_category_code == "796350CACS"
                    ):
                        data.append(material_id)
                self.workflow_detail.add_kv_table("6.匹配的物料id", {"message": data})
                for d in data:
                    for po in po_material_info:
                        if po.get("orderCode") and d == po.get("materialId"):
                            data_po_list.append(po.get("orderCode"))
                            data_list.append(
                                {
                                    "AssetCode": "",
                                    "LineDetailId": str(po.get("orderItemId")),
                                    "QuantityReceived": po.get("orderAmount"),
                                    "SnCode": "",
                                    "AttachmentList": appendix_list,
                                }
                            )

            self.workflow_detail.add_kv_table("7.传参信息", {"message": data_list})
            # 获取企微id
            chat_info = gnetops.request(
                action="Tof",
                method="GetIDCStaffIDByEngName",
                ext_data={
                    "EngName": str(deal_users),
                },
            )
            self.workflow_detail.add_kv_table(
                "8.获取企微id", {"message": str(chat_info)}
            )
            chat_id = ""
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table(
                    "8.获取企微id",
                    {"success": False, "data": f"获取失败:{str(chat_info)}"},
                )
            # 分批次调用回传接口
            chunk_size = 500
            sub_lists = [
                data_list[i: i + chunk_size]
                for i in range(0, len(data_list), chunk_size)
            ]
            info_list = []
            info_failed_list = []
            for i in range(len(sub_lists)):
                # info = gnetops.request(
                #     action="Tbconstruct",
                #     method="FeedbackAcceptanceStatusToStar",
                #     ext_data={
                #         "FeedbackType": 5,  # 0=到货,1=安装,2=初验,3=终,人员入场=4,图纸已送审=5
                #         "ReceiveDate": str(real_time),  # 时间
                #         "ReceiveUserId": chat_id,  # id
                #         "Orderdetails": sub_lists[i],
                #     },
                # )
                # 回传数据
                feedback_info = {
                    "Action": "Tbconstruct",
                    "Method": "FeedbackAcceptanceStatusToStar",
                    "FeedbackType": 5,  # 0=到货,1=安装,2=初验,3=终,人员入场=4,图纸已送审=5
                    "ReceiveDate": str(real_time),  # 时间
                    "ReceiveUserId": chat_id,  # id
                    "Orderdetails": data_list,
                    "SystemId": "2",
                }
                self.workflow_detail.add_kv_table(
                    f"9.第{i}次回传数据接口信息", {"message": feedback_info}
                )
                response_info = self.ctx.variables.get("response_info")
                info = response_info
                self.workflow_detail.add_kv_table(
                    f"9.第{i}次回传数据接口返回数据", {"message": info}
                )
                info_list.append(info)
                # 判断是否验收成功
                if info.get("status") != "success":
                    info_failed_list.append(info)
            # 推送消息
            deal_users = "v_vikyjiang"
            receivers = [deal_users]
            title = f"{project_name}图纸送审状态确认"
            if info_failed_list:
                message = f"{project_name}图纸送审状态传递失败"
                for info in info_failed_list:
                    message += f"\n{info.get('message')}"
            else:
                message = f"{project_name}图纸送审状态传递成功"
            # 应用消息
            tof.send_company_wechat_message(
                receivers=receivers, title=title, message=message
            )
            # dcops消息
            for account in receivers:
                ChatOpsSend(
                    user_name=account,
                    msg_content=message,
                    msg_type="text",
                    des_type="single",
                ).call()
            variables = {
                "po_material_info": len(po_material_info),
                "material_id_list": len(material_id_list),
                "material_list": material_list,
                "data_list": data_list,
                "data_po_list": data_po_list,
                "chat_id": chat_id,
                "info_list": info_list,
                "info_failed_list": info_failed_list,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table(
                "2.获取po信息", {"message": "获取不到需求单号"}
            )
            return {"code": -1, "message": "获取不到需求单号"}

    def analysis_project_control_plan(self, project_name):
        """
        合建项目-报建-解析总控计划
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql_1 = ("SELECT serial_number,work_content,start_time,completion_time,project_progress,construction_attribute "
                 f"FROM construct_plan_data WHERE project_name='{project_name}' AND not_involved='0'")
        query_data = db.get_all(sql_1)
        current_section = ""
        current_work = ""
        # 对应字段
        all_filed_dict = {
            "xmlx_dict": {"work_list": []},
            "jlfb_dict": {"work_list": []},
            "sgxkz_dict": {"work_list": []},
            "xfba_dict": {"work_list": []},
            "jgba_dict": {"work_list": []},
            # "dagzlgd_dict": {},
        }
        if query_data:
            for row in query_data:
                seq_no = row.get("serial_number", "")
                work_content = row.get('work_content', "")
                start_time = row.get('start_time', "")
                completion_time = row.get('completion_time', "")
                project_progress = row.get('project_progress', "")
                construction_attribute = row.get('construction_attribute', "")
                current_dict = {
                    "seq_no": seq_no,
                    "work_content": work_content,
                    "start_time": start_time,
                    "finish_time": completion_time,
                    "construction_time": project_progress,
                    "construction_attribute": construction_attribute
                }
                # 检查是否是2级标题
                if '.' not in seq_no or seq_no.count('.') == 1:
                    if work_content == "项目报建（含消防）":
                        current_work = work_content
                        current_section = seq_no
                        all_filed_dict["xmlx_dict"].update({
                            "start_time": start_time,
                            "finish_time": completion_time
                        })
                    continue
                # 获取三级标题内容
                if current_work == "项目报建（含消防）":
                    self.workflow_detail.add_kv_table("报建工作项", {"workcontent": work_content})
                    if seq_no.startswith(current_section) and seq_no.count('.') == 2:
                        if work_content in ["项目立项", "设计合同签订", "图纸送审"]:
                            all_filed_dict["xmlx_dict"]["work_list"].append(current_dict)
                        elif work_content not in ["施工许可证下发", "消防备案", "竣工备案"]:
                            all_filed_dict["jlfb_dict"]["work_list"].append(current_dict)
                        if work_content == "施工许可证下发":
                            all_filed_dict["sgxkz_dict"]["work_list"].append(current_dict)
                        elif work_content == "消防备案":
                            all_filed_dict["xfba_dict"]["work_list"].append(current_dict)
                        elif work_content == "竣工备案":
                            all_filed_dict["jgba_dict"]["work_list"].append(current_dict)
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "xmlx_dict": all_filed_dict["xmlx_dict"],
            "jlfb_dict": all_filed_dict["jlfb_dict"],
            "sgxkz_dict": all_filed_dict["sgxkz_dict"],
            "xfba_dict": all_filed_dict["xfba_dict"],
            "jgba_dict": all_filed_dict["jgba_dict"],
            # "dagzlgd_dict": all_filed_dict["dagzlgd_dict"],
            "xmlx_responsible_person": todo_handler,
            "jlfb_responsible_person": todo_handler,
            "sgxkz_responsible_person": todo_handler,
            "xfba_responsible_person": todo_handler,
            "jgba_responsible_person": todo_handler,
            "current_section": current_section,
            "current_work": current_work,
            # "dagzlgd_responsible_person": all_filed_dict["dagzlgd_dict"]["responsible_person"],
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def joint_analysis_mains_power_plan(self, project_name):
        """
        合建-施工准备-市电专项
        "2.9.1": "外电报装",
        "2.9.2": "外电方案",
        "2.9.3": "高可靠费缴费",
        "2.9.4": "供电合同签订",
        "2.9.5": "调度协议签订",
        "2.9.6": "送电前检测",
        "2.9.7": "送电",
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql_1 = ("SELECT serial_number,work_content,start_time,completion_time,project_progress,construction_attribute "
                 f"FROM construct_plan_data WHERE project_name='{project_name}' AND not_involved='0'")
        query_data = db.get_all(sql_1)
        current_section = ""
        current_work = ""
        # 对应字段
        all_filed_dict = {
            "wdbz_dict": {"work_list": []}
        }
        # 解析
        if query_data:
            for row in query_data:
                seq_no = row.get("serial_number", "")
                work_content = row.get('work_content', "")
                start_time = row.get('start_time', "")
                completion_time = row.get('completion_time', "")
                project_progress = row.get('project_progress', "")
                construction_attribute = row.get('construction_attribute', "")
                current_dict = {
                    "seq_no": seq_no,
                    "work_content": work_content,
                    "start_time": start_time,
                    "finish_time": completion_time,
                    "construction_time": project_progress,
                    "construction_attribute": construction_attribute
                }
                # 检查是否是2级标题
                if '.' not in seq_no or seq_no.count('.') == 1:
                    if work_content == "市电专项":
                        current_work = work_content
                        current_section = seq_no
                    continue
                # 获取三级标题内容
                if current_work == "市电专项":
                    self.workflow_detail.add_kv_table("市电工作项", {"workcontent": work_content})
                    if seq_no.startswith(current_section) and seq_no.count('.') == 2:
                        all_filed_dict["wdbz_dict"]["work_list"].append(current_dict)
        # 获取待办处理人（主要是下一个节点: 送电前检测、外电报装、外电方案）
        todo_handler = ConstructionApplicationInfoQuery.get_todo_handler(project_name, '合建方-项目经理')
        variables = {
            "wdbz_dict": all_filed_dict["wdbz_dict"],
            "wdbz_responsible_person": todo_handler,
            "current_work": current_work,
            "current_section": current_section,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
