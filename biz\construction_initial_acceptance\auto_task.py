from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, curl, config
import json
from iBroker.sdk.notification.chatops import ChatOpsSend

from biz.construction_final_acceptance.tools import Tools


class InitialAcceptanceAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_project_info(
        self,
        project_name,
    ):
        """
        获取项目信息
        """
        # 获取PM
        PM = Tools.get_PM(project_name)
        # 获取项目编号
        project_code = Tools.get_project_code(project_name)
        variables = {"PM": PM, "project_code": project_code}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_project_po_info(
        self,
        project_name,
    ):
        """
        获取项目全部PO信息
        """
        # 获取已验收的品类
        accept_category_list = []
        res_list = Tools.get_initial_acceptance_details(project_name)
        if res_list:
            for item in res_list:
                accept_category_list.append(item.get("category_name"))
        # 获取需求编号
        code_id_list = Tools.get_request_order_number(project_name)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        # 从数据库获取 品名-品类 映射
        category_mapping = Tools.get_category_mapping()
        self.workflow_detail.add_kv_table(
            "2.品名-品类 映射", {"message": category_mapping}
        )
        # 获取PO
        # po信息列表
        po_material_info = []
        # 处理后的po-品类信息列表（页面验收po列表展示信息）
        po_category_dict = {
            # 集成及配套工程
            "integrate_list": [],
            # 设备
            "device_list": [],
            # 服务
            "service_list": [],
            # 其它（缺少映射）
            "other_list": [],
        }
        # 物料id
        material_id_list = []
        # 品类-po集合
        category_name_po_details_dict = {}
        if code_id_list:
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"},
                )
                po_info = resp.json()
                # self.workflow_detail.add_kv_table("3.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"3.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        # 过滤po为空的数据
                        if data.get("orderCode") and data.get("orderItemId"):
                            id = data.get("orderItemId")
                            data["orderItemId"] = str(id)
                            data["code_id"] = code_id
                            po_info_list.append(data)
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}2.获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "4.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "5.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "6.物料信息", {"message": len(material_list)}
            )
            material_id_name_dict = {}
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    if material.get("materialQueryDTO"):
                        # 物料id
                        material_id = material.get("materialQueryDTO").get("materialId")
                        # 品名
                        material_category_name = material.get("materialQueryDTO").get(
                            "materialCategoryName"
                        )
                        material_id_name_dict[material_id] = {
                            # 原品名
                            "material_category_name": material_category_name,
                            # 映射后品名
                            "category_name": category_mapping.get(
                                material_category_name
                            ),
                        }
            # self.workflow_detail.add_kv_table(
            #     "7.物料id-品类", {"message": material_id_name_dict}
            # )
            for k in po_material_info:
                category_name_dict = material_id_name_dict.get(k.get("materialId"))
                material_category_name = ""
                category_name = ""
                if category_name_dict:
                    material_category_name = category_name_dict.get(
                        "material_category_name"
                    )
                    category_name = category_name_dict.get("category_name")
                    # 展示未验收的品类
                    if category_name not in accept_category_list:
                        po_details_list = [
                            {
                                "code_id": k.get("code_id"),
                                # "order_code": k.get("orderCode"),
                                # "material_id": k.get("materialId"),
                                # "material_category_name": material_category_name,
                                "category_name": category_name,
                                # "order_item_id": k.get("orderItemId"),
                                # "order_amount": k.get("orderAmount"),
                            }
                        ]
                        if category_name not in category_name_po_details_dict:
                            category_name_po_details_dict[category_name] = (
                                po_details_list
                            )
                        else:
                            category_name_po_details_dict[
                                category_name
                            ] += po_details_list
            # self.workflow_detail.add_kv_table(
            #     "8.处理后的品类-po详细集合", {"message": category_name_po_details_dict}
            # )
            # 从数据库获取 品类-分类 映射
            category_class_mapping = Tools.get_category_class_mapping()
            for category_name, po_details in category_name_po_details_dict.items():
                class_name = category_class_mapping.get(category_name, "other_list")
                # po_code_list = []
                code_id_list = []
                for details in po_details:
                    # if details.get("order_code") not in po_code_list:
                    #     po_code_list.append(details.get("order_code"))
                    if details.get("code_id") not in code_id_list:
                        code_id_list.append(details.get("code_id"))
                po_category_dict[class_name].append(
                    {
                        "category_name": category_name,
                        "code_id": ";".join(code_id_list),
                        # "po_code": ";".join(po_code_list),
                        # 默认全勾选
                        "selected": ["是"],
                        # "po_details": po_details,
                    }
                )
            self.workflow_detail.add_kv_table(
                "9.处理后的品类-po列表", {"message": po_category_dict}
            )
        variables = {
            "code_id_list": code_id_list,
            "category_mapping": category_mapping,
            "po_material_info": len(po_material_info),
            "po_category_dict": po_category_dict,
            # "category_name_po_details_dict": category_name_po_details_dict,
            # 一键操作
            "selected_switch": True,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_project_po_info_test(self, project_name, po_info):
        """
        获取项目全部PO信息
        测试环境 模拟数据处理
        """
        # 获取已验收的品类
        accept_category_list = []
        res_list = Tools.get_initial_acceptance_details(project_name)
        if res_list:
            for item in res_list:
                accept_category_list.append(item.get("category_name"))
        # 获取需求编号
        code_id_list = Tools.get_request_order_number(project_name)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        # 从数据库获取 品名-品类 映射
        category_mapping = Tools.get_category_mapping()
        self.workflow_detail.add_kv_table(
            "2.品名-品类 映射", {"message": category_mapping}
        )
        # 获取PO
        # po信息列表
        po_material_info = []
        # 处理后的po-品类信息列表（页面验收po列表展示信息）
        po_category_dict = {
            # 集成及配套工程
            "integrate_list": [],
            # 设备
            "device_list": [],
            # 服务
            "service_list": [],
            # 其它（缺少映射）
            "other_list": [],
        }
        # 物料id
        material_id_list = []
        # 品类-po集合
        category_name_po_details_dict = {}
        if code_id_list:
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                # resp = curl.get(
                #     title="Dcops获取po信息",
                #     system_name="Dcops",
                #     url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                #     postdata="",
                #     append_header={"Content-Type": "application/json"},
                # )
                # po_info = resp.json()
                po_info = self.ctx.variables.get("po_info")
                # self.workflow_detail.add_kv_table("3.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"3.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        # 过滤po为空的数据
                        if data.get("orderCode") and data.get("orderItemId"):
                            id = data.get("orderItemId")
                            data["orderItemId"] = str(id)
                            data["code_id"] = code_id
                            po_info_list.append(data)
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}2.获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "4.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "5.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "6.物料信息", {"message": len(material_list)}
            )
            material_id_name_dict = {}
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    if material.get("materialQueryDTO"):
                        # 物料id
                        material_id = material.get("materialQueryDTO").get("materialId")
                        # 品名
                        material_category_name = material.get("materialQueryDTO").get(
                            "materialCategoryName"
                        )
                        material_id_name_dict[material_id] = {
                            # 原品名
                            "material_category_name": material_category_name,
                            # 映射后品名
                            "category_name": category_mapping.get(
                                material_category_name
                            ),
                        }
            # self.workflow_detail.add_kv_table(
            #     "7.物料id-品类", {"message": material_id_name_dict}
            # )
            for k in po_material_info:
                category_name_dict = material_id_name_dict.get(k.get("materialId"))
                material_category_name = ""
                category_name = ""
                if category_name_dict:
                    material_category_name = category_name_dict.get(
                        "material_category_name"
                    )
                    category_name = category_name_dict.get("category_name")
                    # 展示未验收的品类
                    if category_name not in accept_category_list:
                        po_details_list = [
                            {
                                "code_id": k.get("code_id"),
                                # "order_code": k.get("orderCode"),
                                # "material_id": k.get("materialId"),
                                # "material_category_name": material_category_name,
                                "category_name": category_name,
                                # "order_item_id": k.get("orderItemId"),
                                # "order_amount": k.get("orderAmount"),
                            }
                        ]
                        if category_name not in category_name_po_details_dict:
                            category_name_po_details_dict[category_name] = (
                                po_details_list
                            )
                        else:
                            category_name_po_details_dict[
                                category_name
                            ] += po_details_list
            # self.workflow_detail.add_kv_table(
            #     "8.处理后的品类-po详细集合", {"message": category_name_po_details_dict}
            # )
            # 从数据库获取 品类-分类 映射
            category_class_mapping = Tools.get_category_class_mapping()
            for category_name, po_details in category_name_po_details_dict.items():
                class_name = category_class_mapping.get(category_name, "other_list")
                # po_code_list = []
                code_id_list = []
                for details in po_details:
                    # if details.get("order_code") not in po_code_list:
                    #     po_code_list.append(details.get("order_code"))
                    if details.get("code_id") not in code_id_list:
                        code_id_list.append(details.get("code_id"))
                po_category_dict[class_name].append(
                    {
                        "category_name": category_name,
                        "code_id": ";".join(code_id_list),
                        # "po_code": ";".join(po_code_list),
                        # 默认全勾选
                        "selected": ["是"],
                        # "po_details": po_details,
                    }
                )
            self.workflow_detail.add_kv_table(
                "9.处理后的品类-po列表", {"message": po_category_dict}
            )
        variables = {
            "code_id_list": code_id_list,
            "category_mapping": category_mapping,
            "po_material_info": len(po_material_info),
            "po_category_dict": po_category_dict,
            # "category_name_po_details_dict": category_name_po_details_dict,
            # 一键操作
            "selected_switch": True,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_feedback_new(
        self, project_name, po_category_dict, appendix_list, real_time, deal_users
    ):
        """
        初验信息回传给星辰(支持部分验收)
        """
        po_category_all_selected_list = []
        all_category_name_list = []
        # 获取确认验收的品类
        for key, data in po_category_dict.items():
            for item in data:
                if item.get("selected"):
                    if item.get("selected")[0] == "是":
                        po_category_all_selected_list.append(item)
                        all_category_name_list.append(item.get("category_name"))
        self.workflow_detail.add_kv_table(
            "1.验收品类信息", {"message": po_category_all_selected_list}
        )
        # 再次调接口查po信息
        # 获取需求编号
        code_id_list = Tools.get_request_order_number(project_name)
        self.workflow_detail.add_kv_table("2.获取需求id", {"message": code_id_list})
        # 从数据库获取 品名-品类 映射
        category_mapping = Tools.get_category_mapping()
        self.workflow_detail.add_kv_table(
            "3.品名-品类 映射", {"message": category_mapping}
        )
        # 获取PO
        # po信息列表
        po_material_info = []
        # 物料id
        material_id_list = []
        # 回传数据
        data_list = []
        # 回传po数据
        data_po_list = []
        if code_id_list:
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"},
                )
                po_info = resp.json()
                # self.workflow_detail.add_kv_table("3.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"4.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        # 过滤po为空的数据
                        if data.get("orderCode") and data.get("orderItemId"):
                            id = data.get("orderItemId")
                            data["orderItemId"] = str(id)
                            data["code_id"] = code_id
                            po_info_list.append(data)
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"5.{code_id}获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "6.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "7.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "8.物料信息", {"message": len(material_list)}
            )
            material_id_name_dict = {}
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    if material.get("materialQueryDTO"):
                        # 物料id
                        material_id = material.get("materialQueryDTO").get("materialId")
                        # 品名
                        material_category_name = material.get("materialQueryDTO").get(
                            "materialCategoryName"
                        )
                        material_id_name_dict[material_id] = {
                            # 原品名
                            "material_category_name": material_category_name,
                            # 映射后品名
                            "category_name": category_mapping.get(
                                material_category_name
                            ),
                        }
            # self.workflow_detail.add_kv_table(
            #     "7.物料id-品类", {"message": material_id_name_dict}
            # )
            for k in po_material_info:
                category_name_dict = material_id_name_dict.get(k.get("materialId"))
                material_category_name = ""
                category_name = ""
                if category_name_dict:
                    material_category_name = category_name_dict.get(
                        "material_category_name"
                    )
                    category_name = category_name_dict.get("category_name")
                    # 过滤确认验收的品类po
                    if category_name in all_category_name_list:
                        data_po_list.append(k.get("orderCode"))
                        data_list.append(
                            {
                                "AssetCode": "",
                                "LineDetailId": str(k.get("orderItemId")),
                                "QuantityReceived": k.get("orderAmount"),
                                "SnCode": "",
                                "AttachmentList": appendix_list,
                            }
                        )
        self.workflow_detail.add_kv_table("9.传参信息", {"message": data_list})
        # 获取企微id
        chat_info = gnetops.request(
            action="Tof",
            method="GetIDCStaffIDByEngName",
            ext_data={
                "EngName": str(deal_users),
            },
        )
        self.workflow_detail.add_kv_table("10.获取企微id", {"message": str(chat_info)})
        chat_id = ""
        if chat_info != 0:
            chat_id = str(chat_info)
        else:
            self.workflow_detail.add_kv_table(
                "10.获取企微id",
                {"success": False, "data": f"获取失败:{str(chat_info)}"},
            )
        # 分批次调用回传接口
        chunk_size = 500
        sub_lists = [
            data_list[i : i + chunk_size] for i in range(0, len(data_list), chunk_size)
        ]
        info_list = []
        info_failed_list = []
        for i in range(len(sub_lists)):
            info = gnetops.request(
                action="Tbconstruct",
                method="FeedbackAcceptanceStatusToStar",
                ext_data={
                    "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
                    "ReceiveDate": str(real_time),  # 时间
                    "ReceiveUserId": chat_id,  # id
                    "Orderdetails": sub_lists[i],
                },
            )
            self.workflow_detail.add_kv_table(
                f"11.第{i}次回传数据接口返回数据", {"message": info}
            )
            info_list.append(info)
            # 判断是否验收成功
            if info.get("status") != "success":
                info_failed_list.append(info)
        # 推送消息
        acceptance_result_receivers = config.get_config_map(
            "acceptance_result_receivers"
        )
        receivers = [deal_users, *acceptance_result_receivers]
        title = f"{project_name}项目初验状态确认"
        if info_failed_list:
            message = f"{project_name}项目初验验收失败"
            for info in info_failed_list:
                message += f"\n{info.get('message')}"
        else:
            message = f"{project_name}项目初验验收成功"
        # 应用消息
        tof.send_company_wechat_message(
            receivers=receivers, title=title, message=message
        )
        # dcops消息
        for account in receivers:
            ChatOpsSend(
                user_name=account,
                msg_content=message,
                msg_type="text",
                des_type="single",
            ).call()
        variables = {
            "po_category_all_selected_list": po_category_all_selected_list,
            "code_id_list2": code_id_list,
            "category_mapping2": category_mapping,
            "po_material_info2": len(po_material_info),
            "data_list": data_list,
            "data_po_list": data_po_list,
            "chat_id": chat_id,
            "info_list": info_list,
            "info_failed_list": info_failed_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_feedback_new_test(
        self, project_name, po_category_dict, appendix_list, real_time, deal_users
    ):
        """
        初验信息回传给星辰(支持部分验收)
        """
        po_category_all_selected_list = []
        all_category_name_list = []
        # 获取确认验收的品类
        for key, data in po_category_dict.items():
            for item in data:
                if item.get("selected"):
                    if item.get("selected")[0] == "是":
                        po_category_all_selected_list.append(item)
                        all_category_name_list.append(item.get("category_name"))
        self.workflow_detail.add_kv_table(
            "1.验收品类信息", {"message": po_category_all_selected_list}
        )
        # 再次调接口查po信息
        # 获取需求编号
        code_id_list = Tools.get_request_order_number(project_name)
        self.workflow_detail.add_kv_table("2.获取需求id", {"message": code_id_list})
        # 从数据库获取 品名-品类 映射
        category_mapping = Tools.get_category_mapping()
        self.workflow_detail.add_kv_table(
            "3.品名-品类 映射", {"message": category_mapping}
        )
        # 获取PO
        # po信息列表
        po_material_info = []
        # 物料id
        material_id_list = []
        # 回传数据
        data_list = []
        # 回传po数据
        data_po_list = []
        if code_id_list:
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                # resp = curl.get(
                #     title="Dcops获取po信息",
                #     system_name="Dcops",
                #     url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                #     postdata="",
                #     append_header={"Content-Type": "application/json"},
                # )
                # po_info = resp.json()
                po_info = self.ctx.variables.get("po_info")
                # self.workflow_detail.add_kv_table("3.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"4.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        # 过滤po为空的数据
                        if data.get("orderCode") and data.get("orderItemId"):
                            id = data.get("orderItemId")
                            data["orderItemId"] = str(id)
                            data["code_id"] = code_id
                            po_info_list.append(data)
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
                else:
                    self.workflow_detail.add_kv_table(
                        f"5.{code_id}获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "6.最终po信息", {"message": len(po_material_info)}
            )
            self.workflow_detail.add_kv_table(
                "7.获取物料id", {"message": len(material_id_list)}
            )
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            self.workflow_detail.add_kv_table(
                "8.物料信息", {"message": len(material_list)}
            )
            material_id_name_dict = {}
            if material_list:
                material_info_list = material_list.get("materialList")
                # 构造品名、物料编码、物料id
                data = []
                for material in material_info_list:
                    if material.get("materialQueryDTO"):
                        # 物料id
                        material_id = material.get("materialQueryDTO").get("materialId")
                        # 品名
                        material_category_name = material.get("materialQueryDTO").get(
                            "materialCategoryName"
                        )
                        material_id_name_dict[material_id] = {
                            # 原品名
                            "material_category_name": material_category_name,
                            # 映射后品名
                            "category_name": category_mapping.get(
                                material_category_name
                            ),
                        }
            # self.workflow_detail.add_kv_table(
            #     "7.物料id-品类", {"message": material_id_name_dict}
            # )
            for k in po_material_info:
                category_name_dict = material_id_name_dict.get(k.get("materialId"))
                material_category_name = ""
                category_name = ""
                if category_name_dict:
                    material_category_name = category_name_dict.get(
                        "material_category_name"
                    )
                    category_name = category_name_dict.get("category_name")
                    # 过滤确认验收的品类po
                    if category_name in all_category_name_list:
                        data_po_list.append(k.get("orderCode"))
                        data_list.append(
                            {
                                "AssetCode": "",
                                "LineDetailId": str(k.get("orderItemId")),
                                "QuantityReceived": k.get("orderAmount"),
                                "SnCode": "",
                                "AttachmentList": appendix_list,
                            }
                        )
        self.workflow_detail.add_kv_table("9.传参信息", {"message": data_list})
        # 获取企微id
        chat_info = gnetops.request(
            action="Tof",
            method="GetIDCStaffIDByEngName",
            ext_data={
                "EngName": str(deal_users),
            },
        )
        self.workflow_detail.add_kv_table("10.获取企微id", {"message": str(chat_info)})
        chat_id = ""
        if chat_info != 0:
            chat_id = str(chat_info)
        else:
            self.workflow_detail.add_kv_table(
                "10.获取企微id",
                {"success": False, "data": f"获取失败:{str(chat_info)}"},
            )
        # 分批次调用回传接口
        chunk_size = 500
        sub_lists = [
            data_list[i : i + chunk_size] for i in range(0, len(data_list), chunk_size)
        ]
        info_list = []
        info_failed_list = []
        for i in range(len(sub_lists)):
            # info = gnetops.request(
            #     action="Tbconstruct",
            #     method="FeedbackAcceptanceStatusToStar",
            #     ext_data={
            #         "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
            #         "ReceiveDate": str(real_time),  # 时间
            #         "ReceiveUserId": chat_id,  # id
            #         "Orderdetails": sub_lists[i],
            #     },
            # )
            # 回传数据
            feedback_info = {
                "Action": "Tbconstruct",
                "Method": "FeedbackAcceptanceStatusToStar",
                "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
                "ReceiveDate": str(real_time),  # 时间
                "ReceiveUserId": chat_id,  # id
                "Orderdetails": sub_lists[i],
                "SystemId": "2",
            }
            self.workflow_detail.add_kv_table(
                f"11.第{i}次回传数据接口信息", {"message": feedback_info}
            )
            response_info = self.ctx.variables.get("response_info")
            info = response_info
            self.workflow_detail.add_kv_table(
                f"11.第{i}次回传数据接口返回数据", {"message": info}
            )
            info_list.append(info)
            # 判断是否验收成功
            if info.get("status") != "success":
                info_failed_list.append(info)
        # 推送消息
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        deal_users = flow_responsible_person
        receivers = [deal_users]
        title = f"{project_name}项目初验状态确认"
        if info_failed_list:
            message = f"{project_name}项目初验验收失败"
            for info in info_failed_list:
                message += f"\n{info.get('message')}"
        else:
            message = f"{project_name}项目初验验收成功"
        # 应用消息
        tof.send_company_wechat_message(
            receivers=receivers, title=title, message=message
        )
        # dcops消息
        for account in receivers:
            ChatOpsSend(
                user_name=account,
                msg_content=message,
                msg_type="text",
                des_type="single",
            ).call()
        variables = {
            "po_category_all_selected_list": po_category_all_selected_list,
            "code_id_list2": code_id_list,
            "category_mapping2": category_mapping,
            "po_material_info2": len(po_material_info),
            "data_list": data_list,
            "data_po_list": data_po_list,
            "chat_id": chat_id,
            "info_list": info_list,
            "info_failed_list": info_failed_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_feedback(
        self, project_name, appendix_list, real_time, deal_users
    ):
        """
        初验信息回传给星辰
        """
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"},
                )
                po_info = resp.json()
                # self.workflow_detail.add_kv_table("2.获取po", {"message": po_info})
                if po_info:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}获取po", {"message": len(po_info.get("data"))}
                    )
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
                else:
                    self.workflow_detail.add_kv_table(
                        f"2.{code_id}2.获取po", {"message": "未获取到po"}
                    )
            self.workflow_detail.add_kv_table(
                "3.最终po信息", {"message": len(po_material_info)}
            )
            for po in po_material_info:
                if po.get("orderCode"):
                    data_list.append(
                        {
                            "AssetCode": "",
                            "LineDetailId": str(po.get("orderItemId")),
                            "QuantityReceived": po.get("orderAmount"),
                            "SnCode": "",
                            "AttachmentList": appendix_list,
                        }
                    )
            self.workflow_detail.add_kv_table("4.传参信息", {"message": len(data_list)})
            # 获取企微id
            chat_info = gnetops.request(
                action="Tof",
                method="GetIDCStaffIDByEngName",
                ext_data={
                    "EngName": str(deal_users),
                },
            )
            self.workflow_detail.add_kv_table(
                "5.获取企微id", {"message": str(chat_info)}
            )
            chat_id = ""
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table(
                    "5.获取企微id",
                    {"success": False, "data": f"获取失败:{str(chat_info)}"},
                )
            # 分批次调用回传接口
            chunk_size = 500
            sub_lists = [
                data_list[i : i + chunk_size]
                for i in range(0, len(data_list), chunk_size)
            ]
            info_list = []
            for i in range(len(sub_lists)):
                info = gnetops.request(
                    action="Tbconstruct",
                    method="FeedbackAcceptanceStatusToStar",
                    ext_data={
                        "FeedbackType": 3,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
                        "ReceiveDate": str(real_time),  # 时间
                        "ReceiveUserId": chat_id,  # id
                        "Orderdetails": sub_lists[i],
                    },
                )
                self.workflow_detail.add_kv_table(
                    f"6.第{i}次回传数据接口返回数据", {"message": info}
                )
                info_list.append(info)
            variables = {
                "po_material_info": len(po_material_info),
                "data_list": data_list,
                "chat_id": chat_id,
                # "info": str(info),
                "info_list": info_list,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table(
                "2.获取po信息", {"message": "获取不到需求单号"}
            )
            return {"code": -1, "message": "获取不到需求单号"}

    def initial_acceptance_feedback_test(
        self, project_name, appendix_list, real_time, deal_users, po_info
    ):
        """
        初验信息回传给星辰
        测试环境 模拟数据处理
        """
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                # resp = curl.get(
                #     title="Dcops获取po信息",
                #     system_name="Dcops",
                #     url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                #     postdata="",
                #     append_header={"Content-Type": "application/json"},
                # )
                # po_info = resp.json()
                po_info = self.ctx.variables.get("po_info")
                self.workflow_detail.add_kv_table("2.获取po", {"message": po_info})
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list

            self.workflow_detail.add_kv_table(
                "3.最终po信息", {"message": po_material_info}
            )
            for po in po_material_info:
                data_list.append(
                    {
                        "AssetCode": "",
                        "LineDetailId": str(po.get("orderItemId")),
                        "QuantityReceived": po.get("orderAmount"),
                        "SnCode": "",
                        "AttachmentList": appendix_list,
                    }
                )
            self.workflow_detail.add_kv_table("4.传参信息", {"message": data_list})
            # 获取企微id
            chat_info = gnetops.request(
                action="Tof",
                method="GetIDCStaffIDByEngName",
                ext_data={
                    "EngName": str(deal_users),
                },
            )
            self.workflow_detail.add_kv_table(
                "5.获取企微id", {"message": str(chat_info)}
            )
            chat_id = ""
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table(
                    "5.获取企微id",
                    {"success": False, "data": f"获取失败:{str(chat_info)}"},
                )
            # info = gnetops.request(
            #     action="Tbconstruct",
            #     method="FeedbackAcceptanceStatusToStar",
            #     ext_data={
            #         "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
            #         "ReceiveDate": str(real_time),  # 时间
            #         "ReceiveUserId": chat_id,  # id
            #         "Orderdetails": data_list,
            #     },
            # )
            # 回传数据
            feedback_info = {
                "Action": "Tbconstruct",
                "Method": "FeedbackAcceptanceStatusToStar",
                "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终验,人员入场=4,图纸已送审=5
                "ReceiveDate": str(real_time),  # 时间
                "ReceiveUserId": chat_id,  # id
                "Orderdetails": data_list,
                "SystemId": "2",
            }
            self.workflow_detail.add_kv_table(
                "6.回传数据接口信息", {"message": feedback_info}
            )
            variables = {
                "po_material_info": po_material_info,
                "data_list": data_list,
                # "info": str(info),
                "feedback_info": feedback_info,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table(
                "2.获取po信息", {"message": "获取不到需求单号"}
            )
            return {"code": -1, "message": "获取不到需求单号"}

    def initial_acceptance_feedback_devicedb(
        self, project_name, po_category_dict, info_dict
    ):
        """
        初验建单-devicedb
        """
        # 获取模组名称
        mozu = Tools.get_module_name(project_name)
        if mozu:
            # 流程输入参数构造
            category_date = {}
            # 获取确认验收的品类
            for key, data in po_category_dict.items():
                for item in data:
                    if item.get("selected"):
                        if item.get("selected")[0] == "是":
                            if item.get("category_name") not in category_date:
                                category_date[item.get("category_name")] = (
                                    info_dict.get("actual_finish_time")
                                )
            processTitle = f"{project_name}建设更新设备验收状态（devicedb） "
            # 流程负责人
            flow_responsible_person = config.get_config_string(
                "flow_responsible_person"
            )
            # 建单参数
            data = {
                "CustomVariables": {
                    "Mozu": mozu,
                    "AcceptType": "初验",
                    "PartyAInfo": category_date,
                    "PartyBDate": info_dict.get("actual_finish_time"),
                },
                "ProcessDefinitionKey": "UpdateDeviceAcceptStatus",
                "Source": "",
                "TicketDescription": processTitle,
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": flow_responsible_person,  # 关注人 可填多个
                    "Creator": "dcops-auto",  # 建单人 只能填一个
                    "Deal": flow_responsible_person,  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
                },
            }
            self.workflow_detail.add_kv_table(
                "1.建设更新设备验收状态（devicedb）流程起单输入", {"message": data}
            )
            # 输出
            res = gnetops.request(action="Ticket", method="Create", data=data)
            self.workflow_detail.add_kv_table(
                "2.建设更新设备验收状态（devicedb）流程起单输入输出", {"message": res}
            )
            # 起单工单号
            devicedb_ticket_id = str(res.get("TicketId", ""))
            variables = {
                "devicedb_create_ticket_data": data,
                "devicedb_ticket_id": devicedb_ticket_id,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            variables = {
                "devicedb_create_ticket_data": "未查到模组信息，跳过devicedb建单"
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_email_cfg(self, project_name, info_dict):
        """
        初验推送邮件模板配置
        """
        # 获取模组名称
        mozu = Tools.get_module_name(project_name)
        email_title = f"【TB初验】{project_name}初验完成"
        email_content = f"{project_name}"
        if mozu:
            email_content += f"（模组：{mozu}）"
        email_content += (
            f"已于{info_dict.get('actual_finish_time')}初验完成，感谢支持！"
        )
        email_receiver_list = Tools.get_email_receiver_list(project_name)
        initial_acceptance_email_cfg = config.get_config_map(
            "initial_acceptance_email_cfg"
        )
        # 加上配置的账号
        email_receiver_list = list(
            set([*email_receiver_list, *initial_acceptance_email_cfg])
        )
        email_receiver = ";".join(email_receiver_list)
        variables = {
            "email_title": email_title,
            "email_receiver": email_receiver,
            "email_Cc": "",
            "email_content": email_content,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
