import datetime
import json
import time

from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate
from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops

from biz.common.cycle_task_tools import ticket_is_over
from biz.common.workflow_tools import cycle_task_abort


class TechnicalDocumentUpdate(AjaxTodoBase):
    """
            技术文件更新
        """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        table = process_data.get('table')
        choose_list = process_data.get("choose_list", [])
        update_list = self.ctx.variables.get("update_list")
        # update_list_name = [i.get("file_name") for i in update_list]
        # choose_list_name = [i.get("file_name") for i in choose_list]
        # # 转换为集合
        # update_set = set(update_list_name)
        # choose_set = set(choose_list_name)
        # # 使用差集判断
        # missing_files = update_set - choose_set
        # if missing_files:
        #     return {"code": -1, "msg": "有驳回文件未重新上传审核！！"}
        update_list_len = len(update_list)
        choose_list_len = len(choose_list)
        if choose_list_len < update_list_len:
            return {"code": -1, "msg": "有驳回文件未重新上传审核！！"}
        # leader审核人
        leader_list = []
        # 构建数据格式：{审批人:文件表格}
        leader_approval = {}

        if choose_list:
            for i in choose_list:
                approval = i.get('approval')
                if approval not in leader_list:
                    leader_list.append(approval)
                    leader_approval[approval] = [i]
                else:
                    leader_approval[approval].append(i)

        variables = {
            'table': table,
            'leader_approval': leader_approval,
            'leader_list': leader_list,
            'audit_results': [],
            'choose_list': choose_list,
            'leader_cycle_order_list': [],
            'director_cycle_order_list': []
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TechnicalDocumentSubmission(AjaxTodoBase):
    """
        技术文件上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name')
        project_desc = process_data.get('project_desc')
        table = process_data.get('table')
        # leader审核人
        leader_list = []
        # 构建数据格式：{审批人:文件表格}
        leader_approval = {}

        if table:
            for i in table:
                approval = i.get('approval')
                director = i.get('director')
                if approval not in leader_list:
                    leader_list.append(approval)
                    leader_approval[approval] = [i]
                else:
                    leader_approval[approval].append(i)

        variables = {
            'table': table,
            'leader_approval': leader_approval,
            'leader_list': leader_list,
            'audit_results': [],
            'project_name': project_name,
            'project_desc': project_desc,
            'leader_cycle_order_list': [],
            'director_cycle_order_list': []
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TechnicalDocumentAutomaticTasks(object):
    """
        技术小组组建：自动任务
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def cycle_order_creation(self, leader_approval, project_name, project_desc, process_key):
        """
            leader审核
        """
        res_list = []
        leader_cycle_order_list = []
        ticket_id = self.variables.get("ticket_id")
        if leader_approval:
            for key, value in leader_approval.items():
                custom_variables = {
                    'approval': key,
                    'table_branches': value,
                    'project_name': project_name,
                    'project_desc': project_desc,
                    'f_instance_id': self.ctx.instance_id
                }
                data = {
                    "CustomVariables": custom_variables,
                    "ProcessDefinitionKey": process_key,
                    "Source": "",
                    "TicketDescription": f"{project_name}:技术文件审批",
                    "TicketLevel": "3",
                    "TicketTitle": f"{project_name}:技术文件审批",
                    "UserInfo": {
                        "Concern": "youngshi",
                        "Creator": "youngshi",
                        "Deal": key
                    }
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                leader_cycle_order_list.append(res.get('TicketId'))

            # 门户页面库数据添加
            db = mysql.new_mysql_instance("tbconstruct")
            technical_documentation_preparation = json.dumps(
                {"ticket_id": [ticket_id], "children_ticket_id": leader_cycle_order_list})
            conditions = {"project_name": project_name}
            update_data = {"technical_documentation_preparation": technical_documentation_preparation}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()

            variables = {
                "leader_cycle_order_list": leader_cycle_order_list,
                "leader_res_list": res_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('文件信息',
                                              {"success": False, "data": "未获取文件信息"})

    def wait_document_approval(self):
        """
            等待leader审核完成
        """
        audit_results = self.ctx.variables.get('audit_results')
        leader_cycle_order_list = self.ctx.variables.get('leader_cycle_order_list')
        flag = ticket_is_over(leader_cycle_order_list)
        if flag:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            if len(leader_cycle_order_list) == len(audit_results):
                if "驳回" in audit_results:
                    results = "驳回"
                else:
                    results = "同意"
                variables = {
                    "results": results
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "审核结果数据错误！"}

    def data_processing(self, choose_list, table):
        """
            数据处理
        """
        # 总监审核
        director_list = []
        director_approval = {}
        if table:
            for i in table:
                director = i.get('director')
                if director not in director_list:
                    director_list.append(director)
                    director_approval[director] = [i]
                else:
                    director_approval[director].append(i)
        else:
            return {"code":-1, 'msg':'未获取文件信息,请联系开发人员确认'}
        variables = {
            'update_list': [],
            'director_list': director_list,
            'director_approval': director_approval,
            'director_audit_results': []
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def director_cycle_order_creation(self, director_approval, project_name, project_desc, process_key):
        """
            总监审核
        """
        res_list = []
        director_cycle_order_list = []
        ticket_id = self.variables.get("ticket_id")
        leader_cycle_order_list = self.ctx.variables.get('leader_cycle_order_list')
        if director_approval:
            for key, value in director_approval.items():
                custom_variables = {
                    'approval': key,
                    'table_branches': value,
                    'project_name': project_name,
                    'project_desc': project_desc,
                    'f_instance_id': self.ctx.instance_id
                }
                data = {
                    "CustomVariables": custom_variables,
                    "ProcessDefinitionKey": process_key,
                    "Source": "",
                    "TicketDescription": f"{project_name}:技术文件审批",
                    "TicketLevel": "3",
                    "TicketTitle": f"{project_name}:技术文件审批",
                    "UserInfo": {
                        "Concern": "youngshi",
                        "Creator": "youngshi",
                        "Deal": key
                    }
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                director_cycle_order_list.append(res.get('TicketId'))
            # 门户页面库数据添加
            db = mysql.new_mysql_instance("tbconstruct")
            leader_cycle_order_list = leader_cycle_order_list + director_cycle_order_list
            technical_documentation_preparation = json.dumps(
                {"ticket_id": [ticket_id], "children_ticket_id": leader_cycle_order_list})
            conditions = {"project_name": project_name}
            update_data = {"technical_documentation_preparation": technical_documentation_preparation}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()

            variables = {
                "director_cycle_order_list": director_cycle_order_list,
                "director_res_list": res_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('文件信息',
                                              {"success": False, "data": "未获取文件信息"})

    def wait_document_director_approval(self):
        """
            等待总监审核完成
        """
        director_audit_results = self.ctx.variables.get('director_audit_results')
        director_cycle_order_list = self.ctx.variables.get('director_cycle_order_list')
        flag = ticket_is_over(director_cycle_order_list)
        if flag:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            if len(director_cycle_order_list) == len(director_audit_results):
                if "驳回" in director_audit_results:
                    director_results = "驳回"
                else:
                    director_results = "同意"
                variables = {
                    "director_results": director_results
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}
            else:
                return {"success": False, "data": "审核结果数据错误！"}


class TechnicalDocumentApproval(AjaxTodoBase):
    """
        技术文件审核:leader
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        table_branches = process_data.get('table_branches')
        f_instance_id = self.ctx.variables.get("f_instance_id")
        # 从主流程获取数据
        main_flow_vars = flow.get_variables(f_instance_id,
                                            ["table", "audit_results", "update_list"])
        self.workflow_detail.add_kv_table('主流程数据', {'variables_map': main_flow_vars})
        table = main_flow_vars.get("table", [])
        audit_results = main_flow_vars.get("audit_results", [])
        update_list = main_flow_vars.get("update_list", [])
        results_info = [b.get('results') for b in table_branches]
        if "驳回" in results_info:
            audit_results.append("驳回")
        else:
            audit_results.append("同意")
        for k in table_branches:
            results = k.get('results')
            if results == '驳回':
                update_list.append(k)
                if not k.get('reason'):
                    return {"code": -1, "msg": "请填写驳回原因"}
        for i in table:
            for j in table_branches:
                if i.get('file_name') == j.get('file_name'):
                    i['results'] = j.get('results')
                    i['reason'] = j.get('reason')

        f_variables = {
            'table': table,
            'audit_results': audit_results,
            'update_list': update_list
        }
        # 将流程变量更新至父流程
        WorkflowVarUpdate(instance_id=f_instance_id, variables=f_variables).call()
        variables = {
            'table_branches': table_branches,
            'table': table,
            'audit_results': audit_results,
            'update_list': update_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TechnicalDocumentDirectorApproval(AjaxTodoBase):
    """
        技术文件审核:总监
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        table_branches = process_data.get('table_branches')
        f_instance_id = self.ctx.variables.get("f_instance_id")
        # 从主流程获取数据
        main_flow_vars = flow.get_variables(f_instance_id,
                                            ["table", "director_audit_results", "update_list"])
        self.workflow_detail.add_kv_table('主流程数据', {'variables_map': main_flow_vars})
        table = main_flow_vars.get("table", [])
        director_audit_results = main_flow_vars.get("director_audit_results", [])
        update_list = main_flow_vars.get("update_list", [])

        results_info = [b.get('director_results') for b in table_branches]
        if "驳回" in results_info:
            director_audit_results.append("驳回")
        else:
            director_audit_results.append("同意")
        for k in table_branches:
            if k.get('director_results') == '驳回':
                update_list.append(k)
                if not k.get('director_reason'):
                    return {"code": -1, "msg": "请填写驳回原因"}
        for i in table:
            for j in table_branches:
                if i.get('file_name') == j.get('file_name'):
                    i['director_results'] = j.get('director_results')
                    i['director_reason'] = j.get('director_reason')

        f_variables = {
            'table': table,
            'director_audit_results': director_audit_results,
            'update_list': update_list
        }
        # 将流程变量更新至父流程
        WorkflowVarUpdate(instance_id=f_instance_id, variables=f_variables).call()
        variables = {
            'table_branches': table_branches,
            'table': table,
            'director_audit_results': director_audit_results,
            'update_list': update_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TechnicalDocumentUpdateSelect(AjaxTodoBase):
    """
        技术文件:工单结束前选择是否更新
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        update_select = process_data.get('update_select')
        variables = {
            'update_select': update_select,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
