# iBroker-construct 本地环境配置指南

## 📋 环境概述

本项目是基于 **tRPC Python框架** 的企业级建设管理系统，需要配置以下环境：

- **Python 3.x** 环境
- **MySQL数据库** (tbconstruct)
- **tRPC框架** 配置
- **iBroker SDK** 依赖
- **配置中心** 连接

## 🛠️ 环境配置步骤

### 1. Python环境安装

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source venv/bin/activate
```

### 2. 依赖包安装

```bash
# 安装项目依赖
pip install -r requirements.txt

# 核心依赖包说明:
# - iBroker==0.3.0a73  # 核心SDK
# - trpc==0.7.0        # tRPC框架
# - PyMySQL==0.10.1    # MySQL连接器
# - pandas==1.1.5      # 数据处理
# - openpyxl==3.0.5    # Excel处理
```

### 3. 数据库配置

#### 3.1 MySQL数据库连接
项目使用 `mysql.new_mysql_instance("tbconstruct")` 连接数据库

**主要数据表**:
- `all_construction_ticket_data` - 工单数据表
- `delivery_schedule_management_table` - 交付管理表
- `equipment_production_racking_process` - 设备生产流程表
- `risk_early_warning_data` - 风险预警数据表
- `summary_data` - 汇总数据表

#### 3.2 数据库配置方式
数据库连接通过 **iBroker配置中心** 管理，需要配置：
```
数据库实例名: tbconstruct
连接方式: iBroker.lib.mysql
```

### 4. tRPC框架配置

#### 4.1 服务配置 (trpc_python.yaml)
```yaml
global:
  namespace: Development
  env_name: test

server:
  app: iBroker
  server: construct
  service:
    - name: trpc.iBroker.construct.Proxy
      ip: 0.0.0.0
      port: 8000
      protocol: trpc
    - name: trpc.iBroker.construct.Proxy.http
      ip: 0.0.0.0
      port: 80
      protocol: http
```

#### 4.2 业务配置 (configs/com-config.conf)
```conf
SystemEnName=ibroker-construct
SystemCnName=iBrokerConstruct
BizGroup=construct
IsNewSDKVersion=1
```

### 5. iBroker SDK配置

#### 5.1 配置中心连接
项目依赖iBroker配置中心获取：
- 数据库连接信息
- COS存储配置
- 业务配置参数

#### 5.2 环境变量设置
```bash
# 开发环境标识
export PythonDeployFlag=dev

# 或在Windows中
set PythonDeployFlag=dev
```

### 6. 启动服务

#### 6.1 开发模式启动
```bash
# 直接运行Python服务
python -m trpc.main

# 或使用Docker启动
docker build -t ibroker-construct .
docker run -p 8000:8000 -p 80:80 ibroker-construct
```

#### 6.2 生产模式启动
```bash
# 使用supervisord管理进程
supervisord -c docker/supervisord.conf
```

## 🔧 关键配置说明

### 数据库连接示例
```python
from iBroker.lib import mysql

# 获取数据库连接
db = mysql.new_mysql_instance("tbconstruct")

# 执行查询
result = db.get_all("SELECT * FROM delivery_schedule_management_table")
```

### 配置获取示例
```python
from iBroker.lib import config

# 获取配置映射
cos_config = config.get_config_map("cos_config")

# 获取配置字符串
bucket = config.get_config_string("cos_bucket")

# 获取环境标识
env = config.get_env_flag(False)
```

## ⚠️ 注意事项

### 1. 网络配置
- 确保能访问腾讯云配置中心
- 确保能连接MySQL数据库
- 确保能访问COS存储服务

### 2. 权限配置
- 需要配置中心访问权限
- 需要数据库读写权限
- 需要COS存储访问权限

### 3. 环境依赖
- Python 3.7+ 版本
- MySQL 5.7+ 版本
- 网络连通性要求

## 🚀 快速验证

### 验证数据库连接
```python
from iBroker.lib import mysql

try:
    db = mysql.new_mysql_instance("tbconstruct")
    result = db.get_all("SELECT COUNT(*) as count FROM all_construction_ticket_data")
    print(f"工单数据量: {result[0]['count']}")
    print("✅ 数据库连接成功")
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
```

### 验证服务启动
```bash
# 检查HTTP服务
curl http://localhost:80/health

# 检查tRPC服务
curl http://localhost:8000/health
```

## 📞 技术支持

如遇到配置问题，请检查：
1. iBroker SDK版本是否正确
2. 配置中心连接是否正常
3. 数据库权限是否充足
4. 网络连接是否畅通

---

**配置完成后即可运行 `campus_resources.py` 中的相关功能！** 🎯
