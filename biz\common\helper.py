#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2024/3/19 10:57
# <AUTHOR> v_hxhaoliu
# @File    : helper.py
import datetime


def get_now_datetime() -> str:
    """
    获取当前的格式化的时间  - 日期时间
    """
    today = datetime.datetime.today()
    today = today.strftime("%Y-%m-%d %H:%M:%S")
    return today


def get_now_date() -> str:
    """
    获取 当前 的格式化的时间  - 日期
    """
    today = datetime.datetime.today()
    today = today.strftime("%Y-%m-%d")
    return today


def get_last_date(days) -> str:
    """
        获取当前时间的 几天前 的格式化的时间  - 日期
        days = -1 表示一天前
    """
    today = datetime.datetime.today()
    last_day = today + datetime.timedelta(days=days)
    last_day = last_day.strftime("%Y-%m-%d")
    return last_day
