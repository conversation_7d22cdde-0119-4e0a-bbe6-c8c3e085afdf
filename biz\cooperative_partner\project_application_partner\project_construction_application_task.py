import datetime

from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate

from biz.project_member_account_delete.account_delete_api import (
    AccountDeleteApi,
)
from biz.cooperative_partner.project_application_partner.tools import Tools
from biz.construction_final_acceptance.tools import Tools as Tools2


class ProjectApplicationFlowTrace(AjaxTodoBase):
    """
    项目报建流程进展
    """

    def __init__(self):
        super(ProjectApplicationFlowTrace, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        return {"code": -1, "msg": "项目报建工作未完成，请勿提交"}


class ProjectApplicationXgApproval(AjaxTodoBase):
    """
    项目报建-项管审批
    """

    def __init__(self):
        super(ProjectApplicationXgApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        project_name = self.ctx.variables.get("project_name")
        xg = AccountDeleteApi.get_role_leader(project_name, "项管-项目经理")
        WorkflowVarUpdate(
            instance_id=self.ctx.instance_id,
            variables={
                "xg": xg,
                "xg_approval": "",
                "xg_remark": "",
            },
        ).call()
        self.transfer(xg)

    def end(self, process_data, process_user):
        approval = process_data.get("xg_approval")
        remark = process_data.get("xg_remark")
        variables = {
            "xg_approval": approval,
            "xg_remark": remark,
        }
        if approval == "同意":
            serial_number = self.ctx.variables.get("serial_number")
            if serial_number == "2.4.3":
                work_list = process_data.get("work_list")
                work_dict = work_list[0]
                pm_file = work_dict.get("appendix", [])
                variables = {
                    **variables,
                    "pm_file": pm_file,
                }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectApplicationPmApproval(AjaxTodoBase):
    """
    项目报建-PM审批
    """

    def __init__(self):
        super(ProjectApplicationPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        project_name = self.ctx.variables.get("project_name")
        PM = AccountDeleteApi.get_PM(project_name)
        WorkflowVarUpdate(
            instance_id=self.ctx.instance_id,
            variables={
                "PM": PM,
                "pm_approval": "",
                "pm_remark": "",
            },
        ).call()
        self.transfer(PM)

    def end(self, process_data, process_user):
        pm_approval = process_data.get("pm_approval")
        pm_remark = process_data.get("pm_remark")
        variables = {
            "pm_approval": pm_approval,
            "pm_remark": pm_remark,
        }
        if pm_approval == "同意":
            serial_number = self.ctx.variables.get("serial_number")
            if serial_number == "2.4.3":
                work_list = process_data.get("work_list")
                work_dict = work_list[0]
                pm_appendix_list = []
                # for file in work_dict.get("appendix", []):
                #     pm_appendix_list.append(file["response"]["FileList"][0]["url"])
                pm_file = process_data.get("pm_file", [])
                for file in pm_file:
                    pm_appendix_list.append(file["response"]["FileList"][0]["url"])
                date = datetime.datetime.strptime(
                    work_dict.get("actual_finish_time"), "%Y-%m-%d"
                )
                pm_check_date = date.strftime("%Y-%m-%d %H:%M:%S")
                project_name = self.ctx.variables.get("project_name")
                project_code = Tools2.get_project_code(project_name)
                variables = {
                    **variables,
                    "pm_check_date": pm_check_date,
                    "pm_appendix_list": pm_appendix_list,
                    "project_code": project_code,
                    "work_dict": work_dict,
                }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class XfbgPmApproval(AjaxTodoBase):
    """
    消防第三方检测报告上传-完工确认PM审批
    """

    def __init__(self):
        super(XfbgPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        project_name = self.ctx.variables.get("project_name")
        PM = AccountDeleteApi.get_PM(project_name)
        WorkflowVarUpdate(
            instance_id=self.ctx.instance_id,
            variables={
                "PM": PM,
                "xfbg_pm_approval": "",
                "xfbg_pm_remark": "",
            },
        ).call()
        self.transfer(PM)

    def end(self, process_data, process_user):
        xfbg_pm_approval = process_data.get("xfbg_pm_approval")
        xfbg_pm_remark = process_data.get("xfbg_pm_remark")
        variables = {
            "xfbg_pm_approval": xfbg_pm_approval,
            "xfbg_pm_remark": xfbg_pm_remark,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
