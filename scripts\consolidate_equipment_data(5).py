#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import re
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iBroker.lib import mysql
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CorrectedEquipmentDataConsolidator:
    def __init__(self):
        self.db = mysql.new_mysql_instance("tbconstruct")
        self.start_time = time.time()

    def log_update_status(self, table_name, record_count, status='success', message=''):
        """记录更新状态，用于页面显示"""
        try:
            self.db.execute("""
                INSERT INTO consolidated_data_update_log (table_name, record_count, status, message) 
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                    update_time = CURRENT_TIMESTAMP,
                    record_count = VALUES(record_count),
                    status = VALUES(status),
                    message = VALUES(message)
            """, (table_name, record_count, status, message))
        except Exception as e:
            logger.warning(f"记录更新状态失败: {e}")

    def create_tables(self):
        logger.info("🏗️ 创建数据表（适配字段格式）...")
        create_main_table = """
        CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
            state VARCHAR(50) DEFAULT NULL,
            TicketId VARCHAR(100) NOT NULL,
            Title VARCHAR(255) DEFAULT NULL,
            SchemeNameCn VARCHAR(255) DEFAULT NULL,
            IsForceEnd VARCHAR(255) DEFAULT '0',
            project_name VARCHAR(255) DEFAULT NULL,
            device_name VARCHAR(255) DEFAULT NULL,
            supplier VARCHAR(255) DEFAULT NULL,
            device_type VARCHAR(100) DEFAULT NULL,
            equipment_sla VARCHAR(50) DEFAULT NULL,
            expected_time_equipment VARCHAR(50) DEFAULT NULL,  -- 保留原始格式（含~）
            estimated_time_delivery VARCHAR(50) DEFAULT NULL,   -- 保留原始格式（含~）
            delivery_gap VARCHAR(50) DEFAULT '-',               -- 支持业务字符串（如"已到货"）或数字
            completion_time VARCHAR(20) DEFAULT NULL,           -- 严格年-月-日
            po_create_time DATETIME DEFAULT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (TicketId),
            INDEX idx_project_name (project_name),
            INDEX idx_state (state),
            INDEX idx_device_type (device_type),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        create_log_table = """
        CREATE TABLE IF NOT EXISTS consolidated_data_update_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(100) NOT NULL,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            record_count INT DEFAULT 0,
            status VARCHAR(50) DEFAULT 'success',
            message TEXT,
            INDEX idx_table_name (table_name),
            INDEX idx_update_time (update_time)
        )
        """
        try:
            self.db.execute(create_main_table)
            self.db.execute(create_log_table)
            logger.info("✅ 数据表创建成功（字段格式适配完成）")
            return True
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False

    def fetch_base_tickets(self):
        """获取基础工单数据"""
        logger.info("📋 第一步：获取基础工单数据...")
        query = """
        SELECT DISTINCT
            actd.TicketId,
            actd.Title,
            actd.SchemeNameCn,
            actd.IsForceEnd
        FROM all_construction_ticket_data actd
        WHERE actd.SchemeNameCn LIKE '%设备生产%'
            AND actd.IsForceEnd = 0
        ORDER BY actd.TicketId
        """
        try:
            results = self.db.get_all(query)
            logger.info(f"✅ 获取基础工单: {len(results)} 条")
            return {item['TicketId']: item for item in results}
        except Exception as e:
            logger.error(f"❌ 获取基础工单失败: {e}")
            return {}

    def fetch_production_data(self):
        """获取生产racking数据（eprp表）"""
        logger.info("🏭 第二步：获取生产racking数据...")
        query = """
        SELECT 
            eprp.ticket_id,
            eprp.project_name,
            eprp.device_name,
            eprp.equipment_type
        FROM equipment_production_racking_process eprp
        WHERE eprp.equipment_type = '甲供'
        """
        try:
            results = self.db.get_all(query)
            production_map = {}
            for item in results:
                ticket_id = item['ticket_id']
                if ticket_id not in production_map:
                    production_map[ticket_id] = []
                production_map[ticket_id].append(item)
            logger.info(f"✅ 获取生产数据: {len(production_map)} 个工单")
            return production_map
        except Exception as e:
            logger.error(f"❌ 获取生产数据失败: {e}")
            return {}

    def fetch_delivery_data(self):
        """直接读取原始格式（含~和业务字符串）"""
        logger.info("🚚 第三步：获取交付计划数据（保留原始格式）...")
        query = """
        SELECT 
            dsm.project_name,
            dsm.device_type,
            dsm.supplier,
            dsm.expected_time_equipment,  -- 原始值：2024-07-25~2024-08-10 或 2024-07-18
            dsm.estimated_time_delivery,   -- 原始值：2024-08-11~2024-08-18 或 2024-07-20
            dsm.delivery_gap               -- 原始值：已到货 等业务字符串
        FROM delivery_schedule_management_table dsm
        WHERE dsm.project_name IN (SELECT DISTINCT project_name FROM equipment_production_racking_process)
          AND dsm.device_type IN (SELECT DISTINCT device_name FROM equipment_production_racking_process)
        """
        try:
            results = self.db.get_all(query)
            delivery_map = {}
            for item in results:
                key = f"{item['project_name']}_{item['device_type']}"
                # 确保日期字段保持原始字符串格式
                item['expected_time_equipment'] = str(item['expected_time_equipment']) if item[
                    'expected_time_equipment'] else None
                item['estimated_time_delivery'] = str(item['estimated_time_delivery']) if item[
                    'estimated_time_delivery'] else None
                delivery_map[key] = item
            logger.info(f"✅ 获取交付数据: {len(delivery_map)} 条记录（保留原始格式）")
            return delivery_map
        except Exception as e:
            logger.error(f"❌ 获取交付数据失败: {e}")
            return {}

    def fetch_sla_data(self):
        """获取设备SLA数据（ecmr表）"""
        logger.info("📊 第四步：获取设备SLA数据...")
        query = """
        SELECT 
            ecmr.device_name,
            ecmr.equipment_sla
        FROM equipment_category_mapping_relationship ecmr
        """
        try:
            results = self.db.get_all(query)
            sla_map = {item['device_name']: item['equipment_sla'] for item in results}
            if 'HVDC' in sla_map and '弹性一体柜(HVDC)' not in sla_map:
                sla_map['弹性一体柜(HVDC)'] = sla_map['HVDC']
            logger.info(f"✅ 获取SLA数据: {len(sla_map)} 条记录")
            return sla_map
        except Exception as e:
            logger.error(f"❌ 获取SLA数据失败: {e}")
            return {}

    def fetch_completion_data(self):
        """获取施工完成时间（cpd表）- 用于后续格式化为年-月-日"""
        logger.info("⏱️ 第五步：获取施工完成时间...")
        query = """
        SELECT 
            cpd.work_content,
            cpd.completion_time,  -- 原始时间（可能含时分秒）
            cpd.project_name
        FROM construct_plan_data cpd
        """
        try:
            results = self.db.get_all(query)
            completion_map = {}
            for item in results:
                key = f"{item['project_name']}_{item['work_content']}"
                completion_map[key] = item['completion_time']
                if item['work_content'] == 'HVDC':
                    special_key = f"{item['project_name']}_弹性一体柜(HVDC)"
                    completion_map[special_key] = item['completion_time']
            logger.info(f"✅ 获取完成时间数据: {len(completion_map)} 条记录")
            return completion_map
        except Exception as e:
            logger.error(f"❌ 获取完成时间数据失败: {e}")
            return {}

    def fetch_po_time_data(self):
        """获取PO创建时间（pi表）"""
        logger.info("💸 第六步：获取PO时间数据...")
        query = """
        SELECT 
            pi.project_name,
            pi.material_name,
            pi.po_create_time
        FROM payment_info pi
        WHERE pi.project_name IN (SELECT DISTINCT project_name FROM equipment_production_racking_process)
          AND pi.material_name IN (SELECT DISTINCT device_name FROM equipment_production_racking_process)
        """
        try:
            results = self.db.get_all(query)
            po_time_map = {}
            for item in results:
                key = f"{item['project_name']}_{item['material_name']}"
                po_time_map[key] = item['po_create_time']
                if item['material_name'] == 'HVDC':
                    special_key = f"{item['project_name']}_弹性一体柜(HVDC)"
                    po_time_map[special_key] = item['po_create_time']
            logger.info(f"✅ 获取PO时间数据: {len(po_time_map)} 条记录")
            return po_time_map
        except Exception as e:
            logger.error(f"❌ 获取PO时间数据失败: {e}")
            return {}

    def fetch_state_data(self):
        """获取区域数据（summary_data表）"""
        logger.info("🌍 第七步：获取区域数据...")
        query = """
        SELECT 
            sd.campus,
            sd.project_name,
            sd.state,
            CONCAT(sd.campus, sd.project_name) AS full_project_name
        FROM summary_data sd
        """
        try:
            results = self.db.get_all(query)
            state_map = {item['full_project_name']: item['state'] for item in results}
            logger.info(f"✅ 获取区域数据: {len(state_map)} 条记录")
            return state_map
        except Exception as e:
            logger.error(f"❌ 获取区域数据失败: {e}")
            return {}

    def _format_completion_date(self, time_val):
        """强制转换为 `年-月-日`，兼容所有格式"""
        if not time_val:
            return None
        time_str = str(time_val).strip()
        # 支持的格式列表（覆盖常见场景）
        formats = [
            '%Y-%m-%d %H:%M:%S', '%Y-%m-%d',  # 带时分秒 / 纯日期
            '%Y/%m/%d %H:%M:%S', '%Y/%m/%d',  # 斜杠分隔
            '%Y年%m月%d日', '%Y年%m月%d日 %H:%M:%S'  # 中文格式
        ]
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                return dt.strftime('%Y-%m-%d')
            except ValueError:
                continue
        # 处理特殊格式（如空格分割的日期时间）
        if ' ' in time_str:
            date_part = time_str.split(' ')[0]
            return self._format_completion_date(date_part)  # 递归解析日期部分
        # 检查是否已经是年-月-日格式
        if re.match(r'^\d{4}-\d{2}-\d{2}$', time_str):
            return time_str
        logger.warning(f"无法解析completion_time: {time_val}，保留原始值（建议检查数据）")
        return None  # 无法解析时返回None

    def _format_delivery_gap(self, gap_val):
        """处理delivery_gap字段，确保有有效值"""
        if not gap_val:
            return "-"  # 空值替换为-
        gap_str = str(gap_val).strip()
        # 检查是否为数字或特定业务字符串
        if gap_str in ["已到货", "未到货", "部分到货"]:
            return gap_str
        if gap_str.replace(".", "").isdigit():  # 支持数字和小数
            return gap_str
        return "-"  # 其他情况返回-

    def consolidate_and_insert(self):
        logger.info("🚀 开始数据合并流程（严格匹配字段格式）...")
        try:
            self.db.execute("DELETE FROM consolidated_equipment_data")
            logger.info("🗑️ 清空目标表成功")
        except Exception as e:
            logger.error(f"清空目标表失败: {e}")
            return False

        base_tickets = self.fetch_base_tickets()
        if not base_tickets:
            logger.error("❌ 无基础工单数据，终止流程")
            return False

        production_data = self.fetch_production_data()
        delivery_data = self.fetch_delivery_data()
        sla_data = self.fetch_sla_data()
        completion_data = self.fetch_completion_data()
        po_time_data = self.fetch_po_time_data()
        state_data = self.fetch_state_data()

        combined_data = []
        for ticket_id, base_info in base_tickets.items():
            if ticket_id not in production_data:
                continue
            for prod_item in production_data[ticket_id]:
                project_name = prod_item['project_name']
                device_name = prod_item['device_name']
                mapped_device_name = 'HVDC' if device_name == '弹性一体柜(HVDC)' else device_name

                delivery_key = f"{project_name}_{mapped_device_name}"
                delivery_info = delivery_data.get(delivery_key, {})
                completion_key = f"{project_name}_{device_name}"
                completion_time_raw = completion_data.get(completion_key)
                po_create_time = po_time_data.get(delivery_key)
                state = state_data.get(f"{project_name}")

                # 1. 处理时间字段（确保保持原始字符串格式）
                expected_time = str(delivery_info.get('expected_time_equipment')) if delivery_info.get(
                    'expected_time_equipment') else None
                estimated_time = str(delivery_info.get('estimated_time_delivery')) if delivery_info.get(
                    'estimated_time_delivery') else None

                # 验证时间格式是否正确（但不修改原始数据）
                if expected_time and not re.match(r'^\d{4}-\d{2}-\d{2}(~\d{4}-\d{2}-\d{2})?$', expected_time):
                    logger.warning(f"预期设备时间格式异常: {expected_time} (工单: {ticket_id})")
                    expected_time = None
                if estimated_time and not re.match(r'^\d{4}-\d{2}-\d{2}(~\d{4}-\d{2}-\d{2})?$', estimated_time):
                    logger.warning(f"预计交付时间格式异常: {estimated_time} (工单: {ticket_id})")
                    estimated_time = None

                # 2. 处理delivery_gap字段
                delivery_gap = self._format_delivery_gap(delivery_info.get('delivery_gap'))

                # 3. 强制转换为年-月-日
                completion_time = self._format_completion_date(completion_time_raw)

                combined_record = {
                    'state': state,
                    'TicketId': ticket_id,
                    'Title': base_info['Title'],
                    'SchemeNameCn': base_info['SchemeNameCn'],
                    'IsForceEnd': base_info['IsForceEnd'],
                    'project_name': project_name,
                    'device_name': device_name,
                    'supplier': delivery_info.get('supplier', '待确认'),
                    'device_type': prod_item['equipment_type'],
                    'equipment_sla': sla_data.get(device_name),
                    'expected_time_equipment': expected_time,  # 保持原始字符串格式
                    'estimated_time_delivery': estimated_time,  # 保持原始字符串格式
                    'delivery_gap': delivery_gap,
                    'completion_time': completion_time,
                    'po_create_time': po_create_time
                }
                combined_data.append(combined_record)

        if not combined_data:
            logger.error("❌ 数据组装后无有效记录")
            return False

        logger.info(f"📥 开始插入数据，共 {len(combined_data)} 条...")
        insert_count = 0
        for record in combined_data:
            try:
                insert_sql = """
                INSERT IGNORE INTO consolidated_equipment_data (
                    state, TicketId, Title, SchemeNameCn, IsForceEnd,
                    project_name, device_name, supplier, device_type,
                    equipment_sla, expected_time_equipment, estimated_time_delivery,
                    delivery_gap, completion_time, po_create_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                self.db.execute(insert_sql, (
                    record['state'],
                    record['TicketId'],
                    record['Title'],
                    record['SchemeNameCn'],
                    record['IsForceEnd'],
                    record['project_name'],
                    record['device_name'],
                    record['supplier'],
                    record['device_type'],
                    record['equipment_sla'],
                    record['expected_time_equipment'],  # 直接插入原始格式（含~）
                    record['estimated_time_delivery'],  # 直接插入原始格式（含~）
                    record['delivery_gap'],  # 格式化后的值（如"已到货"或"-"）
                    record['completion_time'],  # 插入年-月-日格式
                    record['po_create_time']
                ))
                insert_count += 1
            except Exception as e:
                logger.warning(f"插入记录失败（TicketId: {record['TicketId']}）: {e}")
                continue

        elapsed_time = time.time() - self.start_time
        self.log_update_status('consolidated_equipment_data', insert_count, 'success',
                               f"数据合并完成，耗时 {elapsed_time:.2f} 秒")
        logger.info(f"✅ 数据合并完成！插入 {insert_count} 条记录，格式完全匹配需求")
        return True


def main():
    """主函数"""
    logger.info("🚀 启动设备数据合并程序（最终修正版）")

    consolidator = CorrectedEquipmentDataConsolidator()

    if not consolidator.create_tables():
        return False

    if not consolidator.consolidate_and_insert():
        return False

    logger.info("🎉 设备数据合并程序执行完成！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)