from iBroker.lib import mysql


def estimate_construction_cycle(ConstructionDeliveryTime, EstimatedDeliveryTime,
                                Campus, SupplierShare, CampusWithCrossSpringFestival):
    tb_db = mysql.new_mysql_instance('tbconstruct')
    EstimateConstructionCycle = 7
    if SupplierShare == '无' and CampusWithCrossSpringFestival == '是':
        EstimateConstructionCycle += 2.5
    elif SupplierShare == '无':
        EstimateConstructionCycle += 2
    elif CampusWithCrossSpringFestival == '是':
        EstimateConstructionCycle += 0.5
    # 新需求条件
    if Campus in ['怀来东园', '怀来瑞北', '天津高新']:
        month = int(ConstructionDeliveryTime.split('-')[1])
        if month == 10:
            EstimateConstructionCycle += 4
        elif month == 11:
            EstimateConstructionCycle += 3
        elif month == 12:
            EstimateConstructionCycle += 2
        elif month == 1:
            EstimateConstructionCycle += 1
    query_data = f"SELECT construction_time_electricity_outside_city FROM " \
                 f"construction_evaluation_data WHERE campus = '{Campus}'"
    result = tb_db.query(query_data)

    if len(result) > 0:
        construction_time_electricity_outside_city = result[0]['construction_time_electricity_outside_city']
        construction_time_electricity_outside_city = float(construction_time_electricity_outside_city)
        EstimateConstructionCycle = float(EstimateConstructionCycle)
        if EstimateConstructionCycle < construction_time_electricity_outside_city:
            EstimateConstructionCycle = construction_time_electricity_outside_city
        else:
            EstimateConstructionCycle = EstimateConstructionCycle

    data = {
        'EstimateConstructionCycle': EstimateConstructionCycle
    }
    return data
