import ast
import datetime
import hashlib
import hmac
import json
import time
import uuid
import numpy as np
from urllib.parse import quote

import pandas as pd
from iBroker.lib.sdk import flow, tof, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class ProjectStartUpDcops(object):
    """
        项目启动
        腾讯方调用接口：输出数据(合作伙伴Dcops系统)
    """

    def __init__(self):
        super(ProjectStartUpDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_start_up_process_initiation(self, project_name):
        """
            创建工单--项目启动
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")

        DemandDelivery = self.ctx.variables.get("demand_delivery", "")
        ConstructionMode = self.ctx.variables.get("construction_mode", "")
        PM = self.ctx.variables.get("PM", "")
        Architecture = self.ctx.variables.get("architecture", "")
        Beautification = self.ctx.variables.get("beautification", "")
        TreeSutra = self.ctx.variables.get("tree_sutra", "")
        WeakCurrent = self.ctx.variables.get("weak_current", "")
        Business = self.ctx.variables.get("business", "")
        SupplyChain = self.ctx.variables.get("supply_chain", "")
        InformPerson = self.ctx.variables.get("inform_a_person", "")
        AHU = self.ctx.variables.get("AHU", "")
        FirewoodHair = self.ctx.variables.get("firewood_hair", "")
        LowPressureCabinet = self.ctx.variables.get("low_pressure_cabinet", "")
        MediumPressureCabinet = self.ctx.variables.get("medium_pressure_cabinet", "")
        Transformers = self.ctx.variables.get("transformers", "")
        SynthesisDistributionCabinet = self.ctx.variables.get("synthesis_distribution_cabinet", "")
        HVDC = self.ctx.variables.get("HVDC", "")
        Battery = self.ctx.variables.get("battery", "")
        PDU = self.ctx.variables.get("PDU", "")
        Cabinet = self.ctx.variables.get("cabinet", "")
        Integrator = self.ctx.variables.get("integrator", "")
        ItemTube = self.ctx.variables.get("item_tube", "")
        SupervisionManagement = self.ctx.variables.get("supervision_and_management", "")
        ThirdPartyTesting = self.ctx.variables.get("third_party_testing", "")

        personnel_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name,
            "DemandDelivery": DemandDelivery,
            "ConstructionMode": ConstructionMode,
            "PM": PM,
            "Architecture": Architecture,
            "Beautification": Beautification,
            "TreeSutra": TreeSutra,
            "WeakCurrent": WeakCurrent,
            "Business": Business,
            "SupplyChain": SupplyChain,
            "InformPerson": InformPerson,
            "AHU": AHU,
            "FirewoodHair": FirewoodHair,
            "LowPressureCabinet": LowPressureCabinet,
            "MediumPressureCabinet": MediumPressureCabinet,
            "Transformers": Transformers,
            "SynthesisDistributionCabinet": SynthesisDistributionCabinet,
            "HVDC": HVDC,
            "Battery": Battery,
            "PDU": PDU,
            "Cabinet": Cabinet,
            "Integrator": Integrator,
            "ItemTube": ItemTube,
            "SupervisionManagement": SupervisionManagement,
            "ThirdPartyTesting": ThirdPartyTesting,
            "info": {"data": 111}
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        data = {
            "Action": "Ticket",  # 原样填写
            "Method": "Create",  # 原样填写
            "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
            # 调用方系统标识
            "CorpId": corp_id,  # (*必填)
            # 服务商企业ID
            "Data": {  # 自定义流程变量
                "CustomVariables": personnel_data,
                # 流程定义标识
                "ProcessDefinitionKey": "tb-project-start",  # (*必填)
                # 工单来源
                "Source": f"外部系统（{Facilitator}）",
                # 工单描述
                "TicketDescription": f"{project_name}:项目启动",  # (*必填)
                # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                # 工单标题
                "TicketTitle": f"{project_name}:项目启动",  # (*必填)
                "UserInfo": {  # 用户信息
                    "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                    "Creator": "v_mmywang",  # (*必填)
                    "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                }
            }
        }

        info = calling_external_interfaces(interface_info, data)

        PartnerInstanceId = str(info['data']['InstanceId'])
        PartnerTicketId = str(info['data']['TicketId'])
        variables = {
            'project_start_up_process_initiation': str(info),
            'system_id': system_id,
            'PartnerInstanceId': PartnerInstanceId,
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def analyze_control_plan(self, project_name):
        progress_table = self.ctx.variables.get("progress_table")
        for row in progress_table:
            progress = row.get("progress")
        ticket_id = self.ctx.variables.get("ticket_id")
        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        if progress:
            """
                excel解析总控计划
            """
            encoded_url = quote(progress, safe=":/")
            df = pd.read_excel(encoded_url, engine='openpyxl', sheet_name='总控计划模版')
            df = df.replace({np.nan: None})
            """
                所有数据存库
            """
            insert_data = []
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                plan_duration_construction = row['工期']
                start_time = row['计划开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划开始时间（年/月/日）']) else None
                completion_time = row['计划完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                insert_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'serial_number': serial_number,
                    'work_content': work_content,
                    'plan_duration_construction': plan_duration_construction,
                    'start_time': start_time,
                    'completion_time': completion_time,
                    'responsible_person': responsible_person,
                    'output_object': output_object,
                    'input_object': input_object,
                    'construction_attribute': construction_attribute,
                    'now_time': now_time
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("excel_data", insert_data)
            tb_db.commit()
        else:
            self.workflow_detail.add_kv_table('解析总控计划', {"success": False, "data": "未获取到总控计划"})
        flow.complete_task(self.ctx.task_id)


class WaitPartnerCallInterfaceXmqdDcops(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceXmqdDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_master_control_plan_upload(self):
        """
            等待总控计划上传
        """
        progress_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT progress " \
              "FROM file_storage_data " \
              f"WHERE dcopsTicketId = '{ticket_id}'" \
              f"AND project_name = '{project_name}' "
        result_list = db.get_all(sql)

        for record in result_list:
            progress = record.get('progress')
            progress = progress.replace("'", '"')
            progress = json.loads(progress)
            # 获取 plan_url 字符串
            if progress is not None:
                for doc in progress:
                    if doc is not None and "response" in doc and "FileList" in doc["response"]:
                        file_list = doc["response"]["FileList"]
                        if len(file_list) > 0 and "url" in file_list[0]:
                            url = file_list[0]["url"]
                            name = file_list[0]['name']
                            progress_table.append({
                                "progress": url,
                                'progress_name': name
                            })

        self.workflow_detail.add_kv_table('总控计划文档', {'message': result_list})
        if result_list:
            variables = {
                "zkjh_result_list": result_list,
                "progress_table": progress_table
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class ZkjhSupervisionExaminationApprovalDcops(AjaxTodoBase):
    """
        总控计划-监理审批
    """

    def __init__(self):
        super(ZkjhSupervisionExaminationApprovalDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_supervision_approval = process_data.get('zkjh_supervision_approval')
        zkjh_supervision_remark = process_data.get('zkjh_supervision_remark')

        if zkjh_supervision_approval == '驳回':
            if not zkjh_supervision_remark:
                zkjh_supervision_remark = '无'
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 1

            zkjh_jl = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_supervision_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                        "data": zkjh_jl,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            if info.get('ErrorCode') == -1:
                return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'zkjh_supervision_approval': zkjh_supervision_approval,
                'zkjh_supervision_remark': zkjh_supervision_remark,
                'zkjh_supervision_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_supervision_approval == '同意':
            if not zkjh_supervision_remark:
                zkjh_supervision_remark = '无'

            variables = {
                'zkjh_supervision_approval': zkjh_supervision_approval,
                'zkjh_supervision_remark': zkjh_supervision_remark
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhItemApprovalDcops(AjaxTodoBase):
    """
        总控计划-项管审批
    """

    def __init__(self):
        super(ZkjhItemApprovalDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_item_approval = process_data.get('zkjh_item_approval')
        zkjh_item_remark = process_data.get('zkjh_item_remark')

        if zkjh_item_approval == '驳回':
            if not zkjh_item_remark:
                zkjh_item_remark = '无'
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 2

            zkjh_xg = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_item_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                        "data": zkjh_xg,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            if info.get('ErrorCode') == -1:
                return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'zkjh_item_approval': zkjh_item_approval,
                'zkjh_item_remark': zkjh_item_remark,
                'zkjh_item_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_item_approval == '同意':
            if not zkjh_item_remark:
                zkjh_item_remark = '无'

            variables = {
                'zkjh_item_approval': zkjh_item_approval,
                'zkjh_item_remark': zkjh_item_remark
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhPMApprovalDcops(AjaxTodoBase):
    """
        总控计划-PM审批
    """

    def __init__(self):
        super(ZkjhPMApprovalDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        zkjh_PM_approval = process_data.get('zkjh_PM_approval')
        zkjh_PM_remark = process_data.get('zkjh_PM_remark')

        if zkjh_PM_approval == '驳回':
            if not zkjh_PM_remark:
                zkjh_PM_remark = '无'
            query_sql = f"DELETE FROM file_storage_data " \
                        f"WHERE dcopsTicketId = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 3

            zkjh_PM = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                        "data": zkjh_PM,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            if info.get('ErrorCode') == -1:
                return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'zkjh_PM_approval': zkjh_PM_approval,
                'zkjh_PM_remark': zkjh_PM_remark,
                'zkjh_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif zkjh_PM_approval == '同意':
            examine_approve = 1
            role = 4

            if not zkjh_PM_remark:
                zkjh_PM_remark = '无'

            zkjh_PM = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": zkjh_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_approval_results",  # (*必填) 云函数名称
                        "data": zkjh_PM,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            if info.get('ErrorCode') == -1:
                return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'zkjh_PM_approval': zkjh_PM_approval,
                'zkjh_PM_remark': zkjh_PM_remark,
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
