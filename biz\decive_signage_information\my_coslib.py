import os
import time
import random
from iBroker.lib import config
from iBroker.lib import exception
from qcloud_cos import CosConfig, CosS3Client, CosClientError, CosServiceError


class Tool:
    env = config.get_env_flag(False)
    if env == "product":
        env = ""
    else:
        env = "test"
    # 存储桶，测试环境使用idc-iflowtest-appid，正式环境使用idc-iflow-appid
    This_Bucket = 'idc-iflow' + env + '-1258344706'

    @staticmethod
    def getCosClient() -> CosS3Client:
        cos_config = config.get_config_map("cos_config")
        if cos_config is None:
            raise exception.LogicException("获取COS配置失败", exception.EC_INNER_ERR)

        cfg = CosConfig(Region=cos_config["Region"], SecretId=cos_config["SecretId"],
                        SecretKey=cos_config["SecretKey"], Token=None, Scheme="https")
        return CosS3Client(cfg)

    @staticmethod
    def uploadFile(file_name: str, cos_path: str = "tbbg/"):
        """上传文件到COS

        Args:
            file_name (str): 文件名，带路径

        Return: True
        """
        # 取绝对路径
        file_name = os.path.abspath(file_name)
        if not os.path.exists(file_name):
            raise exception.LogicException("文件不存在:" + file_name, exception.EC_INNER_ERR)

            # 取文件名
        path = file_name.split(os.sep)
        simple_name = path[-1]
        # 获取当前时间戳拼接随机数
        timestamp = int(time.time())
        random_num = random.randint(1, 1000)
        result = str(timestamp) + str(random_num)
        client = Tool.getCosClient()
        try:
            # 使用'rb'打开文件
            with open(file_name, 'rb') as fp:
                response = client.put_object(
                    Bucket=Tool.This_Bucket,
                    Body=fp,
                    Key=cos_path + result + "_" + simple_name,
                    StorageClass='STANDARD'
                )
                # print(response)
                return cos_path + result + "_" + simple_name
        except (CosServiceError, CosClientError) as e:
            raise exception.LogicException("上传COS出错:" + str(e), exception.EC_INNER_ERR)
