import datetime
import uuid
import numpy as np
import pandas as pd
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql
from biz.construction_process.cos_lib import COSLib


class ExcelUrl(AjaxTodoBase):
    """
        下载excel
    """

    def __init__(self):
        super(ExcelUrl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        excel_url = process_data.get('excel_url', None)
        variables = {
            'excel_url': excel_url
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
