import datetime
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql,config
from iBroker.lib.sdk import gnetops


class DailyPaperCommit(AjaxTodoBase):
    """
    建设日报提交代办类
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticketid = self.ctx.variables.get('ticketid')
        ticket_id = self.ctx.variables.get("ticket_id")
        now_time = self.ctx.variables.get('now_time')
        # 提取日报文件的url
        daily_newspaper = process_data.get('daily_newspaper', '')
        daily_newspaper_table = []
        daily_newspaper_url = []
        insert_data = {}
        for doc in daily_newspaper:
            if "response" in doc and "FileList" in doc["response"]:
                file_list = doc["response"]["FileList"]
                if len(file_list) > 0 and "url" in file_list[0]:
                    url = file_list[0]["url"]
                    name = file_list[0]['name']
                    daily_newspaper_url.append(
                        'url：' + url
                        + '；' + "now_time：" + str(now_time)
                    )
                    daily_newspaper_table.append({
                        "daily_newspaper": url,
                        'daily_newspaper_name': name
                    })
        daily_newspaper_join = ', '.join(daily_newspaper_url)
        if daily_newspaper and len(daily_newspaper) > 0 and 'response' in daily_newspaper[0]:
            insert_data = {
                'daily_newspaper': daily_newspaper_join,
                'ticket_id': ticket_id,
                'ticketid': ticketid
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("daily_create_info_url", insert_data)
        tb_db.commit()
        variables = {
            'daily_newspaper': daily_newspaper,
            'daily_newspaper_table': daily_newspaper_table
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DailyDocumentDisplay(AjaxTodoBase):
    """
        展示日报文件
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables={})


class DailyPaper(object):
    """
      建设日报流程
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def daily_paper_generate(self):
        db = mysql.new_mysql_instance("tbconstruct")
        daily_paper_info = db.get_list("daily_create_info",
                                       fields=['ticketid', 'daily_processor', 'project_name'],
                                       conditions={"status": "0"})
        daily_report_old = config.get_config_map(
            "daily_report_old"
        )
        res_list = []
        processed_project_names = set()  # 用于跟踪已处理的 project_name
        for i in daily_paper_info:
            daily_processor = i.get('daily_processor')
            project_name = i.get('project_name')
            if project_name not in daily_report_old:
                continue
            if daily_processor and project_name not in processed_project_names:
                now = datetime.datetime.now()
                year = now.year
                month = now.month
                day = now.day
                date_string = f"{year}年{month}月{day}号"
                processTitle = date_string + f"{project_name}建设日报"
                data = {
                    "CustomVariables": {
                        "daily_processor": i.get('daily_processor'),
                        "project_name": i.get('project_name'),
                        'ticketid': i.get('ticketid'),
                        'now_time': now.strftime("%Y-%m-%d")
                    },
                    "ProcessDefinitionKey": "project_construct_daily",
                    "Source": "",
                    "TicketDescription": "建设日报流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "keketan;v_mmywang",
                        "Creator": "keketan",
                        "Deal": "keketan"
                    }
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                processed_project_names.add(project_name)

        variables = {
            'res_list': res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def find_a_project_leader(self):
        """
            获取项目日报处理人
        """
        project_name = self.ctx.variables.get('project_name')
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}'" \
                    f"and del_flag = 0"
        project_result = db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        integrator = account_dict.get('集成商-项目经理')
        item_tube = account_dict.get('项管-项目经理')
        supervision_management = account_dict.get('监理-总监理工程师')

        variables = {
            'integrator': integrator,
            'item_tube': item_tube,
            'supervision_management': supervision_management
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def insert_daily_info(self, dailyinfo, project_phase, project_name, ticketid):
        db = mysql.new_mysql_instance("tbconstruct")

        # 写入日报建单数据
        insert_data = {
            "ticketid": ticketid,
            "project_phase": project_phase,
            "project_name": project_name,
        }
        create_id = db.insert("daily_create_info", insert_data)

        # 写入日报详情
        for i in dailyinfo:
            insert_data = {
                "create_id": create_id,
                "procedure": i["procedure"],
            }
            db.insert("daily_details_info", insert_data)
