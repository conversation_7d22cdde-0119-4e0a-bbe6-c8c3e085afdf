#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/04/13 18:48:07
# <AUTHOR> early
# @Site    :
# @File    : tbox_alarm_task
from datetime import datetime, timedelta
import pytz
from iBroker.lib.sdk import flow
from iBroker.lib.sdk.gnetops import create_ticket
from iBroker.lib.sdk.tmp_ngate import get_server_alarm
from iBroker.lib import mysql
from noc_robot.message.send_message import send_text_message
from iBroker.lib.sdk.assetbox import send_sms
from iBroker.lib.sdk.assetbox import send_wework
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk.flow import complete_task
from iBroker.logone import logger


class GetTboxAlarm(object):
    """
    接收定时任务触发告警事件单
    """

    def __init__(self):
        self.workflow_detail = None
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.tbox_db = mysql.new_mysql_instance("sandbox")
        self.workflow_detail = WorkflowDetailService()

    def get_tbox_alarm(self, tbox_ip: str, tbox_alarm_type: str):
        zone = pytz.timezone('Asia/Shanghai')
        begin_time = str((datetime.now(zone) - timedelta(minutes=60)).strftime("%Y-%m-%d %H:%M:%S"))
        end_time = str((datetime.now(zone).strftime("%Y-%m-%d %H:%M:%S")))
        ip = tbox_ip
        alarm_type = tbox_alarm_type
        start = 0
        pagesize = 20
        logger.info(
            "tmp接口请求参数：begin_time：" + begin_time + "，end_time：" + end_time + "，ip：" + ip + "，alarm_type：" + alarm_type)
        alarm_data = get_server_alarm(begin_time, end_time, ip, alarm_type, start, pagesize)
        alarm_data_tmp = alarm_data
        logger.info("tmp接口返回数据：" + str(alarm_data_tmp))
        obj = alarm_data
        # url = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9891ec3-d3f7-4362-b03b-0bd9a7b97b12"
        url = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c2767afe-526e-46ba-bb8b-f3305784b867"
        if len(str(alarm_data_tmp[2])) < 20:
            alarm_str = "服务器IP：" + ip + "，" + begin_time + "到" + end_time + "告警查询结果为空" + ",接口数据:" + str(
                alarm_data_tmp)
            logger.info(alarm_str)
        else:
            for alarm in obj[2]:
                insert_data = {
                    "begin_time": begin_time,
                    "end_time": end_time,
                    "ip": tbox_ip,
                    "alarm_type": tbox_alarm_type,
                    "alarm_data": str(alarm_data),
                    "status": 0,
                    "updated_at": end_time,
                    "created_at": end_time,
                    "tmp_level": alarm["type"],
                    "tmp_type": alarm["type"],
                    "tmp_dept": alarm["dept"],
                    "tmp_service": alarm["service"],
                    "tmp_module": alarm["module"],
                    "tmp_service_group": alarm["service_group"],
                    "tmp_operator": alarm["operator"],
                    "tmp_operator_bak": alarm["operator_bak"],
                    "tmp_id": alarm["id"],
                    "tmp_recover_time": "",
                    "tmp_create_time": alarm["create_time"]
                }
                if "recover_time" not in alarm:
                    # 告警未恢复，需要处理
                    if alarm["type"] == "Ping告警":
                        datalist = self.tbox_db.get_list(
                            table="tbox_alarm",
                            fields=["id", "begin_time", "end_time", "ip", "alarm_type", "alarm_data", "status",
                                    "updated_at",
                                    "created_at"],
                            conditions={"tmp_id": [alarm["id"]]})
                        if not datalist:
                            send_text_message(url=url, context="查询告警数据库为空，开始写入数据库，告警信息：" + str(alarm_data_tmp))
                            res = self.tbox_db.insert(table="tbox_alarm", insertdata=insert_data)

                            logger.info("待走自动派单流程,插入数据ID：" + str(res))
                            tbox_data = self.tbox_db.get_list(
                                table="tbox_config",
                                fields=["id", "operation_guide", "rack", "tbox_ip", "tbox_sn", "room"],
                                conditions={"device_ip": [alarm["ci_name"]]})
                            guide_str = "设备房间：" + tbox_data[0]["room"] + "，设备机架：" + tbox_data[0]["rack"] + \
                                        "，设备SN：" + tbox_data[0]["tbox_sn"] + "，设备IP：" + tbox_data[0]["tbox_ip"] +\
                                        "，操作指引：" + tbox_data[0]["operation_guide"]

                            # 触发drops自动建现场跟进单------------------------------------------------------------
                            sub_ticket_id = create_ticket(
                                flow_key="tbox_event_flow",
                                description="【TBOX故障维修】当前设备已脱机，需现场进行维保处理。操作说明："+guide_str,
                                ticket_level="1",
                                title="智维TBOX采集器故障维修单",
                                creator="earlyzhao",
                                concern="earlyzhao",
                                deal="earlyzhao",
                                source="",
                                custom_var={
                                    "idc": alarm["idc"],
                                    "tmp_id": alarm["id"],
                                    "tbox_ip": tbox_ip
                                }
                            )
                            update_data = {
                                "status": 1,
                                "updated_at": end_time,
                                "ticket_id": sub_ticket_id["TicketId"],
                                "instance_id": sub_ticket_id["InstanceId"]
                            }
                            self.tbox_db.update(table='tbox_alarm', data=update_data,
                                                conditions={"ip": [tbox_ip], "alarm_type": [tbox_alarm_type],
                                                            "tmp_id": [alarm["id"]]})
                            logger.info("自动派单流程已下发，ticket流程单：：" + str(sub_ticket_id))
                            send_text_message(url=url,
                                              context="自动派单流程已下发，ticket流程单：" + str(sub_ticket_id))
                        else:
                            datalist = self.tbox_db.get_list(
                                table="tbox_alarm",
                                fields=["id", "instance_id", "tmp_id", "ip", "alarm_data", "status"],
                                conditions={"tmp_id": [alarm["id"]]})

                            instance_id = datalist[0]['instance_id']
                            message = 'TBOX故障流程单号：' + instance_id + ",告警信息：" + datalist[0]["alarm_data"]
                            logger.info("数据已落库，已派过单，无需重复派单，告警信息：" + str(message))
                            # send_text_message(url=url, context="数据已落库，已派过单，无需重复派单")
                    else:
                        datalist = self.tbox_db.get_list(
                            table="tbox_alarm",
                            fields=["id", "begin_time", "end_time", "ip", "alarm_type", "alarm_data", "status",
                                    "updated_at",
                                    "created_at"],
                            conditions={"tmp_id": [alarm["id"]]})
                        if not datalist:
                            send_text_message(url=url, context="查询数据库为空，但非Ping告警，不派单，写入数据库")
                            insert_res = self.tbox_db.insert(table="tbox_alarm",
                                                             insertdata=insert_data
                                                             )
                            logger.info("非Ping告警落库不派单,告警信息：" + str(alarm_data))
                            send_text_message(url=url, context="插入数据结果：" + str(insert_res))
                        else:
                            logger.info("告警数据已存在,告警信息：" + str(alarm_data))
                            send_text_message(url=url, context="数据已存在,数据信息：" + str(datalist))
                else:
                    datalist = self.tbox_db.get_list(
                        table="tbox_alarm",
                        fields=["id", "instance_id", "tmp_id", "begin_time", "end_time", "ip", "alarm_type",
                                "alarm_data", "status",
                                "updated_at",
                                "created_at"],
                        conditions={"tmp_id": [alarm["id"]]})
                    if datalist:
                        instance_id = datalist[0]['instance_id']
                        message = 'TBOX故障流程单号：' + instance_id + ",告警信息：" + datalist[0]["alarm_data"]
                        if datalist[0]["status"] == 2:
                            logger.info(
                                "当前告警已恢复，已更新过数据库字段，下一步现场结单后会自动校验，告警信息：" + str(message))
                        else:
                            # 告警已恢复，更新db
                            update_data = {
                                "status": 2,
                                "updated_at": end_time,
                                "tmp_recover_time": alarm["recover_time"]
                            }
                            self.tbox_db.update(table='tbox_alarm', data=update_data,
                                                conditions={"tmp_id": [alarm["id"]]})
                            logger.info(
                                "当前告警已恢复，更新数据库字段，下一步现场结单后会自动校验！告警信息：" + str(alarm_data))
                            send_text_message(url=url,
                                              context="当前告警已恢复，更新数据库字段，下一步现场结单后会自动校验，具体信息：" + str(
                                                  message))
                    else:
                        logger.info("告警数据不存在,不做特殊处理！告警信息："+alarm["raw"])
                        # send_text_message(url=url, context="告警数据不存在,不做特殊处理！告警信息："+str(alarm_data))

    def create_tbox_flow(self, user: str, operation_guide: str, tbox_ip: str, tbox_alarm_type: str):
        send_sms(user, operation_guide)
        send_wework(user, operation_guide)
        url = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9891ec3-d3f7-4362-b03b-0bd9a7b97b12"
        alarm_str = "Tbox ip：" + tbox_ip + "创建维修工单到现场，操作指引：" + str(operation_guide) + ",流程任务ID:" + str(
            self.ctx.task_id)
        send_text_message(url=url, context=alarm_str)

        info = self.tbox_db.get_list(table="tbox_alarm", fields=["alarm_data"],
                                     conditions={"ip": [tbox_ip], "alarm_type": [tbox_alarm_type]})

        if info:
            # 将信息详情写入节点详情
            self.workflow_detail.add_kv_table(name='写入自动节点', kv_table={"tbox_alarm_info": info, "user": user,
                                                                             "operation_guide": operation_guide})
        else:
            raise Exception("告警列表里不存在要查找的数据！")
        # 用于流程节点流转，同时将变量info传入流程变量
        flow.complete_task(self.ctx.task_id,
                           variables={"tbox_alarm_info": info, "user": user, "operation_guide": operation_guide})

        return {"success": True}

    def check_tbox_flow(self, tmp_id: str, tbox_ip: str):
        datalist = self.tbox_db.get_list(
            table="tbox_alarm",
            fields=["id", "begin_time", "end_time", "ip", "alarm_type", "alarm_data", "status", "updated_at",
                    "created_at"],
            conditions={"instance_id": [self.ctx.instance_id], "status": [2]})
        url = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9891ec3-d3f7-4362-b03b-0bd9a7b97b12"
        logger.info("自动校验返回：" + str(datalist))
        if not datalist:
            # 查询结果状态为0或1驳回
            complete_task(self.ctx.task_id, {"status": "1"})
            send_text_message(url=url,
                              context="校验不通过，流程驳回给现场,流程ID：" + self.ctx.instance_id + "，告警ID：" +
                                      tmp_id + "，TBOX盒子IP：" + tbox_ip)
        else:
            # 查询结果状态为2则通过
            complete_task(self.ctx.task_id, {"status": "2"})
            send_text_message(url=url,
                              context="校验已通过，流程结单,流程ID：" + self.ctx.instance_id + "，告警ID：" + tmp_id +
                                      "，TBOX盒子IP：" + tbox_ip)
