#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的数据合并脚本 - 不依赖iBroker库
使用标准的pymysql库连接数据库
"""

import pymysql
import logging
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置 - 需要根据实际情况修改
DB_CONFIG = {
    'host': 'localhost',  # 修改为实际的数据库主机
    'user': 'root',       # 修改为实际的用户名
    'password': '',       # 修改为实际的密码
    'database': 'tbconstruct',
    'charset': 'utf8mb4'
}

class StandaloneDataConsolidator:
    def __init__(self):
        self.start_time = time.time()

    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**DB_CONFIG)

    def log_update_status(self, table_name, record_count, status='success', message=''):
        """记录更新状态，用于页面显示"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor()
            
            cursor.execute("""
                INSERT INTO consolidated_data_update_log (table_name, record_count, status, message) 
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE 
                    update_time = CURRENT_TIMESTAMP,
                    record_count = VALUES(record_count),
                    status = VALUES(status),
                    message = VALUES(message)
            """, (table_name, record_count, status, message))
            
            connection.commit()
            cursor.close()
            connection.close()
        except Exception as e:
            logger.warning(f"记录更新状态失败: {e}")

    def create_tables(self):
        """创建必要的表"""
        logger.info("🏗️ 创建数据表...")

        # 创建合并表
        create_main_table = """
        CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
            state VARCHAR(50) DEFAULT NULL,
            TicketId VARCHAR(100) NOT NULL,
            Title VARCHAR(255) DEFAULT NULL,
            SchemeNameCn VARCHAR(255) DEFAULT NULL,
            IsForceEnd VARCHAR(255) DEFAULT '0',
            project_name VARCHAR(255) DEFAULT NULL,
            device_name VARCHAR(255) DEFAULT NULL,
            supplier VARCHAR(255) DEFAULT NULL,
            device_type VARCHAR(100) DEFAULT NULL,
            equipment_sla VARCHAR(50) DEFAULT NULL,
            expected_time_equipment DATETIME DEFAULT NULL,
            estimated_time_delivery DATETIME DEFAULT NULL,
            delivery_gap INT DEFAULT NULL,
            completion_time DATETIME DEFAULT NULL,
            po_create_time DATETIME DEFAULT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (TicketId),
            INDEX idx_project_name (project_name),
            INDEX idx_state (state),
            INDEX idx_device_type (device_type),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """

        # 创建更新日志表
        create_log_table = """
        CREATE TABLE IF NOT EXISTS consolidated_data_update_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(100) NOT NULL,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            record_count INT DEFAULT 0,
            status VARCHAR(50) DEFAULT 'success',
            message TEXT,
            INDEX idx_table_name (table_name),
            INDEX idx_update_time (update_time)
        )
        """

        try:
            connection = self.get_db_connection()
            cursor = connection.cursor()
            
            cursor.execute(create_main_table)
            cursor.execute(create_log_table)
            
            connection.commit()
            cursor.close()
            connection.close()
            
            logger.info("✅ 数据表创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False

    def fetch_base_tickets(self):
        """第1步：获取基础工单数据（最快的查询）"""
        logger.info("📋 第1步：获取基础工单数据...")

        query = """
        SELECT TicketId, Title, SchemeNameCn, IsForceEnd
        FROM all_construction_ticket_data 
        WHERE SchemeNameCn LIKE '%设备生产%'
            AND IsForceEnd = 0
            AND TicketId IS NOT NULL 
            AND TicketId != ''
        ORDER BY TicketId
        LIMIT 2000
        """

        try:
            connection = self.get_db_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            logger.info(f"✅ 获取基础工单: {len(results)} 条")
            return results
        except Exception as e:
            logger.error(f"❌ 获取基础工单失败: {e}")
            return []

    def fetch_equipment_process_data(self, ticket_ids):
        """第2步：获取设备生产流程数据"""
        logger.info("🔧 第2步：获取设备生产流程数据...")

        if not ticket_ids:
            return {}

        # 分批查询，避免IN子句过长
        batch_size = 100
        all_results = {}

        connection = self.get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        for i in range(0, len(ticket_ids), batch_size):
            batch_ids = ticket_ids[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_ids))

            query = f"""
            SELECT ticket_id, project_name, device_name, equipment_type
            FROM equipment_production_racking_process 
            WHERE ticket_id IN ({placeholders})
                AND equipment_type = '甲供'
            """

            try:
                cursor.execute(query, batch_ids)
                results = cursor.fetchall()
                for row in results:
                    all_results[row['ticket_id']] = row
            except Exception as e:
                logger.warning(f"批次查询失败: {e}")
                continue

        cursor.close()
        connection.close()

        logger.info(f"✅ 获取设备流程数据: {len(all_results)} 条")
        return all_results

def main():
    """主函数"""
    logger.info("🚀 启动独立的数据合并程序")

    consolidator = StandaloneDataConsolidator()

    # 创建表
    if not consolidator.create_tables():
        return False

    # 测试基础查询
    base_tickets = consolidator.fetch_base_tickets()
    if base_tickets:
        logger.info(f"✅ 成功获取 {len(base_tickets)} 条基础工单数据")
        
        # 显示前几条数据作为示例
        for i, ticket in enumerate(base_tickets[:3]):
            logger.info(f"示例数据 {i+1}: {ticket}")
    else:
        logger.error("❌ 未获取到基础工单数据")
        return False

    logger.info("🎉 独立数据合并测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
