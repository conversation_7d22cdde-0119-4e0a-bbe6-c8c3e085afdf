import json
from datetime import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import (WorkflowDetailService)
from iBroker.lib import mysql

from biz.construction_preparation_architecture_input.file_tools import FileTools

class ConstructionPreparationArchitectureInput(AjaxTodoBase):
    # 市电专项 分项计划 上传
    def __init__(self):
        super(ConstructionPreparationArchitectureInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 项目技术规格版本
        project_technical_specification_version = process_data.get("project_technical_specification_version", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(project_technical_specification_version, "项目技术规格版本")
        if not file_exit_flag:
            return file_exit_msg
        project_technical_specification_version_file= FileTools.get_file_json(project_technical_specification_version, process_user)
        # 设备配置清单
        equipment_configuration_list = process_data.get("equipment_configuration_list", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(equipment_configuration_list, "设备配置清单")
        if not file_exit_flag:
            return file_exit_msg
        equipment_configuration_list_file= FileTools.get_file_json(equipment_configuration_list, process_user)
        # 设计图纸
        design_drawings = process_data.get("design_drawings", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(design_drawings, "设计图纸")
        if not file_exit_flag:
            return file_exit_msg
        design_drawings_file= FileTools.get_file_json(design_drawings, process_user)
        # 设备材料清单
        equipment_material_list = process_data.get("equipment_material_list", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(equipment_material_list, "设备材料清单")
        if not file_exit_flag:
            return file_exit_msg
        equipment_material_list_file= FileTools.get_file_json(equipment_material_list, process_user)
        # 设备信息
        device_information = process_data.get("device_information", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(device_information, "设备信息")
        if not file_exit_flag:
            return file_exit_msg
        device_information_file= FileTools.get_file_json(device_information, process_user)
        # 设计变更图纸、清单
        design_change_drawings_lists = process_data.get("design_change_drawings_lists", [])
        file_exit_msg, file_exit_flag = FileTools.get_multiple_files_exit_flag(design_change_drawings_lists, "设计变更图纸、清单")
        if not file_exit_flag:
            return file_exit_msg
        design_change_drawings_lists_file= FileTools.get_file_json(design_change_drawings_lists, process_user)
       
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "project_technical_specification_version": project_technical_specification_version,
            "equipment_configuration_list": equipment_configuration_list,
            "design_drawings": design_drawings,
            "equipment_material_list": equipment_material_list,
            "device_information": device_information,
            "design_change_drawings_lists": design_change_drawings_lists,
            "project_technical_specification_version_file": project_technical_specification_version_file,
            "equipment_configuration_list_file": equipment_configuration_list_file,
            "design_drawings_file": design_drawings_file,
            "equipment_material_list_file": equipment_material_list_file,
            "device_information_file": device_information_file,
            "design_change_drawings_lists_file": design_change_drawings_lists_file,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)

# 施工准备阶段-架构输入 数据入库类
class ArchitectureInputIntoDB(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def architecture_input_into_db(self, project_technical_specification_version_file, equipment_configuration_list_file, design_drawings_file, equipment_material_list_file, device_information_file, design_change_drawings_lists_file):
        """
        施工准备阶段-架构输入数据入库
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        project_number = self.ctx.variables.get("project_number")
        insert_data = []
        insert_data.append({
            "ticket_id": ticket_id,
            "project_name": project_name,
            "project_number": project_number,
            "project_technical_specification_version_file": json.dumps(project_technical_specification_version_file, ensure_ascii=False),
            "equipment_configuration_list_file": json.dumps(equipment_configuration_list_file, ensure_ascii=False),
            "design_drawings_file": json.dumps(design_drawings_file, ensure_ascii=False),
            "equipment_material_list_file": json.dumps(equipment_material_list_file, ensure_ascii=False),
            "device_information_file": json.dumps(device_information_file, ensure_ascii=False),
            "design_change_drawings_lists_file": json.dumps(design_change_drawings_lists_file, ensure_ascii=False),
            "create_time": datetime.today().strftime("%Y-%m-%d %H:%M:%S")
        })
        if insert_data:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_preparation_architecture_input", insert_data)
            tb_db.commit()
        variables = {
            "insert_data": insert_data
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
