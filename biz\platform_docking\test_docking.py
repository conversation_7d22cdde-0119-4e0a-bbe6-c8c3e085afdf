# -*- coding: utf-8 -*-
# @author: keketan
# @software: PyCharm
# @file: test_docking.py
# @time: 2024/4/10 11:25
# @描述: 【请添加描述】

from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowContinue
from iBroker.sdk.workflow.workflow import WorkflowVarUpdate
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib.sdk import tof
from iBroker.lib.sdk import idcdb
from iBroker.lib.sdk.soc_lib import get_idc_engineer
from iBroker.lib import exception
from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class TestDocking(object):
    """
    与测试平台对接
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def open_test_order(self):
        """
            测试主流程建单
        """
        data = {
                "MozuName": "南京江宁模组23",
                "TencentProjectManager": "keketan"
        }
        gnetops.request(action="CreateVerification", method="CreateOrder", data=data)

    def query_question_data(self):
        """
            通用查询问题整改流程信息
        """
        data = {
                "MozuName": "南京江宁模组23",
                "TencentProjectManager": "keketan"
        }
        gnetops.request(action="CreateVerification", method="CreateOrder", data=data)