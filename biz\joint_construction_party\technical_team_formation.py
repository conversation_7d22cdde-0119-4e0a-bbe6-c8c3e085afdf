import datetime
import json
import re
import time

from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate
from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops

from biz.common.cycle_task_tools import ticket_is_over
from biz.common.workflow_tools import cycle_task_abort


class TeamFormationBasicInformation(AjaxTodoBase):
    """
        技术小组组建：填写基本信息
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name')
        project_desc = process_data.get('project_desc')
        ticket_id = self.ctx.variables.get('ticket_id')
        source_of_project = process_data.get('source_of_project')
        db = mysql.new_mysql_instance("tbconstruct")
        technical_team_formation = json.dumps({"ticket_id": [ticket_id]})
        insertdata = {"project_name": project_name,
                      "project_desc": project_desc,
                      "technical_team_formation": technical_team_formation,
                      "source_of_project": source_of_project,
                      }
        db.insert("procurement_process_information", insertdata)
        variables = {
            'project_desc': project_desc,
            'project_name': project_name,
            'source_of_project': source_of_project,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationBasicInformationFaq(AjaxTodoBase):
    """
        发标澄清流程：基本信息
    """
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name')
        project_desc = process_data.get('project_desc')
        variables = {
            'project_desc': project_desc,
            'project_name': project_name
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationDesignatedLeader(AjaxTodoBase):
    """
        技术小组组建：指定组长
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get('project_name')
        project_desc = self.ctx.variables.get('project_desc')
        source_of_project = process_data.get('source_of_project')
        # 组长
        leader = process_data.get('leader')
        basic_info = {
            'project_desc': project_desc,
            'project_name': project_name,
            'leader': leader,
            'source_of_project': source_of_project,
        }
        variables = {
            'project_desc': project_desc,
            'project_name': project_name,
            'leader': leader,
            'basic_info': basic_info
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationDesignatedTeam(AjaxTodoBase):
    """
        技术小组组建：指定团队
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 指定团队
        team_info = process_data.get('team_info')
        if not team_info:
            return {"code": -1, "msg": "请选择团队"}
        variables = {
            'team_info': team_info
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class NotifyMembers(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def send_email(self, all_person_list, qw_data, title, email_data):
        # 企微
        for i in all_person_list:
            ChatOpsSend(
                user_name=i,
                msg_content=qw_data,
                msg_type='text',
                des_type='single'
            ).call()
        email_list = []
        for i in all_person_list:
            i += "@tencent.com"
            email_list.append(i)

        # 邮件
        tof.send_email(sendTitle=title, msgContent=email_data, sendTo=email_list)
        # 企微
        tof.send_company_wechat_message(receivers=all_person_list, title=title, message=email_data)
        return 1

    def team_formation_notify_members(self, project_name, members_list, leader):
        """
        技术小组组建成功通知小组成员
        """
        new_project_name = project_name.replace("技术审批", "")
        qw_data = f"您已成为【{new_project_name}】技术小组成员，请多关注此类信息！"
        title = f"{new_project_name}技术小组组建"
        email_data = f"【项目名称】:{new_project_name}<br>"
        email_data += f"【内容】:您已成为【{new_project_name}】技术小组成员，请多关注此类信息！<br>"
        self.send_email(members_list, qw_data, title, email_data)
        # 通知组长小组成员有谁
        qw_data = f"【小组成员】:\n"
        for i in members_list:
            qw_data += f"{i}\n"
        title = f"{new_project_name}技术小组成员组成"
        email_data = f"【项目名称】:{new_project_name}<br>"
        email_data += f"【内容】:您是【{new_project_name}】技术小组的组长，请查收组员组成！<br>"
        email_data += f"【小组成员】: <br>"
        for i in members_list:
            email_data += f"{i}<br>"
        self.send_email([leader], qw_data, title, email_data)

        flow.complete_task(self.ctx.task_id)

    def view_clarification_document(self, project_name, all_person_list, question_answer_document_table):
        """
        通知成员：查看澄清文件
        """
        new_project_name = project_name.replace("技术审批", "")
        title = f"{new_project_name}澄清文件"
        email_data = f"【项目名称】:{new_project_name}<br>"
        email_data += f"【内容】:{new_project_name}澄清文件已上传，请下载查看！<br>"
        qw_data = f"【项目名称】:{new_project_name}\n"
        qw_data += f"【内容】:{new_project_name}澄清文件已上传，请下载查看！\n"
        for i in question_answer_document_table:
            qw_data += f"【{i.get('question_answer_document_name')}】:{i.get('question_answer_document')}\n  "
            email_data += f"【{i.get('question_answer_document_name')}】:{i.get('question_answer_document')}<br>"
        self.send_email(all_person_list, qw_data, title, email_data)
        flow.complete_task(self.ctx.task_id)

    def plan_push(self, project_name, all_person_list, tender_issuance_plan, faq_feedback_plan, faq_reply_plan,
                  bid_return_plan, tender_opening_plan):
        new_project_name = project_name.replace("技术审批", "")
        title = f"{new_project_name}计划时间"
        email_data = f"【项目名称】:{new_project_name}<br>"
        email_data += f"【计划时间】:<br>"
        email_data += f"发标时间：{tender_issuance_plan}<br>"
        email_data += f"澄清答疑反馈时间：{faq_feedback_plan}<br>"
        email_data += f"澄清答疑回复时间：{faq_reply_plan}<br>"
        email_data += f"回标时间：{bid_return_plan}<br>"
        email_data += f"开标时间：{tender_opening_plan}<br>"
        qw_data = f"【项目名称】:{new_project_name}\n"
        qw_data += f"【计划时间】:\n"
        qw_data += f"发标时间：{tender_issuance_plan}\n"
        qw_data += f"澄清答疑反馈时间：{faq_feedback_plan}\n"
        qw_data += f"澄清答疑回复时间：{faq_reply_plan}\n"
        qw_data += f"回标时间：{bid_return_plan}\n"
        qw_data += f"开标时间：{tender_opening_plan}\n"
        self.send_email(all_person_list, qw_data, title, email_data)
        flow.complete_task(self.ctx.task_id)


class TeamFormationAutomaticTasks(object):
    """
        技术小组组建：自动任务
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def wait_members_designation(self):
        res_list = self.ctx.variables.get('res_list')
        cycle_order_list = self.ctx.variables.get('cycle_order_list')
        flag = ticket_is_over(cycle_order_list)
        if flag:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            flow.complete_task(self.ctx.task_id)
            return {"success": True, "data": "流程已结束"}

    def cycle_order_creation(self, team_info, basic_info, project_name):
        """
         技术小组组建：循环建单（指定团队成员工单）
        """
        res_list = []
        cycle_order_list = []
        director_approval = self.ctx.variables.get('director_approval', '同意')
        director_rejection = self.ctx.variables.get('director_rejection', '')
        gm_approval = self.ctx.variables.get('gm_approval', '同意')
        gm_rejection = self.ctx.variables.get('gm_rejection', '')
        member_dict = self.ctx.variables.get('member_dict')
        for i in team_info:
            team = i.get('team')
            team_member_list = member_dict.get(team).get('member_list')
            team_member_options = []
            for item in team_member_list:
                team_member_options.append({"label": item, "value": re.sub(r'\(.*?\)', '', item)})
            team_num = i.get('team_num')
            if int(team_num) > 0:
                basic_info['team_leader'] = i.get('team_leader')
                basic_info['team_num'] = i.get('team_num')
                basic_info['team'] = i.get('team')
                basic_info['f_instance_id'] = self.ctx.instance_id
                basic_info['director_approval'] = director_approval
                basic_info['director_rejection'] = director_rejection
                basic_info['gm_approval'] = gm_approval
                basic_info['gm_rejection'] = gm_rejection
                basic_info['team_member_options'] = team_member_options
                data = {
                    "CustomVariables": basic_info,
                    "ProcessDefinitionKey": "designated_technical_team_members",
                    "Source": "",
                    "TicketDescription": f"{project_name}:指定技术小组团队成员",
                    "TicketLevel": "3",
                    "TicketTitle": f"{project_name}:小组成员确定",
                    "UserInfo": {
                        "Concern": "youngshi",
                        "Creator": "youngshi",
                        "Deal": i.get('team_leader')
                    }
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                cycle_order_list.append(res.get('TicketId'))
        ticket_id = self.ctx.variables.get('ticket_id')

        # 门户页面库数据添加
        db = mysql.new_mysql_instance("tbconstruct")
        technical_team_formation = json.dumps({"ticket_id": [ticket_id], "children_ticket_id": cycle_order_list})
        update_data = {"technical_team_formation": technical_team_formation}
        conditions = {"project_name": project_name}
        db.begin()
        db.update("procurement_process_information", conditions=conditions, data=update_data)
        db.commit()

        variables = {
            "cycle_order_list": cycle_order_list,
            "res_list": res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationDesignatedTeamMembers(AjaxTodoBase):
    """
        技术小组组建：指定团队成员
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        team_members = process_data.get('team_members')
        # 冲流程变量获取数据
        team_leader = self.ctx.variables.get('team_leader')
        team = self.ctx.variables.get('team')
        f_instance_id = self.ctx.variables.get("f_instance_id")
        team_num = self.ctx.variables.get("team_num")
        main_flow_vars = flow.get_variables(f_instance_id,
                                            ["team_info"])
        self.workflow_detail.add_kv_table('主流程数据', {'variables_map': main_flow_vars})
        team_info = main_flow_vars.get("team_info", [])
        team_members_list = []
        team_members_str = ''
        if isinstance(team_members, list):
            team_members_list = team_members
            team_members_str = ';'.join(team_members_list)
        if isinstance(team_members, str):
            team_members_list = [team_members]
            team_members_str = team_members
        if len(team_members_list) > int(team_num):
            return {"code": -1, "msg": "评标人数不能超过需求人数"}
        if team_info:
            for i in team_info:
                if team_leader == i.get('team_leader') and team == i.get('team'):
                    i['team_members'] = team_members_str
        f_variables = {
            'team_info': team_info
        }
        # 将流程变量更新至父流程
        WorkflowVarUpdate(instance_id=f_instance_id, variables=f_variables).call()
        variables = {
            'team_members': team_members,
            'team_info': team_info
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationMemberApprovalDirector(AjaxTodoBase):
    """
        技术小组组建：团队成员审批（总监）
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        director_approval = process_data.get('director_approval')
        director_rejection = process_data.get('director_rejection')
        director_remark = process_data.get('director_remark')
        if director_approval == '驳回':
            if director_rejection:
                variables = {
                    'director_approval': director_approval,
                    'director_rejection': director_rejection,
                    "director_remark": director_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'director_approval': director_approval,
                "director_remark": director_remark
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationMemberApprovalGM(AjaxTodoBase):
    """
        技术小组组建：团队成员源审批（GM）
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        gm_approval = process_data.get('gm_approval')
        gm_rejection = process_data.get('gm_rejection')
        gm_remark = process_data.get('gm_remark')
        team_info = self.ctx.variables.get('team_info')
        leader = self.ctx.variables.get('leader')
        project_name = self.ctx.variables.get('project_name')
        ticket_id = self.ctx.variables.get('ticket_id')
        insert_list = []
        # 团队领导
        leader_list = []
        # 团队成员
        members_list = []
        # 所有人
        all_person = [leader]
        if gm_approval == '驳回':
            if gm_rejection:
                variables = {
                    'gm_approval': gm_approval,
                    'gm_rejection': gm_rejection,
                    "gm_remark": gm_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            for i in team_info:
                team_leader = i.get('team_leader', '')
                team = i.get('team', '')
                members = i.get('team_members', '')
                team_num = i.get('team_num', '')
                list1 = team_leader.split(";")
                list2 = members.split(";")
                leader_list.extend(list1)
                members_list.extend(list2)
                insert_list.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'leader': leader,
                    'team_leader': team_leader,
                    'team_members': members,
                    'team_num': team_num,
                    'team': team
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("technical_team_formation", insert_list)
            tb_db.commit()
            all_person.extend(leader_list)
            all_person.extend(members_list)
            all_person_list = list(set(all_person))
            variables = {
                'gm_approval': gm_approval,
                "gm_remark": gm_remark,
                'leader_list': leader_list,
                "members_list": members_list,
                "insert_list": insert_list,
                "all_person_list": all_person_list
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TeamFormationMemberApprovalCenterLeader(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        center_approval = process_data.get('center_approval')
        center_rejection = process_data.get('center_rejection')
        center_remark = process_data.get('center_remark')
        if center_approval == '驳回':
            if center_rejection:
                variables = {
                    'center_approval': center_approval,
                    'center_rejection': center_rejection,
                    "center_remark": center_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            if process_user == "sagezou":
                is_noop = 1  # 判断中心负责人是否为GM
            else:
                is_noop = 0
            if is_noop:
                gm_approval = process_data.get('center_approval')
                gm_remark = process_data.get('center_remark')
                team_info = self.ctx.variables.get('team_info')
                leader = self.ctx.variables.get('leader')
                project_name = self.ctx.variables.get('project_name')
                ticket_id = self.ctx.variables.get('ticket_id')
                insert_list = []
                # 团队领导
                leader_list = []
                # 团队成员
                members_list = []
                # 所有人
                all_person = [leader]
                for i in team_info:
                    team_leader = i.get('team_leader', '')
                    team = i.get('team', '')
                    members = i.get('team_members', '')
                    team_num = i.get('team_num', '')
                    list1 = team_leader.split(";")
                    list2 = members.split(";")
                    leader_list.extend(list1)
                    members_list.extend(list2)
                    insert_list.append({
                        'ticket_id': ticket_id,
                        'project_name': project_name,
                        'leader': leader,
                        'team_leader': team_leader,
                        'team_members': members,
                        'team_num': team_num,
                        'team': team
                    })
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("technical_team_formation", insert_list)
                tb_db.commit()
                all_person.extend(leader_list)
                all_person.extend(members_list)
                all_person_list = list(set(all_person))
                variables = {
                    'center_approval': center_approval,
                    "center_remark": center_remark,
                    'gm_approval': gm_approval,
                    "gm_remark": gm_remark,
                    'leader_list': leader_list,
                    "members_list": members_list,
                    "insert_list": insert_list,
                    "all_person_list": all_person_list,
                    'is_noop': is_noop
                }
            else:
                variables = {
                    'center_approval': center_approval,
                    "center_remark": center_remark,
                    'is_noop': is_noop
                }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
