from iBroker.lib.sdk import tof, gnetops
import logging

from iBroker.sdk.notification.chatops import ChatOpsSend


class ConstructionWorkOrder(object):
    """
        建设工单
    """

    def __init__(self):
        self.logger = logging.getLogger('ConstructionWorkOrder')
        self.logger.setLevel(logging.DEBUG)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

    def construction_work_order_prompt(self, work_order_number, work_order_heading, process_name, handler):
        """
            建设工单——工单催办
        """
        info = gnetops.request(action="TaskCenter", method="GetRuntimeTaskByTicketId",
                               ext_data={"TicketId": work_order_number})

        # 将处理人员字符串拆分为列表
        personnel_list = handler.split(';')
        # 添加后缀
        email_list = [email + "@tencent.com" for email in personnel_list]

        # 为每个用户创建一个字典来存储他们的链接
        user_links = {user: [] for user in personnel_list}

        try:
            for row in info:
                idcUrl = row.get("idcUrl")
                oaUrl = row.get("oaUrl")
                processUsers = row.get("processUsers")

                # 检查 personnel_list 中的每个用户是否在 processUsers 中
                for user in personnel_list:
                    if user in processUsers:
                        # 如果匹配，添加链接到用户的列表
                        user_links[user].append((oaUrl, idcUrl))

            # 为每个用户生成消息并发送
            for user in personnel_list:
                if user in user_links and user_links[user]:
                    # 用户在 processUsers 中，发送相关链接
                    for oaUrl, idcUrl in user_links[user]:
                        text = f'【流程名称】：{process_name}\n' \
                               f'【任务名称】：{work_order_heading}\n' \
                               f'【待办详情内网链接】：{oaUrl}\n' \
                               f'【待办详情外网链接】：{idcUrl}\n' \
                               '（注：如内网链接无权限访问，请点击外网链接查看待办）'
                        rich_text_template = f'【流程名称】：{process_name}<br>' \
                                             f'【任务名称】：{work_order_heading}<br>' \
                                             f'【待办详情内网链接】：{oaUrl}<br>' \
                                             f'【待办详情外网链接】：{idcUrl}<br>' \
                                             '（注：如内网链接无权限访问，请点击外网链接查看待办）'

                        # 发送邮件
                        tof.send_email(sendTitle=work_order_heading, msgContent=rich_text_template,
                                       sendTo=[user + "@tencent.com"])

                        # 发送聊天消息
                        ChatOpsSend(
                            user_name=user,
                            msg_content=text,
                            msg_type='text',
                            des_type='single'
                        ).call()
                else:
                    # 用户不在 processUsers 中，发送默认链接
                    text = f'【流程名称】：{process_name}\n' \
                           f'【任务名称】：{work_order_heading}\n' \
                           '【待办详情内网链接】：https://dcops.woa.com/operateManage/business/' \
                           f'ToDoDetails?params={work_order_number}\n' \
                           '【待办详情外网链接】：https://dcops.idc.tencent.com/operateManage/business/' \
                           f'details?params={work_order_number}\n' \
                           '（注：如内网链接无权限访问，请点击外网链接查看待办）'
                    rich_text_template = f'【流程名称】：{process_name}<br>' \
                                         f'【任务名称】：{work_order_heading}<br>' \
                                         '【待办详情内网链接】：https://dcops.woa.com/operateManage/business/' \
                                         f'ToDoDetails?params={work_order_number}<br>' \
                                         '【待办详情外网链接】：https://dcops.idc.tencent.com/operateManage/business/' \
                                         f'details?params={work_order_number}<br>' \
                                         '（注：如内网链接无权限访问，请点击外网链接查看待办）'

                    # 发送邮件
                    tof.send_email(sendTitle=work_order_heading, msgContent=rich_text_template,
                                   sendTo=[user + "@tencent.com"])

                    # 发送聊天消息
                    ChatOpsSend(
                        user_name=user,
                        msg_content=text,
                        msg_type='text',
                        des_type='single'
                    ).call()

            response = {
                'code': 200,
                'msg': '催办成功'
            }
        except ValueError as ve:
            response = {
                'code': 500,
                'msg': f'催办失败：{ve}'
            }
        except TypeError as te:
            response = {
                'code': 500,
                'msg': f'催办失败：{te}'
            }
        return response
