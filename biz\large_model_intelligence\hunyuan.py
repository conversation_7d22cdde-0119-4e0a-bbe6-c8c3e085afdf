
from iBroker.lib.sdk import gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

class HunYuan(object):
    """
    混元
    """

    def __init__(self):
        super(HunYuan).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def vincent(self, message):
        """
        文生文
        """
        info = gnetops.request(
            action="HunYuan",
            method="QtextA",
            ext_data={
                "message": message,
            },
        )
        return info

    def figure(self, message, picture):
        """
        图生文
        """
        info = gnetops.request(
            action="HunYuan",
            method="QpictureA",
            ext_data={
                "message": message,
                "picturePath": picture
            },
        )
        return info