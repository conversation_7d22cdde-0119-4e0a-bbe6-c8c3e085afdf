import uuid
import datetime

import lunardate
import numpy as np
import pandas as pd
import requests
import re
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql
from openpyxl import load_workbook


class AnalyzeMasterControlPlan(object):
    """
        解析总控计划
    """

    def __init__(self):
        super(AnalyzeMasterControlPlan, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def master_control_plan_data_repository(self, project_name, progress, ticket_id):
        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        if progress:
            url = progress[0]["response"]["FileList"][0]["url"]
            relative_url = url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
            """
                excel解析总控计划
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl',sheet_name='总控计划模版')
            df = df.replace({np.nan: None})
            """
                所有数据存库
            """
            insert_data = []
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                plan_duration_construction = row['工期']
                start_time = row['计划开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划开始时间（年/月/日）']) else None
                completion_time = row['计划完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                insert_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'serial_number': serial_number,
                    'work_content': work_content,
                    'plan_duration_construction': plan_duration_construction,
                    'start_time': start_time,
                    'completion_time': completion_time,
                    'responsible_person': responsible_person,
                    'output_object': output_object,
                    'input_object': input_object,
                    'construction_attribute': construction_attribute,
                    'now_time': now_time
                })
            try:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("excel_data", insert_data)
                tb_db.commit()
                response = {
                    'code': 200,
                    'msg': '写入成功'
                }
            except ValueError as ve:
                response = {
                    'code': 500,
                    'msg': f'写入失败：{ve}'
                }

            return response
