#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/04/13 18:48:07
# <AUTHOR> early
# @Site    :
# @File    : tbox_event_flow
from multiprocessing import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from noc_robot.message.send_message import send_text_message


class TboxTodo(AjaxTodoBase):
    """
    确认相关逻辑
    """
    PROTOCOL_KEY = "broker.protocol.TboxTodo"

    def __init__(self):
        super(TboxTodo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        next_op = process_data.get("next_op", None)

        self.workflow_detail.add_kv_table(
            name="用户选择内容",
            kv_table={
                "选择": next_op
            }
        )
        url = "https://in.qyapi.weixin.qq.com/cgi-bin/webhook/send?key=d9891ec3-d3f7-4362-b03b-0bd9a7b97b12"
        alarm_str = "end接收到的数据信息：process_data：" + str(process_data) + ",process_user:" + str(
            process_user)
        send_text_message(url=url, context=alarm_str)

        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables={"next_op": next_op})
