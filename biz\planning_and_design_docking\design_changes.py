from iBroker.lib.sdk import tof
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


def check_report(complete_report):
    try:
        url, result = complete_report["response"]["FileList"][0]["url"], False
    except KeyError:
        url, result = {"code": -1, "msg": "未上传变更设计文件"}, True
    except IndexError:
        url, result = {"code": -1, "msg": "未上传变更设计文件"}, True
    return url, result


def url_splicing(data):
    url_list = []
    if data:
        for i in data:
            url, result = check_report(i)
            if result:
                return 0, 0
            if len(i) > 0 and 'response' in i:
                url_list.append(url)
        url_str = ";".join(url_list)
        return url_list, url_str
    else:
        return 0, 0


class OnSiteProblemFeedback(AjaxTodoBase):
    """
        现场问题反馈
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = process_data.get("project_name")
        major = process_data.get("major")
        reason_and_content = process_data.get("reason_and_content")
        supervision_opinions = process_data.get("supervision_opinions")
        project_management_opinions = process_data.get("project_management_opinions")
        supervision_review = process_data.get("supervision_review")
        project_management_review = process_data.get("project_management_review")
        supervision_review_list, supervision_review_str = url_splicing(supervision_review)
        if supervision_review_list == 0 and supervision_review_str == 0:
            return {"code": -1, "msg": "未上监理审核附件"}
        project_management_review_list, project_management_review_str = url_splicing(project_management_review)
        if project_management_review_list == 0 and project_management_review_str == 0:
            return {"code": -1, "msg": "未上传项管审核附件"}

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT item_tube,supervision_and_management,PM FROM project_team_information_data " \
              " WHERE project_name='%s'" % project_name
        # 项管、监理经理人
        chief_interface_list = db.get_all(sql)
        if chief_interface_list:
            # 项管
            item_tube = chief_interface_list[0].get('item_tube')
            # 监理
            supervision_and_management = chief_interface_list[0].get('supervision_and_management')
            # PM
            PM = chief_interface_list[0].get('PM')
        else:
            item_tube = ''
            supervision_and_management = ''
            PM = ''

        insert_data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "major": major,
            "reason_and_content": reason_and_content,
            "supervision_opinions": supervision_opinions,
            "project_management_opinions": project_management_opinions,
            "supervision_review": supervision_review_str,
            "project_management_review": project_management_review_str
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("deepen_design_change", insert_data)
        tb_db.commit()

        # 存流程变量
        variables = {
            "project_name": project_name,
            "major": major,
            "reason_and_content": reason_and_content,
            "supervision_opinions": supervision_opinions,
            "project_management_opinions": project_management_opinions,
            "supervision_review": supervision_review,
            "project_management_review": project_management_review,
            "supervision_review_str": supervision_review_str,
            "project_management_review_str": project_management_review_str,
            "item_tube": item_tube,
            "supervision_and_management": supervision_and_management,
            "PM": PM,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProblemFeedbackReview(AjaxTodoBase):
    """
        问题反馈审核
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        review = process_data.get("review")
        rejection_input = process_data.get("rejection_input")
        if review == '驳回':
            if rejection_input:
                variables = {
                    'review': review,
                    'rejection_input': rejection_input,
                    'rejection': rejection_input
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'review': review,
            }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ChangesDesignDrawings(AjaxTodoBase):
    """
        设计图纸变更
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        major = self.ctx.variables.get("major")

        change_list = process_data.get("change_list")
        update_drawing = process_data.get("update_drawing")
        judge_meeting = process_data.get("judge_meeting")
        submitter = process_data.get("submitter")
        time = process_data.get("time")
        # 变更文件详细信息
        # 文件名称
        file_name_list = []
        file_url_list = []
        # 存库：构造
        insert_list = []
        if change_list:
            for i in change_list:
                file_name_list.append(i.get("file_name"))
                before = i.get("before_url")
                before_url_list, before_url_str = url_splicing(before)
                if before_url_list == 0 and before_url_str == 0:
                    return {"code": -1, "msg": "未上传变更前设计文件"}
                after = i.get("after_url")
                after_url_list, after_url_str = url_splicing(after)
                if after_url_list == 0 and after_url_str == 0:
                    return {"code": -1, "msg": "未上传变更后设计文件"}
                file_url_list.append(after_url_str)
                insert_list.append({
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "file_name": i.get("file_name"),
                    "file_no": i.get("file_no"),
                    "adjust_content": i.get("adjust_content"),
                    "before_url": before_url_str,
                    "after_url": after_url_str,
                    'submitter': submitter,
                    "time": time,
                    "major": major
                })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("deepen_design_change_document_details", insert_list)
            tb_db.commit()
        # 更新图纸
        update_drawing_list, update_drawing_str = url_splicing(update_drawing)
        if update_drawing_list == 0 and update_drawing_str == 0:
            return {"code": -1, "msg": "未上传变更图纸"}
        # 审核会纪要
        judge_meeting_list, judge_meeting_str = url_splicing(judge_meeting)
        if judge_meeting_list == 0 and judge_meeting_str == 0:
            return {"code": -1, "msg": "未上传审核文件"}
        # 变更主流程数据
        insert_data = {
            "update_drawing": update_drawing_str,
            "judge_meeting": judge_meeting_str,
            'submitter': submitter,
            "time": time
        }
        con = {"ticket_id": ticket_id}
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("deepen_design_change", insert_data, con)
        tb_db.commit()
        # 推送消息
        chat_data = ''
        chat_data += f"【项目名称】: {project_name}\n"
        chat_data += f"【提交时间】:{time}\n"
        chat_data += ("【变更链接】: https://dcops.woa.com/operateManage/business/ToDoDetails?"
                      f"params={ticket_id}&isHistory=1\n")

        # 存流程变量
        variables = {
            "change_list": change_list,
            "update_drawing": update_drawing,
            "judge_meeting": judge_meeting,
            'time': time,
            "submitter": submitter,
            "insert_list": insert_list,
            "insert_data": insert_data,
            'chat_data': chat_data
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PushMessage(object):
    """
        企业微信消息推送
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def push_message(self):
        # 项管
        item_tube = self.ctx.variables.get("item_tube")
        # 监理
        supervision_and_management = self.ctx.variables.get("supervision_and_management")
        # PM
        PM = self.ctx.variables.get("PM")
        # 建单人
        Creator = self.ctx.variables.get("Creator")
        # 工单标题
        # ProcessTitle = self.ctx.variables.get("ProcessTitle")
        # 项目名称
        project_name = self.ctx.variables.get("project_name")
        # chat_data
        chat_data = self.ctx.variables.get("chat_data")

        receivers_list = [item_tube, supervision_and_management, PM, Creator]
        new_list = [x for x in receivers_list if x is not None and x != '' and x != []]
        title = f"{project_name} 设计变更结束"
        tof.send_company_wechat_message(receivers=new_list, title=title, message=chat_data)
        flow.complete_task(self.ctx.task_id)
