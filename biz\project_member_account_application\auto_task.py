from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.project_member_account_application.basic_field_handle import (
    BasicFieldHandle,
)
from iBroker.lib import config


# 施工准备阶段-报建流程 自动任务类
class AccountApplicationAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query_basic_info(self):
        campus_name_list = BasicFieldHandle.get_basic_info("园区")
        campus_name_options = BasicFieldHandle.get_options_info(campus_name_list)
        project_name_list = BasicFieldHandle.get_basic_info("项目")
        project_name_options = BasicFieldHandle.get_options_info(project_name_list)
        role_list = BasicFieldHandle.get_basic_info("角色")
        role_options = BasicFieldHandle.get_options_info(role_list)
        service_provider_list = BasicFieldHandle.get_basic_info("所属服务商")
        service_provider_options = BasicFieldHandle.get_options_info(
            service_provider_list
        )
        position_list = BasicFieldHandle.get_basic_info("职务")
        position_options = BasicFieldHandle.get_options_info(position_list)
        # 建单人(pm)
        PM = self.ctx.variables.get("Creator")
        # 账号开通负责人
        account_application_handler = config.get_config_map(
            "account_application_handler"
        )
        variables = {
            "PM": PM,
            "account_application_handler": account_application_handler,
            "campus_name_options": campus_name_options,
            "project_name_options": project_name_options,
            "role_options": role_options,
            "service_provider_options": service_provider_options,
            "position_options": position_options,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
