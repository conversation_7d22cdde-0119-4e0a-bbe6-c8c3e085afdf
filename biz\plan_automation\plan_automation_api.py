from iBroker.lib import config
from iBroker.lib.sdk import tof, gnetops
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.lib import mysql
import re


class PlanAutomationApi(object):

    # 工单催办
    @staticmethod
    def ticket_reminder(ticket_id):
        if ticket_id:
            # 获取工单信息
            ticket_list = PlanAutomationApi.get_ticket_info(ticket_id)
            if ticket_list:
                ticket_status = ticket_list[0].get("TicketStatus")
                if ticket_status != "END":
                    title = ticket_list[0].get("Title")
                    SLALevel = ticket_list[0].get("SLALevel")
                    # 获取任务链接
                    url_res = gnetops.request(
                        action="TaskCenter",
                        method="GetRuntimeTaskByTicketId",
                        ext_data={"TicketId": ticket_id, "FilterException": False},
                    )
                    if url_res:
                        for url in url_res:
                            user = url.get("processUsers")
                            user_list = user.split(";")
                            task_name = url.get("taskName")
                            todo_name = url.get("todoName")
                            create_time = url.get("createTime")
                            is_exception = url.get("filterException")
                            oaUrl = url.get("oaUrl")
                            idcUrl = url.get("idcUrl")
                            mobileUrl = url.get("mobileUrl")
                            hxnMobileUrl = url.get("hxnMobileUrl")
                            if is_exception:
                                # 异常待办-只推内网
                                text_oa = (
                                    "收到流程待办任务催办：\n"
                                    f"【工单标题】：{title}\n"
                                    f"【工单号】：{ticket_id}\n"
                                    f"【任务名称】：{task_name}\n"
                                    f"【任务类型】：人工待办任务({todo_name})\n"
                                    f"【紧急级别】：L{SLALevel}\n"
                                    f"【任务开始时间】：{create_time}\n"
                                    f"【任务链接】：[任务链接]({oaUrl})\n"
                                    f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。\n"
                                )
                                # 发送催办消息-OA账号
                                for account in user_list:
                                    ChatOpsSend(
                                        user_name=account,
                                        msg_content=text_oa,
                                        msg_type="markdown",
                                        des_type="single",
                                    ).call()
                                # 发送邮件-OA账号
                                email_list_oa = [
                                    account + "@tencent.com" for account in user_list
                                ]
                                if email_list_oa:
                                    email_title = f"收到流程待办任务催办：{task_name}"
                                    email_content_oa = (
                                        "收到流程待办任务催办：<br>"
                                        f"【工单标题】：{title}<br>"
                                        f"【工单号】：{ticket_id}<br>"
                                        f"【任务名称】：{task_name}<br>"
                                        f"【任务类型】：人工待办任务({todo_name})<br>"
                                        f"【紧急级别】：L{SLALevel}<br>"
                                        f"【任务开始时间】：{create_time}<br>"
                                        f"【任务链接】：<a href='{oaUrl}'>任务链接</a><br>"
                                        f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。<br>"
                                    )
                                    tof.send_email(
                                        sendTitle=email_title,
                                        msgContent=email_content_oa,
                                        sendTo=email_list_oa,
                                    )
                            else:
                                # 正常待办-可能推 内网、idc、犀牛鸟
                                # 账号处理
                                oa_user_list = []
                                hxn_user_list = []
                                idc_user_list = []
                                for u in user_list:
                                    if u.startswith("C_"):
                                        hxn_user_list.append(u)
                                    else:
                                        oa_user_list.append(u)
                                        idc_user_list.append(u)
                                # oa
                                if oa_user_list:
                                    text_oa = (
                                        "收到流程待办任务催办：\n"
                                        f"【工单标题】：{title}\n"
                                        f"【工单号】：{ticket_id}\n"
                                        f"【任务名称】：{task_name}\n"
                                        "【任务类型】：人工待办任务\n"
                                        f"【紧急级别】：L{SLALevel}\n"
                                        f"【任务开始时间】：{create_time}\n"
                                        f"【任务链接】：[任务链接]({oaUrl})\n"
                                        f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。\n"
                                    )
                                    # 发送催办消息-OA账号
                                    for account in oa_user_list:
                                        ChatOpsSend(
                                            user_name=account,
                                            msg_content=text_oa,
                                            msg_type="markdown",
                                            des_type="single",
                                        ).call()
                                    # 发送邮件-OA账号
                                    email_list_oa = [
                                        account + "@tencent.com"
                                        for account in oa_user_list
                                    ]
                                    if email_list_oa:
                                        email_title = (
                                            f"收到流程待办任务催办：{task_name}"
                                        )
                                        email_content_oa = (
                                            "收到流程待办任务催办：<br>"
                                            f"【工单标题】：{title}<br>"
                                            f"【工单号】：{ticket_id}<br>"
                                            f"【任务名称】：{task_name}<br>"
                                            "【任务类型】：人工待办任务<br>"
                                            f"【紧急级别】：L{SLALevel}<br>"
                                            f"【任务开始时间】：{create_time}<br>"
                                            f"【任务链接】：<a href='{oaUrl}'>任务链接</a><br>"
                                            f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。<br>"
                                        )
                                        tof.send_email(
                                            sendTitle=email_title,
                                            msgContent=email_content_oa,
                                            sendTo=email_list_oa,
                                        )
                                # idc
                                if idc_user_list:
                                    text_idc = (
                                        "收到流程待办任务催办：\n"
                                        f"【工单标题】：{title}\n"
                                        f"【工单号】：{ticket_id}\n"
                                        f"【任务名称】：{task_name}\n"
                                        "【任务类型】：人工待办任务\n"
                                        f"【紧急级别】：L{SLALevel}\n"
                                        f"【任务开始时间】：{create_time}\n"
                                        f"【PC端任务链接】：[PC端任务链接]({idcUrl})\n"
                                        f"【移动端任务链接】：[移动端任务链接]({mobileUrl})\n"
                                        f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。\n"
                                    )
                                    # 发送催办消息-IDC科技账号
                                    data = {
                                        "MsgContent": text_idc,
                                        "SendTo": idc_user_list,
                                        "companyName": "",
                                    }
                                    res = gnetops.request(
                                        action="WeWork",
                                        method="SendMarkdownMsg",
                                        data=data,
                                    )
                                    # 发送邮件-IDC科技账号
                                    email_list_idc = [
                                        account + "@obsidian-service.com "
                                        for account in oa_user_list
                                    ]
                                    if email_list_idc:
                                        email_title = (
                                            f"收到流程待办任务催办：{task_name}"
                                        )
                                        email_content_idc = (
                                            "收到流程待办任务催办：<br>"
                                            f"【工单标题】：{title}<br>"
                                            f"【工单号】：{ticket_id}<br>"
                                            f"【任务名称】：{task_name}<br>"
                                            "【任务类型】：人工待办任务"
                                            f"【紧急级别】：L{SLALevel}<br>"
                                            f"【任务开始时间】：{create_time}<br>"
                                            f"【PC端任务链接】：<a href='{idcUrl}'>PC端任务链接</a><br>"
                                            f"【移动端任务链接】：<a href='{mobileUrl}'>移动端任务链接</a><br>"
                                            f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。<br>"
                                        )
                                        tof.send_email(
                                            sendTitle=email_title,
                                            msgContent=email_content_idc,
                                            sendTo=email_list_idc,
                                        )
                                # xnn
                                if hxn_user_list:
                                    text_xnn = (
                                        "收到流程待办任务催办：\n"
                                        f"【工单标题】：{title}\n"
                                        f"【工单号】：{ticket_id}\n"
                                        f"【任务名称】：{task_name}\n"
                                        "【任务类型】：人工待办任务\n"
                                        f"【紧急级别】：L{SLALevel}\n"
                                        f"【任务开始时间】：{create_time}\n"
                                        f"【PC端任务链接】：[PC端任务链接]({idcUrl})\n"
                                        f"【移动端任务链接】：[移动端任务链接]({hxnMobileUrl})\n"
                                        f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。\n"
                                    )
                                    # 发送催办消息-犀牛鸟账号
                                    data = {
                                        "MsgContent": text_xnn,
                                        "SendTo": hxn_user_list,
                                        "companyName": "",
                                    }
                                    res = gnetops.request(
                                        action="WeWork",
                                        method="SendMarkdownMsg",
                                        data=data,
                                    )
                                    # 发送邮件-犀牛鸟账号
                                    # 获取邮箱
                                    email_list_xnn = PlanAutomationApi.get_user_email(
                                        user_list
                                    )
                                    if email_list_xnn:
                                        email_title = (
                                            f"收到流程待办任务催办：{task_name}"
                                        )
                                        email_content_xnn = (
                                            "收到流程待办任务催办：<br>"
                                            f"【工单标题】：{title}<br>"
                                            f"【工单号】：{ticket_id}<br>"
                                            f"【任务名称】：{task_name}<br>"
                                            "【任务类型】：人工待办任务"
                                            f"【紧急级别】：L{SLALevel}<br>"
                                            f"【任务开始时间】：{create_time}<br>"
                                            f"【PC端任务链接】：<a href='{idcUrl}'>PC端任务链接</a><br>"
                                            f"【移动端任务链接】：<a href='{hxnMobileUrl}'>移动端任务链接</a><br>"
                                            f"【催办原因】：由于您是该单的待办处理人，所以收到此消息。请及时关注。<br>"
                                        )
                                        tof.send_email(
                                            sendTitle=email_title,
                                            msgContent=email_content_xnn,
                                            sendTo=email_list_xnn,
                                        )
                        return "催办成功"
                    else:
                        return "催办失败，未查询到待办任务（当前任务节点可能为自动任务），请联系管理员催办"
                else:
                    return "催办失败，工单已完成，无需催办"
            else:
                return "催办失败，未查询到工单"
        else:
            return "催办失败，未获取到工单"

    # 查询工单信息
    @staticmethod
    def get_ticket_info(ticket_id):
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "TicketStatus": "",
                    "Title": "",
                    "SLALevel": "",
                },
                "SearchCondition": {"TicketId": [str(ticket_id)]},
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List") if query_result else []
        return result_list

    # 查询任务信息
    @staticmethod
    def get_task_info(ticket_id=None, todo_id=None):
        query_data = {
            "SchemaId": "task_center_todo_runtime",
            "Data": {
                "ResultColumns": {
                    "Id": "",
                    "ProcessUsers": "",
                    "TaskName": "",
                    "CreateTime": "",
                },
                "SearchCondition": {},
            },
        }
        if ticket_id:
            query_data["Data"]["SearchCondition"]["TicketId"] = ticket_id
        if todo_id:
            query_data["Data"]["SearchCondition"]["Id"] = todo_id
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List") if query_result else []
        return result_list

    # 查询用户邮箱
    @staticmethod
    def get_user_email(user_list):
        account_temp = ", ".join(f"'{u}'" for u in user_list)
        email_sql = (
            "SELECT DISTINCT email FROM project_role_account "
            f"WHERE account in ({account_temp}) "
            "AND del_flag = 0"
        )
        db = mysql.new_mysql_instance("tbconstruct")
        result = db.query(email_sql)
        email_list = []
        if result:
            email_list = [item.get("email") for item in result]
        return email_list
