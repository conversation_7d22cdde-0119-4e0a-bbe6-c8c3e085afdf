-- ========================================
-- 货期管理SQL测试脚本
-- 用于排查dynamic_header和tabular_data_query返回空数据问题
-- ========================================

-- 步骤1: 检查各表基础数据量
-- ========================================
SELECT '1. 检查各表数据量' as step;

SELECT 'all_construction_ticket_data' as table_name, COUNT(*) as total_count 
FROM all_construction_ticket_data;

SELECT 'equipment_production_racking_process' as table_name, COUNT(*) as total_count 
FROM equipment_production_racking_process;

SELECT 'delivery_schedule_management_table' as table_name, COUNT(*) as total_count 
FROM delivery_schedule_management_table;

SELECT 'equipment_arrival_data' as table_name, COUNT(*) as total_count 
FROM equipment_arrival_data;

-- 步骤2: 检查强制结单工单数量
-- ========================================
SELECT '2. 检查强制结单工单' as step;

-- 总工单数
SELECT 'total_tickets' as type, COUNT(*) as count 
FROM all_construction_ticket_data;

-- 强制结单工单数
SELECT 'force_end_tickets' as type, COUNT(*) as count 
FROM all_construction_ticket_data 
WHERE IsForceEnd = '1';

-- 有效工单数（排除强制结单）
SELECT 'valid_tickets' as type, COUNT(*) as count 
FROM all_construction_ticket_data 
WHERE (IsForceEnd IS NULL OR IsForceEnd != '1');

-- 步骤3: 检查表关联情况
-- ========================================
SELECT '3. 检查表关联情况' as step;

-- 工单表与设备生产表关联
SELECT 'actd_eprp_join' as join_type, COUNT(*) as count
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1');

-- 加上交付计划表关联
SELECT 'actd_eprp_dsm_join' as join_type, COUNT(*) as count
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
AND dsm.project_name IS NOT NULL;

-- 步骤4: 检查设备类型匹配问题
-- ========================================
SELECT '4. 检查设备类型匹配' as step;

-- 检查设备生产表中的设备名称
SELECT 'eprp_device_names' as type, device_name, COUNT(*) as count
FROM equipment_production_racking_process 
WHERE device_name IS NOT NULL AND device_name != ''
GROUP BY device_name
ORDER BY count DESC
LIMIT 10;

-- 检查交付计划表中的设备类型
SELECT 'dsm_device_types' as type, device_type, COUNT(*) as count
FROM delivery_schedule_management_table 
WHERE device_type IS NOT NULL AND device_type != ''
GROUP BY device_type
ORDER BY count DESC
LIMIT 10;

-- 检查设备类型匹配情况
SELECT 'device_type_match' as type, COUNT(*) as count
FROM equipment_production_racking_process eprp
INNER JOIN delivery_schedule_management_table dsm 
ON dsm.project_name = eprp.project_name AND dsm.device_type = eprp.device_name;

-- 步骤5: 简化版dynamic_header查询测试
-- ========================================
SELECT '5. 简化版dynamic_header查询' as step;

SELECT DISTINCT
    dsm.project_name,
    dsm.supplier,
    dsm.project_required_delivery_time,
    dsm.device_type,
    eprp.ticket_id as production_work_order
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
AND dsm.project_name IS NOT NULL
ORDER BY dsm.project_name, dsm.supplier
LIMIT 10;

-- 步骤6: 简化版tabular_data_query查询测试
-- ========================================
SELECT '6. 简化版tabular_data_query查询' as step;

SELECT
    dsm.project_name,
    dsm.supplier,
    dsm.device_type,
    dsm.equipment_sla,
    dsm.expected_time_equipment,
    dsm.estimated_time_delivery,
    dsm.delivery_gap,
    eprp.ticket_id as production_work_order
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
AND dsm.project_name IS NOT NULL
ORDER BY dsm.project_name, dsm.device_type
LIMIT 10;

-- 步骤7: 检查项目名称数据
-- ========================================
SELECT '7. 检查项目名称数据' as step;

-- 设备生产表中的项目名称
SELECT 'eprp_projects' as type, project_name, COUNT(*) as count
FROM equipment_production_racking_process 
WHERE project_name IS NOT NULL AND project_name != ''
GROUP BY project_name
ORDER BY count DESC
LIMIT 10;

-- 交付计划表中的项目名称
SELECT 'dsm_projects' as type, project_name, COUNT(*) as count
FROM delivery_schedule_management_table 
WHERE project_name IS NOT NULL AND project_name != ''
GROUP BY project_name
ORDER BY count DESC
LIMIT 10;

-- 步骤8: 检查具体的匹配问题
-- ========================================
SELECT '8. 检查项目名称匹配问题' as step;

-- 找出在设备生产表中但不在交付计划表中的项目
SELECT 'eprp_not_in_dsm' as type, eprp.project_name
FROM equipment_production_racking_process eprp
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
WHERE dsm.project_name IS NULL
AND eprp.project_name IS NOT NULL
GROUP BY eprp.project_name
LIMIT 10;

-- 找出在交付计划表中但不在设备生产表中的项目
SELECT 'dsm_not_in_eprp' as type, dsm.project_name
FROM delivery_schedule_management_table dsm
LEFT JOIN equipment_production_racking_process eprp ON eprp.project_name = dsm.project_name
WHERE eprp.project_name IS NULL
AND dsm.project_name IS NOT NULL
GROUP BY dsm.project_name
LIMIT 10;

-- 步骤9: 最终测试 - 完整查询但限制结果
-- ========================================
SELECT '9. 完整查询测试' as step;

-- dynamic_header完整查询（限制10条）
SELECT DISTINCT
    dsm.project_name,
    dsm.supplier,
    dsm.project_required_delivery_time,
    dsm.device_type,
    dsm.equipment_sla,
    dsm.expected_time_equipment,
    dsm.estimated_time_delivery,
    dsm.delivery_gap,
    eprp.ticket_id as production_work_order
FROM all_construction_ticket_data actd
LEFT JOIN equipment_production_racking_process eprp ON eprp.ticket_id = actd.TicketId
LEFT JOIN delivery_schedule_management_table dsm ON dsm.project_name = eprp.project_name
    AND dsm.device_type = eprp.device_name
WHERE (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
AND dsm.project_name IS NOT NULL
ORDER BY dsm.project_name, dsm.supplier
LIMIT 10;
