import threading
import json
from datetime import datetime
from iBroker.lib import mysql, config
from iBroker.lib.sdk import gnetops, flow
from iBroker.sdk.workflow.workflow import WorkflowContinue, WorkflowVarUpdate

from biz.utils.change_url import (
    transform_url_list,
    test_download_link,
    download_and_upload_file,
)


class Tools(object):
    # 完工确认逻辑处理：校验数据；处理数据格式；获取对应节点信息；获取待办处理人；扭转待办
    @staticmethod
    def completion_confirmation_handle(data):
        # data = {
        #     # 腾讯侧工单号
        #     "TencentTicketId": "",
        #     # 项目名称
        #     "ProjectName": "",
        #     # 子项编号
        #     "NumberName": "",
        #     # 子项任务名称
        #     "TaskName": "",
        #     # 实际开始时间
        #     "StartTime": "",
        #     # 实际完成时间
        #     "FinishTime": "",
        #     # 进度偏移说明
        #     "ScheduleSkewingExplain": "",
        #     # 指定资料文件（多文件）
        #     "Appendix": [""],
        #     # 备注
        #     "Remark": "",
        #     # 子项任务taskid
        #     "SubTaskId": "",
        #     # 子项任务工单号
        #     "subtask_ticket_id": "",
        # }
        project_name = data["ProjectName"]
        serial_number = data["NumberName"]
        work_content = data["TaskName"]
        ticket_id = data["TencentTicketId"]
        # 处理数据格式
        Appendix = data["Appendix"] if data["Appendix"] else []
        appendix = json.loads(transform_url_list(str(Appendix)))
        # 获取对应节点信息
        node_info = {
            "serial_number": serial_number,
            "work_content": work_content,
            "start_time": "",
            "finish_time": "",
            "actual_start_time": data["StartTime"],
            "actual_finish_time": data["FinishTime"],
            "schedule_skewing_explain": data["ScheduleSkewingExplain"],
            "remark": data["Remark"],
            "appendix": appendix,
        }
        plan = Tools.get_construction_plan(project_name, serial_number, work_content)
        if plan:
            node_info["start_time"] = plan["start_time"]
            node_info["finish_time"] = plan["completion_time"]
        node_info["construction_time"] = Tools.days_between_dates(
            node_info["start_time"], node_info["finish_time"]
        )
        node_info["actual_construction_time"] = Tools.days_between_dates(
            node_info["actual_start_time"], node_info["actual_finish_time"]
        )
        node_info["construction_time_deviation"] = (
            (node_info["actual_construction_time"] - node_info["construction_time"])
            if node_info["actual_construction_time"] is not None
            and node_info["construction_time"] is not None
            else None
        )
        node_info["schedule_deviation"] = Tools.days_between_dates(
            node_info["finish_time"], node_info["actual_finish_time"]
        )
        task_id = data.get("SubTaskId")
        variables_map = {
            "work_list": [node_info],
        }
        WorkflowContinue(task_id, variables_map).call()
        # 异步处理文件
        subtask_ticket_id = data["subtask_ticket_id"]
        threading.Thread(
            target=Tools.async_files_handle,
            args=(
                Appendix,
                Tools.update_ticket_files,
            ),
            kwargs={
                "callback_kwargs": {"ticket_id": subtask_ticket_id},
                "transform": True,
            },
        ).start()

    # 附件处理完后的回调函数
    def update_ticket_files(new_files_list, handle_info_list, ticket_id):
        """
        更新工单里的附件
        """
        instance_id = Tools.get_instance_id(ticket_id)
        files_list = new_files_list
        flow_vars = flow.get_variables(
            instance_id,
            ["work_list"],
        )
        work_list = flow_vars.get("work_list")
        if work_list:
            work_list[0]["appendix"] = files_list
        WorkflowVarUpdate(
            instance_id=instance_id,
            variables={
                "work_list": work_list,
                "handle_info_list": handle_info_list,
            },
        ).call()

    def file_handle(url):
        """
        文件处理
        """
        file_domain = config.get_config_map("dcops_file_domain_name")
        flag, msg = test_download_link(url)
        if flag:
            cos_flag = False
            for domain in file_domain:
                if url.startswith(domain):
                    cos_flag = True
                    break
            if cos_flag:
                return url, f"域名匹配，无需处理：{msg}"
            new_url = download_and_upload_file(url)
            return new_url, f"处理成功：{new_url}"
        else:
            return url, f"链接不可用：{msg}"

    # 异步附件处理
    def async_files_handle(files_list, callback, callback_kwargs, transform=False):
        """
        将合作伙伴平台附件转成cos附件
        :param
            files_list: list 附件链接
            callback: 回调函数（附件处理完后的回调函数）
                :param
                    new_files_list: list 处理后的附件链接
                    handle_info_list: list 处理详情
                    callback_args: dict 回调额外参数
            callback_args: dict 回调额外参数
        """
        new_files_list = []
        handle_info_list = []
        lock = threading.Lock()

        def worker(idx, url):
            new_url, msg = Tools.file_handle(url)
            with lock:
                new_files_list.append(new_url)
                handle_info_list.append(f"第{idx + 1}个文件处理信息：【{msg}】")

        threads = []
        for i in range(len(files_list)):
            url = files_list[i]
            t = threading.Thread(target=worker, args=(i, url))
            t.start()
            threads.append(t)
        for t in threads:
            t.join()
        try:
            if transform:
                new_files_list = json.loads(transform_url_list(str(new_files_list)))
            callback(new_files_list, handle_info_list, **callback_kwargs)
            return True, f"回调函数执行成功"
        except RuntimeError as e:
            return f"回调函数执行失败：{e}"

    # 计算天数
    @staticmethod
    def days_between_dates(date_str1, date_str2):
        date_format = "%Y-%m-%d"
        try:
            date1 = datetime.strptime(date_str1, date_format)
            date2 = datetime.strptime(date_str2, date_format)
            delta = date2 - date1
            days = delta.days + 1
            return days
        except (ValueError, TypeError):
            return None

    # 获取节点数据
    @staticmethod
    def get_construction_plan(project_name, serial_number, work_content):
        sql = (
            "SELECT project_name, serial_number, work_content, start_time, completion_time "
            "FROM construct_plan_data "
            f"WHERE project_name = '{project_name}' "
            f"AND serial_number = '{serial_number}' "
            f"AND work_content = '{work_content}' "
        )
        db = mysql.new_mysql_instance("tbconstruct")
        res = db.get_row(sql)
        return res

    # 获取instance_id
    @staticmethod
    def get_instance_id(ticket_id):
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {"InstanceId": "", "TicketId": ""},
                "SearchCondition": {"TicketId": [str(ticket_id)]},
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List")
        instance_id = ""
        if result_list:
            instance_id = result_list[0].get("InstanceId")
        return instance_id

    # 获取task_id
    @staticmethod
    def get_task_id(ticket_id, task_name):
        instance_id = Tools.get_instance_id(ticket_id)
        if instance_id:
            data = {"InstanceId": instance_id}
            res = gnetops.request(
                action="TaskCenter", method="QueryWorkflowLog", data=data
            )
            for row in res:
                if (
                    row.get("TaskName") == task_name
                    and row.get("TaskStatus") == "进行中"
                ):
                    return row.get("TaskId")
        else:
            return "未获取到instance_id"

    # 完工确认逻辑处理：校验数据；处理数据格式；获取待办处理人；扭转待办
    @staticmethod
    def fire_test_report_handle(data):
        # data = {
        #     # 腾讯侧工单号
        #     "TencentTicketId": "",
        #     # 项目名称
        #     "ProjectName": "",
        #     # 消防第三方检测报告（多文件）
        #     "FireThirdPartyTestReport": [""],
        #     # 备注
        #     "Remark": "",
        # }
        project_name = data["ProjectName"]
        ticket_id = data["TencentTicketId"]
        # 处理数据格式
        Appendix = (
            data["FireThirdPartyTestReport"] if data["FireThirdPartyTestReport"] else []
        )
        xfbg_file = json.loads(transform_url_list(str(Appendix)))
        xfbg_remark = data["Remark"]
        # 扭转待办
        task_id = Tools.get_task_id(ticket_id, "消防第三方检测报告上传")
        variables_map = {
            "xfbg_file": xfbg_file,
            "xfbg_remark": xfbg_remark,
        }
        WorkflowContinue(task_id, variables_map).call()
        # 异步处理文件
        threading.Thread(
            target=Tools.async_files_handle,
            args=(
                Appendix,
                Tools.update_xfbg_file,
            ),
            kwargs={
                "callback_kwargs": {"ticket_id": ticket_id},
                "transform": True,
            },
        ).start()

    # 附件处理完后的回调函数
    def update_xfbg_file(new_files_list, handle_info_list, ticket_id):
        """
        更新消防第三方检测报告
        """
        instance_id = Tools.get_instance_id(ticket_id)
        WorkflowVarUpdate(
            instance_id=instance_id,
            variables={
                "xfbg_file": new_files_list,
                "xfbg_file_handle_info_list": handle_info_list,
            },
        ).call()

    # 查2.4项目报建全部子任务
    @staticmethod
    def query_construction_plan(project_name):
        sql = (
            "SELECT project_name, serial_number, work_content, start_time, completion_time AS finish_time, "
            "not_involved "
            "FROM construct_plan_data "
            f"WHERE project_name = '{project_name}' AND serial_number LIKE '2.4.%' "
        )
        db = mysql.new_mysql_instance("tbconstruct")
        plan_list = db.query(sql)
        return plan_list

    # 查子任务
    @staticmethod
    def query_subtask_plan(project_name, serial_number_list):
        serial_number_temp = ", ".join(f"'{item}'" for item in serial_number_list)
        sql = (
            "SELECT project_name, serial_number, work_content, start_time, completion_time AS finish_time, "
            "not_involved "
            "FROM construct_plan_data "
            f"WHERE project_name = '{project_name}' AND serial_number IN ({serial_number_temp}) "
        )
        db = mysql.new_mysql_instance("tbconstruct")
        plan_list = db.query(sql)
        return plan_list

    # 起子任务工单
    @staticmethod
    def create_ticket(args):
        project_name = args.get("project_name")
        serial_number = args.get("serial_number")
        work_content = args.get("work_content")
        TicketTitle = f"{project_name}项目报建-{serial_number}{work_content}过程记录"
        data = {
            "CustomVariables": args,
            "ProcessDefinitionKey": "partner_project_construction_application_sub_item_task",
            "Source": "",
            "TicketDescription": TicketTitle,
            "TicketLevel": "3",
            "TicketTitle": TicketTitle,
            "UserInfo": {
                "Concern": "v_vikyjiang",
                "Deal": "v_vikyjiang",
                "Creator": "dcops",
            },
        }
        res = gnetops.request(action="Ticket", method="Create", data=data)
        return res
