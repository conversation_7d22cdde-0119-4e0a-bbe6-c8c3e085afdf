import ast
import json

from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.cooperative_partner.cooperative_partner_general_method import calling_external_interfaces


class AcceptanceTransferDimensionWB(object):
    """
        验收转维
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(AcceptanceTransferDimensionWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def acceptance_transfer_dimension_process_initiation(self, project_name):
        """
            创建工单--验收转维
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/Create_new_accept_to_maint"
            }
        )

        db = mysql.new_mysql_instance("tbconstruct")
        mozu_sql = "SELECT module_name FROM project_module_mapping_relationship " \
                   " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(mozu_sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        insert_data = {
            'ticket_id': ticket_id,
            'project_name': project_name,
            'module_name': module_name
        }

        if insert_data:
            db.insert("maintenance_main_process", insert_data)

        PartnerTicketId = str(info['Data'])
        variables = {
            'info': str(info),
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_training_plan_and_data_approval_results(self, project_name, examine_approve_result, dismiss_role,
                                                         remark):
        """
            培训计划与资料审批结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")

        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'
        feedback_training_plan_and_data_approval_results_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "feedback_training_plan_and_data_approval_results",  # (*必填) 云函数名称
                        "data": feedback_training_plan_and_data_approval_results_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": feedback_training_plan_and_data_approval_results_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                  "feedback_training_plan_and_data_approval_results"
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'feedback_training_plan_and_data_approval_results': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def material_information_approval_feedback(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            物资信息审批反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")
        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'
        material_information_approval_feedback_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "material_information_approval_feedback",  # (*必填) 云函数名称
                        "data": material_information_approval_feedback_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": material_information_approval_feedback_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/material_information_approval_feedback",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'material_information_approval_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceYszw(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceYszw, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_training_plan_and_data_upload(self):
        """
            等待培训计划与资料上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        mozu_sql = "SELECT module_name FROM project_module_mapping_relationship " \
                   " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(mozu_sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        sql = "SELECT training_name, plan_time, factory, upload_training " \
              "FROM maintenance_training " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND mozu_name = '{module_name}' "
        result_list = db.get_all(sql)

        training_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            training_name = record.get('training_name', "")  # 培训名称
            plan_time = record.get('plan_time', "")  # 计划时间
            factory = record.get('factory', "")  # 厂家
            upload_training = record.get('upload_training', "[]")  # 默认值为一个空列表字符串

            # 尝试将 upload_training 字符串解析为列表
            try:
                upload_trainings = ast.literal_eval(upload_training)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                upload_trainings = []  # 如果解析失败，设置为空列表
            upload_training_list = []
            # 确保 upload_trainings 是一个列表
            if isinstance(upload_trainings, list):
                # 为每个 URL 创建一个新的字典
                for url in upload_trainings:
                    upload_training_list.append({"upload_training": url})

            training_file_list.append({
                'training_name': training_name,
                'plan_time': plan_time,
                'factory': factory,
                'upload_training': upload_training_list
            })

        self.workflow_detail.add_kv_table('培训计划与资料信息', {'message': result_list})
        if result_list:
            variables = {
                "module_name": module_name,
                "px_result_list": result_list,
                "training_file_list": training_file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def material_information_upload(self):
        """
            等待物资信息上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        mozu_sql = "SELECT module_name FROM project_module_mapping_relationship " \
                   " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(mozu_sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        sql = "SELECT material_name, material_quantity, upload_materials " \
              "FROM maintenance_material_acceptance " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND mozu_name = '{module_name}' "
        result_list = db.get_all(sql)
        material_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            material_name = record.get('material_name', "")  # 培训名称
            material_quantity = record.get('material_quantity', "")  # 计划时间
            upload_materials = record.get('upload_materials', "[]")  # 默认值为一个空列表字符串

            # 尝试将 upload_materials 字符串解析为列表
            try:
                upload_materialses = ast.literal_eval(upload_materials)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                upload_materialses = []  # 如果解析失败，设置为空列表

            upload_materials_list = []
            # 确保 upload_materialses 是一个列表
            if isinstance(upload_materialses, list):
                # 为每个 URL 创建一个新的字典
                for url in upload_materialses:
                    upload_materials_list.append({"upload_materials": url})

            material_file_list.append({
                'material_name': material_name,
                'material_quantity': material_quantity,
                'upload_materials': upload_materials_list
            })

        self.workflow_detail.add_kv_table('物资信息', {'message': result_list})
        if result_list:
            variables = {
                "wz_result_list": result_list,
                "material_file_list": material_file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def material_information_upload_new(self):
        """
            等待物资信息上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        mozu_sql = "SELECT module_name FROM project_module_mapping_relationship " \
                   " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(mozu_sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        sql = "SELECT material_name, material_quantity, upload_materials " \
              "FROM maintenance_material_acceptance " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND mozu_name = '{module_name}' "
        result_list = db.get_all(sql)
        material_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            material_name = record.get('material_name', "")  # 培训名称
            material_quantity = record.get('material_quantity', "")  # 物资数量
            upload_materials = record.get('upload_materials', "[]")  # 默认值为一个空列表字符串

            # 尝试将 upload_materials 字符串解析为列表
            try:
                upload_materialses = ast.literal_eval(upload_materials)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                upload_materialses = []  # 如果解析失败，设置为空列表

            upload_materials_list = []
            # 确保 upload_materialses 是一个列表
            if isinstance(upload_materialses, list):
                # 为每个 URL 创建一个新的字典
                for url in upload_materialses:
                    upload_materials_list.append({"upload_materials": url})

            if not material_quantity:  # 如果为空或 None 或 空字符串
                material_quantity = 0
            else:
                try:
                    material_quantity = int(material_quantity)
                except ValueError:
                    review_object = AcceptanceTransferDimensionWB()
                    review_object.material_information_approval_feedback(project_name, "物资数量请填写纯数字", "",
                                                                         "数据格式错误")
                    return {"success": True, "data": "流程已结束"}
            material_file_list.append({
                'material_name': material_name,
                'material_quantity': material_quantity,
                'upload_materials': upload_materials_list
            })

        self.workflow_detail.add_kv_table('物资信息', {'message': result_list})
        if result_list:
            variables = {
                "wz_result_list": result_list,
                "material_file_list": material_file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceYszw(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceYszw, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_basic_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT ticket_id FROM maintenance_main_process " \
              f"WHERE project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if result_list:
            ticket_id = result_list[0].get("ticket_id")
            return ticket_id
        else:
            return 0

    def training_plan_and_data_upload(self, TencentTicketId, ProjectName, TrainingList):
        """
            培训计划与资料上传
        """
        insert_data = []
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}

        info = self.get_basic_info(project_name=ProjectName)
        if info == 0:
            return {'code': -1, 'msg': '参数错误'}

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship " \
              " WHERE project_name='%s'" % ProjectName
        module_name_list = db.get_all(sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        for row in TrainingList:
            training_name = row.get('TrainingName')
            plan_start_time = row.get('PlanStartTime')
            manufacturers = row.get('Manufacturers')
            training_mateIals = row.get('TrainingMateIals')
            remark = row.get('Remark')

            insert_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'mozu_name': module_name,
                'training_name': training_name,
                'plan_time': plan_start_time,
                'factory': manufacturers,
                'upload_training': str(training_mateIals)
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert_batch("maintenance_training", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def material_information_upload(self, TencentTicketId, ProjectName, MaterialsList):
        """
            物资信息上传
        """
        insert_data = []
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}

        info = self.get_basic_info(project_name=ProjectName)
        if info == 0:
            return {'code': -1, 'msg': '参数错误'}

        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship " \
              " WHERE project_name='%s'" % ProjectName
        module_name_list = db.get_all(sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        for row in MaterialsList:
            materials_name = row.get('MaterialsName')
            materials_quantity = row.get('MaterialsQuantity')
            materials_material = row.get('MaterialsMaterial')
            remark = row.get('Remark')

            insert_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'mozu_name': module_name,
                'material_name': materials_name,
                'material_quantity': materials_quantity,
                'upload_materials': str(materials_material)
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert_batch("maintenance_material_acceptance", insert_data)
        tb_db.commit()

        return {'code': 200, 'msg': '成功'}


class PxSupervisionExaminationApproval(AjaxTodoBase):
    """
        培训资料-监理审批
    """

    def __init__(self):
        super(PxSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        px_supervision_approval = process_data.get('px_supervision_approval')
        px_supervision_remark = process_data.get('px_supervision_remark')

        if px_supervision_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_training " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'px_supervision_approval': px_supervision_approval,
            'px_supervision_remark': px_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PxItemApproval(AjaxTodoBase):
    """
        培训资料-项管审批
    """

    def __init__(self):
        super(PxItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        px_item_approval = process_data.get('px_item_approval')
        px_item_remark = process_data.get('px_item_remark')

        if px_item_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_training " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'px_item_approval': px_item_approval,
            'px_item_remark': px_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PxPmApproval(AjaxTodoBase):
    """
        培训资料-PM审批
    """

    def __init__(self):
        super(PxPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        px_PM_approval = process_data.get('px_PM_approval')
        px_PM_remark = process_data.get('px_PM_remark')

        if px_PM_approval == '驳回':
            query_sql = "DELETE FROM maintenance_training " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'px_PM_approval': px_PM_approval,
            'px_PM_remark': px_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WzSupervisionExaminationApproval(AjaxTodoBase):
    """
        物资信息-监理审批
    """

    def __init__(self):
        super(WzSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        wz_supervision_approval = process_data.get('wz_supervision_approval')
        wz_supervision_remark = process_data.get('wz_supervision_remark')

        if wz_supervision_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_material_acceptance " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'wz_supervision_approval': wz_supervision_approval,
            'wz_supervision_remark': wz_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WzItemApproval(AjaxTodoBase):
    """
        物资信息-项管审批
    """

    def __init__(self):
        super(WzItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        wz_item_approval = process_data.get('wz_item_approval')
        wz_item_remark = process_data.get('wz_item_remark')

        if wz_item_approval == '驳回':
            query_sql = f"DELETE FROM maintenance_material_acceptance " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'wz_item_approval': wz_item_approval,
            'wz_item_remark': wz_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WzPmApproval(AjaxTodoBase):
    """
        物资信息-PM审批
    """

    def __init__(self):
        super(WzPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        wz_PM_approval = process_data.get('wz_PM_approval')
        wz_PM_remark = process_data.get('wz_PM_remark')

        if wz_PM_approval == '驳回':
            query_sql = "DELETE FROM maintenance_material_acceptance " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        f"AND mozu_name = '{module_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'wz_PM_approval': wz_PM_approval,
            'wz_PM_remark': wz_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
