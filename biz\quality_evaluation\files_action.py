from datetime import datetime


# 文件操作
class QualityEvaluationFileAction(object):
    # 判断文件是否存在(多个)
    @staticmethod
    def get_multiple_files_exit_flag(file_list, file_label):
        missing_values = []
        for i in range(len(file_list)):
            try:
                if not file_list[i]["response"]["FileList"][0]["url"]:
                    missing_values.append(f"第{i + 1}个文件未上传完成")
            except KeyError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
            except IndexError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
            except TypeError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
        if missing_values:
            error = "；<br>".join(missing_values)
            return {
                "code": -1,
                "msg": f"{file_label}未上传完成。<br>错误为{error}。<br>请确保所有文件上传完成后再做结单处理！",
            }, False
        return {"code": 0, "msg": f"{file_label}上传正确"}, True

    # 多个文件处理-json
    @staticmethod
    def get_file_json(file_list, uploader):
        file_json = []
        for file in file_list:
            file_info = file["response"]["FileList"][0]
            file_json.append(
                {
                    "file_name": file_info["name"],
                    "file_url": file_info["url"],
                    "file_uploader": uploader,
                    "upload_time": datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
                }
            )
        return file_json
