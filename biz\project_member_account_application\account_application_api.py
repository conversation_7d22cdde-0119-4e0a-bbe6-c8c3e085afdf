from iBroker.lib import mysql


class AccountApplicationApi(object):
    # 判断是否有重复的账号（集成商、项管、监理） del_flag=1为删除的账号
    @staticmethod
    def DuplicateAccount(table_list):
        duplicate_account_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in table_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    name = ""
                    project_list = []
                    for account in result:
                        role = account.get("role")
                        # 只判断集成商、项管、监理
                        if "集成商" in role or "项管" in role or "监理" in role:
                            name = account.get("name")
                            project_list.append(account.get("project_name"))
                    duplicate_account_list.append(
                        f"{name}已在{(',').join(project_list)}中开通账号"
                    )
        return duplicate_account_list

    # 判断是否有重复的账号（集成商、项管、监理） del_flag=1为删除的账号
    # 判断已存在的和新申请的姓名是否一致
    @staticmethod
    def account_validate(table_list):
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in table_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    name = info.get("name")
                    phone = info.get("phone")
                    for account in result:
                        # 重复账号只判断集成商、项管、监理
                        exist_role = account.get("role")
                        exist_project_name = account.get("project_name")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_account = account.get("account")
                        if (
                            "集成商" in exist_role
                            or "项管" in exist_role
                            or "监理" in exist_role
                        ):
                            account_validate_list.append(
                                f"【已开通账号】({name}, {phone})已开通账号："
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        if name != exist_name:
                            account_validate_list.append(
                                f"【同手机号不同姓名】({name}, {phone})对应已开通账号："
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        return account_validate_list

    @staticmethod
    def query_exist_account(member_list):
        """
        判断项目中是否开通过同手机号、同姓名、同角色的账号
        """
        for info in member_list:
            db = mysql.new_mysql_instance("tbconstruct")
            project_name = info.get("campus_name") + info.get("project_name")
            phone = info.get("phone")
            name = info.get("name")
            role = info.get("role")
            sql = (
                f"SELECT * FROM project_role_account WHERE "
                f"project_name='{project_name}' "
                f"and phone='{phone}' "
                f"and name='{name}' "
                f"and role='{role}' "
                f"and del_flag=0"
            )
            result = db.get_all(sql)
            info["existFlag"] = "是" if result else "否"
        return member_list
