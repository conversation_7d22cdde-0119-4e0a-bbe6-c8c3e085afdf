import json
from datetime import datetime, timedelta

from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

data_query_base = {
    "SchemaId": "ticket_base",
    "ReturnTotalRows": 1,
    "Data": {
        "ResultColumns": {
            "TicketId": "",
            "InstanceId": "",
            "ProcessDefinitionKey": "",
            "SchemeNameCn": "",
            "Description": "",
            "Title": "",
            "TicketStatus": "",
            "CurrentTasks": "",
            "CurrentTaskDetails": "",
            "CurrentAllProcessUsers": "",
            "CreateTime": "",
            "EndTime": "",
            "CustomRequestVarKV": "",
            "IsForceEnd": "",
            "ForceEndReason": "",
            "ForceEndOperator": "",
            "Manager": "",
            "UserInfo": "",
        }
    },
}


def process_insert_data(data: list = []):
    insert_data = []
    for item in data:
        insert_data.append(
            {
                "TicketId": item["TicketId"],
                "InstanceId": item["InstanceId"],
                "ProcessDefinitionKey": item["ProcessDefinitionKey"],
                "SchemeNameCn": item["SchemeNameCn"],
                "Description": item["Description"],
                "Title": item["Title"],
                "TicketStatus": item["TicketStatus"],
                "CurrentTasks": json.dumps(item["CurrentTasks"], ensure_ascii=False),
                "CurrentTaskDetails": json.dumps(
                    item["CurrentTaskDetails"], ensure_ascii=False
                ),
                "CurrentAllProcessUsers": item["CurrentAllProcessUsers"],
                "CreateTime": item["CreateTime"],
                "EndTime": item["EndTime"],
                "CustomRequestVarKV": json.dumps(
                    item["CustomRequestVarKV"], ensure_ascii=False
                ),
                "IsForceEnd": item["IsForceEnd"],
                "ForceEndReason": item["ForceEndReason"],
                "ForceEndOperator": item["ForceEndOperator"],
                "Manager": item["Manager"],
                "UserInfo": json.dumps(item["UserInfo"], ensure_ascii=False),
            }
        )
    return insert_data


# SyncTicketData 同步工单数据
class SyncTicketData(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def sync_all_tikets(self):

        query_data = {
            "SchemaId": "ticket_base",
            "ReturnTotalRows": 1,
            "Data": {
                "ResultColumns": {
                    "TicketId": "",
                    "InstanceId": "",
                    "ProcessDefinitionKey": "",
                    "SchemeNameCn": "",
                    "Description": "",
                    "Title": "",
                    "TicketStatus": "",
                    "CurrentTasks": "",
                    "CurrentTaskDetails": "",
                    "CurrentAllProcessUsers": "",
                    "CreateTime": "",
                    "EndTime": "",
                    "CustomRequestVarKV": "",
                    "IsForceEnd": "",
                    "ForceEndReason": "",
                    "ForceEndOperator": "",
                    "Manager": "",
                    "UserInfo": "",
                },
            },
        }
        query_data = data_query_base
        max_data = 100
        query_data["Data"]["SearchCondition"] = {"SceneType": ["建设平台"]}
        query_data["Data"]["Limit"] = {"Size": max_data, "Start": 0}
        # 获取所有工单信息
        ticket = gnetops.request(
            action="QueryDataTodo", method="Run", ext_data=query_data
        )
        dataList = ticket["List"]
        db = mysql.new_mysql_instance("tbconstruct")
        db.insert_batch("all_construction_ticket_data", process_insert_data(dataList))
        #
        total_rows = ticket["TotalRows"]
        if total_rows > max_data:
            for page in range(1, int(total_rows / max_data) + 1):
                limit = {"Size": max_data, "Start": page * max_data}
                query_data["Data"]["Limit"] = limit
                ticket = gnetops.request(
                    action="QueryDataTodo", method="Run", ext_data=query_data
                )
                db.insert_batch(
                    "all_construction_ticket_data", process_insert_data(ticket["List"])
                )

    def sync_date_yesterday(self):
        db = mysql.new_mysql_instance("tbconstruct")
        max_data = 100
        # 先更新运行中工单的状态
        update_data = db.get_list(
            "all_construction_ticket_data",
            fields=["TicketId"],
            conditions={
                "TicketStatus": ["OPEN"],
            },
        )
        for item in update_data:
            query_data_update = {
                "SchemaId": "ticket_base",
                "ReturnTotalRows": 1,
                "Data": {
                    "ResultColumns": {
                        "TicketId": "",
                        "TicketStatus": "",
                        "CurrentTasks": "",
                        "CurrentTaskDetails": "",
                        "CurrentAllProcessUsers": "",
                        "EndTime": "",
                        "IsForceEnd": "",
                        "ForceEndReason": "",
                        "ForceEndOperator": "",
                    },
                    "SearchCondition": {"TicketId": item["TicketId"]},
                },
            }
            data_query_one = gnetops.request(
                action="QueryDataTodo", method="Run", ext_data=query_data_update
            )
            data_one = data_query_one["List"][0]
            update_one = {
                "TicketStatus": data_one["TicketStatus"],
                "CurrentTasks": json.dumps(
                    data_one["CurrentTasks"], ensure_ascii=False
                ),
                "CurrentTaskDetails": json.dumps(
                    data_one["CurrentTaskDetails"], ensure_ascii=False
                ),
                "CurrentAllProcessUsers": data_one["CurrentAllProcessUsers"],
                "EndTime": data_one["EndTime"],
                "IsForceEnd": data_one["IsForceEnd"],
                "ForceEndReason": data_one["ForceEndReason"],
                "ForceEndOperator": data_one["ForceEndOperator"],
            }
            db.update(
                "all_construction_ticket_data",
                update_one,
                conditions={"TicketId": item["TicketId"]},
            )

        # 插入前一天新增的工单数据
        current_time = datetime.now()
        previous_day = current_time - timedelta(days=1)
        # 格式化日期时间
        current_time_formatted = current_time.strftime("%Y-%m-%d %H:%M:%S")
        previous_day_formatted = previous_day.strftime("%Y-%m-%d %H:%M:%S")

        query_data_insert = data_query_base
        query_data_insert["Data"]["SearchCondition"] = {
            "SceneType": ["建设平台"],
            "CreateTime": {"gt": previous_day_formatted, "lt": current_time_formatted},
        }

        query_data_insert["Data"]["Limit"] = {"Size": max_data, "Start": 0}
        ticket_today = gnetops.request(
            action="QueryDataTodo", method="Run", ext_data=query_data_insert
        )
        if len(ticket_today["List"]) != 0:
            ticket_today_ids = [item["TicketId"] for item in ticket_today["List"]]
            db.delete("all_construction_ticket_data", {"TicketId": ticket_today_ids})
            db.insert_batch(
                "all_construction_ticket_data",
                process_insert_data(ticket_today["List"]),
            )
            total_rows = ticket_today["TotalRows"]
            if total_rows > max_data:
                for page in range(1, int(total_rows / max_data) + 1):
                    limit = {"Size": max_data, "Start": page * max_data}
                    query_data_insert["Data"]["Limit"] = limit
                    ticket_today = gnetops.request(
                        action="QueryDataTodo", method="Run", ext_data=query_data_insert
                    )
                    if len(ticket_today["List"]) != 0:
                        db.insert_batch(
                            "all_construction_ticket_data",
                            process_insert_data(ticket_today["List"]),
                        )
        flow.complete_task(self.ctx.task_id, variables={})
