# 设备数据查询修复方案

## 问题背景

在`campus_resources.py`的设备数据查询功能中遇到以下问题：
1. `UnboundLocalError: local variable 'device_record' referenced before assignment`
2. 需要查询项目状态但添加JOIN会导致数据重复
3. 复杂的多表JOIN查询性能差

## 解决方案概述

通过创建数据合并表`consolidated_equipment_data`来解决复杂JOIN和数据重复问题，同时修复了变量引用错误。

## 快速开始

### 🚀 优化版数据合并（推荐）

根据组长反馈优化的高性能版本：

```bash
# 进入项目目录
cd iBroker-construct

# 运行优化的数据合并脚本（分表查询，性能更好）
python scripts/optimized_data_consolidation.py
```

### ⚡ 快速修复（备选方案）

如果遇到存储过程错误，使用快速修复脚本：

```bash
# 运行快速修复脚本（避免存储过程问题）
python scripts/quick_fix_consolidated_table.py
```

### ✅ 验证修复效果

```bash
# 运行基础功能测试
python tests/test_campus_resources_fix.py

# 运行性能测试（推荐）
python tests/test_optimized_performance.py
```

### 3. 测试页面功能

1. **无条件查询**: 不选择任何筛选条件，直接点击查询
   - 预期：不再报错，返回所有数据

2. **园区筛选**: 选择特定园区（如"清远清新"）
   - 预期：返回该园区的设备数据，页面正常显示

3. **项目状态筛选**: 选择项目状态（如"已交付"）
   - 预期：返回对应状态的项目，无重复数据

## 文件说明

### 核心文件
- `biz/construction_big_disk/campus_resources.py` - 修复后的主要业务逻辑
- `scripts/consolidate_equipment_data.py` - 数据合并脚本
- `sql/create_consolidated_equipment_data.sql` - SQL建表脚本

### 文档和测试
- `docs/equipment_data_consolidation_solution.md` - 详细技术方案
- `tests/test_campus_resources_fix.py` - 功能测试脚本
- `README_EQUIPMENT_DATA_FIX.md` - 本文件

## 🆕 新增功能

### 1. 表更新显示
- **数据更新时间显示** - 页面显示最后数据更新时间
- **记录数量统计** - 显示当前数据表记录总数
- **更新状态指示** - 显示数据同步状态（成功/异常）
- **自动刷新提醒** - 数据过期时提醒用户刷新

### 2. 高性能查询优化
- **分表查询** - 避免复杂JOIN，分步获取数据
- **参数化查询** - 防止SQL注入，提高安全性
- **查询限制** - 添加LIMIT限制，避免大数据量卡顿
- **索引优化** - 为常用查询字段添加索引

## 主要修改

### 1. 修复变量引用错误
```python
# 修复前
device_record[f'{project_key}production_work_order'] = row.get('TicketId') or ''

# 修复后
device_record = processed_devices[device_key]
device_record[f'{project_key}production_work_order'] = row.get('TicketId') or ''
```

### 2. 简化SQL查询
```python
# 修复前（复杂JOIN）
unified_query = f"""
    SELECT DISTINCT actd.TicketId, ...
    FROM all_construction_ticket_data actd
    LEFT JOIN equipment_production_racking_process eprp ON ...
    LEFT JOIN summary_data sd ON ...
    WHERE {complex_conditions}
    GROUP BY actd.TicketId
"""

# 修复后（简单查询）
unified_query = f"""
    SELECT TicketId, ..., state as project_state
    FROM consolidated_equipment_data
    WHERE 1=1 {conditions}
    ORDER BY TicketId, device_name LIMIT 3000
"""
```

### 3. 创建合并表
```sql
CREATE TABLE consolidated_equipment_data (
    state VARCHAR(50) DEFAULT NULL,           -- 项目状态
    TicketId VARCHAR(100) NOT NULL,          -- 工单号（主键）
    project_name VARCHAR(255) DEFAULT NULL,  -- 项目名称
    device_name VARCHAR(255) DEFAULT NULL,   -- 设备名称
    -- ... 其他字段
    PRIMARY KEY (TicketId)
);
```

## 定期维护

### 设置定时任务
```bash
# 编辑crontab
crontab -e

# 添加以下行（每小时执行一次数据同步）
0 * * * * cd /path/to/iBroker-construct && python scripts/consolidate_equipment_data.py >> /var/log/equipment_sync.log 2>&1
```

### 监控数据同步
```bash
# 检查合并表数据量
mysql -u username -p -e "SELECT COUNT(*) FROM tbconstruct.consolidated_equipment_data;"

# 检查最近更新时间
mysql -u username -p -e "SELECT MAX(po_create_time) FROM tbconstruct.consolidated_equipment_data;"
```

## 故障排除

### 问题1: 存储过程不存在错误
```
OperationalError: (1305, 'PROCEDURE idc_tb_dt_database.InsertConsolidatedEquipmentData does not exist')
```

**解决方案**：使用快速修复脚本
```bash
python scripts/quick_fix_consolidated_table.py
```

### 问题2: 数据合并脚本执行失败
```bash
# 检查数据库连接
python -c "from iBroker.lib import mysql; db = mysql.new_mysql_instance('tbconstruct'); print('连接成功')"

# 检查表是否存在
mysql -u username -p -e "SHOW TABLES LIKE 'consolidated_equipment_data';" tbconstruct
```

### 问题2: 页面仍然显示"暂无数据"
```bash
# 检查合并表是否有数据
mysql -u username -p -e "SELECT COUNT(*) FROM tbconstruct.consolidated_equipment_data;"

# 检查特定条件的数据
mysql -u username -p -e "SELECT * FROM tbconstruct.consolidated_equipment_data WHERE project_name LIKE '%清远清新%' LIMIT 5;" tbconstruct
```

### 问题3: 仍然报device_record错误
1. 确认代码修改已生效
2. 重启应用服务
3. 检查Python语法错误

## 性能优化建议

### 1. 添加索引
```sql
-- 为常用查询字段添加索引
ALTER TABLE consolidated_equipment_data ADD INDEX idx_project_name (project_name);
ALTER TABLE consolidated_equipment_data ADD INDEX idx_state (state);
ALTER TABLE consolidated_equipment_data ADD INDEX idx_device_type (device_type);
```

### 2. 分区表（可选）
```sql
-- 如果数据量很大，可以按时间分区
ALTER TABLE consolidated_equipment_data 
PARTITION BY RANGE (YEAR(po_create_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 联系支持

如果遇到问题，请：
1. 查看日志文件：`/var/log/equipment_sync.log`
2. 运行测试脚本：`python tests/test_campus_resources_fix.py`
3. 检查数据库连接和表结构
4. 联系开发团队获取支持

---

**注意**: 首次部署时必须先运行数据合并脚本创建合并表，否则查询会失败。
