#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2024/3/5 10:19
# <AUTHOR> v_hxhaoliu
# @File    : component_tools.py
def get_img_url(image_list):
    """
    获取图片的url
    :param image_dict: 上传组件的字段值
    :return:
    """
    res = ''
    if image_list:
        temp = []
        for image in image_list:
            image_url = image['response']['FileList'][0]['url']
            temp.append(image_url)
        res = '; '.join(temp)
    return res


image_list = [
    {'name': 'hdthybr0cae.jpg',
     'response': {
         'FileList': [
             {'name': 'hdthybr0cae.jpg',
              'status': 'success',
              'uid': 1709603989840,
              'url': 'https://test.otob.dcops.qq.com/relay/cosfile/relay/20240305/'
                     'hdthybr0cae_6acdddd69936455d919dafdc4b480aaa.jpg'
              }
         ]
     },
     'status': 'success',
     'uid': 1709603989840,
     'url': 'https://test.otob.dcops.qq.com/relay/cosfile/relay/20240305/'
            'hdthybr0cae_6acdddd69936455d919dafdc4b480aaa.jpg'
     }, {
        'name': 'R.jpg',
        'response':
            {
                'FileList': [
                    {'name': 'R.jpg',
                     'status': 'success',
                     'uid': 1709604057238,
                     'url': 'https://test.otob.dcops.qq.com/relay/cosfile/relay/20240305/'
                            'R_171e8fe1aa1544a1868ab710eed82d82.jpg'
                     }
                ]
            },
        'size': 417496, 'status': 'success', 'uid': 1709604057084}]

res = get_img_url('')
print(res)
