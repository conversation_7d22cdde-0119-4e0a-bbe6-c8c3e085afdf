import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


class ProjectOpenDcops(object):
    """
        项目开办
        腾讯方调用接口：输出数据（合作伙伴Dcops系统）
    """

    def __init__(self):
        super(ProjectOpenDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_project_open(self, project_name):
        """
            创建工单--项目开办
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_start_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        data = {
            "Action": "Ticket",  # 原样填写
            "Method": "Create",  # 原样填写
            "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
            # 调用方系统标识
            "CorpId": corp_id,  # (*必填)
            # 服务商企业ID
            "Data": {  # 自定义流程变量
                "CustomVariables": project_start_data,
                # 流程定义标识
                "ProcessDefinitionKey": "project_start",  # (*必填)
                # 工单来源
                "Source": f"外部系统（{Facilitator}）",
                # 工单描述
                "TicketDescription": f"{project_name}:项目开办",  # (*必填)
                # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                # 工单标题
                "TicketTitle": f"{project_name}:项目开办",  # (*必填)
                "UserInfo": {  # 用户信息
                    "Concern": "v_mmywang",  # 关注人(存在多个使用分号(;)分隔)
                    "Creator": "v_mmywang",  # (*必填)
                    "Deal": "v_mmywang"  # 处理人(存在多个使用分号(;)分隔)
                }
            }
        }

        info = calling_external_interfaces(interface_info, data)

        PartnerInstanceId = str(info['data']['InstanceId'])
        PartnerTicketId = str(info['data']['TicketId'])

        variables = {
            'create_project_open': str(info),
            'system_id': system_id,
            'PartnerInstanceId': PartnerInstanceId,
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def scheme_review_feedback(self, project_name, sgfa_scheme_supervision_approval, sgfa_scheme_supervision_remark):
        """
            项目开办方案审核反馈
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_id = self.ctx.variables.get("system_id")
        scheme_supervision_approval = 0
        if sgfa_scheme_supervision_approval == "同意":
            scheme_supervision_approval = 1
        elif sgfa_scheme_supervision_approval == "驳回":
            scheme_supervision_approval = 2
        scheme_review_feedback_data = {
            "ProjectName": project_name,  # 项目名称
            "SchemeSupervisionApproval": scheme_supervision_approval,  # 监理审批
            "SchemeSupervisionRemark": sgfa_scheme_supervision_remark,  # 监理备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "scheme_review_feedback",  # (*必填) 云函数名称
                    "data": scheme_review_feedback_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)
        result_str = info['data']['result']

        # 将 result 字符串解析为字典
        result_dict = json.loads(result_str)

        # 获取 code 的值
        code = result_dict.get('code')
        message = result_dict.get('message')
        if code != 0:
            raise Exception(f"报错信息为： {message}")
        variables = {
            'scheme_review_feedback': str(info),
            'kbfa_data': data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def completion_audit_feedback(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            项目开办完工审核反馈
        """
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        completion_audit_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "completion_audit_feedback",  # (*必填) 云函数名称
                    "data": completion_audit_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)

        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceKbDcops(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceKbDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_construction_plan_submission(self):
        """
            等待施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT plan_url, construction_plan_remark " \
              "FROM equipment_before_url " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"and name = 'start' " \
              f"and project_name = '{project_name}' "
        result_list = db.get_all(sql)

        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        plan_url_table = []
        if result_list:
            for row in result_list:
                plan_url = row.get('plan_url')
                plan_url = plan_url.replace("'", '"')
                plan_url = json.loads(plan_url)
                construction_plan_remark = row.get('construction_plan_remark')
                if plan_url is not None:
                    for doc in plan_url:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                plan_url_table.append({
                                    "plan_url": url,
                                    'plan_url_name': name
                                })
            variables = {
                "wait_construction_plan_submission": result_list,
                "plan_url": plan_url_table,
                "construction_plan_remark": construction_plan_remark,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_completion_confirmation_data_submission(self):
        """
            等待完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND (serial_number = '2.7' OR serial_number = '2.7.1' OR serial_number = '2.7.2')"
        file_sql = "SELECT report_url, photo_url " \
                   "FROM equipment_before_url " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}'" \
                   f"AND name = 'start'"
        word_list = db.get_all(word_sql)
        kb_file_list = db.get_all(file_sql)
        word_table = []
        appendix_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            # 根据 serial_number 选择需要的字段
            if serial_number == '2.7':
                kb_work_content = row.get('work_content', '')
                kb_start_time = row.get('start_time', '')
                kb_completion_time = row.get('completion_time', '')
                kb_responsible_person = row.get('responsible_person', '')

            if serial_number in ['2.7.1', '2.7.2']:
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')

                if appendix is not None:
                    appendix = appendix.replace("'", '"')
                    appendix = json.loads(appendix)
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix_table": appendix_table
                })
        report_url_table = []
        photo_url_table = []
        # 遍历原始列表
        for record in kb_file_list:
            # 获取 plan_url 列表
            report_url = record.get('report_url', [])
            photo_url = record.get('photo_url', [])
            if report_url is not None:
                report_url = report_url.replace("'", '"')
                report_url = json.loads(report_url)
                for doc in report_url:
                    if doc is not None and "response" in doc and "FileList" in doc["response"]:
                        file_list = doc["response"]["FileList"]
                        if len(file_list) > 0 and "url" in file_list[0]:
                            url = file_list[0]["url"]
                            name = file_list[0]['name']
                            report_url_table.append({
                                "report_url": url,
                                'report_url_name': name
                            })
            if photo_url is not None:
                photo_url = photo_url.replace("'", '"')
                photo_url = json.loads(photo_url)
                for doc in photo_url:
                    if doc is not None and "response" in doc and "FileList" in doc["response"]:
                        file_list = doc["response"]["FileList"]
                        if len(file_list) > 0 and "url" in file_list[0]:
                            url = file_list[0]["url"]
                            name = file_list[0]['name']
                            photo_url_table.append({
                                "photo_url": url,
                                'photo_url_name': name
                            })
        if word_list and kb_file_list:
            variables = {
                'kb_work_content': kb_work_content,
                'kb_start_time': kb_start_time,
                'kb_completion_time': kb_completion_time,
                'kb_responsible_person': kb_responsible_person,
                'word_table': word_table,
                'report_url_table': report_url_table,
                'photo_url_table': photo_url_table,
                'word_list': word_list,
                'kb_file_list': kb_file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
