# DeliveryScheduleManagement 类优化实现方案

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-22
- **负责人**: Bob (架构师)
- **项目**: iBroker-Construct 货期管理优化

## 2. 代码优化方案

### 2.1 tabular_data_query 函数优化

#### 2.1.1 删除重复代码
当前 `tabular_data_query` 函数从第2757行开始存在重复的实现代码，这部分代码是旧版本的实现，应该被删除。优化后只保留前面的优化版本实现。

#### 2.1.2 SQL查询优化
保留现有的优化查询，确保强制结单过滤条件正确应用：

```python
# 强制结单过滤 - 最重要的修复
conditions.append("(actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')")
```

#### 2.1.3 数据处理优化
统一实际到货时间的处理逻辑，确保格式一致：

```python
# 处理实际到货时间
actual_arrival_time = row.get('actual_arrival_time')
if actual_arrival_time and str(actual_arrival_time) != '0000-00-00 00:00:00':
    if isinstance(actual_arrival_time, datetime.datetime):
        actual_time_str = actual_arrival_time.strftime('%Y-%m-%d')
    else:
        actual_time_str = str(actual_arrival_time)[:10]
else:
    actual_time_str = None
```

### 2.2 dynamic_header 函数优化

#### 2.2.1 保留现有优化
`dynamic_header` 函数已经实现了单次复杂查询，保留现有的优化实现。

#### 2.2.2 完善强制结单过滤
确保强制结单过滤条件正确应用，与 `tabular_data_query` 函数保持一致。

#### 2.2.3 优化数据去重逻辑
改进数据去重逻辑，提高处理效率：

```python
# 数据去重和处理
unique_data = []
seen_records = set()

for row in result:
    # 处理项目交付时间格式
    delivery_time = row.get('project_required_delivery_time', '')
    if '→' in delivery_time:
        delivery_time = delivery_time.split('→')[1].strip()

    record_key = (
        row.get('project_name'),
        row.get('supplier'),
        delivery_time
    )

    if record_key not in seen_records:
        unique_data.append({
            'project_name': row.get('project_name'),
            'supplier': row.get('supplier'),
            'project_required_delivery_time': delivery_time,
            'required_delivery_time': row.get('project_required_delivery_time')
        })
        seen_records.add(record_key)
```

## 3. 具体实现步骤

### 3.1 tabular_data_query 函数修改

1. **删除重复代码**:
   - 删除从第2757行开始的重复实现代码（包含注释 `"""表格数据查询（甲供）"""` 及之后的代码）

2. **保留优化版本**:
   - 保留前面的优化实现代码
   - 确保强制结单过滤条件正确应用

3. **完善数据处理**:
   - 统一实际到货时间的处理逻辑
   - 确保返回数据格式一致

### 3.2 测试验证

1. **单项目模式测试**:
   - 使用 `project_name` 参数进行查询测试
   - 验证返回数据格式和内容是否正确

2. **多项目模式测试**:
   - 使用多个查询条件组合进行测试
   - 验证返回数据格式和内容是否正确

3. **强制结单过滤测试**:
   - 确保强制结单的工单被正确过滤
   - 验证过滤条件是否正确应用

## 4. 代码修改详情

### 4.1 tabular_data_query 函数修改

```python
def tabular_data_query(self, project_name=None, state=None, PM=None, demand_delivery=None):
    """
        表格数据查询（甲供）- 优化版本
        主要改进:
        1. 使用单次复杂查询获取所有数据
        2. 添加强制结单工单过滤
        3. 智能设备类型匹配
        4. 消除N+1查询问题
    """
    # 获取数据库连接
    db = mysql.new_mysql_instance("tbconstruct")

    # 构建WHERE条件
    conditions = []

    # 强制结单过滤 - 最重要的修复
    conditions.append("(actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')")

    if project_name:
        conditions.append(f"dsm.project_name LIKE '%{project_name}%'")

    if state:
        conditions.append(f"sd.state = '{state}'")

    if PM:
        conditions.append(f"rewd.PM = '{PM}'")

    if demand_delivery:
        start_date, end_date = demand_delivery
        conditions.append(
            f"STR_TO_DATE(rewd.demand_delivery, '%Y年%m月%d日') BETWEEN "
            f"STR_TO_DATE('{start_date}', '%Y年%m月%d日') AND "
            f"STR_TO_DATE('{end_date}', '%Y年%m月%d日')"
        )

    where_clause = " AND ".join(conditions) if conditions else "1=1"

    # 统一查询SQL - 核心优化点
    unified_query = f"""
        SELECT
            dsm.project_name,
            dsm.supplier,
            dsm.device_type,
            dsm.equipment_sla,
            dsm.expected_time_equipment,
            dsm.estimated_time_delivery,
            dsm.delivery_gap,
            ead.actual_arrival_time,
            pi.po_create_time,
            eprp.ticket_id as production_work_order,
            actd.IsForceEnd,
            rewd.PM,
            rewd.demand_delivery,
            sd.state,
            sd.campus
        FROM delivery_schedule_management_table dsm
        LEFT JOIN equipment_arrival_data ead
            ON dsm.project_name = CONCAT(ead.campus, ead.project_name)
            AND dsm.device_type = ead.equipment_category
        LEFT JOIN payment_info pi
            ON dsm.project_name = pi.project_name
        LEFT JOIN equipment_category_mapping_relationship ecmr
            ON pi.material_name = ecmr.material_category_name
            AND dsm.device_type = ecmr.device_name
        LEFT JOIN equipment_production_racking_process eprp
            ON dsm.project_name = eprp.project_name
            AND dsm.device_type = eprp.device_name
        LEFT JOIN all_construction_ticket_data actd
            ON eprp.ticket_id = actd.TicketId
        LEFT JOIN risk_early_warning_data rewd
            ON dsm.project_name = rewd.project_name
        LEFT JOIN summary_data sd
            ON CONCAT(sd.campus, sd.project_name) = rewd.project_name
        WHERE {where_clause}
        ORDER BY dsm.project_name, dsm.device_type
    """

    try:
        result = db.get_all(unified_query)
    except Exception as e:
        print(f"查询执行失败: {e}")
        return {}

    # 处理查询结果
    if project_name and not state and not PM and not demand_delivery:
        # 单项目模式处理
        data = []
        for row in result:
            # 确保强制结单工单已被过滤
            if row.get('IsForceEnd') == '1':
                continue

            project_name_val = row.get('project_name')
            supplier = row.get('supplier')
            device_type = row.get('device_type')

            # 处理实际到货时间
            actual_arrival_time = row.get('actual_arrival_time')
            if actual_arrival_time and str(actual_arrival_time) != '0000-00-00 00:00:00':
                if isinstance(actual_arrival_time, datetime.datetime):
                    actual_time_str = actual_arrival_time.strftime('%Y-%m-%d')
                else:
                    actual_time_str = str(actual_arrival_time)[:10]
            else:
                actual_time_str = None

            data.append({
                'project_name': f'{project_name_val}-{supplier}',
                'device_type': device_type,
                'equipment_SLA': row.get('equipment_sla'),
                'expected_time_equipment': row.get('expected_time_equipment'),
                'estimated_time_delivery': row.get('estimated_time_delivery'),
                'delivery_GAP': row.get('delivery_gap'),
                'po_create_time': row.get('po_create_time'),
                'production_work_order': row.get('production_work_order'),
                'actual_time_delivery': actual_time_str
            })

        return data
    else:
        # 多项目模式处理
        data = {'13yue': []}
        processed_devices = {}

        for row in result:
            # 确保强制结单工单已被过滤
            if row.get('IsForceEnd') == '1':
                continue

            device_type = row.get('device_type')
            project_name_val = row.get('project_name')

            # 简化的项目key生成
            project_key = re.sub(r'[^\w]', '', project_name_val)[:10]

            # 处理实际到货时间
            actual_arrival_time = row.get('actual_arrival_time')
            if actual_arrival_time and str(actual_arrival_time) != '0000-00-00 00:00:00':
                if isinstance(actual_arrival_time, datetime.datetime):
                    actual_time_str = actual_arrival_time.strftime('%Y-%m-%d')
                else:
                    actual_time_str = str(actual_arrival_time)[:10]
            else:
                actual_time_str = None

            # 查找或创建设备记录
            device_key = device_type
            if device_key not in processed_devices:
                processed_devices[device_key] = {
                    'device_type': device_type,
                    'equipment_SLA': row.get('equipment_sla'),
                    'original_project_name': project_name_val
                }
                data['13yue'].append(processed_devices[device_key])

            # 添加项目特定的字段
            device_record = processed_devices[device_key]
            device_record[f'{project_key}delivery_GAP'] = row.get('delivery_gap')
            device_record[f'{project_key}estimated_time_delivery'] = row.get('estimated_time_delivery')
            device_record[f'{project_key}expected_time_equipment'] = row.get('expected_time_equipment')
            device_record[f'{project_key}production_work_order'] = row.get('production_work_order')
            device_record[f'{project_key}actual_time_delivery'] = actual_time_str
            device_record[f'{project_key}po_create_time'] = row.get('po_create_time')

        return data
```

## 5. 总结

本优化方案主要针对 `DeliveryScheduleManagement` 类的 `tabular_data_query` 函数进行优化，删除了重复代码，保留了优化版本的实现，确保强制结单过滤条件正确应用，并统一了数据处理逻辑。通过这些优化，可以提高查询性能，改善数据展示异常问题，提高代码可维护性。

优化后的代码将更加简洁、高效，能够更好地满足业务需求。
