from iBroker.lib.sdk import gnetops
from iBroker.sdk.workflow.workflow import WorkflowContinue, WorkflowVarUpdate

class TicketProcessingTools(object):
    def reverse_the_waiting_state(self,ticket_id: str, variables: dict):
        """
            扭转空白方法等待节点状态，仅适用于串行节点
        """
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                },
                "SearchCondition": {
                    "TicketId": [ticket_id]
                },
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        detail = query_result.get('List')[0]
        data_instanceId = {
            "InstanceId": detail['InstanceId']
        }
        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        task_status = res[-1].get('TaskStatus')
        if task_status == "进行中":
            task_id = res[-1].get('TaskId')
            WorkflowContinue(task_id=task_id, variables_map=variables).call()
            return {"code": 0, "msg": "success"}
        else:
            raise Exception(f"工单节点状态有误，扭转失败")


    def process_variable_modify(self,ticket_id: str, variables: dict):
        """
            修改对应工单流程变量
        """
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                },
                "SearchCondition": {
                    "TicketId": [ticket_id]
                },
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        detail = query_result.get('List')[0]
        instance_id = detail['InstanceId']
        WorkflowVarUpdate(instance_id=instance_id, variables=variables).call()
        return {"code": 0, "msg": "success"}
