import datetime
import uuid

import numpy as np
import pandas as pd
from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib


class GetDataInLibrary(object):
    """
    获取库中数据
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def flash_in_percentage_of_construct_plan(self):
        sql_is_hire = "SELECT project_name,construction_mode FROM risk_early_warning_data"
        db = mysql.new_mysql_instance("tbconstruct")
        sql_result = db.query(sql_is_hire)
        rent_project_list = []
        for item in sql_result:
            if item.get("construction_mode", "") != "自建":
                rent_project_list.append(item.get("project_name"))

        data_proportion = {
            "项目启动": "0.02",
            "施工准备": "0.1",
            "安装施工": "0.75",
            "测试验证": "0.1",
            "验收转维": "0.03"
        }

        for project_name in rent_project_list:
            update_list = []
            sql_construction_plan = (
                    "SELECT serial_number,work_content,project_progress,start_time,completion_time,first_level_proportion,"
                    "responsible_person,input_object,output_object,construction_attribute,project_name,second_level_proportion"
                    "third_level_proportion,not_involved "
                    "FROM construct_plan_data WHERE project_name='%s'"
                    % project_name
            )
            construction_plan_data = db.query(sql_construction_plan)
            if construction_plan_data:
                for item in construction_plan_data:
                    serial_number = item.get("serial_number")
                    if serial_number.count(".") == 0:
                        current_proportion = data_proportion.get(item.get("work_content"))
                        update_list.append({
                            "serial_number": str(item.get("serial_number")),
                            "work_content": item.get("work_content") or "",
                            "project_progress": item.get("project_progress") or "",
                            "start_time": item.get("start_time") or "",
                            "completion_time": item.get("completion_time") or "",
                            "responsible_person": item.get("responsible_person") or "",
                            "input_object": item.get("input_object") or "",
                            "output_object": item.get("output_object") or "",
                            "construction_attribute": item.get("construction_attribute") or "",
                            "project_name": project_name or "",
                            "first_level_proportion": current_proportion,
                            "second_level_proportion": "0",
                            "third_level_proportion": "0",
                            "not_involved": item.get("not_involved") or "0",
                        })
            response_data = gnetops.request(
                action="Project",
                method="UpdateConstructPlanData",
                ext_data={
                    "project_name": project_name,
                    "update_list": update_list,
                    "ticket_id": "0",
                    "flow_name": "测试刷入",
                    "operator": "v_zongxiyu"
                },
                scheme="ifob-infrastructure",
            )
            break
        return update_list

    def get_master_control_plan_data(self):
        """
        获取总控计划数据
        （旧总控计划数据）
        """
        old_zkjh_list = []
        first_list = []
        second_list = []
        third_list = []
        foured_list = []
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        # 查询项目原总控计划数据
        query_sql = f"""
                    SELECT @row_number := @row_number + 1 AS new_id, t.* 
                    FROM 
                    (SELECT * 
                    FROM construct_plan_data 
                    WHERE project_name = '{project_name}' 
                    ORDER BY id) AS t, (SELECT @row_number := 0) AS r
                """
        result = db.get_all(query_sql)
        for row in result:
            serial_number = row.get("serial_number", "None")
            if serial_number is not None:
                if serial_number != "None" and serial_number.count(".") == 0:
                    first_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 1:
                    second_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 2:
                    third_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 3:
                    foured_list.append(row)
        if not result:
            raise Exception(f"{project_name}-未找到项目总控计划数据")
        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                for third_item in third_list:
                    third_children = []
                    for fourth_item in foured_list:
                        if fourth_item["serial_number"].startswith(
                                third_item["serial_number"]
                        ):
                            third_children.append(
                                {
                                    "id": fourth_item.get("new_id"),
                                    "serial_number": fourth_item.get("serial_number"),
                                    "work_content": fourth_item.get("work_content"),
                                    "project_progress": fourth_item.get("project_progress"),
                                    "start_time": fourth_item.get("start_time"),
                                    "completion_time": fourth_item.get("completion_time"),
                                    "responsible_person": fourth_item.get(
                                        "responsible_person"
                                    ),
                                    "input_object": fourth_item.get("input_object"),
                                    "output_object": fourth_item.get("output_object"),
                                    "construction_attribute": fourth_item.get(
                                        "construction_attribute"
                                    ),
                                    "not_involved": fourth_item.get("whethe_involves", ""),
                                    "allowEdit": False,
                                    "project_progress_modified": False,
                                    "start_time_modified": False,
                                    "completion_time_modified": False,
                                    "responsible_person_modified": False,
                                    "not_involved_modified": False
                                }
                            )
                    if third_item["serial_number"].startswith(
                            second_item["serial_number"]
                    ):
                        second_children.append(
                            {
                                "id": third_item.get("new_id"),
                                "serial_number": third_item.get("serial_number"),
                                "work_content": third_item.get("work_content"),
                                "project_progress": third_item.get("project_progress"),
                                "start_time": third_item.get("start_time"),
                                "completion_time": third_item.get("completion_time"),
                                "responsible_person": third_item.get(
                                    "responsible_person"
                                ),
                                "input_object": third_item.get("input_object"),
                                "output_object": third_item.get("output_object"),
                                "construction_attribute": third_item.get(
                                    "construction_attribute"
                                ),
                                "not_involved": third_item.get("whethe_involves", ""),
                                "allowEdit": False,
                                "project_progress_modified": False,
                                "start_time_modified": False,
                                "completion_time_modified": False,
                                "responsible_person_modified": False,
                                "children": third_children,
                                "not_involved_modified": False,
                                "third_level_proportion": third_item.get("proportion_three"),
                                "third_level_proportion_modified": False
                            }
                        )
                if second_item["serial_number"].startswith(first_item["serial_number"]):
                    first_children.append(
                        {
                            "id": second_item.get("new_id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "responsible_person": second_item.get("responsible_person"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get(
                                "construction_attribute"
                            ),
                            "not_involved": second_item.get("whethe_involves", ""),
                            "allowEdit": False,
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "children": second_children,
                            "not_involved_modified": False
                        }
                    )
            old_zkjh_list.append(
                {
                    "id": first_item.get("new_id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "responsible_person": first_item.get("responsible_person"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "not_involved": first_item.get("whethe_involves", ""),
                    "allowEdit": False,
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False,
                    "children": first_children,
                    "not_involved_modified": False
                }
            )
        variables = {"old_zkjh_list": old_zkjh_list}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_project_staff_information(self):
        """
        获取项目人员信息
        """
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        # 获取项管、监理、集成商
        query_sql1 = (
            "SELECT project_name, role, account "
            "FROM project_role_account "
            f"WHERE project_name = '{project_name}'"
            "and del_flag = 0"
        )
        query_sql2 = (
            "SELECT PM "
            "FROM risk_early_warning_data "
            f"WHERE project_name = '{project_name}'"
        )
        project_result1 = db.get_all(query_sql1)
        project_result2 = db.get_all(query_sql2)
        account_dict = {}
        for row in project_result2:
            PM = row.get("PM")
        for row in project_result1:
            for role in ["集成商-项目经理", "项管-项目经理", "监理-总监理工程师",
                         "总包-项目经理", "合建方-项目经理"]:
                if role in row["role"]:
                    account_dict[role] = row["account"]
        integrator = account_dict.get("集成商-项目经理")
        if not integrator:
            integrator = account_dict.get("总包-项目经理")
        item_tube = account_dict.get("项管-项目经理")
        if not item_tube:
            item_tube = account_dict.get("合建方-项目经理")
        supervision_management = account_dict.get("监理-总监理工程师")

        variables = {
            "PM": PM,
            "integrator": integrator,
            "item_tube": item_tube,
            "supervision_management": supervision_management,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def time_disposal(self, zkjh_table):
        """
        时间处理
        """
        data = []
        for row in zkjh_table:
            start_time = row.get("start_time")
            completion_time = row.get("completion_time")

            # start_time
            if start_time:
                delta = datetime.timedelta(days=int(start_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                start_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                start_time = ''

            # completion_time
            if completion_time:
                delta = datetime.timedelta(days=int(completion_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                completion_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                completion_time = ''

            not_involved = row.get("not_involved") if row.get("not_involved") == "否" else "是"

            data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress", ''),
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "responsible_person": row.get("responsible_person", ''),
                    "input_object": row.get("input_object", ''),
                    "output_object": row.get("output_object", ''),
                    "construction_attribute": row.get("construction_attribute", ''),
                    "not_involved": not_involved,
                    "third_level_proportion": row.get("third_level_proportion", ''),
                }
            )
        return data

    def time_disposal_new(self, zkjh_table, project_name):
        """
        时间处理：解析 Excel 序列号时间、构建树状结构、更新父级时间
        """
        data = []

        # 统一 serial_number 为字符串格式
        for row in zkjh_table:
            not_involved = row.get("not_involved")

            # 如果 work_content 是 "其他"，且 not_involved 没有填写，则默认设为 "否"
            if row.get("work_content") == "其他" and not row.get("not_involved"):
                not_involved = "否"
            else:
                not_involved = not_involved or "是"

            row["serial_number"] = str(row["serial_number"])
            data.append({
                "serial_number": row["serial_number"],
                "work_content": row["work_content"],
                "project_progress": row.get("project_progress"),
                "start_time": row.get("start_time"),
                "completion_time": row.get("completion_time"),
                "responsible_person": row.get("responsible_person"),
                "input_object": row.get("input_object"),
                "output_object": row.get("output_object"),
                "construction_attribute": row.get("construction_attribute"),
                "not_involved": not_involved,
                "third_level_proportion": row.get("third_level_proportion"),
            })

        url = config.get_config_string("master_control_plan_template")
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel解析总控计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl', sheet_name='总控计划模版')
        df = df.replace({np.nan: None})
        template_data = self.extract_template_structure(df)

        db = mysql.new_mysql_instance("tbconstruct")
        sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                       f"WHERE project_name='{project_name}'")
        is_hire = False if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else True
        if not is_hire:
            validation_result = self.self_validate_plan_against_template(data, template_data)
        else:
            validation_result = self.hire_validate_plan_against_template(data, template_data)
        if validation_result.get("code") == -1:
            return validation_result

        # 构建树结构
        tree = self.build_tree(data)
        processed_tree = [self.update_node_times(node) for node in tree]
        plan_data, unspecified_str = self.extract_flat_data(processed_tree)
        if len(unspecified_str) != len("涉及但未填写开始、结束时间：<br>"):
            return {"code": -1, "msg": unspecified_str}

        return plan_data

    def build_tree(self, data, parent_id=None):
        """构建树结构"""
        tree = []
        for item in data:
            serial = item["serial_number"]
            if parent_id is None and "." not in serial:
                node = {**item, "children": []}
                children = [n for n in data if n["serial_number"].startswith(serial + ".") and
                            len(n["serial_number"].split(".")) == len(serial.split(".")) + 1]
                if children:
                    node["children"] = self.build_tree(data, serial)
                tree.append(node)
            elif parent_id and serial.startswith(parent_id + ".") and \
                    len(serial.split(".")) == len(parent_id.split(".")) + 1:
                node = {**item, "children": []}
                children = [n for n in data if n["serial_number"].startswith(serial + ".") and
                            len(n["serial_number"].split(".")) == len(serial.split(".")) + 1]
                if children:
                    node["children"] = self.build_tree(data, serial)
                tree.append(node)
        return tree

    def parse_date(self, date_str):
        """将字符串日期转换为 datetime 对象以便比较"""
        if date_str:
            for fmt in ("%Y-%m-%d", "%Y/%m/%d", "%Y年%m月%d日"):
                try:
                    return datetime.datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
        return None

    def update_node_times(self, node):
        children = node.get("children", [])

        if not children:
            return node

        start_times = []
        completion_times = []

        for child in children:
            # 递归更新子节点的时间
            updated_child = self.update_node_times(child)

            start = self.parse_date(updated_child.get("start_time"))
            completion = self.parse_date(updated_child.get("completion_time"))

            if start:
                start_times.append(start)
            if completion:
                completion_times.append(completion)

        # 取最早开始时间和最晚完成时间
        if start_times:
            earliest_start = min(start_times).strftime("%Y-%m-%d")
            node["start_time"] = earliest_start
        if completion_times:
            latest_completion = max(completion_times).strftime("%Y-%m-%d")
            node["completion_time"] = latest_completion

        return node

    def extract_flat_data(self, tree):
        """
        深度优先遍历树结构，提取所有节点并组成一个扁平列表
        """
        result = []
        unspecified_list = []

        def is_valid_date(date_value):
            return isinstance(date_value, datetime.datetime) or date_value is None

        def dfs(node):
            # 复制节点数据，避免修改原始数据
            node_copy = {key: value for key, value in node.items() if not isinstance(value, list)}
            if node_copy.get("not_involved") == "是":
                if not is_valid_date(node_copy["start_time"]) or not is_valid_date(node_copy["completion_time"]):
                    unspecified_list.append(
                        f"{node_copy.get('serial_number')}、{node_copy.get('work_content')}<br>"
                    )
            result.append(node_copy)

            # 递归处理子节点
            for child in node.get("children", []):
                dfs(child)

        for root_node in tree:
            dfs(root_node)

        if unspecified_list:
            unspecified_str = "涉及但未填写开始、结束时间：<br>" + ''.join(unspecified_list)
        else:
            unspecified_str = "涉及但未填写开始、结束时间：<br>"

        return result, unspecified_str

    def extract_template_structure(self, df):
        """
        提取 Excel 模板中的一、二、三级标题结构
        返回格式：
            [
                {"serial": "1", "content": "一级标题"},
                {"serial": "1.1", "content": "二级标题"},
                {"serial": "1.1.1", "content": "三级标题"},
                ...
            ]
        """
        template_data = []

        for index, row in df.iterrows():
            serial_number = str(row.get("序号")) if pd.notna(row.get("序号")) else ""
            work_content = str(row.get("工作内容")) if pd.notna(row.get("工作内容")) else ""

            # 判断是否为一二三级标题
            if serial_number.count(".") == 0 or serial_number.count(".") == 1 or serial_number.count(".") == 2:
                template_data.append({
                    "serial_number": serial_number,
                    "work_content": work_content
                })

        return template_data

    def work_content_empty_check(self, plan_data):
        errors = []
        serial_number_set = set()

        def is_valid_serial(serial):
            try:
                parts = serial.split('.')
                for p in parts:
                    int(p)  # 每一部分都必须是整数
                return True
            except (ValueError, AttributeError):
                return False

        for item in plan_data:
            serial_number = item["serial_number"]
            work_content = item["work_content"]

            if not isinstance(serial_number, str) or not is_valid_serial(serial_number):
                errors.append(f"编号 {serial_number} 不合法，请检查格式是否为数字或类似 '1.2' 的结构<br>")
                continue

            if serial_number in serial_number_set:
                errors.append(f"编号 {serial_number} 出现重复<br>")
            else:
                serial_number_set.add(serial_number)

            not_involved = item["not_involved"]
            if not_involved == "是":
                if "." in serial_number:  # 非一级节点
                    if not serial_number or serial_number.strip() == "":
                        errors.append(f"编号为空或无效，请检查数据<br>")
                    if not work_content or work_content.strip() == "":
                        errors.append(f"编号 {serial_number} 的工作内容为空<br>")
        return errors

    def self_validate_plan_against_template(self, data, template_data):
        """
        plan_data: 传入的计划数据（经过 time_disposal_new 处理后的 flat list）
        template_data: Excel 模板中提取的标题结构
        返回：校验结果 {'code': 0/-1, 'msg': ''}
        """
        plan_dict = {item["serial_number"]: item for item in data}
        template_dict = {item["serial_number"]: item for item in template_data}

        errors = self.work_content_empty_check(data)

        if errors:
            error_msg = "结构校验失败：<br>" + "".join(errors)
            return {"code": -1, "msg": error_msg}

        level_counts = {
            1: 1,  # 一级编号计数器（应从1开始递增）
            2: {},  # 二级编号计数器（按父编号记录当前最大子编号）
            3: {}  # 三级编号计数器（按父编号记录当前最大孙编号）
        }

        for temp_item in template_data:
            temp_serial = temp_item["serial_number"]
            temp_content = temp_item["work_content"]

            plan_item = plan_dict.get(temp_serial)
            if not plan_item:
                errors.append(f"模板中存在编号 {temp_serial}，但计划中未找到<br>")
                continue

            if plan_item["work_content"] != temp_content:
                errors.append(
                    f"编号 {temp_serial} 内容不一致：模板为 '{temp_content}'，计划为 '{plan_item['work_content']}'<br>")

            parts = temp_serial.split(".")
            level = len(parts)

            if level == 1:
                current = int(parts[0])
                if current != level_counts[level]:
                    errors.append(f"一级编号 {temp_serial} 应为 {level_counts[level]}<br>")
                level_counts[level] += 1

            elif level == 2:
                parent = parts[0]
                current = int(parts[1])
                expected = level_counts[level].get(parent, 1)  # 二级从 1 开始
                if current != expected:
                    errors.append(f"二级编号 {temp_serial} 应为 {parent}.{expected}<br>")
                level_counts[level][parent] = expected + 1

            elif level == 3:
                parent = ".".join(parts[:2])
                current = int(parts[2])
                expected = level_counts[level].get(parent, 1)  # 三级从 1 开始
                if current != expected:
                    errors.append(f"三级编号 {temp_serial} 应为 {parent}.{expected}<br>")
                level_counts[level][parent] = expected + 1

            if errors:
                error_msg = "结构校验失败：<br>" + "".join(errors) + "如需要增加工作项可在'其他'下增加四级标题"
                return {"code": -1, "msg": error_msg}

        return {"code": 0, "msg": "结构校验通过"}

    def hire_validate_plan_against_template(self, data, template_data):
        """
        plan_data: 传入的计划数据（经过 time_disposal_new 处理后的 flat list）
        template_data: Excel 模板中提取的标题结构
        返回：校验结果 {'code': 0/-1, 'msg': ''}
        """
        plan_dict = {item["serial_number"]: item for item in data}
        template_dict = {item["serial_number"]: item for item in template_data}

        errors = self.work_content_empty_check(data)

        if errors:
            error_msg = "结构校验失败：<br>" + "".join(errors)
            return {"code": -1, "msg": error_msg}

        for temp_serial, temp_item in template_dict.items():
            if "." not in temp_serial:  # 仅处理一级标题
                plan_item = plan_dict.get(temp_serial)
                if not plan_item:
                    errors.append(f"模板中存在一级编号 {temp_serial}，但计划中未找到<br>")
                    continue

                if plan_item["work_content"] != temp_item["work_content"]:
                    errors.append(
                        f"一级编号 {temp_serial} 内容不一致：模板为 '{temp_item['work_content']}'，"
                        f"计划为 '{plan_item['work_content']}'<br>"
                    )

        # 递增性校验（不允许跳号）
        serial_list = sorted(
            [item["serial_number"] for item in data],
            key=lambda x: [int(p) for p in x.split(".")]
        )

        expected_counters = {
            "level_1": 1  # 一级编号从 1 开始
        }
        errors = []

        for serial in serial_list:
            parts = serial.split(".")
            level = len(parts)

            if level == 1:
                current_num = int(parts[0])
                expected_num = expected_counters["level_1"]

                if current_num != expected_num:
                    errors.append(f"一级编号应为 {expected_num}，实际为 {serial}<br>")
                expected_counters["level_1"] += 1  # 下一个应+1

            elif level == 2:
                parent = parts[0]
                current_num = int(parts[1])

                # 初始化二级计数器
                if parent not in expected_counters:
                    expected_counters[parent] = 1

                expected_num = expected_counters[parent]

                if current_num != expected_num:
                    errors.append(f"二级编号 {serial} 应为 {parent}.{expected_num}<br>")
                expected_counters[parent] += 1

            elif level == 3:
                parent = ".".join(parts[:2])
                current_num = int(parts[2])

                # 初始化三级计数器
                if parent not in expected_counters:
                    expected_counters[parent] = 1

                expected_num = expected_counters[parent]

                if current_num != expected_num:
                    errors.append(f"三级编号 {serial} 应为 {parent}.{expected_num}<br>")
                expected_counters[parent] += 1

            if errors:
                error_msg = "结构校验失败：<br>" + "".join(errors)
                return {"code": -1, "msg": error_msg}

        return {"code": 0, "msg": "结构校验通过"}

    def analyze_control_plan_bak(self, project_name, control_plan_url):
        """
            excel解析上传总控计划
        """

        ticket_id = self.ctx.variables.get("ticket_id")
        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')

        relative_url = control_plan_url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)

        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl', sheet_name='总控计划模版')
        df = df.replace({np.nan: None})
        """
            所有数据存库
        """
        insert_data = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            work_content = row['工作内容']
            plan_duration_construction = row['工期']
            start_time = row['计划开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['计划开始时间（年/月/日）']) else None
            completion_time = row['计划完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['计划完成时间（年/月/日）']) else None
            responsible_person = row['责任人（开通账号人员）']
            output_object = row['输出物']
            input_object = row['输入物']
            construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
            not_involved = row.get('是否涉及（是/否）')
            insert_data.append({
                'ticket_id': ticket_id,
                'project_name': project_name,
                'serial_number': serial_number,
                'work_content': work_content,
                'plan_duration_construction': plan_duration_construction,
                'start_time': start_time,
                'completion_time': completion_time,
                'responsible_person': responsible_person,
                'output_object': output_object,
                'input_object': input_object,
                'construction_attribute': construction_attribute,
                "not_involved": not_involved
            })
        time_verification = GetDataInLibrary()
        zkjh_table = time_verification.time_disposal_new(insert_data, project_name)
        msg = ""
        # 时间校验判断
        verification_passed = 1
        # 新增判断：是否是错误信息？
        if isinstance(zkjh_table, dict) and zkjh_table.get("code") == -1:
            # 返回错误提示给前端
            verification_passed = 0
            msg = zkjh_table.get("msg")
        # 确保 zkjh_table 是列表
        if not isinstance(zkjh_table, list):
            verification_passed = 0
        if verification_passed == 1:
            return {"verification_passed": 1, "msg": msg}
        else:
            return {"verification_passed": 0, "msg": msg}


class ImportTheOverallControlPlan(AjaxTodoBase):
    """
    导入总控计划
    （新总控计划数据）
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        zkjh_table = process_data.get("zkjh_table")
        time_verification = GetDataInLibrary()
        project_name = self.ctx.variables.get("project_name")
        zkjh_table = time_verification.time_disposal_new(zkjh_table, project_name)
        # 新增判断：是否是错误信息？
        if isinstance(zkjh_table, dict) and zkjh_table.get("code") == -1:
            # 返回错误提示给前端
            return {"code": -1, "msg": zkjh_table["msg"]}

        # 确保 zkjh_table 是列表
        if not isinstance(zkjh_table, list):
            return {"code": -1, "msg": "数据格式异常，请检查输入内容"}

        # 正常返回
        for index, row in enumerate(zkjh_table, start=1):
            row["id"] = index

        new_zkjh_list = []
        first_list = []
        second_list = []
        third_list = []
        fourth_list = []

        for row in zkjh_table:
            serial_number = str(row.get("serial_number", "None"))
            if serial_number != "None":
                if serial_number.count(".") == 0:
                    first_list.append(row)
                elif serial_number.count(".") == 1:
                    second_list.append(row)
                elif serial_number.count(".") == 2:
                    third_list.append(row)
                elif serial_number.count(".") == 3:
                    fourth_list.append(row)

        url = process_data.get("template_url")
        url = config.get_config_string("master_control_plan_template")
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel解析总控计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl', sheet_name='总控计划模版')
        df = df.replace({np.nan: None})

        error_serial_number = []
        error_message = ""

        processed_second_ids = set()  # 记录已处理的二级标题ID
        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                # 判断是否为当前一级标题下的二级标题
                if str(second_item["serial_number"]).startswith(str(first_item["serial_number"])):
                    # 筛选出属于当前二级标题的所有三级标题
                    matching_third_items = [
                        item for item in third_list
                        if str(item["serial_number"]).startswith(str(second_item["serial_number"]))
                    ]
                #     third_level_proportion_sum = 0
                #     # 计算三级标题的权重总和
                #     for third_item in matching_third_items:
                #         not_involved = third_item.get("not_involved", "")
                #         # 忽略 not_involved 为 "否" 的三级标题
                #         if not_involved == "否":
                #             continue
                #         third_level_proportion = third_item.get("third_level_proportion", 0)
                #         if not third_level_proportion:
                #             third_level_proportion = 0
                #         try:
                #             third_level_proportion = float(third_level_proportion)
                #         except ValueError:
                #             error_message += f"三级标题 {third_item['serial_number']} 的权重无法转换为浮点数，请检查<br>"
                #             third_level_proportion = 0
                #
                #         third_level_proportion_sum += third_level_proportion
                #
                #     # 检查三级标题的权重总和是否为 1（允许小范围浮点误差）或者 0
                #     if abs(round(third_level_proportion_sum, 2) - 1) > 1e-6 and abs(third_level_proportion_sum) > 1e-6:
                #
                #         # 从 df 中提取相同工作内容的三级占比，并重新计算总和
                #         updated_third_level_proportions = []  # 存储从 df 提取的三级占比
                #         for third_item in matching_third_items:
                #             work_content = third_item.get("work_content")
                #             matching_rows = df[df["工作内容"] == work_content]  # 匹配工作内容
                #             for _, row in matching_rows.iterrows():
                #                 third_level_proportion_from_df = row.get("三级占比")
                #                 if pd.notna(third_level_proportion_from_df):
                #                     try:
                #                         third_level_proportion_from_df = float(third_level_proportion_from_df)
                #                         updated_third_level_proportions.append(third_level_proportion_from_df)
                #                     except ValueError:
                #                         error_message += (f"模板文件工作内容 '{work_content}' "
                #                                           f"的三级占比无法转换为浮点数，请联系开发人员<br>")
                #
                #         # 重新计算三级占比总和
                #         updated_third_level_proportion_sum = sum(updated_third_level_proportions)
                #
                #         # 判断重新计算的总和是否为 1
                #         if abs(updated_third_level_proportion_sum - 1) <= 1e-6:  # 允许小范围浮点误差
                #             # 更新原流程中的数据
                #             for third_item in matching_third_items:
                #                 not_involved = third_item.get("not_involved", "")
                #                 # 忽略 not_involved 为 "否" 的三级标题
                #                 if not_involved == "否":
                #                     continue
                #                 work_content = third_item.get("work_content")
                #                 matching_row = df[df["工作内容"] == work_content]
                #                 if not matching_row.empty:
                #                     updated_third_level_proportion = matching_row.iloc[0].get("三级占比")
                #                     if pd.notna(updated_third_level_proportion):
                #                         third_item["third_level_proportion"] = float(updated_third_level_proportion)
                #         elif abs(updated_third_level_proportion_sum) <= 1e-6:
                #             pass
                #         else:
                #             error_message += (f"二级标题 {second_item['serial_number']} 下属三级标题权重总和为 "
                #                               f"{round(third_level_proportion_sum, 2)}<br>")

                    # 处理四级标题
                    for third_item in matching_third_items:
                        third_children = []
                        for four_item in fourth_list:
                            if str(four_item["serial_number"]).startswith(str(third_item["serial_number"])):
                                third_children.append(
                                    {
                                        "id": four_item.get("id"),
                                        "serial_number": four_item.get("serial_number"),
                                        "work_content": four_item.get("work_content"),
                                        "project_progress": four_item.get("project_progress"),
                                        "start_time": four_item.get("start_time"),
                                        "completion_time": four_item.get("completion_time"),
                                        "responsible_person": four_item.get("responsible_person"),
                                        "input_object": four_item.get("input_object"),
                                        "output_object": four_item.get("output_object"),
                                        "construction_attribute": four_item.get("construction_attribute"),
                                        "not_involved": four_item.get("not_involved", ""),
                                        "allowEdit": False,
                                        "project_progress_modified": False,
                                        "start_time_modified": False,
                                        "completion_time_modified": False,
                                        "responsible_person_modified": False,
                                        "not_involved_modified": False,
                                    }
                                )
                        second_children.append({
                            "id": third_item.get("id"),
                            "serial_number": third_item.get("serial_number"),
                            "work_content": third_item.get("work_content"),
                            "project_progress": third_item.get("project_progress"),
                            "start_time": third_item.get("start_time"),
                            "completion_time": third_item.get("completion_time"),
                            "responsible_person": third_item.get("responsible_person"),
                            "input_object": third_item.get("input_object"),
                            "output_object": third_item.get("output_object"),
                            "construction_attribute": third_item.get("construction_attribute"),
                            "not_involved": third_item.get("not_involved", ""),
                            "allowEdit": False,
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "children": third_children,
                            "not_involved_modified": False,
                            "third_level_proportion": third_item.get("third_level_proportion"),
                            "third_level_proportion_modified": False
                        })

                    first_children.append(
                        {
                            "id": second_item.get("id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "responsible_person": second_item.get("responsible_person"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get(
                                "construction_attribute"
                            ),
                            "not_involved": second_item.get("not_involved", ""),
                            "allowEdit": False,
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "children": second_children,
                            "not_involved_modified": False,
                        }
                    )
            new_zkjh_list.append(
                {
                    "id": first_item.get("id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "responsible_person": first_item.get("responsible_person"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "not_involved": first_item.get("not_involved", ""),
                    "allowEdit": False,
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False,
                    "children": first_children,
                    "not_involved_modified": False,
                }
            )
        if error_message:
            return {"code": -1, "msg": error_message}
        variables = {
            "zkjh_table": zkjh_table,
            "new_zkjh_list": new_zkjh_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ModificationOverallControlPlan(AjaxTodoBase):
    """
    修改总控计划
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        insert_data = []
        new_data = []
        old_data = []
        first_list = []
        second_list = []
        third_list = []
        fourth_list = []  # 新增四级标题列表
        new_zkjh_list = []
        reason_for_modification = process_data.get("reason_for_modification")
        project_name = self.ctx.variables.get("project_name")
        new_zkjh = self.ctx.variables.get("new_zkjh_list")
        old_zkjh = self.ctx.variables.get("old_zkjh_list")

        # 新总控计划数据存储
        for row in new_zkjh:
            children1 = row.get("children")
            new_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "not_involved": row.get("not_involved", ""),
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False,
                    "not_involved_modified": False,
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                new_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "not_involved": item1.get("not_involved", ""),
                        "project_progress_modified": False,
                        "start_time_modified": False,
                        "completion_time_modified": False,
                        "responsible_person_modified": False,
                        "not_involved_modified": False,
                    }
                )
                for item2 in children2:
                    new_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "not_involved": item2.get("not_involved", ""),
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "not_involved_modified": False,
                            "third_level_proportion": item2.get("third_level_proportion"),
                            "third_level_proportion_modified": False
                        }
                    )
                    # 处理四级标题
                    children3 = item2.get("children")
                    for item3 in children3:
                        new_data.append(
                            {
                                "serial_number": item3.get("serial_number"),
                                "work_content": item3.get("work_content"),
                                "project_progress": item3.get("project_progress"),
                                "start_time": item3.get("start_time"),
                                "completion_time": item3.get("completion_time"),
                                "responsible_person": item3.get("responsible_person"),
                                "input_object": item3.get("input_object"),
                                "output_object": item3.get("output_object"),
                                "construction_attribute": item3.get("construction_attribute"),
                                "not_involved": item3.get("not_involved", ""),
                                "project_progress_modified": False,
                                "start_time_modified": False,
                                "completion_time_modified": False,
                                "responsible_person_modified": False,
                                "not_involved_modified": False,
                            }
                        )

        # 旧总控计划数据存储
        for row in old_zkjh:
            children1 = row.get("children")
            old_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "not_involved": row.get("not_involved", ""),
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False,
                    "not_involved_modified": False,
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                old_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "not_involved": item1.get("not_involved", ""),
                        "project_progress_modified": False,
                        "start_time_modified": False,
                        "completion_time_modified": False,
                        "responsible_person_modified": False,
                        "not_involved_modified": False,
                    }
                )
                for item2 in children2:
                    old_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "not_involved": item2.get("not_involved", ""),
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "not_involved_modified": False,
                            "third_level_proportion": item2.get("third_level_proportion"),
                            "third_level_proportion_modified": False
                        }
                    )
                    # 处理四级标题
                    children3 = item2.get("children")
                    if children3 is not None:  # Check if children3 is not None
                        for item3 in children3:
                            old_data.append(
                                {
                                    "serial_number": item3.get("serial_number"),
                                    "work_content": item3.get("work_content"),
                                    "project_progress": item3.get("project_progress"),
                                    "start_time": item3.get("start_time"),
                                    "completion_time": item3.get("completion_time"),
                                    "responsible_person": item3.get("responsible_person"),
                                    "input_object": item3.get("input_object"),
                                    "output_object": item3.get("output_object"),
                                    "construction_attribute": item3.get("construction_attribute"),
                                    "not_involved": item3.get("not_involved", ""),
                                    "project_progress_modified": False,
                                    "start_time_modified": False,
                                    "completion_time_modified": False,
                                    "responsible_person_modified": False,
                                    "not_involved_modified": False,
                                }
                            )

        # 总控计划对比
        for new_item, old_item in zip(new_data, old_data):
            if new_item["start_time"] != old_item["start_time"]:
                new_item["start_time_modified"] = True

            if new_item["completion_time"] != old_item["completion_time"]:
                new_item["completion_time_modified"] = True

            if new_item.get("responsible_person") != old_item["responsible_person"]:
                new_item["responsible_person_modified"] = True

            # 计算project_progress
            if (
                    new_item["start_time"] is not None
                    and new_item["completion_time"] is not None
                    and isinstance(new_item["start_time"], str)
                    and isinstance(new_item["completion_time"], str)
                    and new_item["start_time"].strip() != ""
                    and new_item["completion_time"].strip() != ""
            ):
                start_time = datetime.datetime.strptime(
                    new_item["start_time"], "%Y-%m-%d"
                )
                completion_time = datetime.datetime.strptime(
                    new_item["completion_time"], "%Y-%m-%d"
                )
                project_progress = (completion_time - start_time).days + 1
                new_item["project_progress"] = str(project_progress)

            # 对比project_progress和old_zkjh_list中的值
            if new_item["project_progress"] != old_item["project_progress"]:
                new_item["project_progress_modified"] = True

            # 对此是否涉及字段
            if new_item["not_involved"] != old_item["not_involved"]:
                new_item["not_involved_modified"] = True

            # 对比三级占比字段
            if new_item.get("third_level_proportion") != old_item.get("third_level_proportion"):
                new_item["third_level_proportion_modified"] = True

        for index, row in enumerate(new_data, start=1):
            row["id"] = index

        # 新总控计划组合
        for row in new_data:
            serial_number = str(row.get("serial_number", "None"))
            if serial_number is not None:
                if serial_number != "None" and serial_number.count(".") == 0:
                    first_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 1:
                    second_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 2:
                    third_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 3:  # 处理四级标题
                    fourth_list.append(row)

        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                for third_item in third_list:
                    third_children = []  # 新增第三级子项列表
                    for fourth_item in fourth_list:  # 处理四级标题
                        if str(fourth_item["serial_number"]).startswith(
                                str(third_item["serial_number"])
                        ):
                            third_children.append(
                                {
                                    "id": fourth_item.get("id"),
                                    "serial_number": fourth_item.get("serial_number"),
                                    "work_content": fourth_item.get("work_content"),
                                    "project_progress": fourth_item.get("project_progress"),
                                    "start_time": fourth_item.get("start_time"),
                                    "completion_time": fourth_item.get("completion_time"),
                                    "responsible_person": fourth_item.get("responsible_person"),
                                    "input_object": fourth_item.get("input_object"),
                                    "output_object": fourth_item.get("output_object"),
                                    "construction_attribute": fourth_item.get("construction_attribute"),
                                    "allowEdit": fourth_item.get("allowEdit"),
                                    "not_involved": fourth_item.get("not_involved"),
                                    "project_progress_modified": fourth_item.get("project_progress_modified"),
                                    "start_time_modified": fourth_item.get("start_time_modified"),
                                    "completion_time_modified": fourth_item.get("completion_time_modified"),
                                    "responsible_person_modified": fourth_item.get("responsible_person_modified"),
                                }
                            )
                    if str(third_item["serial_number"]).startswith(
                            str(second_item["serial_number"])
                    ):
                        second_children.append(
                            {
                                "id": third_item.get("id"),
                                "serial_number": third_item.get("serial_number"),
                                "work_content": third_item.get("work_content"),
                                "project_progress": third_item.get("project_progress"),
                                "start_time": third_item.get("start_time"),
                                "completion_time": third_item.get("completion_time"),
                                "responsible_person": third_item.get("responsible_person"),
                                "input_object": third_item.get("input_object"),
                                "output_object": third_item.get("output_object"),
                                "construction_attribute": third_item.get("construction_attribute"),
                                "allowEdit": third_item.get("allowEdit"),
                                "not_involved": third_item.get("not_involved"),
                                "project_progress_modified": third_item.get("project_progress_modified"),
                                "start_time_modified": third_item.get("start_time_modified"),
                                "completion_time_modified": third_item.get("completion_time_modified"),
                                "responsible_person_modified": third_item.get("responsible_person_modified"),
                                "children": third_children,  # 添加四级标题的子项
                                "not_involved_modified": third_item.get("not_involved_modified"),
                                "third_level_proportion_modified": third_item.get("third_level_proportion_modified"),
                                "third_level_proportion": third_item.get("third_level_proportion"),
                            }
                        )
                if str(second_item["serial_number"]).startswith(
                        str(first_item["serial_number"])
                ):
                    first_children.append(
                        {
                            "id": second_item.get("id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "responsible_person": second_item.get("responsible_person"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get("construction_attribute"),
                            "allowEdit": second_item.get("allowEdit"),
                            "not_involved": second_item.get("not_involved"),
                            "project_progress_modified": second_item.get("project_progress_modified"),
                            "start_time_modified": second_item.get("start_time_modified"),
                            "completion_time_modified": second_item.get("completion_time_modified"),
                            "responsible_person_modified": second_item.get("responsible_person_modified"),
                            "children": second_children,
                            "second_level_proportion": second_item.get("second_level_proportion"),
                            "not_involved_modified": second_item.get("not_involved_modified"),
                        }
                    )
            new_zkjh_list.append(
                {
                    "id": first_item.get("id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "responsible_person": first_item.get("responsible_person"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "allowEdit": first_item.get("allowEdit"),
                    "not_involved": first_item.get("not_involved"),
                    "project_progress_modified": first_item.get("project_progress_modified"),
                    "start_time_modified": first_item.get("start_time_modified"),
                    "completion_time_modified": first_item.get("completion_time_modified"),
                    "responsible_person_modified": first_item.get("responsible_person_modified"),
                    "children": first_children,
                    "not_involved_modified": first_item.get("not_involved_modified"),
                    "third_level_proportion_modified": first_item.get("third_level_proportion_modified"),
                    "first_level_proportion": first_item.get("first_level_proportion"),
                    "third_level_proportion": first_item.get("third_level_proportion"),
                }
            )
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        # 新总控计划数据存储
        for row in new_zkjh_list:
            children1 = row.get("children")
            insert_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "project_name": project_name,
                    "now_time": now_time,
                    "whethe_involves": "1" if row.get("not_involved") == "否" else "0",
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                insert_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "project_name": project_name,
                        "now_time": now_time,
                        "whethe_involves": "1" if item1.get("not_involved") == "否" else "0",
                    }
                )
                for item2 in children2:
                    insert_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "project_name": project_name,
                            "now_time": now_time,
                            "whethe_involves": "1" if item2.get("not_involved") == "否" else "0",
                            "third_level_proportion": item2.get("third_level_proportion"),
                        }
                    )
                    # 处理四级标题的插入
                    children3 = item2.get("children")
                    for item3 in children3:
                        insert_data.append(
                            {
                                "serial_number": item3.get("serial_number"),
                                "work_content": item3.get("work_content"),
                                "project_progress": item3.get("project_progress"),
                                "start_time": item3.get("start_time"),
                                "completion_time": item3.get("completion_time"),
                                "responsible_person": item3.get("responsible_person"),
                                "input_object": item3.get("input_object"),
                                "output_object": item3.get("output_object"),
                                "construction_attribute": item3.get("construction_attribute"),
                                "project_name": project_name,
                                "now_time": now_time,
                                "whethe_involves": "1" if item3.get("not_involved") == "否" else "0",
                            }
                        )

        variables = {
            "insert_data": insert_data,
            "new_zkjh_list": new_zkjh_list,
            "reason_for_modification": reason_for_modification,
            "new_data": new_data,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectManagementApproval(AjaxTodoBase):
    """
    项管审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        item_tube_approval = process_data.get("item_tube_approval")
        item_tube_remark = process_data.get("item_tube_remark")

        variables = {
            "item_tube_approval": item_tube_approval,
            "item_tube_remark": item_tube_remark,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SupervisionApproval(AjaxTodoBase):
    """
    监理审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        supervision_management_approval = process_data.get(
            "supervision_management_approval"
        )
        supervision_management_remark = process_data.get(
            "supervision_management_remark"
        )

        variables = {
            "supervision_management_approval": supervision_management_approval,
            "supervision_management_remark": supervision_management_remark,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMApproval(AjaxTodoBase):
    """
    PM审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        PM_approval = process_data.get("PM_approval")
        PM_remark = process_data.get("PM_remark")

        project_name = self.ctx.variables.get("project_name")
        insert_data = self.ctx.variables.get("insert_data")
        insert_data_new = []

        creator = self.ctx.variables.get("Creator")
        process_definition_key = self.ctx.variables.get("_process_definition_key")
        ticket_id = self.ctx.variables.get("ticket_id")
        for item in insert_data:
            insert_data_new.append({
                "serial_number": str(item.get("serial_number")),
                "work_content": str(item.get("work_content")) or "",
                "start_time": item.get("start_time") or "",
                "completion_time": item.get("completion_time") or "",
                "responsible_person": str(item.get("responsible_person")) or "",
                "input_object": str(item.get("input_object")) or "",
                "output_object": str(item.get("output_object")) or "",
                "construction_attribute": str(item.get("construction_attribute")) or "",
                "project_name": project_name or "",
                "not_involved": item.get("whethe_involves") or "0",
            })

        if PM_approval == "通过":
            db = mysql.new_mysql_instance("tbconstruct")
            db.begin()
            db.insert_batch("excel_data", insert_data)
            db.commit()
            try:
                response_data = gnetops.request(
                    action="Project",
                    method="UpdateConstructPlanData",
                    ext_data={
                        "project_name": project_name,
                        "update_list": insert_data_new,
                        "ticket_id": ticket_id,
                        "flow_name": process_definition_key,
                        "operator": creator
                    },
                    scheme="ifob-infrastructure",
                )

            except Exception as e:
                raise e

        variables = {"PM_approval": PM_approval, "PM_remark": PM_remark, "insert_data_new": insert_data_new}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class MasterControlPlan(object):
    """
    总控计划
    """

    def table_presentation(self, project_name):
        """
            表格数据查询
        """
        zkjh_list = []
        first_list = []
        second_list = []
        third_list = []
        fourth_list = []  # 新增四级标题列表
        db = mysql.new_mysql_instance("tbconstruct")
        # 查询项目原总控计划数据
        query_sql = f"""
            SELECT @row_number := @row_number + 1 AS new_id, t.* 
            FROM 
            (SELECT * 
            FROM construct_plan_data 
            WHERE project_name = '{project_name}' 
            ORDER BY id) AS t, (SELECT @row_number := 0) AS r
        """
        result = db.get_all(query_sql)
        for row in result:
            serial_number = row.get("serial_number", "None")
            if serial_number is not None:
                if serial_number != "None" and serial_number.count(".") == 0:
                    first_list.append(row)
                elif serial_number.count(".") == 1:
                    second_list.append(row)
                elif serial_number.count(".") == 2:
                    third_list.append(row)
                elif serial_number.count(".") == 3:  # 处理四级标题
                    fourth_list.append(row)

        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                for third_item in third_list:
                    third_children = []  # 新增三子级列表
                    for fourth_item in fourth_list:  # 处理四级标题
                        if fourth_item["serial_number"].startswith(third_item["serial_number"]):
                            third_children.append(
                                {
                                    "id": fourth_item.get("new_id"),
                                    "serial_number": fourth_item.get("serial_number"),
                                    "work_content": fourth_item.get("work_content"),
                                    "project_progress": fourth_item.get("project_progress"),
                                    "start_time": fourth_item.get("start_time"),
                                    "completion_time": fourth_item.get("completion_time"),
                                    "input_object": fourth_item.get("input_object"),
                                    "output_object": fourth_item.get("output_object"),
                                    "construction_attribute": fourth_item.get("construction_attribute"),
                                    "actual_start_time": fourth_item.get("actual_start_time"),
                                    "actual_finish_time": fourth_item.get("actual_finish_time"),
                                    "week_progress": str(fourth_item.get("week_progress")) + "%",
                                    "not_involved": "不涉及" if fourth_item.get("not_involved") == 1 else None,
                                }
                            )
                    if third_item["serial_number"].startswith(second_item["serial_number"]):
                        second_children.append(
                            {
                                "id": third_item.get("new_id"),
                                "serial_number": third_item.get("serial_number"),
                                "work_content": third_item.get("work_content"),
                                "project_progress": third_item.get("project_progress"),
                                "start_time": third_item.get("start_time"),
                                "completion_time": third_item.get("completion_time"),
                                "input_object": third_item.get("input_object"),
                                "output_object": third_item.get("output_object"),
                                "construction_attribute": third_item.get("construction_attribute"),
                                "actual_start_time": third_item.get("actual_start_time"),
                                "actual_finish_time": third_item.get("actual_finish_time"),
                                "week_progress": str(third_item.get("week_progress")) + "%",
                                "children": third_children,  # 添加三子级
                                "not_involved": "不涉及" if third_item.get("not_involved") == 1 else None,
                            }
                        )
                if second_item["serial_number"].startswith(first_item["serial_number"]):
                    first_children.append(
                        {
                            "id": second_item.get("new_id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get("construction_attribute"),
                            "actual_start_time": second_item.get("actual_start_time"),
                            "actual_finish_time": second_item.get("actual_finish_time"),
                            "week_progress": str(second_item.get("week_progress")) + "%",
                            "children": second_children,
                            "not_involved": "不涉及" if second_item.get("not_involved") == 1 else None,
                        }
                    )
            zkjh_list.append(
                {
                    "id": first_item.get("new_id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "actual_start_time": first_item.get("actual_start_time"),
                    "actual_finish_time": first_item.get("actual_finish_time"),
                    "week_progress": str(first_item.get("week_progress")) + "%",
                    "children": first_children,
                    "not_involved": "不涉及" if first_item.get("not_involved") == 1 else None,
                }
            )
        return zkjh_list

    def data_repository(self, url):
        df = pd.read_excel(url)
        df = df.replace({pd.np.nan: None})
        data1 = []
        data2 = [
            {"js_id": "183617416345632", "ref_id": "183611239925792", "task_name": "方案与图纸设计",
             "personliable": "leviyli", "degree_of_completion": "100", "task_status": "", "progress_status": "",
             "scheduled_start_time": "2023-03-20T00:00:00.000Z",
             "plannedcompletion_time": "2023-04-12T00:00:00.000Z", "actual_start_time": None,
             "actual_completion_time": None, "last_updated": None, "lag_days": "", "days_of_delay": "",
             "task_type": "准备施工", "link_main_form_id": "", "progress_remark": "", "status": "已结单",
             "inst_id": "64f996db74f951019281e798", "create_time": "2023-09-07T09:24:43.000Z",
             "_id": "183617416345632"},
            {"js_id": "183617416345633", "ref_id": "183611239925792", "task_name": "设备品牌确认",
             "personliable": "leviyli", "degree_of_completion": "100", "task_status": "", "progress_status": "",
             "scheduled_start_time": "2023-04-10T00:00:00.000Z",
             "plannedcompletion_time": "2023-04-20T00:00:00.000Z", "actual_start_time": None,
             "actual_completion_time": None, "last_updated": None, "lag_days": "", "days_of_delay": "",
             "task_type": "准备施工", "link_main_form_id": "", "progress_remark": "", "status": "已结单",
             "inst_id": "64f996db74f951019281e798", "create_time": "2023-09-07T09:24:43.000Z",
             "_id": "183617416345633"},
            {"js_id": "183617624826912", "ref_id": "183611239925792", "task_name": "施工单位进场",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-04-25T00:00:00.000Z",
             "plannedcompletion_time": "2023-04-29T00:00:00.000Z", "actual_start_time": "2023-04-27T00:00:00.000Z",
             "actual_completion_time": "2023-04-29T00:00:00.000Z", "last_updated": None, "lag_days": "2",
             "days_of_delay": "1", "task_type": "准备施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826912"},
            {"js_id": "183617624826913", "ref_id": "183611239925792", "task_name": "服务商进场",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-04-20T00:00:00.000Z",
             "plannedcompletion_time": "2023-04-21T00:00:00.000Z", "actual_start_time": "2023-04-20T00:00:00.000Z",
             "actual_completion_time": "2023-04-21T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "", "task_type": "准备施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826913"},
            {"js_id": "183617624826914", "ref_id": "183611239925792", "task_name": "工程/设备/服务招标",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-04-04T00:00:00.000Z",
             "plannedcompletion_time": "2023-09-25T00:00:00.000Z", "actual_start_time": "2023-09-05T00:00:00.000Z",
             "actual_completion_time": "2023-09-25T00:00:00.000Z", "last_updated": None, "lag_days": "154",
             "days_of_delay": "", "task_type": "准备施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826914"},
            {"js_id": "183617624826915", "ref_id": "183611239925792", "task_name": "红线内电力工程",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-06-15T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-15T00:00:00.000Z", "actual_start_time": "2023-06-15T00:00:00.000Z",
             "actual_completion_time": "2023-07-15T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826915"},
            {"js_id": "183617624826916", "ref_id": "183611239925792", "task_name": "红线外电力工程",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后,完成时间延期", "scheduled_start_time": "2023-07-05T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-10T00:00:00.000Z", "actual_start_time": "2023-07-11T00:00:00.000Z",
             "actual_completion_time": "2023-07-17T00:00:00.000Z", "last_updated": None, "lag_days": "6",
             "days_of_delay": "7", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826916"},
            {"js_id": "183617624826917", "ref_id": "183611239925792", "task_name": "外电验收送电",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-07-10T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-15T00:00:00.000Z", "actual_start_time": "2023-07-11T00:00:00.000Z",
             "actual_completion_time": "2023-07-15T00:00:00.000Z", "last_updated": None, "lag_days": "1",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826917"},
            {"js_id": "183617624826918", "ref_id": "183611239925792", "task_name": "第一路市电验收送电",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后,完成时间延期", "scheduled_start_time": "2023-07-15T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-16T00:00:00.000Z", "actual_start_time": "2023-07-16T00:00:00.000Z",
             "actual_completion_time": "2023-07-18T00:00:00.000Z", "last_updated": None, "lag_days": "1",
             "days_of_delay": "2", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826918"},
            {"js_id": "183617624826919", "ref_id": "183611239925792", "task_name": "第二路市电验收送电",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后,完成时间延期", "scheduled_start_time": "2023-07-15T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-16T00:00:00.000Z", "actual_start_time": "2023-07-16T00:00:00.000Z",
             "actual_completion_time": "2023-07-18T00:00:00.000Z", "last_updated": None, "lag_days": "1",
             "days_of_delay": "2", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826919"},
            {"js_id": "183617624826920", "ref_id": "183611239925792", "task_name": "装饰装修",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-05-25T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-21T00:00:00.000Z", "actual_start_time": "2023-05-24T00:00:00.000Z",
             "actual_completion_time": "2023-07-21T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826920"},
            {"js_id": "183617624826921", "ref_id": "183611239925792", "task_name": "柴发系统施工",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-05-10T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-15T00:00:00.000Z", "actual_start_time": "2023-05-10T00:00:00.000Z",
             "actual_completion_time": "2023-07-15T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826921"},
            {"js_id": "183617624826922", "ref_id": "183611239925792", "task_name": "消防系统施工",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-05-25T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-30T00:00:00.000Z", "actual_start_time": "2023-05-26T00:00:00.000Z",
             "actual_completion_time": "2023-07-30T00:00:00.000Z", "last_updated": None, "lag_days": "1",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826922"},
            {"js_id": "183617624826923", "ref_id": "183611239925792", "task_name": "暖通系统施工",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-05-01T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-15T00:00:00.000Z", "actual_start_time": "2023-05-03T00:00:00.000Z",
             "actual_completion_time": "2023-07-15T00:00:00.000Z", "last_updated": None, "lag_days": "2",
             "days_of_delay": "0", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826923"},
            {"js_id": "183617624826924", "ref_id": "183611239925792", "task_name": "方仓集成",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "开始时间滞后", "scheduled_start_time": "2023-04-12T00:00:00.000Z",
             "plannedcompletion_time": "2023-06-25T00:00:00.000Z", "actual_start_time": "2023-04-15T00:00:00.000Z",
             "actual_completion_time": "2023-06-25T00:00:00.000Z", "last_updated": None, "lag_days": "3",
             "days_of_delay": "", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826924"},
            {"js_id": "183617624826925", "ref_id": "183611239925792", "task_name": "弱电系统施工",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-05-25T00:00:00.000Z",
             "plannedcompletion_time": "2023-07-15T00:00:00.000Z", "actual_start_time": "2023-05-25T00:00:00.000Z",
             "actual_completion_time": "2023-07-15T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "0", "task_type": "安装施工", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826925"},
            {"js_id": "183617624826926", "ref_id": "183611239925792", "task_name": "验证测试",
             "personliable": "yanjunchen", "degree_of_completion": "100", "task_status": "",
             "progress_status": "正常", "scheduled_start_time": "2023-08-01T00:00:00.000Z",
             "plannedcompletion_time": "2023-08-30T00:00:00.000Z", "actual_start_time": "2023-08-01T00:00:00.000Z",
             "actual_completion_time": "2023-08-30T00:00:00.000Z", "last_updated": None, "lag_days": "",
             "days_of_delay": "", "task_type": "验证测试", "link_main_form_id": "", "progress_remark": "",
             "status": "已结单", "inst_id": "64f997a6866d0f0192b2ff47", "create_time": "2023-09-07T09:28:06.000Z",
             "_id": "183617624826926"}
        ]
        data3 = [
            {"fc_id": 146, "ref_id": "183611239925792", "number": None, "inte_cab": "IT仓-1",
             "plan_start_ins_time": "2023/5/1", "plan_end_ins_time": "2023/6/15",
             "scene_start_ins_time": "2023/5/5", "scene_end_ins_time": "2023/6/20",
             "install_progress": "开始时间滞后,完成时间延期", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 146},
            {"fc_id": 147, "ref_id": "183611239925792", "number": None, "inte_cab": "IT仓-2",
             "plan_start_ins_time": "2023/5/15", "plan_end_ins_time": "2023/7/15",
             "scene_start_ins_time": "2023/5/25", "scene_end_ins_time": "2023/7/5",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 147},
            {"fc_id": 148, "ref_id": "183611239925792", "number": None, "inte_cab": "IT仓-3",
             "plan_start_ins_time": "2023/5/1", "plan_end_ins_time": "2023/6/15",
             "scene_start_ins_time": "2023/5/12", "scene_end_ins_time": "2023/6/20",
             "install_progress": "开始时间滞后,完成时间延期", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 148},
            {"fc_id": 149, "ref_id": "183611239925792", "number": None, "inte_cab": "IT仓-4",
             "plan_start_ins_time": "2023/5/15", "plan_end_ins_time": "2023/7/15",
             "scene_start_ins_time": "2023/5/25", "scene_end_ins_time": "2023/7/5",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 149},
            {"fc_id": 150, "ref_id": "183611239925792", "number": None, "inte_cab": "中压仓",
             "plan_start_ins_time": "2023/6/20", "plan_end_ins_time": "2023/7/15",
             "scene_start_ins_time": "2023/6/26", "scene_end_ins_time": "2023/7/15",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 150},
            {"fc_id": 151, "ref_id": "183611239925792", "number": None, "inte_cab": "低压仓",
             "plan_start_ins_time": "2023/6/15", "plan_end_ins_time": "2023/7/10",
             "scene_start_ins_time": "2023/6/16", "scene_end_ins_time": "2023/7/9",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 151},
            {"fc_id": 152, "ref_id": "183611239925792", "number": None, "inte_cab": "柴发仓",
             "plan_start_ins_time": "2023/6/5", "plan_end_ins_time": "2023/7/10",
             "scene_start_ins_time": "2023/6/6", "scene_end_ins_time": "2023/7/10",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 152},
            {"fc_id": 153, "ref_id": "183611239925792", "number": None, "inte_cab": "空调仓",
             "plan_start_ins_time": "2023/5/10", "plan_end_ins_time": "2023/7/15",
             "scene_start_ins_time": "2023/5/15", "scene_end_ins_time": "2023/7/15",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 153},
            {"fc_id": 154, "ref_id": "183611239925792", "number": None, "inte_cab": "水处理仓",
             "plan_start_ins_time": "2023/6/26", "plan_end_ins_time": "2023/7/16",
             "scene_start_ins_time": "2023/6/28", "scene_end_ins_time": "2023/7/16",
             "install_progress": "开始时间滞后", "acc_start_time": None, "first_delivery": None,
             "acc_end_time": None, "last_delivery": None, "idcrm_project_code": None,
             "degree_of_completion": "100", "_id": 154}
        ]
        data4 = [
            {"sb_id": "183613737877536", "ref_id": "183611239925792", "device": "柴油发电机主机", "description": "",
             "amount": "7", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "柴油发电机主机_额定电压10.5kV/基本功率1800kW/额定频率50Hz/额定转速1500转/分(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2022-02-19T16:00:00.000Z", "material_code": "796350BDGT10003",
             "material_id": "6586278", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877536"},
            {"sb_id": "183613737877537", "ref_id": "183611239925792", "device": "柴发方仓", "description": "",
             "amount": "7", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "柴发方仓_13000L*3000W*3500H集装箱箱体/含日用油箱、接地电阻柜、配电箱、照明、消防(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2022-02-19T16:00:00.000Z", "material_code": "796350BDGP10001",
             "material_id": "6586279", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877537"},
            {"sb_id": "183613737877538", "ref_id": "183611239925792", "device": "柴发机组配套", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "柴发机组配套_7台1800kW柴发机组及集装箱对应的厂验测试/备件维修处理/现场主动维护保养(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2022-02-19T16:00:00.000Z", "material_code": "796350BDTG10006",
             "material_id": "6586284", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877538"},
            {"sb_id": "183613737877539", "ref_id": "183611239925792", "device": "柴发方仓", "description": "",
             "amount": "7", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "北方地区柴发集装箱采用100mm厚岩棉板方式，柴发油箱间保温采取增加防爆油汀等措施",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-08-29T16:00:00.000Z", "material_code": "796350BDGP1000B",
             "material_id": "6988768", "ordering_time": "2023-04-26 11:43:16", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877539"},
            {"sb_id": "183613737877540", "ref_id": "183611239925792", "device": "间接蒸发冷AHU", "description": "",
             "amount": "9", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "间接蒸发冷AHU_间接蒸发式制冷设备/制冷量260KW(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-05-09T16:00:00.000Z", "material_code": "796350BAHU10000",
             "material_id": "6584122", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877540"},
            {"sb_id": "183613737877541", "ref_id": "183611239925792", "device": "蓄电池", "description": "",
             "amount": "2560", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "蓄电池 不确定 蓄电池_不确定_不确定_12 V ( 2V*6 )阀控式密封铅酸免维护蓄电池/单体功率608W/235AH(RMA_3年_生产物料) Pcs",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-04-09T16:00:00.000Z", "material_code": "796350BSTC1002A",
             "material_id": "6586126", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877541"},
            {"sb_id": "183613737877542", "ref_id": "183611239925792", "device": "综合配电柜", "description": "",
             "amount": "72", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "综合配电柜_标准间综合配电柜，包含400A/3P断路器，防雷器，监控系统，智能电量表等(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-02-22T16:00:00.000Z", "material_code": "796350BCPD0005",
             "material_id": "6586122", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877542"},
            {"sb_id": "183613737877543", "ref_id": "183611239925792", "device": "高压直流系统", "description": "",
             "amount": "768", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "高压直流系统_标准间整流模块，额定输出电压240V，额定输出功率15kW(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-04-14T16:00:00.000Z", "material_code": "796350BHVS000C",
             "material_id": "6586120", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877543"},
            {"sb_id": "183613737877544", "ref_id": "183611239925792", "device": "高压直流系统", "description": "",
             "amount": "72", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "高压直流系统_标准间高压直流柜,标准间输入400VAC，输出240VDC，额定容量180kW(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-02-22T16:00:00.000Z", "material_code": "796350BHVS000B",
             "material_id": "6586119", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877544"},
            {"sb_id": "183613737877545", "ref_id": "183611239925792", "device": "机柜", "description": "",
             "amount": "128", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "机柜_600W*1200D*2500H机柜/配26个盲板、26对导轨、单侧侧门板(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2022-02-19T16:00:00.000Z", "material_code": "796350BRCK10005",
             "material_id": "6586283", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877545"},
            {"sb_id": "183613737877546", "ref_id": "183611239925792", "device": "机柜", "description": "",
             "amount": "832", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "机柜_600W*1200D*2500H机柜/配26个盲板、26对导轨、不含侧门板(RMA_3年_生产物料)",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2022-02-19T16:00:00.000Z", "material_code": "796350BRCK10004",
             "material_id": "6586282", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877546"},
            {"sb_id": "183613737877547", "ref_id": "183611239925792", "device": "机柜", "description": "",
             "amount": "14400", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "机柜_免工具1U盲板(RMA_3年_生产物料)", "plan_acc_start_time": None, "first_delivery": "",
             "acc_end_time": None, "last_delivery": "", "plan_arrival_time": "2023-05-14T16:00:00.000Z",
             "material_code": "796350BRCK10003", "material_id": "6586281", "ordering_time": "2023-04-13 19:12:23",
             "brand": "不确定", "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "",
             "category_code": None, "_id": "183613737877547"},
            {"sb_id": "183613737877548", "ref_id": "183611239925792", "device": "PDU", "description": "",
             "amount": "1920", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "PDU 不确定 IT间机柜PDU/240V直流/40A/8位*C13+6位*C19 条", "plan_acc_start_time": None,
             "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-04-24T16:00:00.000Z", "material_code": "796350BPDU10030",
             "material_id": "7246973", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877548"},
            {"sb_id": "183613737877549", "ref_id": "183611239925792", "device": "PDU", "description": "",
             "amount": "960", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "PDU 不确定 IT间GPU服务器机柜PDU/380V交流/32A/15位*C13+12位*C19 条",
             "plan_acc_start_time": None, "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-04-24T16:00:00.000Z", "material_code": "796350BPDU10031",
             "material_id": "7246974", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877549"},
            {"sb_id": "183613737877552", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_电源进线柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None,
             "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-05-04T16:00:00.000Z", "material_code": "796350BMVD0053",
             "material_id": "6586129", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877552"},
            {"sb_id": "183613737877553", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_市电联络柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None,
             "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-05-04T16:00:00.000Z", "material_code": "796350BMVD004F",
             "material_id": "6584147", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877553"},
            {"sb_id": "183613737877554", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_高压计量柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None,
             "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-05-04T16:00:00.000Z", "material_code": "796350BMVD004A",
             "material_id": "6584132", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877554"},
            {"sb_id": "183613737877555", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_PT柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None, "first_delivery": "",
             "acc_end_time": None, "last_delivery": "", "plan_arrival_time": "2023-05-04T16:00:00.000Z",
             "material_code": "796350BMVD0050", "material_id": "6584148", "ordering_time": "2023-04-28 20:39:13",
             "brand": "不确定", "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "",
             "category_code": None, "_id": "183613737877555"},
            {"sb_id": "183613737877556", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "1", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_油机进线柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None,
             "first_delivery": "", "acc_end_time": None, "last_delivery": "",
             "plan_arrival_time": "2023-05-04T16:00:00.000Z", "material_code": "796350BMVD004E",
             "material_id": "6584146", "ordering_time": "2023-04-28 20:39:13", "brand": "不确定",
             "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "", "category_code": None,
             "_id": "183613737877556"},
            {"sb_id": "183613737877557", "ref_id": "183611239925792", "device": "中压配电柜", "description": "",
             "amount": "5", "device_type": "", "expected_arrival_time": "2023-06-05T16:00:00.000Z",
             "actual_arrival_time": None, "acc_status": "", "install_status": "", "install_time": None,
             "model": "中压配电柜_出线柜-A路(RMA_3年_生产物料)", "plan_acc_start_time": None, "first_delivery": "",
             "acc_end_time": None, "last_delivery": "", "plan_arrival_time": "2023-05-04T16:00:00.000Z",
             "material_code": "796350BMVD004D", "material_id": "6584145", "ordering_time": "2023-04-28 20:39:13",
             "brand": "不确定", "idcrm_project_code": "FR-2023-YZI-001", "idcrm_material_detail": "",
             "category_code": None, "_id": "183613737877557"}
        ]
        data5 = [
            {"jd_id": "183611239936032", "ref_id": "183611239925792", "number": "1",
             "service_name": "半栋（960机柜）第三方测试服务",
             "service_provider_name": "北京中科仙络智算科技股份有限公司", "adm_time": "2023-08-31T00:00:00.000Z",
             "acc_start_time": "", "first_delivery": "", "acc_end_time": "", "last_delivery": "",
             "material_code": "796350CTHR000A", "material_id": "7007547", "idcrm_project_code": "",
             "create_time": "2023-09-07T07:44:11.000Z", "remark": None, "amount": "1", "_id": "183611239936032"},
            {"jd_id": "183611239936033", "ref_id": "183611239925792", "number": "2",
             "service_name": "半栋（960机柜）项目监理", "service_provider_name": "中邮通建设咨询有限公司",
             "adm_time": "2023-04-24T00:00:00.000Z", "acc_start_time": "", "first_delivery": "",
             "acc_end_time": "", "last_delivery": "", "material_code": "796350CPRS0007", "material_id": "7007546",
             "idcrm_project_code": "", "create_time": "2023-09-07T07:44:11.000Z", "remark": None, "amount": "1",
             "_id": "183611239936033"},
            {"jd_id": "183611239936034", "ref_id": "183611239925792", "number": "3",
             "service_name": "半栋（960机柜）机电项目管理",
             "service_provider_name": "上海华建工程建设咨询有限公司",
             "adm_time": "2023-04-24T00:00:00.000Z", "acc_start_time": "",
             "first_delivery": "", "acc_end_time": "", "last_delivery": "",
             "material_code": "796350CPRM0004", "material_id": "7007545",
             "idcrm_project_code": "", "create_time": "2023-09-07T07:44:11.000Z",
             "remark": None, "amount": "1", "_id": "183611239936034"},
            {"jd_id": "211632998047697302", "ref_id": "183611239925792", "number": "1",
             "service_name": "T-Block 单层库 后半栋，960机架，3年原厂维保",
             "service_provider_name": "中兴通讯股份有限公司", "adm_time": None, "acc_start_time": "",
             "first_delivery": "", "acc_end_time": "", "last_delivery": "", "material_code": "796350BIEP1003C",
             "material_id": "7007560", "idcrm_project_code": "", "create_time": "2023-09-07T08:36:58.000Z",
             "remark": None, "amount": "1", "_id": "211632998047697302"},
            {"jd_id": "697805249121320540", "ref_id": "183611239925792", "number": "1",
             "service_name": "配套工程及报审报验-半栋", "service_provider_name": "中兴通讯股份有限公司",
             "adm_time": None, "acc_start_time": "", "first_delivery": "", "acc_end_time": "", "last_delivery": "",
             "material_code": "796350BIEP1000D", "material_id": "6586285", "idcrm_project_code": "",
             "create_time": "2023-09-07T08:37:29.000Z", "remark": None, "amount": "1",
             "_id": "697805249121320540"}
        ]

        decoded_data = []
        for _, row in df.iterrows():
            instanceId = row.get('instanceId')
            configId = row.get('configId')
            name = row.get('name')
            type = row.get('type')
            planStartDate = row.get('planStartDate')
            planFinishDate = row.get('planFinishDate')
            actualStartDate = row.get('actualStartDate')
            actualFinishDate = row.get('actualFinishDate')
            manager = row.get('manager')
            status = row.get('status')
            process = row.get('process')
            schedule = row.get('schedule')
            pid = row.get('pid')
            level = row.get('level')
            cancelDetail = row.get('cancelDetail')
            priority = row.get('priority')
            isValid = row.get('isValid')
            rackNum = row.get('rackNum')
            batche = row.get('batche')
            taskPid = row.get('taskPid')
            extTask = row.get('extTask')
            issueCnt = row.get('issueCnt')
            fileCnt = row.get('fileCnt')
            updateTime = row.get('updateTime')
            sysSetFinishDate = row.get('sysSetFinishDate')
            data1.append({
                'instanceId': instanceId,
                'configId': configId,
                'name': name,
                'type': type,
                'planStartDate': planStartDate,
                'planFinishDate': planFinishDate,
                'actualStartDate': actualStartDate,
                'actualFinishDate': actualFinishDate,
                'manager': manager,
                'status': status,
                'process': process,
                'schedule': schedule,
                'pid': pid,
                'level': level,
                'cancelDetail': cancelDetail,
                'priority': priority,
                'isValid': isValid,
                'rackNum': rackNum,
                'batche': batche,
                'taskPid': taskPid,
                'extTask': extTask,
                'issueCnt': issueCnt,
                'fileCnt': fileCnt,
                'updateTime': updateTime,
                'sysSetFinishDate': sysSetFinishDate
            })

        # 解码Unicode转义字符
        for item in data1:
            decoded_item = {}
            for key, value in item.items():
                if isinstance(value, str):
                    decoded_item[key] = value.encode('utf-8').decode('unicode_escape')
                else:
                    decoded_item[key] = value
            decoded_data.append(decoded_item)
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        db.insert_batch("mht_acdc_task", decoded_data)
        db.insert_batch("constructing_assignment", data2)
        db.insert_batch("warehouse_list", data3)
        db.insert_batch("device_list", data4)
        db.insert_batch("electromechanical_construction_service", data5)
        db.commit()
