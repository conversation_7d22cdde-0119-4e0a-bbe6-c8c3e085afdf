from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue
from iBroker.lib import mysql, config
from iBroker.sdk.notification.chatops import ChatOpsSend
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json
from biz.cooperative_partner.project_application_partner.construction_application_process_partner_api import (
    ConstructionApplicationProcessPartnerApi,
)


class PlanAutomationAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 获取建设中项目列表
    def get_project_list(self):
        """
        获取建设中项目列表
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT t1.project_name, t3.integrators FROM risk_early_warning_data t1 "
            "JOIN summary_data t2 "
            "ON t1.project_name = CONCAT(t2.campus, t2.project_name) "
            "JOIN supplier_resources t3 "
            "ON t1.project_name = CONCAT(t3.campus, t3.project) "
            "WHERE t2.state = '建设中' AND t1.construction_mode = '自建'"
        )
        project_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.建设中项目", {"message": project_list})
        project_jcs_dict = {}
        if project_list:
            project_jcs_dict = {
                item.get("project_name"): item.get("integrators")
                for item in project_list
            }
        variables = {"project_list": project_list, "project_jcs_dict": project_jcs_dict}
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 获取项目关键节点最新总控计划 + 园区、项目、PM
    def get_plan(self, project_list):
        """
        获取项目关键节点最新总控计划
        """
        # "2.1" 深化设计 停止建单
        serial_number_list = ["2.4", "2.7", "2.9", "3", "4", "5"]
        project_plan_dict = {}
        for p in project_list:
            project_name = p.get("project_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = self.get_plan_sql(project_name, serial_number_list)
            plan_list = db.query(sql)
            project_plan_dict[project_name] = plan_list
        variables = {
            "project_plan_dict": project_plan_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 生成sql 获取项目关键节点最新总控计划 + 园区、项目、PM
    def get_plan_sql(self, project_name, serial_number_list):
        serial_number_temp = ", ".join(f"'{item}'" for item in serial_number_list)
        sql = (
            "SELECT t1.project_name, t1.serial_number, t1.work_content, t1.start_time, t1.completion_time, "
            "t3.campus, t3.project, t3.PM FROM construct_plan_data t1 "
            "JOIN risk_early_warning_data t3 ON t1.project_name = t3.project_name "
            f"WHERE t1.project_name = '{project_name}' AND t1.serial_number IN ({serial_number_temp}) "
        )
        return sql

    # 计划解析：根据计划开始时间建单
    def plan_analysis(self, project_plan_dict):
        """
        计划解析：根据计划开始时间建单
        """
        project_create_ticket_dict = {}
        create_ticket_info_list = []
        for project_name, plan_info in project_plan_dict.items():
            for p in plan_info:
                serial_number = p.get("serial_number")
                start_time = p.get("start_time")
                end_time = p.get("completion_time")
                arg_info = {
                    "project_name": project_name,
                    "campus": p.get("campus"),
                    "project": p.get("project"),
                    "PM": p.get("PM"),
                }
                # 建单
                res = self.create_ticket_analysis(
                    serial_number, start_time, end_time, arg_info
                )
                # p["create_ticket_info"] = res
                item = {
                    "project_name": project_name,
                    "PM": p.get("PM"),
                    "serial_number": serial_number,
                    "work_content": p.get("work_content"),
                    "start_time": p.get("start_time"),
                    "end_time": p.get("completion_time"),
                    "msg": res.get("msg"),
                    "ticket": res.get("data"),
                    "err": res.get("err"),
                }
                create_ticket_info_list.append(item)
                if project_name not in project_create_ticket_dict:
                    project_create_ticket_dict[project_name] = []
                project_create_ticket_dict[project_name].append(item)
                # 已创建工单 给 PM、youngshi、viky推送消息（viky需整理到"自建租赁项目进展"在线表格）
                if res.get("msg") == "已创建工单":
                    acceptance_result_receivers = config.get_config_map(
                        "acceptance_result_receivers"
                    )
                    user_list = [p.get("PM"), *acceptance_result_receivers]
                    ticket_id = res.get("data")[0]
                    env = config.get_env_flag(False)
                    if env == "product":
                        url = "https://dcops.woa.com/appManage/tickets/details"
                    else:
                        url = "https://dcops.test.woa.com/appManage/tickets/details"
                    oaUrl = f"{url}?params={ticket_id}"
                    text = (
                        f"{project_name}已按计划自动创建工单：\n"
                        f"【项目名称】：{project_name}\n"
                        f"【对应计划】：{serial_number}{p.get('work_content')}\n"
                        f"【计划开始时间】：{p.get('start_time')}\n"
                        f"【工单号】：[{ticket_id}]({oaUrl})\n"
                    )
                    for user in user_list:
                        ChatOpsSend(
                            user_name=user,
                            msg_content=text,
                            msg_type="markdown",
                            des_type="single",
                        ).call()
                elif res.get("msg") == "建单失败":
                    ticket_id = self.ctx.variables.get("ticket_id")
                    env = config.get_env_flag(False)
                    if env == "product":
                        url = "https://dcops.woa.com/appManage/tickets/details"
                    else:
                        url = "https://dcops.test.woa.com/appManage/tickets/details"
                    oaUrl = f"{url}?params={ticket_id}"
                    acceptance_result_receivers = config.get_config_map(
                        "acceptance_result_receivers"
                    )
                    user_list = [*acceptance_result_receivers]
                    text = (
                        f"{project_name}建单失败，请关注：\n"
                        f"【项目名称】：{project_name}\n"
                        f"【对应计划】：{serial_number}{p.get('work_content')}\n"
                        f"【计划开始时间】：{p.get('start_time')}\n"
                        f"【工单号】：[{ticket_id}]({oaUrl})\n"
                        f"【错误信息】: {res.get('err')}\n"
                    )
                    for user in user_list:
                        ChatOpsSend(
                            user_name=user,
                            msg_content=text,
                            msg_type="markdown",
                            des_type="single",
                        ).call()
        variables = {
            "project_plan_dict": project_plan_dict,
            "create_ticket_info_list": create_ticket_info_list,
            "project_create_ticket_dict": project_create_ticket_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 查询是否有建单
    def query_exit_ticket(self, project_name, serial_number, is_open=False):
        flow_dict = {
            "2.1": [
                "obtaining_documents_during_detailed_design_phase",
                "cooperative_partner_deepen_design",
            ],
            "2.4": [
                "project_construction_application",
                "cooperative_partner_project_construction_application",
            ],
            "2.7": [
                "construction_before_equipment_arrival",
                "cooperative_partner_project_open",
                "cooperative_partner_project_open_wd",
                "cooperative_partner_project_open_Dcops",
            ],
            "2.8": [
                "construction_before_equipment_arrival",
                "cooperative_partner_construction_preparation",
                "cooperative_partner_construction_preparation_wd",
            ],
            "2.9": [
                "mains_power_special",
                "cooperative_mains_special",
                "cooperative_mains_special_wd",
                "cooperative_mains_special_Dcops",
            ],
            "3": [
                "project_site_implementation",
                "cooperative_partner_installation_construction",
                "cooperative_partner_installation_construction_wd",
            ],
            "4": [
                "construct_test_phase_bak",
                "cooperative_partner_test_verification",
                "construct_test_phase",
            ],
            "5": [
                "construction_maintenance_platform_docking",
                "cooperative_partner_construction_maintenance_platform_docking",
            ],
            "5.2": ["tb_change"],
        }
        flow_list = flow_dict.get(serial_number)
        if flow_list:
            flow_temp = ", ".join(f"'{item}'" for item in flow_list)
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT * FROM all_construction_ticket_data "
                f"WHERE Title like '%{project_name}%' "
                "AND IsForceEnd = '0' "
                f"AND ProcessDefinitionKey IN ({flow_temp})"
            )
            if is_open:
                sql += " AND TicketStatus != 'END'"
            res = db.query(sql)
            return res
        return False

    # 自动建单逻辑判断
    def create_ticket_analysis(self, serial_number, start_time, end_time, arg_info):
        """
        2.1 深化设计
        2.4 项目报建（含消防）
        2.7 项目开办
        2.9 市电专项
        3 安装施工
        4 测试验证（前一周建单）
        """
        if start_time and end_time:
            today = datetime.today().date()
            start_time_temp = datetime.strptime(start_time, "%Y-%m-%d").date()
            start_time_20_days_ago = start_time_temp - relativedelta(days=20)
            end_time_temp = datetime.strptime(end_time, "%Y-%m-%d").date()
            project_name = arg_info.get("project_name")
            serial_number_today = ["2.1", "2.4", "2.7", "2.9", "3"]
            serial_number_20_days_ago = ["4", "5"]
            if (
                today >= start_time_temp
                and today <= end_time_temp
                and serial_number in serial_number_today
            ):
                exit_ticket = self.query_exit_ticket(project_name, serial_number)
                if exit_ticket:
                    res = {
                        "msg": "已存在工单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    flow_info_dict = self.get_flow_info(serial_number, arg_info)
                    if flow_info_dict:
                        create_ticket = self.create_ticket(flow_info_dict)
                        res = {
                            "msg": "已创建工单",
                            "data": [str(create_ticket.get("TicketId"))],
                        }
                    elif flow_info_dict is False:
                        # res = {"msg": "由对方建单", "data": None}
                        if serial_number == "2.4":
                            # 腾讯侧调用建单接口触发对方建单
                            create_ticket = ConstructionApplicationProcessPartnerApi.create_partner_ticket(
                                arg_info.get("project_name")
                            )
                            if create_ticket.get("code") == 0:
                                res = {
                                    "msg": "已创建工单",
                                    "data": [str(create_ticket.get("ticket_id"))],
                                }
                            else:
                                res = {
                                    "msg": "建单失败",
                                    "err": create_ticket.get("msg"),
                                    "data": [],
                                }
                        else:
                            # 建单接口尚未开发联调
                            res = {"msg": "由对方建单", "data": None}
                    else:
                        res = {
                            "msg": "流程信息获取失败，无法自动建单",
                            "data": None,
                        }
                return res
            elif (
                today >= start_time_20_days_ago
                and today <= end_time_temp
                and serial_number in serial_number_20_days_ago
            ):
                exit_ticket = self.query_exit_ticket(project_name, serial_number)
                if exit_ticket:
                    res = {
                        "msg": "已存在工单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    flow_info_dict = self.get_flow_info(serial_number, arg_info)
                    if flow_info_dict:
                        create_ticket = self.create_ticket(flow_info_dict)
                        res = {
                            "msg": "已创建工单",
                            "data": [str(create_ticket.get("TicketId"))],
                        }
                    else:
                        res = {
                            "msg": "流程信息获取失败，无法自动建单",
                            "data": None,
                        }
                return res
            else:
                exit_ticket = self.query_exit_ticket(project_name, serial_number)
                if exit_ticket:
                    res = {
                        "msg": "平台已有工单，无需自动建单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    res = {
                        "msg": "无需自动建单(不在计划时间范围内)；平台未查询到工单，若需要补单或有其它疑问请联系管理员",
                        "data": None,
                    }
                return res
        else:
            return {"msg": "计划开始/完成时间为空，无需自动建单", "data": None}

    # 获取流程建单信息
    def get_flow_info(self, serial_number, arg_info):
        project_name = arg_info.get("project_name")
        campus = arg_info.get("campus")
        PM = arg_info.get("PM")
        key = "own"
        cooperative_partner_serial_number = ["2.1", "2.4", "2.7", "2.9", "3", "4", "5"]
        is_it_cooperative_partner, Facilitator = self.is_it_cooperative_partner(
            arg_info
        )
        if (
            is_it_cooperative_partner
            and serial_number in cooperative_partner_serial_number
        ):
            key = "partner"
            # 2.4 2.9 由对方建单(腾讯侧调用接口触发对方建单)
            if serial_number in ["2.4", "2.9"]:
                return False
        flow_dict = {
            "2.1": {
                "own": {
                    "ProcessDefinitionKey": "obtaining_documents_during_detailed_design_phase",
                    "processTitle": f"{project_name}深化设计资料审核",
                    "CustomVariables": {
                        "project_name": project_name,
                        "integrator": Facilitator,
                    },
                    "Concern": "v_zongxiyu",
                    "Deal": "v_zongxiyu",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_deepen_design",
                    "processTitle": f"{project_name}深化设计资料审核",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "2.4": {
                "own": {
                    "ProcessDefinitionKey": "project_construction_application",
                    "processTitle": f"{project_name}项目报建过程记录",
                    "CustomVariables": {"project_name": project_name},
                    "Concern": "v_vikyjiang",
                    "Deal": "v_vikyjiang",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_project_construction_application",
                    "processTitle": f"{project_name}项目报建过程记录",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "2.9": {
                "own": {
                    "ProcessDefinitionKey": "mains_power_special",
                    "processTitle": f"{project_name}市电专项流程",
                    "CustomVariables": {"project_name": project_name},
                    "Concern": "v_vikyjiang",
                    "Deal": "v_vikyjiang",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_mains_special",
                    "processTitle": f"{project_name}市电专项流程",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "2.7": {
                "own": {
                    "ProcessDefinitionKey": "construction_before_equipment_arrival",
                    "processTitle": f"{project_name}项目施工前准备阶段",
                    "CustomVariables": {
                        "project_name": project_name,
                        "campus": campus,
                    },
                    "Concern": "v_zongxiyu",
                    "Deal": "v_zongxiyu",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_project_open",
                    "processTitle": f"{project_name}项目开办",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "3": {
                "own": {
                    "ProcessDefinitionKey": "project_site_implementation",
                    "processTitle": f"{project_name}项目安装施工",
                    "CustomVariables": {
                        "project_name": project_name,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_installation_construction",
                    "processTitle": f"{project_name}项目安装施工",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "4": {
                "own": {
                    "ProcessDefinitionKey": "construct_test_phase_bak",
                    "processTitle": f"{project_name}第三方测试启动",
                    "CustomVariables": {
                        "project_name": project_name,
                    },
                    "Concern": "v_zongxiyu",
                    "Deal": "v_zongxiyu",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_test_verification",
                    "processTitle": f"{project_name}第三方测试启动",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
            "5": {
                "own": {
                    "ProcessDefinitionKey": "construction_maintenance_platform_docking",
                    "processTitle": f"{project_name}建设转维过程跟踪",
                    "CustomVariables": {
                        "project_name": project_name,
                    },
                    "Concern": "v_zongxiyu",
                    "Deal": "v_zongxiyu",
                },
                "partner": {
                    "ProcessDefinitionKey": "cooperative_partner_construction_maintenance_platform_docking",
                    "processTitle": f"{project_name}建设转维过程跟踪",
                    "CustomVariables": {
                        "project_name": project_name,
                        "Facilitator": Facilitator,
                    },
                    "Concern": "v_mmywang",
                    "Deal": "v_mmywang",
                },
            },
        }
        flow_info_dict = flow_dict.get(serial_number).get(key)
        return flow_info_dict

    def is_it_cooperative_partner(self, arg_info):
        """
        判断是否为合作伙伴
        """
        project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        campus = arg_info.get("campus")
        project = arg_info.get("project")
        Facilitator = project_jcs_dict.get(campus + project)
        # db = mysql.new_mysql_instance("tbconstruct")
        # sql = (
        #     "SELECT integrators "
        #     "FROM supplier_resources "
        #     f"WHERE campus = '{campus}' "
        #     f"AND project = '{project}' "
        # )
        # result_list = db.get_all(sql)
        # for row in result_list:
        #     Facilitator = row.get("integrators")
        # Facilitator = row.get("integrators")
        # 获取合作伙伴名单
        cooperative_partner_process = config.get_config_map(
            "cooperative_partner_process"
        )
        # 判断是否为合作伙伴
        if Facilitator in cooperative_partner_process:
            return True, Facilitator
        else:
            return False, Facilitator

    # 自动起单
    def create_ticket(self, flow_dict):
        data = {
            "CustomVariables": flow_dict.get("CustomVariables"),
            "ProcessDefinitionKey": flow_dict.get("ProcessDefinitionKey"),
            "Source": "",
            "TicketDescription": flow_dict.get("processTitle"),
            "TicketLevel": "3",
            "TicketTitle": flow_dict.get("processTitle"),
            "UserInfo": {
                "Concern": flow_dict.get("Concern"),  # 关注人 可填多个
                "Creator": "dcops",  # 建单人 只能填一个
                "Deal": flow_dict.get(
                    "Deal"
                ),  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
            },
        }
        res = gnetops.request(action="Ticket", method="Create", data=data)
        return res

    # 催办：获取项目关键节点最新总控计划
    def get_plan2(self, project_list):
        """
        催办：获取项目关键节点最新总控计划
        """
        serial_number_list = ["2.4", "2.8", "2.9", "3"]
        project_plan_dict2 = {}
        for p in project_list:
            project_name = p.get("project_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = self.get_plan_sql(project_name, serial_number_list)
            plan_list = db.query(sql)
            project_plan_dict2[project_name] = plan_list
        variables = {
            "project_plan_dict2": project_plan_dict2,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 计划解析：根据计划完成时间催办
    def plan_analysis2(self, project_plan_dict2):
        """
        计划解析：根据计划完成时间催办
        """
        reminder_ticket_info_list = []
        project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        for project_name, plan_info in project_plan_dict2.items():
            jcs = project_jcs_dict.get(project_name)
            for p in plan_info:
                serial_number = p.get("serial_number")
                end_time = p.get("completion_time")
                reminder_flag = self.reminder_ticket_analysis(serial_number, end_time)
                if reminder_flag:
                    exit_ticket = self.query_exit_ticket(
                        project_name, serial_number, True
                    )
                    if exit_ticket:
                        # p["reminder_ticket_info"] = exit_ticket
                        # jcs = self.get_integrators(project_name)
                        for t in exit_ticket:
                            cost_time = self.get_max_cost_time(
                                json.loads(t.get("CurrentTaskDetails"))
                            )
                            item = {
                                "project_name": project_name,
                                "PM": p.get("PM"),
                                "serial_number": serial_number,
                                "work_content": p.get("work_content"),
                                "start_time": p.get("start_time"),
                                "end_time": p.get("completion_time"),
                                "ticket_id": t.get("TicketId"),
                                "title": t.get("Title"),
                                "create_time": t.get("CreateTime"),
                                "current_tasks": t.get("CurrentTasks"),
                                "current_all_process_users": t.get(
                                    "CurrentAllProcessUsers"
                                ),
                                "reason": "已到计划完成时间未完成工单",
                                "cost_time": cost_time,
                                "jcs": jcs,
                            }
                            reminder_ticket_info_list.append(item)
        variables = {
            "project_plan_dict2": project_plan_dict2,
            "reminder_ticket_info_list": reminder_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_max_cost_time(self, data):
        """
        计算当前待办最大耗时
        """
        cost_time = ""
        min_start_time = None
        if data:
            for item in data:
                start_time = item.get("StartTime")
                if start_time:
                    start_time_temp = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    if not min_start_time:
                        min_start_time = start_time_temp
                    min_start_time = min(min_start_time, start_time_temp)
            now = datetime.today()
            time_difference = now - min_start_time
            days = time_difference.days
            hours = time_difference.seconds // 3600
            cost_time = f"{days}天{hours}小时"
        return cost_time

    # 自动催办逻辑判断
    def reminder_ticket_analysis(self, serial_number, end_time):
        """
        2.4 项目报建（含消防）
        2.9 市电专项
        # 2.7 项目开办
        2.8 设备进场前施工
        3 安装施工
        2.5 子单11个品类
        """
        if end_time:
            today = datetime.today().date()
            time = datetime.strptime(end_time, "%Y-%m-%d").date()
            serial_number_today = ["2.4", "2.8", "2.9", "3"]
            if today >= time and (
                serial_number in serial_number_today or serial_number.startswith("2.5.")
            ):
                return True
        return False

    # 变更：获取项目关键节点最新总控计划（5.2）
    def get_plan3(self, project_list):
        """
        变更：获取项目关键节点最新总控计划（5.2）
        """
        serial_number_list = ["5.2"]
        project_plan_dict3 = {}
        for p in project_list:
            project_name = p.get("project_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = self.get_plan_sql(project_name, serial_number_list)
            plan_list = db.query(sql)
            project_plan_dict3[project_name] = plan_list
        variables = {
            "project_plan_dict3": project_plan_dict3,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 计划解析：变更催办
    def plan_analysis3(self, project_plan_dict3):
        """
        计划解析：变更催办
        """
        reminder_change_ticket_info_list = []
        project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        for project_name, plan_info in project_plan_dict3.items():
            jcs = project_jcs_dict.get(project_name)
            for p in plan_info:
                serial_number = p.get("serial_number")
                end_time = p.get("completion_time")
                if end_time:
                    end_time_temp = datetime.strptime(end_time, "%Y-%m-%d").date()
                    end_time_one_month_ago = end_time_temp - relativedelta(month=1)
                    today = datetime.today().date()
                    if today >= end_time_one_month_ago and serial_number == "5.2":
                        exit_ticket = self.query_exit_ticket(
                            project_name, serial_number, True
                        )
                        if exit_ticket:
                            # p["reminder_change_ticket_info"] = exit_ticket
                            # jcs = self.get_integrators(project_name)
                            for t in exit_ticket:
                                if t.get("TicketStatus") != "END":
                                    cost_time = self.get_max_cost_time(
                                        json.loads(t.get("CurrentTaskDetails"))
                                    )
                                    item = {
                                        "project_name": project_name,
                                        "PM": p.get("PM"),
                                        "serial_number": "-",
                                        "work_content": "TB变更",
                                        "start_time": "-",
                                        "end_time": "-",
                                        "ticket_id": t.get("TicketId"),
                                        "title": t.get("Title"),
                                        "create_time": t.get("CreateTime"),
                                        "current_tasks": t.get("CurrentTasks"),
                                        "current_all_process_users": t.get(
                                            "CurrentAllProcessUsers"
                                        ),
                                        "reason": "初验计划完成时间前一个月未完成变更指令下发",
                                        "cost_time": cost_time,
                                        "jcs": jcs,
                                    }
                                    reminder_change_ticket_info_list.append(item)
                    elif serial_number == "5.2":
                        exit_ticket = self.query_exit_ticket(
                            project_name, serial_number, True
                        )
                        # p["reminder_change_ticket_info"] = exit_ticket
                        # jcs = self.get_integrators(project_name)
                        for t in exit_ticket:
                            create_time = t.get("CreateTime")
                            create_time_temp = datetime.strptime(
                                create_time, "%Y-%m-%d %H:%M:%S"
                            )
                            create_time_one_month_later = (
                                create_time_temp + relativedelta(month=1)
                            )
                            now = datetime.today()
                            if now >= create_time_one_month_later:
                                cost_time = self.get_max_cost_time(
                                    json.loads(t.get("CurrentTaskDetails"))
                                )
                                item = {
                                    "project_name": project_name,
                                    "PM": p.get("PM"),
                                    "serial_number": "-",
                                    "work_content": "TB变更",
                                    "start_time": "-",
                                    "end_time": "-",
                                    "ticket_id": t.get("TicketId"),
                                    "title": t.get("Title"),
                                    "create_time": t.get("CreateTime"),
                                    "current_tasks": t.get("CurrentTasks"),
                                    "current_all_process_users": t.get(
                                        "CurrentAllProcessUsers"
                                    ),
                                    "reason": "建单后一个月未完成变更指令下发",
                                    "cost_time": cost_time,
                                    "jcs": jcs,
                                }
                                reminder_change_ticket_info_list.append(item)
                elif serial_number == "5.2":
                    exit_ticket = self.query_exit_ticket(
                        project_name, serial_number, True
                    )
                    # p["reminder_change_ticket_info"] = exit_ticket
                    # jcs = self.get_integrators(project_name)
                    for t in exit_ticket:
                        create_time = t.get("CreateTime")
                        create_time_temp = datetime.strptime(
                            create_time, "%Y-%m-%d %H:%M:%S"
                        )
                        create_time_one_month_later = create_time_temp + relativedelta(
                            month=1
                        )
                        now = datetime.today()
                        if now >= create_time_one_month_later:
                            cost_time = self.get_max_cost_time(
                                json.loads(t.get("CurrentTaskDetails"))
                            )
                            item = {
                                "project_name": project_name,
                                "PM": p.get("PM"),
                                "serial_number": "-",
                                "work_content": "TB变更",
                                "start_time": "-",
                                "end_time": "-",
                                "ticket_id": t.get("TicketId"),
                                "title": t.get("Title"),
                                "create_time": t.get("CreateTime"),
                                "current_tasks": t.get("CurrentTasks"),
                                "current_all_process_users": t.get(
                                    "CurrentAllProcessUsers"
                                ),
                                "reason": "建单后一个月未完成变更指令下发",
                                "cost_time": cost_time,
                                "jcs": jcs,
                            }
                            reminder_change_ticket_info_list.append(item)
        variables = {
            "project_plan_dict3": project_plan_dict3,
            "reminder_change_ticket_info_list": reminder_change_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 设备：获取项目关键节点最新总控计划（2.5.x）
    def get_plan4(self, project_list):
        """
        设备：获取项目关键节点最新总控计划（2.5.x）
        """
        project_plan_dict4 = {}
        for p in project_list:
            project_name = p.get("project_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT t1.project_name, t1.serial_number, t1.work_content, t1.start_time, t1.completion_time, "
                "t3.campus, t3.project, t3.PM FROM construct_plan_data t1 "
                "JOIN risk_early_warning_data t3 ON t1.project_name = t3.project_name "
                f"WHERE t1.project_name = '{project_name}' AND t1.serial_number LIKE '2.5.%' "
                "AND t1.not_involved = 0"
            )
            plan_list = db.query(sql)
            project_plan_dict4[project_name] = plan_list
        variables = {
            "project_plan_dict4": project_plan_dict4,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 计划解析：设备生产跟踪催办
    def plan_analysis4(self, project_plan_dict4):
        """
        计划解析：设备生产跟踪催办
        """
        reminder_device_ticket_info_list = []
        project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        for project_name, plan_info in project_plan_dict4.items():
            jcs = project_jcs_dict.get(project_name)
            for p in plan_info:
                serial_number = p.get("serial_number")
                work_content = p.get("work_content")
                end_time = p.get("completion_time")
                reminder_flag = self.reminder_ticket_analysis(serial_number, end_time)
                if reminder_flag:
                    exit_ticket = self.query_exit_ticket4(
                        project_name, work_content, "2.5", True
                    )
                    if exit_ticket:
                        # p["reminder_ticket_info"] = exit_ticket
                        # jcs = self.get_integrators(project_name)
                        for t in exit_ticket:
                            cost_time = self.get_max_cost_time(
                                json.loads(t.get("CurrentTaskDetails"))
                            )
                            item = {
                                "project_name": project_name,
                                "PM": p.get("PM"),
                                "serial_number": serial_number,
                                "work_content": p.get("work_content"),
                                "start_time": p.get("start_time"),
                                "end_time": p.get("completion_time"),
                                "ticket_id": t.get("TicketId"),
                                "title": t.get("Title"),
                                "create_time": t.get("CreateTime"),
                                "current_tasks": t.get("CurrentTasks"),
                                "current_all_process_users": t.get(
                                    "CurrentAllProcessUsers"
                                ),
                                "reason": "已到计划完成时间未完成工单",
                                "cost_time": cost_time,
                                "jcs": jcs,
                            }
                            reminder_device_ticket_info_list.append(item)
        variables = {
            "project_plan_dict4": project_plan_dict4,
            "reminder_device_ticket_info_list": reminder_device_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 查询是否有建单
    def query_exit_ticket4(
        self, project_name, work_content, serial_number, is_open=False
    ):
        flow_dict = {
            "2.5": [
                "equipment_production_tracking_management",
                "cooperative_partner_equipment_production_arrived",
                "cooperative_partner_equipment_production_arrived_wd",
            ],
        }
        flow_list = flow_dict.get(serial_number)
        if flow_list:
            flow_temp = ", ".join(f"'{item}'" for item in flow_list)
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT * FROM all_construction_ticket_data "
                f"WHERE Title like '%{project_name}%' "
                f"AND Title like '%{work_content}%' "
                "AND IsForceEnd = '0' "
                f"AND ProcessDefinitionKey IN ({flow_temp})"
            )
            if is_open:
                sql += " AND TicketStatus != 'END'"
            res = db.query(sql)
            return res
        return False

    def get_all_reminder_ticket_info_list(
        self,
        reminder_ticket_info_list,
        reminder_change_ticket_info_list,
        reminder_device_ticket_info_list,
        reminder_start_ticket_info_list,
    ):
        """
        按项目归类
        """
        project_dict = {}
        if reminder_start_ticket_info_list:
            for item in reminder_start_ticket_info_list:
                project_name = item.get("project_name")
                if item.get("project_name"):
                    if project_dict.get(item.get("project_name")):
                        project_dict[project_name].append(item)
                    else:
                        project_dict[project_name] = []
                        project_dict[project_name].append(item)
        if reminder_ticket_info_list:
            for item in reminder_ticket_info_list:
                project_name = item.get("project_name")
                if item.get("project_name"):
                    if project_dict.get(item.get("project_name")):
                        project_dict[project_name].append(item)
                    else:
                        project_dict[project_name] = []
                        project_dict[project_name].append(item)
        if reminder_device_ticket_info_list:
            for item in reminder_device_ticket_info_list:
                project_name = item.get("project_name")
                if item.get("project_name"):
                    if project_dict.get(item.get("project_name")):
                        project_dict[project_name].append(item)
                    else:
                        project_dict[project_name] = []
                        project_dict[project_name].append(item)
        if reminder_change_ticket_info_list:
            for item in reminder_change_ticket_info_list:
                project_name = item.get("project_name")
                if item.get("project_name"):
                    if project_dict.get(item.get("project_name")):
                        project_dict[project_name].append(item)
                    else:
                        project_dict[project_name] = []
                        project_dict[project_name].append(item)
        all_reminder_ticket_info_list = []
        for key, val in project_dict.items():
            all_reminder_ticket_info_list += val
        variables = {
            "all_reminder_ticket_info_list": all_reminder_ticket_info_list,
            "project_reminder_ticket_dict": project_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 项目启动催办（需求下发后12天内未完成项目启动）
    def project_start_analysis(self, project_list):
        """
        项目启动催办（需求下发后12天内未完成项目启动）
        """
        reminder_start_ticket_info_list = []
        if project_list:
            project_temp = ", ".join(
                f"""'{item.get("project_name")}'""" for item in project_list
            )
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT t1.project_name, t1.demand_distribution, t1.dcopsTicketId, t1.PM "
                "FROM risk_early_warning_data t1 "
                f"WHERE t1.project_name in ({project_temp})"
            )
            info_list = db.query(sql)
            project_jcs_dict = self.ctx.variables.get("project_jcs_dict", {})
            for i in info_list:
                demand_distribution = i.get("demand_distribution")
                if demand_distribution:
                    demand_distribution_temp = datetime.strptime(
                        demand_distribution, "%Y-%m-%d"
                    ).date()
                    demand_distribution_two_month_later = (
                        demand_distribution_temp + relativedelta(days=12)
                    )
                    today = datetime.today().date()
                    if today >= demand_distribution_two_month_later:
                        ticket_info = self.query_ticket(i.get("dcopsTicketId"), True)
                        if ticket_info:
                            # jcs = self.get_integrators(i.get("project_name"))
                            jcs = project_jcs_dict.get(i.get("project_name"))
                            for t in ticket_info:
                                cost_time = self.get_max_cost_time(
                                    json.loads(t.get("CurrentTaskDetails"))
                                )
                                item = {
                                    "project_name": i.get("project_name"),
                                    "PM": i.get("PM"),
                                    "serial_number": "-",
                                    "work_content": "项目启动",
                                    "start_time": "-",
                                    "end_time": "-",
                                    "ticket_id": t.get("TicketId"),
                                    "title": t.get("Title"),
                                    "create_time": t.get("CreateTime"),
                                    "current_tasks": t.get("CurrentTasks"),
                                    "current_all_process_users": t.get(
                                        "CurrentAllProcessUsers"
                                    ),
                                    "reason": "需求下发后12天内未完成项目启动",
                                    "cost_time": cost_time,
                                    "jcs": jcs,
                                }
                                reminder_start_ticket_info_list.append(item)
        variables = {
            "reminder_start_ticket_info_list": reminder_start_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 查询工单by工单号
    def query_ticket(self, ticket_id, is_open=False):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT * FROM all_construction_ticket_data "
        if ticket_id:
            sql += f"WHERE TicketId = '{ticket_id}' "
        if is_open:
            sql += "AND TicketStatus != 'END' "
        sql += "AND IsForceEnd = '0' "
        res = db.query(sql)
        return res

    # 获取集成商
    def get_integrators(self, project_name):
        integrators = ""
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT integrators FROM supplier_resources "
            f"WHERE CONCAT(campus, project) = '{project_name}' "
        )
        res = db.query(sql)
        if res:
            integrators = res[0].get("integrators")
        return integrators

    # 整理PM建单数据
    def get_pm_project_data(
        self, project_create_ticket_dict, project_reminder_ticket_dict
    ):
        """
        整理PM建单数据
        """
        data = {}
        for key in set(project_create_ticket_dict.keys()) | set(
            project_reminder_ticket_dict.keys()
        ):
            create_ticket_info_list = project_create_ticket_dict.get(key, [])
            all_reminder_ticket_info_list = project_reminder_ticket_dict.get(key, [])
            PM = "youngshi"
            if create_ticket_info_list:
                PM = create_ticket_info_list[0].get("PM")
            elif all_reminder_ticket_info_list:
                PM = all_reminder_ticket_info_list[0].get("PM")
            data[key] = {
                "create": create_ticket_info_list,
                "reminder": all_reminder_ticket_info_list,
                "PM": PM,
            }
        variables = {
            "create_ticket_to_pm_data": data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 按项目给PM建单
    def create_ticket_to_pm(self, create_ticket_to_pm_data):
        """
        按项目给PM建单
        """
        create_ticket_to_pm_dict = {}
        for project_name, val in create_ticket_to_pm_data.items():
            PM = val.get("PM", "youngshi")
            flow_info_dict = {
                "ProcessDefinitionKey": "construction_plan_automation_pm",
                "processTitle": f"{project_name}自动建单、工单催办",
                "CustomVariables": {
                    "project_name": project_name,
                    "PM": PM,
                    "create_ticket_info_list": val.get("create"),
                    "all_reminder_ticket_info_list": val.get("reminder"),
                },
                "Concern": PM,
                "Deal": PM,
            }
            create_ticket = self.create_ticket(flow_info_dict)
            create_ticket_to_pm_dict[project_name] = str(create_ticket.get("TicketId"))
        variables = {
            "create_ticket_to_pm_dict": create_ticket_to_pm_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitProjectTicketInfoConfirm(object):
    """
    循环等待"自动建单、自动催办信息汇总"完成
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_confirm(self):
        """
        循环等待"自动建单、自动催办信息汇总"完成
        """
        instance_id = self.ctx.instance_id
        data_instanceId = {"InstanceId": instance_id}
        # 请求获取工作流日志
        res = gnetops.request(
            action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId
        )
        for row in res:
            TaskName = row.get("TaskName")
            TaskStatus = row.get("TaskStatus")
            # 处理 "自动建单、自动催办信息汇总" 任务
            if TaskName == "自动建单、自动催办信息汇总":
                if TaskStatus != "已完成":
                    TaskId = row.get("TaskId")
                    CreateTime = row.get("CreateTime")
                    create_time = datetime.strptime(CreateTime, "%Y-%m-%d %H:%M:%S")
                    create_time_later = create_time + relativedelta(days=1)
                    today = datetime.today()
                    if today >= create_time_later:
                        WorkflowContinue(task_id=TaskId).call()
                        flow.complete_task(self.ctx.task_id)
                        return {"success": True, "data": "已到时间"}
                    else:
                        return {"success": False, "data": "未到时间"}
                elif TaskStatus == "已完成":
                    flow.complete_task(self.ctx.task_id)
                    return {"success": True, "data": "已手动提交"}
