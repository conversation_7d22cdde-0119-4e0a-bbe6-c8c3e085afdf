import json

from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow

from biz.quality_evaluation.rating_handle import ProjectRatingHandle


# 质量评估数据入库类
class QualityEvaluationIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_info_into_db(self):
        """
        项目信息存入数据库(含评分附件)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_info_list = self.ctx.variables.get("project_info_list")
        business_rater = self.ctx.variables.get("business_rater")
        supply_chain_rater = self.ctx.variables.get("supply_chain_rater")
        planning_rater = self.ctx.variables.get("planning_rater")
        product_rater = self.ctx.variables.get("product_rater")
        project_rater = self.ctx.variables.get("project_rater")
        optimize_rater = self.ctx.variables.get("optimize_rater")
        weak_electricity_rater = self.ctx.variables.get("weak_electricity_rater")
        internal_control_rater = self.ctx.variables.get("internal_control_rater")
        business_file_json = self.ctx.variables.get("business_file_json")
        supply_chain_file_json = self.ctx.variables.get("supply_chain_file_json")
        planning_file_json = self.ctx.variables.get("planning_file_json")
        product_file_json = self.ctx.variables.get("product_file_json")
        project_file_json = self.ctx.variables.get("project_file_json")
        optimize_file_json = self.ctx.variables.get("optimize_file_json")
        weak_electricity_file_json = self.ctx.variables.get(
            "weak_electricity_file_json"
        )
        project_info_insert = []
        for prject in project_info_list:
            project_info_insert.append(
                {
                    "project_name": prject.get("project_name"),
                    "first_test_date": prject.get("first_test_date"),
                    "pm": prject.get("pm"),
                    "business_rater": business_rater,
                    "supply_chain_rater": supply_chain_rater,
                    "planning_rater": planning_rater,
                    "product_rater": product_rater,
                    "project_rater": project_rater,
                    "optimize_rater": optimize_rater,
                    "weak_electricity_rater": weak_electricity_rater,
                    "internal_control_rater": internal_control_rater,
                    "ticket_id": ticket_id,
                    "business_file_json": json.dumps(
                        business_file_json, ensure_ascii=False
                    ),
                    "supply_chain_file_json": json.dumps(
                        supply_chain_file_json, ensure_ascii=False
                    ),
                    "planning_file_json": json.dumps(
                        planning_file_json, ensure_ascii=False
                    ),
                    "product_file_json": json.dumps(
                        product_file_json, ensure_ascii=False
                    ),
                    "project_file_json": json.dumps(
                        project_file_json, ensure_ascii=False
                    ),
                    "optimize_file_json": json.dumps(
                        optimize_file_json, ensure_ascii=False
                    ),
                    "weak_electricity_file_json": json.dumps(
                        weak_electricity_file_json, ensure_ascii=False
                    ),
                }
            )
        if project_info_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("quality_evaluation_project", project_info_insert)
            tb_db.commit()
        variables = {"project_info_insert": project_info_insert}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def team_rating_info_into_db(self):
        """
        团队评分信息存入数据库
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        all_flat_score_list = self.ctx.variables.get("all_flat_score_list")
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        team_rating_insert = []
        for rating in all_flat_score_list:
            project_name = rating.get("project_name")
            project_category_basic_info = project_category_basic_info_dict[project_name]
            category_en_cn_field_dict = project_category_basic_info[
                "category_en_cn_field_dict"
            ]
            team_rating_insert.append(
                {
                    "project_name": project_name,
                    "team_name": rating.get("team_name"),
                    "key_indicator": rating.get("key_indicator"),
                    "evaluation_dimensions": rating.get("evaluation_dimensions"),
                    "assessment_point": rating.get("assessment_point"),
                    "scoring_rubrics": rating.get("scoring_rubrics"),
                    "category_eq_class_id": rating.get("category_eq_class_id"),
                    "category_eq_class_name": rating.get("category_eq_class_name"),
                    "dimension_weight": str(rating.get("dimension_weight", "")),
                    "team_weight": str(rating.get("team_weight", "")),
                    "category_name": (
                        category_en_cn_field_dict[rating.get("category_name")]
                        if rating.get("category_name")
                        else ""
                    ),
                    "category_score": rating.get("category_score"),
                    "ticket_id": ticket_id,
                }
            )
        if team_rating_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "quality_evaluation_project_team_rating", team_rating_insert
            )
            tb_db.commit()
        variables = {"team_rating_insert": team_rating_insert}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def project_rating_info_into_db(self):
        """
        项目评分信息存入数据库
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_category_score_list = self.ctx.variables.get(
            "project_category_score_list"
        )
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        project_category_info = ProjectRatingHandle.get_project_category_info(
            project_category_score_list, project_category_basic_info_dict
        )
        project_rating_insert = []
        for project_name, category_info in project_category_info.items():
            project_category_basic_info = project_category_basic_info_dict[project_name]
            category_en_cn_field_dict = project_category_basic_info[
                "category_en_cn_field_dict"
            ]
            for category, info in category_info.items():
                project_rating_insert.append(
                    {
                        "ticket_id": ticket_id,
                        "project_name": project_name,
                        "category_name": category_en_cn_field_dict[category],
                        "category_supplier": info["category_supplier"],
                        "category_score": info["category_score"],
                        "category_level": info["category_level"],
                        "red_yellow_line": info["red_yellow_line"],
                        "red_yellow_line_remark": info["red_yellow_line_remark"],
                        "ticket_id": ticket_id,
                    }
                )
        if project_rating_insert:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "quality_evaluation_project_rating", project_rating_insert
            )
            tb_db.commit()
        variables = {
            "project_category_info": project_category_info,
            "project_rating_insert": project_rating_insert,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
