from iBroker.lib import mysql


class Tools(object):

    # 获取项目名称
    @staticmethod
    def get_project_name(module_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT project_name FROM project_module_mapping_relationship WHERE module_name = '{module_name}'"
        project_name_list = db.get_all(sql)
        project_name = ""
        if project_name_list:
            project_name = project_name_list[0].get("project_name")
        return project_name

    # 获取项目角色账号
    @staticmethod
    def query_project_role_account(project_name, role):
        query_sql = (
            f"SELECT role, account FROM project_role_account WHERE project_name = '{project_name}' "
            f"AND role IN ({role}) "
            "AND del_flag = '0'"
        )
        db = mysql.new_mysql_instance("tbconstruct")
        query_result = db.query(query_sql)
        return query_result
    
    # 获取项目PM
    @staticmethod
    def get_PM(project_name):
        query_sql = f"SELECT PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        PM = ""
        if query_result and query_result[0]:
            PM = query_result[0].get("PM")
        return PM
