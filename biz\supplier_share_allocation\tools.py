from iBroker.lib import mysql


class Tools(object):

    # 获取对应品类供应商数据
    @staticmethod
    def get_category_supplier_info():
        query_sql = f"select * from category_supplier_info"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        category_supplier_dict = {}
        if query_result:
            category_supplier_dict = {
                item.get("category"): item.get("supplier") for item in query_result
            }
        return category_supplier_dict
