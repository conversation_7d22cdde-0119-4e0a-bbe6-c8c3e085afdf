from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql

# 项目名称变更
class ProjectNameChange(AjaxTodoBase):
    def __init__(self):
        super(ProjectNameChange, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        new_campus = process_data.get("new_campus")
        new_project = process_data.get("new_project")
        if not new_campus or not new_project:
            return {"code": -1, "msg": "变更后园区、项目不能为空"}
        old_campus = process_data.get("old_campus")
        old_project = process_data.get("old_project")
        old_project_name = old_campus + old_project
        new_project_name = new_campus + new_project
        if old_project_name == new_project_name:
            return {"code": -1, "msg": "变更前后项目名称不能一致"}
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT project_name FROM risk_early_warning_data WHERE project_name = '{new_project_name}' "
        res = db.query(sql)
        if res:
            return {"code": -1, "msg": "变更后项目名称(园区+项目)已存在"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "new_campus": new_campus,
            "new_project": new_project,
            "leader_approval": "同意",
            "leader_remark": "",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 项目名称变更-领导审批
class ProjectNameChangeLeaderApproval(AjaxTodoBase):
    def __init__(self):
        super(ProjectNameChangeLeaderApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval", "")
        leader_remark = process_data.get("leader_remark", "")
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "leader_approval": leader_approval,
            "leader_remark": leader_remark,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
