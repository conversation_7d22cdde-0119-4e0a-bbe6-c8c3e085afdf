#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2024/1/17 16:00
# <AUTHOR> v_hxhaoliu
# @File    : idcdb_tools.py
from datetime import datetime

from iBroker.lib.sdk import idcdb


def get_region(mozu: str) -> str:
    """
    使用idcdb查询 模组 对应的 区域
    """
    mozu = idcdb.get_dictionary(
        table='mozu', fields=['region_name'], search_condition={'mozu_name': [mozu]}
    )
    if mozu:
        return mozu['region_name']
    return ''


def cut_mozu(mozu_name: str) -> str:
    """
    对MozuName字段进行截取，MozuName是一个字符串，形式为 '["重庆泰和模组01","重庆泰和模组02","重庆泰和模组03"]'
    :param mozu_name: MozuName字段值
    :return: 返回数组中的第一个模组，如 重庆泰和模组01
    """
    mozu_name = mozu_name[2:-2]
    b = mozu_name.split(',')
    if len(b) >= 2:
        return b[0][0:-1]
    else:
        return b[0]


def get_campus(mozu: str) -> str:
    """
    使用idcdb查询 模组 对应的 园区
    """
    mozu = idcdb.get_dictionary(
        table='mozu', fields=['campus_name'], search_condition={'mozu_name': [mozu]}
    )
    if mozu:
        return mozu['campus_name']
    return ''

def get_infrastructurePrincipal(mozu: str) -> str:
    """
    模组负责人
    """
    mozu = idcdb.get_dictionary(
        table='mozu', fields=['mozu_infrastructurePrincipal'], search_condition={'mozu_name': [mozu]}
    )
    if mozu:
        return mozu['mozu_infrastructurePrincipal']
    return ''


def get_now() -> str:
    """
    获取当前的格式化的时间
    """
    today = datetime.today()
    today = today.strftime("%Y-%m-%d %H:%M:%S")
    return today
