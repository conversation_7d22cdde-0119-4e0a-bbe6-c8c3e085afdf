import ast
import copy
import json
import re
from collections import defaultdict

from iBroker.lib.sdk import gnetops, flow, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config


class ProcurementStageProgressDisplay(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def check_ticket_status(self, ticket_id):
        """
            参数:ticket_id(str或List[str])
            返回:List[dict]
        """
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "TicketId": "",
                    "InstanceId": "",
                    "TicketStatus": "",
                },
                "SearchCondition": {
                    "TicketId": ticket_id
                }
            }
        }
        query_result = gnetops.request(
            action="QueryData",
            method="Run",
            ext_data=query_data
        )
        ticket_result = query_result.get('List')
        return ticket_result

    def check_children_tickets(self, children_ticket_id):
        """
            参数:children_ticket_id(list)
            返回:List[dict]
        """
        # # 检查是否为单子流程多子单
        # children_ticket_id_list = []
        # if all(not isinstance(item, list) for item in children_ticket_id):
        #     children_ticket_id_list = children_ticket_id
        #
        # # 检查是否为双子流程多子单
        # if all(isinstance(item, list) for item in children_ticket_id):
        #     children_ticket_id_list = children_ticket_id[0] + children_ticket_id[1]
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "SchemeNameCn": "",
                    "TicketId": "",
                    "InstanceId": "",
                    "TicketStatus": "",
                    "CurrentAllProcessUsers": ""
                },
                "SearchCondition": {
                    "TicketId": children_ticket_id
                }
            }
        }
        query_result = gnetops.request(
            action="QueryData",
            method="Run",
            ext_data=query_data
        )
        children_ticket_result = query_result.get('List')
        return children_ticket_result

    def get_information_about_procurement_project(self, projectName=None, page_size=None, page_number=None,
                                                  year=None,
                                                  type=None,
                                                  account=None):
        """
        :param project_name: 项目名称
        :param page_size: 页码
        :param page_number: 页数
        :param ticket_id: 工单ID
        :param ticket_name: 工单名称
        :param stage_name: 流程标识名称
        :param responsible_person: 负责人
        :return:
        """
        # 查询表中阶段单号
        if not page_size:
            page_size = 10
        if not page_number:
            page_number = 1
        # 计算开始和结束索引
        start_index = (page_number - 1) * page_size
        sql = ("SELECT * FROM procurement_process_information LIMIT %s OFFSET %s" % (page_size, start_index))
        db = mysql.new_mysql_instance("tbconstruct")
        # 查询总记录数
        count_sql = "SELECT COUNT(*) AS total FROM procurement_process_information"
        count_result = db.get_row(count_sql)

        # 获取总记录数
        total = count_result['total'] if count_result and 'total' in count_result else 0
        query = db.get_all(sql)
        table_data = []
        # 总人员列表
        technical_person_list = config.get_config_map("test_question_permissions")
        bid_person_list = config.get_config_map("test_question_permissions")
        if query:
            # table_data[0]["projectDesc"] = query[0]["project_desc"]
            # 循环判断阶段状态并处理对应流程
            for index, item in enumerate(query):
                table_data.append({
                    "type": item["source_of_project"],
                    "projectName": item["project_name"],
                    "currentProgress": ""
                })
                table_data_bak = []
                ticket_id_query = []
                ticket_id_query_map = {}
                for key, values in item.items():
                    if key == "technical_team_formation":
                        if values == "null" or (values is None):
                            table_data[index]["projectStepsInfo"] = {"active": 0}
                            table_data[index]["technicalTeamInfo"] = {
                                "havePermission": True,
                                "isFinish": False,
                                "flowInfo": [{
                                    "mainTicketId": '暂无该阶段主单信息',
                                    "childFlowInfo": [
                                        {
                                            "childFlowName": '',
                                            "childTicketId": '',
                                            "childTicketStatus": '',
                                            "responsiblePerson": '',
                                        },
                                    ]
                                }]
                            }
                        else:
                            values_dict = json.loads(values)
                            # 检查 values 是否为 None 或者已经正确解析为字典
                            if not isinstance(values_dict, dict):
                                raise ValueError(f"解析结果不是字典类型{values}")
                            # 获取 ticket_id 或者设置默认为空字符串
                            ticket_id = values_dict.get("ticket_id", [])
                            children_ticket_id = values_dict.get("children_ticket_id", [])
                            table_data[index]["technicalTeamInfo"] = {}
                            table_data[index]["technicalTeamInfo"]["flowInfo"] = []
                            table_data[index]["technicalTeamInfo"]["havePermission"] = True
                            # 处理主ticket
                            table_data[index]["technicalTeamInfo"]["flowInfo"].append({"mainTicketId": ticket_id[0]})
                            ticket_id_send = ticket_id[0]
                            ticket_id_query.append(ticket_id[0])
                            ticket_id_query_map.update({ticket_id[0]: children_ticket_id})
                            ticket_result = self.check_ticket_status(ticket_id_send)
                            if ticket_result[0].get("TicketStatus") == "OPEN":
                                table_data[index]["technicalTeamInfo"]["isFinish"] = False
                                table_data[index]["projectStepsInfo"] = {"active": 0}
                                if children_ticket_id:
                                    table_data[index]["technicalTeamInfo"]["flowInfo"][0]["childFlowInfo"] = []
                                    # 处理子单状态
                                    children_ticket_result = self.check_children_tickets(children_ticket_id)
                                    for row in children_ticket_result:
                                        if row.get("TicketStatus") == "OPEN":
                                            table_data[index]["technicalTeamInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "进行中"
                                            })
                                        elif row.get("TicketStatus") == "END":
                                            table_data[index]["technicalTeamInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "已完成"
                                            })
                                else:
                                    table_data[index]["technicalTeamInfo"]["flowInfo"][0].update(
                                        {"childFlowInfo": []})
                                    table_data[index]["technicalTeamInfo"]["flowInfo"][0]["childFlowInfo"].append({
                                        "childTicketId": "",
                                        "InstanceId": "",
                                        "childFlowName": "",
                                        "responsiblePerson": "",
                                        "childTicketStatus": ""
                                    })
                            elif ticket_result[0].get("TicketStatus") == "END":
                                table_data[index]["projectStepsInfo"] = {"active": 1}
                                table_data[index]["technicalTeamInfo"]["isFinish"] = True
                                table_data[index]["technicalTeamInfo"]["havePermission"] = True
                                table_data[index]["technicalTeamInfo"]["detailsInfo"] = []
                                sql = (
                                    f"SELECT leader,team_leader,team_members,team_num,team "
                                    f"FROM technical_team_formation "
                                    f"WHERE ticket_id = {ticket_id_send}")
                                db = mysql.new_mysql_instance("tbconstruct")
                                query = db.get_all(sql)
                                for i in query:
                                    table_data[index]["technicalTeamInfo"]["leader"] = i.get("leader")
                                    table_data[index]["technicalTeamInfo"]["detailsInfo"].append({
                                        "team": i.get("team"),
                                        "teamLeader": i.get("team_leader"),
                                        "teamMembers": i.get("team_members"),
                                        "teamNum": i.get("team_num")
                                    })
                                    technical_person_list.append(i.get("team_leader"))
                                    team_members = i.get("team_members").split(";")
                                    technical_person_list.extend(team_members)
                    if key == "technical_documentation_preparation":
                        if values == "null" or (values is None):
                            table_data[index]["technicalFileInfo"] = {
                                "havePermission": True,
                                "isFinish": False,
                                "flowInfo": [{
                                    "mainTicketId": '暂无该阶段主单信息',
                                    "childFlowInfo": [
                                        {
                                            "childFlowName": '',
                                            "childTicketId": '',
                                            "childTicketStatus": '',
                                            "responsiblePerson": '',
                                        },
                                    ]
                                }]
                            }
                        else:
                            values_dict = json.loads(values)
                            # 检查 values 是否为 None 或者已经正确解析为字典
                            if not isinstance(values_dict, dict):
                                raise ValueError(f"解析结果不是字典类型{values}")
                            # 获取 ticket_id 或者设置默认为空字符串
                            ticket_id = values_dict.get("ticket_id", [])
                            children_ticket_id = values_dict.get("children_ticket_id", [])
                            table_data[index]["technicalFileInfo"] = {}
                            table_data[index]["technicalFileInfo"]["flowInfo"] = []
                            table_data[index]["technicalFileInfo"]["havePermission"] = True
                            # 处理主ticket
                            table_data[index]["technicalFileInfo"]["flowInfo"].append({"mainTicketId": ticket_id[0]})
                            ticket_id_send = ticket_id[0]
                            ticket_id_query.append(ticket_id[0])
                            ticket_id_query_map.update({ticket_id[0]: children_ticket_id})
                            ticket_result = self.check_ticket_status(ticket_id_send)
                            # 主流程未结束时
                            if ticket_result[0].get("TicketStatus") == "OPEN":
                                table_data[index]["technicalFileInfo"]["isFinish"] = False
                                table_data[index]["projectStepsInfo"] = {"active": 1}
                                # 处理子tickets
                                if children_ticket_id:
                                    table_data[index]["technicalFileInfo"]["flowInfo"][0]["childFlowInfo"] = []
                                    # 处理子单状态
                                    children_ticket_result = self.check_children_tickets(children_ticket_id)
                                    for row in children_ticket_result:
                                        if row.get("TicketStatus") == "OPEN":
                                            table_data[index]["technicalFileInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "进行中"
                                            })
                                        elif row.get("TicketStatus") == "END":
                                            table_data[index]["technicalFileInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "已完成"
                                            })
                                else:
                                    table_data[index]["technicalFileInfo"]["flowInfo"][0].update(
                                        {"childFlowInfo": []})
                                    table_data[index]["technicalFileInfo"]["flowInfo"][0]["childFlowInfo"].append({
                                        "childTicketId": "",
                                        "InstanceId": "",
                                        "childFlowName": "",
                                        "responsiblePerson": "",
                                        "childTicketStatus": ""
                                    })
                            elif ticket_result[0].get("TicketStatus") == "END":
                                table_data[index]["projectStepsInfo"] = {"active": 2}
                                table_data[index]["technicalFileInfo"]["isFinish"] = True
                                table_data[index]["technicalFileInfo"]["detailsInfo"] = []
                                instance_id = ticket_result[0].get("InstanceId")
                                main_table = flow.get_variables(instance_id, ["table"])["table"]
                                for row in main_table:
                                    table_data[index]["technicalFileInfo"]["detailsInfo"].append({
                                        "fileName": row.get("file_name"),
                                        "adjustmentPart": row.get("adjustment_part"),
                                        "fileUrl": row.get("file")[0]["response"]["FileList"][0]["url"],
                                        "fileUploader": row.get("uploader"),
                                    })
                    if key == "bid_evaluation_team_formation":
                        if values == "null" or (values is None):
                            evaluation_team_info = {
                                "havePermission": True,
                                "isFinish": False,
                                "flowInfo": [{
                                    "mainTicketId": '暂无该阶段主单信息',
                                    "childFlowInfo": [
                                        {
                                            "childFlowName": '',
                                            "childTicketId": '',
                                            "childTicketStatus": '',
                                            "responsiblePerson": '',
                                        },
                                    ]
                                }]
                            }
                            table_data[index]["evaluationTeamInfo"] = evaluation_team_info.copy()
                            table_data[index]["evaluationStandardInfo"] = evaluation_team_info.copy()
                            table_data[index]["bidReturnDocumentInfo"] = evaluation_team_info.copy()
                        else:
                            values_dict = json.loads(values)
                            # 检查 values 是否为 None 或者已经正确解析为字典
                            if not isinstance(values_dict, dict):
                                raise ValueError(f"解析结果不是字典类型{values}")
                            # 获取 ticket_id 或者设置默认为空字符串
                            ticket_id = values_dict.get("ticket_id", [])
                            children_ticket_id = values_dict.get("children_ticket_id", [])
                            table_data[index]["evaluationTeamInfo"] = {}  # 构建技术小组
                            table_data[index]["evaluationStandardInfo"] = {}  # 构建标准文件
                            table_data[index]["bidReturnDocumentInfo"] = {}  # 构建回标文件
                            table_data[index]["evaluationTeamInfo"]["flowInfo"] = []
                            table_data[index]["evaluationStandardInfo"]["flowInfo"] = []
                            table_data[index]["bidReturnDocumentInfo"]["flowInfo"] = []
                            table_data[index]["evaluationTeamInfo"]["havePermission"] = True
                            table_data[index]["evaluationStandardInfo"]["havePermission"] = True
                            table_data[index]["bidReturnDocumentInfo"]["havePermission"] = True
                            # 处理主ticket
                            table_data[index]["evaluationTeamInfo"]["flowInfo"].append({"mainTicketId": ticket_id[0]})
                            table_data[index]["evaluationStandardInfo"]["flowInfo"].append(
                                {"mainTicketId": ticket_id[0]})
                            table_data[index]["bidReturnDocumentInfo"]["flowInfo"].append(
                                {"mainTicketId": ticket_id[0]})
                            ticket_id_send = ticket_id[0]
                            ticket_id_query.append(ticket_id[0])
                            ticket_id_query_map.update({ticket_id[0]: children_ticket_id})
                            ticket_result = self.check_ticket_status(ticket_id_send)
                            instance_id = ticket_result[0].get("InstanceId")
                            # 查询主流程具体进展
                            data_instanceId = {
                                "InstanceId": ticket_result[0].get("InstanceId"),
                            }
                            res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
                            # 初始化三个阶段是否完成的标志
                            team_is_finish = False
                            standard_is_finish = False
                            bid_is_finish = False
                            # 循环判断运行到哪一个节点
                            for row in res:
                                if row.get("TaskName") == "通知小组成员任务":
                                    team_is_finish = True
                                elif row.get("TaskName") == "开标、上传回标文件":
                                    standard_is_finish = True
                                elif row.get("TaskName") == "招采对接起单":
                                    bid_is_finish = True
                            if team_is_finish:
                                table_data[index]["evaluationTeamInfo"]["isFinish"] = True
                                table_data[index]["evaluationTeamInfo"]["detailsInfo"] = []
                                table_data[index]["projectStepsInfo"] = {"active": 3}
                                instance_id = ticket_result[0].get("InstanceId")
                                team_info = flow.get_variables(instance_id,
                                                               ["team_table", "bid_evaluation_team_leader"])
                                table_data[index]["evaluationTeamInfo"]["leader"] = team_info.get(
                                    "bid_evaluation_team_leader")
                                for k in team_info.get("team_table"):
                                    table_data[index]["evaluationTeamInfo"]["detailsInfo"].append({
                                        "team": k.get("team"),
                                        "teamLeader": k.get("party_name"),
                                        "teamNum": k.get("personnel_demand"),
                                        "teamMembers": k.get("personnel_details")
                                    })
                                    bid_person_list.append(k.get("party_name"))
                                    bid_team_members = k.get("personnel_details").split(";")
                                    bid_person_list.extend(bid_team_members)
                            else:
                                table_data[index]["projectStepsInfo"] = {"active": 2}
                                table_data[index]["evaluationTeamInfo"]["isFinish"] = False
                                # 处理子tickets
                                if children_ticket_id:
                                    table_data[index]["evaluationTeamInfo"]["flowInfo"][0]["childFlowInfo"] = []
                                    # 处理子单状态
                                    children_ticket_result = self.check_children_tickets(children_ticket_id)
                                    for row in children_ticket_result:
                                        if row.get("TicketStatus") == "OPEN":
                                            table_data[index]["evaluationTeamInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "进行中"
                                            })
                                        elif row.get("TicketStatus") == "END":
                                            table_data[index]["evaluationTeamInfo"]["flowInfo"][0][
                                                "childFlowInfo"].append({
                                                "childTicketId": row.get("TicketId"),
                                                "InstanceId": row.get("InstanceId"),
                                                "childFlowName": row.get("SchemeNameCn"),
                                                "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                    -1].strip() if row[
                                                    'CurrentAllProcessUsers'] else "",
                                                "childTicketStatus": "已完成"
                                            })
                                else:
                                    table_data[index]["evaluationTeamInfo"]["flowInfo"][0].update(
                                        {"childFlowInfo": []})
                                    table_data[index]["evaluationTeamInfo"]["flowInfo"][0]["childFlowInfo"].append({
                                        "childTicketId": "",
                                        "InstanceId": "",
                                        "childFlowName": "",
                                        "responsiblePerson": "",
                                        "childTicketStatus": ""
                                    })
                            if standard_is_finish:
                                table_data[index]["evaluationStandardInfo"]["isFinish"] = True
                                table_data[index]["evaluationStandardInfo"]["detailsInfo"] = []
                                table_data[index]["projectStepsInfo"] = {"active": 4}
                                team_info = flow.get_variables(instance_id,
                                                               ["bid_evaluation_standard_documents_table"])[
                                    "bid_evaluation_standard_documents_table"]
                                for k in team_info:
                                    table_data[index]["evaluationStandardInfo"]["detailsInfo"].append({
                                        "sectionName": k.get("section_name"),
                                        "standardRemark": k.get("standard_remark"),
                                        "standardFileName": k.get("standard")[0]["name"],
                                        "standardFileUrl": k.get("standard")[0]["response"]["FileList"][0]["url"],
                                    })
                            else:
                                table_data[index]["projectStepsInfo"] = {"active": 3}
                                table_data[index]["evaluationStandardInfo"]["isFinish"] = False

                            if bid_is_finish:
                                replyFileUrl = '无'
                                replyFileName = '无'
                                replyFileList = []
                                faq_document_id = ticket_id[0]
                                if faq_document_id:
                                    sql = ("SELECT ticket_id FROM information_joint_builder_faq "
                                           f"WHERE tender_evaluation_order={faq_document_id} LIMIT 1")
                                    faq_order_query = db.get_row(sql)
                                    if faq_order_query:
                                        faq_ticket_result = self.check_ticket_status(faq_order_query.get("ticket_id"))
                                        instance_id = faq_ticket_result[0].get("InstanceId")
                                        if instance_id:
                                            faq_order_instance_query = flow.get_variables(
                                                instance_id, ["clarification_document_table"])
                                            clarification_document_table = faq_order_instance_query.get(
                                                "clarification_document_table")
                                            if clarification_document_table:
                                                for clarification_document in clarification_document_table:
                                                    replyFileList.append({
                                                        "fileName": clarification_document.get(
                                                            "clarification_document_name"),
                                                        "fileUrl": clarification_document.get("clarification_document"),
                                                    })
                                                # replyFileUrl = clarification_document_table[0].get(
                                                #     "clarification_document"
                                                # )
                                                # replyFileName = clarification_document_table[0].get(
                                                #     "clarification_document_name"
                                                # )

                                table_data[index]["bidReturnDocumentInfo"]["isFinish"] = True
                                table_data[index]["bidReturnDocumentInfo"]["detailsInfo"] = []
                                table_data[index]["projectStepsInfo"] = {"active": 5}
                                instance_id = ticket_result[0].get("InstanceId")
                                ticket_info = flow.get_variables(instance_id,
                                                                 ["bid_evaluation_standard_documents_table",
                                                                  "supplier_list"])
                                team_info = ticket_info.get("bid_evaluation_standard_documents_table")
                                supplier_list = ticket_info.get("supplier_list")
                                # supplier_list = ["华瑞晟（科华）", "中兴", "维谛", "蓝色帛缔（德衡）", "本贸"]
                                for k, m in enumerate(team_info):
                                    table_data[index]["bidReturnDocumentInfo"]["detailsInfo"].append({
                                        "sectionName": m.get("section_name"),
                                        "tableData": []
                                    })
                                    # 初始化辅助字典来累积每个供应商的文件信息
                                    supplier_files = defaultdict(list)

                                    for l in m.get("back_label_file", []):
                                        file_name = l.get("name")
                                        if supplier_list:
                                            supplier = next((sup for sup in supplier_list if sup in file_name),
                                                            '供应商')
                                        else:
                                            supplier = '供应商'

                                        # 累积文件信息
                                        supplier_files[supplier].append({
                                            "fileName": file_name,
                                            "fileUrl": l.get("response", {}).get("FileList", [{}])[0].get("url")
                                        })

                                    # 构建最终的 back_file 列表
                                    back_file = []
                                    for supplier, files in supplier_files.items():
                                        back_file.append({
                                            "supplier": supplier,
                                            "fileName": files[0]["fileName"] if files else None,
                                            "fileUrl": files[0]["fileUrl"] if files else None,
                                            "replyFileUrl": replyFileUrl,
                                            "replyFileName": replyFileName,
                                            "fileList": files,  # 新增字段，包含所有文件信息
                                            "replyFileList": replyFileList
                                        })
                                    table_data[index]["bidReturnDocumentInfo"]["detailsInfo"][k][
                                        "tableData"] = back_file
                            else:
                                table_data[index]["projectStepsInfo"] = {"active": 4}
                                table_data[index]["bidReturnDocumentInfo"]["isFinish"] = False
                    if key == "tender_evaluation":
                        if values == "null" or (values is None):
                            table_data[index]["tenderEvaluationInfo"] = {
                                "havePermission": True,
                                "isFinish": False,
                                "flowInfo": [{
                                    "mainTicketId": '暂无该阶段主单信息',
                                    "childFlowInfo": [
                                        {
                                            "childFlowName": '',
                                            "childTicketId": '',
                                            "childTicketStatus": '',
                                            "responsiblePerson": '',
                                        },
                                    ]
                                }]
                            }
                        else:
                            values_dict = json.loads(values)
                            # 检查 values 是否为 None 或者已经正确解析为字典
                            if not isinstance(values_dict, dict):
                                raise ValueError(f"解析结果不是字典类型{values}")
                            # 获取 ticket_id 或者设置默认为空字符串
                            ticket_id = values_dict.get("ticket_id", [])
                            ticket_id_query += ticket_id
                            children_ticket_id = values_dict.get("children_ticket_id", [])
                            for s in ticket_id:
                                ticket_id_query_map.update({s: children_ticket_id})
                            table_data[index]["tenderEvaluationInfo"] = {}
                            table_data[index]["tenderEvaluationInfo"]["flowInfo"] = []
                            table_data[index]["tenderEvaluationInfo"]["havePermission"] = True
                            # 处理主ticket
                            table_data[index]["tenderEvaluationInfo"]["detailsInfo"] = []
                            for i, j in enumerate(ticket_id):
                                table_data[index]["tenderEvaluationInfo"]["flowInfo"].append(
                                    {
                                        "mainTicketId": j,
                                        "sectionName": table_data[index]["evaluationStandardInfo"]["detailsInfo"][i][
                                            "sectionName"]
                                    }
                                )
                                ticket_id_send = j
                                ticket_result = self.check_ticket_status(ticket_id_send)
                                instance_id = ticket_result[0].get("InstanceId")
                                if ticket_result[0].get("TicketStatus") == "OPEN":
                                    table_data[index]["tenderEvaluationInfo"]["isFinish"] = False
                                    table_data[index]["projectStepsInfo"] = {"active": 5}
                                    # 处理子tickets
                                    if children_ticket_id:
                                        table_data[index]["tenderEvaluationInfo"]["flowInfo"][i]["childFlowInfo"] = []
                                        # 处理子单状态
                                        children_ticket_result = self.check_children_tickets(children_ticket_id)
                                        main_child_map_list = flow.get_variables(instance_id,
                                                                                 ["ticket_id_list",
                                                                                  "ticket_id_list_2"])
                                        if main_child_map_list.get("ticket_id_list_2"):
                                            child_map_list = main_child_map_list.get(
                                                "ticket_id_list") + main_child_map_list.get(
                                                "ticket_id_list_2")
                                        elif main_child_map_list.get("ticket_id_list"):
                                            child_map_list = main_child_map_list.get("ticket_id_list")
                                        else:
                                            child_map_list = []
                                        for row in children_ticket_result:
                                            if row.get("TicketStatus", "") == "OPEN" and int(row.get(
                                                    "TicketId", "")) in child_map_list:
                                                table_data[index]["tenderEvaluationInfo"]["flowInfo"][i][
                                                    "childFlowInfo"].append({
                                                    "childTicketId": row.get("TicketId"),
                                                    "InstanceId": row.get("InstanceId"),
                                                    "childFlowName": row.get("SchemeNameCn"),
                                                    "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                        -1].strip() if row[
                                                        'CurrentAllProcessUsers'] else "",
                                                    "childTicketStatus": "进行中"
                                                })
                                            elif row.get("TicketStatus", "") == "END" and int(row.get(
                                                    "TicketId", "")) in child_map_list:
                                                table_data[index]["tenderEvaluationInfo"]["flowInfo"][i][
                                                    "childFlowInfo"].append({
                                                    "childTicketId": row.get("TicketId"),
                                                    "InstanceId": row.get("InstanceId"),
                                                    "childFlowName": row.get("SchemeNameCn"),
                                                    "responsiblePerson": row.get("CurrentAllProcessUsers").split(',')[
                                                        -1].strip() if row[
                                                        'CurrentAllProcessUsers'] else "",
                                                    "childTicketStatus": "已完成"
                                                })
                                    else:
                                        table_data[index]["tenderEvaluationInfo"]["flowInfo"][i].update(
                                            {"childFlowInfo": []})
                                        table_data[index]["tenderEvaluationInfo"]["flowInfo"][i][
                                            "childFlowInfo"].append({
                                            "childTicketId": "",
                                            "InstanceId": "",
                                            "childFlowName": "",
                                            "responsiblePerson": "",
                                            "childTicketStatus": ""
                                        })
                                elif ticket_result[0].get("TicketStatus") == "END":
                                    table_data[index]["projectStepsInfo"] = {"active": 6}
                                    table_data[index]["tenderEvaluationInfo"]["isFinish"] = True
                                    table_data[index]["tenderEvaluationInfo"]["havePermission"] = True
                                    table_data[index]["tenderEvaluationInfo"]["detailsInfo"].append(
                                        {
                                            "sectionName":
                                                table_data[index]["evaluationStandardInfo"]["detailsInfo"][i][
                                                    "sectionName"],
                                            "tableData": []
                                        }
                                    )
                                    table_info = []
                                    technical_score_summary = flow.get_variables(instance_id,
                                                                                 ["technical_score_summary"])[
                                        "technical_score_summary"]
                                    for row in technical_score_summary:
                                        table_info.append({
                                            "manufacturers": row.get("manufacturers"),
                                            "technicalScore": row.get("skill_score")
                                        })
                                    table_data[index]["tenderEvaluationInfo"]["detailsInfo"][i][
                                        "tableData"] = table_info
        # 检查账户是否在竞标人员列表中
        if account in bid_person_list:
            # 如果账户在竞标人员列表中，直接返回所有数据
            return {"total": total, "tableData": table_data,
                    "ticket_id_query": ticket_id_query,
                    "ticket_id_query_map": ticket_id_query_map}

        # 检查账户是否在技术人员列表中
        if account in technical_person_list:
            # 如果账户在技术人员列表中但不在竞标人员列表中，移除特定键
            for i in table_data:
                i.pop("evaluationTeamInfo", None)
                i.pop("evaluationStandardInfo", None)
                i.pop("bidReturnDocumentInfo", None)
                i.pop("tenderEvaluationInfo", None)
            return {"total": total, "tableData": table_data,
                    "ticket_id_query": ticket_id_query,
                    "ticket_id_query_map": ticket_id_query_map}

        # 如果账户不在任何一个列表中
        table_data = []
        return {"total": total, "tableData": table_data,
                "ticket_id_query": ticket_id_query,
                "ticket_id_query_map": ticket_id_query_map}

    def get_information_about_procurement_project_bak(self, projectName=None, page_size=None, page_number=None,
                                                      year=None,
                                                      type=None,
                                                      account=None):
        """
        :param project_name: 项目名称
        :param page_size: 页码
        :param page_number: 页数
        :param ticket_id: 工单ID
        :param ticket_name: 工单名称
        :param stage_name: 流程标识名称
        :param responsible_person: 负责人
        :return:
        """
        # 查询表中阶段单号
        if not page_size:
            page_size = 10
        if not page_number:
            page_number = 1
        # 计算开始和结束索引
        start_index = (page_number - 1) * page_size
        if projectName:
            sql = ("SELECT * FROM procurement_process_information WHERE project_name LIKE '%" + projectName + "%'"
                                                                                                              "LIMIT %s OFFSET %s") % (
                      page_size, start_index)
        else:
            sql = ("SELECT * FROM procurement_process_information LIMIT %s OFFSET %s" % (page_size, start_index))
        db = mysql.new_mysql_instance("tbconstruct")
        # 查询总记录数
        count_sql = "SELECT COUNT(*) AS total FROM procurement_process_information"
        count_result = db.get_row(count_sql)

        # 获取总记录数
        total = count_result['total'] if count_result and 'total' in count_result else 0
        query = db.get_all(sql)
        # 总人员列表
        person_list = config.get_config_map("test_question_permissions")
        technical_person_list = copy.deepcopy(person_list)
        bid_person_list = copy.deepcopy(person_list)
        if query:
            # 构造最终数据结构
            final_data = {
                "total": total,
                "tableData": []
            }
            # 将字符串形式的JSON解析为字典，并收集所有的TicketId
            all_ticket_ids = set()

            # 收集所有需要查询的技术小组组建的主工单号
            technical_team_ticket_ids = set()
            # 收集所有需要查询的技术文件制定的主工单号
            technical_documentation_ids = set()
            # 收集所有需要查询的评标小组组建的主工单号
            bid_evaluation_team_ticket_ids = set()

            for query_items in query:
                for phase, ticket_info in query_items.items():
                    if (phase == 'project_name'
                            or phase == 'project_desc'
                            or phase == 'source_of_project'
                            or phase == 'id'):
                        continue  # 跳过非工单信息的字段
                    if not (ticket_info is None or ticket_info == 'null'):
                        ticket_info = json.loads(ticket_info)
                        # 确保 ticket_id 和 children_ticket_id 是列表，并将其元素转换为字符串
                        ticket_ids = ticket_info.get('ticket_id', [])
                        children_ticket_ids = ticket_info.get('children_ticket_id', [])

                        if phase == "technical_team_formation":
                            technical_team_ticket_ids.update(ticket_ids)
                        if phase == "technical_documentation_preparation":
                            technical_documentation_ids.update(ticket_ids)

                        # 将所有工单ID转换为字符串，无论它们原本是整数还是字符串
                        ticket_ids = [str(tid) for tid in ticket_ids]
                        children_ticket_ids = [str(cid) for cid in children_ticket_ids]

                        # 收集所有的工单ID
                        all_ticket_ids.update(ticket_ids)
                        all_ticket_ids.update(children_ticket_ids)
            final_data.update({"technical_team_ticket_ids": technical_team_ticket_ids,
                               "technical_documentation_ids": technical_documentation_ids})

            # 构造查询数据
            query_data = {
                "SchemaId": "ticket_base",
                "Data": {
                    "ResultColumns": {
                        "TicketId": "",
                        "InstanceId": "",
                        "TicketStatus": "",
                        "CurrentAllProcessUsers": "",
                        "CurrentTasks": "",
                        "Title": ""
                    },
                    "SearchCondition": {
                        "TicketId": list(all_ticket_ids)  # 将集合转换为列表
                    }
                }
            }

            # 查询工单状态 (假设gnetops.request是已经定义好的函数)
            query_result = gnetops.request(
                action="QueryData",
                method="Run",
                ext_data=query_data
            )

            ticket_result = query_result.get('List')

            # 创建一个映射工单ID到其状态信息的字典
            ticket_status_map = {item['TicketId']: item for item in ticket_result}

            # 阶段映射
            phase_mapping = {
                'technical_team_formation': 'technicalTeamInfo',
                'technical_documentation_preparation': 'technicalFileInfo',
                'bid_evaluation_team_formation': 'evaluationTeamInfo',
                'tender_evaluation': 'tenderEvaluationInfo'
            }
            final_data.update({"query": query, "ticket_result": ticket_result})
            for query_items in query:
                if 'technical_team_formation' in query_items:
                    ticket_info_str = query_items.get('technical_team_formation')
                    if not (ticket_info_str is None or ticket_info_str == 'null', ""):
                        ticket_info = json.loads(ticket_info_str)
                        technical_team_ticket_ids.update(ticket_info.get('ticket_id', []))
            # if 'technical_documentation_preparation' in query_items:
            #     ticket_info_str = query_items.get('technical_documentation_preparation', "")
            #     if not (ticket_info_str is None or ticket_info_str == 'null'):
            #         ticket_info = json.loads(ticket_info_str)
            # technical_documentation_ids.update(ticket_info.get('ticket_id', []))
            # 评标小组信息和技术小组信息在同一张表中，查库时拼接一起返回
            # if 'bid_evaluation_team_formation' in query_items:
            #     ticket_info_str = query_items.get('bid_evaluation_team_formation', "")
            #     if not (ticket_info_str is None or ticket_info_str == 'null'):
            #         ticket_info = json.loads(ticket_info_str)
            #         technical_team_ticket_ids.update(ticket_info.get('ticket_id', []))
            #         bid_evaluation_team_ticket_ids.update(ticket_info.get('ticket_id', []))

            # 一次性查询所有小组的信息数据
            if technical_team_ticket_ids:
                formatted_ticket_ids = ','.join([f"'{ticket_id}'" for ticket_id in technical_team_ticket_ids])
                final_data.update({"technical_team_ticket_ids": technical_team_ticket_ids})
                sql_1 = (
                    f"SELECT ticket_id, leader, team_leader, team_members, team_num, team FROM technical_team_formation "
                    f"WHERE ticket_id IN ({formatted_ticket_ids})"
                )
                technical_team_query_data = db.get_all(sql_1)
                technical_team_data_map = {}
                if technical_team_query_data:
                    for item in technical_team_query_data:
                        ticket_id = item.get('ticket_id')
                        if ticket_id not in technical_team_data_map:
                            technical_team_data_map[ticket_id] = []
                        technical_team_data_map[ticket_id].append({
                            'leader': item.get('leader'),
                            'team': item.get('team'),
                            'teamLeader': item.get('team_leader'),
                            'teamNum': item.get('team_num'),
                            'teamMembers': item.get('team_members'),
                        })
                    final_data.update({"technical_team_data_map": technical_team_data_map})
                else:
                    final_data.update({"technical_team_query_data": technical_team_query_data})
                    technical_team_data_map = {}
            else:
                technical_team_data_map = {}

            # if technical_documentation_ids:
            #     formatted_ticket_ids = ','.join([f"'{ticket_id}'" for ticket_id in technical_documentation_ids])
            #     final_data.update({"technical_documentation_ids": technical_documentation_ids})
            #     sql_2 = (
            #         f"SELECT file,file_name,uploader,adjustment_part,ticket_id,supplier_list "
            #         f"FROM information_joint_builder "
            #         f"WHERE ticket_id IN ({','.join(map(str, technical_documentation_ids))})"
            #     )
            #     technical_documentation_query_data = db.get_all(sql_2)
            #     technical_documentation_map = {}
            #     if technical_documentation_query_data:
            #         for item in technical_documentation_query_data:
            #             ticket_id = item.get('ticket_id')
            #             if ticket_id not in technical_documentation_map:
            #                 technical_documentation_map[ticket_id] = []
            #             technical_documentation_map[ticket_id].append({
            #                 'supplier_list': item.get('supplier_list',""),
            #                 'fileUrl': item.get('file',""),
            #                 'fileName': item.get('file_name',""),
            #                 'fileUploader': item.get('uploader',""),
            #                 'adjustmentPart': item.get('adjustment_part',"")
            #             })
            #         final_data.update({"technical_documentation_query_data": technical_documentation_query_data})
            #         final_data.update({"technical_documentation_map": technical_documentation_map})
            #     else:
            #         technical_documentation_map = {}
            # else:
            #     technical_documentation_map = {}

            # if bid_evaluation_team_ticket_ids:
            #     # 从数据库获取第四阶段标准文件信息
            #     sql_3 = (
            #         f"SELECT clarification_document,back_label_file,standard,tender_evaluation_order "
            #         f"FROM information_joint_builder_faq "
            #         f"WHERE tender_evaluation_order IN "
            #         f"({','.join(map(str, bid_evaluation_team_ticket_ids))})")
            #     bid_query_data = db.get_row(sql_3)
            #     if bid_query_data:
            #         for item in bid_query_data:
            #             ticket_id = item.get('tender_evaluation_order')
            #             # 处理 standard 字段
            #             standard_query = query.get('standard', '')
            #             back_label_file = item.get('back_label_file')
            #             clarification_document = item.get('clarification_document')
            #             if standard_query:
            #                 # 将 standard_query 转换为列表
            #                 try:
            #                     standard_query_list = json.loads(standard_query)
            #                 except json.JSONDecodeError:
            #                     standard_query_list = [standard_query]
            #                 # 对每个字符串进行修复并解析
            #                 # standard = [ast.literal_eval(string) for string in standard_query_list]
            #             else:
            #                 standard = []
            #             # 处理back_label_file回标文件字段
            #             # 处理clarification_document澄清答疑文件字段
            #             if back_label_file:
            #                 # 将 back_label_file 转换为列表
            #                 try:
            #                     back_label_file_list = json.loads(back_label_file)
            #                 except json.JSONDecodeError:
            #                     back_label_file_list = [back_label_file]
            #                 # back_label_file_list = [file.strip() for file in back_label_file_list]
            #             else:
            #                 clarification_document_list = []
            #             if clarification_document:
            #                 clarification_document_list = clarification_document.split(',')
            #             else:
            #                 clarification_document_list = []

            for query_items in query:
                project_entry = {
                    "type": query_items.get('source_of_project'),
                    "projectName": query_items['project_name'],
                    "currentProgress": '当前进展',
                    "projectStepsInfo": {
                        "active": 0,
                    },
                    "technicalTeamInfo": {"flowInfo": []},
                    "technicalFileInfo": {"flowInfo": []},
                    "evaluationTeamInfo": {"flowInfo": []},
                    "evaluationStandardInfo": {"flowInfo": []},
                    "bidReturnDocumentInfo": {"flowInfo": []},
                    "tenderEvaluationInfo": {"flowInfo": []}
                }

                # Helper function to process flow information
                def process_flow_info(phase, ticket_info_str):
                    try:
                        ticket_info = json.loads(ticket_info_str)
                    except json.JSONDecodeError:
                        print(f"Failed to decode JSON for phase: {phase}")
                        return
                    for main_ticket_id in ticket_info.get('ticket_id', []):
                        main_ticket_status = ticket_status_map.get(str(main_ticket_id), {})
                        main_ticket_users = main_ticket_status.get('CurrentAllProcessUsers', '')
                        main_ticket_status_info = main_ticket_status.get('TicketStatus', '')
                        # main_ticket_current_task = main_ticket_status.get('CurrentTasks', [])
                        main_ticket_instance_id = main_ticket_status.get('InstanceId', '')

                        # 初始化阶段主流程ID
                        flow_entry = {
                            "mainTicketId": str(main_ticket_id),
                            "childFlowInfo": []
                        }

                        if main_ticket_status_info == 'OPEN':
                            project_entry["isFinish"] = False

                            if ticket_info.get('children_ticket_id', []):
                                for child_ticket_id in ticket_info.get('children_ticket_id', []):
                                    child_ticket_status = ticket_status_map.get(str(child_ticket_id), {})
                                    child_ticket_users = child_ticket_status.get('CurrentAllProcessUsers', '')
                                    child_ticket_status_info = child_ticket_status.get('TicketStatus', '')
                                    child_ticket_name = child_ticket_status.get('Title', '')

                                    flow_entry["childFlowInfo"].append({
                                        "childFlowName": child_ticket_name,
                                        "childTicketId": str(child_ticket_id),
                                        "childTicketStatus": "运行中" if child_ticket_status_info == "OPEN" else "已结束",
                                        "responsiblePerson": child_ticket_users
                                    })
                            # 评标小组及文件制定阶段信息单独处理
                            if phase == 'evaluationTeamInfo':
                                # 初始化评标小组组建三个阶段是否完成的标志
                                # 小组组建
                                team_is_finish = False
                                # 标准文件
                                standard_is_finish = False
                                # 回标文件
                                bid_is_finish = False
                                # 查询主流程具体进展
                                data_instanceId = {
                                    "InstanceId": main_ticket_instance_id,
                                }
                                res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog",
                                                      data=data_instanceId)
                                # 循环判断运行到哪一个节点
                                for row in res:
                                    if row.get("TaskName") == "通知小组成员任务":
                                        team_is_finish = True
                                    elif row.get("TaskName") == "开标、上传回标文件":
                                        standard_is_finish = True
                                    elif row.get("TaskName") == "招采对接起单":
                                        bid_is_finish = True
                                if team_is_finish:
                                    project_entry["evaluationTeamInfo"]["isFinish"] = True
                                    # 从technical_team_data_map中获取对应小组的数据
                                    if str(main_ticket_id) in technical_team_data_map:
                                        team_data = technical_team_data_map[str(main_ticket_id)]
                                        project_entry["evaluationTeamInfo"]["leader"] = team_data[0].get("leader",
                                                                                                         "")
                                        project_entry["evaluationTeamInfo"]["detailsInfo"] = team_data
                                if standard_is_finish:
                                    project_entry["evaluationStandardInfo"]["isFinish"] = True
                                if bid_is_finish:
                                    project_entry["bidReturnDocumentInfo"]["isFinish"] = True

                        else:
                            project_entry[phase]["isFinish"] = True
                            project_entry[phase]["detailsInfo"] = []
                            if phase == "technicalTeamInfo":
                                project_entry[phase]["leader"] = ""

                                # 从technical_team_data_map中获取对应的数据
                                if str(main_ticket_id) in technical_team_data_map:
                                    team_data = technical_team_data_map[str(main_ticket_id)]
                                    project_entry[phase]["leader"] = team_data[0].get("leader", "")
                                    project_entry[phase]["detailsInfo"] = team_data

                            # elif phase == "technicalFileInfo":
                            #     # 从technical_documentation_map中获取对应的数据
                            #     if str(main_ticket_id) in technical_documentation_map:
                            #         file_data = technical_documentation_map.get(str(main_ticket_id),[])
                            #         project_entry[phase]["detailsInfo"] = file_data
                        #     elif phase == "tender_evaluation":
                        project_entry[phase]["flowInfo"].append(flow_entry)

                # Process each phase's flow information
                for phase, ticket_info_str in query_items.items():
                    if phase not in phase_mapping:
                        continue
                    if not (ticket_info_str is None or ticket_info_str == 'null'):
                        process_flow_info(phase_mapping[phase], ticket_info_str)

                final_data["tableData"].append(project_entry)
        return final_data

    def send_email(self, member_list):
        email_data = ("<p>【项目名称】：南京江宁8号楼液冷项目T-block集成商招采</p><br>"
                      "<p>【项目概况】：南京江宁8号楼液冷项目建设一个液冷T-block项目，含4个液冷I房间，对应T-block集成商进行招采</p><br>"
                      "<p>【内容】：你已成为南京江宁8号楼液冷项目T-block集成商招采技术小组成员，组长：yanjunchen，请多关注此类信息！</p><br>")
        title = "南京江宁8号楼液冷项目T-block集成商招采"
        # member_list = ["<EMAIL>"]
        tof.send_email(sendTitle=title, msgContent=email_data, sendTo=member_list)
