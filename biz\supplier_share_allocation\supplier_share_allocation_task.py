import datetime

from iBroker.lib import mysql
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate


# 项目供应商份额分配输入
class SupplierShareAllocationInput(AjaxTodoBase):
    def __init__(self):
        super(SupplierShareAllocationInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        data_list = process_data.get("data_list")
        remark = process_data.get("remark")
        today = datetime.datetime.today()
        today = today.strftime("%Y-%m-%d %H:%M:%S")
        handle_user_info = {"user": process_user, "date": today}
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "data_list": data_list,
            "remark": remark,
            "handle_user_info": handle_user_info,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


class ObtainSupplierInformation(object):
    def __init__(self):
        super(ObtainSupplierInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def obtain_supplier_information(self, campus, project, data_list, f_instance_id):
        """ 获取供应商信息 """
        project_name = campus + project
        db = mysql.new_mysql_instance("tbconstruct")
        # 参数化 SQL
        supplier_sql = """
            SELECT 
                p.po_code,
                COALESCE(e.device_name, p.material_name) AS material_name,
                COALESCE(s.js_supplier_name, p.supplier) AS supplier
            FROM 
                payment_info p
            LEFT JOIN 
                equipment_category_mapping_relationship e 
                ON p.material_name = e.material_category_name
            LEFT JOIN 
                supplier_name_mapping s 
                ON p.supplier = s.sw_supplier_name
            WHERE 
                p.project_name = %s;
        """
        field_mapping = {
            '集成商': 'integrators',
            '项管': 'project_manager',
            '监理': 'supervision',
            '测试': 'examination',
            'AHU': 'AHU',
            '柴发': 'chaifa',
            '低压柜': 'low_voltage_cabinet',
            '中压柜': 'medium_voltage_cabinet',
            '电池': 'battery',
            '机柜': 'cabinet',
            '变压器': 'transformer',
            '高压直流 &列头柜': 'headboard',
            'PDU': 'PDU'
        }

        supplier_list = db.get_all(supplier_sql, (project_name,))

        # 处理数据更新逻辑
        if supplier_list:
            # 如果data_list为空，创建一个新字典
            if not data_list:
                data_list = [{}]

            # 假设我们只更新第一个字典（根据实际需求调整）
            target_data = data_list[0] if data_list else {}

            # 更新供应商信息
            for row in supplier_list:
                material_name = row.get('material_name')
                supplier = row.get('supplier')
                if not supplier:
                    continue
                if material_name in field_mapping:
                    field_name = field_mapping[material_name]
                    # 更新字段，无论是否已存在（根据实际需求调整）
                    target_data[field_name] = supplier

            # 如果data_list为空，添加更新后的字典
            if not data_list:
                data_list.append(target_data)

            # 更新工作流变量
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    "data_list": data_list
                }
            ).call()
            return {
                "code": 0,
                "msg": "刷新成功，请点击重置按钮刷新查看",
                "data": data_list
            }
        else:
            return {
                "code": 1,
                "msg": "数据无更新，请填写缺失数据",
                "data": data_list
            }



