import copy
import json
from datetime import datetime

import lunardate
import requests
import re
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate, WorkflowContinue
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql
from openpyxl import load_workbook


class PrepareMaterialsUpload(AjaxTodoBase):
    """
        测试阶段：
            测试主流程：准备阶段资料上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 工单号
        ticket_id = self.ctx.variables.get("ticket_id")
        # 项目名称
        project_name = process_data.get('project_name')
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship " \
              " WHERE project_name='%s'" % project_name
        module_list = db.get_all(sql)
        if module_list:
            module_name = module_list[0].get('module_name')
        else:
            module_name = ''
            self.workflow_detail.add_kv_table('获取模组名称', {"success": False, "data": "未获取到模组信息"})

        module_name = self.ctx.variables.get("module_name")

        # 项目阶段
        project_phase = process_data.get('project_phase')
        # 计划整体前置条件
        overall_test_conditions = process_data.get('overall_test_conditions')
        # 计划表
        test_plan_list = process_data.get('test_plan_list')
        insert_list = []
        if test_plan_list:
            for i in test_plan_list:
                i['project_name'] = project_name
                i['ticket_id'] = ticket_id
                insert_list.append(i)
        # 存库
        insert_data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "project_phase": project_phase,
            "overall_test_conditions": overall_test_conditions,
            "module_name": module_name
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.insert("test_main_process", insert_data)
        tb_db.insert_batch('test_main_process_schedule', insert_list)
        tb_db.commit()
        # 存流程变量
        variables = {
            "test_plan_list": test_plan_list,
            "project_name": project_name,
            "project_phase": project_phase,
            "module_name": module_name
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CreatingTestFlow(object):
    """
        测试阶段：
            测试主流程：创建测试主流程
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def creat_test_task(self, module_name):
        info = gnetops.request(action="CreateVerification",
                               method="CreateOrder",
                               data={},
                               ext_data={
                                   "MozuName": module_name
                               }

                               )
        self.workflow_detail.add_kv_table('创建测试方工单', {"data": info})
        variables = {
            "test_info": info
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_count(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT role,account,name,phone FROM project_role_account WHERE project_name = '{project_name}' " \
               " AND (del_flag = 0 OR del_flag IS NULL)"
        result = db.get_all(sql1)
        variables = {
            'role_info': result
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class TestDataReturn(object):
    """
        测试阶段：
            测试主流程：测试阶段主流程结束，更改状态
    """

    def test_data_callback(self, project_name, test_report, conclusion, conclusion_desc):
        """
        project_name:项目名称——str
        test_report:初版测试报告——[{},{}]
        conclusion:结论——str
        conclusion_desc：结论描述——str
        """
        url_list = []
        if test_report:
            for i in test_report:
                try:
                    url = i["response"]["FileList"][0]["url"]
                except KeyError:
                    return {"code": -1, "msg": "数据传入错误"}
                except IndexError:
                    return {"code": -1, "msg": "数据传入错误"}
                url_list.append(url)
        else:
            test_report = ''
        test_report_url = ";".join(url_list)
        update_data = {
            'test_report': str(test_report),
            'test_report_url': test_report_url,
            'conclusion': conclusion,
            'conclusion_desc': conclusion_desc
        }
        conditions = {
            'module_name': project_name
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("test_main_process", update_data, conditions)
        tb_db.commit()

        return {"success": True, "data": "成功"}

    def test_detail_data_callback(self, project_name, test_detail_report, test_conclusion):
        """
        project_name:项目名称——str
        test_detail_report:最终测试报告——[{}，{}]
        test_conclusion:总结——[]
        """

        url_list = []
        if test_detail_report:
            for i in test_detail_report:
                try:
                    url = i["response"]["FileList"][0]["url"]
                except KeyError:
                    return {"code": -1, "msg": "数据传入错误"}
                except IndexError:
                    return {"code": -1, "msg": "数据传入错误"}
                url_list.append(url)
        else:
            test_detail_report = ''
        test_detail_report_url = ";".join(url_list)
        test_conclusion_url = ";".join(test_conclusion)
        update_data = {
            'test_detail_report': str(test_detail_report),
            'test_detail_report_url': test_detail_report_url,
            'test_conclusion': test_conclusion_url
        }
        conditions = {
            'module_name': project_name
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("test_main_process", update_data, conditions)
        tb_db.commit()

        return {"success": True, "data": "成功"}


class VerifyTestResults(object):
    """
        测试阶段：
            测试主流程：验证测试结果
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def verify_test_results(self):
        project_name = self.ctx.variables.get('project_name')
        ticket_id = self.ctx.variables.get('ticket_id')
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT test_report,test_conclusion,test_detail_report FROM test_main_process " \
              f"WHERE ticket_id = '{ticket_id}' "
        test_result_list = db.get_all(sql)
        if test_result_list:
            for i in test_result_list:
                if i.get('test_report') and i.get('test_conclusion') and i.get('test_detail_report'):
                    update_data = {
                        'status': 1
                    }
                    conditions = {
                        'ticket_id': ticket_id
                    }
                    tb_db = mysql.new_mysql_instance("tbconstruct")
                    tb_db.begin()
                    tb_db.update("test_main_process", update_data, conditions)
                    tb_db.commit()
                    flow.complete_task(self.ctx.task_id)
                    resp = gnetops.request(
                        action="Nbroker",
                        method="UrlProxy",
                        ext_data={
                            "NbrokerData": {
                                "context": {
                                    "service_name": "GetData",
                                    "method_name": "get_data"
                                },
                                "args": {
                                    "project_name": project_name,  # 项目名称，必填
                                },
                            },
                            "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task"
                        }
                    )
                    if resp.get("code") == 200:
                        result = resp.get("result", {})
                        finish_time = result.get("progress_data", {}).get("finish_time")
                        db = mysql.new_mysql_instance("tbconstruct")
                        sql = (f"SELECT campus,project,dcopsTicketId FROM risk_early_warning_data "
                               f"WHERE project_name = '{project_name}'")
                        query_data = db.get_row(sql)
                        if query_data and finish_time:
                            campus = query_data.get("campus")
                            ticket_id = query_data.get("dcopsTicketId")
                            project = query_data.get("project")
                            response_data = gnetops.request(
                                action="Project",
                                method="UpdateSummaryData",
                                ext_data={
                                    "campus": campus,
                                    "project_name": project,
                                    "ticket_id": ticket_id,
                                    "update_filed": "TestEndTime",
                                    "update_value": finish_time
                                },
                                scheme="ifob-infrastructure",
                            )
                    return {"success": True, "data": "流程已结束"}


class GetListFilesFromTestbench(object):
    """
    获取测试平台文件列表
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()


    def is_hire(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                       f"WHERE project_name='{project_name}'")
        is_hire = "是" if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else "否"
        variables = {
            "is_hire": is_hire
        }
        if is_hire == "是":
            sql = ("SELECT module_name FROM project_module_mapping_relationship "
                   f"WHERE project_name = '{project_name}'")
            module_name = db.get_row(sql).get('module_name')
            variables.update({
                "module_name": module_name
            })
        else:
            sql = ("SELECT module_name FROM project_module_mapping_relationship "
                   f"WHERE project_name = '{project_name}'")
            module_name_query = db.get_all(sql)
            module_name_list = []
            if not module_name_query:
                raise Exception(f"没有找到{project_name}项目对应模组")
            for item in module_name_query:
                module_name_list.append({
                    "label": item.get("module_name"),
                    "value": item.get("module_name"),
                })
            variables.update({
                "module_name_list": module_name_list
            })
        flow.complete_task(self.ctx.task_id,variables=variables)


    def get_files_list(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        module_name = self.ctx.variables.get("module_name")
        # sql = ("SELECT module_name FROM project_module_mapping_relationship "
        #        f"WHERE project_name = '{project_name}'")
        # module_name = db.get_row(sql).get('module_name')
        f_instance_id = self.ctx.instance_id
        file_list = []
        rep = gnetops.request(action="Nbroker",
                              method="UrlProxy",
                              ext_data={
                                  "NbrokerData": {
                                      "context": {
                                          "service_name": "TestFileListSearch",
                                          "method_name": "test_file_list_search"
                                      },
                                      "args": {
                                          "mozu_name": f'{module_name}'
                                      },
                                  },
                                  "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                              }
                              )
        data_back = rep.get("result")
        if data_back and data_back.get("code") == 200:
            file_list = []
            file_list_initial = data_back.get("data")
            for item in file_list_initial:
                file_list.append({
                    "file_name": item.get("file_name"),
                    "id": str(item.get("id")),
                    "file_type": item.get("file_type"),
                    "file_source": item.get("file_source"),
                    "remark": "无"
                })
        else:
            self.workflow_detail.add_kv_table("接口查询出错", {"错误信息": f"{rep}"})
        sql2 = ("SELECT file_name,url,file_remark FROM maintenance_upload_data "
                f"WHERE project_name = '{project_name}' AND node = '架构'")
        query = db.get_all(sql2)
        jg_file_id_list = []
        if query:
            # 创建一个映射，将 file_name 映射到对应的 file 对象
            file_map = {file.get("file_name"): file for file in file_list}

            for item in query:
                file_name = item.get("file_name")
                if file_name in file_map:
                    file = file_map[file_name]
                    jg_file_id_list.append(file.get("id"))

                    # 获取 remark 并处理 "无" 的情况
                    remark = item.get("file_remark", "")
                    if remark != "无":
                        # 更新 file 的信息
                        if item.get("url"):
                            file.update({
                                "file_url": json.loads(item.get("url")),
                                "remark": remark
                            })
                        else:
                            file.update({
                                "file_url": [],
                                "remark": remark
                            })
                    else:
                        # 更新 file 的信息
                        if item.get("url"):
                            file.update({
                                "file_url": json.loads(item.get("url")),
                                "remark": "无"
                            })
                        else:
                            file.update({
                                "file_url": [],
                                "remark": "无"
                            })

        variables = {"file_list": file_list,
                     "module_name": module_name,
                     "f_instance_id": f_instance_id,
                     "is_first_update": "1",
                     "jg_file_id_list": jg_file_id_list
                     }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def update_file_list(self, file_list=None, module_name=None,
                         f_instance_id=None, project_name=None,
                         jg_file_id_list=None, is_first_update=None):
        main_variables = flow.get_variables(f_instance_id, ["file_list", "review_ticket_list"])
        file_list_1 = main_variables.get("file_list")

        if not file_list or not file_list_1:
            return {"code": -1, "msg": "参数缺失"}

        file_dict = {item.get("id"): item for item in file_list}
        file_dict_1 = {item.get("id"): item for item in file_list_1}

        if not jg_file_id_list:
            jg_file_id_list = ["0"]

        def get_urls(item):
            urls = []
            if item.get("file_url"):
                for row in item["file_url"]:
                    url_info = row.get("response", {}).get("FileList", [{}])[0]
                    if url_info.get("url"):
                        urls.append(url_info["url"])
            return ";".join(urls)

        is_update = 0
        file_review_list = []

        for file_id, item in file_dict.items():
            old_item = file_dict_1.get(file_id)
            if not old_item:
                continue  # 或者视为新增项，视业务需求决定

            current_url = get_urls(item)
            old_url = get_urls(old_item)

            current_remark = item.get("remark", "")
            old_remark = old_item.get("remark", "")

            url_changed = current_url != old_url
            remark_changed = current_remark != old_remark

            # 首次上传
            if is_first_update == "1" and file_id in jg_file_id_list:
                is_update += 1
                file_review_list.append({
                    "file_name": item.get("file_name"),
                    "remark": current_remark,
                    "id": file_id,
                    "file_type": item.get("file_type"),
                    "file_source": item.get("file_source"),
                    "file_url": current_url,
                })
            # 非首次，仅当有变化时更新
            elif url_changed or remark_changed:
                is_update += 1
                file_review_list.append({
                    "file_name": item.get("file_name"),
                    "remark": current_remark,
                    "id": file_id,
                    "file_type": item.get("file_type"),
                    "file_source": item.get("file_source"),
                    "file_url": current_url,
                })

        if is_update == 0:
            return {"code": -1, "msg": "文件列表无更新"}

        # 更新标志位
        if is_first_update == "1":
            is_first_update = "0"

        if file_review_list:
            try:
                rep = gnetops.request(action="Nbroker",
                                      method="UrlProxy",
                                      ext_data={
                                          "NbrokerData": {
                                              "context": {
                                                  "service_name": "TestFileUpdate",
                                                  "method_name": "test_file_Update"
                                              },
                                              "args": {
                                                  "file_list": file_review_list
                                              },
                                          },
                                          "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                                      }
                                      )
                data_back = rep.get("result")
                if data_back and data_back.get("code") == 200:
                    WorkflowVarUpdate(
                        instance_id=f_instance_id,
                        variables={
                            "file_list": file_list,
                            "file_list_send": file_review_list,
                            "is_first_update": is_first_update,
                        }
                    ).call()
                    return {"code": 0, "data": file_list, "msg": "更新成功"}
                else:
                    return {"code": -1, "data": rep, "msg": "回传测试平台失败，请稍后重试或联系开发人员"}
            except Exception as e:
                return {"code": -1, "msg": str(e)}
        else:
            return {"code": -1, "msg": "无更新"}

    def wait_(self):
        review_ticket_list = self.ctx.variables.get("review_ticket_list")
        # 查询审核单状态
        data = {
            "ResultColumns": {
                "CreateTime": "",
                "EndTime": "",
                "InstanceId": "",
                # 是否强制结单
                "IsForceEnd": "",
                # 流程定义标识
                "ProcessDefinitionKey": "",
                "ServiceRecoverTime": "",
                "StartProcessTime": "",
                # 单据状态[OPEN:运行中;END:已结束]
                "TicketStatus": "",
                "Title": "",
                "CustomRequestVarKV": "",
            },
            "SearchCondition": {'TicketId': review_ticket_list}
        }
        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']
        cnt = 0  # 用于记录有多少个工单正在运行中
        for item in res_list:
            if item.get('TicketStatus') == 'OPEN':
                cnt += 1
        if cnt:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}

    def upload_file_save(self, file_list, ticket_id, project_name, module_name):
        db = mysql.new_mysql_instance("tbconstruct")
        file_list_save = []
        for item in file_list:
            file_list_save.append({
                "file_id": item.get("id"),
                "file_name": item.get("file_name"),
                "file_url": json.dumps(item.get("file_url"), ensure_ascii=False),
                "remark": item.get("remark"),
                "file_source": item.get("file_source"),
                "file_type": item.get("file_type"),
                "ticket_id": ticket_id,
                "project_name": project_name,
                "mozu_name": module_name
            })
        db.begin()
        db.insert_batch("test_verification_technical_data", file_list_save)
        db.commit()
        flow.complete_task(self.ctx.task_id)

    def get_people_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql_all = (
                "SELECT account,name,role,service_provider FROM project_role_account"
                " WHERE project_name='%s' and del_flag = '0'" % project_name
        )
        all_person_list = db.get_all(sql_all)
        jcs = ""
        xg = ""
        for row in all_person_list:
            role = row.get("role", "")
            account = row.get("account", "")
            name = row.get("name", "")
            if role =="集成商-项目经理":
                jcs = account
            elif role == "项管-项目经理":
                xg = account
            elif role == "总包-项目经理":
                jcs = account
            elif role == "合建方-项目经理":
                xg = account
        flow.complete_task(self.ctx.task_id,variables={"jcs": jcs, "xg": xg})


class VerifyTestSelectModuleName(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        module_name = process_data.get("module_name")
        if not module_name:
            return {"code": -1, "msg": "请选择模组名称"}
        GnetopsTodoEnd(task_id=self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables={"module_name": module_name})



class DataBackToTestbench(AjaxTodoBase):
    """
    数据回填到测试平台
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        module_name = self.ctx.variables.get("module_name")
        review_ticket_list = self.ctx.variables.get("review_ticket_list")
        file_list = process_data.get("file_list")
        file_list_1 = self.ctx.variables.get("file_list")
        f_instance_id = self.ctx.variables.get("f_instance_id")

        if not file_list or not file_list_1:
            return {"code": -1, "msg": "参数缺失"}

        file_dict = {item.get("id"): item for item in file_list}
        file_dict_1 = {item.get("id"): item for item in file_list_1}

        not_ready_file = "下列文件未上传且无备注<br>"
        file_review_list = []

        def get_urls(item):
            urls = []
            if item.get("file_url"):
                for row in item["file_url"]:
                    url_info = row.get("response", {}).get("FileList", [{}])[0]
                    if url_info.get("url"):
                        urls.append(url_info["url"])
            return ";".join(urls)

        for file_id, item in file_dict.items():
            # 获取当前项的信息
            current_url = get_urls(item)
            current_remark = item.get("remark", "").strip()

            # 判断是否为空（无文件 + 无备注）
            is_empty = not current_url and (current_remark == "无" or not current_remark)
            if is_empty:
                not_ready_file += f"【{item.get('file_name')}】<br>"

            old_item = file_dict_1.get(file_id)
            if old_item:
                old_url = get_urls(old_item)
                old_remark = old_item.get("remark", "").strip()
                url_changed = current_url != old_url
                remark_changed = current_remark != old_remark

                # 只要不是空的，且发生了变化，就加入回传列表
                if (url_changed or remark_changed) and not is_empty:
                    file_review_list.append({
                        "file_name": item.get("file_name"),
                        "remark": current_remark,
                        "id": file_id,
                        "file_type": item.get("file_type"),
                        "file_source": item.get("file_source"),
                        "file_url": current_url,
                    })

        # 检查是否有未准备好文件
        if not_ready_file != "下列文件未上传且无备注<br>":
            return {"code": -1, "msg": not_ready_file}

        # 如果有需要更新的内容，发送给平台
        rep = ""
        if file_review_list:
            try:
                rep = gnetops.request(action="Nbroker",
                                      method="UrlProxy",
                                      ext_data={
                                          "NbrokerData": {
                                              "context": {
                                                  "service_name": "TestFileUpdate",
                                                  "method_name": "test_file_Update"
                                              },
                                              "args": {
                                                  "file_list": file_review_list
                                              },
                                          },
                                          "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                                      }
                                      )
                data_back = rep.get("result")
                if data_back and data_back.get("code") != 200:
                    return {"code": -1, "msg": "回传测试平台失败，请稍后重试或联系开发人员"}
            except Exception as e:
                return {"code": -1, "msg": str(e)}

        # 所有检查通过，可以结束流程
        variables = {
            "file_list": file_list,
            "rep": rep
        }

        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TestFileReview(AjaxTodoBase):
    """
    测试平台文件审核
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        file_list = self.ctx.variables.get("file_list")
        f_instance_id = self.ctx.variables.get("f_instance_id")
        file_review_list = process_data.get("file_review_list")
        file_list_send = []
        unreviewed_name_str = ''
        for num, item in enumerate(file_review_list):
            if not item.get("status"):
                unreviewed_name_str += f"{item.get('file_name')},<br>"
            else:
                if item.get("status") == "通过":
                    url_list = []
                    if item.get("file_url") and item["file_url"]:
                        for row in item["file_url"]:
                            if row.get("response", {}).get("FileList", [{}])[0].get("url"):
                                url_list.append(row["response"]["FileList"][0]["url"])
                    file_list_send.append({
                        "file_name": item.get("file_name"),
                        "file_url": ";".join(url_list),
                        "id": int(item.get("id")),
                        "remark": item.get("remark") if item.get("remark") != "无" else "",
                        "file_type": item.get("file_type"),
                        "file_source": item.get("file_source"),
                    })
            for row in file_list:
                if row.get("id") == item.get("id"):
                    remark = item.get("remark")
                    if remark and remark != "无":
                        row["status"] = item.get("status")
                        row["remark"] = item.get("remark")
                    else:
                        row["status"] = item.get("status")

        if not unreviewed_name_str:
            if file_list_send:
                rep = gnetops.request(action="Nbroker",
                                      method="UrlProxy",
                                      ext_data={
                                          "NbrokerData": {
                                              "context": {
                                                  "service_name": "TestFileUpdate",
                                                  "method_name": "test_file_Update"
                                              },
                                              "args": {
                                                  "file_list": file_list_send
                                              },
                                          },
                                          "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                                      }
                                      )
                data_back = rep.get("result")
                if data_back and data_back.get("code") != 200:
                    data = {"code": -1, "data": rep, "msg": "回传测试平台时失败，请稍后重试或联系开发人员"}
                    return data
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    "file_list": file_list,
                }  # 确保键是字符串
            ).call()
            variables = {
                "file_list": file_list,
                "file_list_send": file_list_send,
            }
            GnetopsTodoEnd(self.ctx.task_id)
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"code": 0, "msg": "文件传递测试平台成功"}
        else:
            return {"code": -1, "msg": f"下列文件未审核<br>{unreviewed_name_str}"}
