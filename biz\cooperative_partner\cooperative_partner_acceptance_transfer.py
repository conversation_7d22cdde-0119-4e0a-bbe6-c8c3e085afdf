import copy
import json
import mimetypes
import os
import re
import uuid
import threading
from urllib.parse import unquote

import requests
from iBroker.lib import exception
from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, gnetops, cos
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate
from iBroker.logone import logger
from biz.cooperative_partner.cooperative_partner_general_method import obtain_supplier_information, \
    calling_external_interfaces


class AcceptanceTransferDimension(object):
    """
        验收转维
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(AcceptanceTransferDimension, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def acceptance_transfer_dimension_process_initiation(self, project_name):
        """
            创建工单--验收转维
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        # 获取供应商流程标识
        cooperative_partner_process_identification = config.get_config_map(
            "cooperative_partner_process_identification")
        # 判断并获取对应的 process_identification
        if Facilitator in cooperative_partner_process_identification:
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '验收转维':
                    ProcessDefinitionKey = process.get('process_identification')

        if system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": {
                        "TencentTicketId": ticket_id,
                        "ProjectName": project_name
                    },
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:验收转维",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:验收转维",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_zongxiyu",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_zongxiyu",  # (*必填)
                        "Deal": "v_zongxiyu"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)

            ticket_id = self.ctx.variables.get("ticket_id")
            PartnerTicketId = str(info['data']['TicketId'])
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/Create_new_accept_to_maint"
                }
            )
            PartnerTicketId = str(info['Data'])

        db = mysql.new_mysql_instance("tbconstruct")
        mozu_sql = "SELECT module_name FROM project_module_mapping_relationship " \
                   " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(mozu_sql)
        for mozu in module_name_list:
            module_name = mozu.get('module_name')
        insert_data = {
            'ticket_id': ticket_id,
            'project_name': project_name,
            'module_name': module_name
        }

        if insert_data:
            db.insert("maintenance_main_process", insert_data)

        variables = {
            'info': str(info),
            'corp_id': corp_id,
            'system_id': system_id,
            'system_type': system_type,
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_training_plan_and_data_approval_results(self, project_name, examine_approve_result, dismiss_role,
                                                         remark):
        """
            培训计划与资料审批结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")

        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'
        feedback_training_plan_and_data_approval_results_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "feedback_training_plan_and_data_approval_results",  # (*必填) 云函数名称
                        "data": feedback_training_plan_and_data_approval_results_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": feedback_training_plan_and_data_approval_results_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                                  "feedback_training_plan_and_data_approval_results"
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'feedback_training_plan_and_data_approval_results': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def material_information_approval_feedback(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            物资信息审批反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")
        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        if not remark:
            remark = '无'
        material_information_approval_feedback_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "material_information_approval_feedback",  # (*必填) 云函数名称
                        "data": material_information_approval_feedback_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": material_information_approval_feedback_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/material_information_approval_feedback",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'material_information_approval_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def download_and_upload_file(self, url, save_dir="./relay/construct/"):
        """
        通用文件下载函数（自动识别文件类型）
        :param url: 文件下载地址
        :param save_dir: 保存目录（默认./downloads）
        :return: (文件保存路径) 或 (None) 如果失败
        """
        try:
            # 创建目录（如果不存在）
            os.makedirs(save_dir, exist_ok=True)

            # 第一次请求获取文件信息（HEAD方法）
            headers = {"User-Agent": "Mozilla/5.0"}
            response = requests.get(url, headers=headers, stream=True)
            response.raise_for_status()

            # 解析文件类型（优先从Content-Type获取）
            content_type = response.headers.get("Content-Type", "").split(";")[0]
            file_ext = (
                    mimetypes.guess_extension(content_type) or ".bin"
            )  # 默认用.bin表示未知类型

            # 解析文件名（优先从Content-Disposition获取）
            content_disp = response.headers.get("Content-Disposition", "")
            filename_match = re.search(
                r'filename\*?=["\']?(?:UTF-\d["\']?)?([^"\'\s;]+)', content_disp, re.I
            )

            if filename_match:
                filename = unquote(filename_match.group(1))  # 解码URL编码

                if isinstance(filename, bytes):
                    filename = filename.decode('utf-8')
                # 分离文件名和扩展名
                name, ext = os.path.splitext(filename)
            else:
                # 从URL最后部分生成文件名（带识别出的后缀）
                base_name = url.split('/')[-1].split('?')[0]
                name, ext = os.path.splitext(base_name)
                if not ext:
                    ext = file_ext

            short_uuid = str(uuid.uuid4())[:8]
            name = f"{name}_{short_uuid}"
            filename = f"{name}{ext}"

            name, ext = os.path.splitext(filename)

            # 完整保存路径
            save_path = os.path.join(save_dir, filename)

            # 检查是否已有同名文件，如果有则添加短UUID
            counter = 0
            while os.path.exists(save_path):
                # 只取 UUID 的前8位，避免文件名过长
                short_uuid = str(uuid.uuid4())[:8]
                new_filename = f"{name}_{short_uuid}{ext}"
                save_path = os.path.join(save_dir, new_filename)
                counter += 1
                if counter > 10:
                    raise FileExistsError(f"无法生成唯一文件名：{filename}")

            # 写入文件
            with open(save_path, "wb") as f:
                for chunk in response.iter_content(8192):
                    f.write(chunk)

            print(f"文件已保存至: {save_path}")
            cos_path = "relay/construct/"
            cos.upload_file(file_name=save_path, cos_path=cos_path)
            # upload_file_url = cos.get_file_url(file_name=os.path.basename(save_path), cos_path=cos_path)
            # part = upload_file_url.split("?")
            # url = part[0]
            cos_domain = config.get_config_string("cos_domain", "", False)
            url = f"{cos_domain}/{cos_path}{os.path.basename(save_path)}"

            return url
            # return save_path, content_type

            # return save_path, content_type

        except (
                exception.LogicException,
                IOError,
                requests.exceptions.RequestException,
        ) as e:
            print(f"下载上传文件失败: {str(e)}")
            return url

    def function_1(self,data,new_urls):
        # 更新数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        data_list = [{
            "ticket_id": data.get("ticket_id"),
            "campus": data.get("campus"),
            "project_name": data.get("project_name"),
            "equipment_category": data.get("equipment_category"),
            "actual_delivery_time": data.get("actual_delivery_time"),
            "fh_remark": data.get("fh_remark"),
            "upload_shipping_information": json.dumps(new_urls, ensure_ascii=False),
            'equipment_batch': data.get("equipment_batch")
        }]
        tb_db.insert_batch("equipment_arrival_data", data_list)

    def generic_async_file_processor(self, data: list, function_object: object):
        """
        异步处理每个附件
        :param data:
        data (list of dict):
            数据列表，每个元素为一个字典对象，必须包含 "url_list" 键。
            "url_list" 的值为 JSON 格式的字符串，内容为字符串列表（List[str]）。

            示例：
            [
                {
                    "url_list": "['http://example.com/file1.jpg', 'http://example.com/file2.pdf']"
                },
                ...
            ]
        """
        file_domain = config.get_config_map("dcops_file_domain_name")
        try:
            for item in data:
                original_urls = json.loads(item["url_list"])
                if not isinstance(original_urls, list):
                    original_urls = [original_urls]

                new_urls = []

                for url in original_urls:
                    matched = False
                    for domain in file_domain:
                        if url.startswith(domain):
                            new_urls.append(url)
                            matched = True
                            break  # 域名匹配成功，不再继续判断
                    if matched:
                        continue
                    try:
                        save_dir = "./relay/construct/"
                        # 创建目录（如果不存在）
                        os.makedirs(save_dir, exist_ok=True)

                        # 第一次请求获取文件信息（HEAD方法）
                        headers = {"User-Agent": "Mozilla/5.0"}
                        response = requests.get(url, headers=headers, stream=True)
                        response.raise_for_status()

                        # 解析文件类型（优先从Content-Type获取）
                        content_type = response.headers.get("Content-Type", "").split(";")[0]
                        file_ext = (
                                mimetypes.guess_extension(content_type) or ".bin"
                        )  # 默认用.bin表示未知类型

                        # 解析文件名（优先从Content-Disposition获取）
                        content_disp = response.headers.get("Content-Disposition", "")
                        filename_match = re.search(
                            r'filename\*?=["\']?(?:UTF-\d["\']?)?([^"\'\s;]+)', content_disp, re.I
                        )

                        if filename_match:
                            filename = unquote(filename_match.group(1))  # 解码URL编码

                            if isinstance(filename, bytes):
                                filename = filename.decode('utf-8')
                            # 分离文件名和扩展名
                            name, ext = os.path.splitext(filename)
                        else:
                            # 从URL最后部分生成文件名（带识别出的后缀）
                            base_name = url.split('/')[-1].split('?')[0]
                            name, ext = os.path.splitext(base_name)
                            if not ext:
                                ext = file_ext

                        short_uuid = str(uuid.uuid4())[:8]
                        name = f"{name}_{short_uuid}"
                        filename = f"{name}{ext}"

                        name, ext = os.path.splitext(filename)

                        # 完整保存路径
                        save_path = os.path.join(save_dir, filename)

                        # 检查是否已有同名文件，如果有则添加短UUID
                        counter = 0
                        while os.path.exists(save_path):
                            # 只取 UUID 的前8位，避免文件名过长
                            short_uuid = str(uuid.uuid4())[:8]
                            new_filename = f"{name}_{short_uuid}{ext}"
                            save_path = os.path.join(save_dir, new_filename)
                            counter += 1
                            if counter > 10:
                                raise FileExistsError(f"无法生成唯一文件名：{filename}")

                        # 写入文件
                        with open(save_path, "wb") as f:
                            for chunk in response.iter_content(8192):
                                f.write(chunk)

                        print(f"文件已保存至: {save_path}")
                        cos_path = "relay/construct/"
                        cos.upload_file(file_name=save_path, cos_path=cos_path)
                        # upload_file_url = cos.get_file_url(file_name=os.path.basename(save_path), cos_path=cos_path)
                        # part = upload_file_url.split("?")
                        # url = part[0]
                        cos_domain = config.get_config_string("cos_domain", "", False)
                        new_url = f"{cos_domain}/{cos_path}{os.path.basename(save_path)}"

                        new_urls.append(new_url if new_url else url)


                    except (
                            exception.LogicException,
                            IOError,
                            requests.exceptions.RequestException,
                    ) as e:
                        print(f"下载上传文件失败: {str(e)}")
                        new_urls.append(url)

                logger.info(f"[{item}] 链接已更新为: {new_urls}")
                function_object(data, new_urls)

        except Exception as e:
            logger.info(f"[{item}] 异步处理失败: {e}")


    def get_basic_info(self, project_name, device_name, TencentTicketId):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT ticket_id FROM equipment_production_racking_process " \
              f"WHERE device_name = '{device_name}' " \
              f"AND project_name = '{project_name}' " \
              f"AND ticket_id = '{TencentTicketId}'"
        result_list = db.get_all(sql)
        if result_list:
            project_name = result_list[0].get("ticket_id")
            return project_name
        else:
            return 0

    def delivery_request(self, TencentTicketId, ProjectName, DeviceName, DeliveryList):
        """
            发货申请
        """
        # 输出转换后的数据
        if TencentTicketId and ProjectName and DeviceName and DeliveryList:
            info = self.get_basic_info(project_name=ProjectName, device_name=DeviceName,
                                       TencentTicketId=TencentTicketId)
            if info == 0:
                return {'code': -1, 'msg': '参数错误'}
            match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", ProjectName)
            if match:
                campus = match.group(1)
                project = match.group(2)
            else:
                campus = ProjectName
                project = ProjectName
            data_list = []

            query_data = {
                "SchemaId": "ticket_base",
                "Data": {
                    "ResultColumns": {
                        "InstanceId": "",
                    },
                    "SearchCondition": {
                        "TicketId": TencentTicketId
                    }
                }
            }
            ticket_info = gnetops.request(
                action="QueryData", method="Run", ext_data=query_data
            )
            res_list = ticket_info['List']
            for row in res_list:
                instance_id = row.get('InstanceId')
            WorkflowVarUpdate(instance_id=instance_id, variables={"DeliveryList":DeliveryList}).call()

            for idx, item in enumerate(DeliveryList):
                ActualDeliveryTime = item.get('ActualDeliveryTime')
                Remark = item.get('Remark')
                Appendix = item.get('Appendix') or ''
                EquipmentBatch = item.get('EquipmentBatch')

                if not all([ActualDeliveryTime, EquipmentBatch]):
                    return {'code': -1, 'msg': '参数错误'}

                # 统一处理 Appendix 的两种格式：str 或 [str]
                if isinstance(Appendix, str):
                    original_urls = [Appendix]
                    # is_valid, msg = test_download_link(Appendix)
                    # if not is_valid:
                    #     return {'code': -1, 'msg': f"[{idx}] 链接不可用: {Appendix}, 原因: {msg}"}
                elif isinstance(Appendix, list):
                    # for idx_2, appendix_url in enumerate(Appendix):
                    #     is_valid, msg = test_download_link(appendix_url)
                    #     if not is_valid:
                    #         return {'code': -1, 'msg': f"[{idx_2}] 链接不可用: {appendix_url}, 原因: {msg}"}
                    original_urls = Appendix
                else:
                    original_urls = []

                # 构建插入数据（保留原链接）
                upload_shipping_information = json.dumps(original_urls, ensure_ascii=False)
                if not Remark:
                    Remark = '无'

                data = {
                    "ticket_id": TencentTicketId,
                    "campus": campus,
                    "project_name": project,
                    "equipment_category": DeviceName,
                    "actual_delivery_time": ActualDeliveryTime,
                    "fh_remark": Remark,
                    "upload_shipping_information": upload_shipping_information,
                    'equipment_batch': EquipmentBatch
                }
                data_list.append(data)

                # 启动后台线程处理附件下载与更新
                thread = threading.Thread(
                    target=self.generic_async_file_processor,
                    args=([copy.deepcopy(data)], self.function_1)
                )
                thread.daemon = True  # 设置为守护线程，防止程序挂起
                thread.start()
            return {'code': 200, 'msg': '成功'}

        else:
            return {'code': -1, 'msg': '缺少参数'}
