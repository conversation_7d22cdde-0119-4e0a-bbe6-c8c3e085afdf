"""
角色权限申请相关逻辑
<AUTHOR>
@created 2021/4/19
"""
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops


class RolePermApply(object):
    def __init__(self):
        self.ctx = get_context()

    def initialize(self):
        """
        流程初始化
        :return:
        """
        # TODO
        flow.complete_task(self.ctx.task_id)

    def data_update(self):
        """
        数据更新
        :return:
        """
        flow_vars = flow.get_variables(self.ctx.instance_id, ["PositionName", "PropertyNameToValue", "UserList",
                                                              "PermApplyTicketId", "RealAuditor", "IsPass",
                                                              "RejectReason", "AuditTime"])
        data = {
            "PositionName": flow_vars.get("PositionName"),
            "Properties": flow_vars.get("PropertyNameToValue"),
            "UserList": flow_vars.get("UserList"),
            "PermApplyTicketId": int(flow_vars.get("PermApplyTicketId")),
            "Auditor": flow_vars.get("RealAuditor"),
            "AuditTime": flow_vars.get("AuditTime"),
            "IsPass": int(flow_vars.get("IsPass")),
            "RejectReason": flow_vars.get("RejectReason"),
        }
        gnetops.request("Perm", "AddRole", data)
        flow.complete_task(self.ctx.task_id)
