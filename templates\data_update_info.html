<!-- 数据更新信息显示组件 -->
<div class="data-update-info" style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #409eff;">
    <div class="update-header" style="display: flex; align-items: center; margin-bottom: 5px;">
        <i class="el-icon-time" style="margin-right: 5px; color: #409eff;"></i>
        <span style="font-weight: bold; color: #303133;">数据更新状态</span>
    </div>
    
    <div class="update-details" style="font-size: 12px; color: #606266;">
        <div class="update-item" style="margin-bottom: 3px;">
            <span>最后更新：</span>
            <span id="last-update-time">{{ update_info.last_update }}</span>
            <span style="color: #909399;">
                ({{ update_info.minutes_ago }}分钟前)
            </span>
        </div>
        
        <div class="update-item" style="margin-bottom: 3px;">
            <span>数据记录：</span>
            <span style="color: #67c23a; font-weight: bold;">{{ update_info.record_count }}</span>
            <span>条</span>
        </div>
        
        <div class="update-item" style="margin-bottom: 3px;">
            <span>状态：</span>
            <span class="status-badge" 
                  style="padding: 2px 6px; border-radius: 3px; font-size: 11px;
                         {% if update_info.status == 'success' %}
                         background: #f0f9ff; color: #67c23a; border: 1px solid #b3e19d;
                         {% else %}
                         background: #fef0f0; color: #f56c6c; border: 1px solid #fbc4c4;
                         {% endif %}">
                {% if update_info.status == 'success' %}
                ✓ 正常
                {% else %}
                ✗ 异常
                {% endif %}
            </span>
        </div>
        
        {% if update_info.message %}
        <div class="update-item">
            <span>说明：</span>
            <span style="color: #909399;">{{ update_info.message }}</span>
        </div>
        {% endif %}
    </div>
    
    <!-- 刷新按钮 -->
    <div class="update-actions" style="margin-top: 8px; text-align: right;">
        <button class="refresh-btn" 
                onclick="refreshDataUpdate()"
                style="background: #409eff; color: white; border: none; padding: 4px 8px; 
                       border-radius: 3px; font-size: 11px; cursor: pointer;">
            <i class="el-icon-refresh"></i> 刷新状态
        </button>
        
        {% if update_info.minutes_ago > 60 %}
        <button class="sync-btn" 
                onclick="triggerDataSync()"
                style="background: #e6a23c; color: white; border: none; padding: 4px 8px; 
                       border-radius: 3px; font-size: 11px; cursor: pointer; margin-left: 5px;">
            <i class="el-icon-download"></i> 同步数据
        </button>
        {% endif %}
    </div>
</div>

<script>
// 刷新数据更新状态
function refreshDataUpdate() {
    // 重新加载页面或发送AJAX请求获取最新状态
    location.reload();
}

// 触发数据同步
function triggerDataSync() {
    if (confirm('确定要触发数据同步吗？这可能需要几分钟时间。')) {
        // 显示加载状态
        const syncBtn = document.querySelector('.sync-btn');
        const originalText = syncBtn.innerHTML;
        syncBtn.innerHTML = '<i class="el-icon-loading"></i> 同步中...';
        syncBtn.disabled = true;
        
        // 发送同步请求（需要根据实际API调整）
        fetch('/api/trigger-data-sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('数据同步已启动，请稍后刷新页面查看结果。');
                setTimeout(() => location.reload(), 3000);
            } else {
                alert('数据同步启动失败：' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('同步请求失败:', error);
            alert('数据同步请求失败，请稍后重试。');
        })
        .finally(() => {
            syncBtn.innerHTML = originalText;
            syncBtn.disabled = false;
        });
    }
}

// 自动刷新状态（可选）
setInterval(() => {
    const minutesAgo = parseInt('{{ update_info.minutes_ago }}');
    if (minutesAgo > 0) {
        const timeElement = document.querySelector('#last-update-time');
        if (timeElement) {
            // 更新显示的分钟数
            const newMinutes = minutesAgo + Math.floor((Date.now() - pageLoadTime) / 60000);
            const parentElement = timeElement.parentElement;
            const minutesSpan = parentElement.querySelector('span:last-child');
            if (minutesSpan) {
                minutesSpan.textContent = `(${newMinutes}分钟前)`;
            }
        }
    }
}, 60000); // 每分钟更新一次

// 记录页面加载时间
const pageLoadTime = Date.now();
</script>

<style>
.data-update-info:hover {
    background: #f0f9ff !important;
    transition: background-color 0.3s ease;
}

.refresh-btn:hover {
    background: #337ecc !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.sync-btn:hover {
    background: #cf9236 !important;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.status-badge {
    transition: all 0.2s ease;
}

.update-item {
    transition: color 0.2s ease;
}

.update-item:hover {
    color: #303133 !important;
}
</style>
