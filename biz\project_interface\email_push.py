import datetime
from dateutil.relativedelta import relativedelta
from iBroker.lib.sdk import tof
from iBroker.lib import mysql


class EmailPush(object):
    def page_send_email(self, email_title, email_receiver_list, email_Cc_list, email_content):
        # 接收人
        if email_receiver_list:
            email_receiver = email_receiver_list.split(';')
            for j in range(len(email_receiver)):
                email_receiver[j] += "@tencent.com"
        else:
            email_receiver = []
        # 邮件抄送人
        if email_Cc_list:
            email_Cc = email_Cc_list.split(';')
            for j in range(len(email_Cc)):
                email_Cc[j] += "@tencent.com"
        else:
            email_Cc = []
        tof.send_email(sendTitle=email_title, msgContent=email_content,
                       sendTo=email_receiver,
                       sendCopy=email_Cc)

        return {"code": 200, "msg": "success"}

    def basic(self, info_list):
        # 初始化最早时间变量和对应的记录
        time = ''
        earliest_delivery_target = None
        earliest_campus = None
        earliest_project = None
        # 遍历查询结果
        for row in info_list:
            delivery_target = row.get("delivery_target")
            if delivery_target is not None:
                delivery_target_date = datetime.datetime.strptime(delivery_target, "%Y年%m月%d日")
                if earliest_delivery_target is None or delivery_target_date < earliest_delivery_target:
                    earliest_delivery_target = delivery_target_date
                    earliest_campus = row.get('campus')
                    earliest_project = row.get('project')
        if earliest_delivery_target is not None:
            time = earliest_delivery_target.strftime("%Y-%m-%d")

        data = {
            "time": time,
            "campus": earliest_campus,
            "project": earliest_project
        }
        return data

    def get_time(self, campus, delivery_target):
        po_date = None
        db = mysql.new_mysql_instance("tbconstruct")
        wd_sql = "SELECT have_or_not_external_resources " \
                 "FROM influence_factor " \
                 f"WHERE campus='{campus}'"
        wd_result = db.get_all(wd_sql)
        if wd_result:
            have_external_resources = wd_result[0].get('have_or_not_external_resources')
            if have_external_resources == '无':
                if delivery_target is not None:
                    delivery_date = datetime.datetime.strptime(delivery_target, "%Y-%m-%d")
                    po_date = (delivery_date - relativedelta(months=10)).strftime("%Y-%m-%d")
            elif have_external_resources == '有':
                if delivery_target is not None:
                    delivery_date = datetime.datetime.strptime(delivery_target, "%Y-%m-%d")
                    po_date = (delivery_date - relativedelta(months=6)).strftime("%Y-%m-%d")
            return po_date
        else:
            return 0

    def get_delivery_time(self):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, project, delivery_target,integrators,project_manager, examination,supervision," \
                    "AHU,low_voltage_cabinet,medium_voltage_cabinet,battery,chaifa,cabinet,headboard," \
                    "transformer,PDU,tbox FROM supplier_resources"
        result = db.get_all(query_sql)
        jcs = []
        xg = []
        cs = []
        jl = []
        AHU = []
        dyg = []
        zyg = []
        dc = []
        cf = []
        jg = []
        byq = []
        ltg = []
        PDU = []
        tbox = []
        # 继续处理其他列
        for i in result:
            if i.get("transformer") == 'NA':
                byq.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("integrators") == 'NA':
                jcs.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("project_manager") == 'NA':
                xg.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("examination") == 'NA':
                cs.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("supervision") == 'NA':
                jl.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("AHU") == 'NA':
                AHU.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("low_voltage_cabinet") == 'NA':
                dyg.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("medium_voltage_cabinet") == 'NA':
                zyg.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("battery") == 'NA':
                dc.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("chaifa") == 'NA':
                cf.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("cabinet") == 'NA':
                jg.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("headboard") == 'NA':
                ltg.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("PDU") == 'NA':
                PDU.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
            if i.get("tbox") == 'NA':
                tbox.append({
                    "campus": i.get("campus"),
                    "project": i.get("project"),
                    "delivery_target": i.get("delivery_target")
                })
        name = [{"集成商": jcs}, {"项管": xg}, {"测试": cs}, {"监理": jl}, {"AHU": AHU}, {"低压柜": dyg},
                {"中压柜": zyg}, {"电池": dc}, {"柴发": cf}, {"机柜": jg}, {"变压器": byq}, {"高压直流&列头柜": ltg},
                {"PDU": PDU}, {"tbox": tbox}]
        str_info = "<ol>\n"
        for item in name:
            for key, value in item.items():
                if len(value) > 0:
                    delivery_target = None
                    info = self.basic(value)
                    campus = info.get('campus')
                    time = info.get('time')
                    if time:
                        delivery_target = self.get_time(campus, time)
                    str_info += (
                        f"<li>{key}:缺少{len(value)};最早需求时间:{delivery_target}</li>\n"
                    )
        str_info += "</ol>"
        return str_info
