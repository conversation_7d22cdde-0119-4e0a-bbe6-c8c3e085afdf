# 设备数据合并脚本说明

## 概述

基于您提供的数据库表结构图片，编写了设备数据合并脚本，用于将多个表中的设备相关数据合并到统一的表中。

## 涉及的数据库表

根据图片中的表结构，脚本涉及以下表：

1. **all_construction_ticket_data** - 工单数据表
   - TicketId (工单ID)
   - Title (标题)
   - SchemeNameCn (方案名称)
   - IsForceEnd (是否强制结束)

2. **equipment_production_racking_process** - 设备生产流程表
   - ticket_id (工单ID)
   - project_name (项目名称)
   - device_name (设备名称)
   - equipment_type (设备类型)

3. **delivery_schedule_management_table** - 交付计划管理表
   - project_name (项目名称)
   - device_type (设备类型)
   - supplier (供应商)
   - equipment_sla (设备SLA)
   - expected_time_equipment (预期设备时间)
   - estimated_time_delivery (预计交付时间)
   - delivery_gap (交付间隔)

4. **equipment_category_mapping_relationship** - 设备分类映射关系表
   - device_name (设备名称)
   - equipment_sla (设备SLA)
   - material_category_name (物料分类名称)

5. **summary_data** - 汇总数据表
   - campus (校区)
   - project_name (项目名称)
   - state (状态)

6. **construct_plan_data** - 建设计划数据表
   - project_name (项目名称)
   - work_content (工作内容)
   - completion_time (完成时间)

7. **payment_info** - 付款信息表
   - project_name (项目名称)
   - po_create_time (PO创建时间)

## 文件说明

### 1. consolidate_equipment_data.py
主要的Python脚本，包含以下功能：
- `create_consolidated_table()` - 创建合并表
- `consolidate_data()` - 执行数据合并
- `check_data_quality()` - 数据质量检查

### 2. consolidate_equipment_data.sql
原生SQL查询文件，包含：
- 表创建语句
- 简化的单次查询方案
- 分步查询方案
- 数据质量检查SQL
- 数据清理SQL

### 3. run_consolidation.py
执行脚本，用于运行整个合并流程

### 4. README_consolidation.md
本说明文件

## 使用方法

### 方法1: 使用Python脚本（推荐）

```bash
# 查看帮助
python scripts/run_consolidation.py --help

# 执行数据合并
python scripts/run_consolidation.py
```

### 方法2: 使用SQL文件

```sql
-- 在MySQL客户端中执行
source scripts/consolidate_equipment_data.sql;
```

## 合并逻辑

脚本采用分步查询的方式，避免复杂的多表JOIN：

1. **基础数据获取**: 从工单表和设备生产流程表获取基础信息
2. **交付计划匹配**: 根据项目名称和设备类型匹配交付计划
3. **设备分类匹配**: 根据设备名称获取SLA信息
4. **项目状态匹配**: 根据项目名称获取状态信息
5. **建设计划匹配**: 根据项目名称和工作内容获取完成时间
6. **付款信息匹配**: 根据项目名称获取PO创建时间

## 数据质量保证

- 使用 `INSERT IGNORE` 避免重复数据
- 对关键字段进行非空检查
- 提供默认值处理（如"待确认"、"未知状态"）
- 包含数据质量检查功能
- 提供数据清理SQL

## 性能优化

- 分步查询避免复杂JOIN
- 使用字典缓存提高匹配效率
- 限制查询记录数（默认5000条）
- 提供执行进度日志

## 注意事项

1. **数据库连接**: 确保数据库连接配置正确
2. **权限要求**: 需要对相关表的读取权限和目标表的写入权限
3. **磁盘空间**: 确保有足够的磁盘空间存储合并数据
4. **执行时间**: 建议在业务低峰期执行
5. **数据备份**: 执行前建议备份相关表

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库连接配置
2. **权限不足**: 确认数据库用户权限
3. **表不存在**: 确认所有涉及的表都存在
4. **数据类型不匹配**: 检查字段类型定义

### 日志查看

脚本会输出详细的执行日志，包括：
- 每个步骤的执行状态
- 数据获取数量
- 错误信息和警告
- 执行时间统计

## 扩展说明

如需修改合并逻辑或添加新的数据源：

1. 在 `consolidate_equipment_data.py` 中添加新的查询逻辑
2. 更新 `consolidated_equipment_data` 表结构
3. 修改数据质量检查逻辑
4. 更新相关SQL文件

## 联系支持

如有问题或需要定制化修改，请联系开发团队。
