import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class MainsSpecial(object):
    """
        市电专项
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(MainsSpecial, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def mains_special_completed_confirmation_audit(self, project_name, task_name, examine_approve_result,
                                                   dismiss_role, remark):
        """
            市电专项子任务完工确认审批结果回传
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        system_type = self.ctx.variables.get("system_type")
        system_id = self.ctx.variables.get("system_id")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        sdzx_data = {
            "ProjectName": project_name,  # 项目名称
            "TaskName": task_name,  # 子任务名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": sdzx_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/new_electric_item_sub_confirmation_audit",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "new_electric_item_sub_confirmation_audit",  # (*必填) 云函数名称
                        "data": sdzx_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info['data']['result']
            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceSdzx(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceSdzx, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_wdbz_completion_confirmation_data_submission(self):
        """
            等待外电报装完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word1_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                    "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                    "FROM excel_data " \
                    f"WHERE dcops_ticket_id = '{ticket_id}' " \
                    f"AND project_name = '{project_name}' " \
                    "AND now_time = " \
                    "(SELECT MAX(now_time) " \
                    "FROM excel_data " \
                    f"WHERE project_name = '{project_name}')" \
                    "AND (serial_number = '2.9')"
        word2_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                    "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                    "FROM excel_data " \
                    f"WHERE dcops_ticket_id = '{ticket_id}' " \
                    f"AND project_name = '{project_name}' " \
                    "AND now_time = " \
                    "(SELECT MAX(now_time) " \
                    "FROM excel_data " \
                    f"WHERE project_name = '{project_name}')" \
                    "AND (serial_number = '2.9.1')"
        word1_list = db.get_all(word1_sql)
        word2_list = db.get_all(word2_sql)
        wdbz_word_table = []
        if system_type == '外部':
            for row in word1_list:
                for item in word2_list:
                    serial_number = row.get('serial_number', '')

                    # 根据 serial_number 选择需要的字段
                    if serial_number == '2.9':
                        sdzx_work_content = row.get('work_content', '')
                        sdzx_start_time = row.get('start_time', '')
                        sdzx_completion_time = row.get('completion_time', '')
                        sdzx_responsible_person = row.get('responsible_person', '')

                    if serial_number == '2.9.1':
                        work_content = item.get('work_content', '')
                        start_time = item.get('start_time', '')
                        completion_time = item.get('completion_time', '')
                        actual_start_time = item.get('actual_start_time', '')
                        actual_finish_time = item.get('actual_finish_time', '')
                        schedule_skewing_explain = item.get('schedule_skewing_explain', '')
                        appendix = item.get('appendix', '')

                        # 尝试将 report_url 字符串解析为列表
                        try:
                            appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                        except (ValueError, SyntaxError):
                            appendixs = []  # 如果解析失败，设置为空列表
                        appendix_list = []
                        # 确保 report_urls 是一个列表
                        if isinstance(appendixs, list):
                            # 为每个 URL 创建一个新的字典
                            for url in appendixs:
                                appendix_list.append({"appendix": url})

                        # 计算计划完成天数
                        start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                        completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                        planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                        # 计算实际完成天数
                        actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                        actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                        actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                        wdbz_word_table.append({
                            "serial_number": serial_number,
                            "work_content": work_content,
                            "start_time": start_time,
                            "completion_time": completion_time,
                            "planned_project_duration": planned_project_duration,
                            "actual_start_time": actual_start_time,
                            "actual_finish_time": actual_finish_time,
                            "actual_construction_time": actual_construction_time,
                            "schedule_skewing_explain": schedule_skewing_explain,
                            "appendix": appendix_list
                        })
        elif system_type == 'Dcops':
            for row in word1_list:
                for item in word2_list:
                    serial_number = row.get('serial_number', '')

                    # 根据 serial_number 选择需要的字段
                    if serial_number == '2.9':
                        sdzx_work_content = row.get('work_content', '')
                        sdzx_start_time = row.get('start_time', '')
                        sdzx_completion_time = row.get('completion_time', '')
                        sdzx_responsible_person = row.get('responsible_person', '')

                    if serial_number == '2.9.1':
                        work_content = item.get('work_content', '')
                        start_time = item.get('start_time', '')
                        completion_time = item.get('completion_time', '')
                        actual_start_time = item.get('actual_start_time', '')
                        actual_finish_time = item.get('actual_finish_time', '')
                        schedule_skewing_explain = item.get('schedule_skewing_explain', '')
                        appendix = item.get('appendix', '')
                        appendix_list = []
                        # 使用 ast.literal_eval 解析字符串
                        file_list = ast.literal_eval(appendix) if appendix else []
                        # 处理文件列表
                        for file_item in file_list:
                            # 从 response 中获取 FileList
                            if isinstance(file_item, dict) and 'response' in file_item:
                                file_list_data = file_item['response'].get('FileList', [])
                                # 遍历 FileList 获取 url
                                for file_data in file_list_data:
                                    if 'url' in file_data:
                                        appendix_list.append({"appendix": file_data['url']})

                        # 计算计划完成天数
                        start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                        completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                        planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                        # 计算实际完成天数
                        actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                        actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                        actual_construction_time = (
                                actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                        wdbz_word_table.append({
                            "serial_number": serial_number,
                            "work_content": work_content,
                            "start_time": start_time,
                            "completion_time": completion_time,
                            "actual_start_time": actual_start_time,
                            "actual_finish_time": actual_finish_time,
                            "schedule_skewing_explain": schedule_skewing_explain,
                            "actual_construction_time": actual_construction_time,
                            "planned_project_duration": planned_project_duration,
                            "appendix": appendix_list
                        })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word2_list})
        if word2_list:
            variables = {
                'sdzx_work_content': sdzx_work_content,
                'sdzx_start_time': sdzx_start_time,
                'sdzx_completion_time': sdzx_completion_time,
                'sdzx_responsible_person': sdzx_responsible_person,
                'wdbz_word_table': wdbz_word_table,
                'word_list': word2_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_wdfa_completion_confirmation_data_submission(self):
        """
            等待外电方案完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.2'"
        word_list = db.get_all(word_sql)
        wdfa_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.2':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    wdfa_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.2':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）
                    wdfa_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'wdfa_word_table': wdfa_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_kkf_completion_confirmation_data_submission(self):
        """
            等待可靠费完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.3'"
        word_list = db.get_all(word_sql)
        kkf_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.3':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    kkf_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.3':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})
                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    kkf_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'kkf_word_table': kkf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gdht_completion_confirmation_data_submission(self):
        """
            等待供电合同完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.4'"
        word_list = db.get_all(word_sql)
        gdht_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.4':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    gdht_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.4':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})
                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    gdht_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gdht_word_table': gdht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_ddxy_completion_confirmation_data_submission(self):
        """
            等待调度协议完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.5'"
        word_list = db.get_all(word_sql)
        ddxy_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.5':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    ddxy_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.5':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})
                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    ddxy_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'ddxy_word_table': ddxy_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sdqjc_completion_confirmation_data_submission(self):
        """
            等待送电前检测完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.6'"
        word_list = db.get_all(word_sql)
        sdqjc_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.6':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    sdqjc_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.6':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})
                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    sdqjc_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sdqjc_word_table': sdqjc_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sd_completion_confirmation_data_submission(self):
        """
            等待送电完工确认数据提交
        """
        system_type = self.ctx.variables.get("system_type")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.9.7'"
        word_list = db.get_all(word_sql)
        sd_word_table = []
        if system_type == '外部':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.7':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')

                    # 尝试将 report_url 字符串解析为列表
                    try:
                        appendixs = ast.literal_eval(appendix)  # 将字符串解析为列表
                    except (ValueError, SyntaxError):
                        appendixs = []  # 如果解析失败，设置为空列表
                    appendix_list = []
                    # 确保 report_urls 是一个列表
                    if isinstance(appendixs, list):
                        # 为每个 URL 创建一个新的字典
                        for url in appendixs:
                            appendix_list.append({"appendix": url})

                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    sd_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "planned_project_duration": planned_project_duration,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "actual_construction_time": actual_construction_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "appendix": appendix_list
                    })
        elif system_type == 'Dcops':
            for row in word_list:
                serial_number = row.get('serial_number', '')

                if serial_number == '2.9.7':
                    work_content = row.get('work_content', '')
                    start_time = row.get('start_time', '')
                    completion_time = row.get('completion_time', '')
                    actual_start_time = row.get('actual_start_time', '')
                    actual_finish_time = row.get('actual_finish_time', '')
                    schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                    appendix = row.get('appendix', '')
                    appendix_list = []
                    # 尝试解析 appendix 为 JSON 格式
                    file_list = ast.literal_eval(appendix) if appendix else []
                    # 处理文件列表
                    for file_item in file_list:
                        # 从 response 中获取 FileList
                        if isinstance(file_item, dict) and 'response' in file_item:
                            file_list_data = file_item['response'].get('FileList', [])
                            # 遍历 FileList 获取 url
                            for file_data in file_list_data:
                                if 'url' in file_data:
                                    appendix_list.append({"appendix": file_data['url']})
                    # 计算计划完成天数
                    start_date = datetime.datetime.strptime(start_time, '%Y-%m-%d')
                    completion_date = datetime.datetime.strptime(completion_time, '%Y-%m-%d')
                    planned_project_duration = (completion_date - start_date).days  # 计算计划工期（天数）

                    # 计算实际完成天数
                    actual_start_date = datetime.datetime.strptime(actual_start_time, '%Y-%m-%d')
                    actual_finish_date = datetime.datetime.strptime(actual_finish_time, '%Y-%m-%d')
                    actual_construction_time = (actual_finish_date - actual_start_date).days  # 计算实际工期（天数）

                    sd_word_table.append({
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "schedule_skewing_explain": schedule_skewing_explain,
                        "actual_construction_time": actual_construction_time,
                        "planned_project_duration": planned_project_duration,
                        "appendix": appendix_list
                    })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sd_word_table': sd_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceSdzx(object):
    """
        市电专项子任务完工确认
    """

    def __init__(self):
        super(PartnerCallInterfaceSdzx, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def new_electric_item_sub_readiness_confirmed(self, TencentTicketId, ProjectName, NumberName, TaskName, StartTime,
                                                  FinishTime, ScheduleSkewingExplain, Appendix, Remark):
        """
            市电专项子项施工完工确认
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        # 校验 TaskName
        if not TaskName or not NumberName:
            return {'code': -1, 'msg': '子任务名称或子任务编号缺失'}
        # 校验 StartTime 和 FinishTime
        if not StartTime or not FinishTime:
            return {'code': -1, 'msg': '实际开始时间和实际完成时间缺失'}
        # 校验 ScheduleSkewingExplain
        if not ScheduleSkewingExplain:
            return {'code': -1, 'msg': '进度偏移说明'}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()

        sdzx_conditions = {
            'project_name': ProjectName,
            'work_content': '市电专项'
        }
        sdzx_update_data = {
            "dcops_ticket_id": TencentTicketId,
            "project_name": ProjectName,
        }
        tb_db.update("excel_data", sdzx_update_data, sdzx_conditions)
        if TaskName == "外电报装":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix)
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "外电方案":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "可靠费":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "供电合同":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "调度协议":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "送电前检测":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "送电":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": str(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务

        return {'code': 200, 'msg': '成功'}

    def create_ticket(self, ProjectName, PartnerTicketId, Facilitator):
        """
            创建市电专项工单（起腾讯侧工单）
        """
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        res_list = []
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号{ProjectName}"
        processTitle = date_string + "市电专项"
        if system_type == "外部":
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'project_name': ProjectName,
                        'system_type': system_type,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_mains_special",
                    "Source": "",
                    "TicketDescription": "市电专项完工确认流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)
        elif system_type == "Dcops":
            supplier_info = 'supplier_differentiation'
            system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'project_name': ProjectName,
                        'system_type': system_type,
                        'system_id': system_id,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_mains_special",
                    "Source": "",
                    "TicketDescription": "市电专项完工确认流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)

        for row in res_list:
            TicketId = row.get('TicketId')

        return {
            'code': 200,
            'msg': '成功',
            'data': TicketId
        }


class SdzxWdbzSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxWdbzSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_wdbz_scheme_supervision_approval = process_data.get('wgqr_wdbz_scheme_supervision_approval')
        wgqr_wdbz_scheme_supervision_remark = process_data.get('wgqr_wdbz_scheme_supervision_remark')

        variables = {
            'wgqr_wdbz_scheme_supervision_approval': wgqr_wdbz_scheme_supervision_approval,
            'wgqr_wdbz_scheme_supervision_remark': wgqr_wdbz_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxWdfaSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxWdfaSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_wdfa_scheme_supervision_approval = process_data.get('wgqr_wdfa_scheme_supervision_approval')
        wgqr_wdfa_scheme_supervision_remark = process_data.get('wgqr_wdfa_scheme_supervision_remark')

        variables = {
            'wgqr_wdfa_scheme_supervision_approval': wgqr_wdfa_scheme_supervision_approval,
            'wgqr_wdfa_scheme_supervision_remark': wgqr_wdfa_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxKkfSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxKkfSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_kkf_scheme_supervision_approval = process_data.get('wgqr_kkf_scheme_supervision_approval')
        wgqr_kkf_scheme_supervision_remark = process_data.get('wgqr_kkf_scheme_supervision_remark')

        variables = {
            'wgqr_kkf_scheme_supervision_approval': wgqr_kkf_scheme_supervision_approval,
            'wgqr_kkf_scheme_supervision_remark': wgqr_kkf_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxGdhtSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxGdhtSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_gdht_scheme_supervision_approval = process_data.get('wgqr_gdht_scheme_supervision_approval')
        wgqr_gdht_scheme_supervision_remark = process_data.get('wgqr_gdht_scheme_supervision_remark')

        variables = {
            'wgqr_gdht_scheme_supervision_approval': wgqr_gdht_scheme_supervision_approval,
            'wgqr_gdht_scheme_supervision_remark': wgqr_gdht_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxDdxySupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxDdxySupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_ddxy_scheme_supervision_approval = process_data.get('wgqr_ddxy_scheme_supervision_approval')
        wgqr_ddxy_scheme_supervision_remark = process_data.get('wgqr_ddxy_scheme_supervision_remark')

        variables = {
            'wgqr_ddxy_scheme_supervision_approval': wgqr_ddxy_scheme_supervision_approval,
            'wgqr_ddxy_scheme_supervision_remark': wgqr_ddxy_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxSdqjcSupervisionExaminationApproval(AjaxTodoBase):
    """
        设备进场-地面处理及放线-施工方案监理审批
    """

    def __init__(self):
        super(SdzxSdqjcSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_sdqjc_scheme_supervision_approval = process_data.get('wgqr_sdqjc_scheme_supervision_approval')
        wgqr_sdqjc_scheme_supervision_remark = process_data.get('wgqr_sdqjc_scheme_supervision_remark')

        variables = {
            'wgqr_sdqjc_scheme_supervision_approval': wgqr_sdqjc_scheme_supervision_approval,
            'wgqr_sdqjc_scheme_supervision_remark': wgqr_sdqjc_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxWgqrSdSupervisionExaminationApproval(AjaxTodoBase):
    """
        市电专项-送电-完工确认监理审批
    """

    def __init__(self):
        super(SdzxWgqrSdSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_sd_scheme_supervision_approval = process_data.get('wgqr_sd_scheme_supervision_approval')
        wgqr_sd_scheme_supervision_remark = process_data.get('wgqr_sd_scheme_supervision_remark')

        variables = {
            'wgqr_sd_scheme_supervision_approval': wgqr_sd_scheme_supervision_approval,
            'wgqr_sd_scheme_supervision_remark': wgqr_sd_scheme_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxWgqrSdItemApproval(AjaxTodoBase):
    """
        市电专项-送电-完工确认项管审批
    """

    def __init__(self):
        super(SdzxWgqrSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_sd_item_approval = process_data.get('wgqr_sd_item_approval')
        wgqr_sd_item_remark = process_data.get('wgqr_sd_item_remark')

        variables = {
            'wgqr_sd_item_approval': wgqr_sd_item_approval,
            'wgqr_sd_item_remark': wgqr_sd_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SdzxWgqrSdPmApproval(AjaxTodoBase):
    """
        市电专项-送电-完工确认PM审批
    """

    def __init__(self):
        super(SdzxWgqrSdPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        wgqr_sd_PM_approval = process_data.get('wgqr_sd_PM_approval')
        wgqr_sd_PM_remark = process_data.get('wgqr_sd_PM_remark')

        variables = {
            'wgqr_sd_PM_approval': wgqr_sd_PM_approval,
            'wgqr_sd_PM_remark': wgqr_sd_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
