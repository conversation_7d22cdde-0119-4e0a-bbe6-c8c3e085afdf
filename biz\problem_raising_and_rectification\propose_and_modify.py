from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib.sdk import flow
from iBroker.lib import mysql

from biz.construction_before_eruipment.tools import Tools


# 选择
class SelectQuestionCategory(AjaxTodoBase):
    """
    选择问题类别
    """

    def __init__(self):
        super(SelectQuestionCategory, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        responsible_unit_list = []
        ticket_id = self.ctx.variables.get("ticket_id")
        next_op = process_data.get("next_op", None)
        project_name = process_data.get("project_name", None)
        # 构造表单展示数据
        problem_list = [{"project_name": project_name}]
        # 获取责任单位数据源
        db = mysql.new_mysql_instance("tbconstruct")
        # 责任单位下拉框选项
        sql = f"SELECT supplier FROM category_supplier_info"
        field_value_query = db.get_all(sql)
        responsible_unit_set = set()
        if field_value_query:
            for record in field_value_query:
                field_value = record.get("supplier")
                if field_value:
                    field_value_list = field_value.split(";")
                    for i in field_value_list:
                        if i.strip():  # 确保不添加空字符串
                            responsible_unit_set.add(i.strip())
            # 将去重后的供应商信息加入 responsible_unit_list
        for unit in responsible_unit_set:
            responsible_unit_list.append({
                "label": unit,
                "value": unit,
            })
        variables = {
            "next_op": next_op,
            "problem_list": problem_list,
            "project_name": project_name,
            "responsible_unit_list": responsible_unit_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 问题上传
class ProblemUpload(AjaxTodoBase):
    """
    问题上传
    """

    def __init__(self):
        super(ProblemUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        next_op = self.ctx.variables.get("next_op")
        problem_list = process_data.get("problem_list", None)
        # 获取整改人信息
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT project_name FROM quality_technics_management WHERE t_id='{ticket_id}'"
        sql2 = f"SELECT project_name FROM equipment_management WHERE t_id='{ticket_id}'"
        sql3 = f"SELECT project_name FROM safety_problem WHERE t_id='{ticket_id}'"
        data_1 = db.get_all(sql1)
        data_2 = db.get_all(sql2)
        data_3 = db.get_all(sql3)
        zb_account = ""
        xg_account = ""
        jl_account = ""
        if project_name == "贵安七星B2-1":
            sql = (
                    "SELECT item_tube,supervision_and_management,integrator FROM project_team_information_data "
                    " WHERE project_name='%s'" % project_name
            )
            # 项管、监理经理人
            chief_interface_list = db.get_all(sql)
            if chief_interface_list:
                # 项管
                xg_account = chief_interface_list[0].get("item_tube")
                # 监理
                jl_account = chief_interface_list[0].get("supervision_and_management")
                # 集成商
                zb_account = chief_interface_list[0].get("integrator")
        else:
            zb_sql = (
                    "SELECT account FROM project_role_account"
                    " WHERE project_name='%s' and role = '集成商-项目经理' and del_flag = 0"
                    % project_name
            )
            jl_sql = (
                    "SELECT account,service_provider FROM project_role_account"
                    " WHERE project_name='%s' and role = '监理-总监理工程师' and del_flag = 0 "
                    % project_name
            )
            xg_sql = (
                    "SELECT account,service_provider FROM project_role_account"
                    " WHERE project_name='%s' and role = '项管-项目经理' and del_flag = 0 "
                    % project_name
            )
            zb = db.get_all(zb_sql)
            jl = db.get_all(jl_sql)
            xg = db.get_all(xg_sql)
            if jl:
                jl_account = jl[0].get("account")
            if xg:
                xg_account = xg[0].get("account")
            if zb:
                zb_account = zb[0].get("account")
        insert_data = {}
        variables = {}
        if problem_list and len(problem_list) > 0:
            for i in problem_list:
                problem_photo_upload = i.get("problem_photo_upload", None)
                # 获取图片的url
                url_list = []
                i["problem_photo"] = []
                if problem_photo_upload:
                    for j in problem_photo_upload:
                        url, result = Tools.check_photo(j)
                        if result:
                            return url
                        url_list.append(url)
                        i["problem_photo"].append(
                            {
                                "url": url,
                                "height": "150px",
                                "width": "150px",
                                "marginRight": "5px",
                            }
                        )

                    # 将图片上传成功的响应数据修改成图片的url
                i["problem_photo_upload"] = ";".join(url_list)
                con = {"t_id": ticket_id}
                insert_data["t_id"] = ticket_id
                insert_data["project_name"] = project_name
                # 问题项
                insert_data["question_item"] = i.get("question_item")
                # 问题类别
                insert_data["problem_category"] = i.get("problem_category")
                # 问题位置
                insert_data["problem_location"] = i.get("problem_location")
                # 问题描述
                insert_data["problem_description"] = i.get("problem_description")
                # 问题照片
                insert_data["problem_photo"] = i.get("problem_photo_upload")
                # 问题具体影响
                insert_data["problem_impact"] = i.get("problem_impact")
                # 影响分类
                insert_data["impact_classification"] = i.get("impact_classification")
                # 问题等级
                insert_data["problem_level"] = i.get("problem_level")
                # 责任单位
                insert_data["responsible_unit"] = i.get("responsible_unit")
                # 问题提出时间
                insert_data["problem_put_forward_time"] = i.get(
                    "problem_put_forward_time"
                )
                tb_db = mysql.new_mysql_instance("tbconstruct")
                if data_1 or data_2 or data_3:
                    if next_op == 1:
                        # 所属专业
                        insert_data["belong_professional"] = i.get(
                            "belong_professional"
                        )
                        # 设备名称
                        insert_data["device_name"] = i.get("device_name")
                        # 存甲供设备表
                        tb_db.begin()
                        tb_db.update("equipment_management", insert_data, con)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }

                    # 质量问题特有数据
                    elif next_op == 2:
                        # 所属专业
                        insert_data["belong_professional"] = i.get(
                            "belong_professional"
                        )
                        # 存质量管理表
                        tb_db.begin()
                        tb_db.update("quality_technics_management", insert_data, con)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }

                    # 安全问题特有数据
                    elif next_op == 3:
                        # 施工单位
                        insert_data["construction_unit"] = i.get("construction_unit")
                        # 存安全管理表
                        tb_db.begin()
                        tb_db.update("safety_problem", insert_data, con)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }
                else:
                    # 设备问题特有数据
                    if next_op == 1:
                        # 所属专业
                        insert_data["belong_professional"] = i.get(
                            "belong_professional"
                        )
                        # 设备名称
                        insert_data["device_name"] = i.get("device_name")
                        # 存甲供设备表
                        tb_db.begin()
                        tb_db.insert("equipment_management", insert_data)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }

                    # 质量问题特有数据
                    elif next_op == 2:
                        # 所属专业
                        insert_data["belong_professional"] = i.get(
                            "belong_professional"
                        )
                        # 存质量管理表
                        tb_db.begin()
                        tb_db.insert("quality_technics_management", insert_data)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }

                    # 安全问题特有数据
                    elif next_op == 3:
                        # 施工单位
                        insert_data["construction_unit"] = i.get("construction_unit")
                        # 存安全管理表
                        tb_db.begin()
                        tb_db.insert("safety_problem", insert_data)
                        tb_db.commit()
                        variables = {
                            "problem_list": problem_list,
                            "solve_list": problem_list,
                            "zb_account": zb_account,
                            "jl_account": jl_account,
                            "xg_account": xg_account,
                        }
        else:
            return {"code": -1, "msg": "问题详情未上传"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProblemSpecify(AjaxTodoBase):
    """
    问题整改：指定整改人
    """

    def __init__(self):
        super(ProblemSpecify, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        rectification_person = process_data.get("rectification_person")
        variables = {
            "rectification_person": rectification_person,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


# 问题整改
class ProblemRectification(AjaxTodoBase):
    """
    问题整改
    """

    def __init__(self):
        super(ProblemRectification, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        solve_list = process_data.get("solve_list", None)
        # 数据存储
        insert_data = {}

        if solve_list and len(solve_list) > 0:
            for i in solve_list:
                photos_after_rectification = i.get("photos_after_rectification")
                url_list = []
                i["fix_photo"] = []
                if photos_after_rectification:
                    for j in photos_after_rectification:
                        # 获取整改照片的url
                        url, result = Tools.check_photo(j)
                        if result:
                            return url
                        url_list.append(url)
                        i["fix_photo"].append(
                            {
                                "url": url,
                                "height": "150px",
                                "width": "150px",
                                "marginRight": "5px",
                            }
                        )
                # 通用数据
                # 问题措施
                insert_data["rectification_reform_measures"] = i.get(
                    "rectification_reform_measures"
                )
                # 问题关闭时间
                insert_data["problem_end_time"] = i.get("problem_end_time")
                # 整改照片
                insert_data["photos_after_rectification"] = ";".join(url_list)
                insert_data["remark"] = i.get("remark")

        variables = {"solve_list": solve_list, "insert_data": insert_data}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SupervisionAndAudit(AjaxTodoBase):
    """
    监理审核
    """

    def __init__(self):
        super(SupervisionAndAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jl_remark = process_data.get("jl_remark")
        jl_review = process_data.get("jl_review")
        jl_rejection = process_data.get("jl_rejection")
        if jl_review:
            if jl_review == "驳回":
                if jl_rejection:
                    variables = {
                        "jl_review": jl_review,
                        "jl_rejection": jl_rejection,
                        "jl_remark": jl_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {"jl_review": jl_review, "jl_remark": jl_remark}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectManagementReview(AjaxTodoBase):
    """
    项管审核
    """

    def __init__(self):
        super(ProjectManagementReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        next_op = self.ctx.variables.get("next_op")
        insert_data = self.ctx.variables.get("insert_data")
        jl_remark = self.ctx.variables.get("jl_remark")
        jl_rejection = self.ctx.variables.get("jl_rejection")
        xg_remark = process_data.get("xg_remark")
        xg_review = process_data.get("xg_review")
        xg_rejection = process_data.get("xg_rejection")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        insert_data["xg_remark"] = xg_remark
        insert_data["xg_rejection"] = xg_rejection
        insert_data["jl_rejection"] = jl_rejection
        insert_data["jl_remark"] = jl_remark
        if xg_review:
            if xg_review == "驳回":
                if xg_rejection:
                    variables = {
                        "xg_review": xg_review,
                        "xg_rejection": xg_rejection,
                        "xg_remark": xg_remark,
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                con = {"t_id": ticket_id}
                # 设备问题特有数据
                if next_op == 1:
                    # 存甲供设备表
                    tb_db.begin()
                    tb_db.update("equipment_management", insert_data, con)
                    tb_db.commit()
                # 质量问题特有数据
                elif next_op == 2:
                    # 存质量管理表
                    tb_db.begin()
                    tb_db.update("quality_technics_management", insert_data, con)
                    tb_db.commit()
                # 安全问题特有数据
                elif next_op == 3:
                    # 存安全管理表
                    tb_db.begin()
                    tb_db.update("safety_problem", insert_data, con)
                    tb_db.commit()
                variables = {"xg_review": xg_review, "xg_remark": xg_remark}
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BuildingBatchInfoData(AjaxTodoBase):
    """
    批量插入数据流程
    """

    def __init__(self):
        super(BuildingBatchInfoData, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        next_op = self.ctx.variables.get("next_op")
        solve_list = process_data.get("solve_list")
        for solve in solve_list:
            solve["project_name"] = project_name
            solve["problem_put_forward_time"] = solve.get("problem_put_forward_time")
            solve["problem_end_time"] = solve.get("problem_end_time")
            solve["problem_location"] = solve.get("problem_location")
            solve["problem_description"] = solve.get("problem_description")
            solve["problem_impact"] = solve.get("problem_impact")
            solve["problem_level"] = solve.get("problem_level")
            solve["rectification_reform_measures"] = solve.get(
                "rectification_reform_measures"
            )
            solve["responsible_unit"] = solve.get("responsible_unit")
            solve["impact_classification"] = solve.get("impact_classification")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        # 设备问题特有数据
        if next_op == 1:
            # 存甲供设备表
            tb_db.begin()
            tb_db.insert_batch("equipment_management", solve_list)
            tb_db.commit()
        # 质量问题特有数据
        elif next_op == 2:
            # 存质量管理表
            tb_db.begin()
            tb_db.insert_batch("quality_technics_management", solve_list)
            tb_db.commit()
        # 安全问题特有数据
        elif next_op == 3:
            # 存安全管理表
            tb_db.begin()
            tb_db.insert_batch("safety_problem", solve_list)
            tb_db.commit()
        variables = {"solve_list": solve_list}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
