import json
from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow
from biz.construction_final_acceptance.tools import Tools


# 初验反馈流程 数据入库类
class InitialAcceptanceIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def initial_acceptance_into_db(self, project_name, project_code, info_dict):
        """
        初验反馈流程数据入库(construction_feedback_to_xingchen)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account = self.ctx.variables.get("PM")
        files_json = Tools.get_files_json(info_dict.get("initial_files"))
        # 初验状态反馈存库-to星辰
        insert_list = []
        insert_list.append(
            {
                "project_name": project_name,
                "project_code": project_code,
                "ticket_id": ticket_id,
                "type": "初验",
                "actual_date": info_dict.get("actual_finish_time"),
                "account": account,
                "files_json": json.dumps(files_json, ensure_ascii=False),
                "remark": info_dict.get("remark"),
            }
        )
        if insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_feedback_to_xingchen", insert_list)
            tb_db.commit()
        variables = {"insert_list": insert_list}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_po_details_into_db(
        self, project_name, project_code, info_dict, po_category_all_selected_list
    ):
        """
        初验反馈流程: po验收详情数据入库(initial_acceptance_po_details)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account = self.ctx.variables.get("PM")
        files_json = Tools.get_files_json(info_dict.get("initial_files"))
        # 初验验收详情存库(po详情)
        po_details_insert_list = []
        for item in po_category_all_selected_list:
            po_details_insert_list.append(
                {
                    "project_name": project_name,
                    "project_code": project_code,
                    "ticket_id": ticket_id,
                    "code_id": item.get("code_id"),
                    "order_code": item.get("po_code"),
                    # "material_id": item.get("material_id"),
                    # "material_category_name": item.get("material_category_name"),
                    "category_name": item.get("category_name"),
                    # "order_item_id": item.get("order_item_id"),
                    # "order_amount": item.get("order_amount"),
                    "actual_date": info_dict.get("actual_finish_time"),
                    "account": account,
                    "files_json": json.dumps(files_json, ensure_ascii=False),
                    "remark": info_dict.get("remark"),
                    # "acceptance_flag": (
                    #     item.get("selected")[0] if item.get("selected") else None
                    # ),
                }
            )
        if po_details_insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch(
                "initial_acceptance_category_details", po_details_insert_list
            )
            tb_db.commit()
        variables = {"po_details_insert_list": po_details_insert_list}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def initial_acceptance_status_into_db(self, project_name, info_dict):
        """
        取消周报日报推送（更新week_create_info、daily_create_info状态status为1）
        更新summary_data数据（建设状态state为已交付，初验时间InspectionEndTime为实际初验时间）
        """
        # 取消周报日报推送
        create_info_update_data = {"status": 1}
        create_info_conditions = {"project_name": project_name}
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update(
            "week_create_info", create_info_update_data, create_info_conditions
        )
        tb_db.update(
            "daily_create_info", create_info_update_data, create_info_conditions
        )
        tb_db.commit()
        variables = {"create_info_update_data": create_info_update_data}
        # 更新summary_data数据
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = (
            "SELECT campus, project_name FROM summary_data "
            f"WHERE CONCAT(campus, project_name) = '{project_name}'"
        )
        result = db.get_all(query_sql)
        if result:
            summary_data_update_data = {
                "state": "已交付",
                "InspectionEndTime": info_dict.get("actual_finish_time"),
            }
            summary_data_conditions = {
                "campus": result[0].get("campus"),
                "project_name": result[0].get("project_name"),
            }
            tb_db.update(
                "summary_data", summary_data_update_data, summary_data_conditions
            )
            variables["summary_data_update_data"] = create_info_update_data
            variables["summary_data_conditions"] = summary_data_conditions
        flow.complete_task(self.ctx.task_id, variables=variables)
