import ast

from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.cooperative_partner.cooperative_partner_general_method import obtain_supplier_information, \
    calling_external_interfaces


class TestVerificationWB(object):
    """
        测试验证
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(TestVerificationWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def the_test_verification_process_initiation(self, project_name):
        """
            创建工单--测试验证
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        file_list = self.ctx.variables.get("file_list")
        module_name = self.ctx.variables.get("module_name")

        # 为 file_list 中的每个字典添加 module_name
        updated_file_list = []
        if file_list:
            for file_item in file_list:
                file_item_copy = file_item.copy()  # 创建副本以避免修改原始数据
                file_item_copy['mozu_name'] = module_name
                updated_file_list.append(file_item_copy)

        the_test_verification_process_initiation_data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name,
            "FileList": updated_file_list,
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)

        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")

        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"

        # 判断并获取对应的 process_identification
        if system_type == "Dcops":
            # 获取供应商流程标识
            cooperative_partner_process_identification = config.get_config_map(
                "cooperative_partner_process_identification")
            for process in cooperative_partner_process_identification[Facilitator]:
                if process.get('process_name') == '测试验证':
                    ProcessDefinitionKey = process.get('process_identification')

        PartnerInstanceId = ''
        if system_type == "Dcops":
            data = {
                "Action": "Ticket",  # 原样填写
                "Method": "Create",  # 原样填写
                "SystemId": 13,  # (*必填)(可取值区间为[ > 0]) (要求数据类型[int])
                # 调用方系统标识
                "CorpId": corp_id,  # (*必填)
                # 服务商企业ID
                "Data": {  # 自定义流程变量
                    "CustomVariables": the_test_verification_process_initiation_data,
                    # 流程定义标识
                    "ProcessDefinitionKey": ProcessDefinitionKey,  # (*必填)
                    # 工单来源
                    "Source": f"外部系统（{Facilitator}）",
                    # 工单描述
                    "TicketDescription": f"{project_name}:第三方验证测试",  # (*必填)
                    # 工单级别(必须与SLA匹配，如果无法匹配，自动调整为1)
                    "TicketLevel": 1,  # (*必填)(要求数据类型[int])
                    # 工单标题
                    "TicketTitle": f"{project_name}:第三方验证测试",  # (*必填)
                    "UserInfo": {  # 用户信息
                        "Concern": "v_zongxiyu",  # 关注人(存在多个使用分号(;)分隔)
                        "Creator": "v_zongxiyu",  # (*必填)
                        "Deal": "v_zongxiyu"  # 处理人(存在多个使用分号(;)分隔)
                    }
                }
            }

            info = calling_external_interfaces(interface_info, data)
            self.workflow_detail.add_kv_table("建单信息",{"info": info})
            PartnerTicketId = str(info['data']['TicketId'])
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": the_test_verification_process_initiation_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/the_test_verification_process_initiation"
                }
            )

            PartnerTicketId = str(info['Data'])
            self.workflow_detail.add_kv_table("建单信息", {"info": info})
        variables = {
            'the_test_verification_process_initiation_data_info': str(info),
            'the_test_verification_process_initiation_data': the_test_verification_process_initiation_data,
            'PartnerTicketId': PartnerTicketId,
            'system_type': system_type,
            'system_id': system_id
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceCsyz(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceCsyz, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_technical_data_upload(self):
        """
            等待技术资料上传
        """
        file_list = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_id, file_name, file_type, file_source, file_url, remark " \
              "FROM test_verification_technical_data " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' "
        result_list = db.get_all(sql)
        for record in result_list:
            file_id = record.get('file_id', "")  # id
            file_name = record.get('file_name', "")  # 文件名称
            file_type = record.get('file_type', "")  # 文件类型
            file_source = record.get('file_source', "")  # 文件来源
            file_url = record.get('file_url', "")  # url
            remark = record.get('remark', "")  # 备注

            # # 尝试将 file_url 字符串解析为列表
            # try:
            #     file_urls = ast.literal_eval(file_url)  # 将字符串解析为列表
            # except (ValueError, SyntaxError):
            #     file_urls = []  # 如果解析失败，设置为空列表

            # file_url_list = []
            # # 确保 file_urls 是一个列表
            # if isinstance(file_urls, list):
            #     # 为每个 URL 创建一个新的字典
            #     for url in file_urls:
            #         file_url_list.append({"file_url": url})

            file_url_list = [{"file_url": file_url}]

            file_list.append({
                'id': file_id,
                'file_name': file_name,
                'file_type': file_type,
                'file_source': file_source,
                'file_url': file_url_list,
                'remark': remark
            })

        self.workflow_detail.add_kv_table('技术资料', {'message': result_list})
        if result_list:
            variables = {
                "jszl_result_list": result_list,
                "file_list": file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceCsyz(object):
    """
        测试验证
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceCsyz, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def technical_data_upload(self, TencentTicketId, ProjectName, FileList, CompleteOrNot):
        """
            技术资料上传
        """
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}

        if not CompleteOrNot:
            data = []
            for row in FileList:
                id = row.get('Id')
                file_url = row.get('FileUrl')
                remark = row.get('Remark')

                result = ";".join(file_url)

                data.append({
                    'id': id,
                    'file_url': result,
                    'remark': remark,
                })
            gnetops.request(
                action="Nbroker",
                method="UrlProxy",
                ext_data={
                    "NbrokerData": {
                        "context": {
                            "service_name": "TestFileUpdate",
                            "method_name": "test_file_Update"
                        },
                        "args": {
                            "file_list": data
                        },
                    },
                    "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                }
            )
        elif CompleteOrNot:
            data = []
            insert_data = []
            for row in FileList:
                id = row.get("Id")
                mozu_name = row.get("MozuName")
                file_name = row.get("FileName")
                file_source = row.get("FileSource")
                file_type = row.get("FileType")
                file_url = row.get("FileUrl")
                remark = row.get("Remark")

                insert_data.append({
                    'ticket_id': TencentTicketId,
                    'project_name': ProjectName,
                    'file_id': id,
                    'mozu_name': mozu_name,
                    'file_name': file_name,
                    'file_source': file_source,
                    'file_type': file_type,
                    'file_url': file_url,
                    'remark': remark
                })

                result = ";".join(file_url)

                data.append({
                    'id': id,
                    'file_url': result,
                    'remark': remark
                })
            gnetops.request(
                action="Nbroker",
                method="UrlProxy",
                ext_data={
                    "NbrokerData": {
                        "context": {
                            "service_name": "TestFileUpdate",
                            "method_name": "test_file_Update"
                        },
                        "args": {
                            "file_list": data
                        },
                    },
                    "ServiceUrl": "http://ibroker-youhua:8080/nBroker/api/v1/task",
                }
            )
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            if insert_data:
                tb_db.insert_batch("test_verification_technical_data", insert_data)
            tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class JszlSupervisionExaminationApproval(AjaxTodoBase):
    """
        技术资料-监理审批
    """

    def __init__(self):
        super(JszlSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jszl_supervision_approval = process_data.get('jszl_supervision_approval')
        jszl_supervision_remark = process_data.get('jszl_supervision_remark')
        system_type = self.ctx.variables.get('system_type')
        system_id = self.ctx.variables.get('system_id')
        info = {}
        if jszl_supervision_approval == '驳回':
            query_sql = f"DELETE FROM test_verification_technical_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 1

            technical_data_approval_results_data = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": jszl_supervision_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "technical_data_approval_results",  # (*必填) 云函数名称
                            "data": technical_data_approval_results_data,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                if Facilitator == "中兴":
                    data["NbrokerData"]["args"].update({"namespace": "default2"})
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": technical_data_approval_results_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/technical_data_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'jszl_supervision_approval': jszl_supervision_approval,
                'jszl_supervision_remark': jszl_supervision_remark,
                'jszl_supervision_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif jszl_supervision_approval == '同意':
            variables = {
                'jszl_supervision_approval': jszl_supervision_approval,
                'jszl_supervision_remark': jszl_supervision_remark,
                'jszl_supervision_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class JszlItemApproval(AjaxTodoBase):
    """
        技术资料-项管审批
    """

    def __init__(self):
        super(JszlItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jszl_item_approval = process_data.get('jszl_item_approval')
        jszl_item_remark = process_data.get('jszl_item_remark')
        system_type = self.ctx.variables.get('system_type')
        system_id = self.ctx.variables.get('system_id')
        info = {}
        if jszl_item_approval == '驳回':
            query_sql = f"DELETE FROM test_verification_technical_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 2
            technical_data_approval_results_data = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、item；4、无
                "Remark": jszl_item_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "technical_data_approval_results",  # (*必填) 云函数名称
                            "data": technical_data_approval_results_data,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                if Facilitator == "中兴":
                    data["NbrokerData"]["args"].update({"namespace": "default2"})
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": technical_data_approval_results_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/technical_data_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'jszl_item_approval': jszl_item_approval,
                'jszl_item_remark': jszl_item_remark,
                'jszl_item_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif jszl_item_approval == '同意':
            variables = {
                'jszl_item_approval': jszl_item_approval,
                'jszl_item_remark': jszl_item_remark,
                'jszl_item_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class JszlPMApproval(AjaxTodoBase):
    """
        技术资料-PM审批
    """

    def __init__(self):
        super(JszlPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jszl_PM_approval = process_data.get('jszl_PM_approval')
        jszl_PM_remark = process_data.get('jszl_PM_remark')
        system_type = self.ctx.variables.get('system_type')
        system_id = self.ctx.variables.get('system_id')
        info = {}
        if jszl_PM_approval == '驳回':
            query_sql = f"DELETE FROM test_verification_technical_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            examine_approve = 2
            role = 3

            technical_data_approval_results_data = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": jszl_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "technical_data_approval_results",  # (*必填) 云函数名称
                            "data": technical_data_approval_results_data,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                if Facilitator == "中兴":
                    data["NbrokerData"]["args"].update({"namespace": "default2"})
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": technical_data_approval_results_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/technical_data_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'jszl_PM_approval': jszl_PM_approval,
                'jszl_PM_remark': jszl_PM_remark,
                'jszl_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif jszl_PM_approval == '同意':
            if not jszl_PM_remark:
                jszl_PM_remark = '无'
            examine_approve = 1
            role = 4
            technical_data_approval_results_data = {
                "ProjectName": project_name,  # 项目名称
                "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
                "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
                "Remark": jszl_PM_remark,  # 备注
                "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
            }
            if system_type == "Dcops":
                # 此节点再七彩石配置中的key
                interface_info = 'create_order'
                # 构造请求数据
                data = {
                    "SystemId": "13",
                    "Action": "Nbroker",
                    "Method": "UrlProxy",
                    "NbrokerData": {
                        "context": {
                            "service_name": "TCForward",  # (*必填) 原样填写
                            "method_name": "index"  # (*必填) 原样填写
                        },
                        "args": {
                            "function_name": "technical_data_approval_results",  # (*必填) 云函数名称
                            "data": technical_data_approval_results_data,  # 传递给云函数的入参
                            "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                        }
                    },
                    "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
                }
                if Facilitator == "中兴":
                    data["NbrokerData"]["args"].update({"namespace": "default2"})
                info = calling_external_interfaces(interface_info, data)

                result_str = info['data']

                # 获取 code 的值
                code = result_str.get('code')
                message = result_str.get('msg')
                if code != 0:
                    raise Exception(f"报错信息为： {message}")
            elif system_type == "外部":
                info = gnetops.request(
                    action='Request',
                    method='RequestToFacilitator',
                    ext_data={
                        "Facilitator": Facilitator,
                        "ReqData": technical_data_approval_results_data,
                        "RequestMethod": "POST",
                        "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/technical_data_approval_results"
                    }
                )
                if info.get('ErrorCode') == -1:
                    return {'code': -1, 'msg': f'{info.get("Message")}'}

            variables = {
                'jszl_PM_approval': jszl_PM_approval,
                'jszl_PM_remark': jszl_PM_remark,
                'jszl_info': info
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
