import uuid
from venv import logger

from iBroker.lib import config, mysql
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.notification.chatops import Chat<PERSON><PERSON>Send
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib
from iBroker.sdk.notification.chatops import ChatopsSendFile

# 日报项管填写
class DailyReportSend(AjaxTodoBase):
    def __init__(self):
        super(DailyReportSend, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fileUrl = process_data.get("fileUrl")
        variables = {
            "fileUrl": fileUrl,
        }
        ChatOpsSend("ww138719774037141", fileUrl, "text", "group").call()
        # new_file_name = str(uuid.uuid4()) + ".png"
        # COSLib.download_2(file_name=fileUrl, save_to_file=new_file_name)
        # ChatopsSendFile(new_file_name, "ww138719774037141", "group")

        # tof.send_email(
        #     sendTitle="每日进展报告下载连接",
        #     msgContent=fileUrl,
        #     sendTo=[process_user],
        # )
        flow.complete_task(self.ctx.task_id, variables=variables)