import json
from datetime import datetime

from iBroker.lib import mysql, curl
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue


def get_filename_and_url(file_list):
    new_list = []
    try:
        for file in file_list:
            file_name = file.get("name")
            file_url = file["response"]["FileList"][0]["url"]
            new_list.append({'file_name': file_name, 'file_url': file_url})
    except KeyError:
        return 1
    except IndexError:
        return 1
    return new_list


class ConstructionMaintenanceInitiate(object):
    """
        建设接维平台对接:发起类
    """

    def __init__(self):
        super(ConstructionMaintenanceInitiate, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_review_file_upload_people_info(self, project_name):
        """
        获取复盘文件上传及审核处理人
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT account, role FROM project_role_account " \
              "WHERE project_name = '%s' " \
              "AND role IN ('集成商-项目经理','监理-总监理工程师', '项管-项目经理') " \
              "AND del_flag = '0'" % project_name
        sql_2 = f"SELECT PM FROM risk_early_warning_data WHERE project_name='{project_name}'"
        people_list = db.get_all(sql)
        PM = db.get_all(sql_2)[0].get("PM")
        if people_list:
            jcs = ""
            xg = ""
            jl = ""
            for i in people_list:
                if i.get("role") == "集成商-项目经理":
                    jcs = i.get("account")
                if i.get("role") == "项管-项目经理":
                    xg = i.get("account")
                if i.get("role") == "监理-总监理工程师":
                    jl = i.get("account")
        else:
            return {"code": -1, "msg": "人员信息未录入"}
        variables = {
            "jcs": jcs,
            "xg": xg,
            "PM": PM,
            "jl": jl,
            "jcs_reject": "否",
            "jl_reject": "否",
            "xg_reject": "否",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def review_file_save(self):
        """
        复盘文件存库
        """
        ticket_id = self.ctx.variables.get('ticket_id')
        project_name = self.ctx.variables.get('project_name', "")
        xg_file_upload_list = self.ctx.variables.get('xg_file_upload_list', [])
        jcs_file_upload_list = self.ctx.variables.get('jcs_file_upload_list', [])
        xg_file_remark = self.ctx.variables.get('xg_file_remark', "")
        jcs_file_remark = self.ctx.variables.get('jcs_file_remark', "")
        jl_file_upload_list = self.ctx.variables.get('jl_file_upload_list', [])
        jl_file_remark = self.ctx.variables.get('jl_file_remark', "")
        PM_file = self.ctx.variables.get('PM_file', "")
        PM_remark = self.ctx.variables.get('PM_remark', "")
        jl = self.ctx.variables.get('jl', "")
        xg = self.ctx.variables.get('xg', "")
        jcs = self.ctx.variables.get('jcs', "")
        pm = self.ctx.variables.get('PM', "")
        db = mysql.new_mysql_instance('tbconstruct')
        xg_insert_dict = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "url": json.dumps(xg_file_upload_list, ensure_ascii=False),
            "file_remark": xg_file_remark,
            "uploader": xg,
            "node": "复盘",
            "file_type": "项管"
        }
        jcs_insert_dict = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "url": json.dumps(jcs_file_upload_list, ensure_ascii=False),
            "file_remark": jcs_file_remark,
            "uploader": jcs,
            "node": "复盘",
            "file_type": "集成商"
        }
        jl_insert_dict = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "url": json.dumps(jl_file_upload_list, ensure_ascii=False),
            "file_remark": jl_file_remark,
            "uploader": jl,
            "node": "复盘",
            "file_type": "监理"
        }
        if PM_file:
            PM_insert_dict = {
                "ticket_id": ticket_id,
                "project_name": project_name,
                "url": json.dumps(PM_file, ensure_ascii=False),
                "file_remark": PM_remark,
                "uploader": pm,
                "node": "复盘",
                "file_type": "项管"
            }
            db.insert("maintenance_upload_data", PM_insert_dict)
        db.insert("maintenance_upload_data", xg_insert_dict)
        db.insert("maintenance_upload_data", jcs_insert_dict)
        db.insert("maintenance_upload_data", jl_insert_dict)
        variables = {}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_review_file_upload_ticket(self, project_name):
        """
        初验完成启复盘文件上传子单
        """
        ticket_id = self.ctx.variables.get('ticket_id', "")
        initial_date_time = self.ctx.variables.get('initial_date_time', "")
        if not initial_date_time:
            initial_date_time = self.ctx.variables.get('final_date_time', "")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT campus,project FROM risk_early_warning_data WHERE project_name='{project_name}'"
        project_info = tb_db.get_row(sql)
        if project_info:
            campus = project_info.get("campus", '')
            project = project_info.get("project", '')
            if campus and project:
                sql_2 = (f"SELECT delivery_rack,rack_constructing FROM summary_data "
                         f"WHERE campus='{campus}' AND project_name='{project}'")
                number_of_rack_dict = tb_db.get_row(sql_2)
                # 默认值设为 0（字符串或整数视情况）
                number_of_rack = "0"  # 或者 0，取决于后续使用方式

                if number_of_rack_dict:
                    # 先尝试取 rack_constructing，如果不存在则取 delivery_rack
                    number_of_rack = number_of_rack_dict.get("rack_constructing",
                                                             number_of_rack_dict.get("delivery_rack", "0"))
                    response_data = gnetops.request(
                        action="Project",
                        method="UpdateSummaryData",
                        ext_data={
                            "campus": campus,
                            "project_name": project,
                            "ticket_id": ticket_id,
                            "update_filed": "rack_finish",
                            "update_value": str(number_of_rack),
                        },
                        scheme="ifob-infrastructure",
                    )

                response_data = gnetops.request(
                    action="Project",
                    method="UpdateSummaryData",
                    ext_data={
                        "campus": campus,
                        "project_name": project,
                        "ticket_id": ticket_id,
                        "update_filed": "InspectionEndTime",
                        "update_value": initial_date_time,
                    },
                    scheme="ifob-infrastructure",
                )
        ticket_info = gnetops.create_ticket(
            flow_key="construct_review_file_upload",
            description='该工单为："' + project_name + '复盘文件上传',
            ticket_level=3,
            title=project_name + '：复盘文件上传',
            creator="v_zongxiyu",
            concern="v_zongxiyu",  # 关注人, 这里暂为空
            deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var={
                "project_name": project_name,
            }
        )
        variables = {"review_file_upload_ticket": ticket_info.get("TicketId")}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_file_info(self, module_name, project_name):
        """
        获取资料
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name,url,file_type,is_need FROM maintenance_upload_data " \
              " WHERE project_name='%s'" % project_name
        query = db.get_all(sql)
        file_list = []
        replenish_file_list = []
        if query:
            for i in query:
                if i.get("url"):
                    url = json.loads(i.get("url"))
                else:
                    url = ""
                if not i.get("is_need"):
                    file_list.append({
                        "file_name": i.get("file_name"),
                        "file_type": i.get("file_type"),
                        "mozu_name": module_name,
                        "url": url
                    })
                elif i.get("is_need") == "后补上传":
                    replenish_file_list.append({
                        "file_name": i.get("file_name"),
                        "file_type": i.get("file_type"),
                        "url": url
                    })

        sql_query = ("SELECT account, role FROM project_role_account "
                     "WHERE project_name = '%s' "
                     "AND role IN ('集成商-项目经理', '监理-总监理工程师', '项管-项目经理', '总包-项目经理', '合建方-项目经理') "
                     "AND del_flag = '0'") % project_name
        jg_sql = "SELECT architecture,PM FROM project_team_information_data " \
                 " WHERE project_name='%s'" % project_name
        people_list_1 = db.get_all(sql_query)
        jl = ""
        jcs = ""
        xg = ""
        if people_list_1:
            for i in people_list_1:
                if i.get("role") == "监理-总监理工程师":
                    jl = i.get("account")
                if i.get("role") == "集成商-项目经理":
                    jcs = i.get("account")
                if i.get("role") == "项管-项目经理":
                    xg = i.get("account")
                if i.get("role") == "总包-项目经理":
                    jcs = i.get("account")
                if i.get("role") == "合建方-项目经理":
                    xg = i.get("account")
        else:
            return {"code": -1, "msg": "人员信息未录入"}
        people_list_2 = db.get_all(jg_sql)
        if people_list_2:
            pm = people_list_2[0].get("PM")
            jg = people_list_2[0].get("architecture")
        else:
            return {"code": -1, "msg": "人员信息未录入"}
        jg_file_list = []
        sql2 = "SELECT file_name,url,file_type,file_remark FROM maintenance_upload_data " \
               " WHERE project_name='%s' and node='架构'" % project_name
        query2 = db.get_all(sql2)
        if query2:
            for file in query2:
                if file.get("url"):
                    url = json.loads(file.get("url"))
                else:
                    url = ""
                jg_file_list.append({
                    "file_name": file.get("file_name"),
                    "file_type": file.get("file_type"),
                    "url": url,
                    "file_remark": file.get("file_remark")
                })
        if not jg_file_list:
            raise Exception("未获取到深化设计文件信息")
        variables = {
            "file_result": file_list,
            "jg": jg,
            "xg": xg,
            "jl": jl,
            "pm": pm,
            "jcs": jcs,
            "jg_file_list": jg_file_list,
            "replenish_file_list": replenish_file_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def main_process(self, ticket_id, project_name):
        """
            接维主流程
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship " \
              " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(sql)
        if module_name_list:
            module_name = module_name_list[0].get('module_name')
            info = gnetops.create_ticket(
                flow_key="dc_take_over",
                description=f"{module_name}接维单",
                ticket_level="3",
                title=f"{module_name}接维单",
                creator="lichfang",
                concern="",
                deal="",
                source="",
                custom_var={"mozu_name": module_name}
            )
            ticket_info = info.get('TicketId')
            if str(ticket_info).isdigit():
                # 建单成功存数据库
                insert_data = {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "module_name": module_name,
                    "ticket_info": str(ticket_info)
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("maintenance_main_process", insert_data)
                tb_db.commit()
                variables = {
                    "module_name": module_name,
                    "ticket_info": str(ticket_info),
                    'main_process': str(info)
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
            else:
                self.workflow_detail.add_kv_table('建单失败', {"data": str(info)})
        else:
            self.workflow_detail.add_kv_table('获取模组名称失败', {"data": module_name_list})

    def joint_main_process(self, ticket_id, project_name):
        """
            租赁项目接维主流程
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT module_name FROM project_module_mapping_relationship " \
              " WHERE project_name='%s'" % project_name
        module_name_list = db.get_all(sql)
        if module_name_list:
            module_name = module_name_list[0].get('module_name')
            info = gnetops.create_ticket(
                flow_key="jie_wei_rental",
                description=f"{module_name}接维单",
                ticket_level="3",
                title=f"{module_name}接维单",
                creator="lichfang",
                concern="",
                deal="",
                source="",
                custom_var={"mozu_name": module_name}
            )
            ticket_info = info.get('TicketId')
            if str(ticket_info).isdigit():
                # 建单成功存数据库
                insert_data = {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "module_name": module_name,
                    "ticket_info": str(ticket_info)
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert("maintenance_main_process", insert_data)
                tb_db.commit()
                variables = {
                    "module_name": module_name,
                    "ticket_info": str(ticket_info),
                    'main_process': str(info)
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
            else:
                self.workflow_detail.add_kv_table('建单失败', {"data": str(info)})
                raise Exception(f'建单失败,报错为 {str(info)}')
        else:
            self.workflow_detail.add_kv_table('获取模组名称失败', {"data": module_name_list})
            raise Exception('获取模组名称失败')

    def creat_maintenance_data_upload(self, project_name):
        f_instance_id = self.ctx.instance_id
        variables_map = {
            'f_instance_id': f_instance_id,
            'project_name': project_name,
        }
        ticket_info = gnetops.create_ticket(
            flow_key="upload_process_of_maintenance_data",
            description='该工单为："' + project_name + '的接维资料上传"服务',
            ticket_level=3,
            title=project_name + ':接维资料上传',
            creator="v_zongxiyu",
            concern="",  # 关注人, 这里暂为空
            deal="v_zongxiyu",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var=variables_map  # 自定义流程变量，抛给派单子流程
        )
        upload_ticket_id = [ticket_info.get('TicketId')]
        variables = {
            'f_instance_id': f_instance_id,
            'upload_ticket_id': upload_ticket_id
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def waiting_maintenance_data_upload(self):
        upload_ticket_id = self.ctx.variables.get('upload_ticket_id')
        data = {
            "ResultColumns": {
                "CreateTime": "",
                "EndTime": "",
                "InstanceId": "",
                # 是否强制结单
                "IsForceEnd": "",
                # 流程定义标识
                "ProcessDefinitionKey": "",
                "ServiceRecoverTime": "",
                "StartProcessTime": "",
                # 单据状态[OPEN:运行中;END:已结束]
                "TicketStatus": "",
                "Title": "",
                "CustomRequestVarKV": "",
            },
            "SearchCondition": {'TicketId': upload_ticket_id}
        }
        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']
        cnt = 0  # 用于记录有多少个工单正在运行中
        for item in res_list:
            if item.get('TicketStatus') == 'OPEN':
                cnt += 1
        if cnt:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}

    def data_acceptance_initiate(self, ticket_info, file_result):
        """
            资料验收发起CheckAndAcceptData-Update
        """
        info = gnetops.request(action="CheckAndAcceptData",
                               method="Update",
                               ext_data={
                                   "FileList": file_result,
                                   "TicketId": ticket_info
                               })
        variables = {
            'data_acceptance': str(info),
            "final_file_list": []
        }
        self.workflow_detail.add_kv_table('发起结果', {"data": str(info)})
        if '成功' in str(info):
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def material_acceptance_initiate(self, material_file_list, ticket_info):
        """
            物资验收发起
        """
        info_list = []
        for info in material_file_list:
            info_list.append({
                "mess_name": info.get("material_name"),
                "mess_count": info.get("material_quantity"),
                "mess_url": info.get("upload_materials")
            })
        info = gnetops.request(action="CheckAndAcceptSupplies",
                               method="Update",
                               ext_data={
                                   "MassList": material_file_list,
                                   "TicketId": ticket_info
                               })
        variables = {
            'material_acceptance': str(info)
        }
        self.workflow_detail.add_kv_table('发起结果', {"data": str(info)})
        if '成功' in str(info):
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def training_initiate(self, ticket_info, training_file_list):
        """
            培训实施发起
        """
        data = {

        }
        for i in training_file_list:
            data["training_name"] = i.get("training_name")
        info = gnetops.request(action="CheckAndAcceptTraining",
                               method="Update",
                               ext_data={
                                   "TicketId": ticket_info,
                                   "TrainingList": training_file_list
                               })
        variables = {
            'training_initiate': str(info)
        }
        self.workflow_detail.add_kv_table('发起结果', {"data": str(info)})
        if '成功' in str(info):
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def initial_opinion_initiate(self, ticket_info, module_name):
        """
            初验发起
        """
        now = datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        info = gnetops.request(action="CheckAndAcceptWaitInitial",
                               method="Update",
                               data={
                                   "TicketId": ticket_info,  # 例
                                   "MozuName": module_name,
                                   "AcceptanceType": "初验",
                                   "AcceptanceOpinion": "初验发起",
                                   "AcceptanceDate": current_date
                               })
        variables = {
            'initiate_acceptance': str(info)
        }
        self.workflow_detail.add_kv_table('发起结果', {"data": str(info)})
        if '成功' in str(info):
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def final_opinion_initiate(self, ticket_info, module_name, final_file_list):
        """
            终验发起
        """
        final_file_list_send = []
        if final_file_list:
            for file in final_file_list:
                final_file_list_send.append({
                    "file_name": file.get("file_name"),
                    "file_type": file.get("file_type"),
                    "mozu_name": module_name,
                    "url": file.get("url")
                })
        now = datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        info = gnetops.request(action="CheckAndAcceptInitial",
                               method="Update",
                               scheme="ifob-fre",
                               data={
                                   "TicketId": ticket_info,  # 例
                                   "MozuName": module_name,
                                   "AcceptanceType": "终验",
                                   "AcceptanceOpinion": "终验发起",
                                   "AcceptanceDate": current_date,
                                   "EndFileList": final_file_list_send
                               })
        self.workflow_detail.add_kv_table('终验发起结果', {"data": str(info)})
        if '成功' in str(info):
            variables = {
                'final_acceptance': str(info)
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def initial_inspection_report_feedback(self, ticket_info, initial_report):
        """
            初验报告传递
        """
        now = datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        info = gnetops.request(action="CheckAndAcceptWaitSupplies",
                               method="Update",
                               ext_data={
                                   "TicketId": ticket_info,
                                   "ReportList": initial_report
                               },
                               scheme="ifob-fre")
        variables = {
            'initiate_acceptance': str(info)
        }
        self.workflow_detail.add_kv_table('发起结果', {"data": str(info)})
        if '成功' in str(info):
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table('发起失败', {"data": str(info)})

    def create_review_file_upload_ticket_list(self, project_name_list):
        """
        复盘文件批量起单接口
        """
        review_file_upload_ticket = []
        for item in project_name_list:
            ticket_info = gnetops.create_ticket(
                flow_key="construct_review_file_upload",
                description='该工单为："' + item + '复盘文件上传',
                ticket_level=3,
                title=item + '：复盘文件上传',
                creator="v_zongxiyu",
                concern="v_zongxiyu",  # 关注人, 这里暂为空
                deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var={
                    "project_name": item,
                }
            )
            review_file_upload_ticket.append(ticket_info.get("TicketId"))
        variables = {
            'review_file_upload_ticket': review_file_upload_ticket
        }
        return variables


class ConstructionMaintenanceProcess(object):
    """
        建设接维平台对接:接口类
    """

    def __init__(self):
        super(ConstructionMaintenanceProcess, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_project_name(self, module_name):
        project_name = ''
        ticket_id = ''
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT ticket_id,project_name FROM maintenance_main_process " \
              " WHERE module_name='%s'" % module_name
        project_name_list = db.get_all(sql)
        if project_name_list:
            for i in project_name_list:
                project_name = i.get('project_name')
                ticket_id = i.get('ticket_id')

        return {"project_name": project_name, "ticket_id": ticket_id}

    def base_opinion(self, mozu_name, acceptance_type, acceptance_opinion, acceptance_opinion_description, type_str):
        info = self.get_project_name(mozu_name)
        project_name = info.get("project_name")
        ticket_id = info.get("ticket_id")
        insert_data = {
            "project_name": project_name,
            "ticket_id": ticket_id,
            'mozu_name': mozu_name,
            'acceptance_type': acceptance_type,
            'acceptance_opinion': acceptance_opinion,
            'acceptance_opinion_description': acceptance_opinion_description,
            'type': type_str
        }
        # 存数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("maintenance_acceptance", insert_data)
        tb_db.commit()
        return 0

    def data_acceptance_feedback(self, mozu_name, data_acceptance_status):
        """
            资料验收情况反馈接口
        """
        info = self.get_project_name(mozu_name)
        project_name = info.get("project_name")
        ticket_id = info.get("ticket_id")

        if data_acceptance_status and mozu_name:
            insert_list = []
            for data in data_acceptance_status:
                url = data.get('url')
                if url:
                    url = json.dumps(url, ensure_ascii=False)
                else:
                    url = '[]'
                insert_list.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'mozu_name': mozu_name,
                    'file_name': data.get('file_name'),
                    'file_id': data.get('file_id'),
                    'file_type': data.get('file_type'),
                    'url': url,
                    'status': data.get('status'),
                    'comments': data.get('comments'),
                    "attribute": data.get('attribute')
                })
            if insert_list:
                # 存数据库
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("maintenance_data_acceptance", insert_list)
                tb_db.commit()
                return {'code': 200, 'msg': '成功'}
            else:
                return {'code': -1, 'msg': '数据错误'}
        else:
            return {'code': -1, 'msg': '缺少参数'}

    def material_acceptance_feedback(self, mozu_name, material_acceptance_status):
        """
            物资验收情况反馈接口
        """
        info = self.get_project_name(mozu_name)
        project_name = info.get("project_name")
        ticket_id = info.get("ticket_id")

        if material_acceptance_status and mozu_name:
            for material in material_acceptance_status:
                con = {
                    'mozu_name': mozu_name,
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'material_name': material.get('mess_name'),
                }
                data = {
                    'material_quantity': material.get('mess_count'),
                    'conclusion': material.get('conclusion'),
                    'illustrate': material.get('illustrate'),
                    'state': 1
                }
                # 存数据库
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("maintenance_material_acceptance", data, con)
                tb_db.commit()
            return {'code': 200, 'msg': '成功'}
        else:
            return {'code': -1, 'msg': '缺少参数'}

    def training_feedback(self, mozu_name, training_list):
        """
            培训实施情况反馈接口
        """
        info = self.get_project_name(mozu_name)
        project_name = info.get("project_name")
        ticket_id = info.get("ticket_id")
        if mozu_name and training_list:
            for training in training_list:
                con = {
                    'mozu_name': mozu_name,
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'training_name': training.get('training_name'),
                }
                actual_completion_time = training.get('actual_completion_time')
                completion = training.get('completion')
                data = {
                    'actual_completion_time': actual_completion_time,
                    'completion': completion,
                    'state': 1
                }
                # 存数据库
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("maintenance_training", data, con)
                tb_db.commit()
            return {'code': 200, 'msg': '成功'}
        else:
            return {'code': -1, 'msg': '缺少参数'}

    def on_site_inspection_feedback(self, mozu_name, result):
        """
            现场现场查验情况反馈接口
        """
        info = self.get_project_name(mozu_name)
        project_name = info.get("project_name")
        ticket_id = info.get("ticket_id")
        if mozu_name and result:
            data = {
                'mozu_name': mozu_name,
                'ticket_id': ticket_id,
                'project_name': project_name,
                'result': result
            }
            # 存数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("maintenance_inspection", data)
            tb_db.commit()

        return {'code': 200, 'msg': '成功'}

    def initial_opinion_feedback(self, mozu_name, acceptance_type, acceptance_opinion, acceptance_opinion_description):
        """
            初验意见反馈接口
        """
        if mozu_name and acceptance_type and acceptance_opinion and acceptance_opinion_description:
            type_str = '初验'
            info = self.base_opinion(mozu_name, acceptance_type, acceptance_opinion, acceptance_opinion_description,
                                     type_str)
            if info == 0:
                return {'code': 200, 'msg': '成功'}
            else:
                return {'code': -1, 'msg': '存库异常'}
        else:
            return {'code': -1, 'msg': '缺少参数'}

    def final_opinion_feedback(self, mozu_name, acceptance_type, acceptance_opinion, acceptance_opinion_description):
        """
            终验意见反馈接口
        """
        if mozu_name and acceptance_type and acceptance_opinion and acceptance_opinion_description:
            type_str = '终验'
            info = self.base_opinion(mozu_name, acceptance_type, acceptance_opinion, acceptance_opinion_description,
                                     type_str)
            if info == 0:
                return {'code': 200, 'msg': '成功'}
            else:
                return {'code': -1, 'msg': '存库异常'}
        else:
            return {'code': -1, 'msg': '缺少参数'}


class ConstructionMaintenanceCycle(object):
    """
        建设接维平台对接:循环任务类
    """

    def __init__(self):
        super(ConstructionMaintenanceCycle, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_acceptance_cycle(self):
        """
            资料验收反馈情况
        """
        # 获取流程变量中架构文件数据与此数据拼接
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name,file_type,url,status,comments,attribute FROM maintenance_data_acceptance " \
              f"WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('资料查验数据查询信息', {'message': result_list})
        if result_list:
            for result in result_list:
                url = result.get('url')
                if url and url != 'None' and url != '':
                    url_str = url.replace("'", '"')
                    result['url'] = json.loads(url_str)
            variables = {
                "data_feedback": result_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def material_acceptance_cycle(self):
        """
            物资验收反馈情况
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT material_name,material_quantity,conclusion,illustrate,state,upload_materials " \
              f"FROM maintenance_material_acceptance WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'"
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('物资数据查询信息', {'message': result_list})
        if result_list:
            num = 0
            for result in result_list:
                upload_materials = result.get('upload_materials')
                if upload_materials and upload_materials != 'None' and upload_materials != '':
                    upload_materials_str = upload_materials.replace("'", '"')
                    result['upload_materials'] = json.loads(upload_materials_str)
                if result.get('state') == 1:
                    num += 1
            if num == len(result_list):
                variables = {
                    "material_feedback": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def training_cycle(self):
        """
            培训实施反馈情况
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT training_name,plan_time,factory,upload_training,actual_completion_time,completion,state  " \
              f"FROM maintenance_training WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'"
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('培训数据查询信息', {'message': result_list})
        if result_list:
            num = 0
            for result in result_list:
                upload_training = result.get('upload_training')
                if upload_training and upload_training != 'None' and upload_training != '':
                    upload_training = upload_training.replace("'", '"')
                    result['upload_training'] = json.loads(upload_training)
                if result.get('state') == 1:
                    num += 1
            if num == len(result_list):
                variables = {
                    "training_feedback": result_list
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def on_site_inspection_cycle(self):
        """
            现场查验反馈情况
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT result FROM maintenance_inspection " \
              f" WHERE ticket_id = '{ticket_id}'and project_name = '{project_name}'"
        result_list = db.get_all(sql)
        self.workflow_detail.add_kv_table('现场查验数据查询信息', {'message': result_list})
        if result_list:
            result = result_list[0].get('result')
            variables = {
                "inspection_feedback": result
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def initial_opinion_cycle(self):
        """
            初验意见反馈情况
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT mozu_name,acceptance_opinion FROM maintenance_acceptance " \
              f"WHERE ticket_id = '{ticket_id}'and type = '初验' and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if result_list:
            if result_list[0].get('acceptance_opinion'):
                flow.complete_task(self.ctx.task_id)
                return {"success": True, "data": "流程已结束"}

    def final_opinion_cycle(self):
        """
            终验意见反馈情况
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT mozu_name,acceptance_opinion FROM maintenance_acceptance " \
              f"WHERE ticket_id = '{ticket_id}'and type = '终验' and project_name = '{project_name}' "
        result_list = db.get_all(sql)
        if result_list:
            if result_list[0].get('acceptance_opinion'):
                flow.complete_task(self.ctx.task_id)
                return {"success": True, "data": "流程已结束"}


class ConstructionMaintenanceArchitectureUpload(AjaxTodoBase):
    """
            建设接维平台对接：架构方资料上传
        """

    def __init__(self):
        super(ConstructionMaintenanceArchitectureUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jg_file_list = process_data.get('jg_file_list')
        file_result = self.ctx.variables.get("file_result")
        jg = self.ctx.variables.get("jg")
        module_name = self.ctx.variables.get("module_name")
        # 获取当前日期和时间
        now = datetime.now()
        # 格式化输出当前年月日
        current_year_month_day = now.strftime("%Y-%m-%d")

        file_list = []
        for file in jg_file_list:
            url = file.get("url")
            file_type = file.get("file_type")
            file_name = file.get("file_name")
            if url:
                file_result.append({
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": url,
                    "mozu_name": module_name
                })
                file_list.append({
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": json.dumps(url, ensure_ascii=False),
                    "uploader": jg,
                    "upload_time": current_year_month_day,
                    "node": "架构"
                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("maintenance_upload_data", file_list)
        tb_db.commit()

        variables = {
            'jg_file_list': jg_file_list,
            'file_result': file_result
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceArchitectureDesignReview(AjaxTodoBase):
    """
    Main class:设计PM审核
    """

    def __init__(self):
        super(ConstructionMaintenanceArchitectureDesignReview, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        design_approval = process_data.get('design_approval')
        design_remark = process_data.get('design_remark')
        design_rejection = process_data.get('design_rejection')
        if design_approval:
            if design_approval == '驳回':
                if design_rejection:
                    variables = {
                        'design_approval': design_approval,
                        'design_remark': design_remark,
                        'design_rejection': design_rejection
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    'design_approval': design_approval,
                    'design_remark': design_remark,
                }
        else:
            return {"code": -1, "msg": "请选择审核结果"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DesignPMFileRetransmission(AjaxTodoBase):
    """
        设计PM文件重新传输
    """

    def __init__(self):
        super(DesignPMFileRetransmission, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        jg_file_list = process_data.get("jg_file_list")
        file_result = self.ctx.variables.get("file_result")
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        for item in jg_file_list:
            conditions = {"project_name": project_name, "file_name": item.get("file_name")}
            db.update("maintenance_upload_data", {"url": json.dumps(item.get("url"), ensure_ascii=False)}, conditions)
        db.commit()
        for file in file_result:
            for item in jg_file_list:
                if item.get("file_name") == file.get("file_name"):
                    file["url"] = item.get("url")
        variables = {
            'jg_file_list': jg_file_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceTrainingUpload(AjaxTodoBase):
    """
        建设接维平台对接：培训资料上传
    """

    def __init__(self):
        super(ConstructionMaintenanceTrainingUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        training_file_list = process_data.get("training_file_list")
        variables = {
            'training_file_list': training_file_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceMaterialUpload(AjaxTodoBase):
    """
        建设接维平台对接：物资信息上传
    """

    def __init__(self):
        super(ConstructionMaintenanceMaterialUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        material_file_list = process_data.get("material_file_list")
        variables = {
            'material_file_list': material_file_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class Final_Inspection_Data_Upload(AjaxTodoBase):
    """
        建设接维平台对接：终验资料上传
    """

    def __init__(self):
        super(Final_Inspection_Data_Upload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        final_file_list = process_data.get("final_file_list")
        for file in final_file_list:
            url = file.get("url")
            if url:
                file["ticket_id"] = ticket_id
                file["project_name"] = project_name
                file["url"] = json.dumps(url, ensure_ascii=False)
                file["file_name"] = file.get("file_name")
                file["file_type"] = file.get("file_type")
                file["uploader"] = file.get("uploader")
                file["upload_time"] = datetime.now().strftime('%Y-%m-%d')
                file["node"] = file.get("node")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("maintenance_upload_data", final_file_list)
        tb_db.commit()
        variables = {
            "final_file_list": final_file_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceSupervisionMain(AjaxTodoBase):
    """
        建设接维平台对接：监理审核
    """

    def __init__(self):
        super(ConstructionMaintenanceSupervisionMain, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get('logo')
        if logo == '培训验收':
            jl_training_review = process_data.get("jl_training_review")
            if not jl_training_review:
                return {"code": -1, "msg": "请选择审核结果"}
            jl_training_rejection = process_data.get("jl_training_rejection")
            if jl_training_review == '驳回':
                if jl_training_rejection:
                    variables = {
                        'jl_training_review': jl_training_review,
                        'jl_training_rejection': jl_training_rejection,
                        'xg_training_review': '',
                        'training_pm_review': ''
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    'jl_training_review': jl_training_review,
                }
        elif logo == '物资验收':
            jl_material_review = process_data.get("jl_material_review")
            if not jl_material_review:
                return {"code": -1, "msg": "请选择审核结果"}
            jl_material_rejection = process_data.get("jl_material_rejection")
            if jl_material_review == '驳回':
                if jl_material_rejection:
                    variables = {
                        'jl_material_review': jl_material_review,
                        'jl_material_rejection': jl_material_rejection,
                        'xg_material_review': '',
                        'material_pm_review': ''
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    'jl_material_review': jl_material_review,
                }
        else:
            return {"code": -1, "msg": "未标明类型"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceItemMain(AjaxTodoBase):
    """
        建设接维平台对接：项管审核
    """

    def __init__(self):
        super(ConstructionMaintenanceItemMain, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get('logo')
        if logo == '培训验收':
            xg_training_review = process_data.get("xg_training_review")
            if not xg_training_review:
                return {"code": -1, "msg": "请选择审核结果"}
            xg_training_rejection = process_data.get("xg_training_rejection")
            if xg_training_review == '驳回':
                if xg_training_rejection:
                    variables = {
                        'xg_training_review': xg_training_review,
                        'xg_training_rejection': xg_training_rejection,
                        'training_pm_review': ''
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    'xg_training_review': xg_training_review,
                }
        elif logo == '物资验收':
            xg_material_review = process_data.get("xg_material_review")
            if not xg_material_review:
                return {"code": -1, "msg": "请选择审核结果"}
            xg_material_rejection = process_data.get("xg_material_rejection")
            if xg_material_review == '驳回':
                if xg_material_rejection:
                    variables = {
                        'xg_material_review': xg_material_review,
                        'xg_material_rejection': xg_material_rejection,
                        'material_pm_review': ''
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    'xg_material_review': xg_material_review,
                }
        else:
            return {"code": -1, "msg": "未标明类型"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenancePMMain(AjaxTodoBase):
    """
        建设接维平台对接：pm验收审核
    """

    def __init__(self):
        super(ConstructionMaintenancePMMain, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get('logo')
        pm_review = process_data.get("pm_review")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name", "")
        mozu_name = self.ctx.variables.get('module_name', "")
        material_file_list = self.ctx.variables.get('material_file_list', "")
        training_file_list = self.ctx.variables.get('training_file_list', "")
        con = {
            "ticket_id": ticket_id
        }
        if logo == '资料验收':
            data_pm_review = process_data.get("data_pm_review")
            if not data_pm_review:
                return {"code": -1, "msg": "请选择审核结果"}
            data_pm_rejection = process_data.get("data_pm_rejection")
            if data_pm_review == '驳回':
                if data_pm_rejection:
                    variables = {
                        'data_pm_review': data_pm_review,
                        'data_pm_rejection': data_pm_rejection
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                variables = {
                    "data_pm_review": data_pm_review
                }
                # 存数据库
                insert = {
                    "data": data_pm_review
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("maintenance_main_process", insert, con)
                tb_db.commit()
        elif logo == '物资验收':
            material_pm_review = process_data.get("material_pm_review")
            if not material_pm_review:
                return {"code": -1, "msg": "请选择审核结果"}
            material_pm_rejection = process_data.get("material_pm_rejection")
            if material_pm_review == '驳回':
                if material_pm_rejection:
                    variables = {
                        'material_pm_review': material_pm_review,
                        'material_pm_rejection': material_pm_rejection
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                insert_list = []
                if material_file_list:
                    for i in material_file_list:
                        insert_list.append({
                            'ticket_id': ticket_id,
                            'project_name': project_name,
                            'mozu_name': mozu_name,
                            'material_name': i.get('material_name'),
                            'material_quantity': i.get('material_quantity'),
                            'upload_materials': json.dumps(i.get('upload_materials'))
                        })
                    # 存数据库
                    tb_db = mysql.new_mysql_instance("tbconstruct")
                    tb_db.begin()
                    tb_db.insert_batch("maintenance_material_acceptance", insert_list)
                    tb_db.commit()
                variables = {
                    "material_pm_review": material_pm_review
                }
                # 存数据库
                insert = {
                    "material": material_pm_review
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("maintenance_main_process", insert, con)
                tb_db.commit()
        elif logo == '现场查验验收':
            variables = {
                "on_site_pm": pm_review
            }
            # 存数据库
            insert = {
                "on_site": pm_review
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("maintenance_main_process", insert, con)
            tb_db.commit()
        elif logo == '培训验收':
            training_pm_review = process_data.get("training_pm_review")
            if not training_pm_review:
                return {"code": -1, "msg": "请选择审核结果"}
            training_pm_rejection = process_data.get("training_pm_rejection")
            if training_pm_review == '驳回':
                if training_pm_rejection:
                    variables = {
                        'training_pm_review': training_pm_review,
                        'training_pm_rejection': training_pm_rejection
                    }
                else:
                    return {"code": -1, "msg": "请填写驳回原因"}
            else:
                insert_list = []
                if training_file_list:
                    for i in training_file_list:
                        insert_list.append({
                            'ticket_id': ticket_id,
                            'project_name': project_name,
                            'mozu_name': mozu_name,
                            'training_name': i.get('training_name'),
                            'plan_time': i.get('plan_time'),
                            'factory': i.get('factory'),
                            'upload_training': json.dumps(i.get('upload_training'))
                        })
                    # 存数据库
                    tb_db = mysql.new_mysql_instance("tbconstruct")
                    tb_db.begin()
                    tb_db.insert_batch("maintenance_training", insert_list)
                    tb_db.commit()
                variables = {
                    "training_pm_review": training_pm_review
                }
                # 存数据库
                insert = {
                    "training": training_pm_review
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("maintenance_main_process", insert, con)
                tb_db.commit()
        elif logo == '初验':
            if not pm_review:
                return {"code": -1, "msg": "请选择审核结果"}
            variables = {
                "initial_opinion_pm": pm_review
            }
            # 存数据库
            insert = {
                "initial_opinion": pm_review
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("maintenance_main_process", insert, con)
            tb_db.commit()
        elif logo == '终验':
            if not pm_review:
                return {"code": -1, "msg": "请选择审核结果"}
            variables = {
                "final_conclusion_pm": pm_review
            }
            # 存数据库
            insert = {
                "final_conclusion": pm_review
            }
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("maintenance_main_process", insert, con)
            tb_db.commit()
        else:
            return {"code": -1, "msg": "未标明类型"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceInitial(AjaxTodoBase):
    """
        建设接维平台对接：初验结论
    """

    def __init__(self):
        super(ConstructionMaintenanceInitial, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        initial_date_time = process_data.get("initial_date_time")
        initial_summary = process_data.get("initial_summary")
        initial_report = process_data.get("initial_report")
        initial_other = process_data.get("initial_other")
        initial_conclusion = process_data.get("initial_conclusion")
        ticket_info = self.ctx.variables.get("ticket_info")
        module_name = self.ctx.variables.get("module_name")
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        appendix_list = []

        if initial_summary:
            try:
                for file in initial_summary:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"纪要上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"纪要上传失败:{e}"}
        if initial_report:
            try:
                for file in initial_report:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"报告上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"报告上传失败:{e}"}
        if initial_other:
            try:
                for file in initial_other:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"其他资料上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"其他资料上传失败:{e}"}

        variables = {
            "initial_date_time": initial_date_time,
            "initial_summary": initial_summary,
            "initial_report": initial_report,
            "initial_other": initial_other,
            "initial_conclusion": initial_conclusion,
            "initial_appendix_list": appendix_list
        }
        data = {
            "project_name": project_name,
            "module_name": module_name,
            "ticket_info": ticket_info,
            "ticket_id": ticket_id,
            "conclusion": initial_conclusion,
            "date_time": str(initial_date_time),
            "summary": str(initial_summary),
            "report": str(initial_report),
            "other": str(initial_other),
            "type": "初验"
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("maintenance_conclusion", data)
        tb_db.commit()
        # ticket_info = gnetops.create_ticket(
        #     flow_key="construction_initial_feedback_to_xingchen",
        #     description='该工单为："' + project_name + '接维对接初验验收状态反馈',
        #     ticket_level=3,
        #     title=project_name + '：接维对接初验验收状态反馈',
        #     creator="v_zongxiyu",
        #     concern="v_zongxiyu",  # 关注人, 这里暂为空
        #     deal="v_vikyjiang",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
        #     source="",
        #     custom_var={
        #         "project_name": project_name
        #     }
        # )
        # variables.update({"initial_test_feedback_ticket_id": [ticket_info.get("ticket_id")]})
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceFinalDocumentUpload(AjaxTodoBase):
    """
        建设接维平台对接：终验报告上传
    """

    def __init__(self):
        super(ConstructionMaintenanceFinalDocumentUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        fail_list = []
        final_file_list_save = []
        replenish_file_list_save = []
        replenish_file_list = process_data.get("replenish_file_list")
        final_file_list = process_data.get("final_file_list")
        for file in final_file_list:
            file_name = file.get("file_name")
            file_type = file.get("file_type")
            file_url = file.get("url")
            file_remark = file.get("file_remark")
            if file_url:
                for item in file_url:
                    if item["status"] == "fail":
                        fail_list.append(f"【{file_name}:{item['name']}】")
            else:
                fail_list.append(f"【{file_name}】")
            if not file_remark:
                file_remark = ""
            final_file_list_save.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                "file_type": file_type,
                "file_name": file_name,
                "url": json.dumps(file_url) if file_url else "",
                "uploader": process_user,
                "upload_time": datetime.now().strftime('%Y-%m-%d'),
                "node": "终验",
                "file_remark": file_remark
            })
        variables = {
            "final_file_list_save": final_file_list_save,
            "final_file_list": final_file_list
        }
        if replenish_file_list:
            for file in replenish_file_list:
                file_name = file.get("file_name")
                file_type = file.get("file_type")
                file_url = file.get("url")
                file_remark = file.get("file_remark")
                if file_url:
                    for item in file_url:
                        if item["status"] == "fail":
                            fail_list.append(f"【{file_name}:{item['name']}】")
                else:
                    fail_list.append(f"【{file_name}】")
                if not file_remark:
                    file_remark = ""
                replenish_file_list_save.append({
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    "file_type": file_type,
                    "file_name": file_name,
                    "url": json.dumps(file_url) if file_url else "",
                    "uploader": process_user,
                    "upload_time": datetime.now().strftime('%Y-%m-%d'),
                    "node": "集成商",
                    "file_remark": file_remark
                })
            variables.update({"replenish_file_list_save": replenish_file_list_save,
                              "replenish_file_list": replenish_file_list})
        if fail_list:
            return {"code": -1, "msg": f"{fail_list}上传失败"}
        else:
            db = mysql.new_mysql_instance("tbconstruct")
            db.begin()
            db.insert_batch("maintenance_upload_data", final_file_list_save)
            if replenish_file_list_save:
                for item in replenish_file_list_save:
                    conditions = {
                        "project_name": project_name,
                        "file_name": item["file_name"],
                        "file_type": item["file_type"]
                    }
                    db.update("maintenance_upload_data", conditions=conditions, data=item)
            db.commit()
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceFinal(AjaxTodoBase):
    """
        建设接维平台对接：终验结论
    """

    def __init__(self):
        super(ConstructionMaintenanceFinal, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        final_date_time = process_data.get("final_date_time")
        final_summary = process_data.get("final_summary")
        final_report = process_data.get("final_report")
        final_other = process_data.get("final_other")
        final_conclusion = process_data.get("final_conclusion")
        ticket_info = self.ctx.variables.get("ticket_info")
        module_name = self.ctx.variables.get("module_name")
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        appendix_list = []
        if final_summary:
            try:
                for file in final_summary:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"纪要上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"纪要上传失败:{e}"}
        if final_report:
            try:
                for file in final_report:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"报告上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"报告上传失败:{e}"}
        if final_other:
            try:
                for file in final_other:
                    file_url = file["response"]["FileList"][0]["url"]
                    appendix_list.append(file_url)
            except KeyError as e:
                return {"code": -1, "msg": f"其他资料上传上传失败:{e}"}
            except IndexError as e:
                return {"code": -1, "msg": f"其他资料上传上传失败:{e}"}

        variables = {
            "final_date_time": final_date_time,
            "final_summary": final_summary,
            "final_report": final_report,
            "final_other": final_other,
            "final_conclusion": final_conclusion,
            "final_appendix_list": appendix_list
        }
        data = {
            "project_name": project_name,
            "module_name": module_name,
            "ticket_info": ticket_info,
            "ticket_id": ticket_id,
            "conclusion": final_conclusion,
            "date_time": str(final_date_time),
            "summary": str(final_summary),
            "report": str(final_report),
            "other": str(final_other),
            "type": "终验"
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("maintenance_conclusion", data)
        tb_db.commit()
        # final_file_list_save = self.ctx.variables.get("final_file_list_save")
        # tb_db = mysql.new_mysql_instance("tbconstruct")
        # tb_db.begin()
        # tb_db.insert_batch("maintenance_upload_data", final_file_list_save)
        # tb_db.commit()
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionMaintenanceAcceptance(object):
    def __init__(self):
        super(ConstructionMaintenanceAcceptance, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def empty_node(self):
        flow.complete_task(self.ctx.task_id)

    def initial_acceptance(self, project_name, initial_date_time, initial_appendix_list, PM):
        # 获取需求单号
        db = mysql.new_mysql_instance('tbconstruct')
        sql = "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd " \
              "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode " \
              f"WHERE rewd.project_name = '{project_name}' "
        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table('获取需求id', {'message': code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get('idcrmOrderCode')
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"})
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
            self.workflow_detail.add_kv_table('最终po信息', {'message': po_material_info})
            for po in po_material_info:
                data_list.append({"AssetCode": "",
                                  "LineDetailId": str(po.get('orderItemId')),
                                  "QuantityReceived": po.get('orderAmount'),
                                  "SnCode": "",
                                  "AttachmentList": initial_appendix_list
                                  })

            self.workflow_detail.add_kv_table('传参信息', {'message': data_list})
            # 获取企微id
            chat_info = gnetops.request(action="Tof",
                                        method="GetIDCStaffIDByEngName",
                                        ext_data={
                                            "EngName": str(PM),
                                        }
                                        )
            self.workflow_detail.add_kv_table('获取企微id', {'message': str(chat_info)})
            chat_id = ''
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table('获取企微id',
                                                  {"success": False, "data": f"获取失败:{str(chat_info)}"})
            info = gnetops.request(action="Tbconstruct",
                                   method="FeedbackAcceptanceStatusToStar",
                                   ext_data={
                                       "FeedbackType": 2,  # 0=到货,1=安装,2=初验,3=终,人员入场=4
                                       "ReceiveDate": str(initial_date_time),  # 时间
                                       "ReceiveUserId": chat_id,  # id
                                       "Orderdetails": data_list,
                                   }
                                   )

            variables = {
                "po_material_info": po_material_info,
                "data_list": data_list,
                "info": str(info),
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table('获取po信息', {'message': '获取不到需求单号'})
            return {'code': -1, 'message': '获取不到需求单号'}

    def final_acceptance(self, project_name, final_date_time, final_appendix_list, PM):
        # 获取需求单号
        db = mysql.new_mysql_instance('tbconstruct')
        sql = "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd " \
              "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode " \
              f"WHERE rewd.project_name = '{project_name}' "
        code_id_list = db.query(sql)
        self.workflow_detail.add_kv_table('获取需求id', {'message': code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 传参信息表
            data_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get('idcrmOrderCode')
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"})
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list

            self.workflow_detail.add_kv_table('最终po信息', {'message': po_material_info})
            for po in po_material_info:
                data_list.append({"AssetCode": "",
                                  "LineDetailId": str(po.get('orderItemId')),
                                  "QuantityReceived": po.get('orderAmount'),
                                  "SnCode": "",
                                  "AttachmentList": final_appendix_list
                                  })

            self.workflow_detail.add_kv_table('传参信息', {'message': data_list})
            # 获取企微id
            chat_info = gnetops.request(action="Tof",
                                        method="GetIDCStaffIDByEngName",
                                        ext_data={
                                            "EngName": str(PM),
                                        }
                                        )
            self.workflow_detail.add_kv_table('获取企微id', {'message': str(chat_info)})
            chat_id = ''
            if chat_info != 0:
                chat_id = str(chat_info)
            else:
                self.workflow_detail.add_kv_table('获取企微id',
                                                  {"success": False, "data": f"获取失败:{str(chat_info)}"})
            info = gnetops.request(action="Tbconstruct",
                                   method="FeedbackAcceptanceStatusToStar",
                                   ext_data={
                                       "FeedbackType": 3,  # 0=到货,1=安装,2=初验,3=终,人员入场=4
                                       "ReceiveDate": str(final_date_time),  # 时间
                                       "ReceiveUserId": chat_id,  # id
                                       "Orderdetails": data_list,
                                   }
                                   )

            variables = {
                "po_material_info": po_material_info,
                "data_list": data_list,
                "info": str(info),
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

        else:
            self.workflow_detail.add_kv_table('获取po信息', {'message': '获取不到需求单号'})
            return {'code': -1, 'message': '获取不到需求单号'}


class XGProjectDeliveryReviewFileUpload(AjaxTodoBase):
    """
        项管上传复盘文件
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        xg_file_upload_list = process_data.get('xg_file_upload_list', [])
        xg_file_remark = process_data.get('xg_file_remark', "")
        fail_file_str = "有文件上传失败:<br>"
        if not xg_file_upload_list:
            return {'code': -1, 'msg': '请上传文件'}
        for file in xg_file_upload_list:
            FileList = file.get('response', {}).get('FileList', [])
            if FileList:
                if FileList[0].get('status') != "success":
                    fail_file_str += f"{FileList[0].get('name')}<br>"
        if fail_file_str != "有文件上传失败:<br>":
            return {'code': -1, 'msg': fail_file_str}
        else:
            review_file_list = self.ctx.variables.get('review_file_list', [])
            is_in_list = False
            if review_file_list:
                for item in review_file_list:
                    if item.get("person_name") == "项管":
                        item["file_upload_list"] = xg_file_upload_list
                        item["file_remark"] = xg_file_remark
                        is_in_list = True
            if not is_in_list:
                review_file_list.append({"person_name": "项管",
                                         "file_upload_list": xg_file_upload_list,
                                         "file_remark": xg_file_remark})
            variables = {
                "xg_file_upload_list": xg_file_upload_list,
                "xg_file_remark": xg_file_remark,
                "review_file_list": review_file_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class JCSProjectDeliveryReviewFileUpload(AjaxTodoBase):
    """
        集成商上传复盘文件
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        jcs_file_upload_list = process_data.get('jcs_file_upload_list', [])
        jcs_file_remark = process_data.get('jcs_file_remark', "")
        fail_file_str = "有文件上传失败:<br>"
        if not jcs_file_upload_list:
            return {'code': -1, 'msg': '请上传文件'}
        for file in jcs_file_upload_list:
            FileList = file.get('response', {}).get('FileList', [])
            if FileList:
                if FileList[0].get('status') != "success":
                    fail_file_str += f"{FileList[0].get('name')}<br>"
        if fail_file_str != "有文件上传失败:<br>":
            return {'code': -1, 'msg': fail_file_str}
        else:
            review_file_list = self.ctx.variables.get('review_file_list', [])
            is_in_list = False
            if review_file_list:
                for item in review_file_list:
                    if item.get("person_name") == "集成商":
                        item["file_upload_list"] = jcs_file_upload_list
                        item["file_remark"] = jcs_file_remark
                        is_in_list = True
            if not is_in_list:
                review_file_list.append({"person_name": "集成商",
                                         "file_upload_list": jcs_file_upload_list,
                                         "file_remark": jcs_file_remark})
            variables = {
                "jcs_file_upload_list": jcs_file_upload_list,
                "jcs_file_remark": jcs_file_remark,
                "review_file_list": review_file_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class JLProjectDeliveryReviewFileUpload(AjaxTodoBase):
    """
        监理上传复盘文件
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get('ticket_id')
        jl_file_upload_list = process_data.get('jl_file_upload_list', [])
        jl_file_remark = process_data.get('jl_file_remark', "")
        fail_file_str = "有文件上传失败:<br>"
        if not jl_file_upload_list:
            return {'code': -1, 'msg': '请上传文件'}
        for file in jl_file_upload_list:
            FileList = file.get('response', {}).get('FileList', [])
            if FileList:
                if FileList[0].get('status') != "success":
                    fail_file_str += f"{FileList[0].get('name')}<br>"
        if fail_file_str != "有文件上传失败:<br>":
            return {'code': -1, 'msg': fail_file_str}
        else:
            review_file_list = self.ctx.variables.get('review_file_list', [])
            is_in_list = False
            if review_file_list:
                for item in review_file_list:
                    if item.get("person_name") == "监理":
                        item["file_upload_list"] = jl_file_upload_list
                        item["file_remark"] = jl_file_remark
                        is_in_list = True
            if not is_in_list:
                review_file_list.append({"person_name": "监理",
                                         "file_upload_list": jl_file_upload_list,
                                         "file_remark": jl_file_remark})
            variables = {
                "jl_file_upload_list": jl_file_upload_list,
                "jl_file_remark": jl_file_remark,
                "review_file_list": review_file_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class PMProjectDeliveryReviewFileAudit(AjaxTodoBase):
    """
        PM审核复盘文件
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_pass = process_data.get('is_pass')
        PM_remark = process_data.get('PM_remark', "")
        PM_rejection = process_data.get('PM_rejection', "")
        PM_file = process_data.get('PM_file', "")
        if is_pass == '是':
            review_file_list = self.ctx.variables.get('review_file_list', [])
            variables = {
                "PM_remark": PM_remark,
                "is_pass": is_pass,
                "PM_file": PM_file,
                "review_file_list": review_file_list,
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            if not PM_rejection:
                return {'code': -1, 'msg': '请填写驳回原因'}
            variables = {
                "PM_remark": PM_remark,
                "PM_rejection": PM_rejection,
                "is_pass": is_pass,
                "PM_file": PM_file
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)


class PMProjectDeliveryReviewFileAuditNew(AjaxTodoBase):
    """
        PM审核复盘文件
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        review_file_list = process_data.get('review_file_list', [])
        jl_reject = self.ctx.variables.get('jl_reject', "")
        jcs_reject = self.ctx.variables.get('jcs_reject', "")
        xg_reject = self.ctx.variables.get('xg_reject', "")
        jl_rejection = ""
        xg_rejection = ""
        jcs_rejection = ""
        for item in review_file_list:
            if item.get("is_pass") == '否':
                if not item.get("PM_rejection"):
                    return {'code': -1, 'msg': '请填写驳回原因'}
                if item.get("person_name") == "监理":
                    jl_reject = "否"
                    jl_rejection = item.get("PM_rejection")
                elif item.get("person_name") == "集成商":
                    jcs_reject = "否"
                    jcs_rejection = item.get("PM_rejection")
                elif item.get("person_name") == "项管":
                    xg_reject = "否"
                    xg_rejection = item.get("PM_rejection")
            else:
                if item.get("person_name") == "监理":
                    jl_reject = "是"
                elif item.get("person_name") == "集成商":
                    jcs_reject = "是"
                elif item.get("person_name") == "项管":
                    xg_reject = "是"
        if any([jl_reject == "否", jcs_reject == "否", xg_reject == "否"]):
            is_pass = "否"
        else:
            is_pass = "是"
        PM_file = process_data.get('PM_file', "")

        variables = {
            "is_pass": is_pass,
            "jl_reject": jl_reject,
            "jl_rejection": jl_rejection,
            "jcs_reject": jcs_reject,
            "jcs_rejection": jcs_rejection,
            "xg_reject": xg_reject,
            "xg_rejection": xg_rejection,
            "review_file_list": review_file_list,
            "PM_file": PM_file
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
