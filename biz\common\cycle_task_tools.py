#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2023/12/13
# <AUTHOR> v_hxhaoliu-dev
# @File    : cycle_task_tools.py
# @Software: PyCharm
from iBroker.lib.sdk import gnetops, flow


def get_ticket_info(ticket_id_list: list):
    """
    根据工单号列表获取工单信息
    :param ticket_id_list: 工单号列表
    :return:
    """
    data = {
        "ResultColumns": {
            "CreateTime": "",
            "EndTime": "",
            "InstanceId": "",
            # 是否强制结单
            "IsForceEnd": "",
            # 流程定义标识
            "ProcessDefinitionKey": "",
            "ServiceRecoverTime": "",
            "StartProcessTime": "",
            # 单据状态[OPEN:运行中;END:已结束]
            "TicketStatus": "",
            "Title": "",
            "CustomRequestVarKV": "",
        },
        "SearchCondition": {'TicketId': ticket_id_list}
    }

    extra_data = {"SchemaId": "ticket_base"}
    ticket_info = gnetops.request(
        action="QueryData", method="Run", data=data, ext_data=extra_data
    )
    res = ticket_info['List']
    return res


def ticket_is_over(ticket_id_list: list):
    """
    判断工单是否结束
    :param ticket_id_list: 工单号列表
    :return:
    """
    cnt = 0  # 用于记录有多少个工单正在运行中
    res_list = get_ticket_info(ticket_id_list)
    for item in res_list:
        if item.get('TicketStatus') == 'OPEN':
            cnt += 1
    return cnt


def cycle_task_over(self):
    """判断流程或节点是否结束
    主要作用就是消除循环任务因为流程或任务节点结束而报错
    """
    # 判断当前流程是否结束，如果流程已结束，便结束定时任务
    if flow.is_instance_end(self.ctx.instance_id):
        return {"success": True, "data": "流程已结束"}
    # 判断节点是否结束，如果任务节点已经流转结束，便结束定时任务
    if flow.is_task_end(self.ctx.task_id):
        return {"success": True, "data": f"任务节点{self.ctx.task_id}已结束"}
