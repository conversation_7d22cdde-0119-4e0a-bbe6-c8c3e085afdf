import ast
import datetime
import json

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl



def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


class ProjectApplicationWB(object):
    """
        项目报建
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(ProjectApplicationWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_application_confirmation_of_completion_review(self, project_name, number_name, task_name,
                                                              examine_approve_result, dismiss_role, remark):
        """
            项目报建子项任务完工确认审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        data = {
            "ProjectName": project_name,  # 项目名称
            # 子项编号名称（2.4.1、2.4.2、2.4.3、2.4.4、2.4.5、2.4.6、2.4.7、2.4.8、2.4.9、2.4.10、2.4.11、2.4.12）
            "NumberName": number_name,
            # 子项任务名称（项目立项、设计合同签订上传、图纸送审、（监理直接发包、上传）、（工程直接发包、上传）、监理合同签订上传、
            # 工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险、施工许可证下发、消防备案、竣工备案）
            "TaskName": task_name,
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                              "project_application_confirmation_of_completion_review"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'project_application_confirmation_of_completion_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def fire_third_party_test_report_PM_review(self, project_name, PM_approval, PM_remark):
        """
            消防第三方检测报告PM审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        if PM_approval == "同意":
            examine_approve = 1
        elif PM_approval == "驳回":
            examine_approve = 2

        if not PM_remark:
            PM_remark = '无'

        data = {
            "ProjectName": project_name,  # 项目名称
            "PmApproval": examine_approve,  # PM审批（1、同意，2、驳回）
            "PMRemark": PM_remark,  # PM备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/fire_third_party_test_report_PM_review",
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'fire_third_party_test_report_PM_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceXmbj(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceXmbj, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_fire_third_party_test_report_uploaded(self):
        """
            等待消防第三方检测报告上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT fire_test_report " \
                   "FROM construction_application_second_level " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' "
        word_list = db.get_all(word_sql)
        xf_word_table = []
        fire_test_report = ""
        for row in word_list:
            fire_test_report = json.loads(row.get('fire_test_report', '')) if row.get('fire_test_report') else ''

            # # 尝试将 report_url 字符串解析为列表
            # try:
            #     fire_test_reports = ast.literal_eval(fire_test_report)  # 将字符串解析为列表
            # except (ValueError, SyntaxError):
            #     fire_test_reports = []  # 如果解析失败，设置为空列表
            # fire_test_report_list = []
            # # 确保 report_urls 是一个列表
            # if isinstance(fire_test_reports, list):
            #     # 为每个 URL 创建一个新的字典
            for url in fire_test_report:
                fire_test_report.append({"fire_test_report": url})

            xf_word_table.append({
                "fire_test_report_list": fire_test_report
            })
        self.workflow_detail.add_kv_table('消防第三方检测报告上传数据', {'message': word_list})
        if fire_test_report:
            variables = {
                'xf_word_table': xf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_xmlx_completion_confirmation_data_submission(self):
        """
            等待项目立项完工确认数据提交
        """
        xmbj_dict = {}
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND (serial_number = '2.4' OR serial_number = '2.4.1')"
        word_list = db.get_all(word_sql)
        xmlx_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            # 根据 serial_number 选择需要的字段
            if serial_number == '2.4':
                xmbj_work_content = row.get('work_content', '')
                xmbj_start_time = row.get('start_time', '')
                xmbj_completion_time = row.get('completion_time', '')
                xmbj_responsible_person = row.get('responsible_person', '')

                xmbj_dict = {
                    'work_content': xmbj_work_content,
                    'start_time': xmbj_start_time,
                    'finish_time': xmbj_completion_time,
                    'responsible_person': xmbj_responsible_person,
                }

            if serial_number == '2.4.1':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                xmlx_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "construction_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'xmbj_dict': xmbj_dict,
                'xmlx_word_table': xmlx_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sjht_completion_confirmation_data_submission(self):
        """
            等待设计合同签订上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.2'"
        word_list = db.get_all(word_sql)
        sjht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.2':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})
                sjht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sjht_word_table': sjht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_tzss_completion_confirmation_data_submission(self):
        """
            等待图纸送审完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.3'"
        word_list = db.get_all(word_sql)
        tzss_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.3':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                tzss_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'tzss_word_table': tzss_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jlzf_completion_confirmation_data_submission(self):
        """
            等待监理直接发包、上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.4'"
        word_list = db.get_all(word_sql)
        jlzf_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.4':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                jlzf_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jlzf_word_table': jlzf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gczf_completion_confirmation_data_submission(self):
        """
            等待工程直接发包、上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.5'"
        word_list = db.get_all(word_sql)
        gczf_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.5':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})
                gczf_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gczf_word_table': gczf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jlht_completion_confirmation_data_submission(self):
        """
            等待监理合同签订上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.6'"
        word_list = db.get_all(word_sql)
        jlht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.6':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                jlht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jlht_word_table': jlht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gcht_completion_confirmation_data_submission(self):
        """
            等待工程合同签订上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.7'"
        word_list = db.get_all(word_sql)
        gcht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.7':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})
                gcht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gcht_word_table': gcht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_nmght_completion_confirmation_data_submission(self):
        """
            等待农民工保证金监管合同签订上传完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.8'"
        word_list = db.get_all(word_sql)
        nmght_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.8':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                nmght_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'nmght_word_table': nmght_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gsyw_completion_confirmation_data_submission(self):
        """
            等待工伤意外险完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.9'"
        word_list = db.get_all(word_sql)
        gsyw_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.9':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                gsyw_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gsyw_word_table': gsyw_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sgxk_completion_confirmation_data_submission(self):
        """
            等待施工许可证下发完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.10'"
        word_list = db.get_all(word_sql)
        sgxk_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.10':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                sgxk_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sgxk_word_table': sgxk_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_xfba_completion_confirmation_data_submission(self):
        """
            等待消防备案完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.11'"
        word_list = db.get_all(word_sql)
        xfba_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.11':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})
                xfba_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'xfba_word_table': xfba_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jgba_completion_confirmation_data_submission(self):
        """
            等待竣工备案完工确认数据提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.12'"
        word_list = db.get_all(word_sql)
        jgba_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.12':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = json.loads(row.get('appendix', ''))

                appendix_list = []
                # 确保 report_urls 是一个列表
                if isinstance(appendix, list):
                    # 为每个 URL 创建一个新的字典
                    for url in appendix:
                        appendix_list.append({"appendix": url})

                jgba_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_list
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jgba_word_table': jgba_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceXmbj(object):
    """
        项目报建
        接口数据上传
    """

    def __init__(self):
        super(PartnerCallInterfaceXmbj, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_application_confirmation_of_completion(self, TencentTicketId, ProjectName, NumberName, TaskName,
                                                       StartTime, FinishTime, ScheduleSkewingExplain, Appendix, Remark):
        """
            项目报建子任务完工确认
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        # 校验 TaskName
        if not TaskName or not NumberName:
            return {'code': -1, 'msg': '子任务名称或子任务编号缺失'}
        # 校验 StartTime 和 FinishTime
        if not StartTime or not FinishTime:
            return {'code': -1, 'msg': '实际开始时间和实际完成时间缺失'}
        # 校验 ScheduleSkewingExplain
        if not ScheduleSkewingExplain:
            return {'code': -1, 'msg': '进度偏移说明'}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()

        sdzx_conditions = {
            'project_name': ProjectName,
            'work_content': '项目报建（含消防）',
        }
        sdzx_update_data = {
            "dcops_ticket_id": TencentTicketId,
            "project_name": ProjectName,
        }
        tb_db.update("excel_data", sdzx_update_data, sdzx_conditions)
        if TaskName == "项目立项":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix)
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "设计合同签订上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "图纸送审":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "监理直接发包、上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "工程直接发包、上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "监理合同签订上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "工程合同签订上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "农民工保证金监管合同签订上传":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "工伤意外险":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "施工许可证下发":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "消防备案":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "竣工备案":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务
        elif TaskName == "档案馆资料归档":
            # 转换 StartTime 和 FinishTime 为 YYYY-MM-DD 格式
            if StartTime:
                StartTime = datetime.datetime.strptime(StartTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            if FinishTime:
                FinishTime = datetime.datetime.strptime(FinishTime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            # 这里可以根据需要处理每个任务项的数据
            # 例如，构建一个数据字典用于后续操作
            work_conditions = {
                'project_name': ProjectName,
                'work_content': TaskName,
            }
            work_update_data = {
                "dcops_ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "actual_start_time": StartTime,
                "actual_finish_time": FinishTime,
                "schedule_skewing_explain": ScheduleSkewingExplain,
                "appendix": json.dumps(Appendix),
            }
            tb_db.update("excel_data", work_update_data, work_conditions)
            tb_db.commit()  # 确保提交事务

        return {'code': 200, 'msg': '成功'}

    def fire_third_party_test_report_uploaded(self, TencentTicketId, ProjectName, FireThirdPartyTestReport, Remark):
        """
            消防第三方检测报告上传
        """
        if not Remark:
            Remark = '无'
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, responsible_person," \
                   "input_object, output_object, construction_attribute " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4'" \
                   "AND work_content = '项目报建（含消防）'"
        word_list = db.get_all(word_sql)
        insert_data = {}
        for row in word_list:
            serial_number = row.get('serial_number')
            work_content = row.get('work_content')
            start_time = row.get('start_time')
            completion_time = row.get('completion_time')
            responsible_person = row.get('responsible_person')
            input_object = row.get('input_object')
            output_object = row.get('output_object')
            construction_attribute = row.get('construction_attribute')
            insert_data = {
                "ticket_id": TencentTicketId,
                "project_name": ProjectName,
                "seq_no": serial_number,
                "work_content": work_content,
                "start_time": start_time,
                "finish_time": completion_time,
                "responsible_person": responsible_person,
                "input": input_object,
                "output": output_object,
                "construction_attribute": construction_attribute,
                "fire_test_report": json.dumps(FireThirdPartyTestReport)
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert("construction_application_second_level", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def create_ticket(self, ProjectName, PartnerTicketId, Facilitator):
        """
            创建项目报建流程（起腾讯侧工单）
        """
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        res_list = []
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号{ProjectName}"
        processTitle = date_string + "项目报建"
        if system_type == '外部':
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'Facilitator': Facilitator,
                        'project_name': ProjectName,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_partner_project_construction_application",
                    "Source": "",
                    "TicketDescription": processTitle,
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)
        elif system_type == 'Dcops':
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        'Facilitator': Facilitator,
                        'project_name': ProjectName,
                        'system_id': system_id,
                        'PartnerTicketId': PartnerTicketId
                    },
                    "ProcessDefinitionKey": "cooperative_partner_project_construction_application",
                    "Source": "",
                    "TicketDescription": processTitle,
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
            res_list.append(res)

        for row in res_list:
            TicketId = row.get('TicketId')

        return {
            'code': 200,
            'msg': '成功',
            'data': TicketId
        }


class XmlxItemApproval(AjaxTodoBase):
    """
        项目立项-完工确认项管审批
    """

    def __init__(self):
        super(XmlxItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        xmlx_item_approval = process_data.get('xmlx_item_approval')
        xmlx_item_remark = process_data.get('xmlx_item_remark')

        variables = {
            'xmlx_item_approval': xmlx_item_approval,
            'xmlx_item_remark': xmlx_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SjhtItemApproval(AjaxTodoBase):
    """
        设计合同签订上传-完工确认项管审批
    """

    def __init__(self):
        super(SjhtItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sjht_item_approval = process_data.get('sjht_item_approval')
        sjht_item_remark = process_data.get('sjht_item_remark')

        variables = {
            'sjht_item_approval': sjht_item_approval,
            'sjht_item_remark': sjht_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class TzssSdItemApproval(AjaxTodoBase):
    """
        图纸送审-完工确认项管审批
    """

    def __init__(self):
        super(TzssSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        tzss_item_approval = process_data.get('tzss_item_approval')
        tzss_item_remark = process_data.get('tzss_item_remark')

        variables = {
            'tzss_item_approval': tzss_item_approval,
            'tzss_item_remark': tzss_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JlzfSdItemApproval(AjaxTodoBase):
    """
        监理直接发包、上传-完工确认项管审批
    """

    def __init__(self):
        super(JlzfSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jlzf_item_approval = process_data.get('jlzf_item_approval')
        jlzf_item_remark = process_data.get('jlzf_item_remark')

        variables = {
            'jlzf_item_approval': jlzf_item_approval,
            'jlzf_item_remark': jlzf_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class GczfSdItemApproval(AjaxTodoBase):
    """
        工程直接发包、上传-完工确认项管审批
    """

    def __init__(self):
        super(GczfSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        gczf_item_approval = process_data.get('gczf_item_approval')
        gczf_item_remark = process_data.get('gczf_item_remark')

        variables = {
            'gczf_item_approval': gczf_item_approval,
            'gczf_item_remark': gczf_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JlhtSdItemApproval(AjaxTodoBase):
    """
        监理合同签订上传-完工确认项管审批
    """

    def __init__(self):
        super(JlhtSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jlht_item_approval = process_data.get('jlht_item_approval')
        jlht_item_remark = process_data.get('jlht_item_remark')

        variables = {
            'jlht_item_approval': jlht_item_approval,
            'jlht_item_remark': jlht_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class GchtSdItemApproval(AjaxTodoBase):
    """
        工程合同-完工确认项管审批
    """

    def __init__(self):
        super(GchtSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        gcht_item_approval = process_data.get('gcht_item_approval')
        gcht_item_remark = process_data.get('gcht_item_remark')

        variables = {
            'gcht_item_approval': gcht_item_approval,
            'gcht_item_remark': gcht_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class NmghtSdItemApproval(AjaxTodoBase):
    """
        农民工保证金监管合同签订上传-完工确认项管审批
    """

    def __init__(self):
        super(NmghtSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        nmght_item_approval = process_data.get('nmght_item_approval')
        nmght_item_remark = process_data.get('nmght_item_remark')

        variables = {
            'nmght_item_approval': nmght_item_approval,
            'nmght_item_remark': nmght_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class GsywSdItemApproval(AjaxTodoBase):
    """
        工伤意外险-完工确认项管审批
    """

    def __init__(self):
        super(GsywSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        gsyw_item_approval = process_data.get('gsyw_item_approval')
        gsyw_item_remark = process_data.get('gsyw_item_remark')

        variables = {
            'gsyw_item_approval': gsyw_item_approval,
            'gsyw_item_remark': gsyw_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SgxkSdItemApproval(AjaxTodoBase):
    """
        施工许可证下发-完工确认项管审批
    """

    def __init__(self):
        super(SgxkSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        sgxk_item_approval = process_data.get('sgxk_item_approval')
        sgxk_item_remark = process_data.get('sgxk_item_remark')

        variables = {
            'sgxk_item_approval': sgxk_item_approval,
            'sgxk_item_remark': sgxk_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class XfbaSdItemApproval(AjaxTodoBase):
    """
        消防备案-完工确认项管审批
    """

    def __init__(self):
        super(XfbaSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        xfba_item_approval = process_data.get('xfba_item_approval')
        xfba_item_remark = process_data.get('xfba_item_remark')

        variables = {
            'xfba_item_approval': xfba_item_approval,
            'xfba_item_remark': xfba_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JgbaSdItemApproval(AjaxTodoBase):
    """
        竣工备案-完工确认项管审批
    """

    def __init__(self):
        super(JgbaSdItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jgba_item_approval = process_data.get('jgba_item_approval')
        jgba_item_remark = process_data.get('jgba_item_remark')

        variables = {
            'Jgba_item_approval': jgba_item_approval,
            'jgba_item_remark': jgba_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class XfbgPmApproval(AjaxTodoBase):
    """
        消防第三方检测报告上传-完工确认PM审批
    """

    def __init__(self):
        super(XfbgPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        xfbg_PM_approval = process_data.get('xfbg_PM_approval')
        xfbg_PM_remark = process_data.get('xfbg_PM_remark')

        variables = {
            'xfbg_PM_approval': xfbg_PM_approval,
            'xfbg_PM_remark': xfbg_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
