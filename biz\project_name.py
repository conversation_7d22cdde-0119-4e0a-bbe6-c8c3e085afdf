from iBroker.lib import mysql


class Database(object):
    def FindProjectName(self, database_name):
        """
            查找项目
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT campus,project_name FROM {database_name}"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            project_name = row["project_name"]
            campus = row["campus"]
            data.append({'project_name': project_name, 'campus': campus})
        return data

    def Find<PERSON>and<PERSON>(self, database_name, campus, project_name):
        """
            查找项目所对应的负责人
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT integrator, item_tube, supervision_management " \
                    f"FROM {database_name} WHERE campus = '{campus}' and project_name = '{project_name}'"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            integrator = row["integrator"]
            item_tube = row["item_tube"]
            supervision_management = row["supervision_management"]
            data.append({
                'integrator': integrator,
                'item_tube': item_tube,
                'supervision_management': supervision_management
            })
        return data
