from iBroker.lib import mysql


class Construction(object):
    def estimateconstructioncycle(self, ConstructionDeliveryTime, EstimatedDeliveryTime,
                                  Campus, SupplierShare, CampusWithCrossSpringFestival):
        tb_db = mysql.new_mysql_instance('tbconstruct')
        EstimateConstructionCycle = 7
        if SupplierShare == '无' and CampusWithCrossSpringFestival == '是':
            EstimateConstructionCycle += 2.5
        elif SupplierShare == '无':
            EstimateConstructionCycle += 2
        elif CampusWithCrossSpringFestival == '是':
            EstimateConstructionCycle += 0.5
        # 新需求条件
        if Campus in ['怀来东园', '怀来瑞北', '天津高新']:
            month = int(ConstructionDeliveryTime.split('-')[1])
            if month == 10:
                EstimateConstructionCycle += 4
            elif month == 11:
                EstimateConstructionCycle += 3
            elif month == 12:
                EstimateConstructionCycle += 2
            elif month == 1:
                EstimateConstructionCycle += 1
        query_data = f"SELECT construction_time_electricity_outside_city FROM " \
                     f"construction_evaluation_data WHERE campus = '{Campus}'"
        result = tb_db.query(query_data)

        if len(result) > 0:
            construction_time_electricity_outside_city = result[0]['construction_time_electricity_outside_city']
            construction_time_electricity_outside_city = float(construction_time_electricity_outside_city)
            EstimateConstructionCycle = float(EstimateConstructionCycle)
            if EstimateConstructionCycle < construction_time_electricity_outside_city:
                EstimateConstructionCycle = construction_time_electricity_outside_city
            else:
                EstimateConstructionCycle = EstimateConstructionCycle

        data = {
            'EstimateConstructionCycle': EstimateConstructionCycle
        }
        return data

    def evaluation_data(self, campus, projectName):
        tb_db = mysql.new_mysql_instance('tbconstruct')
        # 查询数据
        query_data = f"SELECT withr_without_substation, whether_create_new_pipe_ditch, " \
                     f"construction_time_electricity_outside_city, have_ability_comment, " \
                     f"campus_with_without_check_accept, campus_with_without_real_estate_certificate " \
                     f"FROM construction_evaluation_data WHERE campus = '{campus}' AND project_name = '{projectName}'"
        result = tb_db.query(query_data)

        if len(result) > 0:
            data = {
                'WithrWithoutSubstation': result[0]['withr_without_substation'],
                'WhetherCreateNewPipeDitch': result[0]['whether_create_new_pipe_ditch'],
                'ConstructionTimeElectricityOutsideCity': result[0]['construction_time_electricity_outside_city'],
                'HaveAbilityComment': result[0]['have_ability_comment'],
                'CampusWithWithoutCheckAccept': result[0]['campus_with_without_check_accept'],
                'CampusWithWithoutRealEstateCertificate': result[0]['campus_with_without_real_estate_certificate']
            }
            return data
        else:
            return {}

    def query_data(self, ConstructionDeliveryTime, EstimatedDeliveryTime,
                   Campus, SupplierShare, CampusWithCrossSpringFestival,
                   EstimateConstructionCycle, DemandDeliveryCycle):
        if not EstimateConstructionCycle or not DemandDeliveryCycle or not ConstructionDeliveryTime:
            if not EstimateConstructionCycle:
                EstimateConstructionCycle = 7
            if not DemandDeliveryCycle:
                DemandDeliveryCycle = 0
            if not ConstructionDeliveryTime:
                ConstructionDeliveryTime = '0000-00-00'

        # 数据库信息查询
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT area, campus, project_name," \
                    f" delivery_rack,state,supplier_resources, construction_time_electricity_outside_city " \
                    f" FROM construction_evaluation_data"

        result = db.get_all(query_sql)
        isActive_dict = {}
        area_dict = {}
        for row in result:
            area = row["area"]
            campus_name = row["campus"]
            project_name = row["project_name"]
            delivery_rack = row["delivery_rack"]
            supplier_resources = row['supplier_resources']
            construction_time_electricity_outside_city = float(row["construction_time_electricity_outside_city"])
            state = row['state']
            isActive = 0
            campu = ''

            estimate_cycle = float(EstimateConstructionCycle)
            DemandDeliveryCycle = float(DemandDeliveryCycle)
            construction_time_electricity_outside_city = float(construction_time_electricity_outside_city)

            if SupplierShare == '无' and CampusWithCrossSpringFestival == '是':
                estimate_cycle += 2.5
            elif SupplierShare == '无':
                estimate_cycle += 2
            elif CampusWithCrossSpringFestival == '是':
                estimate_cycle += 0.5

            if campus_name in ['怀来东园', '怀来瑞北', '天津高新']:
                month = int(ConstructionDeliveryTime.split('-')[1])
                if month == 10:
                    estimate_cycle += 4
                elif month == 11:
                    estimate_cycle += 3
                elif month == 12:
                    estimate_cycle += 2
                elif month == 1:
                    estimate_cycle += 1

            if estimate_cycle < construction_time_electricity_outside_city:
                estimate_cycle = construction_time_electricity_outside_city
            elif estimate_cycle > construction_time_electricity_outside_city:
                estimate_cycle = estimate_cycle

            if Campus == '':
                if not Campus:
                    campu = campus_name
                for i in campu:
                    estimate_cycle = float(EstimateConstructionCycle)
                    DemandDeliveryCycle = float(DemandDeliveryCycle)
                    construction_time_electricity_outside_city = float(construction_time_electricity_outside_city)

                    if SupplierShare == '无' and CampusWithCrossSpringFestival == '是':
                        estimate_cycle += 2.5
                    elif SupplierShare == '无':
                        estimate_cycle += 2
                    elif CampusWithCrossSpringFestival == '是':
                        estimate_cycle += 0.5

                    if i in ['怀来东园', '怀来瑞北', '天津高新']:
                        month = int(ConstructionDeliveryTime.split('-')[1])
                        if month == 10:
                            estimate_cycle += 4
                        elif month == 11:
                            estimate_cycle += 3
                        elif month == 12:
                            estimate_cycle += 2
                        elif month == 1:
                            estimate_cycle += 1

                    if estimate_cycle < construction_time_electricity_outside_city:
                        estimate_cycle = construction_time_electricity_outside_city
                    elif estimate_cycle > construction_time_electricity_outside_city:
                        estimate_cycle = estimate_cycle

                if estimate_cycle <= DemandDeliveryCycle:
                    isActive = 1
                else:
                    isActive = 0
            if campus_name == Campus:
                if estimate_cycle <= DemandDeliveryCycle:
                    isActive = 1
                else:
                    isActive = 0
                isActive_dict[campus_name] = isActive
            # 检查是否已存在该 area
            if area in area_dict:
                campus_dict = area_dict[area]["campus"]
                # 检查是否已存在该 campusName
                if campus_name in campus_dict:
                    campus = campus_dict[campus_name]
                    campus["project"].append({
                        "ProjectName": project_name,
                        'DeliveryRack': delivery_rack,
                        'IntegrationUnit': supplier_resources,
                        'State': state,
                        'isActive': isActive
                    })
                else:
                    campus_dict[campus_name] = {
                        "campusName": campus_name,
                        "project": [{
                            "ProjectName": project_name,
                            'DeliveryRack': delivery_rack,
                            'IntegrationUnit': supplier_resources,
                            'State': state,
                            'isActive': isActive
                        }]
                    }
            else:
                area_dict[area] = {
                    "area": area,
                    "campus": {
                        campus_name: {
                            "campusName": campus_name,
                            "project": [{
                                "ProjectName": project_name,
                                'DeliveryRack': delivery_rack,
                                'IntegrationUnit': supplier_resources,
                                'State': state,
                                'isActive': isActive
                            }]
                        }
                    }
                }

        data = []
        for area_data in area_dict.values():
            area_data["campus"] = list(area_data["campus"].values())
            data.append(area_data)

        return data
