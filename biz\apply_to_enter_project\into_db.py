from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow


# 项目成员账号申请 数据入库类
class ApplyToEnterProjectIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def account_into_db(self, member_list, PM):
        """
        项目成员账号申请信息入库
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account_insert_list = []
        for info in member_list:
            account_insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "campus": info.get("campus_name"),
                    "project": info.get("project_name"),
                    "project_name": info.get("campus_name") + info.get("project_name"),
                    "role": info.get("role"),
                    "name": info.get("exist_name"),
                    "application_account": info.get("exist_account"),
                    "account_lable": info.get("exist_account_label"),
                    "account": info.get("exist_account"),
                    "service_provider": info.get("service_provider"),
                    "position": info.get("position"),
                    "phone": info.get("phone"),
                    "email": info.get("exist_email"),
                    "department": info.get("department", "IDC平台部"),
                    "sex": info.get("sex", "男"),
                    "needs": info.get("needs", "开通"),
                    "account_responsible_person": info.get(
                        "account_responsible_person", PM
                    ),
                    "application_reason": info.get(
                        "application_reason", "开通园区建设项目人员账号"
                    ),
                    "remark": info.get("remark"),
                    "del_flag": 0,
                    "distinguish": 0,
                }
            )
        if account_insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("project_role_account", account_insert_list)
            tb_db.commit()
        variables = {
            "account_insert_list": account_insert_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
