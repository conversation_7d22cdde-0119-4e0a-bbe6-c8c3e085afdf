from iBroker.lib import mysql

from biz.quality_evaluation.supplier import SupplierQuery


# 品类名称查询(质量评估评价标准分类下的所有品类)
class CategoryQuery(object):
    # 品类处理-通过实际项目品类供应商数量动态增加品类
    # （如：PUD有两个供应商，应生成PDU_1和PDU_2两个品类，同时生成对应的key。最终还需动态生成表格列json返回给前端。）
    @staticmethod
    def category_handle(project_name):
        """
        category_supplier = {
                "ahu": "申菱;ahu供应商",
                "cf": "科勒;cf供应商",
                "byq": "AEG;byq供应商",
                "zyg": "施耐德;zyg供应商",
                "dyg": "任达;dyg供应商",
                "hvdc": "中恒;hvdc供应商",
                "jg": "盛道;jg供应商",
                "pdu": "突破;胜威南方",
                "dc": "西恩迪;dc供应商",
                "jcs": "本贸;jcs供应商",
                "xg": "华建;xg供应商",
                "jl": "中邮通;jl供应商",
                "cs": "创意银河;cs供应商",
            }
        standard_class_category_name_cn = {
                "standard_class1_category": ['AHU', '柴发', '变压器', '中压柜', '低压柜', '综合配电柜'],
                "standard_class2_category": ['机柜', 'PDU', '电池'],
                "standard_class3_category": ['JDM'],
                "standard_class4_category": ['集成商'],
                "standard_class5_category": ['项管'],
                "standard_class6_category": ['监理'],
                "standard_class7_category": ['测试']
        }
        standard_class_category_name_en = {
                "standard_class1_category": ['ahu', 'cf', 'byq', 'zyg', 'dyg', 'hvdc'],
                "standard_class2_category": ['jg', 'pdu', 'dc'],
                "standard_class3_category": ['jdm'],
                "standard_class4_category": ['jcs'],
                "standard_class5_category": ['xg'],
                "standard_class6_category": ['jl'],
                "standard_class7_category": ['cs']
            }
        category_cn_en_field_dict = {
            "AHU": "ahu",
            "柴发": "cf",
            "变压器": "byq",
            "中压柜": "zyg",
            "低压柜": "dyg",
            "综合配电柜": "hvdc",
            "机柜": "jg",
            "PDU": "pdu",
            "电池": "dc",
            # "JDM": "jdm",
            "集成商": "jcs",
            "项管": "xg",
            "监理": "jl",
            "测试": "cs",
        }
        category_en_cn_field_dict = {
            "ahu":"AHU",
            "cf": "柴发",
            "byq": "变压器",
            "zyg": "中压柜",
            "dyg": "低压柜",
            "hvdc": "综合配电柜",
            "jg": "机柜",
            "pdu": "PDU",
            "dc": "电池",
            # "jdm": "JDM",
            "jcs": "集成商",
            "xg": "项管",
            "jl": "监理",
            "cs": "测试",
        }
        """
        # 获取项目供应商
        # 代码写死
        # category_supplier = SupplierQuery.get_project_category_supplier(project_name)
        # 数据库查询 （数据来源未确定，临时借用存库表来查询）
        category_supplier = SupplierQuery.query_project_category_supplier(project_name)
        # 获取标准分类对应的品类
        standard_class_category_name_cn = (
            CategoryQuery.get_standard_class_category_name()
        )
        # 获取标准分类对应的品类字段
        standard_class_category_name_en = (
            CategoryQuery.get_standard_class_category_name_field()
        )
        # 获取categoryCn: categoryEn
        category_cn_en_field_dict = CategoryQuery.get_category_field_dict()
        # 获取categoryEn: categoryCn
        category_en_cn_field_dict = CategoryQuery.get_category_key_field_dict()

        # 取出有多个供应商的品类
        multiple_suppliers_category_map = {}
        for category, supplier in category_supplier.items():
            if len(supplier.split(";")) > 1:
                multiple_suppliers_category_map[category] = supplier.split(";")
        # 生成对应供应商数量的数据（供应商、品类、品类key）
        for category_en, supplier_list in multiple_suppliers_category_map.items():
            category_cn = category_en_cn_field_dict[category_en]
            if (
                category_en
                in standard_class_category_name_en["standard_class1_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class1_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class1_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class1_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class1_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class1_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)
            if (
                category_en
                in standard_class_category_name_en["standard_class2_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class2_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class2_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class2_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class2_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class2_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)
            if (
                category_en
                in standard_class_category_name_en["standard_class4_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class4_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class4_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class4_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class4_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class4_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)
            if (
                category_en
                in standard_class_category_name_en["standard_class5_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class5_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class5_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class5_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class5_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class5_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)
            if (
                category_en
                in standard_class_category_name_en["standard_class6_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class6_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class6_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class6_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class6_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class6_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)
            if (
                category_en
                in standard_class_category_name_en["standard_class7_category"]
            ):
                for i in range(len(supplier_list)):
                    new_category_en = f"{category_en}_{i + 1}"
                    new_category_cn = (
                        f"{category_en_cn_field_dict[category_en]}_{i + 1}"
                    )
                    category_supplier.setdefault(new_category_en, supplier_list[i])
                    category_en_cn_field_dict.setdefault(
                        new_category_en, new_category_cn
                    )
                    category_cn_en_field_dict.setdefault(
                        new_category_cn, new_category_en
                    )
                    index = standard_class_category_name_en[
                        "standard_class7_category"
                    ].index(category_en)
                    standard_class_category_name_en["standard_class7_category"].insert(
                        index, new_category_en
                    )
                    standard_class_category_name_cn["standard_class7_category"].insert(
                        index, new_category_cn
                    )
                category_supplier.pop(category_en)
                standard_class_category_name_cn["standard_class7_category"].remove(
                    category_cn
                )
                standard_class_category_name_en["standard_class7_category"].remove(
                    category_en
                )
                category_en_cn_field_dict.pop(category_en)
                category_cn_en_field_dict.pop(category_cn)

        project_category_basic_info = {
            "category_supplier": category_supplier,
            "standard_class_category_name_en": standard_class_category_name_en,
            "standard_class_category_name_cn": standard_class_category_name_cn,
            "category_en_cn_field_dict": category_en_cn_field_dict,
            "category_cn_en_field_dict": category_cn_en_field_dict,
        }
        return project_category_basic_info

    @staticmethod
    def get_standard_class_category_name():
        """
        standard_class_category_dict = {
            "standard_class1_category": ['AHU', '柴发', '变压器', '中压柜', '低压柜', '综合配电柜'],
            "standard_class2_category": ['机柜', 'PDU', '电池'],
            "standard_class3_category": ['JDM'],
            "standard_class4_category": ['集成商'],
            "standard_class5_category": ['项管],
            "standard_class6_category": ['监理],
            "standard_class7_category": ['测试]
        }
        """
        query_sql = "select * from quality_evaluation_standard_class"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        standard_class_category_dict = {
            "standard_class1_category": [],
            "standard_class2_category": [],
            "standard_class3_category": [],
            "standard_class4_category": [],
            "standard_class5_category": [],
            "standard_class6_category": [],
            "standard_class7_category": [],
        }
        standard_class_category_field_dict = {
            1: "standard_class1_category",
            2: "standard_class2_category",
            3: "standard_class3_category",
            4: "standard_class4_category",
            5: "standard_class5_category",
            6: "standard_class6_category",
            7: "standard_class7_category",
        }
        for item in query_result:
            standard_class_category_dict[
                standard_class_category_field_dict[item["id"]]
            ] = list(filter(None, item["category_name"].split(";")))
        return standard_class_category_dict

    @staticmethod
    def get_standard_class_category_name_field():
        """
        standard_class_category_dict = {
            "standard_class1_category": ['ahu', 'cf', 'byq', 'zyg', 'dyg', 'hvdc'],
            "standard_class2_category": ['jg', 'pdu', 'dc'],
            "standard_class3_category": ['jdm'],
            "standard_class4_category": ['jcs'],
            "standard_class5_category": ['xg'],
            "standard_class6_category": ['jl'],
            "standard_class7_category": ['cs']
        }
        """
        query_sql = "select * from quality_evaluation_standard_class"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        standard_class_category_dict = {
            "standard_class1_category": [],
            "standard_class2_category": [],
            "standard_class3_category": [],
            "standard_class4_category": [],
            "standard_class5_category": [],
            "standard_class6_category": [],
            "standard_class7_category": [],
        }
        standard_class_category_field_dict = {
            1: "standard_class1_category",
            2: "standard_class2_category",
            3: "standard_class3_category",
            4: "standard_class4_category",
            5: "standard_class5_category",
            6: "standard_class6_category",
            7: "standard_class7_category",
        }
        category_field_dict = CategoryQuery.get_category_field_dict()
        for item in query_result:
            category_list = list(filter(None, item["category_name"].split(";")))
            for category in category_list:
                standard_class_category_dict[
                    standard_class_category_field_dict[item["id"]]
                ].append(category_field_dict[category])
        return standard_class_category_dict

    @staticmethod
    def get_category_field_dict():
        category_field_dict = {
            "AHU": "ahu",
            "柴发": "cf",
            "变压器": "byq",
            "中压柜": "zyg",
            "低压柜": "dyg",
            "综合配电柜": "hvdc",
            "机柜": "jg",
            "PDU": "pdu",
            "电池": "dc",
            # "JDM": "jdm",
            "集成商": "jcs",
            "项管": "xg",
            "监理": "jl",
            "测试": "cs",
        }
        return category_field_dict

    @staticmethod
    def get_category_key_field_dict():
        category_key_field_dict = {
            "ahu": "AHU",
            "cf": "柴发",
            "byq": "变压器",
            "zyg": "中压柜",
            "dyg": "低压柜",
            "hvdc": "综合配电柜",
            "jg": "机柜",
            "pdu": "PDU",
            "dc": "电池",
            # "jdm": "JDM",
            "jcs": "集成商",
            "xg": "项管",
            "jl": "监理",
            "cs": "测试",
        }
        return category_key_field_dict
