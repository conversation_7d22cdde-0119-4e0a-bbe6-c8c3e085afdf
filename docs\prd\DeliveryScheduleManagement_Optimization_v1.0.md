# DeliveryScheduleManagement 类优化需求分析

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-22
- **负责人**: Emma (产品经理)
- **项目**: iBroker-Construct 货期管理优化

## 2. 需求背景

### 2.1 现有问题
根据表结构信息文件中的需求描述，当前 `DeliveryScheduleManagement` 类存在以下问题：

1. **数据展示异常**: 设备类型固定写死在代码里，更改时将名字合并，导致功能展示异常，数据查不出
2. **查询性能问题**: 通过查两张表关联，存在N+1查询问题
3. **强制结单处理**: 强制结单的工单在字段中有标识，查询时需排除，但现有代码处理不完善
4. **测试环境数据不全**: 测试环境信息不全，影响功能验证

### 2.2 优化目标
1. **查询优化**: 不再通过查两张表关联，使用平台所有功能的表进行查询
2. **SQL优化**: 通过 project_name 和 device_type 等做 join 查询，避免多次查数据库
3. **强制结单处理**: 完善强制结单工单的过滤逻辑
4. **数据筛选**: 区分甲供和乙供设备，表中有相关配置表明设备是甲供还是乙供

## 3. 涉及的核心函数

### 3.1 dynamic_header 函数
**功能**: 动态表头生成（甲供）
**当前问题**:
- 存在重复的SQL查询逻辑
- 强制结单过滤不完善
- 数据处理逻辑复杂

### 3.2 tabular_data_query 函数
**功能**: 表格数据查询（甲供）
**当前问题**:
- 存在N+1查询问题
- 有重复的代码逻辑（函数末尾有重复的实现）
- 强制结单工单过滤逻辑不一致

## 4. 涉及的数据表结构

### 4.1 核心表结构
1. **delivery_schedule_management_table** (交付计划管理表)
   - 主要字段: project_name, supplier, device_type, equipment_sla, expected_time_equipment, estimated_time_delivery, delivery_gap

2. **all_construction_ticket_data** (所有施工工单数据)
   - 关键字段: TicketId, IsForceEnd (强制结束标识)

3. **equipment_production_racking_process** (设备生产上架流程)
   - 关键字段: ticket_id, project_name, device_name

4. **equipment_arrival_data** (设备到货数据)
   - 关键字段: campus, project_name, equipment_category, actual_arrival_time

### 4.2 表关联关系
- `delivery_schedule_management_table.project_name` = `risk_early_warning_data.project_name`
- `equipment_arrival_data.equipment_category` = `delivery_schedule_management_table.device_type`
- `equipment_production_racking_process.ticket_id` = `all_construction_ticket_data.TicketId`

## 5. 优化方案

### 5.1 SQL查询优化
**目标**: 使用单次复杂查询替代多次简单查询

**优化策略**:
1. 将所有相关表通过LEFT JOIN连接
2. 在WHERE子句中统一处理过滤条件
3. 添加强制结单工单过滤: `(actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')`

### 5.2 代码结构优化
**目标**: 消除重复代码，提高代码可维护性

**优化策略**:
1. 删除 `tabular_data_query` 函数末尾的重复实现代码
2. 统一数据处理逻辑
3. 优化项目key生成逻辑

### 5.3 数据处理优化
**目标**: 提高数据处理效率和准确性

**优化策略**:
1. 优化实际到货时间的处理逻辑
2. 改进项目名称和供应商的组合逻辑
3. 统一时间格式处理

## 6. 具体实现计划

### 6.1 dynamic_header 函数优化
1. **保留现有的优化版本**: 当前函数已经实现了单次复杂查询
2. **完善强制结单过滤**: 确保过滤逻辑正确执行
3. **优化数据去重逻辑**: 提高数据处理效率

### 6.2 tabular_data_query 函数优化
1. **删除重复代码**: 移除函数末尾从第2757行开始的重复实现
2. **统一查询逻辑**: 使用与dynamic_header相同的优化查询方式
3. **完善数据处理**: 统一实际到货时间等字段的处理逻辑

### 6.3 测试验证
1. **单项目模式测试**: 验证project_name参数的查询功能
2. **多项目模式测试**: 验证多条件组合查询功能
3. **强制结单过滤测试**: 确保强制结单的工单被正确过滤
4. **性能测试**: 验证查询性能是否得到改善

## 7. 预期效果

### 7.1 性能提升
- 减少数据库查询次数，从N+1查询优化为单次查询
- 提高数据处理效率

### 7.2 功能完善
- 正确处理强制结单工单的过滤
- 改善数据展示异常问题
- 提高代码可维护性

### 7.3 开发效率
- 消除重复代码，降低维护成本
- 统一数据处理逻辑，便于后续扩展

## 8. 风险评估

### 8.1 技术风险
- **低风险**: 主要是代码优化，不涉及业务逻辑变更
- **缓解措施**: 充分测试，确保功能兼容性

### 8.2 时间风险
- **中等风险**: 需要在周四前完成开发和测试
- **缓解措施**: 优先处理核心问题，分阶段实施

## 9. 下一步行动

1. **立即开始**: 清理tabular_data_query函数的重复代码
2. **优化查询**: 完善SQL查询逻辑和强制结单过滤
3. **测试验证**: 进行全面的功能和性能测试
4. **部署准备**: 准备周四发布所需的部署文档

---

**文档状态**: 已完成需求分析
**下一步**: 开始代码优化实施
