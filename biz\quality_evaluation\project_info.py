from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import config, mysql


# 项目信息查询
class ProjectInfoQuery(object):

    # 获取所有项目信息(PM、初验时间)
    @staticmethod
    def get_project_info_dict():
        """
        获取所有项目信息(PM、初验时间)
        """
        query_sql = (
            "SELECT project_name, actual_date, account FROM construction_feedback_to_xingchen "
            "WHERE type = '初验'"
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        project_info_dict = {}
        if query_result:
            for item in query_result:
                dict = {
                    "project_name": item.get("project_name"),
                    "first_test_date": item.get("actual_date"),
                    "pm": item.get("account"),
                }
                project_info_dict[item.get("project_name")] = dict
        return project_info_dict

    # 获取所有项目信息(PM、初验时间)-临时（七彩石获取，目前人工维护，因数据库数据未联动）
    @staticmethod
    def get_project_info_dict_temp():
        """
        获取所有项目信息(PM、初验时间)
        """
        # 获取七彩石配置
        project_info_dict = config.get_config_map("quality_evaluation_project_info")
        return project_info_dict


class ProjectInfoConfirm(AjaxTodoBase):
    # 项目信息确认
    def __init__(self):
        super(ProjectInfoConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取项目信息
        project_info_list = process_data.get("project_info_list", None)
        if not project_info_list:
            return {"code": -1, "msg": "请正确填写参评项目信息"}
        # 获取评分接口人信息
        #  商务
        business_rater = process_data.get("business_rater", None)
        # 供应链
        supply_chain_rater = process_data.get("supply_chain_rater", None)
        # 规划
        planning_rater = process_data.get("planning_rater", None)
        # 产品
        product_rater = process_data.get("product_rater", None)
        # 项目
        project_rater = process_data.get("project_rater", None)
        # 优化
        optimize_rater = process_data.get("optimize_rater", None)
        # 弱电
        weak_electricity_rater = process_data.get("weak_electricity_rater", None)
        # 内控
        internal_control_rater = process_data.get("internal_control_rater", None)
        # 获取七彩石配置
        team_rater_leader = config.get_config_map(
            "quality_evaluation_team_rater_leader"
        )
        if (
            "business_rater_leader" in team_rater_leader
            and team_rater_leader["business_rater_leader"]
        ):
            business_rater_leader = team_rater_leader["business_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置商务leader信息"}
        if (
            "supply_chain_rater_leader" in team_rater_leader
            and team_rater_leader["supply_chain_rater_leader"]
        ):
            supply_chain_rater_leader = team_rater_leader["supply_chain_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置供应链leader信息"}
        if (
            "planning_rater_leader" in team_rater_leader
            and team_rater_leader["planning_rater_leader"]
        ):
            planning_rater_leader = team_rater_leader["planning_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置规划leader信息"}
        if (
            "product_rater_leader" in team_rater_leader
            and team_rater_leader["product_rater_leader"]
        ):
            product_rater_leader = team_rater_leader["product_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置产品leader信息"}
        if (
            "project_rater_leader" in team_rater_leader
            and team_rater_leader["project_rater_leader"]
        ):
            project_rater_leader = team_rater_leader["project_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置项目leader信息"}
        if (
            "optimize_rater_leader" in team_rater_leader
            and team_rater_leader["optimize_rater_leader"]
        ):
            optimize_rater_leader = team_rater_leader["optimize_rater_leader"]
        else:
            return {"code": -1, "msg": "请联系管理员配置优化leader信息"}
        if (
            "weak_electricity_rater_leader" in team_rater_leader
            and team_rater_leader["weak_electricity_rater_leader"]
        ):
            weak_electricity_rater_leader = team_rater_leader[
                "weak_electricity_rater_leader"
            ]
        else:
            return {"code": -1, "msg": "请联系管理员配置弱电leader信息"}
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "project_info_list": project_info_list,
            "business_rater": business_rater,
            "supply_chain_rater": supply_chain_rater,
            "planning_rater": planning_rater,
            "product_rater": product_rater,
            "project_rater": project_rater,
            "optimize_rater": optimize_rater,
            "weak_electricity_rater": weak_electricity_rater,
            "internal_control_rater": internal_control_rater,
            "team_rater_leader": team_rater_leader,
            "business_rater_leader": business_rater_leader,
            "business_rater_leader_approval": 1,
            "supply_chain_rater_leader": supply_chain_rater_leader,
            "supply_chain_rater_leader_approval": 1,
            "planning_rater_leader": planning_rater_leader,
            "planning_rater_leader_approval": 1,
            "product_rater_leader": product_rater_leader,
            "product_rater_leader_approval": 1,
            "project_rater_leader": project_rater_leader,
            "project_rater_leader_approval": 1,
            "optimize_rater_leader": optimize_rater_leader,
            "optimize_rater_leader_approval": 1,
            "weak_electricity_rater_leader": weak_electricity_rater_leader,
            "weak_electricity_rater_leader_approval": 1,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)
