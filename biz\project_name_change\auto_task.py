from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config
from biz.wework_dept.wework_dept_api import (
    WeworkDeptApi,
)


class ProjectNameChangeAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 修改risk表
    def update_risk_db(self, old_campus, old_project, new_campus, new_project):
        old_project_name = old_campus + old_project
        new_project_name = new_campus + new_project
        update_data = {
            "campus": new_campus,
            "project": new_project,
            "project_name": new_project_name,
        }
        conditions = {
            "project_name": old_project_name,
        }
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        db.update("risk_early_warning_data", update_data, conditions)
        db.commit()
        flow.complete_task(self.ctx.task_id)

    # 修改summary_data表
    def update_summary_db(self, old_campus, old_project, new_campus, new_project):
        update_data = {
            "campus": new_campus,
            "project_name": new_project,
        }
        conditions = {
            "campus": old_campus,
            "project_name": old_project,
        }
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        db.update("summary_data", update_data, conditions)
        db.commit()
        flow.complete_task(self.ctx.task_id)

    # 修改账号表
    def update_account_db(self, old_campus, old_project, new_campus, new_project):
        old_project_name = old_campus + old_project
        new_project_name = new_campus + new_project
        update_data = {
            "campus": new_campus,
            "project": new_project,
            "project_name": new_project_name,
        }
        conditions = {
            "project_name": old_project_name,
        }
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        db.update("project_role_account", update_data, conditions)
        db.commit()
        flow.complete_task(self.ctx.task_id)

    # 修改账号配置表
    def update_account_basic_db(self, old_campus, old_project, new_campus, new_project):
        sql = f"select * from project_member_basic_info where field_name in ('合建园区', '合建项目')"
        db = mysql.new_mysql_instance("tbconstruct")
        result = db.query(sql)
        for item in result:
            if item.get("field_name") == "合建园区":
                if new_campus != old_campus:
                    campus_val = item.get("field_value")
                    campus_list = [
                        item for item in campus_val.split(";") if item != old_campus
                    ]
                    if new_campus not in campus_list:
                        campus_list.append(new_campus)
                    new_campus_val = ";".join(campus_list)
                    update_data = {
                        "field_value": new_campus_val,
                    }
                    conditions = {
                        "field_name": "合建园区",
                    }
                    db.begin()
                    db.update("project_member_basic_info", update_data, conditions)
                    db.commit()
            if item.get("field_name") == "合建项目":
                if new_project != old_project:
                    project_val = item.get("field_value")
                    project_list = [
                        item for item in project_val.split(";") if item != old_project
                    ]
                    if new_project not in project_list:
                        project_list.append(new_project)
                    new_project_val = ";".join(project_list)
                    update_data = {
                        "field_value": new_project_val,
                    }
                    conditions = {
                        "field_name": "合建项目",
                    }
                    db.begin()
                    db.update("project_member_basic_info", update_data, conditions)
                    db.commit()
        flow.complete_task(self.ctx.task_id)

    # 组织架构修改
    def update_wework_dept_determine(
        self, old_campus, old_project, new_campus, new_project
    ):
        old_project_name = old_campus + old_project
        new_project_name = new_campus + new_project
        update_dept_flag = 0
        # 全部组织架构
        dept_list = WeworkDeptApi.query_dept()
        dept_name_list = [item.get("label") for item in dept_list]
        if new_project_name not in dept_name_list:
            update_dept_flag = 1
        if update_dept_flag:
            # 建单人（一般是PM）
            creator = self.ctx.variables.get("Creator")
            # 邮件推送配置信息
            eml_cfg = config.get_config_map("wework_dept_apply_email_info")
            # 流程负责人
            flow_responsible_person = config.get_config_string(
                "flow_responsible_person"
            )
            # 给 邮件推送配置信息 的 receiver 推送邮件
            eml_cfg_update = eml_cfg.get("update")
            receiver = eml_cfg_update.get("receiver")
            title = eml_cfg_update.get("title")
            Cc = eml_cfg_update.get("Cc")
            Cc.append(creator)
            Cc.append(flow_responsible_person)
            Cc = list(set(Cc))
            email_title = f"{old_project_name}{title}"
            email_content = (
                f"{(',').join(receiver)}：<br>"
                f"<p style='text-indent: 2em;'>请修改组织架构：【{old_project_name}】更改为【{new_project_name}】</p>"
                f"<p style='text-indent: 2em;'>谢谢！</p>"
            )
            email_receiver = (
                [item + "@tencent.com" for item in receiver] if receiver else []
            )
            email_Cc = [item + "@tencent.com" for item in Cc] if Cc else []
            tof.send_email(
                sendTitle=email_title,
                msgContent=email_content,
                sendTo=email_receiver,
                sendCopy=email_Cc,
            )
            email_info_apply = {
                "email_title": email_title,
                "email_content": email_content,
                "email_receiver": email_receiver,
                "email_Cc": email_Cc,
            }
            variables = {
                "old_project_name": old_project_name,
                "new_project_name": new_project_name,
                "dept_list": dept_list,
                "dept_name_list": dept_name_list,
                "update_dept_flag": update_dept_flag,
                "eml_cfg": eml_cfg,
                "eml_cfg_update": eml_cfg_update,
                "email_info_apply": email_info_apply,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            variables = {
                "old_project_name": old_project_name,
                "new_project_name": new_project_name,
                "dept_list": dept_list,
                "dept_name_list": dept_name_list,
                "update_dept_flag": update_dept_flag,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
