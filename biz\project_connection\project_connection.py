from datetime import datetime

import lunardate
import requests
import re
from iBroker.lib.sdk import flow, tof, gnetops
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib
from iBroker.lib import mysql
from openpyxl import load_workbook


class CreateTicket(object):
    def __init__(self):
        super(CreateTicket, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_start(self, project_name, campus):
        """
        开办及设备到场前施工
        """

        processTitle = f"{project_name}:项目施工前准阶段"
        data = {
            "CustomVariables": {
                "campus": campus,
                "project_name": project_name
            },
            "ProcessDefinitionKey": "construction_before_equipment_arrival",
            "Source": "",
            "TicketDescription": f"{project_name}:开办及设备到场前施工",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "keketan,youngshi,v_mtdcwang",
                "Creator": "youngshi",
                "Deal": "youngshi"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {
            'project_start_info': res
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def construction_application(self, project_name):
        """
        项目报建
        """
        processTitle = f"{project_name}:项目报建过程记录"
        data = {
            "CustomVariables": {
                "project_name": project_name
            },
            "ProcessDefinitionKey": "project_construction_application",
            "Source": "",
            "TicketDescription": f"{project_name}:项目报建过程记录",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "keketan;youngshi;v_vikyjiang",
                "Creator": "v_vikyjiang",
                "Deal": "v_vikyjiang"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {
            'construction_application_info': res
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def mains_power_special(self, project_name):
        """
        市电专项
        """
        processTitle = f"{project_name}:市电专项流程"
        data = {
            "CustomVariables": {
                "project_name": project_name
            },
            "ProcessDefinitionKey": "mains_power_special",
            "Source": "",
            "TicketDescription": f"{project_name}:市电专项流程",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "keketan;youngshi;v_vikyjiang",
                "Creator": "v_vikyjiang",
                "Deal": "v_vikyjiang"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {
            'mains_power_special_info': res
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def project_site_implementation(self, project_name, PM):
        """
        现场施工
        """

        processTitle = f"{project_name}:市电专项流程"
        data = {
            "CustomVariables": {
                "project_name": project_name,
                "handlers": PM
            },
            "ProcessDefinitionKey": "mains_power_special",
            "Source": "",
            "TicketDescription": f"{project_name}:市电专项流程",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "keketan,youngshi,v_mmywang",
                "Creator": "keketan,youngshi,v_mmywang",
                "Deal": "keketan,youngshi,v_mmywang"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {
            'mains_power_special_info': res
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
