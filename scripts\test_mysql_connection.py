#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MySQL连接的简化脚本
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mysql_connection():
    """测试MySQL连接"""
    try:
        # 这里需要根据实际配置修改连接参数
        connection = pymysql.connect(
            host='localhost',  # 需要修改为实际的数据库主机
            user='root',       # 需要修改为实际的用户名
            password='',       # 需要修改为实际的密码
            database='tbconstruct',  # 数据库名
            charset='utf8mb4'
        )
        
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 测试查询
        cursor.execute("SELECT COUNT(*) as count FROM consolidated_equipment_data")
        result = cursor.fetchone()
        logger.info(f"consolidated_equipment_data表中有 {result['count']} 条记录")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    test_mysql_connection()
