import datetime
import math
import os
import re
from collections import defaultdict
from typing import Union, Any

import pandas as pd
from dateutil.relativedelta import relativedelta
from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from pypinyin import pinyin, Style

from biz.construction_process.cos_lib import COSLib
from biz.project_interface.progress_management import ProgressManagement


class ProjectsUnderConstructionInPark(object):
    def data_query(self):
        try:
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = "SELECT campus,state,delivery_rack FROM construction_evaluation_data"

            result = db.get_all(query_sql)
            data = []
            for row in result:
                campus = row.get('campus')
                state = row.get('state')
                delivery_rack = row.get('delivery_rack')
                if state == '在建':
                    data.append({
                        'name': campus,
                        'value': delivery_rack,
                    })
            return data
        except Exception as e:
            print(f"[ERROR] data_query 数据库查询失败: {e}")
            return []

    def tabular_data(self):
        try:
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = "SELECT campus,trans_former_station,external_electrical_capacitytotal," \
                        "external_power_utilization_rate,ability_comment,ability_comment_rest," \
                        "estimate_construction_cycle,construction_cycle_affect_factor,annual_electricity_consumption" \
                        " FROM park_resources"
            result = db.get_all(query_sql)
            data = []
            for row in result:
                campus = row.get('campus')
                trans_former_station = row.get('trans_former_station')
                external_electrical_capacitytotal = row.get('external_electrical_capacitytotal')
                external_power_utilization_rate = row.get('external_power_utilization_rate')
                ability_comment = row.get('ability_comment')
                ability_comment_rest = row.get('ability_comment_rest')
                estimate_construction_cycle = row.get('estimate_construction_cycle')
                construction_cycle_affect_factor = row.get('construction_cycle_affect_factor')
                annual_electricity_consumption = row.get('annual_electricity_consumption')
                data.append({
                    'Campus': campus,
                    'TransformerStation': trans_former_station,
                    'ExternalElectricalCapacitytotal': external_electrical_capacitytotal,
                    'ExternalPowerUtilizationRate': external_power_utilization_rate,
                    'AbilityComment': ability_comment,
                    'AbilityCommentRest': ability_comment_rest,
                    'EstimateConstructionCycle': estimate_construction_cycle,
                    'ConstructionCycleAffectFactor': construction_cycle_affect_factor,
                    'AnnualElectricityConsumption': annual_electricity_consumption
                })
            return data
        except Exception as e:
            print(f"[ERROR] tabular_data 数据库查询失败: {e}")
            return []

    def supplier_resource_inquiry(self):
        try:
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = "SELECT area,campus,project,delivery_target,state " \
                        "FROM supplier_resources "
            result = db.get_all(query_sql)
            data = {}
            for row in result:
                area = row.get('area')
                campus = row.get('campus')
                project = row.get('project')
                delivery_target = row.get('delivery_target')
                state = row.get('state')

                if area not in data:
                    data[area] = {'campusList': []}

                # 检查 project 是否为空
                if not project:  # 如果 project 为空，跳过该行
                    continue

                campus_list = data[area]['campusList']
                campus_index = next((index for (index, d) in enumerate(campus_list) if d['campusName'] == campus), None)

                if campus_index is None:
                    campus_list.append({'campusName': campus, 'projectList': []})
                    campus_index = len(campus_list) - 1

                project_list = campus_list[campus_index]['projectList']
                project_index = next((index for (index, d) in enumerate(project_list) if d['projectName'] == project), None)

                if project_index is None:
                    project_list.append({'projectName': project, 'months': {}})
                    project_index = len(project_list) - 1
                if delivery_target is not None and '年' in delivery_target and '月' in delivery_target:
                    months = project_list[project_index]['months']
                    month = int(delivery_target.split('年')[1].split('月')[0])
                    year = delivery_target.split('年')[0]
                    formatted_month = f'{year}year{month}month'
                    months[formatted_month] = f'{project}, {state}'
                else:
                    # 处理异常情况，例如给 months 赋默认值或者抛出异常
                    months = {}  # 或者 raise Exception("Invalid delivery_target value")

            formatted_data = []
            for area, area_data in data.items():
                campus_list = area_data['campusList']
                formatted_campus_list = []
                for campus_data in campus_list:
                    project_list = campus_data['projectList']
                    formatted_project_list = []
                    for project_data in project_list:
                        formatted_project_list.append(project_data['months'])
                    formatted_campus_list.append({
                        'campusName': campus_data['campusName'],
                        'projectList': formatted_project_list
                    })
                formatted_data.append({
                    'area': area,
                    'campusList': formatted_campus_list
                })
            return formatted_data
        except Exception as e:
            print(f"[ERROR] supplier_resource_inquiry 数据库查询失败: {e}")
            return []

    def check_if_there_are_suppliers(self, type):
        """
            有无供应商
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT area,campus,project,state,integrators,project_manager,supervision,examination,AHU," \
                    "low_voltage_cabinet,medium_voltage_cabinet,cabinet,battery,chaifa,transformer," \
                    "headboard,PDU,requirement FROM supplier_resources"
        result = db.get_all(query_sql)

        data = {
            "有供应商": [],
            "无供应商": [],
            "常规需求": [],
            "GPU需求": []
        }

        for row in result:
            campus = row['campus']
            project = row['project']
            state = row['state']
            integrators = row['integrators']
            project_manager = row['project_manager']
            supervision = row['supervision']
            examination = row['examination']
            AHU = row['AHU']
            low_voltage_cabinet = row['low_voltage_cabinet']
            medium_voltage_cabinet = row['medium_voltage_cabinet']
            cabinet = row['cabinet']
            battery = row['battery']
            chaifa = row['chaifa']
            transformer = row['transformer']
            headboard = row['headboard']
            PDU = row['PDU']

            # 检查 project 是否为空
            if not project:  # 如果 project 为空，跳过该行
                continue

            if integrators == 'NA' or project_manager == 'NA' or supervision == 'NA' or examination == 'NA' or \
                    AHU == 'NA' or low_voltage_cabinet == 'NA' or medium_voltage_cabinet == 'NA' or \
                    battery == 'NA' or chaifa == 'NA' or transformer == 'NA' or headboard == 'NA' or \
                    PDU == 'NA' or cabinet == 'NA':
                data["无供应商"].append([campus, f"{project}, {state}"])
            else:
                data["有供应商"].append([campus, f"{project}, {state}"])

        for item in result:
            campus = item.get('campus')
            project = item.get('project')
            state = item.get('state')
            requirement = item.get('requirement')

            # 检查 project 是否为空
            if not project:  # 如果 project 为空，跳过该行
                continue

            # 检查 requirement 是否为空
            if not requirement:  # 如果 requirement 为空，跳过该行
                continue

            if requirement == 'CG':
                data["常规需求"].append([campus, f"{project}, {state}"])
            elif requirement == 'GPU':
                data["GPU需求"].append([campus, f"{project}, {state}"])

        return data

    def find_no_vendor_data(self, project, campus):
        """
            供应商展示
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT integrators,project_manager,supervision,examination,AHU," \
                    "low_voltage_cabinet,medium_voltage_cabinet,battery,chaifa,transformer,cabinet," \
                    f"headboard,PDU FROM supplier_resources WHERE project = '{project}' AND campus = '{campus}'"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            integrators = row['integrators']
            project_manager = row['project_manager']
            supervision = row['supervision']
            examination = row['examination']
            AHU = row['AHU']
            low_voltage_cabinet = row['low_voltage_cabinet']
            medium_voltage_cabinet = row['medium_voltage_cabinet']
            cabinet = row['cabinet']
            battery = row['battery']
            chaifa = row['chaifa']
            transformer = row['transformer']
            headboard = row['headboard']
            PDU = row['PDU']
            data.append({
                'integrators': integrators,
                'project_manager': project_manager,
                'supervision': supervision,
                'examination': examination,
                'AHU': AHU,
                'low_voltage_cabinet': low_voltage_cabinet,
                'medium_voltage_cabinet': medium_voltage_cabinet,
                'cabinet': cabinet,
                'battery': battery,
                'chaifa': chaifa,
                'transformer': transformer,
                'headboard': headboard,
                'PDU': PDU,
            })

        return data

    def power_data(self, campasName):
        """
            园区资源——电力模板
        """
        db = mysql.new_mysql_instance("tbconstruct")
        if campasName:
            query_sql = "SELECT campus, superior_station, external_power_lines," \
                        "outer_line_diameter, DTU, external_electrical_mode," \
                        "sum(external_electrical_capacity) as external_electrical_capacity " \
                        f"FROM summary_data WHERE campus = '{campasName}' " \
                        "GROUP BY campus, superior_station,external_power_lines, " \
                        "outer_line_diameter, DTU, external_electrical_mode"
        else:
            query_sql = "SELECT campus, superior_station, external_power_lines," \
                        "outer_line_diameter, DTU, external_electrical_mode," \
                        "sum(external_electrical_capacity) as external_electrical_capacity " \
                        "FROM summary_data " \
                        "GROUP BY campus, superior_station,external_power_lines, " \
                        "outer_line_diameter, DTU, external_electrical_mode"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            campus = row['campus']
            superior_station = row['superior_station']
            external_power_lines = row['external_power_lines']
            outer_line_diameter = row['outer_line_diameter']
            DTU = row['DTU']
            external_electrical_mode = row['external_electrical_mode']
            external_electrical_capacity = row['external_electrical_capacity']
            external_power_lines = ', '.join(external_power_lines.split(',')) if external_power_lines else ''
            outer_line_diameter = ', '.join(outer_line_diameter.split(' ')) if outer_line_diameter else ''
            external_electrical_mode = ', '.join(
                external_electrical_mode.split(',')) if external_electrical_mode else ''
            data.append({
                'Campus': campus,
                'SuperiorStation': superior_station,
                'ExternalPowerLines': external_power_lines,
                'OuterLineDiameter': outer_line_diameter,
                'DTU': DTU,
                'ExternalElectricalMode': external_electrical_mode,
                'ExternalElectricalCapacity': external_electrical_capacity
            })
        return data

    def evaluative_module(self):
        """
            园区资源--能评模块
        """
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, number_planned_items, can_evaluate_satisfaction, " \
                    "ability_comment, occupied_volume, ability_comment_rest, occupancy_energy_evaluation_index " \
                    "FROM park_resources"
        result = tb_db.get_all(query_sql)
        data = []
        for row in result:
            campus = row.get('campus')
            number_planned_items = row.get('number_planned_items')
            can_evaluate_satisfaction = row.get('can_evaluate_satisfaction')
            ability_comment = row.get('ability_comment')
            occupied_volume = row.get('occupied_volume')
            ability_comment_rest = row.get('ability_comment_rest')
            occupancy_energy_evaluation_index = row.get('occupancy_energy_evaluation_index')

            data.append({
                'campus': campus,
                'number_planned_items': number_planned_items,
                'can_evaluate_satisfaction': can_evaluate_satisfaction,
                'ability_comment': ability_comment,
                'occupied_volume': occupied_volume,
                'ability_comment_rest': ability_comment_rest,
                'occupancy_energy_evaluation_index': occupancy_energy_evaluation_index,
            })

        return data

    def installation_period(self):
        """
            园区资源--安装周期
        """
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, " \
                    "external_mains_construction_time," \
                    "integration_equipment_service_bidding," \
                    "construction_application_time," \
                    "standard_construction_cycle," \
                    "standard_construction_process," \
                    "effect_of_winter_application," \
                    "winter_application_duration," \
                    "winter_shi_cross_spring_festival_specific_situation," \
                    "wxzc_bjhg_ygjszq,wxzc_sxty_ygjszq," \
                    "xzc_bjhg_ygjszq,xzc_sxty_ygjszq " \
                    "FROM influence_factor"

        result = tb_db.get_all(query_sql)
        data = []
        for row in result:
            campus = row.get('campus')
            external_mains_construction_time = row.get('external_mains_construction_time')
            integration_equipment_service_bidding = row.get('integration_equipment_service_bidding')
            construction_application_time = row.get('construction_application_time')
            standard_construction_cycle = row.get('standard_construction_cycle')
            standard_construction_process = row.get('standard_construction_process')
            effect_of_winter_application = row.get('effect_of_winter_application')
            winter_application_duration = row.get('winter_application_duration')
            winter_shi_cross_spring_festival_specific_situation = row.get(
                'winter_shi_cross_spring_festival_specific_situation')
            wxzc_bjhg_ygjszq = row.get('wxzc_bjhg_ygjszq')
            wxzc_sxty_ygjszq = row.get('wxzc_sxty_ygjszq')
            xzc_bjhg_ygjszq = row.get('xzc_bjhg_ygjszq')
            xzc_sxty_ygjszq = row.get('xzc_sxty_ygjszq')

            data.append({
                'campus': campus,
                'external_mains_construction_time': external_mains_construction_time,
                'integration_equipment_service_bidding': integration_equipment_service_bidding,
                'construction_application_time': construction_application_time,
                'standard_construction_cycle': standard_construction_cycle,
                'standard_construction_process': standard_construction_process,
                'effect_of_winter_application': effect_of_winter_application,
                'winter_application_duration': winter_application_duration,
                'winter_shi_cross_spring_festival_specific_situation':
                    winter_shi_cross_spring_festival_specific_situation,
                'wxzc_bjhg_ygjszq': wxzc_bjhg_ygjszq,
                'wxzc_sxty_ygjszq': wxzc_sxty_ygjszq,
                'xzc_bjhg_ygjszq': xzc_bjhg_ygjszq,
                'xzc_sxty_ygjszq': xzc_sxty_ygjszq
            })

        return data

    def supplier_inquiry(self, project_state=None, campus_name=None, delivery_target=None):
        """
            供应商数据
        """
        conditions = []
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT " \
                    "area, campus, project,delivery_target,integrators, project_manager, " \
                    "supervision,examination,AHU,low_voltage_cabinet, medium_voltage_cabinet," \
                    "battery,chaifa,cabinet,transformer,headboard,PDU " \
                    "FROM supplier_resources"

        if campus_name:
            conditions.append(f"campus = '{campus_name}'")

        if delivery_target:
            start_date, end_date = delivery_target
            conditions.append(
                f"STR_TO_DATE(delivery_target, '%Y年%m月%d日') BETWEEN STR_TO_DATE('{start_date}', '%Y年%m月%d日') "
                f"AND STR_TO_DATE('{end_date}', '%Y年%m月%d日')")

        if project_state:
            conditions.append(f"state = '{project_state}'")

        condition_str = " AND ".join(conditions) if conditions else ""  # 构建条件字符串

        if condition_str:
            query_sql += f" WHERE {condition_str}"

        result = db.get_all(query_sql)

        data = []
        if result:
            for row in result:
                area = row.get('area')
                campus = row.get('campus')
                project = row.get('project')
                delivery_target = row.get('delivery_target')
                integrators = row.get('integrators')
                project_manager = row.get('project_manager')
                supervision = row.get('supervision')
                examination = row.get('examination')
                AHU = row.get('AHU')
                low_voltage_cabinet = row.get('low_voltage_cabinet')
                medium_voltage_cabinet = row.get('medium_voltage_cabinet')
                battery = row.get('battery')
                chaifa = row.get('chaifa')
                cabinet = row.get('cabinet')
                transformer = row.get('transformer')
                headboard = row.get('headboard')
                PDU = row.get('PDU')

                # 检查 project 是否为空
                if not project:  # 如果 project 为空，跳过该行
                    continue

                if campus == '天津高新' and project == '天津复建':
                    continue

                existing_area = next((a for a in data if a['area'] == area), None)  # 检查区域是否已存在于数据中
                if existing_area:  # 如果区域已存在
                    existing_campus = next((c for c in existing_area['campusList'] if c['campusName'] == campus),
                                           None)  # 检查园区是否已存在于区域的园区列表中
                    if existing_campus:  # 如果园区已存在
                        existing_campus['projectList'].append({
                            'projectName': project,
                            'integrators': integrators,
                            'project_manager': project_manager,
                            'supervision': supervision,
                            'examination': examination,
                            'AHU': AHU,
                            'low_voltage_cabinet': low_voltage_cabinet,
                            'medium_voltage_cabinet': medium_voltage_cabinet,
                            'battery': battery,
                            'chaifa': chaifa,
                            'cabinet': cabinet,
                            'transformer': transformer,
                            'headboard': headboard,
                            'PDU': PDU,
                            'delivery_target': delivery_target
                        })  # 将项目名称添加到现有的项目列表中
                    else:  # 如果园区不存在
                        # 创建一个新的园区列表
                        existing_area['campusList'].append({
                            'campusName': campus,
                            'projectList': [{
                                'projectName': project,
                                'integrators': integrators,
                                'project_manager': project_manager,
                                'supervision': supervision,
                                'examination': examination,
                                'AHU': AHU,
                                'low_voltage_cabinet': low_voltage_cabinet,
                                'medium_voltage_cabinet': medium_voltage_cabinet,
                                'battery': battery,
                                'chaifa': chaifa,
                                'cabinet': cabinet,
                                'transformer': transformer,
                                'headboard': headboard,
                                'PDU': PDU,
                                'delivery_target': delivery_target
                            }]
                        })
                else:  # 如果区域不存在
                    # 创建一个新的区域列表
                    data.append({
                        'area': area,
                        'campusList': [{
                            'campusName': campus,
                            'projectList': [{
                                'projectName': project,
                                'integrators': integrators,
                                'project_manager': project_manager,
                                'supervision': supervision,
                                'examination': examination,
                                'AHU': AHU,
                                'low_voltage_cabinet': low_voltage_cabinet,
                                'medium_voltage_cabinet': medium_voltage_cabinet,
                                'battery': battery,
                                'chaifa': chaifa,
                                'cabinet': cabinet,
                                'transformer': transformer,
                                'headboard': headboard,
                                'PDU': PDU,
                                'delivery_target': delivery_target
                            }]
                        }]
                    })
        if data:
            # 安全地按照 projectName 进行排序
            try:
                if (len(data) > 0 and 'campusList' in data[0] and
                    len(data[0]['campusList']) > 0 and 'projectList' in data[0]['campusList'][0]):
                    data[0]['campusList'][0]['projectList'] = sorted(
                        data[0]['campusList'][0]['projectList'],
                        key=lambda x: x.get('projectName', '')
                    )
            except (IndexError, KeyError, TypeError) as e:
                # 记录错误但不中断程序执行
                print(f"[WARNING] 排序项目列表时出错: {e}")
        return data

    def supplier_form_inquiry(self):
        """
            供应商表格查询
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT area, campus, project_name,Integration_unit,delivery_rack, state,construction_mode " \
                    "FROM summary_data"
        result = db.get_all(query_sql)
        data = []
        isRed = False
        for row in result:
            area = row.get('area')
            campus = row.get('campus')
            project_name = row.get('project_name')
            Integration_unit = row.get('Integration_unit')
            delivery_rack = row.get('delivery_rack')
            state = row.get('state')
            construction_mode = row.get('construction_mode')

            if state and state == '评估中' or state == '建设中':
                gyszy_sql = "SELECT delivery_target,integrators,project_manager,supervision," \
                            "examination,AHU,low_voltage_cabinet," \
                            "medium_voltage_cabinet,cabinet,battery,chaifa,transformer,headboard,PDU " \
                            "FROM supplier_resources " \
                            f"WHERE campus='{campus}' AND project='{project_name}'"
                gyszy_result = db.get_all(gyszy_sql)
                for row in gyszy_result:
                    delivery_target = row.get('delivery_target')
                    integrators = row.get('integrators')
                    project_manager = row.get('project_manager')
                    supervision = row.get('supervision')
                    examination = row.get('examination')
                    AHU = row.get('AHU')
                    low_voltage_cabinet = row.get('low_voltage_cabinet')
                    medium_voltage_cabinet = row.get('medium_voltage_cabinet')
                    cabinet = row.get('cabinet')
                    battery = row.get('battery')
                    chaifa = row.get('chaifa')
                    transformer = row.get('transformer')
                    headboard = row.get('headboard')
                    PDU = row.get('PDU')

                    # 获取当前日期
                    now = datetime.datetime.now()
                    if delivery_target:
                        try:
                            delivery_target_datetime = datetime.datetime.strptime(delivery_target, '%Y年%m月%d日')
                            # 计算当前时间与 delivery_target 的时间差
                            time_diff = delivery_target_datetime - now
                        except (ValueError, TypeError) as e:
                            print(f"[WARNING] 日期解析失败: {e}, delivery_target: {delivery_target}")
                            continue  # 跳过这个记录
                        if (integrators == 'NA' or project_manager == 'NA' or supervision == 'NA' or AHU == 'NA' or
                            low_voltage_cabinet == 'NA' or medium_voltage_cabinet == 'NA' or battery == 'NA' or
                            chaifa == 'NA' or transformer == 'NA' or headboard == 'NA' or
                            PDU == 'NA' or cabinet == 'NA') or \
                                (examination == 'NA' and (
                                        datetime.timedelta(days=60) >= time_diff >= datetime.timedelta(days=0))):
                            isRed = True
                        else:
                            isRed = False

            existing_area: dict[str, Union[bool, Any]] = next((a for a in data if a['area'] == area), None)
            if existing_area:  # 如果区域已存在
                existing_campus = next((c for c in existing_area['campus'] if c['campusName'] == campus),
                                       None)  # 检查园区是否已存在于区域的园区列表中
                if existing_campus:  # 如果园区已存在
                    if Integration_unit == '':
                        Integration_unit = '暂无供应商'
                    existing_campus['project'].append({
                        'ProjectName': project_name,
                        'IntegrationUnit': Integration_unit,
                        'DeliveryRack': delivery_rack,
                        'State': state,
                        'ConstructionMode': construction_mode,
                        'StateConstructionMode': state + construction_mode,
                        'isRed': isRed,
                    })  # 将项目名称添加到现有的项目列表中
                else:  # 如果园区不存在
                    existing_area['campus'].append({  # 创建一个新的园区列表
                        'campusName': campus,
                        'project': [{
                            'ProjectName': project_name,
                            'IntegrationUnit': Integration_unit,
                            'DeliveryRack': delivery_rack,
                            'State': state,
                            'ConstructionMode': construction_mode,
                            'StateConstructionMode': state + construction_mode,
                            'isRed': isRed,
                        }]
                    })
            else:  # 如果区域不存在
                data.append({  # 创建一个新的区域列表
                    'area': area,
                    'campus': [{
                        'campusName': campus,
                        'project': [{
                            'ProjectName': project_name,
                            'IntegrationUnit': Integration_unit,
                            'DeliveryRack': delivery_rack,
                            'State': state,
                            'ConstructionMode': construction_mode,
                            'StateConstructionMode': state + construction_mode,
                            'isRed': isRed,
                        }]
                    }]
                })
        # 过滤掉 area 为空的项
        data = [d for d in data if d['area']]
        return data

    def dynamic_table_header_generation(self):
        """
            动态表头
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus,delivery_target FROM supplier_resources"
        result = db.get_all(query_sql)
        data = [
            {
                "prop": "area",
                "label": "区域",
                "minWidth": "100",
            },
            {
                "prop": "campusName",
                "label": "园区",
                "minWidth": "100",
            },
            {
                "prop": "projectName",
                "label": "项目",
                "minWidth": "100",
            }
        ]
        delivery_targets = [row.get("delivery_target") for row in result if row.get("delivery_target")]
        campus = [row.get("campus") for row in result if row.get("campus")]
        if delivery_targets and campus:
            try:
                min_date = min(delivery_targets)
                max_date = max(delivery_targets)
                min_date = datetime.datetime.strptime(min_date, "%Y年%m月%d日")
                min_date -= relativedelta(months=6)  # 推七个月
                max_date = datetime.datetime.strptime(max_date, "%Y年%m月%d日")
            except (ValueError, TypeError) as e:
                print(f"[WARNING] 动态表头日期解析失败: {e}")
                return None

            # 获取园区对应的have_or_not_external_resources
            campus_name = campus[0]  # 假设只有一个园区
            wd_sql = f"SELECT have_or_not_external_resources FROM influence_factor WHERE campus='{campus_name}'"
            wd_result = db.get_all(wd_sql)
            if wd_result:
                have_or_not_external_resources = wd_result[0].get("have_or_not_external_resources")
                if have_or_not_external_resources == "有":
                    min_date -= relativedelta(months=0)
                elif have_or_not_external_resources == "无":
                    min_date -= relativedelta(months=3)

            # 生成动态表头数据
            start_year = min_date.year
            end_year = max_date.year
            for year in range(start_year, end_year + 1):
                year_label = str(year) + "年"
                year_field = {
                    "prop": "",
                    "label": "需求交付时间：" + year_label,
                    "minWidth": "100",
                    "children": []
                }
                if year == start_year:
                    start_month = min_date.month
                else:
                    start_month = 1
                if year == end_year:
                    end_month = max_date.month
                else:
                    end_month = 12
                for month in range(start_month, end_month + 1):
                    label = str(month) + "月"
                    prop = str(year) + "year" + str(month) + "month"
                    month_field = {
                        "prop": prop,
                        "label": label,
                        "minWidth": "100",
                    }
                    year_field["children"].append(month_field)
                data.append(year_field)

            return data
        else:
            return None

    def supplier_resources_dynamic_table_header(self):
        """
            供应商资源动态表头
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus,delivery_target FROM supplier_resources"
        result = db.get_all(query_sql)
        data = [
            {
                "prop": "area",
                "label": "区域",
                "minWidth": "100",
            },
            {
                "prop": "campusName",
                "label": "园区",
                "minWidth": "100",
            }
        ]
        delivery_targets = [row.get("delivery_target") for row in result if row.get("delivery_target")]
        campus = [row.get("campus") for row in result if row.get("campus")]
        if delivery_targets and campus:
            try:
                min_date = min(delivery_targets)
                max_date = max(delivery_targets)
                min_date = datetime.datetime.strptime(min_date, "%Y年%m月%d日")
                max_date = datetime.datetime.strptime(max_date, "%Y年%m月%d日")
            except (ValueError, TypeError) as e:
                print(f"[WARNING] 供应商资源动态表头日期解析失败: {e}")
                return None

            # 获取园区对应的have_or_not_external_resources
            campus_name = campus[0]  # 假设只有一个园区
            wd_sql = f"SELECT have_or_not_external_resources FROM influence_factor WHERE campus='{campus_name}'"
            wd_result = db.get_all(wd_sql)
            if wd_result:
                have_or_not_external_resources = wd_result[0].get("have_or_not_external_resources")
                if have_or_not_external_resources == "有":
                    min_date -= relativedelta(months=0)
                elif have_or_not_external_resources == "无":
                    min_date -= relativedelta(months=3)

            # 生成动态表头数据
            start_year = min_date.year
            end_year = max_date.year
            for year in range(start_year, end_year + 1):
                year_label = str(year) + "年"
                year_field = {
                    "prop": "",
                    "label": "需求交付时间：" + year_label,
                    "minWidth": "100",
                    "children": []
                }
                if year == start_year:
                    start_month = min_date.month
                else:
                    start_month = 1
                if year == end_year:
                    end_month = max_date.month
                else:
                    end_month = 12
                for month in range(start_month, end_month + 1):
                    label = str(month) + "月"
                    prop = str(year) + "year" + str(month) + "month"
                    month_field = {
                        "prop": prop,
                        "label": label,
                        "minWidth": "100",
                    }
                    year_field["children"].append(month_field)
                data.append(year_field)

            return data
        else:
            return None

    def construction_plan(self):
        """
            建设规划
        """
        global have_or_not_external_resources
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT area, campus, project, delivery_target " \
                    "FROM supplier_resources "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            area = row['area']
            campus = row['campus']
            project = row['project']
            delivery_target = row['delivery_target']

            wd_sql = "SELECT have_or_not_external_resources " \
                     "FROM influence_factor " \
                     f"WHERE campus='{campus}'"
            wd_result = db.get_all(wd_sql)

            area_data = next((item for item in data if item['area'] == area), None)

            # 检查 project 是否为空
            if not project:  # 如果 project 为空，跳过该行
                continue

            if area_data is None:
                area_data = {'area': area, 'campusList': []}
                data.append(area_data)
            campus_list = area_data['campusList']
            campus_data = next((item for item in campus_list if item['campusName'] == campus), None)
            if campus_data is None:
                campus_data = {'campusName': campus, 'projectList': []}
                campus_list.append(campus_data)
            project_list = campus_data['projectList']
            project_data = next((item for item in project_list if item['projectName'] == project), None)
            if project_data is None:
                project_data = {'projectName': project}
                project_list.append(project_data)
            # 按照 projectName 进行排序
            project_list.sort(key=lambda x: x['projectName'])
            # 计算其他时间节点
            if delivery_target is not None \
                    and '年' in delivery_target \
                    and '月' in delivery_target \
                    and '日' in delivery_target:
                try:
                    delivery_date = datetime.datetime.strptime(delivery_target, "%Y年%m月%d日")
                except (ValueError, TypeError) as e:
                    print(f"[WARNING] 建设规划日期解析失败: {e}, delivery_target: {delivery_target}")
                    continue  # 跳过这个记录
                debug_date = delivery_date - relativedelta(months=1)
                warehouse_date1 = delivery_date - relativedelta(months=2)
                warehouse_date2 = delivery_date - relativedelta(months=3)
                completion_date1 = delivery_date - relativedelta(months=4)
                completion_date2 = delivery_date - relativedelta(months=5)
                po_date = delivery_date - relativedelta(months=6)
                if wd_result and all(row.get("have_or_not_external_resources") == "无" for row in wd_result):
                    po_date -= relativedelta(months=4)

                delivery_time = datetime.datetime.strptime(delivery_target, "%Y年%m月%d日")
                debug_time = datetime.datetime.strptime(debug_date.strftime("%Y年%m月%d日"), "%Y年%m月%d日")
                warehouse_time1 = datetime.datetime.strptime(warehouse_date1.strftime("%Y年%m月%d日"),
                                                             "%Y年%m月%d日")
                warehouse_time2 = datetime.datetime.strptime(warehouse_date2.strftime("%Y年%m月%d日"),
                                                             "%Y年%m月%d日")
                completion_time1 = datetime.datetime.strptime(completion_date1.strftime("%Y年%m月%d日"),
                                                              "%Y年%m月%d日")
                completion_time2 = datetime.datetime.strptime(completion_date2.strftime("%Y年%m月%d日"),
                                                              "%Y年%m月%d日")
                po_time = datetime.datetime.strptime(po_date.strftime("%Y年%m月%d日"), "%Y年%m月%d日")

                if debug_date.year < delivery_date.year:
                    project_data[f'{debug_time.year}year{debug_time.month}month'] = "送电调试"
                else:
                    project_data[f'{debug_time.year}year{debug_time.month}month'] = "送电调试"

                project_data[f'{delivery_time.year}year{delivery_time.month}month'] = f"测试/交付"

                if warehouse_date1.year < delivery_date.year:
                    project_data[f'{warehouse_time1.year}year{warehouse_time1.month}month'] = "方仓进场/安装"
                else:
                    project_data[f'{warehouse_time1.year}year{warehouse_time1.month}month'] = "方仓进场/安装"

                if warehouse_date2.year < delivery_date.year:
                    project_data[f'{warehouse_time2.year}year{warehouse_time2.month}month'] = "方仓进场/安装"
                else:
                    project_data[f'{warehouse_time2.year}year{warehouse_time2.month}month'] = "方仓进场/安装"

                if completion_date1.year < delivery_date.year:
                    project_data[f'{completion_time1.year}year{completion_time1.month}month'] = "生产/集成"
                else:
                    project_data[f'{completion_time1.year}year{completion_time1.month}month'] = "生产/集成"

                if completion_date2.year < delivery_date.year:
                    project_data[f'{completion_time2.year}year{completion_time2.month}month'] = "生产/集成"
                else:
                    project_data[f'{completion_time2.year}year{completion_time2.month}month'] = "生产/集成"

                if po_date.year < delivery_date.year:
                    project_data[f'{po_time.year}year{po_time.month}month'] = "需求/PO下发"
                else:
                    project_data[f'{po_time.year}year{po_time.month}month'] = "需求/PO下发"

        return data

    def risk_assessment(self, page_number, page_size, supplier_risk, assessable_risk, issuing_risk):
        """
            风险评估
        """
        db = mysql.new_mysql_instance("tbconstruct")

        rewd_sql = "SELECT project_name FROM risk_early_warning_data"
        sr_sql1 = "SELECT campus, project, delivery_target FROM supplier_resources"
        sr_sql2 = "SELECT campus, project, integrators, project_manager, supervision, examination, AHU, " \
                  "low_voltage_cabinet, medium_voltage_cabinet,battery, chaifa, cabinet, transformer, headboard, PDU " \
                  "FROM supplier_resources"
        jszq_sql = "SELECT campus, have_or_not_external_resources FROM influence_factor"
        pr_sql = "SELECT campus, can_evaluate_satisfaction FROM park_resources"

        rewd_result = db.get_all(rewd_sql)
        sr_result1 = db.get_all(sr_sql1)
        sr_result2 = db.get_all(sr_sql2)
        jszq_result = db.get_all(jszq_sql)
        pr_result = db.get_all(pr_sql)
        risk_project_list = []
        supplier_na_list = []  # 存储供应商资源为NA的项目
        pr_data = []
        supplier_dict = {
            'integrators': '集成商',
            'project_manager': '项管',
            'supervision': '监理',
            'examination': '测试',
            'AHU': 'AHU',
            'low_voltage_cabinet': '低压柜',
            'medium_voltage_cabinet': '中压柜',
            'battery': '电池',
            'chaifa': '柴发',
            'cabinet': '机柜',
            'transformer': '变压器',
            'headboard': '高压直流 &列头柜',
            'PDU': 'PDU'
        }

        # 遍历sr_result2，找出供应商资源为NA的项目
        for item in sr_result2:
            campus = item.get('campus')
            project = item.get('project')
            integrators = item.get('integrators')
            project_manager = item.get('project_manager')
            supervision = item.get('supervision')
            examination = item.get('examination')
            AHU = item.get('AHU')
            low_voltage_cabinet = item.get('low_voltage_cabinet')
            medium_voltage_cabinet = item.get('medium_voltage_cabinet')
            battery = item.get('battery')
            chaifa = item.get('chaifa')
            cabinet = item.get('cabinet')
            transformer = item.get('transformer')
            headboard = item.get('headboard')
            PDU = item.get('PDU')

            # 检查 project 是否为空
            if not project:  # 如果 project 为空，跳过该行
                continue

            if any(v == 'NA' for v in [integrators, project_manager, supervision, examination, AHU, low_voltage_cabinet,
                                       medium_voltage_cabinet, battery, chaifa, cabinet, transformer, headboard, PDU]):
                supplier_na_list.append({
                    'campus': campus, 'project': project, 'integrators': integrators,
                    'project_manager': project_manager,
                    'supervision': supervision, 'examination': examination, 'AHU': AHU,
                    'low_voltage_cabinet': low_voltage_cabinet, 'medium_voltage_cabinet': medium_voltage_cabinet,
                    'battery': battery, 'chaifa': chaifa, 'cabinet': cabinet, 'transformer': transformer,
                    'headboard': headboard, 'PDU': PDU
                })

        # 遍历查询结果，将数据存入列表
        for item in pr_result:
            campus = item.get('campus')
            can_evaluate_satisfaction = item.get('can_evaluate_satisfaction')
            pr_data.append({'campus': campus, 'can_evaluate_satisfaction': can_evaluate_satisfaction})

        # 添加需求/PO下发的项目到risk_project_list
        for item in sr_result1:
            sr_campus = item.get('campus')
            sr_project = item.get('project')
            sr_project_name = f'{sr_campus}{sr_project}'
            found = False
            for row in rewd_result:
                rewd_project_name = row.get('project_name')
                if sr_project_name == rewd_project_name:
                    found = True
                    break

            if not found and sr_project is not None:  # 仅添加非空的项目
                campus = item.get('campus')
                project_name = item.get('project_name')

                # 屏蔽南京江宁且project_name为B2-1和B2-2的项目
                if campus != '南京江宁' and project_name not in ['B2-1', 'B2-2']:
                    have_external_resources = None
                    for row in jszq_result:
                        if row.get('campus') == campus:
                            have_external_resources = row.get('have_or_not_external_resources')
                            break

                    # 初始化delivery_target变量
                    delivery_target = None

                    # 在相关部分修改如下
                    try:
                        if have_external_resources == '无':
                            delivery_target_str = item.get('delivery_target')
                            if delivery_target_str is not None:
                                delivery_date = datetime.datetime.strptime(delivery_target_str, '%Y年%m月%d日')
                                delivery_target = delivery_date - datetime.timedelta(days=10 * 30)
                                delivery_target = delivery_target.strftime('%Y-%m-%d')
                        elif have_external_resources == '有':
                            delivery_target_str = item.get('delivery_target')
                            if delivery_target_str is not None:
                                delivery_date = datetime.datetime.strptime(delivery_target_str, '%Y年%m月%d日')
                                delivery_target = delivery_date - datetime.timedelta(days=6 * 30)
                                delivery_target = delivery_target.strftime('%Y-%m-%d')
                    except (ValueError, TypeError) as e:
                        # 日期解析失败时记录错误并继续
                        print(f"[WARNING] 日期解析失败: {e}, delivery_target_str: {delivery_target_str}")
                        delivery_target = None

                    if delivery_target is not None:
                        # 现在 delivery_target 已经是 '%Y-%m-%d' 格式的字符串
                        target_date = datetime.datetime.strptime(delivery_target, '%Y-%m-%d')
                        current_date = datetime.datetime.now()
                        one_month_ago = current_date - datetime.timedelta(days=30)
                        if target_date <= one_month_ago:
                            risk_project_list.append({
                                'campus': sr_campus,
                                'project': sr_project,
                                'demand_distribution': '暂未下发需求'
                            })

        # 遍历supplier_na_list，将供应商资源为NA的字段添加到相同项目的字典中
        for item in supplier_na_list:
            campus = item.get('campus')
            project = item.get('project')
            # 屏蔽天津高新的campus
            if campus != '天津高新':
                found = False
                for project_item in risk_project_list:
                    if project_item['campus'] == campus and project_item['project'] == project:
                        supplier_resources = []
                        for key, value in item.items():
                            if value == 'NA' and key in supplier_dict:
                                supplier_resources.append(supplier_dict[key])
                        project_item['supplier_resources'] = f'无：{", ".join(supplier_resources)}'
                        found = True
                        break
                if not found:
                    supplier_resources = []
                    for key, value in item.items():
                        if value == 'NA' and key in supplier_dict:
                            supplier_resources.append(supplier_dict[key])
                    risk_project_list.append({
                        'campus': campus,
                        'project': project,
                        'supplier_resources': f'无：{", ".join(supplier_resources)}'
                    })

        # 遍历risk_project_list列表
        for project_item in risk_project_list:
            campus = project_item['campus']
            found = False

            # 在pr_data列表中查找相同campus的数据
            for pr_item in pr_data:
                if pr_item['campus'] == campus:
                    found = True

                    # 判断can_evaluate_satisfaction是否不满足条件
                    if pr_item['can_evaluate_satisfaction'] != '满足':
                        project_item['park_early_warning'] = '无能评'
        # 计算总数
        total1 = len(risk_project_list)
        # 计算总页数
        page_size = int(page_size)
        page_number = int(page_number)  # 将字符串转换为整数
        total_pages = (len(risk_project_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total1)
        page_list1 = risk_project_list[start_index:end_index]

        # 筛选
        filtered_results = []
        if supplier_risk == '有风险':
            filtered_results = [item for item in risk_project_list if 'supplier_resources' in item]
        elif supplier_risk == '无风险':
            filtered_results = [item for item in risk_project_list if 'supplier_resources' not in item]

        if assessable_risk == '有风险':
            filtered_results = [item for item in risk_project_list if 'park_early_warning' in item]
        elif assessable_risk == '无风险':
            filtered_results = [item for item in risk_project_list if 'park_early_warning' not in item]

        if issuing_risk == '有风险':
            filtered_results = [item for item in risk_project_list if 'demand_distribution' in item]
        elif issuing_risk == '无风险':
            filtered_results = [item for item in risk_project_list if 'demand_distribution' not in item]

        # 筛选结果分页
        # 计算总数
        total2 = len(filtered_results)
        # 计算总页数
        page_size = int(page_size)
        page_number = int(page_number)  # 将字符串转换为整数
        total_pages = (len(filtered_results) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total2)
        page_list2 = filtered_results[start_index:end_index]

        if supplier_risk == '' and assessable_risk == '' and issuing_risk == '':
            return {
                'risk_project_list': page_list1,
                'total': total1
            }
        else:
            return {
                'risk_project_list': page_list2,
                'total': total2
            }


class DataProcessing(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_splicing(self):
        """
            数据拼接
        """
        campus = self.ctx.variables.get('campus')
        project = self.ctx.variables.get('project')
        # 拼接数据
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT delivery_target " \
                    f"FROM supplier_resources " \
                    f"WHERE campus = '{campus}' " \
                    f"AND project = '{project}'"

        query_result = db.get_all(query_sql)
        rich_text_template = "<ol>\n"
        for row in query_result:
            delivery_target = row.get('delivery_target')
            if delivery_target is None:
                continue  # 跳过该次循环
            demand_delivery_date = datetime.datetime.strptime(delivery_target, '%Y年%m月%d日')  # 将字符串转换为日期对象
            if campus in ['重庆泰和', '天津高新', '南京江宁', '仪征东升']:
                delivery_time = demand_delivery_date - relativedelta(months=6)  # 减去6个月
            else:
                delivery_time = demand_delivery_date - relativedelta(months=10)  # 减去9个月
            delivery_time = delivery_time.strftime('%Y-%m-%d')  # 将日期对象转换为字符串格式

            rich_text_template += (
                f"<li>{campus}园区"
                f"{project}项目"
                f"计划{demand_delivery_date.strftime('%Y-%m-%d')}时间交付，"
                f"按计划需要{delivery_time}需求下发，请关注！</li>\n"
            )

        rich_text_template += "</ol>"

        variables = {
            'query_result': query_result,
            'rich_text_template': rich_text_template,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class MailInformationEditing(AjaxTodoBase):
    """
        邮件信息编辑
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        mail_title = process_data.get('mail_title')
        email_receiver = process_data.get('email_receiver')
        email_Cc = process_data.get('email_Cc')
        rich_text_template = process_data.get('rich_text_template')

        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"

        variables = {
            'mail_title': mail_title,
            'email_receiver': email_receiver,
            'email_Cc': email_Cc,
            'rich_text_template': rich_text_template,
            'email_Cc_list': email_Cc_list,
            'email_receiver_list': email_receiver_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class LeaderExamineApprove(AjaxTodoBase):
    """
        Leader审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        examine_approve = process_data.get('examine_approve')
        remark = process_data.get('remark')

        variables = {
            'examine_approve': examine_approve,
            'remark': remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class Mailing(object):
    """
        风险预警邮件发送
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def send(self):
        mail_title = self.ctx.variables.get('mail_title')
        email_Cc_list = self.ctx.variables.get('email_Cc_list')
        email_receiver_list = self.ctx.variables.get('email_receiver_list')
        rich_text_template = self.ctx.variables.get('rich_text_template')

        tof.send_email(sendTitle=mail_title, msgContent=rich_text_template,
                       sendTo=email_receiver_list, sendCopy=email_Cc_list)

        flow.complete_task(self.ctx.task_id, variables={})


class ProjectInformationQuery(object):
    """
        供应商资源查询
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def database_query(self):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT * FROM supplier_resources"
        result = db.get_all(query_sql)
        data_list = []
        for row in result:
            id = row.get("id")
            area = row.get("area")
            campus = row.get("campus")
            project = row.get("project")
            delivery_target = row.get("delivery_target")
            state = row.get("state")
            integrators = row.get("integrators")
            project_manager = row.get("project_manager")
            supervision = row.get("supervision")
            examination = row.get("examination")
            AHU = row.get("AHU")
            chaifa = row.get("chaifa")
            low_voltage_cabinet = row.get("low_voltage_cabinet")
            medium_voltage_cabinet = row.get("medium_voltage_cabinet")
            battery = row.get("battery")
            cabinet = row.get("cabinet")
            transformer = row.get("transformer")
            headboard = row.get("headboard")
            PDU = row.get("PDU")
            data_list.append({
                'id': id,
                'area': area,
                'campus': campus,
                'project': project,
                'delivery_target': delivery_target,
                'state': state,
                'integrators': integrators,
                'project_manager': project_manager,
                'supervision': supervision,
                'examination': examination,
                'AHU': AHU,
                'chaifa': chaifa,
                'low_voltage_cabinet': low_voltage_cabinet,
                'medium_voltage_cabinet': medium_voltage_cabinet,
                'battery': battery,
                'cabinet': cabinet,
                'transformer': transformer,
                'headboard': headboard,
                'PDU': PDU
            })
        # 按照 campus 和 project 进行排序
        data_list = sorted(data_list, key=lambda x: (x['campus'], x['project']))
        variables = {
            'data_list': data_list,
            'data_list2': data_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ModifySupplierResources(AjaxTodoBase):
    """
        供应商资源修改
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        global update_fields, update_sql, params
        data_list2 = self.ctx.variables.get('data_list2')
        data_list = process_data.get('data_list')
        # 初始化一个空列表来存储不同的字段和内容
        differences = []

        # 遍历第一个列表的每个字典
        for data_dict, data_dict2 in zip(data_list, data_list2):
            # 初始化一个空字典来存储不同的字段和内容
            diff_dict = {'campus': data_dict.get('campus'), 'project': data_dict.get('project')}

            # 遍历字典的键值对，检查每个字段是否相同
            for key, value in data_dict.items():
                if key != 'campus' and key != 'project' and data_dict2.get(key) != value:
                    diff_dict[key] = value

            # 检查字典中是否包含'project'键
            if 'project' in diff_dict:
                differences.append(diff_dict)

        # 打印不相同的字段和内容以及额外的"campus"和"project"字段
        for diff in differences:
            # 获取需要更新的字段和内容
            update_fields = []
            params = []
            for key, value in diff.items():
                if key != 'campus' and key != 'project':
                    update_fields.append(f"{key} = %s")
                    params.append(value)

            # 检查是否存在需要更新的字段
            if update_fields:
                # 构建更新语句
                update_sql = f"UPDATE supplier_resources " \
                             f"SET {', '.join(update_fields)} " \
                             f"WHERE campus = %s " \
                             f"AND project = %s"
                params.extend([diff['campus'], diff['project']])

                # 执行更新语句
                db = mysql.new_mysql_instance("tbconstruct")
                db.execute(update_sql, tuple(params))

                # 检查是否涉及integrators字段
                if 'integrators' in update_sql:
                    # 构建额外的更新语句
                    integrators_update_sql1 = f"UPDATE summary_data " \
                                              f"SET Integration_unit = %s " \
                                              f"WHERE campus = %s " \
                                              f"AND project_name = %s"
                    # 构建额外的更新语句
                    integrators_update_sql2 = f"UPDATE construction_evaluation_data " \
                                              f"SET supplier_resources = %s " \
                                              f"WHERE campus = %s " \
                                              f"AND project_name = %s"
                    integrators_params = [diff['integrators'], diff['campus'], diff['project']]

                    # 执行额外的更新语句
                    db.execute(integrators_update_sql1, tuple(integrators_params))
                    db.execute(integrators_update_sql2, tuple(integrators_params))
                if 'state' in update_sql:
                    # 构建额外的更新语句
                    integrators_update_sql1 = f"UPDATE summary_data " \
                                              f"SET state = %s " \
                                              f"WHERE campus = %s " \
                                              f"AND project_name = %s"
                    # 构建额外的更新语句
                    integrators_update_sql2 = f"UPDATE construction_evaluation_data " \
                                              f"SET state = %s " \
                                              f"WHERE campus = %s " \
                                              f"AND project_name = %s"
                    integrators_params = [diff['state'], diff['campus'], diff['project']]

                    # 执行额外的更新语句
                    db.execute(integrators_update_sql1, tuple(integrators_params))
                    db.execute(integrators_update_sql2, tuple(integrators_params))
        variables = {
            'update_sql': update_sql,
            'params': tuple(params),
            'update_fields': update_fields
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ConstructionTable(object):
    """
        建设工作台
    """

    def form_query(self, dcopsTicketId=None, Campus=None, ProjectName=None, ItemNumber=None, DemandDistribution=None,
                   DemandDelivery=None, ConstructionMode=None, PM=None, deputy_PM=None, state=None, page_number=None,
                   page_size=None):
        db = mysql.new_mysql_instance("tbconstruct")
        page_number = int(page_number)
        page_size = int(page_size)
        conditions = []
        conditions2 = []
        data = []

        if dcopsTicketId:
            conditions.append(f"dcopsTicketId = '{dcopsTicketId}'")
        if Campus:
            conditions.append(f"campus = '{Campus}'")
        if ProjectName:
            conditions.append(f"project = '{ProjectName}'")
        if ItemNumber:
            conditions.append(f"item_number = '{ItemNumber}'")
        if DemandDistribution:
            start_date, end_date = DemandDistribution
            conditions.append(f"STR_TO_DATE(demand_distribution, '%Y-%m-%d') BETWEEN '{start_date}' AND '{end_date}'")
        if DemandDelivery:
            start_date, end_date = DemandDelivery
            conditions.append(f"STR_TO_DATE(demand_delivery, '%Y-%m-%d') BETWEEN '{start_date}' AND '{end_date}'")
        if ConstructionMode:
            conditions.append(f"construction_mode = '{ConstructionMode}'")
        if PM:
            conditions.append(f"PM = '{PM}'")
        if state:
            conditions2.append(f"state = '{state}'")
        if deputy_PM:
            conditions.append(f"deputy_PM = '{deputy_PM}'")

        condition_str = " AND ".join(conditions) if conditions else ""  # 构建条件字符串
        condition_str2 = " AND ".join(conditions2) if conditions2 else ""  # 构建条件字符串

        # 项目基本信息查询
        query_sql = "SELECT * FROM risk_early_warning_data"
        if condition_str:
            query_sql += f" WHERE {condition_str}"

        # 项目状态查询
        state_sql = "SELECT * FROM summary_data"
        if condition_str2:
            state_sql += f" WHERE {condition_str2}"

        # 人员信息sql查询
        personal_information_sql = "SELECT project_name, architecture, beautification, tree_sutra " \
                                   "FROM project_team_information_data "

        problem_list = db.get_all(query_sql)
        state_list = db.get_all(state_sql)
        personal_information = db.get_all(personal_information_sql)

        # 在 problem_list 中添加 state 字段
        for problem in problem_list:
            project_name = problem.get('project_name')  # 假设 problem_list 中有 project_name 字段
            # 遍历 state_list，查找匹配的 campus 和 project
            for state_item in state_list:
                campus = state_item.get('campus')
                project = state_item.get('project_name')
                # 检查 campus + project 是否与 problem_list 中的 project_name 匹配
                if f"{campus}{project}" == project_name:
                    problem['state'] = state_item['state']  # 添加 state 字段
                    break  # 找到匹配后可以跳出循环
            else:
                problem['state'] = None  # 如果没有找到匹配项，设置为 None

        # 过滤符合 state 查询条件的项目
        if state:
            problem_list = [problem for problem in problem_list if problem.get('state') == state]

        # 对剩余的数据按照 demand_delivery 进行排序
        sorted_data = sorted(problem_list, key=lambda x: x['demand_delivery'], reverse=True)

        # 分页操作
        total = len(sorted_data)
        total_pages = (total + page_size - 1) // page_size
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = sorted_data[start_index:end_index]

        # 构建返回的数据
        for row in page_list:
            demand_distribution = row.get('demand_distribution')
            demand_delivery = row.get('demand_delivery')
            project = row.get('project')
            campus = row.get('campus')

            # 初始化一个标志，表示是否找到匹配的项目
            found_match = False

            for item in personal_information:
                project_name = item.get('project_name')
                architecture = item.get('architecture')
                beautification = item.get('beautification')
                tree_sutra = item.get('tree_sutra')
                if campus + project == project_name:
                    found_match = True  # 找到匹配项
                    # 找到匹配项
                    data.append({
                        'dcopsTicketId': row.get('dcopsTicketId'),
                        'ProjectName': project,
                        'Area': row.get('region'),
                        'Province': row.get('province'),
                        'Campus': campus,
                        'DemandDistribution': demand_distribution,
                        'DemandDelivery': demand_delivery,
                        'ElectricPowerDemand': row.get('electric_power_demand'),
                        'EstimationPeriod': row.get('estimation_period'),
                        'ConstructionMode': row.get('construction_mode'),
                        'NumberOfRack': row.get('number_of_rack'),
                        'StandAloneWork': row.get('stand_alone_work'),
                        'RackCapacity': row.get('rack_capacity'),
                        'PM': row.get('PM'),
                        'DeputyPM': row.get('deputy_PM'),
                        'ItemNumber': row.get('item_number'),
                        'Architecture': architecture,
                        'Beautification': beautification,
                        'TreeSutra': tree_sutra,
                        'State': row.get('state')  # 使用匹配的状态
                    })
                    break  # 找到匹配项后可以退出循环

            # 如果没有找到匹配项，仍然将原始数据添加到 data 列表中
            if not found_match:
                data.append({
                    'dcopsTicketId': row.get('dcopsTicketId'),
                    'ProjectName': project,
                    'Area': row.get('region'),
                    'Province': row.get('province'),
                    'Campus': campus,
                    'DemandDistribution': demand_distribution,
                    'DemandDelivery': demand_delivery,
                    'ElectricPowerDemand': row.get('electric_power_demand'),
                    'EstimationPeriod': row.get('estimation_period'),
                    'ConstructionMode': row.get('construction_mode'),
                    'NumberOfRack': row.get('number_of_rack'),
                    'StandAloneWork': row.get('stand_alone_work'),
                    'RackCapacity': row.get('rack_capacity'),
                    'PM': row.get('PM'),
                    'ItemNumber': row.get('item_number'),
                    'State': row.get('state')  # 仍然使用匹配的状态
                })

        return {
            'List': data,
            'total': total
        }


class ConstructionOverview(object):
    """
        建设总览
    """

    def project_schedule_management(self):
        """
            项目进度管理
        """
        # 连接数据库
        global state_value
        # 创建 ProgressManagement 实例
        progress_manager = ProgressManagement()
        # 调用 project_under_construction() 方法
        state_data = progress_manager.project_under_construction()
        status_count = {
            "预警": 0,
            "滞后": 0,
            "正常": 0
        }
        for row in state_data:
            state_value = row['state']
            # 判断 details 是否包含预警、滞后、正常
            if "预警" in state_value:
                status_count["预警"] += 1
            if "滞后" in state_value:
                status_count["滞后"] += 1
            if "正常" in state_value:
                status_count["正常"] += 1

        # 计算总数
        total_count = sum(status_count.values())

        # 计算百分比并构建返回结果
        result_list = []
        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })

        return result_list

    def delivery_period_management(self):
        """
            甲供货期管理
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, delivery_gap " \
                    "FROM delivery_schedule_management_table"
        result = db.get_all(query_sql)

        status_count = {
            "货期正常": 0,
            "货期滞后": 0
        }
        data = {}
        for row in result:
            project_name = row['project_name']
            delivery_gap = row['delivery_gap']

            if project_name not in data:
                data[project_name] = []
            data[project_name].append({'project_name': project_name, 'delivery_gap': delivery_gap})
        for project_name, items in data.items():
            has_zero = False
            for item in items:
                if item['delivery_gap'] == '0':
                    has_zero = True
                    break
            if has_zero:
                if any(item['delivery_gap'] != '已到货' and item['delivery_gap'] != '0' for item in items):
                    status_count["货期滞后"] += 1
                else:
                    status_count["货期正常"] += 1
            elif all(item['delivery_gap'] == '已到货' for item in items):
                status_count["货期正常"] += 1
            else:
                status_count["货期滞后"] += 1
        # 计算总数
        total_count = sum(status_count.values())
        # 计算百分比并构建返回结果
        result_list = []
        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })
        return result_list

    def get_classified_data(self, delivery_status):
        """
            货期分类详情
        """
        if delivery_status == '货期滞后':
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = "SELECT project_name, device_type, delivery_gap " \
                        "FROM delivery_schedule_management_table"
            result = db.get_all(query_sql)
            data = []
            project_set = set()
            for row in result:
                project_name = row.get('project_name')
                device_type = row.get('device_type')
                delivery_gap = row.get('delivery_gap')
                if delivery_gap != '0' and delivery_gap != '已到货':
                    if project_name not in project_set:
                        data.append({
                            'project_name': project_name,
                            'device_type': f'类别：{device_type},滞后{delivery_gap}天'
                        })
                        project_set.add(project_name)
                    else:
                        for item in data:
                            if item['project_name'] == project_name:
                                item['device_type'] += ';' + f'类别：{device_type},滞后{delivery_gap}天'
                                break
            return data
        elif delivery_status == '货期正常':
            db = mysql.new_mysql_instance("tbconstruct")
            query_sql = "SELECT project_name, device_type, delivery_gap " \
                        "FROM delivery_schedule_management_table"
            result = db.get_all(query_sql)
            project_set = set()
            for row in result:
                project_name = row.get('project_name')
                delivery_gap = row.get('delivery_gap')
                if delivery_gap != '已到货' and delivery_gap != '0':
                    project_set.discard(project_name)
                else:
                    project_set.add(project_name)

            data = []
            for project in project_set:
                all_devices_delivered = True
                for row in result:
                    if row.get('project_name') == project:
                        delivery_gap = row.get('delivery_gap')
                        if delivery_gap != '已到货' and delivery_gap != '0':
                            all_devices_delivered = False
                            break
                if all_devices_delivered:
                    data.append({'project_name': project})
            return data

    def delivery_category_lag_ranking(self):
        """
            货期品类滞后排行
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT device_type, delivery_gap " \
                    "FROM delivery_schedule_management_table"
        result = db.get_all(query_sql)
        # 创建一个字典来存储品类和对应的滞后时间
        category_retardation = {}

        # 遍历查询结果
        for item in result:
            device_type = item.get('device_type')
            delivery_gap = item.get('delivery_gap')

            # 判断品类的时间不等于'已到货'
            if delivery_gap != '已到货':
                # 检测字符串是否只由数字组成
                if delivery_gap.isdigit():
                    # 如果品类已存在于字典中，则累加滞后时间和计数
                    if device_type in category_retardation:
                        category_retardation[device_type]['retardation_time'] += int(delivery_gap)
                        category_retardation[device_type]['count'] += 1
                    # 如果品类不存在于字典中，则添加新的品类和滞后时间
                    else:
                        category_retardation[device_type] = {'retardation_time': int(delivery_gap), 'count': 1}

        # 计算品类的平均滞后时间
        for category, values in category_retardation.items():
            average_retardation = round(values['retardation_time'] / values['count'])
            category_retardation[category]['retardation_time'] = average_retardation

        # 按照滞后时间降序排序，并取前三名
        sorted_categories = sorted(category_retardation.items(), key=lambda x: x[1]['retardation_time'], reverse=True)[
                            :3]

        # 构建返回结果列表
        result_list = []
        for category, values in sorted_categories:
            result_list.append({
                'category': category,
                'retardation_time': values['retardation_time'],
                'number_of_items': values['count']
            })

        return result_list

    def project_quality_management(self):
        """
            项目质量管理
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT problem_category " \
                    "FROM quality_technics_management"
        result = db.get_all(query_sql)
        status_count = {
            "建设管理": 0,
            "TB设计": 0,
            "乙供设备管理": 0,
            "乙供产品质量": 0,
            "土建建设": 0,
            "资源配套(政府)": 0,
        }
        for row in result:
            problem_category = row.get('problem_category')
            if problem_category in status_count:
                status_count[problem_category] += 1

        # 计算总数
        total_count = sum(status_count.values())

        # 计算百分比并构建返回结果
        result_list = []
        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })
        return result_list

    def safety_management(self):
        """
            安全管理
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT problem_category " \
                    "FROM safety_problem"
        result = db.get_all(query_sql)
        status_count = {
            "三宝四口五临边": 0,
            "高处作业": 0,
            "安全用电": 0,
            "施工机具": 0,
            "防火": 0,
            "起重吊装": 0,
            "其它": 0,
        }
        for row in result:
            problem_category = row.get('problem_category')
            if problem_category in status_count:
                status_count[problem_category] += 1

        # 计算总计数
        total_count = sum(status_count.values())

        # 计算百分比并构建结果
        result_list = []

        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            if percentage < 5:
                status_count["其它"] += count
            else:
                formatted_percentage = "{:.1f}%".format(percentage)
                result_list.append({
                    "name": status,
                    "value": count,
                    "percentage": formatted_percentage
                })

        return result_list

    def safety_issues_in_drilling(self, category):
        """
            钻井安全问题
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT problem_category " \
                    "FROM safety_problem"
        result = db.get_all(query_sql)
        status_count = {
            "三宝四口五临边": 0,
            "高处作业": 0,
            "安全用电": 0,
            "施工机具": 0,
            "防火": 0,
            "起重吊装": 0,
            "其它": 0,
        }
        for row in result:
            problem_category = row.get('problem_category')
            if problem_category in status_count:
                status_count[problem_category] += 1

        total_count = sum(status_count.values())

        result_list = []
        other_count = 0

        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            if percentage < 5 or status == "其它":
                formatted_percentage = "{:.1f}%".format(percentage)
                result_list.append({
                    "name": status,
                    "value": count,
                    "percentage": formatted_percentage
                })
            else:
                other_count += count

        return result_list

    def security_inspection_problem_category(self):
        """
            第三方安全巡检问题类别
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name,security_management_state,supervision_unit_management_behavior," \
                    "main_contractor_unit_management_behavior " \
                    "FROM security_inspection_problem_classification"
        result = db.get_all(query_sql)

        security_sum = 0
        supervision_sum = 0
        main_contractor_sum = 0

        for item in result:
            security_sum += item["security_management_state"]
            supervision_sum += item["supervision_unit_management_behavior"]
            main_contractor_sum += item["main_contractor_unit_management_behavior"]

        security_avg = round(security_sum / len(result), 2)
        supervision_avg = round(supervision_sum / len(result), 2)
        main_contractor_avg = round(main_contractor_sum / len(result), 2)

        fractional_average = [str(security_avg), str(supervision_avg), str(main_contractor_avg)]

        wordage = ['安全管理状态', '监理管理行为', '总包管理行为']

        return {
            'fractional_average': fractional_average,
            'wordage': wordage
        }

    def category_data_query(self, category):
        """
            类别数据查询
        """
        global query_sql
        db = mysql.new_mysql_instance("tbconstruct")
        if category == '安全管理状态':
            query_sql = "SELECT fireproofing,sb_sk_wlb_jsj,safety_utilization_electric_power," \
                        "construction_equipment,lifting_erection " \
                        "FROM security_inspection_problem_classification"
        elif category == '监理管理行为':
            query_sql = "SELECT supervision_department_log,supervision_instruction,review_and_acceptance," \
                        "side_log,security_check,personnel_allocation,supervision_plan,technical_disclosure," \
                        "security_conference " \
                        "FROM security_inspection_problem_classification"
        elif category == '总包管理行为':
            query_sql = "SELECT system_responsibility_safe_production,special_construction_scheme" \
                        ",field_acceptance_test,safety_technology_disclosure,safety_inspection,safety_education," \
                        "emergency_rescue,safety_management_subcontractor,take_appointment_with_certificate," \
                        "handling_production_safety_accidents,safety_sign,safety_civilized_quality_assurance_system," \
                        "hazard_control " \
                        "FROM security_inspection_problem_classification"
        result = db.get_all(query_sql)
        if category == '安全管理状态':
            fireproofing_sum = 0
            sb_sk_wlb_jsj_sum = 0
            safety_utilization_electric_power_sum = 0
            construction_equipment_sum = 0
            lifting_erection_sum = 0
            for item in result:
                fireproofing_sum += item.get("fireproofing")
                sb_sk_wlb_jsj_sum += item.get("sb_sk_wlb_jsj")
                safety_utilization_electric_power_sum += item.get("safety_utilization_electric_power")
                construction_equipment_sum += item.get("construction_equipment")
                lifting_erection_sum += item.get("lifting_erection")
            fireproofing_avg = round(fireproofing_sum / len(result), 2)
            sb_sk_wlb_jsj_avg = round(sb_sk_wlb_jsj_sum / len(result), 2)
            safety_utilization_electric_power_avg = round(safety_utilization_electric_power_sum / len(result), 2)
            construction_equipment_avg = round(construction_equipment_sum / len(result), 2)
            lifting_erection_avg = round(lifting_erection_sum / len(result), 2)

            fractional_average = [str(fireproofing_avg), str(sb_sk_wlb_jsj_avg),
                                  str(safety_utilization_electric_power_avg), str(construction_equipment_avg),
                                  str(lifting_erection_avg)]
            wordage = ['防火', '三宝、四口、五临边、脚手架', '安全用电', '施工机具', '起重吊装']

            return {
                'fractional_average': fractional_average,
                'wordage': wordage
            }

        elif category == '监理管理行为':
            supervision_department_log_sum = 0
            supervision_instruction_sum = 0
            review_and_acceptance_sum = 0
            side_log_sum = 0
            security_check_sum = 0
            personnel_allocation_sum = 0
            supervision_plan_sum = 0
            technical_disclosure_sum = 0
            security_conference_sum = 0
            for item in result:
                supervision_department_log_sum += item.get("supervision_department_log")
                supervision_instruction_sum += item.get("supervision_instruction")
                review_and_acceptance_sum += item.get("review_and_acceptance")
                side_log_sum += item.get("side_log")
                security_check_sum += item.get("security_check")
                personnel_allocation_sum += item.get("personnel_allocation")
                supervision_plan_sum += item.get("supervision_plan")
                technical_disclosure_sum += item.get("technical_disclosure")
                security_conference_sum += item.get("security_conference")
            supervision_department_log_avg = round(supervision_department_log_sum / len(result), 2)
            supervision_instruction_avg = round(supervision_instruction_sum / len(result), 2)
            review_and_acceptance_avg = round(review_and_acceptance_sum / len(result), 2)
            side_log_avg = round(side_log_sum / len(result), 2)
            security_check_avg = round(security_check_sum / len(result), 2)
            personnel_allocation_avg = round(personnel_allocation_sum / len(result), 2)
            supervision_plan_avg = round(supervision_plan_sum / len(result), 2)
            technical_disclosure_avg = round(technical_disclosure_sum / len(result), 2)
            security_conference_avg = round(security_conference_sum / len(result), 2)

            fractional_average = [str(supervision_department_log_avg), str(supervision_instruction_avg),
                                  str(review_and_acceptance_avg), str(side_log_avg), str(security_check_avg),
                                  str(personnel_allocation_avg), str(supervision_plan_avg),
                                  str(technical_disclosure_avg), str(security_conference_avg)]
            wordage = ['监理部日志', '监理指令', '审查验收', '旁站记录', '安全检查', '人员配置', '监理规划', '技术交底',
                       '安全会议']

            return {
                'fractional_average': fractional_average,
                'wordage': wordage
            }
        elif category == '总包管理行为':
            system_responsibility_safe_production_sum = 0
            special_construction_scheme_sum = 0
            field_acceptance_test_sum = 0
            safety_technology_disclosure_sum = 0
            safety_inspection_sum = 0
            safety_education_sum = 0
            emergency_rescue_sum = 0
            safety_management_subcontractor_sum = 0
            take_appointment_with_certificate_sum = 0
            handling_production_safety_accidents_sum = 0
            safety_sign_sum = 0
            safety_civilized_quality_assurance_system_sum = 0
            hazard_control_sum = 0
            for item in result:
                system_responsibility_safe_production_sum += item.get("system_responsibility_safe_production")
                special_construction_scheme_sum += item.get("special_construction_scheme")
                field_acceptance_test_sum += item.get("field_acceptance_test")
                safety_technology_disclosure_sum += item.get("safety_technology_disclosure")
                safety_inspection_sum += item.get("safety_inspection")
                safety_education_sum += item.get("safety_education")
                emergency_rescue_sum += item.get("emergency_rescue")
                safety_management_subcontractor_sum += item.get("safety_management_subcontractor")
                take_appointment_with_certificate_sum += item.get("take_appointment_with_certificate")
                handling_production_safety_accidents_sum += item.get("handling_production_safety_accidents")
                safety_sign_sum += item.get("safety_sign")
                safety_civilized_quality_assurance_system_sum += item.get("safety_civilized_quality_assurance_system")
                hazard_control_sum += item.get("hazard_control")
            system_responsibility_safe_production_avg = round(system_responsibility_safe_production_sum / len(result),
                                                              2)
            special_construction_scheme_avg = round(special_construction_scheme_sum / len(result), 2)
            field_acceptance_test_avg = round(field_acceptance_test_sum / len(result), 2)
            safety_technology_disclosure_avg = round(safety_technology_disclosure_sum / len(result), 2)
            safety_inspection_avg = round(safety_inspection_sum / len(result), 2)
            safety_education_avg = round(safety_education_sum / len(result), 2)
            emergency_rescue_avg = round(emergency_rescue_sum / len(result), 2)
            safety_management_subcontractor_avg = round(safety_management_subcontractor_sum / len(result), 2)
            take_appointment_with_certificate_avg = round(take_appointment_with_certificate_sum / len(result), 2)
            handling_production_safety_accidents_avg = round(handling_production_safety_accidents_sum / len(result), 2)
            safety_sign_avg = round(safety_sign_sum / len(result), 2)
            safety_civilized_quality_assurance_system_avg = round(
                safety_civilized_quality_assurance_system_sum / len(result), 2)
            hazard_control_avg = round(hazard_control_sum / len(result), 2)

            fractional_average = [str(system_responsibility_safe_production_avg), str(special_construction_scheme_avg),
                                  str(field_acceptance_test_avg), str(safety_technology_disclosure_avg),
                                  str(safety_inspection_avg), str(safety_education_avg), str(emergency_rescue_avg),
                                  str(safety_management_subcontractor_avg), str(take_appointment_with_certificate_avg),
                                  str(handling_production_safety_accidents_avg), str(safety_sign_avg),
                                  str(safety_civilized_quality_assurance_system_avg), str(hazard_control_avg)]
            wordage = ['安全生产责任制', '施工组织设计及专项施工方案', '现场验收', '安全技术交底', '安全检查',
                       '安全教育', '应急救援', '分包单位安全管理', '持证上岗', '生产安全事故处理', '安全标志',
                       '安全文明质保体系', '危险源控制', ]

            return {
                'fractional_average': fractional_average,
                'wordage': wordage
            }

    def security_inspection_score_distribution_y(self):
        """
            第三方安全巡检分数分布(y轴)
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, bdaqwm_overall_pass_rate FROM security_inspection_problem_classification"
        result = db.get_all(query_sql)

        data = {}
        for row in result:
            project_name = row.get('project_name')
            pass_rate = row.get('bdaqwm_overall_pass_rate')
            if project_name in data:
                data[project_name].append(pass_rate)
            else:
                data[project_name] = [pass_rate]

        averaged_data = [round(sum(scores) / len(scores), 2) for scores in data.values()]

        return averaged_data

    def security_inspection_score_distribution_x(self):
        """
            第三方安全巡检分数分布(x轴)
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT DISTINCT project_name FROM security_inspection_problem_classification"
        result = db.get_all(query_sql)
        data = [row.get('project_name') for row in result]

        target_projects = ['贵安七星', '仪征东升', '清远清新']

        new_data = []

        for item in data:
            if "B" in item:
                campus, project = item.split("B")
                if campus in target_projects:
                    campus = campus[:2]
                else:
                    campus = campus[-2:]
                new_item = campus + project
                new_data.append(new_item)

        return new_data


class ConstructionReserve(object):
    """
        建设准备
    """

    def planning_evaluation(self):
        """
            规划评估
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, demand_distribution, demand_delivery FROM risk_early_warning_data"
        project_result = db.get_all(query_sql)
        status_count = {
            "匹配周期": 0,
            "不匹配周期": 0
        }

        project_names_found = set()

        for row in project_result:
            project_name = row.get('project_name')
            project_names_found.add(project_name)

        supplier_query_sql = f"SELECT campus, project, delivery_target, state FROM supplier_resources " \
                             f"WHERE CONCAT(campus,project) NOT IN {tuple(project_names_found)}"
        supplier_result = db.get_all(supplier_query_sql)

        for supplier_item in supplier_result:
            campus = supplier_item.get('campus')
            delivery_target = supplier_item.get('delivery_target')
            state = supplier_item.get('state')

            # 判断state是否为"已交付"或"建设中"，如果是则跳过当前循环
            if state == "已交付" or state == "建设中":
                continue

            query_sql = f"SELECT effect_of_winter_application, have_or_not_external_resources " \
                        f"FROM influence_factor WHERE campus='{campus}'"
            result = db.get_all(query_sql)

            for item in result:
                effect_of_winter_application = item.get('effect_of_winter_application')
                have_or_not_external_resources = item.get('have_or_not_external_resources')

                # 判断建设周期
                if have_or_not_external_resources == "有":
                    construction_period = 7  # 有外电资源，建设周期为7个月
                else:
                    construction_period = 10  # 无外电资源，建设周期为10个月

                if effect_of_winter_application == "有":
                    construction_period += 4  # 有冬施影响，建设周期再加4个月

                # 判断匹配交付周期和不匹配交付周期
                today = datetime.date.today()
                delivery_target = datetime.datetime.strptime(delivery_target, "%Y年%m月%d日").date()
                delivery_period = math.ceil((delivery_target - today).days / 30)  # 以月份为单位向上取整

                if delivery_period > construction_period:
                    status_count["匹配周期"] += 1
                else:
                    status_count["不匹配周期"] += 1

        # 计算总数
        total_count = sum(status_count.values())

        # 计算百分比并构建返回结果
        result_list = []
        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })

        return result_list

    def supplier_resource_evaluation(self):
        """
            供应商资源评估
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, project, state, integrators, project_manager, supervision, examination, AHU, " \
                    "low_voltage_cabinet, medium_voltage_cabinet, battery, chaifa, " \
                    "cabinet, transformer, headboard, PDU " \
                    "FROM supplier_resources"
        result = db.get_all(query_sql)

        status_count = {
            "满足": 0,
            "不满足": 0
        }
        for row in result:
            state = row.get('state')

            # 判断state是否为"已交付"或"建设中"，如果是则跳过当前循环
            if state == "已交付" or state == "建设中":
                continue

            has_na = False
            for key, value in row.items():
                if key in ['campus', 'project']:
                    continue
                if value == 'NA':
                    has_na = True
                    break

            if has_na:
                status_count["不满足"] += 1
            else:
                status_count["满足"] += 1

        # 计算总数
        total_count = sum(status_count.values())

        # 计算百分比并构建返回结果
        result_list = []
        for status, count in status_count.items():
            percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })

        return result_list

    def park_resource_evaluation(self):
        """
            园区资源评估
        """
        status_count = {
            "满足": 0,
            "不满足": 0
        }
        project_count = {
            "满足": 0,
            "不满足": 0
        }
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT campus, ability_comment_rest FROM park_resources"
        supplier_sql = f"SELECT campus, project, state FROM supplier_resources"
        result = db.get_all(query_sql)
        supplier_result = db.get_all(supplier_sql)
        # 遍历园区资源
        for item in result:
            campus = item.get('campus')
            ability_comment_rest = item.get('ability_comment_rest')
            if '-' in ability_comment_rest:
                status_count[campus] = "不满足"
            else:
                status_count[campus] = "满足"
        # 遍历供应商资源
        for item in supplier_result:
            campus = item.get('campus')
            state = item.get('state')

            # 判断state是否为"已交付"或"建设中"，如果是则跳过当前循环
            if state == "已交付" or state == "建设中":
                continue

            if campus in status_count:
                if status_count[campus] == "不满足":
                    project_count["不满足"] += 1
                else:
                    project_count["满足"] += 1

        # 计算总数
        total_count = sum(project_count.values())

        # 计算百分比并构建返回结果
        result_list = []
        for status, count in project_count.items():
            project_percentage = count / total_count * 100 if total_count != 0 else 0
            formatted_percentage = "{:.1f}%".format(project_percentage)
            result_list.append({
                "name": status,
                "value": count,
                "percentage": formatted_percentage
            })
        return result_list

    def park_evaluation_drill_down(self, category):
        """
            园区评估弹窗
        """
        result_list = []
        status_count = {
            "满足": 0,
            "不满足": 0
        }
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = f"SELECT campus, ability_comment_rest FROM park_resources"
        supplier_sql = f"SELECT campus, project FROM supplier_resources"
        result = db.get_all(query_sql)
        supplier_result = db.get_all(supplier_sql)
        # 遍历园区资源
        for item in result:
            campus = item.get('campus')
            ability_comment_rest = item.get('ability_comment_rest')
            if '-' in ability_comment_rest:
                status_count[campus] = "不满足"
            else:
                status_count[campus] = "满足"

        # 创建字典来合并相同园区的项目
        merged_projects = {}

        # 遍历供应商资源
        for item in supplier_result:
            campus = item.get('campus')
            project = item.get('project')
            if campus in status_count:
                if status_count[campus] == "不满足":
                    if campus not in merged_projects:
                        merged_projects[campus] = []
                    merged_projects[campus].append(project)

        # 构建最终结果列表
        for campus, projects in merged_projects.items():
            result_list.append({
                "campus": campus,
                "project": ",".join(projects),
                "state": "无能评"
            })

        return result_list


class DeliveryScheduleManagement(object):
    """
        货期管理接口
    """

    def dynamic_header(self, project_name=None, state=None, PM=None, demand_delivery=None):
        """
            动态表头（甲供）- 修复横向重复问题
            主要修复:
            1. 移除SQL中的device_type分组
            2. 按项目聚合，避免设备类型导致的重复列
            3. 添加调试日志
        """
        print(f"[DEBUG] === dynamic_header 开始执行 ===")
        print(f"[DEBUG] 查询参数 - project_name: {project_name}, state: {state}, PM: {PM}")

        # 获取数据库连接
        db = mysql.new_mysql_instance("tbconstruct")

        # 构建WHERE条件 - 以主表为核心，区分甲乙供
        conditions = []

        # 主表核心条件
        conditions.append("actd.SchemeNameCn LIKE '%设备生产%'")
        conditions.append("actd.IsForceEnd = 0")

        # 甲供条件 - 关键修复：区分甲乙供
        conditions.append("eprp.equipment_type = '甲供'")

        if project_name:
            conditions.append(f"actd.Title LIKE '%{project_name}%'")

        # 其他条件保留但基于主表
        if state:
            conditions.append(f"actd.State = '{state}'")

        if PM:
            conditions.append(f"actd.PM = '{PM}'")

        where_clause = " AND ".join(conditions)

        # 重构SQL - 以主表为核心
        unified_query = f"""
            SELECT DISTINCT
                actd.TicketId,
                actd.Title,
                actd.SchemeNameCn,
                actd.IsForceEnd,
                dsm.project_name,
                dsm.supplier,
                dsm.project_required_delivery_time
            FROM all_construction_ticket_data actd
            LEFT JOIN equipment_production_racking_process eprp
                ON eprp.ticket_id = actd.TicketId
            LEFT JOIN delivery_schedule_management_table dsm
                ON dsm.project_name = eprp.project_name
            WHERE {where_clause}
            ORDER BY actd.TicketId
        """

        print(f"[DEBUG] 执行查询SQL: {unified_query[:200]}...")

        try:
            result = db.get_all(unified_query)
            print(f"[DEBUG] 查询结果数量: {len(result)}")
        except Exception as e:
            print(f"[DEBUG] 查询执行失败: {e}")
            return []

        # 数据去重和处理 - 基于主表工单
        unique_data = []
        seen_records = set()

        for row in result:
            # 基于主表工单号进行去重
            ticket_id = row.get('TicketId')

            # 工单号为空的直接跳过
            if not ticket_id or str(ticket_id).strip() == '':
                print(f"[DEBUG] 跳过空工单号记录")
                continue

            project_name = row.get('project_name')
            supplier = row.get('supplier')

            # 处理项目交付时间格式 - 修复空值问题
            delivery_time = row.get('project_required_delivery_time')
            if delivery_time and isinstance(delivery_time, str) and '→' in delivery_time:
                delivery_time = delivery_time.split('→')[1].strip()
            elif not delivery_time:
                delivery_time = ''

            # 只用工单号作为去重条件
            record_key = ticket_id

            if record_key not in seen_records:
                unique_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'supplier': supplier,
                    'project_required_delivery_time': delivery_time,
                    'required_delivery_time': row.get('project_required_delivery_time')
                })
                seen_records.add(record_key)

        print(f"[DEBUG] 去重后数据数量: {len(unique_data)}")

        # 固定表头结构
        result_data = []

        # 数据组合
        if project_name and not state and not PM and not demand_delivery:
            # 固定的表头结构，只替换项目名称
            result_data = [
                {
                    "align": "center",
                    "label": "设备类型",
                    "minWidth": "100",
                    "prop": "device_type"
                },
                {
                    "align": "center",
                    "label": "设备SLA",
                    "minWidth": "100",
                    "prop": "equipment_SLA"
                },
                {
                    "children": [
                        {
                            "children": [
                                {
                                    "align": "center",
                                    "label": "设备期望时间",
                                    "minWidth": "110",
                                    "prop": "expected_time_equipment"
                                },
                                {
                                    "align": "center",
                                    "label": "预计交付时间",
                                    "minWidth": "110",
                                    "prop": "estimated_time_delivery"
                                },
                                {
                                    "align": "center",
                                    "label": "实际交付时间",
                                    "minWidth": "110",
                                    "prop": "actual_time_delivery"
                                },
                                {
                                    "align": "center",
                                    "label": "交付GAP",
                                    "minWidth": "110",
                                    "prop": "delivery_GAP"
                                },
                                {
                                    "align": "center",
                                    "label": "生产工单",
                                    "minWidth": "110",
                                    "prop": "production_work_order"
                                }
                            ],
                            "label": "项目需求交付时间：2025/04/10",
                            "prop": ""
                        }
                    ],
                    "label": f"{project_name}",  # 动态替换项目名称
                    "prop": "project_name"
                }
            ]
        elif project_name or state or PM or demand_delivery:
            # 多项目模式
            month_data = {
                'key': '13yue',
                'title': '',
                'column': [
                    {
                        'prop': 'device_type',
                        'label': '设备类型',
                        'minWidth': '100',
                        'align': 'center'
                    },
                    {
                        'prop': 'equipment_SLA',
                        'label': '设备SLA',
                        'minWidth': '100',
                        'align': 'center'
                    }
                ]
            }

            for row in unique_data:
                project_name_val = row.get('project_name')
                supplier = row.get('supplier')
                delivery_time = row.get('project_required_delivery_time')

                # 简化的项目名处理（移除拼音转换）
                project_key = re.sub(r'[^\w]', '', project_name_val)[:10]  # 简化的key生成

                column = {
                    'prop': 'project_name',
                    'label': f'{project_name_val}-{supplier}',
                    'children': [{
                        'prop': '',
                        'label': f'项目需求交付时间：{delivery_time}',
                        'children': [
                            {
                                'prop': f'{project_key}expected_time_equipment',
                                'label': '设备期望时间',
                                'minWidth': '110',
                                'align': 'center'
                            },
                            {
                                'prop': f'{project_key}estimated_time_delivery',
                                'label': '预计交付时间',
                                'minWidth': '110',
                                'align': 'center'
                            },
                            {
                                'prop': f'{project_key}actual_time_delivery',
                                'label': '实际交付时间',
                                'minWidth': '110',
                                'align': 'center'
                            },
                            {
                                'prop': f'{project_key}po_create_time',
                                'label': 'PO下发时间',
                                'minWidth': '110',
                                'align': 'center'
                            },
                            {
                                'prop': f'{project_key}delivery_GAP',
                                'label': '交付GAP',
                                'minWidth': '110',
                                'align': 'center'
                            },
                            {
                                'prop': f'{project_key}production_work_order',
                                'label': '生产工单',
                                'minWidth': '110',
                                'align': 'center'
                            }
                        ]
                    }]
                }
                month_data['column'].append(column)
            result_data.append(month_data)

        print(f"[DEBUG] 返回固定表头结构，项目名: {project_name}")
        return result_data

    def tabular_data_query(self, project_name=None, state=None, PM=None, demand_delivery=None):
        """
            表格数据查询（甲供）- 修复横向重复问题
            主要修复:
            1. 添加项目-设备组合去重逻辑
            2. 避免相同项目的不同设备类型产生重复列
            3. 添加调试日志
        """
        print(f"[DEBUG] === tabular_data_query 开始执行 ===")
        print(f"[DEBUG] 查询参数 - project_name: {project_name}, state: {state}, PM: {PM}")

        # 获取数据库连接
        db = mysql.new_mysql_instance("tbconstruct")

        # 注意：现在使用合并表，不需要复杂的WHERE条件构建

        # 使用合并表简化查询，避免复杂JOIN和重复数据问题
        unified_query = f"""
            SELECT
                TicketId,
                Title,
                SchemeNameCn,
                IsForceEnd,
                project_name,
                device_name,
                supplier,
                device_type,
                equipment_sla,
                expected_time_equipment,
                estimated_time_delivery,
                delivery_gap,
                completion_time,
                po_create_time,
                state as project_state
            FROM consolidated_equipment_data
            WHERE 1=1
        """

        # 添加查询条件
        if project_name:
            unified_query += f" AND project_name LIKE '%{project_name}%'"

        if state:
            unified_query += f" AND state = '{state}'"

        if PM:
            unified_query += f" AND Title LIKE '%{PM}%'"

        unified_query += " ORDER BY TicketId, device_name LIMIT 3000"

        print(f"[DEBUG] 执行查询SQL: {unified_query[:200]}...")

        try:
            result = db.get_all(unified_query)
            print(f"[DEBUG] 查询结果数量: {len(result)}")
        except Exception as e:
            print(f"[DEBUG] 查询执行失败: {e}")
            return {}

        # 处理查询结果
        if project_name and not state and not PM and not demand_delivery:
            # 单项目模式处理 - 添加数据去重逻辑
            data = []
            seen_records = set()  # 用于去重的集合

            for row in result:
                # 基于主表工单号，已经在WHERE中过滤了IsForceEnd
                ticket_id = row.get('TicketId')

                # 工单号为空的直接跳过
                if not ticket_id or str(ticket_id).strip() == '':
                    print(f"[DEBUG] 跳过空工单号记录")
                    continue

                project_name_val = row.get('project_name')
                supplier = row.get('supplier')
                device_type = row.get('device_type')

                # 只用工单号作为去重条件
                record_key = ticket_id

                # 如果记录已存在，跳过
                if record_key in seen_records:
                    print(f"[DEBUG] 跳过重复工单: {ticket_id}")
                    continue
                seen_records.add(record_key)

                # 安全处理空值
                safe_project_name = project_name_val or ''
                safe_supplier = supplier or ''

                data.append({
                    'project_name': f'{safe_project_name}-{safe_supplier}',
                    'device_type': row.get('device_name') or '',
                    'equipment_SLA': row.get('equipment_sla') or '',
                    'expected_time_equipment': row.get('expected_time_equipment') or '',
                    'estimated_time_delivery': row.get('estimated_time_delivery') or '',
                    'delivery_GAP': row.get('delivery_gap') or '',
                    'po_create_time': row.get('po_create_time') or '',
                    'production_work_order': row.get('TicketId') or '',
                    'actual_time_delivery': row.get('completion_time') or ''
                })

            print(f"[DEBUG] 单项目模式 - 原始记录: {len(result)}, 去重后: {len(data)}")
            return data
        else:
            # 多项目模式处理 - 修复横向重复问题
            data = {'13yue': []}
            processed_devices = {}
            processed_projects = set()  # 用于跟踪已处理的项目，避免重复

            for row in result:
                # 基于主表工单号，已经在WHERE中过滤了IsForceEnd
                ticket_id = row.get('TicketId')

                # 工单号为空的直接跳过
                if not ticket_id or str(ticket_id).strip() == '':
                    print(f"[DEBUG] 跳过空工单号记录")
                    continue

                device_type = row.get('device_type')
                project_name_val = row.get('project_name')

                # 只用工单号作为去重条件
                if ticket_id in processed_projects:
                    print(f"[DEBUG] 跳过重复工单: {ticket_id}")
                    continue  # 跳过重复的工单
                processed_projects.add(ticket_id)
                print(f"[DEBUG] 处理工单: {ticket_id}")

                # 简化的项目key生成 - 安全处理空值
                safe_project_name = project_name_val or 'unknown'
                project_key = re.sub(r'[^\w]', '', safe_project_name)[:10]

                # 注意：合并表中不包含actual_arrival_time字段，已移除相关处理

                # 查找或创建设备记录 - 安全处理空值
                device_key = device_type or 'unknown_device'
                if device_key not in processed_devices:
                    processed_devices[device_key] = {
                        'device_type': device_type or '',
                        'equipment_SLA': row.get('equipment_sla') or '',
                        'original_project_name': project_name_val or ''
                    }
                    data['13yue'].append(processed_devices[device_key])

                # 添加项目特定的字段 - 安全处理空值
                device_record = processed_devices[device_key]
                device_record[f'{project_key}delivery_GAP'] = row.get('delivery_gap') or ''
                device_record[f'{project_key}estimated_time_delivery'] = row.get('estimated_time_delivery') or ''
                device_record[f'{project_key}expected_time_equipment'] = row.get('expected_time_equipment') or ''
                device_record[f'{project_key}production_work_order'] = row.get('TicketId') or ''  # 修复：前后端字段不一致
                # device_record[f'{project_key}production_work_order'] = row.get('production_work_order') or ''
                device_record[f'{project_key}actual_time_delivery'] = row.get('completion_time') or ''
                device_record[f'{project_key}po_create_time'] = row.get('po_create_time') or ''

            print(f"[DEBUG] 最终数据结构 - 设备数量: {len(data['13yue'])}")
            return data

    def dynamic_header_yg(self, project_name):
        """
            动态表头（乙供）
        """
        global required_delivery_time
        db = mysql.new_mysql_instance("tbconstruct")
        project = project_name
        if project:
            query_sql = f"SELECT project_name, supplier, project_required_delivery_time, device_type, equipment_sla, " \
                        f"expected_time_equipment, estimated_time_delivery, delivery_gap " \
                        f"FROM delivery_schedule_management_table WHERE project_name='{project}'"
        result = db.get_all(query_sql)
        data = []
        for row in result:
            project_required_delivery_time = row.get('project_required_delivery_time')
            required_delivery_time = row.get('project_required_delivery_time')
            project_name = row.get('project_name')
            supplier = row.get('supplier')
            if '→' in project_required_delivery_time:
                project_required_delivery_time = project_required_delivery_time.split('→')[1].strip()
            data.append({
                'project_required_delivery_time': project_required_delivery_time,
                'required_delivery_time': required_delivery_time,
                'project_name': project_name,
                'supplier': supplier,
            })
        # 创建一个集合来存储唯一的项目
        unique_projects = set()
        # 创建字典来存储每个月份的项目计数
        month_project_counts = defaultdict(int)
        # 统计每个不同日期的出现次数
        for record in data:
            project_name = record.get('project_name')
            date = datetime.datetime.strptime(record.get('project_required_delivery_time'), '%Y/%m/%d').date()
            month = date.month
            if project_name not in unique_projects:
                unique_projects.add(project_name)
                month_project_counts[month] += 1

        # 去重
        unique_data = []
        seen_records = set()
        for record in data:
            record_tuple = (
                record.get('project_required_delivery_time'), record.get('project_name'), record.get('supplier'),
                record.get('required_delivery_time'))
            if record_tuple not in seen_records:
                unique_data.append(record)
                seen_records.add(record_tuple)
        # 数据组合
        result_data = []
        if project:
            month_data = [
                {
                    'prop': 'device_type',
                    'label': '设备类型',
                    'minWidth': '200',
                    'align': 'center'
                }
            ]
            for row in unique_data:
                project_required_delivery_time = datetime.datetime.strptime(
                    row.get('project_required_delivery_time'),
                    '%Y/%m/%d')
                if project_required_delivery_time.month == month:
                    project_name = row.get('project_name')
                    supplier = row.get('supplier')
                    children = [
                        {
                            'prop': '',
                            'label': f'项目需求交付时间：{required_delivery_time}',
                            'children': [
                                {
                                    'prop': 'expected_time_equipment',
                                    'label': '设备期望时间',
                                    'minWidth': '130',
                                    'align': 'center'
                                },
                                {
                                    'prop': 'estimated_time_delivery',
                                    'label': '预计交付时间',
                                    'minWidth': '130',
                                    'align': 'center'
                                },
                                {
                                    'prop': 'actual_time_delivery',
                                    'label': '实际交付时间',
                                    'minWidth': '130',
                                    'align': 'center'
                                },
                                {
                                    'prop': 'production_work_order',
                                    'label': '生产工单',
                                    'minWidth': '130',
                                    'align': 'center'
                                }
                            ]
                        }
                    ]
                    column = {
                        'prop': 'project_name',
                        'label': f'{project_name}',
                        'children': children
                    }
                    month_data.append(column)
            result_data.extend(month_data)
        return result_data

    def tabular_data_query_yg(self, project_name):
        """
            表格数据（乙供）
        """
        db = mysql.new_mysql_instance("tbconstruct")

        # 查询 excel_data 表中的数据
        query_sql = f"""
                SELECT project_name, serial_number, work_content AS device_type, start_time, completion_time 
                FROM construct_plan_data 
                WHERE project_name='{project_name}'
            """
        result_excel = db.get_all(query_sql)

        # 查询 equipment_arrival_data 表中的数据
        sj_sql = f"""
                SELECT equipment_category AS device_type, planned_arrival_time, actual_arrival_time, ticket_id 
                FROM equipment_arrival_data 
                WHERE CONCAT(campus, project_name)='{project_name}'
            """
        result_sj = db.get_all(sj_sql)

        # 用于存储最终结果和去重的设备类型
        data = []
        processed_devices = set()

        # 获取甲供设备列表，避免重复获取到货数据
        jg_sql = f"SELECT device_type " \
                 f"FROM delivery_schedule_management_table WHERE project_name='{project_name}'"

        result_jg = db.get_all(jg_sql)
        if result_jg:
            for row in result_jg:
                device_type = row.get('device_type', "")
                processed_devices.add(device_type)

        # 处理 excel_data 表中的数据
        for row in result_excel:
            if row.get('serial_number', "").startswith('2.6.'):
                device_type = row.get('device_type')

                # 如果设备已经处理过，跳过
                if device_type in processed_devices:
                    continue

                # 计算 SLA
                start_time = row.get('start_time')
                completion_time = row.get('completion_time')
                if not start_time or not completion_time:
                    equipment_sla = ""
                else:
                    try:
                        equipment_sla = (datetime.datetime.strptime(completion_time, '%Y-%m-%d') -
                                         datetime.datetime.strptime(start_time, '%Y-%m-%d')).days
                    except ValueError:
                        equipment_sla = ""

                # 预期时间
                expected_time_equipment = f"{start_time or ''}~{completion_time or ''}" \
                    if start_time and completion_time else (
                        completion_time or ""
                )

                # 查找实际到货时间
                actual_arrival_time = ''
                estimated_time_delivery = ''
                for sj_row in result_sj:
                    if sj_row.get('device_type') == device_type:
                        actual_arrival_time = sj_row.get('actual_arrival_time')
                        estimated_time_delivery = sj_row.get('planned_arrival_time')

                        # 处理 actual_arrival_time
                        if actual_arrival_time:
                            try:
                                actual_arrival_time = datetime.datetime.strptime(str(actual_arrival_time),
                                                                                 '%Y-%m-%d %H:%M:%S')
                                actual_arrival_time = actual_arrival_time.strftime('%Y-%m-%d')  # 仅保留年月日
                            except ValueError:
                                actual_arrival_time = ""  # 格式错误时返回空字符串
                        else:
                            actual_arrival_time = ""  # 如果为 None，返回空字符串

                        # 处理 planned_arrival_time
                        if estimated_time_delivery:
                            try:
                                estimated_time_delivery = datetime.datetime.strptime(str(estimated_time_delivery),
                                                                                     '%Y-%m-%d %H:%M:%S')
                                estimated_time_delivery = estimated_time_delivery.strftime('%Y-%m-%d')  # 仅保留年月日
                            except ValueError:
                                estimated_time_delivery = ""  # 格式错误时返回空字符串
                        else:
                            estimated_time_delivery = ""  # 如果为 None，返回空字符串

                # 查找生产工单号
                ticket_id_sql = """
                            SELECT ticket_id 
                            FROM equipment_production_racking_process 
                            WHERE project_name=%s AND device_name=%s
                        """
                ticket_id_result = db.get_all(ticket_id_sql, (project_name, device_type))
                production_work_order = ticket_id_result[0].get('ticket_id') if ticket_id_result else None

                # 构建数据
                data.append({
                    'project_name': project_name,
                    'device_type': device_type,
                    'equipment_SLA': str(equipment_sla) if equipment_sla != "" else "",
                    'estimated_time_delivery': estimated_time_delivery,  # 已转换为年月日
                    'expected_time_equipment': f"{start_time or ''}~{completion_time or ''}"
                    if start_time and completion_time else (completion_time or ""),
                    'actual_time_delivery': actual_arrival_time,  # 已转换为年月日
                    'production_work_order': str(production_work_order) if production_work_order else ""
                })

                # 标记设备已处理
                processed_devices.add(device_type)
            elif row.get('serial_number', "").startswith('3'):
                break

        # 处理 equipment_arrival_data 表中的新增设备
        for sj_row in result_sj:
            device_type = sj_row.get('device_type')

            # 如果设备已经处理过，跳过
            if device_type in processed_devices:
                continue

            # 计划到货时间
            estimated_time_delivery = sj_row.get('planned_arrival_time')
            # 处理 planned_arrival_time
            if estimated_time_delivery:
                try:
                    estimated_time_delivery = datetime.datetime.strptime(str(estimated_time_delivery),
                                                                         '%Y-%m-%d %H:%M:%S')
                    estimated_time_delivery = estimated_time_delivery.strftime('%Y-%m-%d')  # 仅保留年月日
                except ValueError:
                    estimated_time_delivery = ""  # 格式错误时返回空字符串
            else:
                estimated_time_delivery = ""  # 如果为 None，返回空字符串

            # 实际到货时间
            actual_arrival_time = sj_row.get('actual_arrival_time')
            # 处理 actual_arrival_time
            if actual_arrival_time:
                try:
                    actual_arrival_time = datetime.datetime.strptime(str(actual_arrival_time),
                                                                     '%Y-%m-%d %H:%M:%S')
                    actual_arrival_time = actual_arrival_time.strftime('%Y-%m-%d')  # 仅保留年月日
                except ValueError:
                    actual_arrival_time = ""  # 格式错误时返回空字符串
            else:
                actual_arrival_time = ""  # 如果为 None，返回空字符串

            # 查找生产工单号
            production_work_order = sj_row.get('ticket_id')

            # 预期时间
            expected_time_equipment = f"{estimated_time_delivery or ''}~{actual_arrival_time or ''}" \
                if estimated_time_delivery and actual_arrival_time else (
                    actual_arrival_time or ""
            )

            # 构建数据
            data.append({
                'project_name': project_name,
                'device_type': device_type,
                'equipment_SLA': '',
                'estimated_time_delivery': estimated_time_delivery,  # 已转换为年月日
                'expected_time_equipment': expected_time_equipment,
                'actual_time_delivery': actual_arrival_time,  # 已转换为年月日
                'production_work_order': str(production_work_order) if production_work_order else ""
            })

            # 标记设备已处理
            processed_devices.add(device_type)

        return data

    def data_export(self, party_a_table=None, party_b_table=None):
        """
            甲供、乙供表格信息导出
        """
        chinese_headers_a = {
            "project_name": "项目名称-供应商",
            "device_type": "设备类型",
            "equipment_SLA": "交付SLA",
            "po_create_time": "PO下发时间",
            "expected_time_equipment": "设备期望时间",
            "estimated_time_delivery": "预计交付时间",
            "actual_time_delivery": "实际交付时间",
            "delivery_GAP": "交付GAP",
            "production_work_order": "生产工单"
        }
        chinese_headers_b = {
            "project_name": "项目名称",
            "device_type": "设备类型",
            "expected_time_equipment": "设备期望时间",
            "estimated_time_delivery": "预计交付时间",
            "actual_time_delivery": "实际交付时间",
            "delivery_GAP": "交付GAP",
            "production_work_order": "生产工单"
        }

        # -------------------- 通过项目名称、设备名称，查询设备的供应商，并且赋值 --------------------
        def get_supplier_info(items):
            if not items:
                return
            for row in items:
                project_name = row.get('project_name')
                if project_name.count('-') >= 2:  # 至少包含两个连字符，可能是带供应商的格式
                    project_name = project_name.rsplit('-', 1)[0]  # 拆分为项目主体和供应商
                else:
                    project_name = project_name  # 不拆分，直接使用原名称
                device_name = row.get('device_type')
                production_work_order = row.get('production_work_order')

                db = mysql.new_mysql_instance("tbconstruct")
                sql = f"""
                      SELECT equipment_category,
                      CONCAT(campus,project_name) AS device_project_name,supplier FROM equipment_arrival_data 
                      WHERE equipment_category = '{device_name}' 
                      AND CONCAT(campus,project_name) = '{project_name}' 
                      AND ticket_id = '{production_work_order}' 
                      """
                result_list = db.get_all(sql)

                for res in result_list:
                    supplier = res.get('supplier', '')
                    device_project_name = res.get('device_project_name', '')
                    equipment_category_name = res.get('equipment_category', '')
                    if device_project_name == project_name and equipment_category_name == device_name:
                        row['project_name'] = f"{project_name}-{supplier}"

        # -------------------- 处理甲供数据：计算交付GAP --------------------
        def calculate_delivery_gap(items):
            if not items:
                return
            for row in items:
                # 解析期望时间（取~右侧日期，若不存在则取整个字符串）
                expected_time = row.get('expected_time_equipment', '')
                expected_end = expected_time.split('~')[-1].strip() if '~' in expected_time else expected_time

                # 解析实际交付时间
                actual_time = row.get('actual_time_delivery', '')

                try:
                    expected_date = datetime.datetime.strptime(expected_end, "%Y-%m-%d")
                    actual_date = datetime.datetime.strptime(actual_time, "%Y-%m-%d")
                    delta = (actual_date - expected_date).days
                except (ValueError, SyntaxError, TypeError, AttributeError) as e:
                    delta = None

                # 应用规则
                if delta is None:
                    row['delivery_GAP'] = "未到货"  # 原始默认值或自定义处理
                elif delta == 0 or delta < 0:
                    row['delivery_GAP'] = "/"  # 实际晚于期望，赋值"/"
                else:
                    row['delivery_GAP'] = f"{abs(delta)}天"

        # -------------------- 处理甲供数据 --------------------
        if party_a_table:
            calculate_delivery_gap(party_a_table)
            get_supplier_info(party_a_table)
        # -------------------- 处理乙供数据（移除交付SLA并计算GAP） --------------------
        if party_b_table:
            # 移除乙供数据中的"equipment_SLA"字段
            for item in party_b_table:
                item.pop('equipment_SLA', None)  # 安全删除，若不存在则忽略
            calculate_delivery_gap(party_b_table)
            get_supplier_info(party_b_table)

        # 创建DataFrame并应用中文表头
        df_party_a = pd.DataFrame(party_a_table)
        df_party_a = df_party_a.rename(columns=chinese_headers_a)
        df_party_a = df_party_a[list(chinese_headers_a.values())]  # 使用values()获取中文列名

        df_party_b = pd.DataFrame(party_b_table)
        df_party_b = df_party_b.rename(columns=chinese_headers_b)
        df_party_b = df_party_b[list(chinese_headers_b.values())]  # 使用values()获取中文列名

        # 生成Excel文件路径
        excel_file = "设备货期表格.xlsx"

        # 使用ExcelWriter写入两个Sheet页
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 写入甲供信息
            df_party_a.to_excel(writer, index=False, sheet_name="甲供信息")
            # 写入乙供信息
            df_party_b.to_excel(writer, index=False, sheet_name="乙供信息")
        # 上传文件到COS并获取URL
        cos_path = "/data_json/"
        COSLib.uploadFile(file_name=excel_file, cos_path=cos_path)
        equipment_table_url = COSLib.get_file_url(file_name=excel_file, cos_path=cos_path)
        if equipment_table_url:
            return {
                "code": 0,
                "message": "导出成功",
                "equipment_table_url": equipment_table_url
            }
        else:
            return {
                "code": -1,
                "message": "导出失败"
            }


class PoInformationForm(object):
    """
        PO信息表格
    """

    def get_po_information(self, project_name=None, device_name=None, page_size=None, page_number=None):
        """
            查找PO信息
        """
        data = []
        conditions = []
        db = mysql.new_mysql_instance("tbconstruct")
        po_information_sql = \
            f"""
            SELECT
              pi.project_name,
              pi.po_code,
              ecm.device_name,
              snm.js_supplier_name AS supplier,
              pi.po_create_time
            FROM
              payment_info pi
              LEFT JOIN equipment_category_mapping_relationship ecm ON pi.material_name = ecm.material_category_name
              LEFT JOIN supplier_name_mapping snm ON pi.supplier = snm.sw_supplier_name
        """
        if project_name:
            conditions.append(f"project_name LIKE '%{project_name}%'")
        if device_name:
            # 将列表转换为元组，用于 SQL 的 IN 语句
            device_names = tuple(device_name)
            if len(device_names) == 1:
                # 如果列表中只有一个元素，需要特殊处理，因为 SQL 的 IN 语句需要至少两个元素
                conditions.append(f"device_name = '{device_names[0]}'")
            else:
                conditions.append(f"device_name IN {device_names}")

        condition_str = " AND ".join(conditions) if conditions else ""  # 构建条件字符串
        if condition_str:
            po_information_sql += f" WHERE {condition_str}"
        po_information_list = db.get_all(po_information_sql)

        for row in po_information_list:
            po_code = row.get('po_code')
            supplier = row.get('supplier')
            po_create_time = row.get('po_create_time')

            # 过滤掉 po_code、supplier、po_create_time 任意一个为空的数据
            if po_code or supplier or po_create_time:
                data.append({
                    'project_name': row.get('project_name'),
                    'po_code': po_code,
                    'device_name': row.get('device_name'),
                    'supplier': supplier,
                    'po_create_time': po_create_time
                })

        if page_size and page_number:
            # 计算总数
            total = len(data)
            # 计算总页数
            page_size = int(page_size)
            page_number = int(page_number)  # 将字符串转换为整数
            total_pages = (len(data) + page_size - 1) // page_size
            # 检查请求的页码是否超出范围
            if page_number < 1:
                page_number = 1
            if page_number > total_pages:
                page_number = total_pages
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            # 检查索引是否超出范围
            if start_index < 0:
                start_index = 0
            end_index = min(end_index, total)
            page_list = data[start_index:end_index]
            return {
                'listed_files': page_list,
                'total': total
            }
        else:
            total = len(data)
            return {
                'listed_files': data,
                'total': total
            }

    def querying_device_name(self):
        """
            查询设备名称
        """
        data = []
        db = mysql.new_mysql_instance("tbconstruct")
        device_name_sql = \
            """
                SELECT DISTINCT
                  ecm.device_name
                FROM
                  payment_info pi
                  LEFT JOIN equipment_category_mapping_relationship ecm ON pi.material_name = ecm.material_category_name
            """

        device_name_list = db.get_all(device_name_sql)
        for row in device_name_list:
            device_name = row.get('device_name')

            data.append(device_name)

        return data


class ExportPoInformationTable(object):
    """
        导出PO信息表格
    """

    def export_po_information_table(self, data=None):
        """
            导出PO信息表格
        """
        chinese_headers = {
            "project_name": "项目名称",
            "po_code": "PO单号",
            "device_name": "设备名称",
            "supplier": "供应商",
            "po_create_time": "PO下发时间"
        }

        # 创建DataFrame并使用中文表头
        df = pd.DataFrame(data)
        df = df.rename(columns=chinese_headers)

        po_table_file = os.path.join('PO信息表格.xlsx')

        # 使用ExcelWriter以便设置列宽
        with pd.ExcelWriter(po_table_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='PO信息')

            # 获取工作表
            worksheet = writer.sheets['PO信息']

            # 自动调整列宽以适配内容
            for idx, col in enumerate(df.columns):
                # 计算最大宽度
                column_data = df[col].astype(str)
                max_length = 0

                # 检查表头宽度
                header_length = len(col) * 2  # 中文字符宽度约为英文字符的2倍

                # 检查数据宽度
                for cell_value in column_data:
                    # 计算单元格内容的显示宽度
                    cell_length = 0
                    for char in str(cell_value):
                        # 中文字符宽度约为英文字符的2倍
                        if '\u4e00' <= char <= '\u9fff':  # 判断是否为中文字符
                            cell_length += 2
                        else:
                            cell_length += 1
                    max_length = max(max_length, cell_length)

                # 取表头宽度和数据宽度的最大值，并添加一些边距
                final_width = max(header_length, max_length) + 4

                # 设置列宽
                worksheet.column_dimensions[chr(65 + idx)].width = final_width
        cos_path = '/data_json/'
        COSLib.uploadFile(file_name=po_table_file, cos_path=cos_path)
        po_table_url = COSLib.get_file_url(file_name=po_table_file, cos_path=cos_path)
        if po_table_url:
            return {
                "code": 0,
                "message": "导出成功",
                "po_table_url": po_table_url
            }
        else:
            return {
                "code": -1,
                "message": "导出失败"
            }


def main():
    # 初始化 DeliveryScheduleManagement 实例
    dsm = DeliveryScheduleManagement()

    # 测试 dynamic_header 方法
    # print("=== 测试 dynamic_header 方法 ===")
    # dynamic_header_result = dsm.dynamic_header(project_name="清远清新B7-2")
    # print("dynamic_header 结果:", dynamic_header_result)

    # 测试 tabular_data_query 方法
    print("\n=== 测试 tabular_data_query 方法 ===")
    tabular_data_result = dsm.tabular_data_query(project_name="清远清新B7-2")
    print("tabular_data_query 结果:", tabular_data_result)


if __name__ == "__main__":
    main()
