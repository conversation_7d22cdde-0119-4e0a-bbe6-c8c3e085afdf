from datetime import datetime

from iBroker.lib import config, mysql
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


# 获取日期转换的星期
def get_week_day():
    today = datetime.today()
    weekday = today.weekday()
    # 将数字转换为星期几的名称
    weekday_name = [
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六",
        "星期日",
    ]
    day_of_week = weekday_name[weekday]
    return day_of_week


# 将数据转换为树结构
def create_tree(data):
    tree = {}

    for item in data:
        serial_number = item["serial_number"]
        work_content = item["work_content"]
        full_content = f"{serial_number} {work_content}"

        # 跳过第一级只有一个数字的序号
        if (
            serial_number is None
            or not isinstance(serial_number, str)
            or "." not in serial_number
        ):
            continue

        parts = serial_number.split(".")
        current_level = tree
        for i, part in enumerate(parts):
            combined_key = (
                full_content
                if i == len(parts) - 1
                else (
                    f"{'.'.join(parts[:i+1])} "
                    + next(
                        (
                            entry["work_content"]
                            for entry in data
                            if entry["serial_number"] == ".".join(parts[: i + 1])
                        ),
                        "",
                    )
                )
            )
            if combined_key not in current_level:
                current_level[combined_key] = {}
            current_level = current_level[combined_key]
    res = {}
    for key, value in tree.items():
        for key1, value1 in value.items():
            res[key1] = value1
    return res


# 获取今日施工情况表格
def get_today_construction(data):
    result = []
    data_tree = create_tree(data)
    for key, value in data_tree.items():
        if value == {}:
            result.append(
                {
                    "zone": key,
                    "sub_item": "",
                    "specific_content": "",
                    "person_num": "",
                    "progress": "",
                }
            )
            continue
        for key1, value1 in value.items():
            if value1 == {}:
                result.append(
                    {
                        "zone": key,
                        "sub_item": key1,
                        "specific_content": "",
                        "person_num": "",
                        "progress": "",
                    }
                )
                continue
            for key2 in value1.keys():
                result.append(
                    {
                        "zone": key,
                        "sub_item": key1,
                        "specific_content": key2,
                        "person_num": "",
                        "progress": "",
                    }
                )
    return result


# 项目日报自动任务
class ProjectDailyReportAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 定时调度任务自动起单
    def daily_paper_generate(self):
        db = mysql.new_mysql_instance("tbconstruct")
        daily_paper_info = db.get_list(
            "daily_create_info",
            fields=["ticketid", "project_name"],
            conditions={"status": "0"},
        )
        daily_report_old = config.get_config_map("daily_report_old")
        res_list = []
        processed_project_names = set()  # 用于跟踪已处理的 project_name
        for i in daily_paper_info:
            project_name = i.get("project_name")
            if project_name in daily_report_old:
                continue
            if project_name not in processed_project_names:
                now = datetime.now()
                year = now.year
                month = now.month
                day = now.day
                date_string = f"{year}年{month}月{day}号"
                processTitle = date_string + f"{project_name}建设日报"
                data = {
                    "CustomVariables": {
                        "daily_processor": i.get("daily_processor"),
                        "project_name": i.get("project_name"),
                        "ticketid": i.get("ticketid"),
                        "now_time": now.strftime("%Y-%m-%d"),
                    },
                    "ProcessDefinitionKey": "construction_project_daily_report",
                    "Source": "",
                    "TicketDescription": "建设项目日报",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "keketan;reywmwang;v_vikyjiang",
                        "Creator": "reywmwang",
                        "Deal": "reywmwang",
                    },
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                processed_project_names.add(project_name)

        variables = {"res_list": res_list}
        flow.complete_task(self.ctx.task_id, variables=variables)
    
    # 获取项目日报相关数据
    def get_project_imformation(self, project_name):
        tb_db = mysql.new_mysql_instance("tbconstruct")
        project_sql = (
            f"select project_name,campus,PM from "
            f"risk_early_warning_data where project_name = '{project_name}'"
        )
        result_project = tb_db.get_row(project_sql)
        campus = result_project["campus"]
        pm_count = result_project["PM"]
        project = project_name.replace(campus, "")
        select_sql = (
            f"select integrators,project_manager,supervision from "
            f"supplier_resources where campus = '{campus}' and project = '{project}'"
        )
        result = tb_db.get_row(select_sql)
        # 获取监理账号
        supervision_account_sql = (
            f"select account,service_provider from project_role_account "
            f"where project_name = '{project_name}' and role = '监理-总监理工程师' and del_flag = 0"
        )
        supervision_account = ""
        supervision = ""
        result_supervision_account = tb_db.get_row(supervision_account_sql)
        if result_supervision_account:
            supervision_account = result_supervision_account["account"]
            supervision = result_supervision_account["service_provider"]
        elif result:
            supervision = result["supervision"]
        # 获取项管账号
        project_manager_account_sql = (
            f"select account,service_provider from project_role_account where project_name = '{project_name}' "
            f"and (role = '项管-项目经理' or role = '合建方-项目经理') and del_flag = 0"
        )
        project_manager_account = ""
        project_manager = ""
        result_project_manager_account = tb_db.get_row(project_manager_account_sql)
        if result_project_manager_account:
            project_manager_account = result_project_manager_account["account"]
            project_manager = result_project_manager_account["service_provider"]
        elif result:
            project_manager = result["project_manager"]
        # 获取集成商账号
        integrators_account_sql = (
            f"select account,service_provider from project_role_account where project_name = '{project_name}' "
            f"and (role = '集成商-项目经理' or role = '总包-项目经理') and del_flag = 0"
        )
        integrators_account = ""
        integrators = ""
        result_integrators_account = tb_db.get_row(integrators_account_sql)
        if result_integrators_account:
            integrators_account = result_integrators_account["account"]
            integrators = result_integrators_account["service_provider"]
        elif result:
            integrators = result["integrators"]
        sql_plan = (
            "SELECT serial_number, work_content "
            "FROM excel_data "
            f"WHERE project_name = '{project_name}' "
            "AND now_time = "
            "(SELECT MAX(now_time) "
            "FROM excel_data "
            f"WHERE project_name = '{project_name}')"
        )
        result_plan = tb_db.query(sql_plan)
        if result_plan:
            # result_plan = result_plan[0:50]
            overall_control_plan_options = create_tree(result_plan)
        else:
            overall_control_plan_options = {}
        print(overall_control_plan_options)
        date = datetime.now().strftime("%Y-%m-%d")
        sql_plan_today = (
            "SELECT serial_number, work_content "
            "FROM excel_data "
            f"WHERE project_name = '{project_name}' "
            "AND now_time = "
            "(SELECT MAX(now_time) "
            "FROM excel_data "
            f"WHERE project_name = '{project_name}') "
            "AND CURDATE() BETWEEN start_time AND completion_time"
        )
        result_plan_today = tb_db.query(sql_plan_today)
        construction_situation_table = []
        if result_plan_today:
            construction_situation_table = get_today_construction(result_plan_today)
        week = get_week_day()
        dailyReportData = {
            "project_name": project_name,
            "integrator": integrators,
            "project_manager": project_manager,
            "supervision": supervision,
            "report_date": date,
            "week": week,
        }
        variables = {
            "dailyReportData": dailyReportData,
            "supervision_account": supervision_account,
            "project_manager_account": project_manager_account,
            "integrators_account": integrators_account,
            "campus": campus,
            "project": project,
            "pm_count": pm_count,
            "isReadonly": False,
            "overall_control_plan_options": overall_control_plan_options,
            "construction_situation_table": construction_situation_table,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
    
    def save_daily_report(self):
        report_data = self.ctx.variables.get("dailyReportData")
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        pm_count = self.ctx.variables.get("pm_count")
        tb_db = mysql.new_mysql_instance("tbconstruct")
        project_name = report_data["project_name"]
        report_date = report_data["report_date"]
        # 判断 material_situation_table 是否为空
        if "material_situation_table" not in report_data:
            material_situation_table = []
        else:
            material_situation_table = report_data["material_situation_table"]
        # 判断 device_situation_table 是否为空
        if "device_situation_table" not in report_data:
            device_situation_table = []
        else:
            device_situation_table = report_data["device_situation_table"]
        # 判断 construction_situation_table 是否为空
        if "construction_situation_table" not in report_data:
            construction_situation_table = []
        else:
            construction_situation_table = report_data["construction_situation_table"]
        if "pictures_table" not in report_data:
            pictures_table = []
        else:
            pictures_table = report_data["pictures_table"]
        insert_project_report = report_data
        report_url = config.get_config_string("date_report_url")
        report_url = f"{report_url}{campus}&project={project}&tab=ProjectDailyReport"
        report_url_out = config.get_config_string("date_report_url_out")
        tb_db.begin()
        tb_db.insert("project_daily_report", insert_project_report)
        query_sql = (
            f"select id from project_daily_report where project_name = '{project_name}' "
            f"and report_date = '{report_date}' order by created_at desc"
        )
        report_id = tb_db.query(query_sql)
        report_id = report_id[0]["id"]
        if len(material_situation_table) != 0:
            for item in material_situation_table:
                item["project_report_id"] = report_id
            tb_db.insert_batch("material_daily_situation", material_situation_table)
        if len(device_situation_table) != 0:
            for item in device_situation_table:
                item["project_report_id"] = report_id
            tb_db.insert_batch("device_daily_situation", device_situation_table)
        if len(construction_situation_table) != 0:
            for item in construction_situation_table:
                update_data = {"daily_progress": item["progress"]}
                cond ={}
                item["project_report_id"] = report_id
                if "specific_content" in item and item["specific_content"] != "":
                    cond = {"serial_number": item["specific_content"].split(" ")[0]}
                elif "sub_item" in item and item["sub_item"] != "":
                    cond = {"serial_number": item["sub_item"].split(" ")[0]}
                else:
                    cond = {"serial_number": item["zone"].split(" ")[0]}
                if len(cond) != 0:
                    cond["project_name"] = project_name
                    tb_db.update(
                        "construct_plan_data",
                        update_data,
                        cond,
                    )
                if "specific_content" not in item:
                    item["specific_content"] = ""
                if "sub_item" not in item:
                    item["sub_item"] = ""
            tb_db.insert_batch(
                "construction_daily_situation", construction_situation_table
            )
        if len(pictures_table) != 0:
            picture_save = []
            for item in pictures_table:
                if "picture_url" not in item :
                    continue
                picture_save.append({
                    "project_report_id": report_id,
                    "picture_url": item.get("picture_url"),
                    "picture_title": item.get("picture_title"),
                })
            if len(picture_save) != 0:
                tb_db.insert_batch("picture_daily_report", picture_save)
        tb_db.commit()

        # 邮件发送
        mail_title = f"{project_name}项目日报({report_date})"
        project_manager_email = ""
        # 获取项管邮箱
        project_manager_email_sql = (
            "select email from project_role_account "
            f"where project_name = '{project_name}' and role = '项管-项目经理' "
            "and del_flag = 0"
        )
        result_project_manager_email = tb_db.get_row(project_manager_email_sql)
        if result_project_manager_email:
            project_manager_email = result_project_manager_email["email"]
        # 如果是合建项目没有获取到项管账号，就获取 合建项管账号
        else:
            project_manager_email_sql = (
                f"select email from project_role_account "
                f"where project_name = '{project_name}' and role = '合建方-项目经理' and del_flag = 0"
            )
            result_project_manager_email = tb_db.get_row(project_manager_email_sql)
            if result_project_manager_email:
                project_manager_email = result_project_manager_email["email"]
        # 获取监理邮箱
        supervision_email = ""
        supervision_email_sql = (
            "select email from project_role_account "
            f"where project_name = '{project_name}' and role = '监理-总监理工程师' "
            "and del_flag = 0"
        )
        result_supervision_email = tb_db.get_row(supervision_email_sql)
        if result_supervision_email:
            supervision_email = result_supervision_email["email"]
        # 如果是合建项目没有获取到 集成商-项目经理 账号，就获取 总包-项目经理 账号
        integrators_email = ""
        integrators_email_sql = (
            "select email from project_role_account "
            f"where project_name = '{project_name}' and role = '集成商-项目经理' "
            "and del_flag = 0"
        )
        result_integrators_email = tb_db.get_row(integrators_email_sql)
        if result_integrators_email:
            integrators_email = result_integrators_email["email"]
        else:
            integrators_email_sql = (
                "select email from project_role_account "
                f"where project_name = '{project_name}' and role = '总包-项目经理' "
                "and del_flag = 0"
            )
            result_integrators_email = tb_db.get_row(integrators_email_sql)
            if result_integrators_email:
                integrators_email = result_integrators_email["email"]
        email_receiver_list = [
            project_manager_email,
            supervision_email,
            integrators_email,
            pm_count,
            "reywmwang",
            "youngshi",
        ]
        # email_receiver_list = ["reywmwang"]

        rich_text_template = (
            f"<p>请查收{project_name}项目日报({report_date})</p><p><a href={report_url}>内网附件链接</a></p>"
            f"<p><a href={report_url_out}>外网附件链接</a></p>"
        )

        tof.send_email(
            sendTitle=mail_title,
            msgContent=rich_text_template,
            sendTo=email_receiver_list,
        )
        variables = {
            "email_receiver_list": email_receiver_list,
            "email_title": mail_title,
            "email_content": rich_text_template,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
