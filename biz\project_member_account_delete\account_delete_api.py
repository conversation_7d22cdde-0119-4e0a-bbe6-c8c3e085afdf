from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class AccountDeleteApi(object):
    # 查询项目所有成员
    @staticmethod
    def query_project_member_info(project_name):
        query_sql = f"select * from project_role_account where project_name = '{project_name}' and del_flag = '0'"
        db = mysql.new_mysql_instance("tbconstruct")
        query_result = db.query(query_sql)
        for item in query_result:
            if "account" in item:
                account_use_info = AccountDeleteApi.query_account_use_info(
                    item.get("account")
                )
                item.setdefault("account_use_info", account_use_info)
        return query_result

    # 查询账号使用情况
    @staticmethod
    def query_account_use_info(account):
        if account:
            db = mysql.new_mysql_instance("tbconstruct")
            sql = f"SELECT * FROM project_role_account WHERE account='{account}' and del_flag=0"
            result = db.get_all(sql)
            if result:
                account_use_info = []
                for exist_data in result:
                    exist_project = exist_data.get("project_name")
                    exist_role = exist_data.get("role")
                    account_use_info.append(f"{exist_project}[{exist_role}]")
                return (";\n").join(account_use_info)
            else:
                return "未存在已开通账号权限"
        return "账号获取失败"

    # 判断是否需要删除企微账号（需要：只有该项目有使用且全部勾选；不需要：其它项目中有使用 or 只有该项目使用但未全部勾选；）
    @staticmethod
    def delete_wework_account_determine(project_name, del_member_list):
        """
        判断是否需要删除企微账号
        (设计院和厂家不删; 只判断项管、监理、集成商)
        （需要：只有该项目有使用且全部勾选；不需要：其它项目中有使用 or 只有项目使用但未全部勾选；）
        """
        # 先计算勾选账号中相同账号的数量
        del_account_num_dict = {}
        for item in del_member_list:
            account = item.get("account")
            id = item.get("id")
            if account not in del_account_num_dict:
                del_account_num_dict[account] = {"num": 1, "id": [id]}
            else:
                del_account_num_dict[account]["num"] += 1
                del_account_num_dict[account]["id"].append(id)
        db = mysql.new_mysql_instance("tbconstruct")
        for item in del_member_list:
            if "account" in item:
                account = item.get("account")
                role = item.get("role")
                sql = f"SELECT * FROM project_role_account WHERE account='{account}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    if "厂家" in role or "设计院" in role:
                        item["is_truly_del_account"] = "否"
                    elif "集成商" in role or "项管" in role or "监理" in role:
                        # 必须详细比对，只用条数比对不准，可能存在其它工单修改数据的情况
                        other_project = False
                        this_project_num = 0
                        this_project_id = []
                        for exist_data in result:
                            exist_id = exist_data.get("id")
                            exist_project = exist_data.get("project_name")
                            if exist_project != project_name:
                                # 其它项目中有使用
                                other_project = True
                            else:
                                # 只有项目使用
                                this_project_num += 1
                                # 判断勾选的数据是否是同一条
                                if exist_id not in del_account_num_dict[account]["id"]:
                                    this_project_id.append(exist_id)
                        if other_project:
                            item["is_truly_del_account"] = "否"
                        else:
                            if this_project_num > del_account_num_dict[account]["num"]:
                                # 未全部勾选
                                item["is_truly_del_account"] = "否"
                            elif (
                                this_project_num == del_account_num_dict[account]["num"]
                            ):
                                # 全部勾选 （还需判断勾选的角色对不对，可通过id判断）
                                if this_project_id:
                                    item["is_truly_del_account"] = "否"
                                else:
                                    item["is_truly_del_account"] = "是"
                            elif (
                                this_project_num < del_account_num_dict[account]["num"]
                            ):
                                # 其它工单删除
                                item["is_truly_del_account"] = "错误，请更新账号信息"
                # 更新账号使用情况（可能其它工单完成修改过数据）
                account_use_info = AccountDeleteApi.query_account_use_info(account)
                item["account_use_info"] = account_use_info
        return del_member_list

    # 查询企业微信账号删除流程建单工单状态和删除结果
    @staticmethod
    def query_del_ticket_info(del_member_list_confirm):
        # 重复工单可直接复用结果，无需重新查询
        ticket_status_dict = {}
        # 重复账号可直接复用结果，无需重新查询
        del_account_status_dict = {}
        for item in del_member_list_confirm:
            ticket_id = item.get("ticket_id")
            if ticket_id:
                userid = item.get("account")
                if ticket_id not in ticket_status_dict:
                    query_data = {
                        "SchemaId": "ticket_base",
                        "Data": {
                            "ResultColumns": {"TicketStatus": "", "TicketId": ""},
                            "SearchCondition": {"TicketId": [str(ticket_id)]},
                        },
                    }
                    query_result = gnetops.request(
                        action="QueryData", method="Run", ext_data=query_data
                    )
                    # item.setdefault("query_result", query_result)
                    result_list = query_result.get("List")
                    for res in result_list:
                        if res.get("TicketId") == str(ticket_id):
                            TicketStatus = res.get("TicketStatus")
                            ticket_status = (
                                "运行中"
                                if res.get("TicketStatus") == "OPEN"
                                else "已结单"
                            )
                            item["ticket_status"] = ticket_status
                            ticket_status_dict[ticket_id] = ticket_status
                            # 可以认为除了OPEN是运行中外，其它的都是已结单
                            # 已结单的需查询企微账号删除结果
                            if TicketStatus != "OPEN":
                                data = {
                                    "userid": userid,
                                    "company": "犀牛鸟有限责任公司",
                                }
                                result = gnetops.request(
                                    action="WeWork", method="QueryWeWorkUser", data=data
                                )
                                if (
                                    result["Result"]["errcode"] == 0
                                    and result["Result"]["userid"] == userid
                                ):
                                    item["del_account_status"] = "删除失败"
                                    del_account_status_dict[userid] = "删除失败"
                                else:
                                    item["del_account_status"] = "删除成功"
                                    del_account_status_dict[userid] = "删除成功"
                else:
                    item["ticket_status"] = ticket_status_dict.get(ticket_id)
                    item["del_account_status"] = del_account_status_dict.get(userid)
        return del_member_list_confirm

    # 获取待办转移人（上级）
    @staticmethod
    def get_new_todo_user(project_name, role):
        """
        获取转单信息（转给上级）
        往上：
            集成商-项目经理
            监理-总监理工程师
            项管-项目经理
        再往上：
            项目PM
        再往上：
            youngshi;yanjunchen;
        """
        # 查项目PM
        PM = AccountDeleteApi.get_PM(project_name)
        new_todo_user = PM
        # 查leader
        if "项管" in role:
            if role != "项管-项目经理":
                # 转给项管-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "项管-项目经理"
                )
        elif "集成商" in role:
            if role != "集成商-项目经理":
                # 转给集成商-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "集成商-项目经理"
                )
        elif "监理" in role:
            if role != "监理-总监理工程师":
                # 转给监理-总监理工程师
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "监理-总监理工程师"
                )
        elif "合建方" in role:
            if role != "合建方-项目经理":
                # 转给合建方-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "合建方-项目经理"
                )
        elif "总包" in role:
            if role != "总包-项目经理":
                # 转给总包-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "总包-项目经理"
                )
        return new_todo_user

    # 查询项目PM
    @staticmethod
    def get_PM(project_name):
        PM = "youngshi"
        query_sql = f"SELECT PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        if query_result:
            PM = query_result[0].get("PM", "youngshi")
        return PM

    # 查询项目role leader
    @staticmethod
    def get_role_leader(project_name, role):
        # 查项目PM
        PM = AccountDeleteApi.get_PM(project_name)
        leader = PM
        query_sql = (
            f"select account from project_role_account "
            f"where project_name = '{project_name}' and role = '{role}' and del_flag=0"
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        if query_result:
            leader = query_result[0].get("account", PM)
        return leader

    # 获取同角色待办转移人（没有再给上级）
    @staticmethod
    def get_same_role_new_todo_user(project_name, role):
        """
        获取转单信息（先转给同角色处理人，若没有同角色处理人才转给上级）
        往上：
            集成商-项目经理
            监理-总监理工程师
            项管-项目经理
        再往上：
            项目PM
        再往上：
            youngshi;yanjunchen;
        """
        # 转给同角色处理人：
        new_todo_user = AccountDeleteApi.get_role_leader(project_name, role)
        # 查项目PM
        PM = AccountDeleteApi.get_PM(project_name)
        # new_todo_user == PM 没有找到同角色处理人
        if new_todo_user != PM:
            return new_todo_user
        if "项管" in role:
            if role != "项管-项目经理":
                # 转给项管-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "项管-项目经理"
                )
        elif "集成商" in role:
            if role != "集成商-项目经理":
                # 转给集成商-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "集成商-项目经理"
                )
        elif "监理" in role:
            if role != "监理-总监理工程师":
                # 转给监理-总监理工程师
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "监理-总监理工程师"
                )
        elif "合建方" in role:
            if role != "合建方-项目经理":
                # 转给合建方-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "合建方-项目经理"
                )
        elif "总包" in role:
            if role != "总包-项目经理":
                # 转给总包-项目经理
                new_todo_user = AccountDeleteApi.get_role_leader(
                    project_name, "总包-项目经理"
                )
        return new_todo_user
