from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.wework_dept.wework_dept_api import (
    WeworkDeptApi,
)


# 组织架构名称修改
class WeworkDeptUpdate(AjaxTodoBase):
    def __init__(self):
        super(WeworkDeptUpdate, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # old_project_name = process_data.get("old_project_name")
        new_project_name = process_data.get("new_project_name")
        dept_list = WeworkDeptApi.query_dept()
        dept_name_list = [item.get("label") for item in dept_list]
        if new_project_name not in dept_name_list:
            return {
                "code": -1,
                "msg": f"暂未查询到新组织架构: {new_project_name} <br>请确认是否修改成功！",
            }
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转流程
        flow.complete_task(self.ctx.task_id)
