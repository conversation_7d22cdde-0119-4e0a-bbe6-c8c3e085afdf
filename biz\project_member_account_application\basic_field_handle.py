from iBroker.lib import mysql


# 模板操作
class BasicFieldHandle(object):

    # 获取项目成员基础表数据
    @staticmethod
    def get_basic_info(field_name):
        query_sql = f"select field_value from project_member_basic_info where field_name = '{field_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        list = []
        if query_result and query_result[0]["field_value"]:
            list = [
                field_value
                for field_value in query_result[0]["field_value"].split(";")
                if field_value != ""
            ]
        return list

    # 将list数据处理成前端下拉选项数据
    @staticmethod
    def get_options_info(list):
        option_list = []
        if list:
            option_list = [
                {"label": field_value, "value": field_value}
                for field_value in list
                if field_value != ""
            ]
        return option_list
