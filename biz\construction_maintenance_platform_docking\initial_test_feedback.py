import json
from datetime import datetime
from iBroker.lib.sdk import flow
from iBroker.lib.sdk import tof
from iBroker.lib import curl
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


class GetFilenameAndUrl(object):
    def get_filename_and_url(self, file_list, deal_users, time):
        new_list = []
        try:
            for file in file_list:
                file_name = file.get("name")
                file_url = file["response"]["FileList"][0]["url"]
                new_list.append({'file_name': file_name, 'file_url': file_url, "uploader": deal_users, 'up_time': time})
        except KeyError:
            return 1
        except IndexError:
            return 1
        return new_list


class InitialTestFeedbackUploadData(AjaxTodoBase):
    """
        初验反馈流程：初验资料上传
    """

    def __init__(self):
        super(InitialTestFeedbackUploadData, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        deal_users = self.ctx.variables.get("PM")
        project_name = self.ctx.variables.get("project_name")
        real_time = process_data.get("real_time")
        initial_report = process_data.get("initial_report")
        remark = process_data.get("remark")
        appendix = process_data.get("appendix")
        # 获取当前日期和时间
        now = datetime.now()
        # 格式化日期为 '年-月-日' 格式的字符串
        formatted_date = now.strftime('%Y-%m-%d')

        report_list = []
        appendix_list = []
        if initial_report:
            report_list = GetFilenameAndUrl().get_filename_and_url(initial_report, deal_users, formatted_date)
            if report_list == 1:
                return {"code": -1, "msg": "初验报告上传错误"}
        if appendix:
            appendix_list = GetFilenameAndUrl().get_filename_and_url(appendix, deal_users, formatted_date)
            if appendix_list == 1:
                return {"code": -1, "msg": "附件上传错误"}

        tb_db = mysql.new_mysql_instance("tbconstruct")
        data = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "initial_report": str(report_list),
            "appendix": str(appendix_list),
            "remark": remark,
            'real_time': real_time,
        }
        # 存数据库
        tb_db.begin()
        tb_db.insert("initial_inspection_feedback_process", data)
        tb_db.commit()
        variables = {
            "real_time": real_time,
            "initial_report": initial_report,
            "appendix": appendix,
            'remark': remark,
            'report_list': report_list,
            'appendix_list': appendix_list,
            'deal_users': deal_users
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class InitialTestFeedbackCallingInterface(object):
    """
            初验反馈流程：调用接口传递数据
        """

    def __init__(self):
        super(InitialTestFeedbackCallingInterface, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def calling_interface(self):
        project_name = self.ctx.variables.get("project_name")
        report_list = self.ctx.variables.get("report_list")
        appendix_list = self.ctx.variables.get("appendix_list")
        real_time = self.ctx.variables.get("real_time")
        PM = self.ctx.variables.get("PM")

        # 附件列表
        attachmentUrls = []
        # 验收报告列表
        inspectionReportUrls = []
        if report_list:
            for report in report_list:
                inspectionReportUrls.append({
                    "key": report.get('file_url'),
                    "value": report.get('file_name')
                })
        if appendix_list:
            for appendix in appendix_list:
                attachmentUrls.append({
                    "key": appendix.get('file_url'),
                    "value": appendix.get('file_name')
                })
        # 获取项目编号和流程单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT item_number,instance_id FROM initial_inspection_feedback_basic_info " \
              "WHERE project_name='%s'" % project_name
        id_list = db.get_all(sql)
        item_number = ''
        instance_id = ''
        if id_list:
            item_number = id_list[0].get('item_number')
            instance_id = id_list[0].get('instance_id')
        else:
            self.workflow_detail.add_kv_table('获取项目编号和流程单号',
                                              {"success": False, "data": "获取失败"})
        data = {
            "idcrmProjectCode": item_number,
            "instanceId": instance_id,
            "nodeType": 2,
            "nodeTypeState": "初验完成",
            "operatorId": PM,
            "attachmentUrls": attachmentUrls,
            "planReceptionTime": '',
            "receptionTime": real_time,
            "inspectionReportUrls": inspectionReportUrls
        }
        if instance_id and item_number:
            # result = curl.post(title="Dcops请求数全通平台接口",
            #                    system_name="Dcops",
            #                    url="http://9.134.186.204:80/newapi/idcplatform/SyncInstallStatus",
            #                    postdata=json.dumps(data),
            #                    append_header={"Content-Type": "application/json"})

            variables = {
                "item_number": item_number,
                "instance_id": instance_id,
                "attachmentUrls": attachmentUrls,
                "inspectionReportUrls": inspectionReportUrls,
                "data": data
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

    def email_notification(self, project_name):
        # 获取项目编号和流程单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT PM FROM project_team_information_data " \
              "WHERE project_name='%s'" % project_name
        email_receiver = db.get_all(sql)
        email_receiver_list = []
        if email_receiver:
            item = email_receiver[0]
            for key, value in item.items():
                name_list = value.split(';')
                for name in name_list:
                    email_receiver_list[name] += "@tencent.com"
        email_title = ''
        email_content = ''
        tof.send_email(sendTitle=email_title, msgContent=email_content,
                       sendTo=email_receiver_list,
                       )
        flow.complete_task(self.ctx.task_id)
