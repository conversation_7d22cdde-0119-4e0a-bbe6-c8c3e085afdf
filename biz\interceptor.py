# -*- coding: utf-8 -*-
# @author: be<PERSON><PERSON>
# @software: PyCharm
# @file: interceptor.py.py
# @time: 2021/8/4 22:53
# -*- coding: utf-8 -*-
"""
Exception Interception function.
"""

import json
from iBroker.sdk.nms.tnc import TNCCallException, TNCTimeoutException
from iBroker.sdk.task.context import get_context
from iBroker.sdk.nms.ticket import TicketUploadProcessMessage
from iBroker.sdk.nms.tmp import NetchangeStrategyDelete
from iBroker.sdk.todo.todo_base import NetOpsTodoCreateProxy


def exception_todo(exc):
    data = {
        "key": "nbroker.protocol.AjaxExceptionTodo",
        "name": "系统异常处理",
        "ajax_service": "AjaxExceptionTodo",
        "is_end": "{{ is_end }}",
        "basic_views": [
            {
                "name": "流程ID",
                "type": "KV<PERSON>ield",
                "data": "{{ ctx.instance_id }}"
            },
            {
                "name": "任务ID",
                "type": "KVField",
                "data": "{{ ctx.task_id }}"
            },
            {
                "name": "主流程ID",
                "type": "KVField",
                "data": "{{ ctx.ajax_variables.parent_id}}"
            }
        ],
        "todo_views": [
            {
                "name": "异常详情",
                "type": "TextArea",
                "key": "exception",
                "data": str(exc)
            }
        ],
        "operate_views": [
            {
                "data": 0,
                "key": "exception_todo",
                "name": "请手工确认处理本异常后，再继续流程",
                "options": [
                    {
                        "name": "跳过",
                        "data": 0
                    },
                    {
                        "name": "重试",
                        "data": 1
                    }
                ],
                "type": "Radio"
            },
            {
                "name": "提交",
                "key": "submit_msg",
                "type": "Button",
                "callback_method": "end"
            }
        ]
    }
    NetOpsTodoCreateProxy().todo_create(todo_key="AjaxExceptionTodo", todo_name="通用系统异常处理",
                                        protocol_data=json.dumps(data))


def tnc_abnormal_interceptor(exc):
    ctx = get_context()
    if isinstance(exc, TNCCallException):
        TicketUploadProcessMessage(ticket_id=ctx.variables["old_ticket_id"], title='Netops TNC异常，请人工处理',
                                   content=str(exc)).call()
    if isinstance(exc, TNCTimeoutException):
        TicketUploadProcessMessage(ticket_id=ctx.variables["old_ticket_id"], title='Netops  TNC超时， 请人工处理',
                                   content=str(exc)).call()
    exception_todo(exc)
    return False


def common_abnormal_interceptor(exc):
    ctx = get_context()
    if "netchange_id" in ctx.variables:
        netchange_id = ctx.variables["netchange_id"]
        NetchangeStrategyDelete(netchange_id).call()
    if "old_ticket_id" in ctx.variables:
        TicketUploadProcessMessage(ticket_id=ctx.variables["old_ticket_id"], title='Netops 程序异常，请人工处理',
                                   content=str(exc)).call()
    exception_todo(exc)
    return True


def common_interceptor(exc):
    exception_todo(exc)
    return True
