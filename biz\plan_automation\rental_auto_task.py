from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config
from iBroker.sdk.notification.chatops import ChatOpsSend
from datetime import datetime
from dateutil.relativedelta import relativedelta
import json

from biz.plan_automation.tools import Tools


# 租赁项目
class RentalPlanAutomationAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 【租赁】获取建设中项目列表
    def get_project_list(self):
        """
        【租赁】获取建设中项目列表
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT t1.project_name FROM risk_early_warning_data t1 "
            "JOIN summary_data t2 "
            "ON t1.project_name = CONCAT(t2.campus, t2.project_name) "
            "WHERE t2.state = '建设中' AND t1.construction_mode != '自建'"
        )
        project_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.建设中租赁项目", {"message": project_list})
        variables = {"project_list": project_list}
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】获取项目关键节点最新总控计划 + 园区、项目、PM
    def get_plan(self, project_list):
        """
        【租赁】获取项目关键节点最新总控计划
        """
        key_node_dict = {
            "项目报建": "2.%",
            "市电专项": "2.%",
            "项目开办": "2.%",
            "安装施工": "3",
            "测试验证": "4",
            "验收转维": "5",
        }
        project_plan_dict = {}
        for p in project_list:
            project_name = p.get("project_name")
            project_plan_dict[project_name] = []
            db = mysql.new_mysql_instance("tbconstruct")
            for key, node in key_node_dict.items():
                sql = self.get_plan_sql(project_name, node, key)
                plan_list = db.query(sql)
                if plan_list:
                    project_plan_dict[project_name].append(*plan_list)
        variables = {
            "project_plan_dict": project_plan_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】生成sql 获取项目关键节点最新总控计划 + 园区、项目、PM
    def get_plan_sql(self, project_name, node, key):
        sql = (
            "SELECT t1.project_name, t1.serial_number, t1.work_content, t1.start_time, t1.completion_time, "
            "t3.campus, t3.project, t3.PM, "
            f"'{key}' AS key_node "
            "FROM construct_plan_data t1 "
            "JOIN risk_early_warning_data t3 ON t1.project_name = t3.project_name "
            f"WHERE t1.project_name = '{project_name}' AND t1.serial_number LIKE '{node}' "
            f"AND t1.work_content LIKE '%{key}%' "
            "AND t1.not_involved = 0"
        )
        return sql

    # 【租赁】计划解析：根据计划开始时间建单
    def plan_analysis(self, project_plan_dict):
        """
        【租赁】计划解析：根据计划开始时间建单
        """
        project_create_ticket_dict = {}
        create_ticket_info_list = []
        for project_name, plan_info in project_plan_dict.items():
            for p in plan_info:
                serial_number = p.get("serial_number")
                work_content = p.get("work_content")
                key_node = p.get("key_node")
                start_time = p.get("start_time")
                end_time = p.get("completion_time")
                arg_info = {
                    "project_name": project_name,
                    "campus": p.get("campus"),
                    "project": p.get("project"),
                    "PM": p.get("PM"),
                }
                # 建单
                res = self.create_ticket_analysis(
                    key_node, start_time, end_time, arg_info
                )
                # p["create_ticket_info"] = res
                item = {
                    "project_name": project_name,
                    "PM": p.get("PM"),
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": p.get("start_time"),
                    "end_time": p.get("completion_time"),
                    "msg": res.get("msg"),
                    "ticket": res.get("data"),
                }
                create_ticket_info_list.append(item)
                if project_name not in project_create_ticket_dict:
                    project_create_ticket_dict[project_name] = []
                project_create_ticket_dict[project_name].append(item)
                # 已创建工单 给 PM、youngshi、viky推送消息（viky需整理到"自建租赁项目进展"在线表格）
                if res.get("msg") == "已创建工单":
                    acceptance_result_receivers = config.get_config_map(
                        "acceptance_result_receivers"
                    )
                    user_list = [p.get("PM"), *acceptance_result_receivers]
                    ticket_id = res.get("data")[0]
                    env = config.get_env_flag(False)
                    if env == "product":
                        url = "https://dcops.woa.com/appManage/tickets/details"
                    else:
                        url = "https://dcops.test.woa.com/appManage/tickets/details"
                    oaUrl = f"{url}?params={ticket_id}"
                    text = (
                        f"{project_name}已按计划自动创建工单：\n"
                        f"【项目名称】：{project_name}\n"
                        f"【对应计划】：{serial_number}{work_content}\n"
                        f"【计划开始时间】：{p.get('start_time')}\n"
                        f"【工单号】：[{ticket_id}]({oaUrl})\n"
                    )
                    for user in user_list:
                        ChatOpsSend(
                            user_name=user,
                            msg_content=text,
                            msg_type="markdown",
                            des_type="single",
                        ).call()
        variables = {
            "project_plan_dict": project_plan_dict,
            "create_ticket_info_list": create_ticket_info_list,
            "project_create_ticket_dict": project_create_ticket_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】
    def query_exit_ticket(self, project_name, key_node, is_open=False):
        flow_dict = {
            "项目报建": [
                "joint_project_construction_application",
            ],
            "项目开办": [
                "joint_construction_before_equipment_arrival",
            ],
            "市电专项": [
                "joint_mains_power_special",
            ],
            "安装施工": ["joint_construction_project_site_implementation"],
            "测试验证": ["construct_test_phase_bak"],
            "验收转维": ["joint_construction_maintenance_docking"],
        }
        flow_list = flow_dict.get(key_node)
        if flow_list:
            flow_temp = ", ".join(f"'{item}'" for item in flow_list)
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT * FROM all_construction_ticket_data "
                f"WHERE Title like '%{project_name}%' "
                "AND IsForceEnd = '0' "
                f"AND ProcessDefinitionKey IN ({flow_temp})"
            )
            if is_open:
                sql += " AND TicketStatus != 'END'"
            res = db.query(sql)
            return res
        return False

    # 【租赁】自动建单逻辑判断
    def create_ticket_analysis(self, key_node, start_time, end_time, arg_info):
        """
        2.->项目报建
        2.->市电专项
        2.->项目开办
        3->安装施工
        4->测试验证 (前20天建单)
        5->验收转维 (前20天建单)
        """
        if start_time and end_time:
            today = datetime.today().date()
            start_time_temp = datetime.strptime(start_time, "%Y-%m-%d").date()
            start_time_20_days_ago = start_time_temp - relativedelta(days=20)
            end_time_temp = datetime.strptime(end_time, "%Y-%m-%d").date()
            project_name = arg_info.get("project_name")
            key_node_today = ["项目报建", "市电专项", "项目开办", "安装施工"]
            key_node_20_days_ago = ["测试验证", "验收转维"]
            if (
                today >= start_time_temp
                and today <= end_time_temp
                and key_node in key_node_today
            ):
                exit_ticket = self.query_exit_ticket(project_name, key_node)
                if exit_ticket:
                    res = {
                        "msg": "已存在工单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    flow_info_dict = self.get_flow_info(key_node, arg_info)
                    if flow_info_dict:
                        create_ticket = Tools.create_ticket(flow_info_dict)
                        res = {
                            "msg": "已创建工单",
                            "data": [str(create_ticket.get("TicketId"))],
                        }
                    else:
                        res = {
                            "msg": "流程信息获取失败，无法自动建单",
                            "data": None,
                        }
                return res
            elif (
                today >= start_time_20_days_ago
                and today <= end_time_temp
                and key_node in key_node_20_days_ago
            ):
                exit_ticket = self.query_exit_ticket(project_name, key_node)
                if exit_ticket:
                    res = {
                        "msg": "已存在工单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    flow_info_dict = self.get_flow_info(key_node, arg_info)
                    if flow_info_dict:
                        create_ticket = Tools.create_ticket(flow_info_dict)
                        res = {
                            "msg": "已创建工单",
                            "data": [str(create_ticket.get("TicketId"))],
                        }
                    else:
                        res = {
                            "msg": "流程信息获取失败，无法自动建单",
                            "data": None,
                        }
                return res
            else:
                exit_ticket = self.query_exit_ticket(project_name, key_node)
                if exit_ticket:
                    res = {
                        "msg": "平台已有工单，无需自动建单",
                        "data": [item.get("TicketId") for item in exit_ticket],
                    }
                else:
                    res = {
                        "msg": "无需自动建单(不在计划时间范围内)；平台未查询到工单，若需要补单或有其它疑问请联系管理员",
                        "data": None,
                    }
                return res
        else:
            return {"msg": "计划开始/完成时间为空，无需自动建单", "data": None}

    # 【租赁】获取流程建单信息
    def get_flow_info(self, key_node, arg_info):
        project_name = arg_info.get("project_name")
        campus = arg_info.get("campus")
        PM = arg_info.get("PM")
        flow_dict = {
            "项目报建": {
                "ProcessDefinitionKey": "joint_project_construction_application",
                "processTitle": f"{project_name}项目报建过程记录",
                "CustomVariables": {"project_name": project_name},
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
            "市电专项": {
                "ProcessDefinitionKey": "joint_mains_power_special",
                "processTitle": f"{project_name}市电专项流程",
                "CustomVariables": {"project_name": project_name},
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
            "项目开办": {
                "ProcessDefinitionKey": "joint_construction_before_equipment_arrival",
                "processTitle": f"{project_name}项目施工前准备阶段",
                "CustomVariables": {
                    "project_name": project_name,
                },
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
            "安装施工": {
                "ProcessDefinitionKey": "joint_construction_project_site_implementation",
                "processTitle": f"{project_name}项目安装施工",
                "CustomVariables": {
                    "project_name": project_name,
                },
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
            "测试验证": {
                "ProcessDefinitionKey": "construct_test_phase_bak",
                "processTitle": f"{project_name}第三方测试启动",
                "CustomVariables": {
                    "project_name": project_name,
                },
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
            "验收转维": {
                "ProcessDefinitionKey": "joint_construction_maintenance_docking",
                "processTitle": f"{project_name}建设转维过程跟踪",
                "CustomVariables": {
                    "project_name": project_name,
                },
                "Concern": "v_zongxiyu",
                "Deal": "v_zongxiyu",
            },
        }
        flow_info_dict = flow_dict.get(key_node)
        return flow_info_dict

    # 【租赁】催办：获取项目关键节点最新总控计划
    def get_plan2(self, project_list):
        """
        【租赁】催办：获取项目关键节点最新总控计划
        """
        key_node_dict = {
            "项目报建": "2.%",
            "市电专项": "2.%",
            "项目开办": "2.%",
            "安装施工": "3",
            "测试验证": "4",
            "验收转维": "5",
        }
        project_plan_dict2 = {}
        for p in project_list:
            project_name = p.get("project_name")
            project_plan_dict2[project_name] = []
            db = mysql.new_mysql_instance("tbconstruct")
            for key, node in key_node_dict.items():
                sql = self.get_plan_sql(project_name, node, key)
                plan_list = db.query(sql)
                if plan_list:
                    project_plan_dict2[project_name].append(*plan_list)
        variables = {
            "project_plan_dict2": project_plan_dict2,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】计划解析：根据计划完成时间催办
    def plan_analysis2(self, project_plan_dict2):
        """
        计划解析：根据计划完成时间催办
        """
        reminder_ticket_info_list = []
        # project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        for project_name, plan_info in project_plan_dict2.items():
            # jcs = project_jcs_dict.get(project_name)
            for p in plan_info:
                key_node = p.get("key_node")
                end_time = p.get("completion_time")
                reminder_flag = self.reminder_ticket_analysis(key_node, end_time)
                if reminder_flag:
                    exit_ticket = self.query_exit_ticket(project_name, key_node, True)
                    if exit_ticket:
                        for t in exit_ticket:
                            cost_time = Tools.get_max_cost_time(
                                json.loads(t.get("CurrentTaskDetails"))
                            )
                            item = {
                                "project_name": project_name,
                                "PM": p.get("PM"),
                                "serial_number": p.get("serial_number"),
                                "work_content": p.get("work_content"),
                                "start_time": p.get("start_time"),
                                "end_time": p.get("completion_time"),
                                "ticket_id": t.get("TicketId"),
                                "title": t.get("Title"),
                                "create_time": t.get("CreateTime"),
                                "current_tasks": t.get("CurrentTasks"),
                                "current_all_process_users": t.get(
                                    "CurrentAllProcessUsers"
                                ),
                                "reason": "已到计划完成时间未完成工单",
                                "cost_time": cost_time,
                                # "jcs": jcs,
                            }
                            reminder_ticket_info_list.append(item)
        variables = {
            "project_plan_dict2": project_plan_dict2,
            "reminder_ticket_info_list": reminder_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】自动催办逻辑判断
    def reminder_ticket_analysis(self, key_node, end_time):
        """
        2.->项目报建
        2.->市电专项
        2.->项目开办
        3->安装施工
        2.x->生产 2.x.-> （设备）
        """
        if end_time:
            today = datetime.today().date()
            time = datetime.strptime(end_time, "%Y-%m-%d").date()
            key_node_today = [
                "项目报建",
                "市电专项",
                "项目开办",
                "安装施工",
                "设备",
            ]
            if today >= time and (key_node in key_node_today):
                return True
        return False

    # 【租赁】设备：获取项目关键节点最新总控计划（2.x->生产 2.x.-> （设备））
    def get_plan4(self, project_list):
        """
        【租赁】设备：获取项目关键节点最新总控计划（2.x->生产 2.x.-> （设备））
        """
        project_plan_dict4 = {}
        for p in project_list:
            project_name = p.get("project_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT t1.project_name, t1.serial_number, t1.work_content, t1.start_time, t1.completion_time, "
                "t3.campus, t3.project, t3.PM, "
                "'设备' AS key_node "
                "FROM construct_plan_data t1 "
                "JOIN ("
                "SELECT serial_number FROM construct_plan_data "
                f"WHERE project_name = '{project_name}' "
                "AND serial_number LIKE '2.%' "
                "AND work_content LIKE '%生产%' "
                "AND not_involved = 0"
                ") AS t2 "
                "ON t1.serial_number LIKE CONCAT(t2.serial_number, '.%') "
                "JOIN risk_early_warning_data t3 ON t1.project_name = t3.project_name "
                f"WHERE t1.project_name = '{project_name}' "
                "AND t1.not_involved = 0"
            )
            plan_list = db.query(sql)
            project_plan_dict4[project_name] = plan_list
        variables = {
            "project_plan_dict4": project_plan_dict4,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】计划解析：设备生产跟踪催办
    def plan_analysis4(self, project_plan_dict4):
        """
        【租赁】计划解析：设备生产跟踪催办
        """
        reminder_device_ticket_info_list = []
        # project_jcs_dict = self.ctx.variables.get("project_jcs_dict")
        for project_name, plan_info in project_plan_dict4.items():
            # jcs = project_jcs_dict.get(project_name)
            for p in plan_info:
                serial_number = p.get("serial_number")
                work_content = p.get("work_content")
                key_node = p.get("key_node")
                end_time = p.get("completion_time")
                reminder_flag = self.reminder_ticket_analysis(key_node, end_time)
                if reminder_flag:
                    exit_ticket = self.query_exit_ticket4(
                        project_name, work_content, key_node, True
                    )
                    if exit_ticket:
                        for t in exit_ticket:
                            cost_time = Tools.get_max_cost_time(
                                json.loads(t.get("CurrentTaskDetails"))
                            )
                            item = {
                                "project_name": project_name,
                                "PM": p.get("PM"),
                                "serial_number": serial_number,
                                "work_content": work_content,
                                "start_time": p.get("start_time"),
                                "end_time": p.get("completion_time"),
                                "ticket_id": t.get("TicketId"),
                                "title": t.get("Title"),
                                "create_time": t.get("CreateTime"),
                                "current_tasks": t.get("CurrentTasks"),
                                "current_all_process_users": t.get(
                                    "CurrentAllProcessUsers"
                                ),
                                "reason": "已到计划完成时间未完成工单",
                                "cost_time": cost_time,
                                # "jcs": jcs,
                            }
                            reminder_device_ticket_info_list.append(item)
        variables = {
            "project_plan_dict4": project_plan_dict4,
            "reminder_device_ticket_info_list": reminder_device_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 【租赁】查询是否有建单
    def query_exit_ticket4(self, project_name, work_content, key_node, is_open=False):
        flow_dict = {
            "设备": [
                "equipment_production_tracking_management",
            ],
        }
        flow_list = flow_dict.get(key_node)
        if flow_list:
            flow_temp = ", ".join(f"'{item}'" for item in flow_list)
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT * FROM all_construction_ticket_data "
                f"WHERE Title like '%{project_name}%' "
                f"AND Title like '%{work_content}%' "
                "AND IsForceEnd = '0' "
                f"AND ProcessDefinitionKey IN ({flow_temp})"
            )
            if is_open:
                sql += " AND TicketStatus != 'END'"
            res = db.query(sql)
            return res
        return False

    # 【租赁】项目启动催办（需求下发后12天内未完成项目启动）
    def project_start_analysis(self, project_list):
        """
        【租赁】项目启动催办（需求下发后12天内未完成项目启动）
        """
        reminder_start_ticket_info_list = []
        if project_list:
            project_temp = ", ".join(
                f"""'{item.get("project_name")}'""" for item in project_list
            )
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT t1.project_name, t1.demand_distribution, t1.dcopsTicketId, t1.PM "
                "FROM risk_early_warning_data t1 "
                f"WHERE t1.project_name in ({project_temp})"
            )
            info_list = db.query(sql)
            # project_jcs_dict = self.ctx.variables.get("project_jcs_dict", {})
            for i in info_list:
                demand_distribution = i.get("demand_distribution")
                if demand_distribution:
                    demand_distribution_temp = datetime.strptime(
                        demand_distribution, "%Y-%m-%d"
                    ).date()
                    demand_distribution_two_month_later = (
                        demand_distribution_temp + relativedelta(days=12)
                    )
                    today = datetime.today().date()
                    if today >= demand_distribution_two_month_later:
                        ticket_info = Tools.query_ticket(i.get("dcopsTicketId"), True)
                        if ticket_info:
                            # jcs = project_jcs_dict.get(i.get("project_name"))
                            for t in ticket_info:
                                cost_time = Tools.get_max_cost_time(
                                    json.loads(t.get("CurrentTaskDetails"))
                                )
                                item = {
                                    "project_name": i.get("project_name"),
                                    "PM": i.get("PM"),
                                    "serial_number": "-",
                                    "work_content": "项目启动",
                                    "start_time": "-",
                                    "end_time": "-",
                                    "ticket_id": t.get("TicketId"),
                                    "title": t.get("Title"),
                                    "create_time": t.get("CreateTime"),
                                    "current_tasks": t.get("CurrentTasks"),
                                    "current_all_process_users": t.get(
                                        "CurrentAllProcessUsers"
                                    ),
                                    "reason": "需求下发后12天内未完成项目启动",
                                    "cost_time": cost_time,
                                    # "jcs": jcs,
                                }
                                reminder_start_ticket_info_list.append(item)
        variables = {
            "reminder_start_ticket_info_list": reminder_start_ticket_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
