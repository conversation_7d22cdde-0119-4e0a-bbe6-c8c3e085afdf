#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备数据合并执行脚本
基于实际数据库表结构
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.consolidate_equipment_data import (
    create_consolidated_table,
    consolidate_data,
    check_data_quality,
    logger
)

def main():
    """主执行函数"""
    print("=" * 60)
    print("设备数据合并脚本开始执行")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 步骤1: 创建合并表
        print("\n步骤1: 创建合并表...")
        if create_consolidated_table():
            print("✓ 合并表创建成功")
        else:
            print("✗ 合并表创建失败")
            return False
        
        # 步骤2: 合并数据
        print("\n步骤2: 开始数据合并...")
        if consolidate_data():
            print("✓ 数据合并成功")
        else:
            print("✗ 数据合并失败")
            return False
        
        # 步骤3: 数据质量检查
        print("\n步骤3: 数据质量检查...")
        if check_data_quality():
            print("✓ 数据质量检查完成")
        else:
            print("✗ 数据质量检查失败")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("设备数据合并脚本执行完成")
        print(f"总执行时间: {execution_time:.2f} 秒")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"脚本执行失败: {e}")
        print(f"\n✗ 脚本执行失败: {e}")
        return False

def show_usage():
    """显示使用说明"""
    print("""
设备数据合并脚本使用说明
========================

功能说明:
- 从多个表中合并设备相关数据到统一的合并表
- 包含工单数据、设备信息、交付计划、项目状态等

涉及的表:
- all_construction_ticket_data (工单数据)
- equipment_production_racking_process (设备生产流程)
- delivery_schedule_management_table (交付计划管理)
- equipment_category_mapping_relationship (设备分类映射)
- summary_data (项目状态汇总)
- construct_plan_data (建设计划)
- payment_info (付款信息)

使用方法:
python scripts/run_consolidation.py

参数说明:
--help, -h    显示此帮助信息

注意事项:
1. 确保数据库连接正常
2. 确保有足够的磁盘空间
3. 建议在低峰期执行
4. 执行前建议备份相关表
    """)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_usage()
    else:
        success = main()
        sys.exit(0 if success else 1)
