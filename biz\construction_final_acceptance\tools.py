from iBroker.lib import mysql


class Tools(object):
    # 查询项目PM
    @staticmethod
    def get_PM(project_name):
        query_sql = f"SELECT PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        PM = ""
        if query_result and query_result[0]:
            PM = query_result[0].get("PM")
        return PM

    # 查询项目编号
    def get_project_code(project_name):
        query_sql = (
            "select item_number from risk_early_warning_data where project_name = '%s' "
            % project_name
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        project_code = ""
        if query_result and query_result[0]:
            project_code = query_result[0].get("item_number")
        return project_code

    # 多个文件处理-json
    @staticmethod
    def get_files_json(file_list):
        files_json = []
        if file_list:
            for file in file_list:
                file_info = file["response"]["FileList"][0]
                files_json.append(
                    {
                        "file_name": file_info["name"],
                        "file_url": file_info["url"],
                    }
                )
        return files_json

    # 获取需求单号
    @staticmethod
    def get_request_order_number(project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        result = db.query(sql)
        return result

    # 从数据库获取 品名-品类 映射
    @staticmethod
    def get_category_mapping():
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT * FROM equipment_category_mapping_relationship "
            "WHERE material_category_name IS NOT NULL AND material_category_name != ''"
        )
        result = db.query(sql)
        category_mapping_dict = {}
        if result:
            for item in result:
                category_mapping_dict[item.get("material_category_name")] = item.get(
                    "device_name"
                )
        return category_mapping_dict

    # 从数据库获取 品类-分类 映射
    @staticmethod
    def get_category_class_mapping():
        category_class_name_dict = {
            "集成及配套工程": "integrate_list",
            "设备": "device_list",
            "服务": "service_list",
        }
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT * FROM equipment_category_mapping_relationship "
            "WHERE material_category_name IS NOT NULL AND material_category_name != ''"
        )
        result = db.query(sql)
        category_class_mapping_dict = {}
        if result:
            for item in result:
                category_class_mapping_dict[item.get("device_name")] = category_class_name_dict.get(
                    item.get("category_class_name")
                )
        return category_class_mapping_dict

    # 根据品类分类
    @staticmethod
    def get_category_class_name(category_name):
        # 集成及配套工程
        integrate_name_list = ["集成商"]
        # 设备
        device_name_list = [
            "柴发方仓",
            "AHU方仓",
            "蓄电池",
            "变压器",
            "机柜",
            "PDU",
            "中压柜",
            "低压柜",
            "HVDC",
            "综合配电柜",
            "T-Box、交换机、传感器等",
            "弹性一体柜(HVDC)",
            "一体冷源（含风墙）",
            "CDU",
        ]
        # 服务
        service_name_list = ["项管", "监理", "第三方测试", "设计院"]
        if category_name in integrate_name_list:
            return "integrate_list"
        elif category_name in device_name_list:
            return "device_list"
        elif category_name in service_name_list:
            return "service_list"
        else:
            return "other_list"

    # 获取已验收过的品类信息（初验）
    @staticmethod
    def get_initial_acceptance_details(project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT * FROM initial_acceptance_category_details WHERE project_name = '{project_name}'"
        result = db.query(sql)
        return result

    # 获取已验收过的品类信息（终验）
    @staticmethod
    def get_final_acceptance_details(project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT * FROM final_acceptance_category_details WHERE project_name = '{project_name}'"
        result = db.query(sql)
        return result

    # 获取模组名称
    @staticmethod
    def get_module_name(project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT module_name FROM project_module_mapping_relationship WHERE project_name = '{project_name}'"
        module_name_list = db.get_all(sql)
        module_name = ""
        if module_name_list:
            module_name = module_name_list[0].get("module_name")
        return module_name

    # 获取初验邮件推送收件人(内部团队)
    @staticmethod
    def get_email_receiver_list(project_name):
        email_receiver_list = []
        PM = Tools.get_PM(project_name)
        if PM:
            email_receiver_list.append(PM)
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = (
            "SELECT architecture, beautification, tree_sutra, "
            "weak_current, business, supply_chain, inform_a_person "
            "FROM project_team_information_data "
            f"WHERE project_name = '{project_name}'"
        )
        result = db.get_all(query_sql)
        if result:
            team = result[len(result) - 1]
            for val in team.values():
                if val:
                    email_receiver_list.append(val)
        return email_receiver_list
