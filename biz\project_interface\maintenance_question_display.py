from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops, flow
from datetime import datetime

class MaintenanceQuestionDisplay(object):
    """
    接维问题跟踪
    """

    def basic_logic(self, question_result):
        # 构造工单号列表
        ticket_id_list = []
        for question in question_result:
            if question.get('fix_photo_url'):
                url_list = question.get('fix_photo_url').split(";")
                question['fix_photo_url'] = url_list
                question['state'] = '已完成'
                question["deal_users"] = ''
            else:
                ticket_id_list.append(question.get('ticket_id'))
                question['fix_photo_url'] = []
            # 构造图片
            if question.get('issue_photo'):
                problem_url_list = question.get('issue_photo').split(";")
                question['issue_photo'] = problem_url_list
            else:
                question['issue_photo'] = []
        # 通过工单号获取流程id
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "CurrentAllProcessUsers": "",
                    "TicketId": ""
                },
                "SearchCondition": {
                    "TicketId": ticket_id_list
                }
            }
        }
        query_result = gnetops.request(
            action="QueryData",
            method="Run",
            ext_data=query_data
        )
        # 节点代办人
        result_list = query_result.get("List")
        for q in question_result:
            for item in result_list:
                deal_users = item.get('CurrentAllProcessUsers')
                if item.get('TicketId') == q.get('ticket_id'):
                    if deal_users:
                        q['deal_users'] = deal_users
                        q['state'] = '进行中'
                    else:
                        q['deal_users'] = '运维平台销项中'
                        q['state'] = '已整改'

        return question_result

    def maintenance_question_display(self, project_name, major, rectification_status, page_size, page_number, ticket_id,
                                     question_ticket):
        page_number = page_number  # 页码
        offset = (page_number - 1) * page_size  # 计算偏移量

        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(ticket_id) FROM maintenance_rectification WHERE project_name='{project_name}'"

        sql1 = "SELECT ticket_id,question_ticket,major,source,device_type,device_number," \
               "issue_description, issue_photo,problem_type,responsible_manufacturer,cause_solution_process," \
               "solve_type, plan_resolution_date,fix_photo_url,remark,modify_remake,rectifier" \
               f" FROM maintenance_rectification WHERE project_name='{project_name}'"
        if major:
            sql += f" AND major = '{major}'"
            sql1 += f" AND major = '{major}'"
        if ticket_id:
            sql += f" AND ticket_id = '{ticket_id}'"
            sql1 += f" AND ticket_id = '{ticket_id}'"
        if question_ticket:
            sql += f" AND question_ticket = '{question_ticket}'"
            sql1 += f" AND question_ticket = '{question_ticket}'"
        if rectification_status:
            if rectification_status == '已完成':
                sql += " AND fix_photo_url IS NOT NULL"
                sql1 += " AND fix_photo_url IS NOT NULL"

            else:
                problem_list = []
                if rectification_status == '已整改':
                    # 问题列表
                    question_result = db.get_all(sql1)
                    question_list = self.basic_logic(question_result)
                    problem_list = []
                    for i in question_list:
                        state = i.get('state')
                        if state == '已整改':
                            problem_list.append(i)

                elif rectification_status == '进行中':
                    # 问题列表
                    question_result = db.get_all(sql1)
                    question_list = self.basic_logic(question_result)
                    problem_list = []
                    for i in question_list:
                        state = i.get('state')
                        if state == '进行中':
                            problem_list.append(i)

                # 计算总数
                total = len(problem_list)
                # 计算总页数
                total_pages = (len(problem_list) + page_size - 1) // page_size
                # 检查请求的页码是否超出范围
                if page_number < 1:
                    page_number = 1
                if page_number > total_pages:
                    page_number = total_pages
                start_index = (page_number - 1) * page_size
                end_index = start_index + page_size
                # 检查索引是否超出范围
                if start_index < 0:
                    start_index = 0
                end_index = min(end_index, total)
                page_list = problem_list[start_index:end_index]
                data = {
                    'question_list': page_list,
                    'total_results': total
                }
                return data
        else:
            sql1 += f" LIMIT {offset}, {page_size}"
            # 总数
            count_info = db.get_all(sql)
            total_results = 0
            if count_info:
                total_results = count_info[0].get('count(ticket_id)')
            # 问题列表
            question_result = db.get_all(sql1)
            question_list = self.basic_logic(question_result)

            data = {
                'question_list': question_list,
                'total_results': total_results
            }
            return data

    def problem_export_total(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT ticket_id,question_ticket,major,source,device_type,device_number," \
               "issue_description, issue_photo,problem_type,responsible_manufacturer,cause_solution_process," \
               "solve_type, plan_resolution_date,fix_photo_url,remark,modify_remake,rectifier" \
               f" FROM maintenance_rectification WHERE project_name='{project_name}'"
        # 问题列表
        question_result = db.get_all(sql1)
        question_list = self.basic_logic(question_result)
        data = {
            'question_list': question_list
        }
        return data

    def test_overview(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(ticket_id) FROM maintenance_rectification WHERE project_name='{project_name}'"
        total_results = 0
        finished = 0
        unfinished = 0
        # 总数
        count_info = db.get_all(sql)
        if count_info:
            total_results = count_info[0].get('count(ticket_id)')
            if total_results == 0:
                return {
                    "total_results": 0,
                    "completed_num": 0,
                    "undone_num": 0,
                    "completion_rate": "0.00%"
                }
        sql1 = "SELECT count(ticket_id) FROM maintenance_rectification " \
               f"WHERE fix_photo_url IS NOT NULL and project_name='{project_name}'"
        sql2 = "SELECT count(ticket_id) FROM maintenance_rectification " \
               f"WHERE fix_photo_url IS NULL and project_name='{project_name}'"
        # 完成和未完成
        finished_list = db.get_all(sql1)
        unfinished_list = db.get_all(sql2)
        if finished_list:
            finished = finished_list[0].get('count(ticket_id)')
        if unfinished_list:
            unfinished = unfinished_list[0].get('count(ticket_id)')
        completion_rate = "{:.2f}%".format(round((float(finished) / float(total_results) * 100), 2))
        data = {
            "total_results": total_results,
            "completed_num": finished,
            "undone_num": unfinished,
            "completion_rate": completion_rate
        }
        return data

    def number_of_majors(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(major) FROM maintenance_rectification WHERE project_name='{project_name}'"
        # 总数
        count_info = db.get_all(sql)
        major_num = []
        if count_info:
            total_results = count_info[0].get('count(major)')
            if total_results == 0:
                return [{
                    'name': '',
                    'value': 0,
                    'percentage': "0.00%"
                }]

            sql1 = "SELECT major, count(major) FROM maintenance_rectification " \
                   f"WHERE project_name='{project_name}' GROUP BY major"
            major_total_list = db.get_all(sql1)
            for i in major_total_list:
                if i.get("major"):
                    if total_results == 0:
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': 0,
                                'percentage': "0.00%"
                            }
                        )
                    else:
                        value = int(i.get("count(major)"))
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': value,
                                'percentage': "{:.2f}%".format(
                                    round((float(value) / float(total_results) * 100), 2))
                            }
                        )
        return major_num

    def number_undone_of_majors(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(major) FROM maintenance_rectification WHERE project_name='{project_name}'"
        # 总数
        count_info = db.get_all(sql)
        major_num = []
        if count_info:
            total_results = count_info[0].get('count(major)')
            if total_results == 0:
                return [{
                    'name': '',
                    'value': 0,
                    'percentage': "0.00%"
                }]
            sql3 = "SELECT major, count(major) FROM maintenance_rectification " \
                   f"WHERE project_name='{project_name}' and fix_photo_url IS NULL GROUP BY major"
            major_uncompleted_list = db.get_all(sql3)
            for i in major_uncompleted_list:
                if i.get("major"):
                    if total_results == 0:
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': 0,
                                'percentage': "0.00%"
                            }
                        )
                    else:
                        value = int(i.get("count(major)"))
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': value,
                                'percentage': "{:.2f}%".format(
                                    round((float(value) / float(total_results) * 100), 2))
                            }
                        )
        return major_num

    def number_of_problem_type(self, project_name):
        # 设备质量/安装工艺/设备调试/规划设计/其它
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT problem_type FROM maintenance_rectification WHERE project_name='{project_name}'"
        # 总数
        count_info = db.get_all(sql)
        total_results = len(count_info)
        if total_results == 0:
            return [{"value": 0, "name": '设备质量',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '安装工艺',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '设备调试',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '规划设计',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '其它',
                     "percentage": "0.00%"},
                    ]
        zl = 0
        az = 0
        ts = 0
        gh = 0
        qt = 0
        for i in count_info:
            if i.get("problem_type") == '设备质量':
                zl += 1
            elif i.get("problem_type") == '安装工艺':
                az += 1
            elif i.get("problem_type") == '设备调试':
                ts += 1
            elif i.get("problem_type") == '规划设计':
                gh += 1
            elif i.get("problem_type") == '其它':
                qt += 1
        problem_type_num = [{"value": zl, "name": '设备质量',
                             "percentage": "{:.2f}%".format(round(float(zl) / float(total_results) * 100))},
                            {"value": az, "name": '安装工艺',
                             "percentage": "{:.2f}%".format(round(float(az) / float(total_results) * 100))},
                            {"value": ts, "name": '设备调试',
                             "percentage": "{:.2f}%".format(round(float(ts) / float(total_results) * 100))},
                            {"value": gh, "name": '规划设计',
                             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
                            {"value": qt, "name": '其它',
                             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
                            ]
        return problem_type_num

    def number_undone_of_problem_type(self, project_name):
        # 设备质量/安装工艺/设备调试/规划设计/其它
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT problem_type FROM maintenance_rectification WHERE project_name='{project_name}'"
               f"and fix_photo_url IS NULL")
        # 总数
        count_info = db.get_all(sql)
        total_results = len(count_info)
        if total_results == 0:
            return [{"value": 0, "name": '设备质量',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '安装工艺',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '设备调试',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '规划设计',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '其它',
                     "percentage": "0.00%"},
                    ]
        zl = 0
        az = 0
        ts = 0
        gh = 0
        qt = 0
        for i in count_info:
            if i.get("problem_type") == '设备质量':
                zl += 1
            elif i.get("problem_type") == '安装工艺':
                az += 1
            elif i.get("problem_type") == '设备调试':
                ts += 1
            elif i.get("problem_type") == '规划设计':
                gh += 1
            elif i.get("problem_type") == '其它':
                qt += 1
        problem_type_num = [{"value": zl, "name": '设备质量',
                             "percentage": "{:.2f}%".format(round(float(zl) / float(total_results) * 100))},
                            {"value": az, "name": '安装工艺',
                             "percentage": "{:.2f}%".format(round(float(az) / float(total_results) * 100))},
                            {"value": ts, "name": '设备调试',
                             "percentage": "{:.2f}%".format(round(float(ts) / float(total_results) * 100))},
                            {"value": gh, "name": '规划设计',
                             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
                            {"value": qt, "name": '其它',
                             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
                            ]
        return problem_type_num

    def get_maintenance_flow_info(self, project_name):
        """
        验收转维流程进展
        """
        flow_list = [
            "construction_maintenance_platform_docking",
            "cooperative_partner_construction_maintenance_platform_docking",
            "joint_construction_maintenance_docking",
        ]
        flow_temp = ", ".join(f"'{item}'" for item in flow_list)
        sql = (
            "SELECT * FROM all_construction_ticket_data "
            f"WHERE Title like '%{project_name}%' "
            "AND IsForceEnd = '0' "
            f"AND ProcessDefinitionKey IN ({flow_temp})"
        )
        db = mysql.new_mysql_instance("tbconstruct")
        res = db.query(sql)
        result = []
        for item in res:
            ticket_id = item.get("TicketId")
            instance_id = item.get("InstanceId")
            ticket_create_time = datetime.strptime(item.get("CreateTime"), "%Y-%m-%d %H:%M:%S")
            if instance_id:
                data = {"InstanceId": instance_id}
                res = gnetops.request(
                    action="TaskCenter", method="QueryWorkflowLog", data=data
                )
                # 培训材料-
                training_materials_task = []
                # 资料-
                data_task = []
                # 物资信息-
                material_information_task = []
                for row in res:
                    temp = {
                        "id": row.get("TaskId"),
                        "process_identification_name": item.get("SchemeNameCn"),
                        "process_identification": item.get("ProcessDefinitionKey"),
                        "ticket_id": ticket_id,
                        "ticket_name": item.get("Title"),
                        "agency_name": row.get("TaskName"),
                        "responsible_person": row.get("Operator"),
                        "ticket_status": row.get("TaskStatus"),
                        "create_time": row.get("CreateTime"),
                        "end_time": row.get("EndTime"),
                    }
                    # 等待集成商资料上传
                    if row.get("TaskName") == "等待集成商资料上传":
                        child_flow_vars = flow.get_variables(
                            instance_id,
                            ["upload_ticket_id"],
                        )
                        upload_ticket_id_temp = child_flow_vars.get(
                            "upload_ticket_id", []
                        )
                        child_ticket_id = ""
                        if upload_ticket_id_temp:
                            child_ticket_id = upload_ticket_id_temp[0]
                        if child_ticket_id:
                            child_ticket_sql = (
                                "SELECT * FROM all_construction_ticket_data "
                                f"WHERE TicketId = '{child_ticket_id}' "
                            )
                            child_ticket_res = db.query(child_ticket_sql)
                            child_task_res = []
                            child_ticket_info = []
                            if child_ticket_res:
                                child_instance_id = child_ticket_res[0].get(
                                    "InstanceId"
                                )
                                child_process_identification_name = child_ticket_res[
                                    0
                                ].get("SchemeNameCn")
                                child_process_identification = child_ticket_res[0].get(
                                    "ProcessDefinitionKey"
                                )
                                child_ticket_name = child_ticket_res[0].get("Title")
                                child_data = {"InstanceId": child_instance_id}
                                child_task_res = gnetops.request(
                                    action="TaskCenter",
                                    method="QueryWorkflowLog",
                                    data=child_data,
                                )
                                for child_row in child_task_res:
                                    child_temp = {
                                        "id": child_row.get("TaskId"),
                                        "process_identification_name": child_process_identification_name,
                                        "process_identification": child_process_identification,
                                        "ticket_id": child_ticket_id,
                                        "ticket_name": child_ticket_name,
                                        "agency_name": child_row.get("TaskName"),
                                        "responsible_person": child_row.get("Operator"),
                                        "ticket_status": child_row.get("TaskStatus"),
                                        "create_time": child_row.get("CreateTime"),
                                        "end_time": child_row.get("EndTime"),
                                    }
                                    child_ticket_info.append(child_temp)
                                temp = {**temp, "child_ticket_info": child_ticket_info}
                    # 培训材料-
                    if row.get("TaskName").startswith("培训材料-"):
                        training_materials_task.append(temp)
                    # 资料-
                    elif row.get("TaskName").startswith("资料-"):
                        data_task.append(temp)
                    # 物资信息-
                    elif row.get("TaskName").startswith("物资信息-"):
                        material_information_task.append(temp)
                    else:
                        result.append(temp)
                        # 获取资料
                        if row.get("TaskName").startswith("获取资料"):
                            # 晚于2025-06-17 17:00:00，则为新流程，能区分3个分支
                            default_time = datetime.strptime(
                                "2025-06-17 17:00:00", "%Y-%m-%d %H:%M:%S"
                            )
                            if ticket_create_time >= default_time:
                                # 固定3个分支
                                branch_list = [
                                    {
                                        "id": f'{row.get("TaskId")}pxcl',
                                        "process_identification_name": item.get(
                                            "SchemeNameCn"
                                        ),
                                        "process_identification": item.get(
                                            "ProcessDefinitionKey"
                                        ),
                                        "ticket_id": ticket_id,
                                        "ticket_name": item.get("Title"),
                                        "agency_name": "培训材料分支",
                                        "child_ticket_info": training_materials_task,
                                    },
                                    {
                                        "id": f'{row.get("TaskId")}zl',
                                        "process_identification_name": item.get(
                                            "SchemeNameCn"
                                        ),
                                        "process_identification": item.get(
                                            "ProcessDefinitionKey"
                                        ),
                                        "ticket_id": ticket_id,
                                        "ticket_name": item.get("Title"),
                                        "agency_name": "资料分支",
                                        "child_ticket_info": data_task,
                                    },
                                    {
                                        "id": f'{row.get("TaskId")}wzxx',
                                        "process_identification_name": item.get(
                                            "SchemeNameCn"
                                        ),
                                        "process_identification": item.get(
                                            "ProcessDefinitionKey"
                                        ),
                                        "ticket_id": ticket_id,
                                        "ticket_name": item.get("Title"),
                                        "agency_name": "物资信息分支",
                                        "child_ticket_info": material_information_task,
                                    },
                                ]
                                result = [*result, *branch_list]
        return result

    def get_instance_id(self, ticket_id):
        query_data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {"InstanceId": "", "TicketId": ""},
                "SearchCondition": {"TicketId": [str(ticket_id)]},
            },
        }
        query_result = gnetops.request(
            action="QueryData", method="Run", ext_data=query_data
        )
        result_list = query_result.get("List")
        instance_id = ""
        if result_list:
            instance_id = result_list[0].get("InstanceId")
        return instance_id
