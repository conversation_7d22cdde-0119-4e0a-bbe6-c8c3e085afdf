# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: requirement.py.py
# @time: 2023/3/6 14:16
# @描述: 需求相关内容
import time
import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.logone import logger
from iBroker.lib import mysql, config
from iBroker.lib.sdk import tof
from iBroker.sdk.workflow.workflow import (WorkflowDetailService, WorkflowVarUpdate)


# 提交变更需求
class RequirementPush(AjaxTodoBase):
    def __init__(self):
        super(RequirementPush, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 获取dcops工单id
        ticket_id = self.ctx.variables.get("TicketId")
        # 获取表单提交的类型数据
        type_list = process_data["change_type"]
        process_data["change_type"] = process_data["change_type"][-1]

        requirement_insert_data = {
            "ProjectId": process_data["project_id"],
            "ChangeItem": process_data["change_item"],
            "ChangeTitle": process_data["change_title"],
            "SubmitPeople": process_data["submit_people"],
            "RequirGroup": process_data["requir_group"],
            "ChangeType": type_list[1],
            "ParentType": type_list[0],
            "ChangeReason": process_data["change_reason"],
            "ChangeArea": process_data["change_area"],
            "ChangeContent": process_data["change_content"] if "change_content" in process_data else "",
            "ChangeComment": process_data["change_comment"] if "change_comment" in process_data else "",
            "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "WishTime": process_data["wish_time"] if "wish_time" in process_data else "",
            "DcopsTicketId": ticket_id,
        }

        # 建立数据库链接
        tb_db = mysql.new_mysql_instance("tb_change")

        tb_db.begin()
        requirement_id = tb_db.insert("requirement", requirement_insert_data)

        # 添加日志
        WorkflowVarUpdate(self.ctx.instance_id, {
            "requirement_id": requirement_id
        }).call()
        logger.info("requirement_id:{}".format(requirement_id))

        requirement_file_insert_data = []
        cos_url = config.get_config_string("cos.url")

        # 内部文件入库
        if requirement_id > 0 and "inside_file_list" in process_data and len(process_data["inside_file_list"]) > 0:
            for item in process_data["inside_file_list"]:
                append_obj = {
                    "RequirementId": requirement_id,
                    "CosPath": item["response"]["FileList"][0]["url"].split(cos_url)[1],
                    "FileName": item["name"],
                    "Creator": process_user,
                    "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "Type": "inside"
                }
                requirement_file_insert_data.append(append_obj)

        # 外部文件入库
        if requirement_id > 0 and "outside_file_list" in process_data and len(
                process_data["outside_file_list"]) > 0:
            for item in process_data["outside_file_list"]:
                append_obj = {
                    "RequirementId": requirement_id,
                    "CosPath": item["response"]["FileList"][0]["url"].split(cos_url)[1],
                    "FileName": item["name"],
                    "Creator": process_user,
                    "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "Type": "outside"
                }
                requirement_file_insert_data.append(append_obj)
        if len(requirement_file_insert_data) > 0:
            logger.info("requirement_file_insert_data:{}".format(requirement_file_insert_data))
            tb_db.insert_batch("requirement_file", requirement_file_insert_data)
        tb_db.commit()

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "direct_leader": process_data["direct_leader"],  # 直属leader
            "change_type": process_data["change_type"],  # 变更类型
            "requirement_id": requirement_id,  # 需求id
            "requir_group": process_data["requir_group"],  # 需求组
        }
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables)


# 审核下发
class AuditCreate:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def OrderDeal(self, change_type: str, direct_leader: str):
        # 读取七彩石审核配置
        audit_config = config.get_config_map("tb_audit_config")
        audit_list = [{
            "audit_people": direct_leader,
            "audit_status": "待审核",
            "audit_reason": "",
            "audit_time": "",
        }]

        # 循环遍历audit_config[change_type]，每遍历一次往audit_list中添加一条数据，且audit_seq+1
        for item in audit_config[change_type]:
            audit_list.append({
                "audit_people": item,
                "audit_status": "待审核",
                "audit_reason": "",
                "audit_time": "",
            })
        # 遍历audit_list排除audit_people重复的数据，并且为每一个audit_people添加audit_seq，且audit_seq从1开始
        audit_list = list({v['audit_people']: v for v in audit_list}.values())
        for i in range(len(audit_list)):
            audit_list[i]["audit_seq"] = i + 1

        # 将audit_list记入日志
        self.workflow_detail.add_kv_table("审核下发", {
            "audit_list": audit_list
        })
        variables = {
            "audit_list": audit_list,
            "audit_user": audit_list[0]["audit_people"]
        }

        # 结束任务，写入流程变量
        flow.complete_task(self.ctx.task_id, variables)


# 变更需求审核
class RequirementAudit(AjaxTodoBase):
    def __init__(self):
        super(RequirementAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def transfer_logic(self, process_data, now_person, target_user):

        # 将target_user用;分割，取第一个
        target_user = target_user.split(";")[0]

        # 查询当前处理人
        gnetops_db = mysql.new_mysql_instance("gnetops")
        query_sql = "select * from task_center_users where TaskId = '%s' limit 1" % self.ctx.task_id
        runtime_data = gnetops_db.query(query_sql)
        principal = runtime_data[0]["Principal"]

        # 获取流程变量audit_list
        audit_list = self.ctx.variables.get("audit_list")
        # 根据now_person获取audit_list中还未审核的数据，并且将其的audit_people改为target_user
        for item in audit_list:
            if item["audit_status"] == "待审核":
                item["audit_people"] = target_user
                break

        # 更新流程变量
        WorkflowVarUpdate(self.ctx.instance_id, {
            "audit_list": audit_list
        }).call()

        # 修改cron的催办数据
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "NotifyUser": target_user,
        }, {
                         "RequirementId": self.ctx.variables.get("requirement_id"),
                         "NotifyUser": principal,
                         "TodoKey": "RequirementAudit"
                     })

        # 调用dcops的转单接口即可
        gnetops.request(action="TaskCenter", method="ReassignBack",
                        data={"NewPrincipals": target_user, "OperateUser": now_person, "TaskId": self.ctx.task_id})
        pass

    def start(self):

        audit_list = self.ctx.variables.get("audit_list")
        requirement_id = self.ctx.variables.get("requirement_id")
        # 获取第二天的时间，格式为2020-01-01
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")

        # 给第一个人写入定时任务数据
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert("requirement_cron", {
            "RequirementId": requirement_id,
            "TicketId": self.ctx.variables.get("TicketId"),
            "TaskId": self.ctx.task_id,
            "NotifyUser": audit_list[0]["audit_people"],
            "NotifyType": "mail",
            "Content": "您有一个需求审核任务需要处理，请及时处理！",
            "Status": "wait",
            "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "NotifyDate": tomorrow,
            "TodoKey": "RequirementAudit"
        })
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 获取流程变量中的审核数据
        audit_list = self.ctx.variables.get("audit_list")
        audit_reason = process_data["audit_reason"] if "audit_reason" in process_data else ""
        audit_result = process_data["audit_result"]

        pass_able = False
        next_user = ""
        now_audit_index = -1
        # 循环遍历audit_list,找到audit_status为待审核的index
        for i in range(len(audit_list)):
            if audit_list[i]["audit_status"] == "待审核":
                now_audit_index = i
                break

        # 如果now_audit_index为-1，说明没有待审核的数据，直接结束流程
        if now_audit_index == -1:
            flow.complete_task(self.ctx.task_id)
            return

        # 根据audit_list[now_audit_index]["audit_people"]和process_user判断是否有权限审核，没有权限直接return
        if audit_list[now_audit_index]["audit_people"] != process_user:
            return

        if audit_result:
            audit_list[now_audit_index]["audit_status"] = "同意"
            audit_list[now_audit_index]["audit_reason"] = audit_reason
            audit_list[now_audit_index]["audit_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            if now_audit_index + 1 < len(audit_list):
                next_user = audit_list[now_audit_index + 1]["audit_people"]
            else:
                pass_able = True
        else:
            audit_list[now_audit_index]["audit_status"] = "驳回"
            audit_list[now_audit_index]["audit_reason"] = audit_reason
            audit_list[now_audit_index]["audit_time"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
            pass_able = True

        # 将audit_list记入日志
        self.workflow_detail.add_kv_table("变更需求审核", {
            "audit_list": audit_list
        })

        # 更新流程变量
        WorkflowVarUpdate(self.ctx.instance_id, {
            "audit_list": audit_list
        }).call()
        tb_db = mysql.new_mysql_instance("tb_change")
        requirement_id = self.ctx.variables.get("requirement_id")

        # 结束掉定时任务
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "RequirementAudit",
                     })

        # 没有需要审核的任务了，结束此节点，开始流转
        if pass_able:
            # 查询 project_source 表

            requirement_id = self.ctx.variables.get("requirement_id")
            sql = "select * from tb_erp_project left join requirement" \
                  + " on requirement.ProjectId = tb_erp_project.Id where requirement.Id = %s" % requirement_id
            # 获取项目信息
            project_infos = tb_db.query(sql)

            project_info = project_infos[0]

            # 审批信息入库
            batch_insert_list = []
            for audit in audit_list:
                batch_insert_list.append({
                    "RequirementId": requirement_id,
                    "TodoKey": "RequirementAudit",
                    "AuditUser": audit.get("audit_people"),
                    "AuditTime": audit.get("audit_time"),
                    "AuditResult": audit.get("audit_status"),
                    "AuditOpinion": audit.get("audit_reason")
                })
            tb_db.insert_batch("tb_change_audit_history", batch_insert_list)
            # 结束dcops待办
            GnetopsTodoEnd(self.ctx.task_id, process_user)

            variables = {
                "project_manager": project_info["Pm"],
                "requirement_gateway": 1 if audit_result else 0,
            }
            self.workflow_detail.add_kv_table("变更需求审核222222222222", {
                "variables": variables
            })
            # 流程流转
            flow.complete_task(self.ctx.task_id, variables)
        else:
            tb_db.insert("requirement_cron", {
                "RequirementId": requirement_id,
                "TicketId": self.ctx.variables.get("TicketId"),
                "TaskId": self.ctx.task_id,
                "NotifyUser": next_user,
                "NotifyType": "mail",
                "Content": "您有一个需求审核任务需要处理，请及时处理！",
                "Status": "wait",
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "NotifyDate": (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d"),
                "TodoKey": "RequirementAudit"
            })

            # 单据移交给下一个审核
            gnetops.request(action="TaskCenter", method="ReassignBack",
                            data={"NewPrincipals": next_user, "OperateUser": process_user, "TaskId": self.ctx.task_id})


# 接收变更需求
class RequirementReceive(AjaxTodoBase):
    def __init__(self):
        super(RequirementReceive, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):

        accept_able = process_data["accept_able"]
        # unit_types = process_data["unit_types"]

        # 建立数据库链接
        tb_db = mysql.new_mysql_instance("tb_change")
        if accept_able:
            requirement_id = self.ctx.variables.get("requirement_id")
            update_data = {
                "AcceptTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "AfterAudit": process_data["after_audit"],
            }

            # 判断process_data["WishTime"]是否为空，如果不为空，将process_data["WishTime"]写入update_data
            if "wish_time" in process_data and process_data["wish_time"] != "":
                update_data["WishTime"] = process_data["wish_time"]
            # 修改需求工单数据
            tb_db.update("requirement", update_data, {"Id": requirement_id})

            # 生成变更单号，相关数据入库
            if "requir_instruct_list" in process_data and len(process_data["requir_instruct_list"]) > 0:
                # 遍历requir_instruct_list，生成变更单号，相关数据入库
                insert_list = []
                for requir_instruct in process_data["requir_instruct_list"]:
                    insert_data = {
                        "RequirementId": requirement_id,  # 需求工单id
                        "ProjectId": requir_instruct["ProjectId"],  # 项目id
                        "TimeStr": requir_instruct["TimeStr"],  # 时间戳
                        "Type": requir_instruct["Type"],  # 类型
                        "Seq": requir_instruct["Seq"],  # 序号
                        "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),  # 创建时间
                        "Status": 1,  # 状态
                    }
                    insert_list.append(insert_data)
                if len(insert_list) > 0:
                    tb_db.insert_batch("requir_instruct", insert_list)
            # 结束dcops待办
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            # 流程流转
            flow.complete_task(self.ctx.task_id)
        else:
            # 无效触发，不进行流转
            print()


# 变更方案调整，pm上传变更方案
class PlanAdjustment(AjaxTodoBase):

    def __init__(self):
        super(PlanAdjustment, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # get requirement_id from ctx.variables
        requirement_id = self.ctx.variables.get("requirement_id")
        # get cos_url from rainbow_config
        cos_url = config.get_config_string("cos.url")

        # get data named pm_file_list from process_data
        pm_file_list = process_data["pm_file_list"]
        # connect to mysql
        tb_db = mysql.new_mysql_instance("tb_change")
        # create a array named insert_data_list
        insert_data_list = []
        # loop pm_file_list
        for pm_file in pm_file_list:
            insert_obj = {
                "RequirementId": requirement_id,
                "CosPath": pm_file["response"]["FileList"][0]["url"].split(cos_url)[1],
                "FileName": pm_file["name"],
                "Creator": process_user,
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "Type": "pm_upload"
            }
            insert_data_list.append(insert_obj)

        # if insert_data_list is not empty, batch insert into requirement_file table
        if len(insert_data_list) > 0:
            tb_db.insert_batch("requirement_file", insert_data_list)

        # 给固定人员发送邮件，提示变更方案已经确定
        dcops_domain = config.get_config_string("dcopsDomain")
        ticket_id = self.ctx.variables.get("ticket_id")
        msglines = []
        msglines.append("【TB变更】来自" + process_user + "的变更方案已经确定，点击链接即可查看详情")
        linkhref = f"http://{dcops_domain}/appManage/tickets/details?params={ticket_id}"
        # 将linkhref转换为超链接，拼接到msglines中
        msglines.append(f"工单链接：<a href='{linkhref}'>工单链接</a>")
        content = str.join("<br />", msglines)
        # 获取七彩石配置
        notify_user = config.get_config_string("PlanAdjustmentMailUser")
        tof.send_email(sendTitle="TB变更审核提醒", msgContent=content, sendTo=[notify_user + "@tencent.com"])

        # end DcopsTodo
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # complete task
        flow.complete_task(self.ctx.task_id)


# 变更方案确认
class PlanConfirm(AjaxTodoBase):

    def __init__(self):
        super(PlanConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):

        requirement_id = self.ctx.variables.get("requirement_id")
        cos_url = config.get_config_string("cos.url")
        pm_file_list = process_data["pm_file_list"]

        if len(pm_file_list) != 0:
            # connect to mysql
            tb_db = mysql.new_mysql_instance("tb_change")
            # 修改pm之前上传的文件，先删除再添加，以前端list为准

            # 查询处需要删除的文件的id
            db_file_list = tb_db.get_list("requirement_file", {"Id"},
                                          {"RequirementId": requirement_id, "Type": "pm_upload"})
            if len(db_file_list) > 0:
                # 将db_file_list的每个元素的Id取出，组成一个list
                db_file_id_list = [db_file["Id"] for db_file in db_file_list]
                # 循环遍历db_file_id_list，删除每个文件
                for db_file_id in db_file_id_list:
                    tb_db.delete_by_id("requirement_file", db_file_id)

            # 将pm_file_list中的文件添加到数据库
            insert_data_list = []
            for pm_file in pm_file_list:
                insert_obj = {
                    "RequirementId": requirement_id,
                    "CosPath": pm_file["response"]["FileList"][0]["url"].split(cos_url)[1],
                    "FileName": pm_file["name"],
                    "Creator": process_user,
                    "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                    "Type": "pm_upload"
                }
                insert_data_list.append(insert_obj)
            if len(insert_data_list) > 0:
                tb_db.insert_batch("requirement_file", insert_data_list)

            # end DcopsTodo
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            # complete task
            flow.complete_task(self.ctx.task_id)
        else:
            # 无效触发
            print()


if __name__ == '__main__':
    tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    print(tomorrow)
    pass
