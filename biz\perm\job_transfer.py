"""
权限系统转岗处理流程
<AUTHOR>
@created 2021/4/19
"""
from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context


class JobTransfer(object):
    def __init__(self):
        self.ctx = get_context()

    def initialize(self):
        """
        流程初始化
        :return:
        """
        # TODO
        flow.complete_task(self.ctx.task_id)

    def data_update(self):
        """
        数据更新
        :return:
        """
        # TODO
        flow.complete_task(self.ctx.task_id)
