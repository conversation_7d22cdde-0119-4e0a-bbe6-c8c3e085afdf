# 模板操作
class ConstructionApplicationFieldHandle(object):

    # 各当前子任务节点页面（三级标题）文件上传字段
    @staticmethod
    def get_upload_file_field_map(type):
        upload_file_field_map = {}
        if type == "市电专项":
            upload_file_field_map = {
                "power_supply_installation_application_file": "供电报装申请表",
                "power_supply_bureau_approval_plan_file": "供电局批复方案",
                "highly_reliable_fee_payment_voucher_file": "高可靠费缴费凭证",
                "power_supply_contract_file": "供电合同",
                "dispatch_protocol_file": "调度协议",
                "equipment_test_report": "设备试验报告",
                "sd_other_file": "送电-上传文件(如有)",
            }
        elif type == "项目报建":
            upload_file_field_map = {
                "project_establishment_report_file": "项目立项报告",
                "design_contract_file": "设计合同",
                "approval_drawings_file": "送审图纸",
                # 第一版 "审图报告"
                "drawing_review_report_file": "审图报告",
                # 第二版 "审图合格证"、"结构力学书"
                "drawing_review_certificate_file": "审图合格证",
                "structural_mechanics_book_file": "结构力学书",
                "jlfb_other_file": "监理直接发包、上传-上传文件(如有)",
                "gcfb_other_file": "工程直接发包、上传-上传文件(如有)",
                "supervision_contract_file": "监理合同",
                "engineering_contract_file": "工程合同",
                "nmgbzjjght_file": "农民工保证金监管合同",
                "insurance_contract_file": "保险合同",
                "construction_certificate_file": "施工证",
                "completion_filing_report_file": "竣工备案报告",
                "fire_protection_filing_report_file": "消防备案报告",
                "dagzlgd_other_file": "档案馆资料归档-上传文件(如有)",
            }
        return upload_file_field_map
