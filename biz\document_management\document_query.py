import copy
import datetime
import json
import re
from collections import defaultdict

from iBroker.lib import mysql, config
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class ProjectDocumentation(object):
    """
        项目文档接口
    """

    def document_query(self, page_number, page_size, project_name, first_level_directory, second_level_directory,
                       file_name, file_url, ticket_id, file_upload_person):
        """
            文档查询
        """
        # 所有文件
        data = []
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT id, ticket_id, file_name, file_url, file_remarks, file_upload_person, file_upload_time " \
                    "FROM document_management_library " \
                    f"WHERE project_name = '{project_name}' " \
                    f"AND first_level_directory = '{first_level_directory}' " \
                    f"AND second_level_directory = '{second_level_directory}'"
        if file_name:
            query_sql += f" AND file_name LIKE '%{file_name}%'"
        if file_url:
            query_sql += f" AND file_url LIKE '%{file_url}%'"
        if ticket_id:
            query_sql += f" AND ticket_id = '{ticket_id}'"
        if file_upload_person:
            query_sql += f" AND file_upload_person = '{file_upload_person}'"

        result = db.get_all(query_sql)
        for row in result:
            id = row.get('id')
            ticket_id = row.get('ticket_id')
            file_name = row.get('file_name')
            file_url = row.get('file_url')
            file_remarks = row.get('file_remarks')
            file_upload_person = row.get('file_upload_person')
            file_upload_time = row.get('file_upload_time')
            file_upload_time = datetime.datetime.strptime(file_upload_time, '%Y-%m-%d')
            file_upload_time = file_upload_time.strftime('%Y-%m-%d')
            data.append({
                'id': id,
                'ticket_id': ticket_id,
                'file_name': file_name,
                'file_url': file_url,
                'file_remarks': file_remarks,
                'file_upload_person': file_upload_person,
                'file_upload_time': file_upload_time,
            })
        # 计算总数
        total = len(data)
        # 计算总页数
        page_size = int(page_size)
        page_number = int(page_number)  # 将字符串转换为整数
        total_pages = (len(data) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = data[start_index:end_index]

        return {
            'listed_files': page_list,
            'total': total
        }

    def project_classification(self, state, role):
        """
            项目状态分类
        """
        # 获取七彩石配置
        project = config.get_config_map("project_documentation_staff_limitations")
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, project_name " \
                    "FROM summary_data "
        if state:
            query_sql += f"WHERE state = '{state}'"
        else:
            query_sql += f"WHERE state <> '未建'"
        result = db.get_all(query_sql)

        # 提取与 role 相关的项目名称
        role_projects = project.get(role, [])

        # 过滤 result 中的项目，并合并 campus 和 project_name
        if role and role_projects:  # 如果 role 不为空且在 project 中找到了相关项目
            filtered_result = [
                f"{item['campus']}{item['project_name']}" for item in result
                if f"{item['campus']}{item['project_name']}" in role_projects
            ]
        else:  # 如果 role 为空或在 project 中找不到相关项目，返回所有项目
            filtered_result = [
                f"{item['campus']}{item['project_name']}" for item in result
            ]
        return filtered_result

    def file_delete(self, ids):
        """
            文件删除
        """
        db = mysql.new_mysql_instance("tbconstruct")
        try:
            id_s = [str(id) for id in ids]
            query_sql = f"DELETE FROM document_management_library WHERE id IN ({','.join(id_s)})"
            db.execute(query_sql)
            response = {
                'code': 200,
                'msg': '文件删除成功'
            }
        except ValueError as ve:
            response = {
                'code': 500,
                'msg': f'文件删除失败：{ve}'
            }

        except TypeError as te:
            response = {
                'code': 500,
                'msg': f'文件删除失败：{te}'
            }

        return response


class GetSevenColoredStones(object):
    """
        获取七彩石配置 & 获取项目PM
    """

    def __init__(self):
        super(GetSevenColoredStones, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def getconf(self):
        project_name = self.ctx.variables.get("project_name")
        # 获取七彩石配置
        table = config.get_config_map(
            "project_document_directory"
        )
        # 获取项目PM
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, PM " \
                    "FROM risk_early_warning_data " \
                    f"WHERE project_name = '{project_name}'"
        PM_result = db.get_all(query_sql)
        if not PM_result:
            # 如果在risk_early_warning_data表中找不到PM，则从summary_data表中查找
            query_sql = "SELECT project_name, PM " \
                        "FROM summary_data " \
                        f"WHERE CONCAT(campus, project_name) = '{project_name}'"
            PM_result = db.get_all(query_sql)
        for row in PM_result:
            PM = row.get('PM')
        variables = {
            "table": table,
            'PM': PM
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectDocumentUpload(AjaxTodoBase):
    """
        项目文档上传
    """

    def __int__(self):
        super(ProjectDocumentUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        Creator = self.ctx.variables.get("Creator")
        project_name = self.ctx.variables.get("project_name")

        table = process_data.get('table')
        insert_data = []
        if table:
            for row in table:
                first_level_directory = row.get('first_level_directory')
                second_level_directory = row.get('second_level_directory')
                file_url = row.get('file_url')
                file_remarks = row.get('file_remarks')
                if file_url is not None:
                    for doc in file_url:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                file_upload_time = datetime.datetime.now()
                                file_upload_time = file_upload_time.strftime('%Y-%m-%d')
                                insert_data.append({
                                    'ticket_id': ticket_id,
                                    'project_name': project_name,
                                    'first_level_directory': first_level_directory,
                                    'second_level_directory': second_level_directory,
                                    'file_name': name,
                                    'file_url': url,
                                    'file_remarks': file_remarks if file_remarks else '',  # 处理file_remarks未定义的情况
                                    'file_upload_person': Creator,
                                    'file_upload_time': file_upload_time,
                                })

        variables = {
            'insert_data': insert_data,
            'project_name': project_name,
            'table': table
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMExamineAndApprove(AjaxTodoBase):
    """
        PM审批
    """

    def __init__(self):
        super(PMExamineAndApprove, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        insert_data = self.ctx.variables.get("insert_data")
        PM_approval = process_data.get("PM_approval", '')
        PM_remarks = process_data.get("PM_remarks", '')

        if PM_approval == '通过' and insert_data:
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("document_management_library", insert_data)
            tb_db.commit()
        variables = {
            'PM_approval': PM_approval,
            'PM_remarks': PM_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectContract(object):
    """
        项目合同接口
    """

    def check_permission(self, account=None, project_name=None, db=None):
        # 默认权限为 False
        have_permission = False

        if not project_name:
            return True

        # 检查 account 是否以 "C_Js" 开头
        if not account.startswith("C_Js") or not account.startswith("Fo"):
            return True

        # 查询外网人员信息
        sql = f"SELECT * FROM project_role_account WHERE project_name = '{project_name}'"
        project_role_account = db.get_all(sql)

        # 如果查询结果为空，则直接返回
        if not project_role_account:
            return False

        # 遍历查询结果，检查角色权限
        for row in project_role_account:
            if row.get("account") == account and row.get("role") in ["监理-总监理工程师", "合建方-项目经理",
                                                                     "项管-项目经理"]:
                return True  # 提前返回，减少嵌套

        # 默认返回 False
        return False

    def contract_inquiry(self, page_number=None, page_size=None, project_name=None,
                         category=None, acquisition_lot=None, contract_name=None, account=None):
        """
            合同查询
        """
        db = mysql.new_mysql_instance("tbconstruct")
        have_permission = self.check_permission(account, project_name, db)
        if not have_permission:
            return {
                'contract_file': [],
                'total': 0
            }

        # 设备名称对应关系
        equipment_name_map = {
            "AHU": "AHU",
            "low_voltage_cabinet": "低压柜",
            "medium_voltage_cabinet": "中压柜",
            "battery": "电池",
            "chaifa": "柴发",
            "cabinet": "机柜",
            "transformer": "变压器",
            "headboard": "高压直流 &列头柜",
            "PDU": "PDU",
            "tbox": "tbox",
            "integrators": "集成商"
        }

        # 正则表达式匹配时间之后到 "_" 之间的部分,用于根据dcops上传的文件链接反推文件名
        def extract_filename_from_url(url):
            match = re.search(r'/(\d{8})/(.*?)(?=_)', url)
            if match:
                return match.group(2)  # 返回时间之后到 "_" 之间的部分
            return "未知文件名"

        # 统一查询条件检查
        def check_conditions(record, category, acquisition_lot, contract_name):
            # 检查 category
            if category and record.get('category') != category:
                return False
            # 检查 acquisition_lot
            if acquisition_lot and acquisition_lot not in record.get('acquisition_lot', ""):
                return False
            # 检查 contract_name
            if contract_name and not any(contract_name in item['name'] for item in record.get('contract_file', [])):
                return False
            return True

        data = []

        # 合同文件
        sql_1 = "SELECT service_provider, contract_url, equipment_name FROM service_provider_contract_documents"
        result_1 = db.get_all(sql_1)

        # 按 service_provider 和 equipment_name 分组
        grouped_data = []
        for row in result_1:
            service_provider = row.get('service_provider')
            equipment_name = row.get('equipment_name')

            # 提取 URL
            try:
                url = json.loads(row.get('contract_url'))[0].get("response").get("FileList")[0].get("url")
            except (TypeError, AttributeError, IndexError):
                url = None
            # 添加设备类别
            category_name = equipment_name_map.get(equipment_name, equipment_name)
            if url:
                grouped_data.append({
                    "category": category_name,
                    "service_provider": service_provider,
                    "urls": [{
                        'url': url,
                        'name': f"{service_provider} {category_name} 合同文件"}]
                })

        # 处理项目相关逻辑
        service_provider_dict = {}
        if project_name:
            sql_3 = f"SELECT project,campus FROM risk_early_warning_data WHERE project_name='{project_name}'"
            project_query = db.get_row(sql_3)
            if project_query:
                project = project_query.get('project', '')
                campus = project_query.get('campus', '')
                sql_4 = (
                            "SELECT integrators, AHU, low_voltage_cabinet, medium_voltage_cabinet, battery, chaifa, cabinet, "
                            "transformer, headboard, PDU, tbox FROM supplier_resources WHERE project='%s' AND campus='%s'") % (
                            project, campus)
                service_provider_list_query = db.get_row(sql_4)
                if service_provider_list_query:
                    for key, values in service_provider_list_query.items():
                        service_provider_dict.update({
                            values: equipment_name_map.get(key, '')
                        })
                else:
                    return {
                        'contract_file': [],
                        'total': 0
                    }
            else:
                return {
                    'contract_file': [],
                    'total': 0
                }
        else:
            service_provider_dict = {}
        # 构建最终记录
        for row in grouped_data:
            service_provider = row.get('service_provider')
            if service_provider_dict and service_provider not in service_provider_dict.keys():
                continue  # 如果 service_provider 不在指定列表中，则跳过

            # 合并设备类别
            categories = row.get('category')
            if service_provider_dict and service_provider_dict.get(service_provider) != categories:
                continue

            # 构建记录
            record = {
                'project_name': project_name,
                'category': categories,
                'acquisition_lot': "",  # 合同文件没有 acquisition_lot
                'contract_file': row['urls']
            }

            # 检查查询条件
            if check_conditions(record, category, acquisition_lot, contract_name):
                data.append(record)

        # 增加检查文件名中是否包含供应商名称的逻辑
        def check_supplier_in_filename(file_name, service_provider_list):
            """
            检查文件名中是否包含供应商名称
            """
            if not service_provider_list:
                return True  # 如果没有供应商列表（即没有项目名称），全量返回
            for supplier in service_provider_list:
                if supplier in file_name:
                    return True
            return False

        # 处理 clarification_document 和 back_label_file 的逻辑
        sql_2 = "SELECT clarification_document,back_label_file,project_name FROM information_joint_builder_faq"
        result_2 = db.get_all(sql_2)

        for row in result_2:
            project_name_from_row = row.get('project_name')  # 获取当前记录的项目名称
            if project_name_from_row == "南京江宁8号楼液冷项目T-block集成商招采":
                continue

            # 如果没有传入 project_name，则不需要限制供应商范围
            if not project_name:
                current_service_provider_list = None  # 没有项目名称时，默认不限制供应商
            else:
                current_service_provider_list = [key for key, values in service_provider_dict.items()]  # 使用传入的供应商列表

            # 处理 clarification_document
            clarification_document = row.get('clarification_document', "")
            if clarification_document:  # 检查是否为空字符串或 None
                clarification_document_list = clarification_document.split(',')
                for i in clarification_document_list:
                    url = i.strip()  # 去除可能的多余空格
                    if url.endswith('.eml'):
                        continue
                    file_name = extract_filename_from_url(url)

                    if file_name:

                        # 检查文件名中是否包含供应商名称
                        if not check_supplier_in_filename(file_name, current_service_provider_list):
                            continue

                        record = {
                            'project_name': project_name or project_name_from_row,
                            'category': "",
                            'acquisition_lot': row.get('project_name'),
                            'contract_file': [{
                                'url': url,
                                'name': file_name
                            }]
                        }
                        # 检查查询条件
                        if check_conditions(record, category, acquisition_lot, contract_name):
                            data.append(record)

            # 处理 back_label_file
            back_label_file = row.get('back_label_file')
            if back_label_file:
                try:
                    back_label_file_bak = json.loads(back_label_file)
                    for i in back_label_file_bak:
                        for key, value in i.get("back_label_file", {}).items():
                            for item in value:
                                if item.endswith('.eml'):
                                    continue
                                file_name = f"{i.get('section_name')}-{key}"

                                if file_name:

                                    # 检查文件名中是否包含供应商名称
                                    if not check_supplier_in_filename(file_name, current_service_provider_list):
                                        continue

                                    record = {
                                        'project_name': project_name or project_name_from_row,
                                        'category': "",
                                        'acquisition_lot': row.get('project_name'),
                                        'contract_file': [{
                                            'url': item,
                                            'name': file_name
                                        }]
                                    }
                                    # 检查查询条件
                                    if check_conditions(record, category, acquisition_lot, contract_name):
                                        data.append(record)
                except json.JSONDecodeError as e:
                    pass
        # 计算总数
        total = len(data)
        # 计算总页数
        page_size = int(page_size)
        page_number = int(page_number)  # 将字符串转换为整数
        total_pages = (len(data) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = data[start_index:end_index]

        return {
            'contract_file': page_list,
            'total': total
        }
