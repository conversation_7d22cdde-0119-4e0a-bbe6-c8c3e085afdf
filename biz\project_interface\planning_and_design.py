import json

from iBroker.lib import mysql


def get_filename(url_list):
    content_url = []
    for url in url_list:
        filename = url.split('/')[-1].rsplit('_', 1)[0]
        file_dict = {'url': url, 'filename': filename}
        content_url.append(file_dict)
    return content_url


class PlanningAndDesignInterface(object):

    def design_document_query(self, project_name):
        document_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name,url,upload_time,file_remark FROM maintenance_upload_data " \
              "WHERE project_name='%s' AND node = '架构'" % project_name
        query_data = db.get_all(sql)
        if query_data:
            for item in query_data:
                file_name = item.get("file_name")
                url = item.get("url")
                upload_time = item.get("upload_time")
                file_remark = item.get("file_remark")
                url_list = []
                if url:
                    for row in json.loads(url):
                        if row.get("response",{}).get("FileList",[]):
                            url_list.append({
                                "name": row.get("name"),
                                "url": row.get("response",{}).get("FileList",[])[0].get("url")
                            })
                    document_list.append({
                        "file_name": file_name,
                        "file_url": url_list,
                        "upload_time": upload_time,
                        "file_remark": file_remark
                    })
                elif file_remark:
                    document_list.append({
                        "file_name": file_name,
                        "file_url": [],
                        "upload_time": upload_time,
                        "file_remark": file_remark
                    })
        data = {
            "document_list": document_list
        }
        return data
    def plan_specifications_data(self, project_name):
        """
            技术规格书
        """
        db = mysql.new_mysql_instance("tbconstruct")
        # 规格书——规格书部分
        sql1 = "SELECT name,content,summary FROM program_specifications_data " \
               "WHERE project_name='%s' AND father = '规格书部分'" % project_name
        # 规格书——图纸部分
        sql2 = "SELECT name,content,summary FROM program_specifications_data " \
               "WHERE project_name='%s' AND father = '图纸部分'" % project_name
        # 规格书——技术文档部分
        sql3 = "SELECT name,content,summary FROM program_specifications_data " \
               "WHERE project_name='%s' AND father = '技术文档部分'" % project_name
        # 规格书上传时间、上传人、编号
        sql4 = "SELECT plan_specifications_time,plan_specifications_submitter,technical_specification_version " \
               "FROM program_and_planning_process WHERE project_name='%s'" % project_name
        specification_list = db.get_all(sql1)
        drawing_list = db.get_all(sql2)
        document_list = db.get_all(sql3)
        basic_data = db.get_all(sql4)
        if basic_data:
            plan_specifications_time = basic_data[0].get("plan_specifications_time")
            plan_specifications_submitter = basic_data[0].get("plan_specifications_submitter")
            technical_specification_version = basic_data[0].get("technical_specification_version")
        else:
            plan_specifications_time = ''
            plan_specifications_submitter = ''
            technical_specification_version = ''
        for specification in specification_list:
            if specification.get("content"):
                url_list = specification.get("content").split(";")
                content_url = get_filename(url_list)
                specification['content'] = content_url
            else:
                specification['content'] = []
            specification['plan_specifications_time'] = plan_specifications_time
            specification['plan_specifications_submitter'] = plan_specifications_submitter
            specification['technical_specification_version'] = technical_specification_version

        for drawing in drawing_list:
            if drawing.get("content"):
                url_list = drawing.get("content").split(";")
                content_url = get_filename(url_list)
                drawing['content'] = content_url
            else:
                drawing['content'] = []
            drawing['plan_specifications_time'] = plan_specifications_time
            drawing['plan_specifications_submitter'] = plan_specifications_submitter
            drawing['technical_specification_version'] = technical_specification_version

        for document in document_list:
            if document.get("content"):
                url_list = document.get("content").split(";")
                content_url = get_filename(url_list)
                document['content'] = content_url
            else:
                document['content'] = []
            document['plan_specifications_time'] = plan_specifications_time
            document['plan_specifications_submitter'] = plan_specifications_submitter
            document['technical_specification_version'] = technical_specification_version
        data = {
            "specification_list": specification_list,
            "drawing_list": drawing_list,
            "document_list": document_list
        }
        return data

    def device_configuration(self, project_name):
        """
            设备清单
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT device_configuration,device_configuration_submitter,device_documents_time  " \
               "FROM program_and_planning_process WHERE project_name='%s'" % project_name
        equipment_list = db.get_all(sql1)
        if equipment_list:
            data = {
                'device_configuration': equipment_list[0].get('device_configuration'),
                'device_configuration_submitter': equipment_list[0].get('device_configuration_submitter'),
                'device_documents_time': equipment_list[0].get('device_documents_time')
            }
        else:
            data = {
                'device_configuration': '',
                'device_configuration_submitter': '',
                'device_documents_time': ''
            }

        return data

    def design_drawings(self, project_name):
        """
            设计图纸
        """
        db = mysql.new_mysql_instance("tbconstruct")
        # 规格书——规格书部分
        sql1 = "SELECT name,content,summary FROM program_design_drawings " \
               "WHERE project_name='%s' AND father = '规格书部分'" % project_name
        # 规格书——图纸部分
        sql2 = "SELECT name,content,summary FROM program_design_drawings " \
               "WHERE project_name='%s' AND father = '图纸部分'" % project_name
        # 规格书——技术文档部分
        sql3 = "SELECT name,content,summary FROM program_design_drawings " \
               "WHERE project_name='%s' AND father = '技术文档部分'" % project_name
        # 规格书上传时间、上传人、编号
        sql4 = "SELECT design_drawings_submitter,device_documents_time " \
               "FROM program_and_planning_process WHERE project_name='%s'" % project_name
        specification_list = db.get_all(sql1)
        drawing_list = db.get_all(sql2)
        document_list = db.get_all(sql3)
        basic_data = db.get_all(sql4)
        if basic_data:
            device_documents_time = basic_data[0].get("device_documents_time")
            design_drawings_submitter = basic_data[0].get("design_drawings_submitter")
        else:
            device_documents_time = ''
            design_drawings_submitter = ''
        for specification in specification_list:
            if specification.get("content"):
                url_list = specification.get("content").split(";")
                content_url = get_filename(url_list)
                specification['content'] = content_url
            else:
                specification['content'] = []
            specification['device_documents_time'] = device_documents_time
            specification['design_drawings_submitter'] = design_drawings_submitter

        for drawing in drawing_list:
            if drawing.get("content"):
                url_list = drawing.get("content").split(";")
                content_url = get_filename(url_list)
                drawing['content'] = content_url
            else:
                drawing['content'] = []
            drawing['device_documents_time'] = device_documents_time
            drawing['design_drawings_submitter'] = design_drawings_submitter

        for document in document_list:
            if document.get("content"):
                url_list = document.get("content").split(";")
                content_url = get_filename(url_list)
                document['content'] = content_url
            else:
                document['content'] = []
            document['device_documents_time'] = device_documents_time
            document['design_drawings_submitter'] = design_drawings_submitter
        data = {
            "specification_list": specification_list,
            "drawing_list": drawing_list,
            "document_list": document_list
        }
        return data

    def design_drawings_changes(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT ticket_id,major,reason_and_content,update_drawing,submitter,time FROM deepen_design_change " \
               "WHERE project_name='%s'" % project_name
        changes_list = db.get_all(sql1)
        if changes_list:
            for i in changes_list:
                ticket_id = i.get('ticket_id')
                sql = "SELECT file_name,file_no,adjust_content,before_url,after_url,submitter,time,major " \
                      "FROM deepen_design_change_document_details WHERE ticket_id='%s'" % ticket_id
                change_document_list = db.get_all(sql)
                if change_document_list:
                    for j in change_document_list:
                        after_url = j.get('after_url')
                        before_url = j.get('before_url')
                        j['after_url'] = after_url.split(';')
                        j['before_url'] = before_url.split(';')
                i["changes"] = change_document_list
                update_drawing = i.get('update_drawing')
                i['update_drawing'] = update_drawing.split(';')
        else:
            changes_list = []
        return changes_list

    def equipment_material(self, project_name, page_size, page_number):
        page_size = page_size  # 每页的结果数量
        page_number = page_number  # 页码
        offset = (page_number - 1) * page_size  # 计算偏移量

        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(ticket_id) FROM program_equipment_material_list WHERE project_name='{project_name}'"

        sql1 = "SELECT ApplicationTypeCode,ApplicationTypeName,BelongApplicationTypeName,BelongApplicationName," \
               "CompleteMachine, device_No,device_category,device_display_name,device_name_chinese,zone_name," \
               "device_name_english, device_routine_name,function_room_name_cn,function_room_name_en,whether_monitor" \
               f" FROM program_equipment_material_list WHERE project_name='{project_name}'"
        sql1 += f" LIMIT {offset}, {page_size}"
        # 规格书上传时间、上传人、编号
        sql4 = "SELECT material_and_identification_submitter,device_documents_time " \
               "FROM program_and_planning_process WHERE project_name='%s'" % project_name

        basic_data = db.get_all(sql4)
        if basic_data:
            material_submitter = basic_data[0].get("material_and_identification_submitter")
            device_documents_time = basic_data[0].get("device_documents_time")
        else:
            material_submitter = ''
            device_documents_time = ''
        # 总数
        count_info = db.get_all(sql)
        total_results = 0
        if count_info:
            total_results = count_info[0].get('count(ticket_id)')
        material_list = db.get_all(sql1)
        for material in material_list:
            material['material_submitter'] = material_submitter
            material['device_documents_time'] = device_documents_time

        data = {
            'material_list': material_list,
            'total_results': total_results
        }
        return data
