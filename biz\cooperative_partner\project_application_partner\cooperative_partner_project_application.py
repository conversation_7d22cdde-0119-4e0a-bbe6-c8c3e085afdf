import hashlib
import hmac
import json
import time
from datetime import datetime

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl

from biz.cooperative_partner.project_application_partner.tools import Tools


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


def MakeAuthorization(
    systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True
) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(
            algoName, timestamp, signature, systemId
        ),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(
        title="Dcops请求外部接口",
        system_name="Dcops",
        url=url,
        postdata=str_data,
        append_header=headers,
        timeout=1800,
    )
    result = resp.json()
    return result


def date_format(date_str):
    try:
        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
        date_only_str = date_obj.strftime("%Y-%m-%d")
        return date_only_str
    except ValueError:
        return False


class PartnerCallInterfaceXmbj(object):
    """
    项目报建
    接口数据上传
    """

    def __init__(self):
        super(PartnerCallInterfaceXmbj, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def create_ticket(self, ProjectName, PartnerTicketId, Facilitator):
        """
        创建项目报建流程（起腾讯侧工单）
        """
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map(
            "cooperative_partner_supplier_distinguish"
        )
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        supplier_info = "supplier_differentiation"
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        res_list = []
        now = datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号{ProjectName}"
        processTitle = date_string + "项目报建"
        if system_type == "外部":
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        "Facilitator": Facilitator,
                        "project_name": ProjectName,
                        "PartnerTicketId": PartnerTicketId,
                    },
                    "ProcessDefinitionKey": "cooperative_partner_project_construction_application",
                    "Source": "",
                    "TicketDescription": processTitle,
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang",
                    },
                },
            }
            # 起单，并抛入data
            res = gnetops.request(
                action="Collaboration", method="CreateTicket", data=data
            )
            res_list.append(res)
        elif system_type == "Dcops":
            data = {
                "FacilitatorName": Facilitator,
                "FacilitatorTicketId": PartnerTicketId,
                "TicketCreateData": {
                    "CustomVariables": {
                        "Facilitator": Facilitator,
                        "project_name": ProjectName,
                        "system_id": system_id,
                        "PartnerTicketId": PartnerTicketId,
                    },
                    "ProcessDefinitionKey": "cooperative_partner_project_construction_application",
                    "Source": "",
                    "TicketDescription": processTitle,
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang",
                    },
                },
            }
            # 起单，并抛入data
            res = gnetops.request(
                action="Collaboration", method="CreateTicket", data=data
            )
            res_list.append(res)

        for row in res_list:
            TicketId = row.get("TicketId")

        return {"code": 200, "msg": "成功", "data": TicketId}

    def project_application_confirmation_of_completion(
        self,
        TencentTicketId,
        ProjectName,
        NumberName,
        TaskName,
        StartTime,
        FinishTime,
        ScheduleSkewingExplain,
        Appendix,
        Remark,
    ):
        """
        项目报建子任务完工确认
        """

        if not Remark:
            Remark = "无"
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {"code": -1, "msg": "工单号或项目名称缺失"}
        # 校验 TaskName
        if not TaskName or not NumberName:
            return {"code": -1, "msg": "子任务名称或子任务编号缺失"}
        # 校验 StartTime 和 FinishTime
        if not StartTime or not FinishTime:
            return {"code": -1, "msg": "实际开始时间或实际完成时间缺失"}
        if not date_format(StartTime) or not date_format(FinishTime):
            return {
                "code": -1,
                "msg": "实际开始时间或实际完成时间格式不符，应为'yyyy-mm-dd'",
            }
        # 校验 ScheduleSkewingExplain
        if not ScheduleSkewingExplain:
            return {"code": -1, "msg": "进度偏移说明"}
        if not Appendix and not Remark:
            return {"code": -1, "msg": "无附件上传请填写备注"}
        # 校验 子任务工作项 是否在计划中
        plan = Tools.get_construction_plan(ProjectName, NumberName, TaskName)
        if not plan:
            return {
                "code": -1,
                "msg": "未查到子任务工作项，请仔细核对子任务编号和子任务名称",
            }
        # 校验 前置工作项完成校验
        instance_id = Tools.get_instance_id(TencentTicketId)
        main_flow_vars = flow.get_variables(
            instance_id,
            ["subtask_ticket"],
        )
        subtask_ticket = main_flow_vars.get("subtask_ticket", {})
        subtask_ticket_id = subtask_ticket.get(NumberName)
        must_subtask = [
            key for key, val in subtask_ticket.items() if val and val != "不涉及"
        ]
        if not subtask_ticket_id:
            return {
                "code": -1,
                "msg": f"请先完成前置工作项：{must_subtask}",
            }
        # 校验 不涉及
        if subtask_ticket_id == "不涉及":
            return {
                "code": -1,
                "msg": "该工作项不涉及",
            }
        # 校验 是否有对应节点
        subtask_instance_id = Tools.get_instance_id(subtask_ticket_id)
        SubTaskId = ""
        query_data = {"InstanceId": subtask_instance_id}
        res = gnetops.request(
            action="TaskCenter", method="QueryWorkflowLog", data=query_data
        )
        for row in res:
            task_name = row.get("TaskName")
            task_status = row.get("TaskStatus")
            target = "等待集成商反馈"
            if task_name == target and task_status == "进行中":
                SubTaskId = row.get("TaskId")
        if not SubTaskId:
            return {
                "code": -1,
                "msg": "未找到该工作项等待集成商反馈节点",
            }
        data = {
            "TencentTicketId": TencentTicketId,
            "ProjectName": ProjectName,
            "NumberName": NumberName,
            "TaskName": TaskName,
            "StartTime": date_format(StartTime),
            "FinishTime": date_format(FinishTime),
            "ScheduleSkewingExplain": ScheduleSkewingExplain,
            "Appendix": Appendix,
            "Remark": Remark,
            "SubTaskId": SubTaskId,
            "subtask_ticket_id": subtask_ticket_id,
        }
        Tools.completion_confirmation_handle(data)
        return {"code": 200, "msg": "成功"}

    def fire_third_party_test_report_uploaded(
        self, TencentTicketId, ProjectName, FireThirdPartyTestReport, Remark
    ):
        """
        消防第三方检测报告上传
        """
        if not Remark:
            Remark = "无"
        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {"code": -1, "msg": "工单号或项目名称缺失"}
        data = {
            "TencentTicketId": TencentTicketId,
            "ProjectName": ProjectName,
            "FireThirdPartyTestReport": FireThirdPartyTestReport,
            "Remark": Remark,
        }
        Tools.fire_test_report_handle(data)
        return {"code": 200, "msg": "成功"}


class ProjectApplication(object):
    """
    项目报建
    腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(ProjectApplication, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_application_confirmation_of_completion_review(
        self,
        project_name,
        number_name,
        task_name,
        examine_approve_result,
        dismiss_role,
        remark,
    ):
        """
        项目报建子项任务完工确认审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        key = ""
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
            key = "jl"
        elif dismiss_role == "项管":
            role = 2
            key = "xg"
        elif dismiss_role == "PM":
            role = 3
            key = "pm"
        elif not dismiss_role:
            role = 4

        data = {
            "ProjectName": project_name,  # 项目名称
            # 子项编号名称（2.4.1、2.4.2、2.4.3、2.4.4、2.4.5、2.4.6、2.4.7、2.4.8、2.4.9、2.4.10、2.4.11、2.4.12）
            "NumberName": number_name,
            # 子项任务名称（项目立项、设计合同签订上传、图纸送审、（监理直接发包、上传）、（工程直接发包、上传）、监理合同签订上传、
            # 工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险、施工许可证下发、消防备案、竣工备案）
            "TaskName": task_name,
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId,  # 合作伙伴工单号
        }

        Facilitator = ""
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT integrators FROM supplier_resources "
            f"WHERE CONCAT(campus, project) = '{project_name}' "
        )
        res = db.get_row(sql)
        Facilitator = res.get("integrators")
        if res:
            Facilitator = res.get("integrators")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map(
            "cooperative_partner_supplier_distinguish"
        )
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        # 此节点再七彩石配置中的key
        interface_info = "create_order"
        supplier_info = "supplier_differentiation"
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = "create_order"
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index",  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "project_application_confirmation_of_completion_review",  # (*必填) 云函数名称
                        "data": data,  # 传递给云函数的入参
                        "systemId": f"{system_id}",  # 为服务商分配的systemId，用于区分服务商云函数
                    },
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task",
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info["data"]

            # 获取 code 的值
            code = result_str.get("code")
            message = result_str.get("msg")
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action="Request",
                method="RequestToFacilitator",
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/"
                    "project_application_confirmation_of_completion_review",
                },
                timeout=1800,
            )
            if info.get("ErrorCode") != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            f"{key}_approval_review": {
                "data": data,
                "res": info,
            }
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def fire_third_party_test_report_PM_review(
        self, project_name, PM_approval, PM_remark
    ):
        """
        消防第三方检测报告PM审核反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        if PM_approval == "同意":
            examine_approve = 1
        elif PM_approval == "驳回":
            examine_approve = 2

        if not PM_remark:
            PM_remark = "无"

        data = {
            "ProjectName": project_name,  # 项目名称
            "PmApproval": examine_approve,  # PM审批（1、同意，2、驳回）
            "PMRemark": PM_remark,  # PM备注
            "PartnerTicketId": PartnerTicketId,  # 合作伙伴工单号
        }

        Facilitator = ""
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT integrators FROM supplier_resources "
            f"WHERE CONCAT(campus, project) = '{project_name}' "
        )
        res = db.get_row(sql)
        Facilitator = res.get("integrators")
        if res:
            Facilitator = res.get("integrators")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map(
            "cooperative_partner_supplier_distinguish"
        )
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        # 此节点再七彩石配置中的key
        interface_info = "create_order"
        supplier_info = "supplier_differentiation"
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = "create_order"
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index",  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "fire_third_party_test_report_PM_review",  # (*必填) 云函数名称
                        "data": data,  # 传递给云函数的入参
                        "systemId": f"{system_id}",  # 为服务商分配的systemId，用于区分服务商云函数
                    },
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task",
            }
            info = calling_external_interfaces(interface_info, data)

            result_str = info["data"]

            # 获取 code 的值
            code = result_str.get("code")
            message = result_str.get("msg")
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action="Request",
                method="RequestToFacilitator",
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/fire_third_party_test_report_PM_review",
                },
                timeout=1800,
            )
            if info.get("ErrorCode") != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {"xfbg_pm_approval_review": info}
        flow.complete_task(self.ctx.task_id, variables=variables)
