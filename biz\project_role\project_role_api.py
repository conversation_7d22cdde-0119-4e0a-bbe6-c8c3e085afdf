from iBroker.lib import config

from biz.project_role.tools import Tools
from biz.project_member_account_application.basic_field_handle import BasicFieldHandle


class ProjectRoleApi(object):
    @staticmethod
    def query_project_role(mozu):
        """
        通过模组获取项目角色
        :param mozu: str,模组
        :return: role_dict: dict, {角色: 账号}
        """
        if mozu:
            project_name = Tools.get_project_name(mozu)
            if project_name:
                # 获取七彩石配置
                device_role_list = config.get_config_map(
                    "device_role_list"
                )
                if device_role_list:
                    # 构造数据
                    role_dict = {key: "" for key in  device_role_list}
                    role_dict["项目PM"] = ""
                    # 获取PM
                    role_dict["项目PM"] = Tools.get_PM(project_name)
                    # 获取项目角色
                    role_str = ', '.join([f"'{role}'" for role in device_role_list])
                    role_account_list = Tools.query_project_role_account(project_name, role_str)
                    for item in role_account_list:
                        if item.get("role") and item.get("account"):
                            if role_dict[item.get("role")]:
                                role_dict[item.get("role")] += "," + item.get("account")
                            else:
                                role_dict[item.get("role")] = item.get("account")
                    return role_dict
                else:
                    return {"code": 400, "msg": "七彩石device_role_list未配置"}
            else:
                return {"code": 400, "msg": "模组未匹配到对应项目名称"}
        else:
            return {"code": 400, "msg": "输入参数为空"}
