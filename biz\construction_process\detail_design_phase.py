import datetime
import json

from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue


class InformationAcquisitionDuringDesignPhase(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def auto_create_design_ticket(self, project_name, integrator):
        flow_key = "obtaining_documents_during_detailed_design_phase"
        ticket_info = gnetops.create_ticket(
            flow_key=flow_key,
            description='该工单为："' + project_name + '深化设计阶段触发工单',
            ticket_level=3,
            title=project_name + ' - 深化设计',
            creator="v_zongxiyu",
            concern="v_zongxiyu",  # 关注人, 这里暂为空
            deal="v_zongxiyu;youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var={
                "project_name": project_name,  # 项目名称
                "integrator": integrator,  # 集成商
            }  # 自定义流程变量，抛给派单子流程
        )
        design_ticket_id = str(ticket_info.get("TicketId"))
        variables = {
            "design_ticket_id": design_ticket_id
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_design_people_info(self, project_name, integrator):
        db = mysql.new_mysql_instance("tbconstruct")
        # 获取项目编号
        item_number_sql = (f"SELECT campus,project,dcopsTicketId,item_number FROM risk_early_warning_data "
                           f"WHERE project_name = '{project_name}'")
        query_data = db.get_row(item_number_sql)
        if query_data:
            project_number = query_data.get("item_number")
            campus = query_data.get("campus")
            project = query_data.get("project")
            main_ticket_id = query_data.get("dcopsTicketId")
        else:
            return {"code": -1, "msg": "项目编号获取失败"}
        # 获取项管人员
        xg_sql = ("SELECT account, role FROM project_role_account "
                  "WHERE project_name = '%s' "
                  "AND role IN ('集成商-项目经理','集成商-设计经理')"
                  "AND del_flag = '0'") % project_name
        xg = db.get_all(xg_sql)
        integrator_dm = ''
        integrator_pm = ''
        if xg:
            for i in xg:
                if i.get("role") == "集成商-项目经理":
                    integrator_pm = i.get("account")
                elif i.get("role") == "集成商-设计经理":
                    integrator_dm = i.get("account")
        # if not integrator_dm:
        #     return {"code": -1, "msg": "未获取到设计经理信息"}
        # if not integrator_pm:
        #     return {"code": -1, "msg": "未获取到项目经理信息"}
        # dm_sql = "SELECT architecture FROM project_team_information_data " \
        #          " WHERE project_name='%s'" % project_name
        # dm_info = db.get_row(dm_sql)
        # if dm_info:
        #     integrator_dm = dm_info["architecture"]
        # else:
        #     return {"code": -1, "msg": "未获取到设计人员信息"}
        status = "已启动"
        project_start_date = datetime.datetime.now().strftime("%Y-%m-%d")
        data = {
            "ProjectCode": project_number,
            "Integrator": integrator,
            "ProjectDate": project_start_date,
            "IntegratorDm": integrator_dm,
            "IntegratorPm": integrator_pm,
            "Status": status
        }
        data = {
            "ProjectCode": "PJ52QWDFTR",
            "Integrator": "德衡",
            "ProjectDate": "2023-05-15",
            "IntegratorDm": "v_zongxiyu",
            "IntegratorPm": "v_zongxiyu",
            "Status": "已启动"
        }
        info_data_back = gnetops.request(action="Tbconstruct",
                                         method="InitiateDesign",
                                         ext_data=data,
                                         scheme="ifob-infrastructure"
                                         )
        self.workflow_detail.add_kv_table("接口回传信息111", info_data_back)
        code = info_data_back.get("code")
        self.workflow_detail.add_kv_table("接口回传状态码", code)
        if code == 0:
            variables = {
                "project_name": project_name,
                "data": data,
                "item_number": "PJ52QWDFTR",
                # "item_number": project_number,
                "data_back": info_data_back,
                "campus": campus,
                "project": project,
                "main_ticket_id": main_ticket_id,
                "integrator_dm": integrator_dm
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        elif code == 500 and info_data_back.get("message") == "深化设计已经启动":
            variables = {
                "project_name": project_name,
                "data": data,
                "item_number": "PJ52QWDFTR",
                # "item_number": project_number,
                "data_back": info_data_back,
                "campus": campus,
                "project": project,
                "main_ticket_id": main_ticket_id,
                "integrator_dm": integrator_dm
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            self.workflow_detail.add_kv_table("失败原因", {"接口回传信息": info_data_back})
            return {"code": -1, "msg": "深化设计建单失败"}

    def query_design_progress_info(self):
        task_id = self.ctx.task_id
        item_number = self.ctx.variables.get("item_number")
        state_data_back = gnetops.request(action="Tbconstruct",
                                          method="QueryDesignProgressInfo",
                                          ext_data={
                                              "ProjectCode": item_number
                                          },
                                          scheme="ifob-infrastructure"
                                          )
        self.workflow_detail.add_kv_table("接口回传信息222", state_data_back)
        code = state_data_back.get("code")
        is_complete = 0
        self.workflow_detail.add_kv_table("接口回传状态码", code)
        if code == 0:
            data = state_data_back.get("data")
            if data and data.get("progress"):
                for item in data.get("progress"):
                    if item.get("step_name") == "设计完成" and item.get("status") == "已完成":
                        completed_at = item.get("completed_at", "")
                        is_complete = 1
                        if completed_at:
                            campus = self.ctx.variables.get("campus")
                            ticket_id = self.ctx.variables.get("main_ticket_id")
                            project = self.ctx.variables.get("project")
                            response_data = gnetops.request(
                                action="Project",
                                method="UpdateSummaryData",
                                ext_data={
                                    "campus": campus,
                                    "project_name": project,
                                    "ticket_id": ticket_id,
                                    "update_filed": "DesignEndTime",
                                    "update_value": completed_at
                                },
                                scheme="ifob-infrastructure",
                            )
                        WorkflowContinue(task_id=task_id).call()
                        return {"success": True, "data": "设计完成"}
        if is_complete == 0:
            return {"success": False, "data": "设计未完成"}

    def database_file_comparison(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        # sql1 = ("SELECT file_name,file_type FROM table_name")
        sql2 = ("SELECT file_name,url,file_type,file_remark FROM maintenance_upload_data "
                "WHERE project_name='%s' and node='架构'") % project_name
        data_list = [
            {"file_name": "深化设计深化确认图纸（包含机房设计说明）", "file_type": "设计说明及图纸"},
            {"file_name": "深化设计评审问题记录清单", "file_type": "设计说明及图纸"},
            {"file_name": "竣工图", "file_type": "设计说明及图纸"},
            {"file_name": "设计变更清单", "file_type": "设计说明及图纸"},
            {"file_name": "电气负荷计算书", "file_type": "计算书"},
            {"file_name": "UPS选型计算书", "file_type": "计算书"},
            {"file_name": "HVDC选型计算书", "file_type": "计算书"},
            {"file_name": "直流屏选型计算书", "file_type": "计算书"},
            {"file_name": "UPS蓄电池选型计算书", "file_type": "计算书"},
            {"file_name": "HVDC蓄电池选型计算书", "file_type": "计算书"},
            {"file_name": "直流屏蓄电池选型计算书", "file_type": "计算书"},
            {"file_name": "保护定值计算书", "file_type": "计算书"},
            {"file_name": "暖通负荷计算书", "file_type": "计算书"},
            {"file_name": "电气系统切控制辑及说明（包含中压、柴发、低压、ATS、供油系统）", "file_type": "控制逻辑说明"},
            {"file_name": "空调系统控制逻辑及说明", "file_type": "控制逻辑说明"},
            {"file_name": "弱电系统控制逻辑及说明", "file_type": "控制逻辑说明"},
            {"file_name": "消防系统控制逻辑及说明", "file_type": "控制逻辑说明"},
            {"file_name": "厂房通风系统控制逻辑及说明", "file_type": "控制逻辑说明"},
            {"file_name": "智能照明系统控制逻辑及说明", "file_type": "控制逻辑说明"},
            {"file_name": "BIM设计文件", "file_type": "模拟仿真"},
            {"file_name": "机房设备清单", "file_type": "设计说明及图纸"},
            {"file_name": "T-block BOM清单", "file_type": "设计说明及图纸"},
            {"file_name": "CFD仿真文件", "file_type": "模拟仿真"},
        ]
        dif_file_list = []
        upload_list = db.get_all(sql2)
        if upload_list:
            for item in data_list:
                is_uploaded = 0
                for row in upload_list:
                    if item.get("file_name") == row.get("file_name"):
                        is_uploaded = 1
                if is_uploaded == 0:
                    dif_file_list.append(item)
        else:
            dif_file_list = data_list
        if dif_file_list:
            is_dif = "1"
        else:
            is_dif = "0"
        variables = {"dif_file_list": dif_file_list,
                     "is_dif": is_dif}
        flow.complete_task(self.ctx.task_id, variables=variables)


class DetailDesignDifferentFileView(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dif_file_list = process_data.get("dif_file_list")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        insert_list = []
        for item in dif_file_list:
            file_url = item.get("file_url", [])
            if file_url:
                for row in file_url:
                    FileList = row.get('response', {}).get('FileList', [])
                    if FileList:
                        if FileList[0].get('status') != "success":
                            return {"code": -1, "msg": "文件上传失败"}
            remark = item.get("remark", "")
            if not remark and not file_url:
                return {"code": -1, "msg": "请上传文件或填写无文件原因备注"}
            insert_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                "file_name": item.get("file_name"),
                "file_type": item.get("file_type"),
                "file_remark": remark,
                "url": json.dumps(file_url, ensure_ascii=False) if file_url else "",
                "node": "架构",
                "upload_time": datetime.datetime.now().strftime("%Y-%m-%d")
            })
        db = mysql.new_mysql_instance("tbconstruct")
        db.insert_batch("maintenance_upload_data", insert_list)
        flow.complete_task(self.ctx.task_id, variables={"dif_file_list": dif_file_list,
                                                        "insert_list": insert_list})
