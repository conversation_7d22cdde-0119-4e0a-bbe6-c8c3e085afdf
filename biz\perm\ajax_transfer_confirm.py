"""
转岗确认相关逻辑
<AUTHOR>
@created 2021/4/19
"""
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.todo_base import AjaxTodoBase


class AjaxTransferConfirm(AjaxTodoBase):
    """
    权限系统转岗确认相关逻辑
    """
    PROTOCOL_KEY = "nbroker.protocol.AjaxTransferConfirm"

    def __init__(self):
        super(AjaxTransferConfirm, self).__init__()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        flow_vars = {}
        if process_data:
            for k, v in dict(process_data).items():
                flow_vars[k] = v

        if process_user:
            flow_vars["ConfirmUser"] = process_user
        flow.complete_task(self.ctx.task_id, flow_vars)
