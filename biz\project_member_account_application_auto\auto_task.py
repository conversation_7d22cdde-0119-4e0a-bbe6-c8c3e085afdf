from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import config
import datetime
from biz.project_member_account_application_auto.basic_field_handle import (
    BasicFieldHandle,
)
from biz.project_member_account_application_auto.account_application_api import (
    AutoAccountApplicationApi,
)


# 账号开通 自动任务类
class AutoAccountApplicationAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def is_joint_construction_project(self, project_name):
        """
        判断是否是"合建"
        """
        flag = BasicFieldHandle.is_joint_construction_project(project_name)
        variables = {
            "is_joint_construction_project": flag,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_basic_info_joint(self):
        """
        获取基础下拉选项(合建)
        """
        # 合建
        campus_name_list = BasicFieldHandle.get_basic_info("合建园区")
        campus_name_options = BasicFieldHandle.get_options_info(campus_name_list)
        project_name_list = BasicFieldHandle.get_basic_info("合建项目")
        project_name_options = BasicFieldHandle.get_options_info(project_name_list)
        role_list = BasicFieldHandle.get_basic_info("合建角色")
        role_options = BasicFieldHandle.get_options_info(role_list)
        position_list = BasicFieldHandle.get_basic_info("合建职务")
        position_options = BasicFieldHandle.get_options_info(position_list)
        # 建单人(pm)
        PM = self.ctx.variables.get("Creator")
        # 账号开通负责人
        account_application_handler = config.get_config_map(
            "account_application_handler"
        )
        variables = {
            "PM": PM,
            "account_application_handler": account_application_handler,
            "campus_name_options": campus_name_options,
            "project_name_options": project_name_options,
            "role_options": role_options,
            "position_options": position_options,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def query_basic_info(self):
        """
        获取基础下拉选项(自建)
        """
        campus_name_list = BasicFieldHandle.get_basic_info("园区")
        campus_name_options = BasicFieldHandle.get_options_info(campus_name_list)
        project_name_list = BasicFieldHandle.get_basic_info("项目")
        project_name_options = BasicFieldHandle.get_options_info(project_name_list)
        role_list = BasicFieldHandle.get_basic_info("角色")
        role_options = BasicFieldHandle.get_options_info(role_list)
        service_provider_list = BasicFieldHandle.get_supplier()
        service_provider_options = BasicFieldHandle.get_options_info(
            service_provider_list
        )
        position_list = BasicFieldHandle.get_basic_info("职务")
        position_options = BasicFieldHandle.get_options_info(position_list)
        # 建单人(pm)
        PM = self.ctx.variables.get("Creator")
        # 账号开通负责人
        account_application_handler = config.get_config_map(
            "account_application_handler"
        )
        variables = {
            "PM": PM,
            "account_application_handler": account_application_handler,
            "campus_name_options": campus_name_options,
            "project_name_options": project_name_options,
            "role_options": role_options,
            "service_provider_options": service_provider_options,
            "position_options": position_options,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 判断是否需要起创建企微账号流程工单（不需要：有存在已创建的科技账号 ）
    def create_wework_account_flow_determine(self, member_list):
        """
        判断是否需要起创建企微账号流程工单（不需要：有存在已创建的科技账号 ）
        """
        # 通过手机号获取账号(能查到就有，查不到就没有)
        member_list = AutoAccountApplicationApi.get_account_by_phone(member_list)
        # 判断是否需要起单
        create_wework_account_flow_flag = 0
        for item in member_list:
            if item["keji_account_flag"] and item.get("userid") and item.get("username"):
                item["is_truly_create_account"] = "否"
            else:
                item["is_truly_create_account"] = "是"
                create_wework_account_flow_flag = 1
        variables = {
            "create_wework_account_flow_flag": create_wework_account_flow_flag,
            "add_member_list": member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 自动起单-创建账号流程（create_wework_account）
    def create_wework_account_automatic(self, member_list):
        """
        自动起单-创建账号流程（create_wework_account）
        """
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        # 需先调接口生成userid、username、user_position，作为建单输入参数
        member_list = AutoAccountApplicationApi.get_account(member_list)
        # 获取建单人
        creator = self.ctx.variables.get("Creator")
        create_input = []
        create_output = []
        # 重复账号可直接复用工单，无需重新起单
        create_account_ticket_dict = {}
        # 起单
        for i in member_list:
            if i.get("is_truly_create_account") == "是":
                name = i.get("name")
                userid = i.get("userid")
                username = i.get("username")
                user_position = i.get("user_position")
                project_name = f"{i.get('campus_name')}{i.get('project_name')}"
                if userid not in create_account_ticket_dict:
                    # 输入
                    processTitle = (
                        f"{project_name}项目成员企业微信账号创建: {name} {userid}"
                    )
                    data = {
                        "CustomVariables": {
                            "full_name": name,
                            "account_type": "IDC建设服务人员",
                            "mobile": i.get("phone"),
                            "gender_return": i.get("sex", "男"),
                            "position": user_position,
                            "dept_chain": f"犀牛鸟有限责任公司/IDC项目建设/{project_name}",
                            "supply_name": i.get("service_provider"),
                            "name": username,
                            "userid": userid,
                            "company": "犀牛鸟有限责任公司",
                        },
                        "ProcessDefinitionKey": "create_wework_account",
                        "Source": "",
                        "TicketDescription": processTitle,
                        "TicketLevel": "3",
                        "TicketTitle": processTitle,
                        "UserInfo": {
                            "Concern": f"{creator};{flow_responsible_person}",  # 关注人 可填多个
                            "Creator": creator,  # 建单人 只能填一个
                            "Deal": flow_responsible_person,  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
                        },
                    }
                    create_input.append(data)
                    self.workflow_detail.add_kv_table(
                        "1.企业微信账号创建流程起单输入", {"message": create_input}
                    )
                    # 输出
                    res = gnetops.request(action="Ticket", method="Create", data=data)
                    create_output.append(res)
                    self.workflow_detail.add_kv_table(
                        "2.企业微信账号创建流程起单输出", {"message": create_output}
                    )
                    # 起单工单号返回
                    i["ticket_id"] = res.get("TicketId")
                    create_account_ticket_dict[userid] = res.get("TicketId")
                else:
                    i["ticket_id"] = create_account_ticket_dict[userid]
        self.workflow_detail.add_kv_table("3.创建账号信息", {"message": member_list})
        add_query_ticket_status_args = {
            "member_list": member_list,
        }
        variables = {
            "create_input": create_input,
            "create_output": create_output,
            "add_member_list": member_list,
            "add_query_ticket_status_args": add_query_ticket_status_args,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 等企业微信账号创建流程所有工单结单(定时任务)
    def query_ticket_status(self, member_list):
        """
        等企业微信账号创建流程所有工单结单(定时任务)
        """
        # 判断是否都结单
        ticket_end_flag = True
        # 重复工单可直接复用结果，无需重新查询
        ticket_status_dict = {}
        for item in member_list:
            ticket_id = item.get("ticket_id")
            if ticket_id:
                if ticket_id not in ticket_status_dict:
                    query_data = {
                        "SchemaId": "ticket_base",
                        "Data": {
                            "ResultColumns": {"TicketStatus": "", "TicketId": ""},
                            "SearchCondition": {"TicketId": [str(ticket_id)]},
                        },
                    }
                    query_result = gnetops.request(
                        action="QueryData", method="Run", ext_data=query_data
                    )
                    result_list = query_result.get("List")
                    for res in result_list:
                        if res.get("TicketId") == str(ticket_id):
                            ticket_status = (
                                "运行中"
                                if res.get("TicketStatus") == "OPEN"
                                else "已结单"
                            )
                            item["ticket_status"] = ticket_status
                            ticket_status_dict[ticket_id] = ticket_status
                            # 可以认为除了OPEN是运行中外，其它的都是已结单
                            if res.get("TicketStatus") == "OPEN":
                                ticket_end_flag = False
                else:
                    item["ticket_status"] = ticket_status_dict[ticket_id]
        date = datetime.datetime.today()
        date = date.strftime("%Y-%m-%d %H:%M:%S")
        self.workflow_detail.add_kv_table(
            f"企业微信账号创建流程建单工单状态({date})",
            {"message": member_list},
        )
        if ticket_end_flag:
            variables = {
                "add_member_list": member_list,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
        else:
            return {"success": False, "data": "流程未结束"}

    # 获取账号
    def query_account(self, member_list):
        """
        获取账号
        """
        member_list = AutoAccountApplicationApi.get_account_by_phone(member_list)
        for item in member_list:
            if item.get("ticket_id") and item.get("ticket_status") == "已结单":
                userid = item.get("userid")
                keji_account_flag = item.get("keji_account_flag")
                if userid and keji_account_flag:
                    item["create_account_status"] = "创建成功"
                else:
                    item["create_account_status"] = "创建失败"
        variables = {
            "add_member_list": member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
