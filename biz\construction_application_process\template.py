import uuid
import numpy as np
import pandas as pd
from datetime import datetime

from biz.construction_process.cos_lib import COSLib


# 模板操作
class TemplateAction(object):

    # 判断文件是否存在(单个)
    @staticmethod
    def get_file_exit_flag(file_list, file_label):
        try:
            msg, flag = file_list[0]["response"]["FileList"][0]["url"], True
        except KeyError:
            msg, flag = {
                "code": -1,
                "msg": f"{file_label}未上传完成，请等待{file_label}上传完成后再做结单处理",
            }, False
        except IndexError:
            msg, flag = {
                "code": -1,
                "msg": f"{file_label}未上传完成，请等待{file_label}上传完成后再做结单处理",
            }, False
        except TypeError:
            msg, flag = {
                "code": -1,
                "msg": f"{file_label}未上传完成，请等待{file_label}上传完成后再做结单处理",
            }, False
        return msg, flag

    # 判断文件是否存在(多个)
    @staticmethod
    def get_multiple_files_exit_flag(file_list, file_label):
        missing_values = []
        for i in range(len(file_list)):
            try:
                if not file_list[i]["response"]["FileList"][0]["url"]:
                    missing_values.append(f"第{i + 1}个文件未上传完成")
            except KeyError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
            except IndexError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
            except TypeError:
                missing_values.append(f"第{i + 1}个文件未上传完成")
        if missing_values:
            error = "；<br>".join(missing_values)
            return {
                "code": -1,
                "msg": f"{file_label}未上传完成。<br>错误为{error}。<br>请确保所有文件上传完成后再做结单处理！",
            }, False
        return {"code": 0, "msg": f"{file_label}上传正确"}, True

    # 判断模板是否正确
    @staticmethod
    def get_template_flag(file_list, file_label, template_field):
        # 获取url
        url = file_list[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl")
        df = df.replace({np.nan: None})
        header_list = [
            "序号",
            "工作内容",
            "工期(日历日）",
            "开始时间（年/月/日）",
            "完成时间（年/月/日）",
            "责任人（开通账号人员）",
            "输入物",
            "输出物",
            "施工属性（电气/暖通/弱电/装修（结构）/消防）",
        ]
        # 判断模板字段是否存在
        missing_values = []
        for header in header_list:
            if header not in df.columns:
                missing_values.append((f"缺少列：{header}"))
        if not missing_values:
            for seq_no, work_content in template_field.items():
                if not (
                    (df["序号"] == seq_no) & (df["工作内容"] == work_content)
                ).any():
                    missing_values.append(
                        f"缺少行：序号：{seq_no}，工作内容：{work_content}"
                    )
        if missing_values:
            error = "；<br>".join(missing_values)
            return {
                "code": -1,
                "msg": f"{file_label}上传错误。<br>错误类型：模板错误。<br>错误内容：<br>{error}。<br>请重新上传！",
            }, False
        return {"code": 0, "msg": f"{file_label}上传正确"}, True

    # 校验必填值
    @staticmethod
    def get_file_input_flag(file_list, file_label):
        # 获取url
        url = file_list[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl")
        df = df.replace({np.nan: None})
        if file_label == "项目报建计划":
            df = df.loc[df["序号"].str.startswith("2.4.", na=False), :]
        elif file_label == "市电专项计划":
            df = df.loc[df["序号"].str.startswith("2.9.", na=False), :]
        # 判断填写值是否正确
        missing_values = []
        for _, row in df.iterrows():
            seq_no = str(row["序号"])
            # work_content = row['工作内容']
            construction_time = row["工期(日历日）"]
            start_time = row["开始时间（年/月/日）"]
            finish_time = row["完成时间（年/月/日）"]
            responsible_person = row["责任人（开通账号人员）"]
            if seq_no.count(".") == 3 and construction_time is None:
                missing_values.append(f"序号: {seq_no}, 工期列不能为空")
            if start_time is None:
                missing_values.append(f"序号: {seq_no}, 开始时间不能为空")
            else:
                try:
                    start_time_temp = datetime.strptime(
                        str(start_time), "%Y-%m-%d %H:%M:%S"
                    )
                except ValueError:
                    missing_values.append(
                        f"序号: {seq_no}, 开始时间: {start_time}, 请填写正确日期"
                    )
            if finish_time is None:
                missing_values.append(f"序号: {seq_no}, 完成时间不能为空")
            else:
                try:
                    finish_time_temp = datetime.strptime(
                        str(finish_time), "%Y-%m-%d %H:%M:%S"
                    )
                except ValueError:
                    missing_values.append(
                        f"序号: {seq_no}, 完成时间: {finish_time}, 请填写正确日期"
                    )
            if seq_no.count(".") == 2 and responsible_person is None:
                missing_values.append(f"序号: {seq_no}, 责任人不能为空")
            if seq_no.count(".") == 2 and responsible_person == "Js_xxx":
                missing_values.append(
                    f"序号: {seq_no}, 责任人: {responsible_person}, 请填写正确责任人账号"
                )
        if missing_values:
            error = "；<br>".join(missing_values)
            return {
                "code": -1,
                "msg": f"{file_label}上传错误。<br>错误类型：数据填写错误。<br>错误内容：<br>{error}。<br>请重新上传！",
            }, False
        return {"code": 0, "msg": f"{file_label}上传正确"}, True

    # 多个文件处理-json
    @staticmethod
    def get_file_json(file_list, uploader):
        file_json = []
        for file in file_list:
            file_info = file["response"]["FileList"][0]
            file_json.append(
                {
                    "file_name": file_info["name"],
                    "file_url": file_info["url"],
                    "file_uploader": uploader,
                    "upload_time": datetime.today().strftime("%Y-%m-%d %H:%M:%S"),
                }
            )
        return file_json
