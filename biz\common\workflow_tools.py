import functools
import inspect
import json
import os
import re
import time
from datetime import datetime, timedelta

from iBroker.lib import curl
from iBroker.lib.sdk import gnetops
from iBroker.lib.sdk.flow import is_task_end
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowContinue


def ibroker_service_call(ibroker_name: str, service_name: str, method_name: str, kwargs: dict):
    """ibroker服务调用

    Args:
        ibroker_name (str): ibroker项目名，如: alarm、dcmanage
        service_name (str): __init__.py中注册的服务，类名称
        method_name (str): 类中的方法

    Returns:
        Any: 对应方法的返回值

    """
    if not (ibroker_name and service_name and method_name):
        return
    url = f'http://ibroker-{ibroker_name}:8080/nBroker/api/v1/task'
    reqdata = {
        'args': kwargs,
        'context': {
            'method_name': method_name,
            'service_name': service_name,
        },
    }
    system_name = os.environ.get('PROJECTFLAG')
    resp = curl.post(
        title=f'{ibroker_name}-{service_name}-{method_name}',
        system_name=system_name,
        url=url,
        postdata=json.dumps(reqdata),
        timeout=60,
    )
    response_data = resp.json()

    return response_data


class WorkflowTools:
    """公共的流程类工具"""

    def __init__(self) -> None:
        self.ctx = get_context()

    def workflow_var_combine(self, args_list: list, is_combine: bool = True, to_var: str = 'new_combine_var'):
        """流程变量合并

        Args:
            args_list (list): 流程变量参数名列表
            is_combine (bool): 是否合并. Defaults to True.
            to_var (str, optional): 合并后的流程变量名. Defaults to 'new_combine_var'.

        Returns:
            to_var: 合并后的流程变量，默认为new_combine_var
        """
        if type(args_list) is str:
            args_list = re.split('[ ,;，；+]', args_list)
            args_list = list(filter(bool, args_list))
        var_list = []
        new_combine_var = None
        for args in args_list:
            var_list.append(self.ctx.get_var(args))
        if not is_combine:
            new_combine_var = args_list
        elif all(map(lambda x: type(x) is list, var_list)):
            new_combine_var = []
            for var in var_list:
                new_combine_var.extend(var)
        elif all(map(lambda x: type(x) is dict, var_list)):
            items = []
            for var in var_list:
                items.extend(var.items())
            new_combine_var = dict(items)
        elif all(map(lambda x: type(x) is str, var_list)):
            new_combine_var = ''.join(var_list)
        else:
            new_combine_var = args_list
        WorkflowContinue(task_id=self.ctx.task_id, variables_map={to_var: new_combine_var}).call()
        return new_combine_var

    def workflow_var_rename(self, var_rename_map: dict):
        """流程变量重命名

        Args:
            var_rename_map (dict): 重命名map，{k:v},需要重命名的流程变量，v修改后的流程变量
        """
        variables_map = dict([(v, self.ctx.get_var(k)) for k, v in var_rename_map.items()])
        WorkflowContinue(self.ctx.task_id, variables_map).call()

    def work_flow_var_is_true(self, var: any):
        """判断流程变量是否为空

        Args:
            var (any): 流程变量池里的变量

        Returns:
            is_true: 1 or 0
        """
        is_true = var
        if (type(var) is list or type(var) is tuple) and var:
            is_true = all(var)
        is_true = 1 if bool(is_true) else 0
        WorkflowContinue(task_id=self.ctx.task_id, variables_map={'is_true': is_true}).call()
        return is_true

    def pause_cycle_task(self, task_list: list = None):
        if not task_list:
            task_list = [self.ctx.instance_id]
        res = pause_task(task_list)
        time.sleep(2)
        res.extend(pause_task(task_list))
        WorkflowContinue(task_id=self.ctx.task_id, variables_map={'pause_cycle_task_status': res}).call()

    def force_finish_ticket(self, reason: str, ticket_id_list: list, deal_user: str):
        res = []
        for ticket_id in ticket_id_list:
            status = force_finish_ticket(reason, ticket_id, deal_user)
            res.append(status)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map={'force_finish_ticket_status': res}).call()

    def create_cycle_task_workflow(
        self,
        func: callable,
        span: int,
        limit_count: int = None,
        star_time: str = None,
        end_time: str = None,
        task_name: str = None,
        args: dict = None,
        task_id: str = None,
        instance_id: str = None,
        is_workflow: bool = True,
    ):
        """创建循环任务

        如果
        Args:
            func (callable): 方法命
            span (int): 执行周期
            limit_count (int, optional):最大执行次数, 为空时根据开始时间和结束时间计算. Defaults to None.
            star_time (str, optional): 开始时间. Defaults to None.
            end_time (str, optional): 结束时间. Defaults to None.
            task_name (str, optional): 任务名称. Defaults to None.
            args (dict, optional): 方法对应的参数. Defaults to None.
            task_id (str, optional): 任务id. Defaults to None.
            instance_id (str, optional): 实例id. Defaults to None.
            is_workflow (bool, optional): 是否流转流程节点. Defaults to True.

        Returns:
            dict: 循环任务创建的状态
        """
        res = create_cycle_task(func, span, limit_count, star_time, end_time, task_name, args, task_id, instance_id)
        if is_workflow:
            WorkflowContinue(task_id=self.ctx.task_id, variables_map={'status': res}).call()
        return res

    def gnetops_request(
        action: str,
        method: str,
        data: dict = None,
        ext_data: dict = None,
        timeout: int = 20,
        scheme: str = '',
        is_extract_data: bool = True,
    ):
        """gnetops.request流程节点封装

        参数说明参考gnetop.request说明文档
        Args:
            action (str): _description_
            method (str): _description_
            data (dict, optional): _description_. Defaults to None.
            ext_data (dict, optional): _description_. Defaults to None.
            timeout (int, optional): _description_. Defaults to 20.
            scheme (str, optional): _description_. Defaults to ''.
            is_extract_data (bool, optional): _description_. Defaults to True.
        """
        data = None if not data else data
        ext_data = None if not ext_data else ext_data
        timeout = 20 if not timeout else int(timeout)
        scheme = '' if not scheme else scheme
        is_extract_data = is_extract_data if type(is_extract_data) is bool else True
        gnetops.request(action, method, data, ext_data, timeout, scheme, is_extract_data)


def query_cycle_task_status(task_name: str):
    data = {
        "IsComplete": "false",
        "TaskName": task_name,
    }
    infos = gnetops.request(action="CycleTaskCenter", method="Search", data=data)
    cycle_task_id = []
    if infos:
        for info in infos:
            if not info["IsComplete"]:
                cycle_task_id.append({"Id": info["Id"], "TaskKey": info["TaskKey"]})
    return cycle_task_id


def pause_task(task_list: list):
    if type(task_list) is str:
        task_list = [task_list]
    res = []

    for task_name in task_list:
        data = {
            "IsComplete": "false",
            "TaskName": task_name,
        }
        while True:
            infos = gnetops.request(action="CycleTaskCenter", method="Search", data=data)
            cycle_task_id = []
            if infos:
                for info in infos:
                    if not info["IsComplete"]:
                        cycle_task_id.append({"Id": info["Id"], "TaskKey": info["TaskKey"]})
            else:
                break
            # 输出被暂停的taskid
            for task_id in cycle_task_id:
                ext_data = {"Id": str(task_id['Id'])}
                status = gnetops.request(
                    action="CycleTaskCenter",
                    method="CompleteTask",
                    ext_data=ext_data,
                    timeout=60,
                )
                res.append(status)
    return res


def create_cycle_task(
    func: callable,
    span: int,
    limit_count: int = None,
    star_time: str = None,
    end_time: str = None,
    task_name: str = None,
    args: dict = None,
    task_id: str = None,
    instance_id: str = None,
):
    """创建循环任务

    Args:
        func (callable): 回调的方法名称, 类名.方法名
        span (int): 循环任务的执行间隔
        limit_count (int, optional): 最大循环次数. Defaults to 99999999.
        star_time (str, optional): 开始执行时间. Defaults to datetime.now().
        end_time (str, optional): 结束时间. Defaults to '2099-01-01 00:00:00'.
        task_name (str, optional): 任务名称. Defaults to None.
        args (dict, optional): 回调方法的参数. Defaults to {}.

    """
    project_flag = os.environ.get('PROJECTFLAG', 'ibroker').lower()
    ctx = get_context()
    if not star_time:
        star_time = datetime.now().isoformat(sep=' ', timespec='seconds')
    star_time = datetime.fromisoformat(star_time)
    if not end_time:
        end_time = '2099-01-01 00:00:00'
    end_time = datetime.fromisoformat(end_time)
    if not limit_count:
        limit_count = int((end_time - star_time) / timedelta(seconds=span))
        limit_count = limit_count if limit_count < 99999999 else 9999999
    task_parameter = {
        "context": {
            "service_name": func.__qualname__.split('.')[0],
            "method_name": func.__name__,
            "instanceId": ctx.instance_id if not instance_id else instance_id,  # 当前流程实例id
            "taskId": ctx.task_id if not task_id else task_id,  # 当前任务节点id
        },
        "args": args if args else {},
    }
    ins_id = ctx.instance_id if not instance_id else instance_id
    data = {
        "ExecuteTime": star_time.isoformat(sep=' ', timespec='seconds'),
        "LimitCount": limit_count,  # 最大循环次数
        "Span": span,  # 任务执行之间的时间间隔(s)
        "TaskKey": ctx.task_id if not task_id else task_id,
        "TaskName": f'{task_name}-{ins_id}-{time.time()}' if task_name else f'{func.__name__}-{ins_id}',
        "TaskParameter": json.dumps(task_parameter),
        "Url": f"http://{project_flag}:8080/nBroker/api/v1/task",
    }
    res = gnetops.request(action="CycleTaskCenter", method="AddTask", data=data, timeout=60)
    # res = {'status': add_task_result, 'task_name': data['TaskName']}
    return res


def cycle_task(
    span: int = None,
    limit_count: int = None,
    star_time: str = None,
    end_time: str = None,
    task_name: str = None,
):
    def decorate(func):
        # _span = span if span else func.__module__
        # _star_time = star_time if star_time else func.__module__
        # _end_time = end_time if end_time else func.__module__
        if not span:
            return {"success": True}

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            project_flag = os.environ.get('PROJECTFLAG', 'ibroker').lower()
            ctx = get_context()
            arg_spec = inspect.getfullargspec(func)
            arg_spec_args = arg_spec.args if arg_spec.args else []
            arg_spec_defaults = arg_spec.defaults if arg_spec.defaults else []
            valus = list(args)
            valus.extend(arg_spec_defaults[len(args) - len(arg_spec_args) + len(arg_spec_defaults) :])
            args_dict = dict(zip(arg_spec_args, valus))
            args_dict.update(kwargs)
            args_dict.pop('self')
            task_parameter = {
                "context": {
                    "service_name": func.__qualname__.split('.')[0],
                    "method_name": func.__name__,
                    "instanceId": ctx.instance_id,  # 当前流程实例id
                    "taskId": ctx.task_id,  # 当前任务节点id
                },
                "args": args_dict,
            }
            _span, _star_time, _end_time, _limit_count = (
                span,
                star_time,
                end_time,
                limit_count,
            )
            if not star_time:
                _star_time = datetime.now().isoformat(sep=' ', timespec='seconds')
            _star_time = datetime.fromisoformat(_star_time)
            if not end_time:
                _end_time = '2099-01-01 00:00:00'
            _end_time = datetime.fromisoformat(_end_time)
            if not limit_count:
                _limit_count = int((_end_time - _star_time) / timedelta(seconds=_span))
                _limit_count = _limit_count if _limit_count < 99999999 else 9999999
            ins_id = ctx.instance_id
            data = {
                "ExecuteTime": _star_time.isoformat(sep=' ', timespec='seconds'),
                "LimitCount": _limit_count,  # 最大循环次数
                "Span": _span,  # 任务执行之间的时间间隔(s)
                "TaskKey": ctx.task_id,
                "TaskName": f'{task_name}-{ins_id}-{time.time()}' if task_name else f'{func.__name__}-{ins_id}',
                "TaskParameter": json.dumps(task_parameter),
                "Url": f"http://{project_flag}:8080/nBroker/api/v1/task",
            }
            add_task_result = gnetops.request(action="CycleTaskCenter", method="AddTask", data=data)
            res = {'status': add_task_result, 'task_name': data['TaskName']}
            return res

        return wrapper

    return decorate


def cycle_task_abort(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        env_flag = os.environ.get('ENVFLAG', None)
        ctx = get_context()
        if env_flag != 'devcloud' and is_task_end(task_id=ctx.task_id):
            return {'success': True}
        return func(*args, **kwargs)

    return wrapper


def task_runtime(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        _ts = time.time()
        res = func(*args, **kwargs)
        _te = time.time()
        print(f'{func} runtime: {_te - _ts}')
        return res

    return wrapper


def force_finish_ticket(reason: str, ticket_id: str, deal_user: str):
    return gnetops.request(
        action='Ticket',
        method='ForceFinsh',
        data={"Reason": reason, "TicketId": ticket_id},
        timeout=120,
        ext_data={'VueOperateUser': deal_user},
    )


def create_ticket(flow_key: str, description: str, ticket_level: str, title: str, creator: str, concern: str = "",
                  deal: str = "", source: str = "", custom_var: dict = None, timeout: int = 20):
    """

    :param flow_key: 流程key
    :param description: 工单描述
    :param ticket_level: 工单等级1,2,3
    :param title: 标题
    :param creator: 创建者
    :param concern: 关注人 ;号分割
    :param deal: 处理人;号分割
    :param source: 来源
    :param custom_var: 自定义输入变量
    :return:工单号
    """
    data = {
        "ProcessDefinitionKey": flow_key,
        "Source": source,
        "TicketDescription": description,
        "TicketLevel": ticket_level,
        "TicketTitle": title,
        "UserInfo": {
            "Concern": concern,
            "Creator": creator,
            "Deal": deal
        }
    }
    if custom_var:
        data["CustomVariables"] = custom_var
    return gnetops.request(action="Ticket", method="Create", data=data, timeout=timeout)
