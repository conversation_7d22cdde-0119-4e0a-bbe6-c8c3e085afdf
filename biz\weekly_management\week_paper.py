import datetime

# import json
import re
import uuid

import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib
from biz.construction_final_acceptance.tools import Tools

#
"""
    旧周报
"""


def build_tree_with_edit_flag(data_list):
    """
    将平面列表数据转换为树状结构，并添加allowEdit字段
    参数:
        data_list: 包含字典的列表，每个字典代表一个节点
    返回:
        带有allowEdit标志的树状结构列表
    """
    # 创建节点字典，方便查找
    nodes = {item["serial_number"]: item for item in data_list}

    # 初始化树结构
    tree = []

    for item in data_list:
        serial = item["serial_number"]
        parts = serial.split(".")
        # item["id"] = serial
        # 判断是否为最内层节点（没有子节点的节点）
        is_leaf = True
        for other_item in data_list:
            if other_item["serial_number"].startswith(serial + "."):
                is_leaf = False
                break

        # 设置allowEdit字段
        item["allowEdit"] = is_leaf

        # 一级节点直接添加到树中
        if len(parts) == 1:
            item["children"] = []
            tree.append(item)
        else:
            # 查找父节点
            parent_serial = ".".join(parts[:-1])
            parent = nodes.get(parent_serial)

            if parent:
                # 确保父节点有children字段
                if "children" not in parent:
                    parent["children"] = []
                # 将当前节点添加到父节点的children中
                item["children"] = []
                parent["children"].append(item)

    return tree


def is_diff(item, otem):
    """
    判断是否不同
    """
    if item.get("actual_finish_time") != otem.get("actual_finish_time"):
        return True
    elif item.get("actual_start_time") != otem.get("actual_start_time"):
        return True
    elif item.get("state") != otem.get("state"):
        return True
    elif item.get("week_progress") != otem.get("week_progress"):
        return True
    else:
        return False


def to_float_or_zero(element):
    """
    将元素安全转换为float类型

    参数:
        element: 输入元素(可能是int, float, str等类型)

    返回:
        float: 转换成功的float值，或转换失败时的0.0
    """
    try:
        # 尝试将元素转换为float
        return float(element)
    except (ValueError, TypeError):
        # 如果转换失败(如字符串不是数字格式，或其他不支持的类型)
        return 0.0


def flatten_tree_to_list(tree_data, children_key="children"):
    """
    将树状结构完全展平为列表结构

    参数:
        tree_data (dict/list): 树状结构数据，可以是字典或列表
        children_key (str): 标识子节点的键名，默认为'children'

    返回:
        list: 完全展平后的列表，包含所有节点数据
    """
    result = []

    # 处理空输入
    if not tree_data:
        return result

    # 处理输入是列表的情况（多个根节点）
    if isinstance(tree_data, list):
        for node in tree_data:
            result.extend(flatten_tree_to_list(node, children_key))
        return result

    # 复制当前节点数据，避免修改原始数据
    node_data = tree_data.copy()

    # 移除子节点键
    children = node_data.pop(children_key, [])

    # 添加当前节点到结果列表
    result.append(node_data)

    # 递归处理所有子节点
    for child in children:
        result.extend(flatten_tree_to_list(child, children_key))

    return result


def calculate_tree_progress(tree_data):
    """
    递归计算树状结构中各节点的week_progress
    参数:
        tree_data: 树状结构数据
    返回:
        计算完成后的树状结构
    """
    for node in tree_data:
        # 递归处理子节点
        if "children" in node and node["children"]:
            calculate_tree_progress(node["children"])

            # 判断节点层级
            level = len(node["serial_number"].split("."))
            if level == 1:  # 第一层节点
                if (
                    sum(child["second_level_proportion"] for child in node["children"])
                    == 1
                ):
                    # 加权计算
                    total = sum(
                        to_float_or_zero(child["week_progress"])
                        * child["second_level_proportion"]
                        for child in node["children"]
                    )
                else:
                    # 默认求平均
                    total = sum(
                        child["week_progress"] for child in node["children"]
                    ) / len(node["children"])
                node["week_progress"] = round(total, 2)

            elif level == 2:  # 第二层节点
                sum_child = sum(
                    child["third_level_proportion"] for child in node["children"]
                )
                if sum_child == 1:
                    # 加权计算
                    total = sum(
                        child["week_progress"] * child["third_level_proportion"]
                        for child in node["children"]
                    )
                    node["week_progress"] = round(total, 2)
                else:
                    # 默认求平均
                    total = sum(
                        child["week_progress"] for child in node["children"]
                    ) / len(node["children"])
                    node["week_progress"] = round(total, 2)
                    node["week_progress"] = round(total, 2)

            else:  # 第三层及以下节点
                # 直接求平均
                sum_num = sum(
                    child["week_progress"] for child in node["children"]
                ) / len(node["children"])
                node["week_progress"] = round(sum_num, 2)

    return tree_data


def calculate_actual_times(tree_data):
    """
    递归计算树状结构中各节点的实际开始和完成时间
    参数:
        tree_data: 树状结构数据
    返回:
        计算完成后的树状结构
    """
    for node in tree_data:
        # 递归处理子节点
        if "children" in node and node["children"]:
            calculate_actual_times(node["children"])

            # 收集子节点的时间数据
            child_start_times = []
            child_finish_times = []
            has_unfinished_child = False

            for child in node["children"]:
                if "actual_start_time" in child and child["actual_start_time"]:
                    child_start_times.append(child["actual_start_time"])
                if "actual_finish_time" not in child:
                    child["actual_finish_time"] = None
                if not child["actual_finish_time"] or child["actual_finish_time"] == "":
                    has_unfinished_child = True
                elif child["actual_finish_time"]:
                    child_finish_times.append(child["actual_finish_time"])
                child["week_progress"] = to_float_or_zero(child["week_progress"])
                child["first_level_proportion"] = to_float_or_zero(
                    child["first_level_proportion"]
                )
                child["second_level_proportion"] = to_float_or_zero(
                    child["second_level_proportion"]
                )
                child["third_level_proportion"] = to_float_or_zero(
                    child["third_level_proportion"]
                )
            # 计算父节点的actual_start_time（取子节点最早时间）
            if child_start_times:
                node["actual_start_time"] = min(child_start_times)
            else:
                node["actual_start_time"] = None

            # 计算父节点的actual_finish_time
            if has_unfinished_child:
                # 如果有子节点未完成，父节点也标记为未完成
                node["actual_finish_time"] = None
            elif child_finish_times:
                # 所有子节点都已完成，取最晚完成时间
                node["actual_finish_time"] = max(child_finish_times)
            else:
                node["actual_finish_time"] = None

    return tree_data


class ModifyDataTableUsingWeeklyReportData(object):
    """
    excel表解析并且数据存库
    """

    def judgment_modification(self, project_name, data):
        """
        判断修改数据
        """
        weekly_newspaper_sql = f"""
            SELECT serial_number,work_content,week_progress,project_name 
            FROM construct_plan_data WHERE project_name = "{project_name}"
        """
        db = mysql.new_mysql_instance("tbconstruct")
        weekly_newspaper_result = db.get_all(weekly_newspaper_sql)

        if weekly_newspaper_result:
            for row in data:
                for item in weekly_newspaper_result:
                    # 判断data和weekly_newspaper_result的serial_number和work_content是否一致
                    # 将两边的serial_number都转换为字符串再进行比较
                    row_serial = (
                        str(row["serial_number"])
                        if row["serial_number"] is not None
                        else ""
                    )
                    item_serial = (
                        str(item["serial_number"])
                        if item["serial_number"] is not None
                        else ""
                    )

                    # 判断data和weekly_newspaper_result的serial_number和work_content是否一致
                    if (
                        row_serial == item_serial
                        and row["task_name"] == item["work_content"]
                    ):
                        try:
                            update_data = {
                                "week_progress": round(row["third_progress"] * 100, 2),
                                "actual_start_time": row["actual_start_time"],
                                "actual_finish_time": row["actual_finish_time"],
                            }
                            # 添加更精确的更新条件，避免误更新
                            conditions = {
                                "project_name": project_name,
                                "serial_number": row["serial_number"],
                                "work_content": row["task_name"],
                            }

                            # 执行更新
                            db.update("construct_plan_data", update_data, conditions)
                            db.commit()

                        except FileNotFoundError as e:
                            return {"code": -1, "msg": f"更新失败：{e}"}

            return {"code": 200, "msg": f"更新成功"}
        else:
            return {"code": -1, "msg": "项目在表中未查询"}


class WeekPaperCommit(AjaxTodoBase):
    """
    建设周报提交代办类
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        ticketid = self.ctx.variables.get("ticketid")
        now_time = self.ctx.variables.get("now_time")
        weekly_newspaper = process_data.get("weekly_newspaper", "")
        insert_data = {}
        if (
            weekly_newspaper
            and len(weekly_newspaper) > 0
            and "response" in weekly_newspaper[0]
        ):
            insert_data = {
                "weekly_newspaper": "url："
                + weekly_newspaper[0]["response"]["FileList"][0]["url"]
                + "；"
                + "now_time："
                + str(now_time),
                "ticket_id": ticket_id,
                "ticketid": ticketid,
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("week_create_info_url", insert_data)
        tb_db.commit()
        variables = {"weekly_newspaper": weekly_newspaper}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WeekRepository(object):
    """
    excel表解析并且数据存库
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def parse_store(self, weekly_newspaper):
        """
        cos下载电力走廊分项计划
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        ticketid = self.ctx.variables.get("ticketid")
        project_name = self.ctx.variables.get("project_name")
        now_time = self.ctx.variables.get("now_time")
        url = weekly_newspaper[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel周报-周报简报解析并存储数据
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl", sheet_name="周报简报")
        df = df.replace({pd.np.nan: None})
        zbjb_data = []
        for _, row in df.iterrows():
            table_of_contents = row.get("目录")
            details = row.get("具体内容")
            if any([table_of_contents, details]):
                zbjb_data.append(
                    {
                        "ticket_id": ticket_id,
                        "table_of_contents": table_of_contents,
                        "details": details,
                        "ticketid": ticketid,
                        "now_time": now_time,
                    }
                )
        """
            excel周报-进度管理解析并存储数据
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl", sheet_name="进度管理")
        df = df.replace({pd.np.nan: None})
        jdgl_data = []
        for _, row in df.iterrows():
            serial_number = row.get("序号")
            task_name = row.get("工作内容")
            plan_start_time = row.get("开始时间（年/月/日）")
            plan_finish_time = row.get("完成时间（年/月/日）")
            actual_start_time = row.get("实际开始时间")
            actual_finish_time = row.get("实际完成时间")
            completion_week = row.get("本周完成情况")
            status = row.get("状态（正常/预警/滞后）")
            existing_problems = row.get("存在问题")
            solution = row.get("解决措施")
            work_plan_next_week = row.get("下周工作计划")
            first_level_proportion = row.get("一级占比")
            second_level_proportion = row.get("二级占比")
            third_level_proportion = row.get("三级占比")
            third_progress = row.get("当前进展")

            if not isinstance(first_level_proportion, (float, int)):
                first_level_proportion = 0
            if not isinstance(second_level_proportion, (float, int)):
                second_level_proportion = 0
            if not isinstance(third_level_proportion, (float, int)):
                third_level_proportion = 0
            if not isinstance(third_progress, (float, int)):
                third_progress = 0

            if isinstance(plan_start_time, datetime.datetime):
                plan_start_time = plan_start_time.strftime("%Y-%m-%d")
            else:
                plan_start_time = None

            if isinstance(plan_finish_time, datetime.datetime):
                plan_finish_time = plan_finish_time.strftime("%Y-%m-%d")
            else:
                plan_finish_time = None

            if isinstance(actual_start_time, datetime.datetime):
                actual_start_time = actual_start_time.strftime("%Y-%m-%d")
            else:
                actual_start_time = None

            if isinstance(actual_finish_time, datetime.datetime):
                actual_finish_time = actual_finish_time.strftime("%Y-%m-%d")
            else:
                actual_finish_time = None

            if any(
                [
                    task_name,
                    serial_number,
                    plan_start_time,
                    plan_finish_time,
                    actual_start_time,
                    actual_finish_time,
                    completion_week,
                    existing_problems,
                    solution,
                    work_plan_next_week,
                    first_level_proportion,
                    second_level_proportion,
                    third_level_proportion,
                    third_progress,
                ]
            ):
                if not isinstance(first_level_proportion, (float, int)):
                    first_level_proportion = 0
                if not isinstance(second_level_proportion, (float, int)):
                    second_level_proportion = 0
                if not isinstance(third_level_proportion, (float, int)):
                    third_level_proportion = 0
                if not isinstance(third_progress, (float, int)):
                    third_progress = 0
                jdgl_data.append(
                    {
                        "ticket_id": ticket_id,
                        "ticketid": ticketid,
                        "task_name": task_name,
                        "serial_number": serial_number,
                        "plan_start_time": plan_start_time,
                        "plan_finish_time": plan_finish_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "completion_week": completion_week,
                        "status": status,
                        "existing_problems": existing_problems,
                        "solution": solution,
                        "work_plan_next_week": work_plan_next_week,
                        "first_level_proportion": first_level_proportion,
                        "second_level_proportion": second_level_proportion,
                        "third_level_proportion": third_level_proportion,
                        "third_progress": third_progress,
                        "now_time": now_time,
                    }
                )
        modifier = ModifyDataTableUsingWeeklyReportData()
        weekly = modifier.judgment_modification(project_name, jdgl_data)
        """
            excel周报-资料管理解析并存储数据
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl", sheet_name="资料管理")
        df = df.replace({pd.np.nan: None})
        zlgl_data = []
        for _, row in df.iterrows():
            stage = row.get("阶段")
            serial_number = row.get("序号")
            two_directory = row.get("二级目录")
            three_directory = row.get("三级目录")
            archiving_situation = row.get("本周归档情况说明")
            if any(
                [
                    serial_number,
                    stage,
                    two_directory,
                    three_directory,
                    archiving_situation,
                ]
            ):
                zlgl_data.append(
                    {
                        "ticket_id": ticket_id,
                        "ticketid": ticketid,
                        "stage": stage,
                        "serial_number": serial_number,
                        "two_directory": two_directory,
                        "three_directory": three_directory,
                        "archiving_situation": archiving_situation,
                    }
                )
        """
            excel周报-人力资源管理解析并存储数据
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl", sheet_name="人力资源管理")
        df = df.replace({pd.np.nan: None})
        rlzy_data = []
        for _, row in df.iterrows():
            serial_number = row.get("序号")
            construction_unit = row.get("施工单位")
            engineering_major = row.get("工程专业")
            this_week_actual_input_days = row.get("本周实际投入人天")
            this_week_planned_input_days = row.get("本周计划投入人天")
            manpower_deviation = row.get("人力偏差")
            progress_influence = row.get("进度影响")
            responsible_person = row.get("责任人")
            solving_measures = row.get("解决措施")
            processing_result = row.get("处理结果")
            next_week_planned_input_days = row.get("下周计划投入人天")
            if any(
                [
                    serial_number,
                    construction_unit,
                    engineering_major,
                    this_week_actual_input_days,
                    this_week_planned_input_days,
                    manpower_deviation,
                    progress_influence,
                    responsible_person,
                    solving_measures,
                    processing_result,
                    next_week_planned_input_days,
                ]
            ):
                rlzy_data.append(
                    {
                        "ticket_id": ticket_id,
                        "ticketid": ticketid,
                        "serial_number": serial_number,
                        "construction_unit": construction_unit,
                        "engineering_major": engineering_major,
                        "this_week_actual_input_days": this_week_actual_input_days,
                        "this_week_planned_input_days": this_week_planned_input_days,
                        "manpower_deviation": manpower_deviation,
                        "progress_influence": progress_influence,
                        "responsible_person": responsible_person,
                        "solving_measures": solving_measures,
                        "processing_result": processing_result,
                        "next_week_planned_input_days": next_week_planned_input_days,
                        "now_time": now_time,
                    }
                )
        if zbjb_data and jdgl_data and zlgl_data and rlzy_data:
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch(
                "weekly_rief_report", [data for data in zbjb_data if data is not None]
            )
            tb_db.insert_batch(
                "schedule_management", [data for data in jdgl_data if data is not None]
            )
            tb_db.insert_batch(
                "data_management", [data for data in zlgl_data if data is not None]
            )
            tb_db.insert_batch(
                "personnel_resource_management",
                [data for data in rlzy_data if data is not None],
            )
            tb_db.commit()

        variables = {
            "zbjb_data": zbjb_data,
            "jdgl_data": jdgl_data,
            "zlgl_data": zlgl_data,
            "rlzy_data": rlzy_data,
            "weekly": weekly,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WeekPaper(object):
    """
    建设周报流程
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def week_paper_generate(self):
        db = mysql.new_mysql_instance("tbconstruct")
        week_paper_info = db.get_list(
            "week_create_info",
            fields=["week_date", "ticketid", "project_name"],
            conditions={"status": "0"},
        )
        res_list = []
        processed_project_names = set()  # 用于跟踪已处理的 project_name
        for i in week_paper_info:
            # 获取daily_paper_info列表中的发送周报日期
            week_date = i.get("week_date")
            project_name = i.get("project_name")
            # 获取当前日期和时间
            now = datetime.datetime.now()
            # 获取当前是星期几（星期一为0，星期日为6）
            weekday = now.weekday()
            # 将星期几转换为对应的字符串
            weekday_str = [
                "星期一",
                "星期二",
                "星期三",
                "星期四",
                "星期五",
                "星期六",
                "星期日",
            ][weekday]
            # 判断当前日期是否符合发送周报日期
            if week_date == weekday_str and project_name not in processed_project_names:
                results = 0
            else:
                results = 1

            if results == 0:
                year = now.year
                month = now.month
                day = now.day

                date_string = f"{year}年{month}月{day}号"
                processTitle = date_string + f"{project_name}建设周报"
                data = {
                    "CustomVariables": {
                        "project_name": i.get("project_name"),
                    },
                    "ProcessDefinitionKey": "weekly_report_process",
                    "Source": "",
                    "TicketDescription": "建设周报流程",
                    "TicketLevel": "3",
                    "TicketTitle": processTitle,
                    "UserInfo": {
                        "Concern": "keketan;v_mmywang",
                        "Creator": "v_mmywang",
                        "Deal": "v_mmywang"
                    }
                }
                # 起单，并抛入data
                res = gnetops.request(action="Ticket", method="Create", data=data)
                res_list.append(res)
                processed_project_names.add(project_name)
        variables = {
            'res_list': res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def find_a_project_leader(self):
        """
        获取项目周报处理人
        """
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = (
            "SELECT project_name, role, account "
            "FROM project_role_account "
            f"WHERE project_name = '{project_name}'"
            f"and del_flag = 0"
        )
        project_result = db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in [
                "集成商-项目经理",
                "项管-项目经理",
                "监理-总监理工程师",
                "合建方-项目经理",
                "总包-项目经理",
            ]:
                if row["role"] and role in row["role"]:
                    account_dict[role] = row["account"]
        integrator = account_dict.get("集成商-项目经理")
        if not integrator:
            integrator = account_dict.get("总包-项目经理")
        item_tube = account_dict.get("项管-项目经理")
        if not item_tube:
            item_tube = account_dict.get("合建方-项目经理")
        supervision_management = account_dict.get("监理-总监理工程师")

        variables = {
            "integrator": integrator,
            "item_tube": item_tube,
            "supervision_management": supervision_management,
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def time_disposal(self, jdgl_table):
        """
        时间处理
        """
        data = []
        for row in jdgl_table:
            plan_start_time = row.get("plan_start_time")
            plan_finish_time = row.get("plan_finish_time")
            actual_start_time = row.get("actual_start_time")
            actual_finish_time = row.get("actual_finish_time")

            # plan_start_time
            if plan_start_time:
                delta = datetime.timedelta(days=int(plan_start_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                plan_start_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                plan_start_time = None

            # plan_finish_time
            if plan_finish_time:
                delta = datetime.timedelta(days=int(plan_finish_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                plan_finish_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                plan_finish_time = None

            # actual_start_time
            if actual_start_time:
                delta = datetime.timedelta(days=int(actual_start_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                actual_start_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                actual_start_time = None

            # actual_finish_time
            if actual_finish_time:
                delta = datetime.timedelta(days=int(actual_finish_time))
                today = datetime.datetime.strptime("1899-12-30", "%Y-%m-%d") + delta
                actual_finish_time = datetime.datetime.strftime(today, "%Y-%m-%d")
            else:
                actual_finish_time = None

            data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "task_name": row.get("task_name"),
                    "plan_start_time": plan_start_time,
                    "plan_finish_time": plan_finish_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "completion_week": row.get("completion_week"),
                    "third_progress": row.get("third_progress"),
                    "status": row.get("status"),
                    "existing_problems": row.get("existing_problems"),
                    "solution": row.get("solution"),
                    "work_plan_next_week": row.get("work_plan_next_week"),
                    "first_level_proportion": row.get("first_level_proportion"),
                    "second_level_proportion": row.get("second_level_proportion"),
                    "third_level_proportion": row.get("third_level_proportion"),
                }
            )
        return data


class ModifiedByWeeklyReport(object):
    """
    通过周报修改数据
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_modification(self):
        """
        数据修改
        """
        zb_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = (
            "SELECT sm.serial_number, sm.task_name, sm.plan_start_time, sm.plan_finish_time, "
            "sm.actual_start_time, sm.actual_finish_time, sm.now_time, wci.project_name "
            "FROM schedule_management sm "
            "JOIN week_create_info wci ON wci.ticketid = sm.ticketid "
            "WHERE (sm.ticketid, sm.now_time) "
            "IN ( SELECT ticketid, MAX(now_time) "
            "FROM schedule_management "
            "GROUP BY ticketid )"
        )

        # 如果在 week_create_info 表中找不到 project_name，则从 risk_early_warning_data 表中查找
        query_sql_risk = (
            "SELECT sm.serial_number, sm.task_name, sm.plan_start_time, sm.plan_finish_time, "
            "sm.actual_start_time, sm.actual_finish_time, sm.now_time, rew.project_name "
            "FROM schedule_management sm "
            "JOIN risk_early_warning_data rew ON rew.dcopsTicketId = sm.ticketid "
            "WHERE (sm.ticketid, sm.now_time) "
            "IN ( SELECT ticketid, MAX(now_time) "
            "FROM schedule_management "
            "GROUP BY ticketid )"
        )

        risk_sql = (
            "SELECT project_name, demand_distribution FROM risk_early_warning_data"
        )
        project_finish_times = {}
        summary_data = {}
        # 列表合并
        problem_list = list(db.get_all(query_sql)) + list(db.get_all(query_sql_risk))
        for row in problem_list:
            serial_number = row.get("serial_number")
            task_name = row.get("task_name")
            plan_start_time = row.get("plan_start_time")
            plan_finish_time = row.get("plan_finish_time")
            actual_start_time = row.get("actual_start_time")
            actual_finish_time = row.get("actual_finish_time")
            now_time = row.get("now_time")
            project_name = row.get("project_name")

            if plan_start_time is not None:
                plan_start_time = plan_start_time.strftime("%Y-%m-%d")

            if plan_finish_time is not None:
                plan_finish_time = plan_finish_time.strftime("%Y-%m-%d")

            if actual_start_time is not None:
                actual_start_time = actual_start_time.strftime("%Y-%m-%d")

            if actual_finish_time is not None:
                actual_finish_time = actual_finish_time.strftime("%Y-%m-%d")

            if now_time is not None:
                now_time = now_time.strftime("%Y-%m-%d")

            zb_list.append(
                {
                    "serial_number": serial_number,
                    "task_name": task_name,
                    "plan_start_time": plan_start_time,
                    "plan_finish_time": plan_finish_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "now_time": now_time,
                    "project_name": project_name,
                }
            )
        if zb_list:
            for row in zb_list:
                serial_number = row.get("serial_number")
                task_name = row.get("task_name")
                actual_start_time = row.get("actual_start_time")
                actual_finish_time = row.get("actual_finish_time")
                project_name = row.get("project_name")

                match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
                if match:
                    campus = match.group(1)
                    project = match.group(2)
                else:
                    campus = project_name
                    project = project_name

                conditions = {"campus": campus, "project_name": project}

                if serial_number == "3" and task_name == "安装施工":
                    data = {}
                    if actual_start_time:
                        data["ConstructionStartTime"] = actual_start_time
                    if actual_finish_time:
                        data["ConstructionEndTime"] = actual_finish_time

                    # 只有在 data 字典中有数据时才进行更新
                    if data:
                        db.update("summary_data", data, conditions)
                elif serial_number == "4" and task_name == "测试验证":
                    if actual_finish_time:
                        data = {"TestEndTime": actual_finish_time}
                        db.update("summary_data", data, conditions)
                elif serial_number == "5.2" and task_name == "初验":
                    if actual_finish_time:
                        data = {"InspectionEndTime": actual_finish_time}
                        db.update("summary_data", data, conditions)
                elif serial_number == "1" and task_name == "项目启动":
                    if actual_finish_time:
                        data = {"ProjectStartTime": actual_finish_time}
                        db.update("summary_data", data, conditions)
                if serial_number is not None:

                    # 拆分 serial_number
                    parts = serial_number.split(".")

                    # 确保有三个部分并且可以转换为整数
                    if len(parts) == 3:
                        major = int(parts[0])
                        minor = int(parts[1])
                        patch = int(parts[2])

                        # 只处理在 2.5.1 到 2.5.8 范围内的版本
                        if major == 2 and minor == 5 and 1 <= patch <= 8:
                            if actual_finish_time:
                                key = project_name
                                if key not in project_finish_times:
                                    project_finish_times[key] = []
                                project_finish_times[key].append(actual_finish_time)

            # 处理收集到的结束时间，确保每个项目只更新一次summary_data
            for project_name, finish_times in project_finish_times.items():
                if finish_times:
                    max_finish_time = max(finish_times)
                    match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
                    if match:
                        campus = match.group(1)
                        project = match.group(2)
                    else:
                        campus = project_name
                        project = project_name
                    conditions_jg = {"campus": campus, "project_name": project}
                    if project_name not in summary_data:
                        summary_data[project_name] = {"SupplyEndTime": max_finish_time}
                    db.update("summary_data", summary_data[project_name], conditions_jg)

            # 处理 risk_sql 数据
            risk_data_list = db.get_all(risk_sql)
            for risk_row in risk_data_list:
                project_name = risk_row.get("project_name")
                demand_distribution = risk_row.get("demand_distribution")
                match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project_name)
                if match:
                    campus = match.group(1)
                    project = match.group(2)
                else:
                    campus = project_name
                    project = project_name
                risk_conditions = {"campus": campus, "project_name": project}
                # 根据需求分配更新 risk_early_warning_data 表
                if demand_distribution:
                    risk_data = {"RequestAcceptanceTime": demand_distribution}
                    db.update("summary_data", risk_data, risk_conditions)

            db.commit()

        variables = {"zb_list": zb_list}

        flow.complete_task(self.ctx.task_id, variables=variables)


"""
    新周报
"""

class GetWeeklyReport(object):
    """
    获取周报
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def inquire_weekly_report(self):
        """
        查询周报
        """
        jb_list = []
        project_name = self.ctx.variables.get("project_name")
        # project_name = "仪征东升B7-2"
        db = mysql.new_mysql_instance("tbconstruct")
        # 周报简报-周报
        weekly_report_brief_sql = f"""
            SELECT weekly_report_cycle, current_stage,overall_image_progress, state, arrival_equipment, 
            project_progress, week_completion, next_week_work_plan, safety_management, quality_control, 
            construction_personnel_input, follow_last_week_key_issues, issues_risks_week, 
            solution_measure FROM weekly_report_brief WHERE project_name = '{project_name}'
        """
        weekly_report_brief_result = db.get_all(weekly_report_brief_sql)
        now_time = datetime.datetime.now().strftime("%Y-%m-%d")
        overall_image_progress = "0%"
        for item in weekly_report_brief_result:
            weekly_report_cycle = item.get("weekly_report_cycle")
            current_stage = item.get("current_stage")
            overall_image_progress = item.get("overall_image_progress")
            state = item.get("state")
            arrival_equipment = item.get("arrival_equipment")
            project_progress = item.get("project_progress")
            week_completion = item.get("week_completion")
            next_week_work_plan = item.get("next_week_work_plan")
            safety_management = item.get("safety_management")
            quality_control = item.get("quality_control")
            construction_personnel_input = item.get("construction_personnel_input")
            follow_last_week_key_issues = item.get("follow_last_week_key_issues")
            issues_risks_week = item.get("issues_risks_week")
            solution_measure = item.get("solution_measure")

            jb_list = [
                {
                    "now_time": now_time,
                    "table_of_contents": "周报周期",
                    "details": weekly_report_cycle,
                },
                {"table_of_contents": "当前阶段", "details": current_stage},
                {
                    "table_of_contents": "整体形象进度",
                    "details": overall_image_progress,
                },
                {"table_of_contents": "状态", "details": state},
                {"table_of_contents": "设备到货", "details": arrival_equipment},
                {"table_of_contents": "工程进度", "details": project_progress},
                {"table_of_contents": "本周完成情况", "details": week_completion},
                {"table_of_contents": "下周工作计划", "details": next_week_work_plan},
                {"table_of_contents": "安全管理", "details": safety_management},
                {"table_of_contents": "质量管理", "details": quality_control},
                {
                    "table_of_contents": "施工人员投入",
                    "details": construction_personnel_input,
                },
                {
                    "table_of_contents": "上周重点问题跟踪情况",
                    "details": follow_last_week_key_issues,
                },
                {"table_of_contents": "本周问题及风险", "details": issues_risks_week},
                {"table_of_contents": "解决措施/求助", "details": solution_measure},
            ]

        # 进度管理-周报
        weekly_progress_management_report_sql = f"""
            SELECT id, serial_number, work_content, start_time, completion_time, actual_start_time, actual_finish_time, 
            week_progress, state, first_level_proportion, second_level_proportion, third_level_proportion
            FROM construct_plan_data
            WHERE project_name = '{project_name}' AND not_involved = 0
        """
        weekly_progress_management_report_result = db.get_all(
            weekly_progress_management_report_sql
        )
        jd_list = []
        if weekly_progress_management_report_result:
            for row in weekly_progress_management_report_result:
                serial_number = row.get("serial_number")
                work_content = row.get("work_content")
                start_time = row.get("start_time")
                completion_time = row.get("completion_time")
                actual_start_time = row.get("actual_start_time")
                actual_finish_time = row.get("actual_finish_time")
                week_progress = row.get("week_progress")
                state = row.get("state")
                first_level_proportion = row.get("first_level_proportion")
                second_level_proportion = row.get("second_level_proportion")
                third_level_proportion = row.get("third_level_proportion")

                jd_list.append(
                    {
                        "id": row.get("id"),
                        "serial_number": serial_number,
                        "work_content": work_content,
                        "start_time": start_time,
                        "completion_time": completion_time,
                        "actual_start_time": actual_start_time,
                        "actual_finish_time": actual_finish_time,
                        "week_progress": week_progress,
                        "state": state,
                        "first_level_proportion": first_level_proportion,
                        "second_level_proportion": second_level_proportion,
                        "third_level_proportion": third_level_proportion,
                    }
                )
        else:
            raise Exception("未找到进度管理数据")
        jd_list = build_tree_with_edit_flag(jd_list)
        # cal = calculate_tree_progress(jd_list)
        # tim = calculate_actual_times(cal)
        # all_progress = 0
        # for row in tim:
        #     all_progress += row.get("week_progress") * row.get("first_level_proportion")
        # all_progress = round(all_progress, 2)
        # list_data = flatten_tree_to_list(tim)
        # print(json.dumps(tim[0], ensure_ascii=False))
        # print(json.dumps(jd_list, ensure_ascii=False))
        flow.complete_task(
            self.ctx.task_id, 
            variables={
                "jd_list": jd_list, 
                "jb_list": jb_list, 
                "old_jd_list":jd_list, 
                "old_progress":overall_image_progress
            }
        )

    def calculate_weekly_report(self, jd_list):
        """
        计算周报
        """
        jd_list_new = calculate_actual_times(jd_list)
        jd_list_new = calculate_tree_progress(jd_list_new)
        all_progress = 0
        first_level_proportion_sum = sum(child["first_level_proportion"] for child in jd_list_new)
        if first_level_proportion_sum == 1:
            for row in jd_list_new:
                all_progress += row.get("week_progress") * row.get("first_level_proportion")
        else:
            for row in jd_list_new:
                all_progress += row.get("week_progress")
            all_progress = all_progress/len(jd_list_new)
        all_progress = round(all_progress, 2)
        return {"all_progress": all_progress, "jd_list": jd_list_new}


class SubmitWeeklyReport(AjaxTodoBase):
    """
    提交周报
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()
    
    def save(self, process_data, process_user):
        new_jd_list = process_data.get("jd_list")  # 页面填写的新进度管理-周报信息
        new_jb_list = process_data.get("jb_list")  # 页面填写的新周报简报-周报信息
        pictures_submit = process_data.get("pictures_submit")
        flow.set_variables(
            self.ctx.instance_id, 
            variables_kv={
                "jd_list": new_jd_list,
                "jb_list": new_jb_list,
                "pictures_submit": pictures_submit,
            },
        )

    def end(self, process_data, process_user):
        state_list = ["正常", "预警", "滞后"]
        new_jd_list = process_data.get("jd_list")  # 页面填写的新进度管理-周报信息
        new_jb_list = process_data.get("jb_list")  # 页面填写的新周报简报-周报信息
        pictures_submit = process_data.get("pictures_submit")
        for row in new_jb_list:
            if row.get("table_of_contents") == "状态":
                if row.get("details") not in state_list:
                    return {
                        "code": -1,
                        "msg": "状态填写错误，请重新填写，需填写（正常、预警、滞后）",
                    }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(
            self.ctx.task_id,
            variables={
                "jd_list": new_jd_list,
                "jb_list": new_jb_list,
                "pictures_submit": pictures_submit,
            },
        )


class ProcessWeeklyReportData(object):
    """
    处理周报数据
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def compute_spot_the_difference(self):
        """
        计算进度以及找出不同
        """
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        process_definition_key = self.ctx.variables.get(
            "_process_definition_key"
        )  # 流程标识
        item_tube = self.ctx.variables.get("item_tube")  # 项管
        pictures_submit = self.ctx.variables.get("pictures_submit")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d")
        new_jd_tree = self.ctx.variables.get("jd_list")  # 新进度管理-周报信息

        new_jb_list = self.ctx.variables.get("jb_list")  # 新周报简报-周报信息
        cal_tree =  calculate_tree_progress(calculate_actual_times(new_jd_tree))  
        all_progress = 0
         
        first_level_proportion_sum = sum(child["first_level_proportion"] for child in cal_tree)
        if first_level_proportion_sum == 1:
            for row in cal_tree:
                all_progress += row.get("week_progress") * row.get("first_level_proportion")
        else:
            for row in cal_tree:
                all_progress += row.get("week_progress")
            all_progress = all_progress/len(cal_tree)
        all_progress = round(all_progress, 2)

        """
            周报简报数据处理
        """
        jb_insert_data = []
        result_dict = {}
        for row in new_jb_list:
            table_of_contents = row.get("table_of_contents")
            details = row.get("details")
            if table_of_contents == "周报周期":
                result_dict["weekly_report_cycle"] = details
            elif table_of_contents == "当前阶段":
                result_dict["current_stage"] = details
            elif table_of_contents == "整体形象进度":
                result_dict["overall_image_progress"] = str(all_progress) + "%"
                row["details"] = str(all_progress) + "%"
            elif table_of_contents == "状态":
                result_dict["state"] = details
            elif table_of_contents == "设备到货":
                result_dict["arrival_equipment"] = details
            elif table_of_contents == "工程进度":
                result_dict["project_progress"] = details
            elif table_of_contents == "本周完成情况":
                result_dict["week_completion"] = details
            elif table_of_contents == "下周工作计划":
                result_dict["next_week_work_plan"] = details
            elif table_of_contents == "安全管理":
                result_dict["safety_management"] = details
            elif table_of_contents == "质量管理":
                result_dict["quality_control"] = details
            elif table_of_contents == "施工人员投入":
                result_dict["construction_personnel_input"] = details
            elif table_of_contents == "上周重点问题跟踪情况":
                result_dict["follow_last_week_key_issues"] = details
            elif table_of_contents == "本周问题及风险":
                result_dict["issues_risks_week"] = details
            elif table_of_contents == "解决措施/求助":
                result_dict["solution_measure"] = details
            result_dict["ticket_id"] = ticket_id
            result_dict["project_name"] = project_name
            result_dict["now_time"] = now_time
        if pictures_submit and len(pictures_submit) > 0:
            pictures_list = []
            for picture in pictures_submit:
                if len(picture) > 0:
                    pictures_list.append(picture)
            result_dict["pictures_submit"] = str(pictures_list)
        jb_insert_data.append(result_dict)

        # 判断本周进度有无变化
        old_progress = self.ctx.variables.get("old_progress")
        progress_change_flag = False
        if old_progress:
            temp = float(old_progress.replace("%", ""))
            if temp != all_progress:
                progress_change_flag = True

        flow.complete_task(
            self.ctx.task_id,
            variables={
                "jd_list": cal_tree,
                "jb_insert_data": jb_insert_data,
                "jb_list": new_jb_list,
                "pictures_submit": pictures_submit,
                "all_progress": all_progress,
                "progress_change_flag": progress_change_flag,
            },
        )


class WeeklyReportInformationConfirmation(AjaxTodoBase):
    """
    周报信息确认
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        confirm_or_not = process_data.get("confirm_or_not")
        file_url = ""
        progress_no_change_remark = ""
        if confirm_or_not == "是":
            progress_change_flag = self.ctx.variables.get("progress_change_flag")
            if not progress_change_flag:
                progress_no_change_remark = process_data.get(
                    "progress_no_change_remark"
                )
                if not progress_no_change_remark:
                    return {
                        "code": -1,
                        "msg": f"检测到本周进度无变化，请填写进度无变化说明!",
                    }
            file_url = process_data.get("fileUrl")
            # 周报下载拦截，先不拦截（现场导出后上传到cos失败）
            # if not file_url:
            #     return {"code": -1, "msg": f"请下载周报文件存档"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(
            self.ctx.task_id,
            variables={
                "confirm_or_not": confirm_or_not,
                "file_url": file_url,
                "progress_no_change_remark": progress_no_change_remark,
            },
        )


class WeeklyReportInformationStorage(object):
    """
    周报信息存储
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_storage(self):
        """
        数据存储
        """
        new_jd_tree = self.ctx.variables.get("jd_list")  # 新进度管理-周报信息
        old_jd_tree = self.ctx.variables.get("old_jd_list")  # 旧进度管理-周报信息
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        item_tube = self.ctx.variables.get("item_tube")
        jb_insert_data = self.ctx.variables.get("jb_insert_data")
        flow_name = self.ctx.variables.get("_process_definition_key")
        old_progress = self.ctx.variables.get("old_progress")
        all_progress = self.ctx.variables.get("all_progress")
        file_url = self.ctx.variables.get("file_url")
        if jb_insert_data and len(jb_insert_data) > 0:
            jb_insert_data[0]["file_url"] = file_url
        new_jd_list = flatten_tree_to_list(new_jd_tree)
        old_jd_list = flatten_tree_to_list(old_jd_tree)
        update_list = []
        diff_log = []
        serial_number_map = {}
        for index, item in enumerate(new_jd_list):
            old_item = old_jd_list[index]
            serial_number_map[item["serial_number"]] = item["work_content"]
            if is_diff(item, old_item):
                update_list.append(
                    {
                        "actual_finish_time": item.get("actual_finish_time"),
                        "actual_start_time": item.get("actual_start_time"),
                        "state": item.get("state"),
                        "week_progress": item.get("week_progress"),
                        "id": item["id"],
                    }
                )
                if (
                    item["allowEdit"]
                    and item["week_progress"] != old_item["week_progress"]
                ):
                    serial_num = item["serial_number"]
                    level_serial = len(serial_num.split("."))
                    parent_version = '.'.join(serial_num.split('.')[:-1])
                    name_log = ''
                    if level_serial == 3:
                        name_log = f"{parent_version} {serial_number_map[parent_version]}/"
                    if level_serial == 4:
                        grand_serial = '.'.join(serial_num.split('.')[:-2])
                        name_log = f"{grand_serial} {serial_number_map[grand_serial]}/" \
                                f"{parent_version} {serial_number_map[parent_version]}/"
                       
                    name_log += f"{serial_num} {item['work_content']}"
                    diff_log.append(
                        {
                            "new_progress": item["week_progress"],
                            "old_progress": old_item["week_progress"],
                            "name": name_log,
                        }
                    )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if update_list:
            for item in update_list:
                tb_db.update(
                    "construct_plan_data",
                    {
                        "actual_finish_time": item["actual_finish_time"],
                        "actual_start_time": item["actual_start_time"],
                        "state": item["state"],
                        "week_progress": item["week_progress"],
                    },
                    {"id": item["id"]},
                )

            operate_log = f"整体形象进度变化：{old_progress} -> {all_progress}%\n"
            for item in diff_log:
                operate_log += f"{item['name']} : {item['old_progress']}% -> {item['new_progress']}%\n"
            tb_db.insert(
                "construct_plan_data_log",
                {
                    "project_name": project_name,
                    "ticket_id": ticket_id,
                    "operator": item_tube,
                    "flow_name": flow_name,
                    "operate_log": operate_log,
                    "operate_time": datetime.datetime.now(),
                },
            )
        tb_db.insert_batch("weekly_report_brief", jb_insert_data)
        tb_db.commit()
        flow.complete_task(self.ctx.task_id, variables={})


class WeekPaperAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 进度无变化 邮件发送
    def send_email(self):
        project_name = self.ctx.variables.get("project_name")
        progress_no_change_remark = self.ctx.variables.get("progress_no_change_remark")
        email_title = f"{project_name}周报进度无变化通知"
        email_content = (
            f"{project_name}周报已提交，本周进度无变化，请PM关注！<br>"
            f"进度说明: {progress_no_change_remark}"
        )
        email_receiver = Tools.get_PM(project_name)
        email_Cc = ""
        email_receiver_list = email_receiver.split(";")
        email_Cc_list = []
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        tof.send_email(
            sendTitle=email_title,
            msgContent=email_content,
            sendTo=email_receiver_list,
            sendCopy=email_Cc_list,
        )
        progress_no_change_email = {
            "email_title": email_title,
            "email_content": email_content,
            "email_receiver": email_receiver,
            "email_Cc": email_Cc,
            "email_receiver_list": email_receiver_list,
            "email_Cc_list": email_Cc_list,
        }
        variables = {
            "progress_no_change_email": progress_no_change_email,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
