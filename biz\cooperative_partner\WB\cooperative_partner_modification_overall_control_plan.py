import ast
import datetime
import hashlib
import hmac
import json
import time
from urllib.parse import quote

from biz.site_implementation.modification_overall_control_plan import GetDataInLibrary
from biz.utils.change_url import download_and_upload_file
# from biz.utils.ticket_processing_tools import reverse_the_waiting_state

import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService,WorkflowVarUpdate
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


class ModificationOverallControlPlanWB(object):
    """
        总控计划修改流程
        腾讯方调用接口：输出数据（合作伙伴外部系统）
    """

    def __init__(self):
        super(ModificationOverallControlPlanWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def master_control_plan_update_results_review(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            总控计划修改审批反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")

        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        master_control_plan_update_results_review_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId,  # 合作伙伴工单号
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": master_control_plan_update_results_review_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_update_results_review",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_update_results_review",  # (*必填) 云函数名称
                        "data": master_control_plan_update_results_review_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def general_control_plan_update_review(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            总控计划修改审批反馈通用方法
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")

        supplier_info = 'supplier_differentiation'
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map("cooperative_partner_supplier_distinguish")
        # 在判断供应商为Dcops系统还是外部系统
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        master_control_plan_update_results_review_data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId,  # 合作伙伴工单号
        }
        if system_type == '外部':
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": master_control_plan_update_results_review_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/master_control_plan_update_results_review",
                }
            )
            if info.get('ErrorCode') != 0:
                raise Exception(f'报错信息为：{info.get("Message")}')
        elif system_type == 'Dcops':
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "master_control_plan_update_results_review",  # (*必填) 云函数名称
                        "data": master_control_plan_update_results_review_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            result_str = info['data']['result']

            # 将 result 字符串解析为字典
            result_dict = json.loads(result_str)

            # 获取 code 的值
            code = result_dict.get('code')
            message = result_dict.get('message')
            if code != 0:
                raise Exception(f"报错信息为： {message}")
        variables = {
            'completion_audit_feedback': str(info)
        }
        return variables


class WaitPartnerCallInterfaceZkjhxg(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceZkjhxg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_construction_plan_submission(self):
        """
            等待施工方案提交
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT master_control_plan, reasons_for_change " \
              "FROM master_control_plan_storage " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              "AND now_time = " \
              "(SELECT MAX(now_time) " \
              "FROM master_control_plan_storage " \
              f"WHERE ticket_id = '{ticket_id}' " \
              f"AND project_name = '{project_name}')"
        result_list = db.get_all(sql)

        if not result_list:
            return {"success": False, "data": "无总控计划"}

        zkjh_table = []
        # 解析文件获取新总控计划数据
        for record in result_list:
            # 获取 master_control_plan
            master_control_plan = record.get('master_control_plan', '')
            reasons_for_change = record.get('reasons_for_change', '')

            """
                解析文件
            """
            encoded_url = quote(master_control_plan, safe=":/")
            df = pd.read_excel(encoded_url)
            df = df.replace({pd.np.nan: None})
            error_entries = []  # 用于存储错误条目
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                plan_duration_construction = row['工期']

                # 安全地处理开始时间和完成时间
                start_time = row['计划开始时间（年/月/日）']
                completion_time = row['计划完成时间（年/月/日）']

                # 如果为空字符串或等价于 NaN 的值（如 float('nan')），统一转为 pd.NaT
                start_time_parsed = pd.to_datetime(start_time, errors='coerce') if pd.notna(
                    start_time) and start_time not in ['', ' '] else pd.NaT
                completion_time_parsed = pd.to_datetime(completion_time, errors='coerce') if pd.notna(
                    completion_time) and completion_time not in ['', ' '] else pd.NaT

                # 判断是否转换失败，并且原始值不是空值
                if (pd.isna(start_time_parsed) and pd.notna(row['计划开始时间（年/月/日）']) and row[
                    '计划开始时间（年/月/日）'] not in ['', ' ']) or \
                        (pd.isna(completion_time_parsed) and pd.notna(row['计划完成时间（年/月/日）']) and row[
                            '计划完成时间（年/月/日）'] not in ['', ' ']):
                    error_entries.append(f"{serial_number}、{work_content}")

                start_time = start_time.strftime("%Y-%m-%d") if isinstance(start_time, pd.Timestamp) else None
                completion_time = completion_time.strftime("%Y-%m-%d") if isinstance(completion_time,
                                                                                     pd.Timestamp) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']

                zkjh_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "project_progress": plan_duration_construction,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "responsible_person": responsible_person,
                    "input_object": input_object,
                    "output_object": output_object,
                    "construction_attribute": construction_attribute
                })
        error_message = ""
        if error_entries:
            # 构造最终的 error_message
            error_message = "时间格式转换失败(如需要可直接不填)：" + "；".join(error_entries) if error_entries else ""
            query_sql = f"DELETE FROM master_control_plan_storage " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name = '{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            function_rewiew_object = ModificationOverallControlPlanWB()
            variables_msg = function_rewiew_object.general_control_plan_update_review(
                project_name=project_name,
                examine_approve_result="驳回",
                dismiss_role=None,
                remark=error_message
            )
            WorkflowVarUpdate(self.ctx.instance_id, variables_msg)
            return {"success": False, "data": "校验失败"}
        for index, row in enumerate(zkjh_table, start=1):
            row["id"] = index
        new_zkjh_list = []
        first_list = []
        second_list = []
        third_list = []
        foured_list = []

        time_verification = GetDataInLibrary()
        zkjh_table = time_verification.time_disposal_new(zkjh_table,project_name)
        # 时间校验判断
        if isinstance(zkjh_table, dict) and zkjh_table.get("code") == -1:

            query_sql = f"DELETE FROM master_control_plan_storage " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name = '{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
            function_rewiew_object = ModificationOverallControlPlanWB()
            variables_msg = function_rewiew_object.general_control_plan_update_review(
                project_name=project_name,
                examine_approve_result="驳回",
                dismiss_role=None,
                remark=zkjh_table.get("msg")
            )
            WorkflowVarUpdate(self.ctx.instance_id, variables_msg)
            return {"success": False, "data": "校验失败"}
        for row in zkjh_table:
            serial_number = str(row.get("serial_number", "None"))
            if serial_number is not None:
                if serial_number != "None" and serial_number.count(".") == 0:
                    first_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 1:
                    second_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 2:
                    third_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 3:
                    foured_list.append(row)
        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                for third_item in third_list:
                    third_children = []
                    for four_item in foured_list:
                        if str(four_item["serial_number"]).startswith(str(third_item["serial_number"])):
                            third_children.append(
                                {
                                    "id": four_item.get("id"),
                                    "serial_number": four_item.get("serial_number"),
                                    "work_content": four_item.get("work_content"),
                                    "project_progress": four_item.get("project_progress"),
                                    "start_time": four_item.get("start_time"),
                                    "completion_time": four_item.get("completion_time"),
                                    "responsible_person": four_item.get("responsible_person"),
                                    "input_object": four_item.get("input_object"),
                                    "output_object": four_item.get("output_object"),
                                    "construction_attribute": four_item.get("construction_attribute"),
                                    "allowEdit": False,
                                    "project_progress_modified": False,
                                    "start_time_modified": False,
                                    "completion_time_modified": False,
                                    "responsible_person_modified": False,
                                }
                            )
                    if str(third_item["serial_number"]).startswith(str(second_item["serial_number"])):
                        second_children.append(
                            {
                                "id": third_item.get("id"),
                                "serial_number": third_item.get("serial_number"),
                                "work_content": third_item.get("work_content"),
                                "project_progress": third_item.get("project_progress"),
                                "start_time": third_item.get("start_time"),
                                "completion_time": third_item.get("completion_time"),
                                "responsible_person": third_item.get("responsible_person"),
                                "input_object": third_item.get("input_object"),
                                "output_object": third_item.get("output_object"),
                                "construction_attribute": third_item.get("construction_attribute"),
                                "allowEdit": False,
                                "project_progress_modified": False,
                                "start_time_modified": False,
                                "completion_time_modified": False,
                                "responsible_person_modified": False,
                                "children": third_children,
                            }
                        )
                if str(second_item["serial_number"]).startswith(str(first_item["serial_number"])):
                    first_children.append(
                        {
                            "id": second_item.get("id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "responsible_person": second_item.get("responsible_person"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get(
                                "construction_attribute"
                            ),
                            "allowEdit": False,
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False,
                            "children": second_children,
                        }
                    )
            new_zkjh_list.append(
                {
                    "id": first_item.get("id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "responsible_person": first_item.get("responsible_person"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "allowEdit": False,
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False,
                    "children": first_children,
                }
            )
        self.workflow_detail.add_kv_table('施工方案信息', {'message': result_list})
        if result_list:
            variables = {
                "result_list": result_list,
                "new_zkjh_list": new_zkjh_list,
                "reasons_for_change": reasons_for_change
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_data_composition(self):
        """
            等待数据组成
        """
        insert_data = []
        new_data = []
        old_data = []
        first_list = []
        second_list = []
        third_list = []
        fourth_list = []  # 新增四级标题列表
        new_zkjh_list = []
        project_name = self.ctx.variables.get("project_name")
        new_zkjh = self.ctx.variables.get("new_zkjh_list")
        old_zkjh = self.ctx.variables.get("old_zkjh_list")

        # 新总控计划数据存储
        for row in new_zkjh:
            children1 = row.get("children")
            new_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                new_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "project_progress_modified": False,
                        "start_time_modified": False,
                        "completion_time_modified": False,
                        "responsible_person_modified": False
                    }
                )
                for item2 in children2:
                    new_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False
                        }
                    )
                    # 处理四级标题
                    children3 = item2.get("children")
                    for item3 in children3:
                        new_data.append(
                            {
                                "serial_number": item3.get("serial_number"),
                                "work_content": item3.get("work_content"),
                                "project_progress": item3.get("project_progress"),
                                "start_time": item3.get("start_time"),
                                "completion_time": item3.get("completion_time"),
                                "responsible_person": item3.get("responsible_person"),
                                "input_object": item3.get("input_object"),
                                "output_object": item3.get("output_object"),
                                "construction_attribute": item3.get("construction_attribute"),
                                "project_progress_modified": False,
                                "start_time_modified": False,
                                "completion_time_modified": False,
                                "responsible_person_modified": False
                            }
                        )

        # 旧总控计划数据存储
        for row in old_zkjh:
            children1 = row.get("children")
            old_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "project_progress_modified": False,
                    "start_time_modified": False,
                    "completion_time_modified": False,
                    "responsible_person_modified": False
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                old_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "project_progress_modified": False,
                        "start_time_modified": False,
                        "completion_time_modified": False,
                        "responsible_person_modified": False
                    }
                )
                for item2 in children2:
                    old_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "project_progress_modified": False,
                            "start_time_modified": False,
                            "completion_time_modified": False,
                            "responsible_person_modified": False
                        }
                    )
                    # 处理四级标题
                    children3 = item2.get("children")
                    if children3 is not None:  # Check if children3 is not None
                        for item3 in children3:
                            insert_data.append(
                                {
                                    "serial_number": item3.get("serial_number"),
                                    "work_content": item3.get("work_content"),
                                    "project_progress": item3.get("project_progress"),
                                    "start_time": item3.get("start_time"),
                                    "completion_time": item3.get("completion_time"),
                                    "responsible_person": item3.get("responsible_person"),
                                    "input_object": item3.get("input_object"),
                                    "output_object": item3.get("output_object"),
                                    "construction_attribute": item3.get("construction_attribute"),
                                    "project_progress_modified": False,
                                    "start_time_modified": False,
                                    "completion_time_modified": False,
                                    "responsible_person_modified": False
                                }
                            )

        # 总控计划对比
        for new_item, old_item in zip(new_data, old_data):
            if new_item["start_time"] != old_item["start_time"]:
                new_item["start_time_modified"] = True

            if new_item["completion_time"] != old_item["completion_time"]:
                new_item["completion_time_modified"] = True

            if new_item.get("responsible_person") != old_item["responsible_person"]:
                new_item["responsible_person_modified"] = True

            # 计算project_progress
            if (
                    new_item["start_time"] is not None
                    and new_item["completion_time"] is not None
            ):
                start_time = datetime.datetime.strptime(
                    new_item["start_time"], "%Y-%m-%d"
                )
                completion_time = datetime.datetime.strptime(
                    new_item["completion_time"], "%Y-%m-%d"
                )
                project_progress = (completion_time - start_time).days + 1
                new_item["project_progress"] = project_progress

            # 对比project_progress和old_zkjh_list中的值
            if new_item["project_progress"] != old_item["project_progress"]:
                new_item["project_progress_modified"] = True

        for index, row in enumerate(new_data, start=1):
            row["id"] = index

        # 新总控计划组合
        for row in new_data:
            serial_number = str(row.get("serial_number", "None"))
            if serial_number is not None:
                if serial_number != "None" and serial_number.count(".") == 0:
                    first_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 1:
                    second_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 2:
                    third_list.append(row)
                if serial_number != "None" and serial_number.count(".") == 3:  # 处理四级标题
                    fourth_list.append(row)

        for first_item in first_list:
            first_children = []
            for second_item in second_list:
                second_children = []
                for third_item in third_list:
                    third_children = []  # 新增第三级子项列表
                    for fourth_item in fourth_list:  # 处理四级标题
                        if str(fourth_item["serial_number"]).startswith(
                                str(third_item["serial_number"])
                        ):
                            third_children.append(
                                {
                                    "id": fourth_item.get("id"),
                                    "serial_number": fourth_item.get("serial_number"),
                                    "work_content": fourth_item.get("work_content"),
                                    "project_progress": fourth_item.get("project_progress"),
                                    "start_time": fourth_item.get("start_time"),
                                    "completion_time": fourth_item.get("completion_time"),
                                    "responsible_person": fourth_item.get("responsible_person"),
                                    "input_object": fourth_item.get("input_object"),
                                    "output_object": fourth_item.get("output_object"),
                                    "construction_attribute": fourth_item.get("construction_attribute"),
                                    "allowEdit": fourth_item.get("allowEdit"),
                                    "project_progress_modified": fourth_item.get("project_progress_modified"),
                                    "start_time_modified": fourth_item.get("start_time_modified"),
                                    "completion_time_modified": fourth_item.get("completion_time_modified"),
                                    "responsible_person_modified": fourth_item.get("responsible_person_modified")
                                }
                            )
                    if str(third_item["serial_number"]).startswith(
                            str(second_item["serial_number"])
                    ):
                        second_children.append(
                            {
                                "id": third_item.get("id"),
                                "serial_number": third_item.get("serial_number"),
                                "work_content": third_item.get("work_content"),
                                "project_progress": third_item.get("project_progress"),
                                "start_time": third_item.get("start_time"),
                                "completion_time": third_item.get("completion_time"),
                                "responsible_person": third_item.get("responsible_person"),
                                "input_object": third_item.get("input_object"),
                                "output_object": third_item.get("output_object"),
                                "construction_attribute": third_item.get("construction_attribute"),
                                "allowEdit": third_item.get("allowEdit"),
                                "project_progress_modified": third_item.get("project_progress_modified"),
                                "start_time_modified": third_item.get("start_time_modified"),
                                "completion_time_modified": third_item.get("completion_time_modified"),
                                "responsible_person_modified": third_item.get("responsible_person_modified"),
                                "children": third_children  # 添加四级标题的子项
                            }
                        )
                if str(second_item["serial_number"]).startswith(
                        str(first_item["serial_number"])
                ):
                    first_children.append(
                        {
                            "id": second_item.get("id"),
                            "serial_number": second_item.get("serial_number"),
                            "work_content": second_item.get("work_content"),
                            "project_progress": second_item.get("project_progress"),
                            "start_time": second_item.get("start_time"),
                            "completion_time": second_item.get("completion_time"),
                            "responsible_person": second_item.get("responsible_person"),
                            "input_object": second_item.get("input_object"),
                            "output_object": second_item.get("output_object"),
                            "construction_attribute": second_item.get("construction_attribute"),
                            "allowEdit": second_item.get("allowEdit"),
                            "project_progress_modified": second_item.get("project_progress_modified"),
                            "start_time_modified": second_item.get("start_time_modified"),
                            "completion_time_modified": second_item.get("completion_time_modified"),
                            "responsible_person_modified": second_item.get("responsible_person_modified"),
                            "children": second_children
                        }
                    )
            new_zkjh_list.append(
                {
                    "id": first_item.get("id"),
                    "serial_number": first_item.get("serial_number"),
                    "work_content": first_item.get("work_content"),
                    "project_progress": first_item.get("project_progress"),
                    "start_time": first_item.get("start_time"),
                    "completion_time": first_item.get("completion_time"),
                    "responsible_person": first_item.get("responsible_person"),
                    "input_object": first_item.get("input_object"),
                    "output_object": first_item.get("output_object"),
                    "construction_attribute": first_item.get("construction_attribute"),
                    "allowEdit": first_item.get("allowEdit"),
                    "project_progress_modified": first_item.get("project_progress_modified"),
                    "start_time_modified": first_item.get("start_time_modified"),
                    "completion_time_modified": first_item.get("completion_time_modified"),
                    "responsible_person_modified": first_item.get("responsible_person_modified"),
                    "children": first_children
                }
            )
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        # 新总控计划数据存储
        for row in new_zkjh_list:
            children1 = row.get("children")
            insert_data.append(
                {
                    "serial_number": row.get("serial_number"),
                    "work_content": row.get("work_content"),
                    "project_progress": row.get("project_progress"),
                    "start_time": row.get("start_time"),
                    "completion_time": row.get("completion_time"),
                    "responsible_person": row.get("responsible_person"),
                    "input_object": row.get("input_object"),
                    "output_object": row.get("output_object"),
                    "construction_attribute": row.get("construction_attribute"),
                    "project_name": project_name,
                    "now_time": now_time
                }
            )
            for item1 in children1:
                children2 = item1.get("children")
                insert_data.append(
                    {
                        "serial_number": item1.get("serial_number"),
                        "work_content": item1.get("work_content"),
                        "project_progress": item1.get("project_progress"),
                        "start_time": item1.get("start_time"),
                        "completion_time": item1.get("completion_time"),
                        "responsible_person": item1.get("responsible_person"),
                        "input_object": item1.get("input_object"),
                        "output_object": item1.get("output_object"),
                        "construction_attribute": item1.get("construction_attribute"),
                        "project_name": project_name,
                        "now_time": now_time
                    }
                )
                for item2 in children2:
                    insert_data.append(
                        {
                            "serial_number": item2.get("serial_number"),
                            "work_content": item2.get("work_content"),
                            "project_progress": item2.get("project_progress"),
                            "start_time": item2.get("start_time"),
                            "completion_time": item2.get("completion_time"),
                            "responsible_person": item2.get("responsible_person"),
                            "input_object": item2.get("input_object"),
                            "output_object": item2.get("output_object"),
                            "construction_attribute": item2.get("construction_attribute"),
                            "project_name": project_name,
                            "now_time": now_time
                        }
                    )
                    # 处理四级标题的插入
                    children3 = item2.get("children")
                    for item3 in children3:
                        insert_data.append(
                            {
                                "serial_number": item3.get("serial_number"),
                                "work_content": item3.get("work_content"),
                                "project_progress": item3.get("project_progress"),
                                "start_time": item3.get("start_time"),
                                "completion_time": item3.get("completion_time"),
                                "responsible_person": item3.get("responsible_person"),
                                "input_object": item3.get("input_object"),
                                "output_object": item3.get("output_object"),
                                "construction_attribute": item3.get("construction_attribute"),
                                "project_name": project_name,
                                "now_time": now_time
                            }
                        )

        variables = {
            "insert_data": insert_data,
            "new_zkjh_list": new_zkjh_list,
            "new_data": new_data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)



class PartnerCallInterfaceZkjhxg(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceZkjhxg, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def submission_of_master_control_plan(self, TencentTicketId, ProjectName, MasterControlPlan, ReasonsForChange):
        """
            总控计划提交
        """
        # # 校验 TencentTicketId 和 ProjectName
        # if not TencentTicketId or not ProjectName:
        #     return {'code': -1, 'msg': '腾讯侧工单号及项目名称缺失'}
        # # 校验 MasterControlPlan 和 ReasonsForChange
        # if not ReasonsForChange:
        #     return {'code': -1, 'msg': '计划变更原因缺失'}
        # if not isinstance(MasterControlPlan, str):
        #     return {'code': -1, 'msg': '总控计划链接格式错误'}
        # master_control_plan = download_and_upload_file(MasterControlPlan)
        # variables = {
        #     "master_control_plan": master_control_plan,
        #     "reasons_for_change": ReasonsForChange,
        # }
        # msg = reverse_the_waiting_state(TencentTicketId, variables)
        # return {'code': 200, 'msg': '成功'}


        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '腾讯侧工单号及项目名称缺失'}
        # 校验 MasterControlPlan 和 ReasonsForChange
        if not ReasonsForChange:
            return {'code': -1, 'msg': '计划变更原因缺失'}

        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        data = {
            'ticket_id': TencentTicketId,
            'project_name': ProjectName,
            "master_control_plan": str(MasterControlPlan),
            'reasons_for_change': ReasonsForChange,
            'now_time': now_time
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("master_control_plan_storage", data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}

    def create_ticket(self, ProjectName, PartnerTicketId, Facilitator):
        """
            创建总控计划（起腾讯侧工单）
        """
        res_list = []
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号{ProjectName}"
        processTitle = date_string + "总控计划修改"
        data = {
            "FacilitatorName": Facilitator,
            "FacilitatorTicketId": PartnerTicketId,
            "TicketCreateData": {
                "CustomVariables": {
                    'project_name': ProjectName,
                    'Facilitator': Facilitator,
                    'PartnerTicketId': PartnerTicketId
                },
                "ProcessDefinitionKey": "cooperative_modification_overall_control_plan_wd",
                "Source": "",
                "TicketDescription": "总控计划修改流程",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "v_mmywang",
                    "Creator": "v_mmywang",
                    "Deal": "v_mmywang"
                }
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Collaboration", method="CreateTicket", data=data)
        res_list.append(res)

        for row in res_list:
            TicketId = row.get('TicketId')

        return {
            'code': 200,
            'msg': '成功',
            'data': TicketId
        }

    def obtain_master_control_plan_template(self):
        """
            获取项目总控计划模板
        """
        master_control_plan_template = config.get_config_string(
            "master_control_plan_template"
        )
        data = {
            "MasterControlPlan": master_control_plan_template
        }

        return {'code': 200, 'msg': '成功', 'data': data}


class ZkjhxgSupervisionExaminationApproval(AjaxTodoBase):
    """
        总控计划修改监理审批
    """

    def __init__(self):
        super(ZkjhxgSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        supervision_management_approval = process_data.get('supervision_management_approval')
        supervision_management_remark = process_data.get('supervision_management_remark')

        if supervision_management_approval == '驳回':
            query_sql = f"DELETE FROM master_control_plan_storage " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name = '{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'supervision_management_approval': supervision_management_approval,
            'supervision_management_remark': supervision_management_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhxgItemApproval(AjaxTodoBase):
    """
        总控计划修改项管审批
    """

    def __init__(self):
        super(ZkjhxgItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        item_tube_approval = process_data.get('item_tube_approval')
        item_tube_remark = process_data.get('item_tube_remark')

        if item_tube_approval == '驳回':
            query_sql = f"DELETE FROM master_control_plan_storage " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name = '{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'item_tube_approval': item_tube_approval,
            'item_tube_remark': item_tube_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ZkjhxgPmApproval(AjaxTodoBase):
    """
        总控计划修改PM审批
    """

    def __init__(self):
        super(ZkjhxgPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        insert_data = self.ctx.variables.get("insert_data")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        PM_approval = process_data.get('PM_approval')
        PM_remark = process_data.get('PM_remark')

        if PM_approval == '驳回':
            query_sql = f"DELETE FROM master_control_plan_storage " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name = '{project_name}'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()
        if PM_approval == '通过':
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("excel_data", insert_data)
            tb_db.commit()
            insert_data_new = []
            for item in insert_data:
                insert_data_new.append(
                    {
                        "serial_number": str(item.get("serial_number")),
                        "work_content": item.get("work_content") or "",
                        "start_time": item.get("start_time") or "",
                        "completion_time": item.get("completion_time") or "",
                        "responsible_person": item.get("responsible_person") or "",
                        "input_object": item.get("input_object") or "",
                        "output_object": item.get("output_object") or "",
                        "construction_attribute": item.get("construction_attribute") or "",
                        "project_name": project_name or ""
                    }
                )
            creator = self.ctx.variables.get("Facilitator")
            process_definition_key = self.ctx.variables.get("_process_definition_key")
            response_data = gnetops.request(
                action="Project",
                method="UpdateConstructPlanData",
                ext_data={
                    "project_name": project_name,
                    "update_list": insert_data_new,
                    "ticket_id": ticket_id,
                    "flow_name": process_definition_key,
                    "operator": creator
                },
                scheme="ifob-infrastructure",
            )
        variables = {
            'PM_approval': PM_approval,
            'PM_remark': PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
