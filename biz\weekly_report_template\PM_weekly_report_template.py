import json
import re
import time
from datetime import datetime, timedelta

from iBroker.lib.sdk import tof, gnetops
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoB<PERSON>
from iBroker.sdk.task.context import get_context
from iBroker.lib import mysql, config
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate


class PMGetBasicInformation(object):
    def __init__(self):
        super(PMGetBasicInformation).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_basic_info(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT sm.third_progress, sm.serial_number, sm.task_name, " \
                    "sm.completion_week, sm.work_plan_next_week, sm.now_time " \
                    "FROM schedule_management sm " \
                    "JOIN week_create_info wci ON wci.ticketid = sm.ticketid " \
                    f"WHERE wci.project_name = '{project_name}' " \
                    "AND sm.now_time = (" \
                    "SELECT MAX(now_time) " \
                    "FROM schedule_management " \
                    "WHERE ticketid " \
                    "IN ( " \
                    "SELECT ticketid " \
                    "FROM week_create_info " \
                    f"WHERE project_name = '{project_name}'))"
        week_basic_info = db.get_all(query_sql)
        first_list = []
        second_list = []
        third_list = []
        fourth_list = []
        for row in week_basic_info:
            serial_number = row.get('serial_number', None)
            if serial_number is not None and serial_number.count('.') == 0:
                first_list.append(row)
            if serial_number is not None and serial_number.count('.') == 1:
                second_list.append(row)
            if serial_number is not None and serial_number.count('.') == 2:
                third_list.append(row)
            if serial_number is not None and serial_number.count('.') == 3:
                fourth_list.append(row)
        dh = "<br>【设备到货】"
        az = "<br>【安装施工】"
        bj = "【报建】"
        qt = "<br>【其他】"
        next_dh = "<br>【设备到货】"
        next_az = "<br>【安装施工】"
        next_bj = "【报建】"
        next_qt = "<br>【其他】"

        for i in week_basic_info:
            task_name = i.get("task_name")
            serial_number = i.get("serial_number")
            completion_week = i.get("completion_week")
            work_plan_next_week = i.get("work_plan_next_week")
            if serial_number is None:
                continue
            index_list = serial_number.split(".")
            if "2.4" <= serial_number <= "2.5" and serial_number.count('.') in (2, 3):
                # 二级标题
                second = index_list[0] + '.' + index_list[1]
                for s in second_list:
                    if second == s.get('serial_number'):
                        if completion_week:
                            bj += (task_name + "完成；")
                        if work_plan_next_week:
                            next_bj += (task_name + "；")

            elif "2.5" <= serial_number <= "2.7" and serial_number.count('.') in (2, 3):
                # 二级标题
                second = index_list[0] + '.' + index_list[1]
                for s in second_list:
                    if second == s.get('serial_number'):
                        if completion_week:
                            dh += (task_name + "到货；")
                        if work_plan_next_week:
                            next_dh += (task_name + "；")

            elif "3.1" <= serial_number <= "3.5" and serial_number.count('.') in (2, 3):
                # 二级标题
                second = index_list[0] + '.' + index_list[1]
                for s in second_list:
                    if second == s.get('serial_number'):
                        if completion_week:
                            if "IT方仓：" not in az:
                                az += "IT方仓："
                        if work_plan_next_week:
                            if "IT方仓：" not in next_az:
                                next_az += "IT方仓："
                # 三级标题
                third = index_list[0] + '.' + index_list[1] + '.' + index_list[2]
                for t in third_list:
                    if third == t.get('serial_number'):
                        if completion_week:
                            data = t.get('task_name') + "安装；"
                            if data not in az:
                                az += data
                        if work_plan_next_week:
                            plan_data = t.get('task_name')
                            if plan_data not in next_az:
                                next_az += (plan_data + "；")
            elif "3.5" <= serial_number <= "4" and completion_week and serial_number.count('.') in (2, 3):
                # 二级标题
                second = index_list[0] + '.' + index_list[1]
                for s in second_list:
                    if second == s.get('serial_number'):
                        a = s.get('task_name') + "："
                        if completion_week:
                            if a not in az:
                                az += a
                        if work_plan_next_week:
                            if a not in next_az:
                                next_az += a
                # 三级标题
                third = index_list[0] + '.' + index_list[1] + '.' + index_list[2]
                for t in third_list:
                    if third == t.get('serial_number'):
                        if completion_week:
                            data = t.get('task_name') + "安装；"
                            if data not in az:
                                az += data
                        if work_plan_next_week:
                            plan_data = t.get('task_name')
                            if plan_data not in next_az:
                                next_az += (plan_data + "；")
            elif (serial_number < "2.4" or serial_number > "4") and serial_number.count('.') in (2, 3):
                # 二级标题
                second = index_list[0] + '.' + index_list[1]
                for s in second_list:
                    if second == s.get('serial_number'):
                        if completion_week:
                            qt += (task_name + "完成；")
                        if work_plan_next_week:
                            next_qt += (task_name + "；")
        completed_this_week = ''
        plan_next_week = ''
        if bj != "【报建】":
            completed_this_week += bj
        if dh != "<br>【设备到货】":
            completed_this_week += dh
        if az != "<br>【安装施工】":
            completed_this_week += az
        if qt != "<br>【其他】":
            completed_this_week += next_qt

        if next_bj != "【报建】":
            plan_next_week += next_bj
        if next_dh != "<br>【设备到货】":
            plan_next_week += next_dh
        if next_az != "<br>【安装施工】":
            plan_next_week += next_az
        if next_qt != "<br>【其他】":
            plan_next_week += next_qt
        return {"completed_this_week": completed_this_week, "plan_next_week": plan_next_week}

    def get_basic_information(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT dcopsTicketId,number_of_rack,PM FROM risk_early_warning_data " \
              f"WHERE project_name = '{project_name}' "
        project_info = db.query(sql)
        if project_info:
            number_of_rack = project_info[0].get("number_of_rack")
            ticket_id = project_info[0].get("dcopsTicketId")
        else:
            number_of_rack = ''
            ticket_id = '1'

        # 获取当前日期
        now = datetime.now()

        # 计算本周四的日期
        this_thursday = now

        # 计算上周五的日期
        last_friday = this_thursday - timedelta(days=7)

        # 格式化日期为 "YYYY-MM-DD"
        formatted_last_friday = last_friday.strftime("%Y-%m-%d")
        formatted_this_thursday = this_thursday.strftime("%Y-%m-%d")

        # 构造 title
        title = f"{project_name}项目周报_{formatted_last_friday}_至_{formatted_this_thursday}"
        # 获取ticket_id
        sql1 = f"SELECT ticketid FROM week_create_info WHERE project_name = '{project_name}'"
        week_create_info = db.get_all(sql1)

        if week_create_info:
            ticket_id1 = week_create_info[-1].get("ticketid")
        else:
            ticket_id1 = '1'
        sql1 = f"SELECT now_time, table_of_contents, details FROM weekly_rief_report " \
               f"WHERE ticketid = '{ticket_id}' "
        sql2 = f"SELECT now_time, table_of_contents, details FROM weekly_rief_report " \
               f"WHERE ticketid = '{ticket_id1}' "
        weekly_info = list(db.get_all(sql2)) + list(db.get_all(sql1))
        completed_and_plan = self.get_basic_info(project_name)
        # 本周完成情况
        completed_this_week = completed_and_plan.get("completed_this_week")
        # 下周计划
        plan_next_week = completed_and_plan.get("plan_next_week")
        html_template = """
                    <!DOCTYPE html>
                    <html lang="en">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <meta name="referrer" content = "no-referrer">
                        <title>周报</title>
                        <style>
                            table,
                            table tr th,
                            table tr td {
                                border:2px solid #dddee0;
                                font-size: 16px;
                            }
                            table tr th {
                                background-color: #fefe23;
                            }
                            table {
                                width: 100%;
                                line-height: 30px;
                                text-align: center;
                                border-collapse: collapse;
                                padding:2px;
                            }
                            .row-header {
                                width: 10%;
                                min-width: 100px;
                                font-weight: bolder;
                                text-align: center;
                            }
                            .row-content {
                                width: 90%;
                                text-align: left;
                                padding-left: 0.75rem;
                                white-space: pre-wrap;
                            }
                            .img-content {
                                display: flex;
                                flex-flow: wrap;
                            }
                            .img-item {
                                margin: 0.5rem;
                            }
                            .img-item img{
                                height: 250px;
                                object-fit: contain;
                            }
                            .img-des {
                                text-align: center;
                            }
                            .red-text{
                                color: #ff0000;
                            }

                        </style>
                        <script type="text/javascript">
                       </script>
                    </head>
                    """

        xg_completed_this_week = '项管暂未填写此内容'
        xg_plan_next_week = '项管暂未填写此内容'
        xg_risk = '项管暂未填写此内容'
        xg_measure = '项管暂未填写此内容'

        query_sql = f"""
            SELECT week_completion,next_week_work_plan,issues_risks_week,solution_measure
            FROM weekly_report_brief
            WHERE project_name = '{project_name}'
              AND STR_TO_DATE(now_time, '%Y-%m-%d') = (
                  SELECT MAX(STR_TO_DATE(now_time, '%Y-%m-%d'))
                  FROM weekly_report_brief
                  WHERE project_name = '{project_name}')
        """
        result = db.get_all(query_sql)
        if result:
            for item in result:
                if item.get("week_completion"):
                    xg_completed_this_week = item.get("week_completion")
                if item.get("next_week_work_plan"):
                    xg_plan_next_week = item.get("next_week_work_plan")
                if item.get("issues_risks_week"):
                    xg_risk = item.get("issues_risks_week")
                if item.get("solution_measure"):
                    xg_measure = item.get("solution_measure")

        # 界面展示信息:第一次周报为空，以后每次从上一次周报中获取
        project_overview = ''
        # 风险
        risk = ''
        # 措施
        measure = ''
        # 状态
        state = ''
        delivery_target = ''
        demand_delivery = ''
        header = ''
        recipient = ''
        Cc = ''
        content = ''
        sql3 = (f"SELECT content,project_overview,risk,measure,plan_next_week,"
                f"delivery_time,delivery_target,recipient,Cc "
                f"FROM weekly_report_template_pm "
                f"WHERE project_name = '{project_name}' "
                f"AND now_time = ( SELECT now_time FROM weekly_report_template_pm "
                f"WHERE project_name = '{project_name}' "
                "ORDER BY STR_TO_DATE(now_time, '%Y-%m-%d %H:%i:%s') DESC LIMIT 1) ")
        overview = db.get_all(sql3)
        if overview:
            project_overview = overview[-1].get('project_overview')
            risk = overview[-1].get('risk')
            measure = overview[-1].get('measure')
            if overview[-1].get('plan_next_week'):
                # 本周工作内容为上周填写的下周计划
                completed_this_week = overview[-1].get('plan_next_week')
                # 下周工作务为上周填写的工作内容，在此基础上修改
                plan_next_week = overview[-1].get('plan_next_week')
            # 需求交付时间
            demand_delivery = overview[-1].get('delivery_time')
            # 交付目标
            delivery_target = overview[-1].get('delivery_target')
            # 收件人
            recipient = overview[-1].get('recipient')
            # 抄送人
            Cc = overview[-1].get('Cc')
            # 邮件内容
            content = overview[-1].get('content')
            # 标题
            header = project_name + '期项目周报' + '-' + state
        if weekly_info:
            grouped_data = {}
            for item in weekly_info:
                time_key = item['now_time']
                if time_key not in grouped_data:
                    grouped_data[time_key] = []
                grouped_data[time_key].append(item)
            new_list = []
            latest_time = None
            for loop_time, group in grouped_data.items():
                if latest_time is None or loop_time > latest_time:
                    latest_time = loop_time
                    new_list = group
            for i in new_list:
                i['now_time'] = i.get('now_time').strftime('%Y-%m-%d %H:%M:%S')
                name = i.get('table_of_contents')
                details = i.get('details', "")
                # 状态
                if name == '状态':
                    state = details
            if not state:
                state = ''
            variables = {
                'project_name': project_name,
                'header': header,
                'delivery_target': delivery_target,
                'project_overview': project_overview,
                'state': state,
                'risk': risk,
                'measure': measure,
                'completed_this_week': completed_this_week,
                'plan_next_week': plan_next_week,
                'html_template_head': html_template,
                'title': title,
                'demand_delivery': demand_delivery,
                'recipient': recipient,
                'Cc': Cc,
                'xg_completed_this_week': xg_completed_this_week,
                'xg_plan_next_week': xg_plan_next_week,
                'xg_risk': xg_risk,
                'xg_measure': xg_measure,
                "content": content
            }
            from iBroker.logone import logger
            logger.info(f"variables:{variables}")

            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            variables = {
                'project_name': project_name,
                'header': header,
                'delivery_target': delivery_target,
                'project_overview': project_overview,
                'state': state,
                'risk': risk,
                'measure': measure,
                'completed_this_week': completed_this_week,
                'plan_next_week': plan_next_week,
                'html_template_head': html_template,
                'title': title,
                'demand_delivery': demand_delivery,
                'recipient': recipient,
                'Cc': Cc,
                'xg_completed_this_week': xg_completed_this_week,
                'xg_plan_next_week': xg_plan_next_week,
                'xg_risk': xg_risk,
                'xg_measure': xg_measure,
                "content": content
            }
            from iBroker.logone import logger
            logger.info(f"variables:{variables}")

            flow.complete_task(self.ctx.task_id, variables=variables)

    def cycle_creat_schedule_ticket(self):
        # 检查周几，非周五不建单
        if datetime.now().isoweekday() != 5:
            flow.complete_task(self.ctx.task_id, variables={"msg": "非周五，不创建工单"})
            return None
        # 获取项目名称列表并转换为集合以提高查找效率
        project_name_set = set(config.get_config_map("pm_weekly_report_project_list"))

        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT campus,project_name FROM summary_data WHERE state = '建设中'"
        sql2 = "SELECT PM, project_name FROM risk_early_warning_data"
        query1 = db.get_all(sql1)
        query2 = db.get_all(sql2)

        ticket_id_list = []

        project_name_set = set(row.get('campus')+row.get('project_name') for row in query1)

        # 收集符合条件的工单信息
        tickets_to_create = [
            {
                "PM": row.get('PM'),
                "project_name": row.get('project_name')
            }
            for row in query2
            if row.get('project_name') in project_name_set
        ]

        # 批量创建工单
        for ticket_info in tickets_to_create:
            ticket_details = gnetops.create_ticket(
                flow_key="PM_weekly_report_template",
                description='该工单为："' + ticket_info['project_name'] + '项目周报"',
                ticket_level=3,
                title=ticket_info['project_name'] + '项目周报',
                creator="v_zongxiyu",
                concern="",  # 关注人, 这里暂为空
                deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=ticket_info  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list.append(ticket_details.get('TicketId'))
            time.sleep(3)  # 确保有间隔时间，避免API限流
        variables = {
            "tickets_to_create": tickets_to_create,
            'ticket_id_list': ticket_id_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMEditingMessage(AjaxTodoBase):
    def __init__(self):
        super(PMEditingMessage).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        pm = process_data.get('PM')
        demand_delivery = process_data.get('demand_delivery')
        title = process_data.get('title')
        recipient = process_data.get('recipient')
        Cc = process_data.get('Cc')
        content = process_data.get('content')
        photo = process_data.get('photo')
        header = process_data.get('header')
        delivery_target = process_data.get('delivery_target')
        project_overview = process_data.get('project_overview')
        completed_this_week = process_data.get('completed_this_week')
        plan_next_week = process_data.get('plan_next_week')
        risk = process_data.get('risk')
        measure = process_data.get('measure')
        project_name = self.ctx.variables.get('project_name')
        ticket_id = self.ctx.variables.get('ticket_id')
        html_template_head = self.ctx.variables.get('html_template_head')
        html_template_body = f"""
                   <body>
                       <table>
                           <thead>
                               <th colspan="2">{header}</th>
                           </thead>
                           {content}
                           <tbody>
                               <tr>
                                   <td class="row-header">交付目标</td>
                                   <td class="row-content">{delivery_target}</td>
                               </tr>
                               <tr>
                                   <td class="row-header">项目概况</td>
                                   <td class="row-content">{project_overview}</td>
                               <tr>
                               <tr>
                                   <td class="row-header">预计交付时间</td>
                                   <td class="row-content">{demand_delivery}</td>
                               <tr>
                               <tr>
                                   <td class="row-header">PM</td>
                                   <td class="row-content">{pm}</td>
                               <tr>
                               <tr>
                                   <td class="row-header">本周工作</td>
                                   <td class="row-content">{completed_this_week}</td>
                               <tr>
                               <tr>
                                   <td class="row-header">下周计划</td>
                                   <td class="row-content">{plan_next_week}</td>
                               <tr>

                               <tr>
                                   <td class="row-header">
                                       <span class="red-text">
                                           风险
                                       </span>
                                   </td>
                                   <td class="row-content">{risk}</td>
                               <tr>

                               <tr>
                                   <td class="row-header">
                                       <span class="red-text">
                                           措施
                                       </span>
                                   </td>
                                   <td class="row-content">{measure} </td>
                               <tr>

                               <tr>
                                   <td class="row-header">现场照片</td>
                                   <td class="row-content">
                                       <div class="img-content">
                   """
        # 接收人
        if recipient:
            recipient_list = recipient.split(';')
            for j in range(len(recipient_list)):
                recipient_list[j] += "@tencent.com"
        else:
            recipient_list = []
        # 邮件抄送人
        if Cc:
            Cc_list = Cc.split(';')
            for j in range(len(Cc_list)):
                Cc_list[j] += "@tencent.com"
        else:
            Cc_list = []
        photo_list = []
        if photo:
            for i in photo:
                try:
                    url = i["response"]["FileList"][0]["url"]
                    name = i.get('name')
                    html_template_body += (
                        f"""<div class="img-item">
                                <img src={url}/>
                                <div class="img-des">
                                    {name}
                                </div>
                            </div>"""
                    )
                    photo_list.append({
                        "url": url,
                        "name": name
                    })
                except IndexError as e:
                    return {"code": -1, "msg": e}
                except KeyError as e:
                    return {"code": -1, "msg": e}

        html_template_body += """

                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </body>
            </html>
        """
        data = {
            'project_name': project_name,
            'ticket_id': ticket_id,
            'title': title,
            'recipient': recipient,
            'Cc': Cc,
            'content': content,
            'header': header,
            'photo': json.dumps(photo_list),
            'delivery_target': delivery_target,
            'project_overview': project_overview,
            'completed_this_week': completed_this_week,
            'plan_next_week': plan_next_week,
            'risk': risk,
            'measure': measure,
            'delivery_time': demand_delivery,
            'pm': pm
        }
        variables = {
            'title': title,
            'content': content,
            'recipient_list': recipient_list,
            'Cc_list': Cc_list,
            'recipient': recipient,
            'Cc': Cc,
            'html_template': html_template_head + html_template_body,
            'photo_list': photo_list,
            'photo': photo,
            'html_template_body': html_template_body,
            'data': data,
            'header': header,
            'delivery_target': delivery_target,
            'project_overview': project_overview,
            'risk': risk,
            'measure': measure,
            'completed_this_week': completed_this_week,
            'plan_next_week': plan_next_week,
            'demand_delivery': demand_delivery,
            'PM': pm

        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMMessageSend(AjaxTodoBase):
    """
    邮件确认、发送
    """

    def __init__(self):
        super(PMMessageSend).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        choose = process_data.get("choose")
        title = self.ctx.variables.get('title')
        recipient_list = self.ctx.variables.get('recipient_list')
        Cc_list = self.ctx.variables.get('Cc_list')
        html_template = self.ctx.variables.get('html_template')
        if choose == '修改':
            variables = {
                'choose': choose,
            }

        else:
            tof.send_email(sendTitle=title, msgContent=html_template,
                           sendTo=recipient_list,
                           sendCopy=Cc_list)
            variables = {
                'choose': choose,
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMMessageStorage(object):
    def __init__(self):
        super(PMMessageStorage).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_storage(self, data):
        now = datetime.now()
        current_time = now.strftime("%Y-%m-%d %H:%M:%S")
        data['now_time'] = current_time
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("weekly_report_template_pm", data)
        tb_db.commit()
        insert_data = {
            'data': data
        }
        flow.complete_task(self.ctx.task_id, variables={"insert_data": insert_data})

    def PM_weekly_report_automatic_creat(self):
        res_list = []
        project = []
        project_info = []
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT project_name,handlers FROM week_create_info WHERE status = 0"
        info = db.get_all(sql)
        for i in info:
            project_name = i.get('project_name')
            PM = i.get('handlers')
            if project_name not in project:
                project.append(project_name)
                project_info.append({
                    'project_name': project_name,
                    'PM': PM
                })
        for i in project_info:
            project_name = i.get('project_name')
            PM = i.get('PM')
            data = {
                "CustomVariables": {
                    "PM": PM,
                    "project_name": project_name
                },
                "ProcessDefinitionKey": "PM_weekly_report_template",
                "Source": "",
                "TicketDescription": f"{project_name}项目PM周报",
                "TicketLevel": "3",
                "TicketTitle": f"{project_name}项目PM周报",
                "UserInfo": {
                    "Concern": "keketan;youngshi;v_zongxiyu",
                    "Creator": "youngshi",
                    "Deal": "youngshi"
                }
            }
            time.sleep(1)
            # 起单，并抛入data
            res = gnetops.request(action="Ticket", method="Create", data=data)
            res_list.append({
                "project_name": project_name,
                "res": res
            })
        variables = {
            'res_list': res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def hun_yuan(self, message):
        info = gnetops.request(
            action="HunYuan",
            method="QtextA",
            ext_data={
                "message": message,
            },
        )

        if info.get('body').get('choices'):
            data = info.get('body').get('choices')[0].get('message').get('content')
            return data

        else:
            return "错误！请稍后重试！"
