from datetime import datetime
import json

import requests
from iBroker.lib.sdk import gnetops
from iBroker.lib import curl
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql


class UploadFiles(AjaxTodoBase):
    """
        导入BOM文件
    """

    def __init__(self):
        super(UploadFiles, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def whether_obtain_material_id_one(self, bom_data1: list, bom_data2: list):
        bom_number1 = {
            "MaterialCode": bom_data1
        }
        bom_number2 = {
            "MaterialCode": bom_data2
        }
        response1 = gnetops.request(action="Tbconstruct", method="ObtainMaterialInfo", ext_data=bom_number1)
        response2 = gnetops.request(action="Tbconstruct", method="ObtainMaterialInfo", ext_data=bom_number2)
        response_data1 = response1  # 返回的数据
        response_data2 = response2  # 返回的数据
        if "materialList" in response_data1 and "materialList" in response_data2:
            material_list = [item["materialCode"] for item in response_data1.get("materialList", []) if
                             item.get("materialId") == 0]
            material_list += [item["materialCode"] for item in response_data2.get("materialList", []) if
                              item.get("materialId") == 0]

            if material_list:
                return {"code": -1,
                        "msg": f"BOM编号,{material_list},的物料id为0，请重新上传"}, True, response_data1, response_data2
            else:
                return {"code": 0, "msg": f"{material_list}上传正确"}, False, response_data1, response_data2

    def end(self, process_data, process_user):
        global bools_one, data_one, bools_two, data_two
        project_name = self.ctx.variables.get('project_name')
        ticket_id = self.ctx.variables.get('ticket_id')
        tableList = process_data.get('tableList')
        tableList2 = process_data.get('tableList2')
        tableList_data = []
        tableList2_data = []
        if tableList is not None and tableList2 is not None:
            for row in tableList:
                level_number = row.get('level_number')
                level1 = row.get('level1')
                level1Name = row.get('level1Name')
                level2 = row.get('level2')
                level2Name = row.get('level2Name')
                quantity = row.get('quantity')
                BOM = row.get('BOM')
                model = row.get('model')
                remark = row.get('remark')
                # 设备类型
                material_category_name = row.get('materialCategoryName')
                # 型号
                material_name = row.get('materialName')

                tableList_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'level_number': level_number,
                    'leve_one': level1,
                    'leve_one_product_name': level1Name,
                    'leve_two': level2,
                    'leve_two_product_name': level2Name,
                    'quantity': quantity,
                    'bom_coding': BOM,
                    'model': model,
                    'remark': remark,
                    'material_category_name': material_category_name,
                    'material_name': material_name
                })
            for row in tableList2:
                level_number = row.get('level_number')
                level1 = row.get('level1')
                level1Name = row.get('level1Name')
                level2 = row.get('level2')
                level2Name = row.get('level2Name')
                quantity = row.get('quantity')
                BOM = row.get('BOM')
                model = row.get('model')
                remark = row.get('remark')
                # 服务商
                material_name = row.get('materialName')
                # 服务商单位名称
                brand = row.get('brand')

                tableList2_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'level_number': level_number,
                    'leve_one': level1,
                    'leve_one_product_name': level1Name,
                    'leve_two': level2,
                    'leve_two_product_name': level2Name,
                    'quantity': quantity,
                    'bom_coding': BOM,
                    'model': model,
                    'remark': remark,
                    'material_name': material_name,
                    'brand': brand

                })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if tableList_data:
            tb_db.insert_batch("bom_inventory_details", tableList_data)
        if tableList2_data:
            tb_db.insert_batch("bom_inventory_data", tableList2_data)
        tb_db.commit()
        bom_data1 = []
        # 取出第一个文件的所有非空的bom编码
        for row in tableList_data:
            bom_coding = row.get('bom_coding')
            if bom_coding is not None and bom_coding != 'null':
                bom_data1.append(
                    bom_coding
                )
        bom_data2 = []
        # 取出第一个文件的所有非空的bom编码
        for row in tableList2_data:
            bom_coding = row.get('bom_coding')
            if bom_coding is not None and bom_coding != 'null':
                bom_data2.append(
                    bom_coding
                )
        if bom_data1:
            data_one, bools_one, response_data1, response_data2 = self.whether_obtain_material_id_one(bom_data1,
                                                                                                      bom_data2)

        if bools_one:
            return data_one
        variables = {
            'tableList': tableList,
            'tableList2': tableList2,
            'tableList_data': tableList_data,
            'tableList2_data': tableList2_data,
            'bom_data1': bom_data1,
            'bom_data2': bom_data2,
            'response_data1': response_data1,
            'response_data2': response_data2
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BomInventoryDetails(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_project_number(self):
        """
            获取项目编号
        """
        project_name = self.ctx.variables.get('project_name')
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT dcopsTicketId,item_number,PM,demand_distribution " \
                    "FROM risk_early_warning_data " \
                    f"WHERE project_name = '{project_name}'"
        result = db.get_all(query_sql)
        demand_distribution = ''
        for row in result:
            dcopsTicketId = row.get('dcopsTicketId')
            item_number = row.get('item_number')
            demand_distribution = row.get('demand_distribution')
            PM = row.get('PM')

        # 获取信息
        db = mysql.new_mysql_instance('tbconstruct')
        sql = "SELECT dd.idcrmProjectCode,dd.projectName,dd.country,dd.region,dd.city,dd.campus,dd.demandLevel, " \
              "dd.bgExpectDeliveryTime,dd.building,dd.demandReason,dd.buildType,dd.assetType,dd.idcType, " \
              "dd.constructDesc,dd.constructionModel,dd.expectRackConsumesTempo,dd.expansionPlans, " \
              "dd.idcRoomfunction,dd.rackSinglePower,dd.rackTotalCapacity,dd.otherDemand,dd.id " \
              "FROM risk_early_warning_data rewd " \
              "JOIN demand_distribution dd ON rewd.item_number = dd.idcrmProjectCode " \
              f"WHERE rewd.project_name = '{project_name}' "

        basic_result = db.get_all(sql)
        basic_info = {}
        computer_room_needs = {}
        net_needs = {}
        optical_cable_needs = {
            "cross_building_list": [],
            "inside_building_list": []
        }
        rack_needs = {
            "rack_delivery_time_list": [],
            "dedicated_rack_needs_list": [],
            "access_room_rack_needs_list": []
        }
        demand_id = ''
        # 基本信息
        if basic_result:
            for basic in basic_result:
                demand_id = str(basic.get('id'))
                basic_info = basic

            # 机房需求
            sql1 = "SELECT idcLevel,idcBearing,btyBackupTime,idcIncRouter,expandableSpace,isOperatorsNeutral, " \
                   f"idcRemark FROM demand_info WHERE demand_id = '{demand_id}' "
            room_result = db.get_all(sql1)
            if room_result:
                for room in room_result:
                    computer_room_needs = room

            # 网平需求
            sql2 = "SELECT comRoomCount,rackCount,rackPduPort,pduDes,ocFiberRoutSize,copCablesRoutSize, " \
                   "routerDemand,odfAccDemand,odfAccCount,odfAccCabType,odfCabDemand " \
                   f"FROM demand_info WHERE demand_id = '{demand_id}' "
            net_result = db.get_all(sql2)
            if net_result:
                for net in net_result:
                    net_needs = net

            # 光缆需求信息
            sql3 = f"SELECT * FROM demend_fiber WHERE demand_id = '{demand_id}' "
            optical_cable_result = db.get_all(sql3)
            if optical_cable_result:
                for optical in optical_cable_result:
                    if optical.get('ocFilberCount'):
                        optical_cable_needs["inside_building_list"].append(optical)
                    else:
                        optical_cable_needs["cross_building_list"].append(optical)

            # 机架需求信息：机架交付时间
            sql4 = f"SELECT * FROM demand_rack_require_devlist WHERE demand_id = '{demand_id}' "
            rack_require_devlist_result = db.get_all(sql4)
            if rack_require_devlist_result:
                for rack in rack_require_devlist_result:
                    if demand_distribution:
                        date_str1 = demand_distribution
                        date_str2 = rack.get('deliveryTime')
                        date_format = "%Y-%m-%d"
                        date1 = datetime.strptime(date_str1, date_format).date()
                        date2 = datetime.strptime(date_str2, date_format).date()
                        diff = date2 - date1
                        days_diff = diff.days
                        rack['demand_distribution'] = demand_distribution
                        rack['lead_time'] = days_diff
                    else:
                        rack['demand_distribution'] = 'demand_distribution'
                        rack['lead_time'] = 'days_diff'
                    rack_needs['rack_delivery_time_list'].append(rack)

            # 机架需求信息：专用机架需求
            sql5 = f"SELECT * FROM demand_rack_require_spclist WHERE demand_id = '{demand_id}' "
            rack_require_spclist_result = db.get_all(sql5)
            if rack_require_spclist_result:
                for spclist in rack_require_spclist_result:
                    rack_needs['dedicated_rack_needs_list'].append(spclist)

            # 机架需求信息：接入间机架需求
            sql6 = f"SELECT * FROM demand_rack_require_acclist WHERE demand_id = '{demand_id}' "
            rack_require_acclist_result = db.get_all(sql6)
            if rack_require_acclist_result:
                for acclist in rack_require_acclist_result:
                    rack_needs['access_room_rack_needs_list'].append(acclist)

        flow.complete_task(self.ctx.task_id, variables={
            'dcopsTicketId': dcopsTicketId,
            'item_number': item_number,
            'PM': PM,
            'basic_info': basic_info,
            "computer_room_needs": computer_room_needs,
            "net_needs": net_needs,
            "optical_cable_needs": optical_cable_needs,
            "rack_needs": rack_needs
        })

    def return_pm_information(self):
        """
            返回PM信息
        """
        dcopsTicketId = self.ctx.variables.get("dcopsTicketId")
        item_number = self.ctx.variables.get("item_number")
        PM = self.ctx.variables.get("PM")
        ticket_id = self.ctx.variables.get("ticket_id")
        insert_data = []

        PM_data = {
            "instanceId": dcopsTicketId,  # 流程单号
            "idcrmProjectCode": item_number,  # 项目编号
            "auditResult": True,  # 需求判断结果
            "suggestion": "",  # 驳回理由，通过意见
            "idcPM": PM  # 分配的PM
        }
        PM_request = curl.post(title="Dcops请求数全通平台接口",
                               system_name="Dcops",
                               url="http://idcrm.woa.com/newapi/construct/UpdateConstructResult",
                               postdata=json.dumps(PM_data),
                               append_header={"Content-Type": "application/json"})
        PM_list = PM_request  # 返回的数据
        for row in PM_list:
            idcrmOrderCode = row.get("idcrmOrderCode")
            instanceId = row.get("instanceId")
            idcrmProjectCode = row.get("idcrmProjectCode")
            insert_data.append({
                "ticket_id": ticket_id,
                "idcrmOrderCode": idcrmOrderCode,
                "instanceId": instanceId,
                "idcrmProjectCode": idcrmProjectCode
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("construction_result", insert_data)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id, variables={
            'PM_data': PM_data,
            'PM_list': PM_list,
            "insert_data": insert_data
        })

    def return_equipment_list(self, response_data1):
        """
            返回设备清单
        """
        tableList_data = self.ctx.variables.get("tableList_data")
        dcopsTicketId = self.ctx.variables.get("dcopsTicketId")
        item_number = self.ctx.variables.get("item_number")
        # equipment_data  返回设备信息
        equipment_data = {
            "instanceId": str(dcopsTicketId),  # I平流程单号
            "idcrmProjectCode": str(item_number),  # 项目编号
            "type": 1,  # 流程节点：1. 设备清单生成；2. 到货验收；3. 初验交付；4. 终验交付
            "operatorId": "",  # 操作人id 仅 type = 2, 3, 4 时， 表示为 收货人, 初验人， 终验人
            "serverList": []
        }
        # 将equipment_data中的materialCode与tableList_data中的bom_coding相匹配
        for item in tableList_data:
            for material in response_data1["materialList"]:
                if item["bom_coding"] == material["materialCode"]:
                    material_query_dto1 = material["materialQueryDTO"]
                    server_item = {
                        "id": "",
                        "materialCode": str(material["materialCode"]),
                        "materialId": str(material["materialId"]),
                        "device": material_query_dto1.get("materialCategoryName") if material_query_dto1 and isinstance(
                            material_query_dto1, dict) else None,
                        "description": "",
                        "model": material_query_dto1.get("materialName") if material_query_dto1 and isinstance(
                            material_query_dto1, dict) else None,
                        "amount": item.get("quantity"),
                        "deviceType": material_query_dto1.get(
                            "materialCategoryName") if material_query_dto1 and isinstance(material_query_dto1,
                                                                                          dict) else None,
                        "expectedArrivalTime": "",
                        "materialRemark": item["remark"]
                    }
                    equipment_data["serverList"].append(server_item)
        equipment_request = curl.post(title="Dcops请求数全通平台接口",
                                      system_name="Dcops",
                                      url="http://idcrm.woa.com/newapi/idcplatform/SyncEquipmentList",
                                      postdata=json.dumps(equipment_data),
                                      append_header={"Content-Type": "application/json"})
        equipment_list = equipment_request  # 返回的数据
        flow.complete_task(self.ctx.task_id, variables={
            'response_data1': response_data1,
            'equipment_data': equipment_data,
            'equipment_list': equipment_list
        })

    def return_construction_service(self, response_data2):
        """
            返回机电建设服务
        """
        tableList2_data = self.ctx.variables.get("tableList2_data")
        dcopsTicketId = self.ctx.variables.get("dcopsTicketId")
        item_number = self.ctx.variables.get("item_number")
        # construction_data  机电建设服务
        construction_data = {
            "instanceId": str(dcopsTicketId),  # I平流程单号
            "idcrmProjectCode": str(item_number),  # 项目编号
            "type": 1,  # 流程节点：1. 设备清单生成；2. 到货验收；3. 初验交付；4. 终验交付
            "operatorId": "",  # 操作人id 仅 type = 2, 3, 4 时， 表示为 收货人, 初验人， 终验人
            "elecBuildingList": []
        }
        # 将construction_data中的materialCode与tableList2_data中的bom_coding相匹配
        number = 0
        for item in tableList2_data:
            for material in response_data2["materialList"]:
                if item["bom_coding"] == material["materialCode"]:
                    material_query_dto2 = material["materialQueryDTO"]
                    server_item = {
                        "id": None,
                        'number': number + 1,
                        "materialCode": str(material["materialCode"]),
                        "materialId": str(material["materialId"]),
                        "serviceName": material_query_dto2.get("materialName") if isinstance(
                            material_query_dto2,
                            dict) else "",
                        "serviceProviderName": material_query_dto2.get("brand") if isinstance(
                            material_query_dto2,
                            dict) else "",
                        "materialRemark": item["remark"],
                        "amount": item.get("quantity")
                    }
                    construction_data["elecBuildingList"].append(server_item)
                    number += 1
        construction_request = curl.post(title="Dcops请求数全通平台接口",
                                         system_name="Dcops",
                                         url="http://idcrm.woa.com/newapi/idcplatform/SyncElecBuildingList",
                                         postdata=json.dumps(construction_data),
                                         append_header={"Content-Type": "application/json"})
        construction_list = construction_request  # 返回的数据
        flow.complete_task(self.ctx.task_id, variables={
            'response_data2': response_data2,
            'construction_data': construction_data,
            'construction_list': construction_list
        })
