# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: audit.py
# @time: 2023/3/7 11:05
# @描述: 【请添加描述】
import time
import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.logone import logger
from iBroker.lib import mysql


# 需求侧总监审批
class RequirementChiefAudit(AjaxTodoBase):
    def __init__(self):
        super(RequirementChiefAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        requirement_id = self.ctx.variables.get("requirement_id")
        # 获取第二天的时间，格式为2020-01-01
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        tb_db = mysql.new_mysql_instance("tb_change")
        reuqir_chief_audit_user = self.ctx.variables.get("reuqir_chief_audit_user")

        tb_db.insert("requirement_cron", {
            "RequirementId": requirement_id,
            "TicketId": self.ctx.variables.get("TicketId"),
            "TaskId": self.ctx.task_id,
            "NotifyUser": reuqir_chief_audit_user,
            "NotifyType": "mail",
            "Content": "您有一个来自TB变更的预算审核任务，请及时处理！",
            "Status": "wait",
            "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "NotifyDate": tomorrow,
            "TodoKey": "RequirementChiefAudit"
        })

        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "需求总监审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "RequirementChiefAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "requirement_chief_audit_gateway": 1 if audit_result else 0
        }

        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "RequirementChiefAudit",
                     })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# IDC总监审批
class IdcChiefAudit(AjaxTodoBase):
    def __init__(self):
        super(IdcChiefAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        requirement_id = self.ctx.variables.get("requirement_id")
        idc_chief_audit_user = self.ctx.variables.get("idc_chief_audit_user")

        # 获取审批结果
        idc_audit_list = self.ctx.variables.get("idc_audit_list")
        pass_able = False

        for audit in idc_audit_list:
            if audit.get("audit_user") == idc_chief_audit_user:
                if audit.get("audit_status") == "通过":
                    pass_able = True
                break

        if pass_able:
            idc_audit_list.append({
                "task": "运营中心总监审批",
                "audit_status": "通过",
                "audit_opinion": "审核人重复，自动跳过",
                "audit_user": idc_chief_audit_user,
                "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "todo_key": "IdcChiefAudit"
            })

            variables = {
                "idc_audit_list": idc_audit_list,
                "idc_chief_audit_gateway": 1
            }
            tb_db = mysql.new_mysql_instance("tb_change")
            tb_db.update("requirement_cron", {
                "Status": "end"
            }, {
                             "TicketId": self.ctx.variables.get("TicketId"),
                             "TaskId": self.ctx.task_id,
                             "TodoKey": "IdcChiefAudit",
                         })

            # 结束dcops待办
            GnetopsTodoEnd(self.ctx.task_id, idc_chief_audit_user)
            # 流程流转
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            # 获取第二天的时间，格式为2020-01-01
            tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
            tb_db = mysql.new_mysql_instance("tb_change")

            tb_db.insert("requirement_cron", {
                "RequirementId": requirement_id,
                "TicketId": self.ctx.variables.get("TicketId"),
                "TaskId": self.ctx.task_id,
                "NotifyUser": idc_chief_audit_user,
                "NotifyType": "mail",
                "Content": "您有一个来自TB变更的预算审核任务，请及时处理！",
                "Status": "wait",
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "NotifyDate": tomorrow,
                "TodoKey": "IdcChiefAudit"
            })

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "运营中心总监审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "IdcChiefAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "idc_chief_audit_gateway": 1 if audit_result else 0
        }
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "IdcChiefAudit",
                     })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# GM审批
class GmAudit(AjaxTodoBase):
    def __init__(self):
        super(GmAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        requirement_id = self.ctx.variables.get("requirement_id")
        # 获取第二天的时间，格式为2020-01-01
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        idc_gm_audit_user = self.ctx.variables.get("idc_gm_audit_user")

        # 获取审批结果
        idc_audit_list = self.ctx.variables.get("idc_audit_list")
        pass_able = False

        for audit in idc_audit_list:
            if audit.get("audit_user") == idc_gm_audit_user:
                if audit.get("audit_status") == "通过":
                    pass_able = True
                break

        if pass_able:
            idc_audit_list.append({
                "task": "GM审批",
                "audit_status": "通过",
                "audit_opinion": "审核人重复，自动跳过",
                "audit_user": idc_gm_audit_user,
                "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "todo_key": "GmAudit"
            })

            variables = {
                "idc_audit_list": idc_audit_list,
                "gm_audit_gateway": 1
            }

            tb_db = mysql.new_mysql_instance("tb_change")
            tb_db.update("requirement_cron", {
                "Status": "end"
            }, {
                             "TicketId": self.ctx.variables.get("TicketId"),
                             "TaskId": self.ctx.task_id,
                             "TodoKey": "GmAudit",
                         })

            # 结束dcops待办
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            # 流程流转
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            tb_db = mysql.new_mysql_instance("tb_change")
            tb_db.insert("requirement_cron", {
                "RequirementId": requirement_id,
                "TicketId": self.ctx.variables.get("TicketId"),
                "TaskId": self.ctx.task_id,
                "NotifyUser": idc_gm_audit_user,
                "NotifyType": "mail",
                "Content": "您有一个来自TB变更的预算审核任务，请及时处理！",
                "Status": "wait",
                "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "NotifyDate": tomorrow,
                "TodoKey": "GmAudit"
            })

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "GM审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "GmAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "gm_audit_gateway": 1 if audit_result else 0
        }

        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "GmAudit",
                     })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# 财管审批
class FinancialManagerAudit(AjaxTodoBase):
    def __init__(self):
        super(FinancialManagerAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "财管审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "FinancialManagerAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "financial_manager_audit_gateway": 1 if audit_result else 0
        }

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# Doa审批
class DoaAudit(AjaxTodoBase):
    def __init__(self):
        super(DoaAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "DOA审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "DoaAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "doa_audit_gateway": 1 if audit_result else 0
        }

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# 审核驳回
class AuditReject:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("AuditReject-ServiceMethod-" + str(time.time()))

        # 同步审批信息
        result = gnetops.request(action="TbChangeJoint", method="SendAuditResultToStars", data={
            "RequirementId": self.ctx.variables.get("requirement_id"),
            "AuditResult": 2
        })

        self.workflow_detail.add_kv_table("同步审批信息至星辰", {
            "SendAuditResultToStars": result
        })

        # 取消财经预算
        budget_result = gnetops.request(action="TbChangeJoint", method="CancelBudget", data={
            "RequirementId": self.ctx.variables.get("requirement_id"),
            "CreatedBy": "dcops-auto"
        })

        self.workflow_detail.add_kv_table("取消财经预算", {
            "CancelBudget": budget_result
        })

        # 审批信息入库
        audit_list = self.ctx.variables.get("audit_list")
        idc_audit_list = self.ctx.variables.get("idc_audit_list")
        requirement_id = self.ctx.variables.get("requirement_id")

        batch_insert_list = []
        for audit in audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": "RequirementAudit",
                "AuditUser": audit.get("audit_people"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_reason")
            })

        for audit in idc_audit_list:
            batch_insert_list.append({
                "RequirementId": requirement_id,
                "TodoKey": audit.get("todo_key"),
                "AuditUser": audit.get("audit_user"),
                "AuditTime": audit.get("audit_time"),
                "AuditResult": audit.get("audit_status"),
                "AuditOpinion": audit.get("audit_opinion")
            })
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.insert_batch("tb_change_audit_history", batch_insert_list)

        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 接收商务GM审批信息
class GmAuditInfo:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("GmAuditInfo-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 审核驳回
class DoaGateWay:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("DoaGateWay-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)
