import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import config
from iBroker.sdk.notification.chatops import ChatOpsSend
from biz.wework_dept.wework_dept_api import (
    WeworkDeptApi,
)


# 企业微信组织架构申请
class WeworkDeptApply(AjaxTodoBase):
    def __init__(self):
        super(WeworkDeptApply, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        apply_dept_list = process_data.get("apply_dept_list")
        if not apply_dept_list:
            return {"code": -1, "message": "申请组织架构不能为空"}

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "apply_dept_list": apply_dept_list,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 企业微信组织架构开通
class WeworkDeptActivate(AjaxTodoBase):
    def __init__(self):
        super(WeworkDeptActivate, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        apply_dept_list_confirm = process_data.get("apply_dept_list_confirm")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "apply_dept_list_confirm": apply_dept_list_confirm,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 企业微信组织架构确认
class WeworkDeptConfirm(AjaxTodoBase):
    def __init__(self):
        super(WeworkDeptConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转流程
        flow.complete_task(self.ctx.task_id)


# 企业微信组织架构开通
class WeworkDeptApplication(AjaxTodoBase):
    def __init__(self):
        super(WeworkDeptApplication, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        create_dept_list = process_data.get("create_dept_list")
        dept_list = WeworkDeptApi.query_dept()
        dept_name_list = [item.get("label") for item in dept_list]
        no_dept_list = []
        for dept in create_dept_list:
            if dept not in dept_name_list:
                no_dept_list.append(dept)
        if no_dept_list:
            return {
                "code": -1,
                "msg": f"暂未查询到申请开通的组织架构: {(','.join(no_dept_list))} <br>请确认是否开通成功！",
            }
        # 项目
        project_name_list = self.ctx.variables.get("project_name_list")
        # 建单人（一般是PM）
        creator = self.ctx.variables.get("Creator")
        # 邮件推送配置信息
        eml_cfg = config.get_config_map("wework_dept_apply_email_info")
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        # 项目去重
        project_name_list = list(set(project_name_list))
        # 给 建单人（一般是PM）推送消息
        eml_cfg_confirm = eml_cfg.get("confirm")
        account_apply_page = eml_cfg_confirm.get("account_apply_page")
        receiver = [creator]
        title = eml_cfg_confirm.get("title")
        content = eml_cfg_confirm.get("content")
        Cc = eml_cfg_confirm.get("Cc")
        Cc.append(flow_responsible_person)
        Cc = list(set(Cc))
        email_title = f"{(',').join(project_name_list)}{title}"
        email_content = (
            f"{(',').join(receiver)}：<br>"
            f"<p style='text-indent: 2em;'>{(', ').join(project_name_list)}{content}</p>"
            f"<p style='text-indent: 2em;'>账号开通入口：<a href='{account_apply_page}'> {account_apply_page}</a></p>"
            f"<p style='text-indent: 2em;'>谢谢！</p>"
        )
        email_receiver = receiver
        email_Cc = Cc
        for i in range(len(email_Cc)):
            email_Cc[i] += "@tencent.com"
        for j in range(len(email_receiver)):
            email_receiver[j] += "@tencent.com"
        tof.send_email(
            sendTitle=email_title,
            msgContent=email_content,
            sendTo=email_receiver,
            sendCopy=email_Cc,
        )
        email_info_confirm = {
            "email_title": email_title,
            "email_content": email_content,
            "email_receiver": email_receiver,
            "email_Cc": email_Cc,
            "receiver": receiver,
        }
        msg_receiver = [
            creator,
            flow_responsible_person,
            *eml_cfg_confirm.get("Cc", []),
        ]
        msg_receiver = list(set(msg_receiver))
        msg_title = f"{(',').join(project_name_list)}{title}"
        msg_message = f"{(', ').join(project_name_list)}{content}"
        # 应用消息
        tof.send_company_wechat_message(
            receivers=msg_receiver, title=msg_title, message=msg_message
        )
        msg_info = {
            "msg_receiver": msg_receiver,
            "msg_title": msg_title,
            "msg_message": msg_message,
        }
        # dcops消息
        for account in msg_receiver:
            ChatOpsSend(
                user_name=account,
                msg_content=msg_message,
                msg_type="text",
                des_type="single",
            ).call()
        variables = {
            "eml_cfg_confirm": eml_cfg_confirm,
            "email_info_confirm": email_info_confirm,
            "msg_info": msg_info,
        }
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)
