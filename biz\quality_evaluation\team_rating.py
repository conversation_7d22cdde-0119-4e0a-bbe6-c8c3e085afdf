from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.quality_evaluation.rating_handle import TeamRatingHandle
from biz.quality_evaluation.files_action import QualityEvaluationFileAction


# 评分汇总
class RatingSummary(AjaxTodoBase):
    def __init__(self):
        super(RatingSummary, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        all_team_rating_standard_list = process_data.get(
            "all_team_rating_standard_list"
        )
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_standard_list(
            all_team_rating_standard_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        project_category_score_list = process_data.get("project_category_score_list")
        is_recalculate = process_data.get("is_recalculate", 0)
        variables = {
            "all_team_rating_standard_list": all_team_rating_standard_list,
            "project_category_score_list": project_category_score_list,
            "is_recalculate": is_recalculate,
            "leader_approval": 1,
            "leader_remark": "",
            "director_approval": 1,
            "director_remark": "",
            "GM_approval": "同意",
            "GM_remark": "",
            "leader_reject_reason": "",
            "director_reject_reason": "",
            "GM_reject_reason": "",
        }
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 商务打分
class BusinessRating(AjaxTodoBase):
    def __init__(self):
        super(BusinessRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取商务评分数据
        team_rating_list = process_data.get("business_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取取商评分详情数据(评分数据+标准数据)
        business_rating_standard_list = TeamRatingHandle.get_team_rating_standard_list(
            team_rating_list, project_category_basic_info_dict
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 商务评分上传文件
        business_file = process_data.get("business_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                business_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        business_file_json = QualityEvaluationFileAction.get_file_json(
            business_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "business_rating_list": team_rating_list,
            "business_rating_standard_list": business_rating_standard_list,
            "business_rater_leader_approval": 1,
            "business_rater_leader_remark": "",
            "business_rater_leader_reject_reason": "",
            "business_file": business_file,
            "business_file_json": business_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 供应链打分
class SupplyChainRating(AjaxTodoBase):
    def __init__(self):
        super(SupplyChainRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取供应链评分数据
        team_rating_list = process_data.get("supply_chain_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取供应链评分详情数据(评分数据+标准数据)
        supply_chain_rating_standard_list = (
            TeamRatingHandle.get_team_rating_standard_list(
                team_rating_list, project_category_basic_info_dict
            )
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 供应链评分上传文件
        supply_chain_file = process_data.get("supply_chain_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                supply_chain_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        supply_chain_file_json = QualityEvaluationFileAction.get_file_json(
            supply_chain_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "supply_chain_rating_list": team_rating_list,
            "supply_chain_rating_standard_list": supply_chain_rating_standard_list,
            "supply_chain_rater_leader_approval": 1,
            "supply_chain_rater_leader_remark": "",
            "supply_chain_rater_leader_reject_reason": "",
            "supply_chain_file": supply_chain_file,
            "supply_chain_file_json": supply_chain_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 规划打分
class PlanningRating(AjaxTodoBase):
    def __init__(self):
        super(PlanningRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取规划评分数据
        team_rating_list = process_data.get("planning_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取规划评分详情数据(评分数据+标准数据)
        planning_rating_standard_list = TeamRatingHandle.get_team_rating_standard_list(
            team_rating_list, project_category_basic_info_dict
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 规划评分上传文件
        planning_file = process_data.get("planning_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                planning_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        planning_file_json = QualityEvaluationFileAction.get_file_json(
            planning_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "planning_rating_list": team_rating_list,
            "planning_rating_standard_list": planning_rating_standard_list,
            "planning_rater_leader_approval": 1,
            "planning_rater_leader_remark": "",
            "planning_rater_leader_reject_reason": "",
            "planning_file": planning_file,
            "planning_file_json": planning_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 产品打分
class ProductRating(AjaxTodoBase):
    def __init__(self):
        super(ProductRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取产品评分数据
        team_rating_list = process_data.get("product_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取产品评分详情数据(评分数据+标准数据)
        product_rating_standard_list = TeamRatingHandle.get_team_rating_standard_list(
            team_rating_list, project_category_basic_info_dict
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 产品评分上传文件
        product_file = process_data.get("product_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                product_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        product_file_json = QualityEvaluationFileAction.get_file_json(
            product_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "product_rating_list": team_rating_list,
            "product_rating_standard_list": product_rating_standard_list,
            "product_rater_leader_approval": 1,
            "product_rater_leader_remark": "",
            "product_rater_leader_reject_reason": "",
            "product_file": product_file,
            "product_file_json": product_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 项目打分
class ProjectRating(AjaxTodoBase):
    def __init__(self):
        super(ProjectRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取项目评分数据
        team_rating_list = process_data.get("project_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取项目评分详情数据(评分数据+标准数据)
        project_rating_standard_list = TeamRatingHandle.get_team_rating_standard_list(
            team_rating_list, project_category_basic_info_dict
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 项目评分上传文件
        project_file = process_data.get("project_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                project_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        project_file_json = QualityEvaluationFileAction.get_file_json(
            project_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "project_rating_list": team_rating_list,
            "project_rating_standard_list": project_rating_standard_list,
            "project_rater_leader_approval": 1,
            "project_rater_leader_remark": "",
            "project_rater_leader_reject_reason": "",
            "project_file": project_file,
            "project_file_json": project_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 优化打分
class OptimizeRating(AjaxTodoBase):
    def __init__(self):
        super(OptimizeRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取优化评分数据
        team_rating_list = process_data.get("optimize_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取优化评分详情数据(评分数据+标准数据)
        optimize_rating_standard_list = TeamRatingHandle.get_team_rating_standard_list(
            team_rating_list, project_category_basic_info_dict
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 优化评分上传文件
        optimize_file = process_data.get("optimize_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                optimize_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        optimize_file_json = QualityEvaluationFileAction.get_file_json(
            optimize_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "optimize_rating_list": team_rating_list,
            "optimize_rating_standard_list": optimize_rating_standard_list,
            "optimize_rater_leader_approval": 1,
            "optimize_rater_leader_remark": "",
            "optimize_rater_leader_reject_reason": "",
            "optimize_file": optimize_file,
            "optimize_file_json": optimize_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 弱电打分
class WeakElectricityRating(AjaxTodoBase):
    def __init__(self):
        super(WeakElectricityRating, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取弱电评分数据
        team_rating_list = process_data.get("weak_electricity_rating_list", None)
        project_category_basic_info_dict = self.ctx.variables.get(
            "project_category_basic_info_dict"
        )
        # 校验评分数据
        msg, checkoutflag = TeamRatingHandle.checkout_team_rating_list(
            team_rating_list, project_category_basic_info_dict
        )
        if not checkoutflag:
            return msg
        # 获取弱电评分详情数据(评分数据+标准数据)
        weak_electricity_rating_standard_list = (
            TeamRatingHandle.get_team_rating_standard_list(
                team_rating_list, project_category_basic_info_dict
            )
        )
        # 增加"打分人"字段（团队领导审批需看到打分人）
        team_rating_list = TeamRatingHandle.add_field(
            team_rating_list, "rater", process_user
        )
        # 弱电评分上传文件
        weak_electricity_file = process_data.get("weak_electricity_file", [])
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = (
            QualityEvaluationFileAction.get_multiple_files_exit_flag(
                weak_electricity_file, "附件上传"
            )
        )
        if not file_exit_flag:
            return file_exit_msg
        weak_electricity_file_json = QualityEvaluationFileAction.get_file_json(
            weak_electricity_file, process_user
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {
            "weak_electricity_rating_list": team_rating_list,
            "weak_electricity_rating_standard_list": weak_electricity_rating_standard_list,
            "weak_electricity_rater_leader_approval": 1,
            "weak_electricity_rater_leader_remark": "",
            "weak_electricity_rater_leader_reject_reason": "",
            "weak_electricity_file": weak_electricity_file,
            "weak_electricity_file_json": weak_electricity_file_json,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)
