import re
import math
import time
import calendar
import datetime

from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowContinue
from iBroker.sdk.workflow.workflow import WorkflowVarUpdate
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.workflow.workflow import WorkflowHistoricInstanceQuery
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib.sdk import tof
from iBroker.lib.sdk import idcdb
from iBroker.lib import exception
from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops
from iBroker.lib.sdk import devicedb


class ProblemApproval(AjaxTodoBase):
    """
    问题整改审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        result1 = process_data.get("result1", None)
        reason1 = process_data.get("reason1", None)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables={"result1": result1, "reason1": reason1})


class ProblemSubmit(AjaxTodoBase):
    """
    问题整改结果上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        result2 = process_data.get("result2", None)
        reason2 = process_data.get("reason2", None)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables={"result2": result2, "reason2": reason2})


class PlanSubmit(AjaxTodoBase):
    """
    问题整改计划提交
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        result2 = process_data.get("result2", None)
        reason2 = process_data.get("reason2", None)
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables={"result2": result2, "reason2": reason2})


class ProblemRectification(object):
    """
      维保po
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()
