import os
import uuid
from datetime import datetime

import pandas as pd
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class AdvanceFieldExitManagement(object):
    """
        进退场管理
    """

    def subcontract_qualification(self, campus):
        """
            分包资质
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT id, company_name, subcontracting_major, qualification, qualification_certificate " \
                    f"FROM subcontract_qualification WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            company_name = row.get('company_name', None)
            subcontracting_major = row.get('subcontracting_major', None)
            qualification = row.get('qualification', None)
            qualification_certificate = row.get('qualification_certificate', None)

            data.append({
                'company_name': company_name,
                'subcontracting_major': subcontracting_major,
                'qualification': qualification,
                'qualification_certificate': qualification_certificate
            })
        return data

    def personnel_qualification(self, campus):
        """
            人员资质
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT company_name, name_of_personnel, type_of_work, qualification, qualification_certificate " \
                    f"FROM personnel_qualification WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            company_name = row.get('company_name', None)
            name_of_personnel = row.get('name_of_personnel', None)
            type_of_work = row.get('type_of_work', None)
            qualification = row.get('qualification', None)
            qualification_certificate = row.get('qualification_certificate', None)

            data.append({
                'company_name': company_name,
                'name_of_personnel': name_of_personnel,
                'type_of_work': type_of_work,
                'qualification': qualification,
                'qualification_certificate': qualification_certificate
            })
        return data

    def personnel_exit(self, campus):
        """
            人员退场
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT company_name, name_of_personnel, exit_time, exit_certificate " \
                    f"FROM personnel_exit WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        data = []
        for row in result:
            company_name = row.get('company_name', None)
            name_of_personnel = row.get('name_of_personnel', None)
            exit_time = row.get('exit_time', None)
            exit_certificate = row.get('exit_certificate', None)

            # 将exit_time转换为"xxxx-xx-xx"格式
            exit_time = exit_time.strftime("%Y-%m-%d")

            data.append({
                'company_name': company_name,
                'name_of_personnel': name_of_personnel,
                'exit_time': exit_time,
                'exit_certificate': exit_certificate
            })

        return data


class SafetyTraining(object):
    """
        安全培训
    """

    def safety_training(self, campus):
        """
            安全培训
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT safety_training_time, safety_training_materials " \
                    f"FROM safety_training WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        quantity = 0  # 初始化数量为0
        aqpx_list = []
        for row in result:
            safety_training_time = row.get('safety_training_time', None)
            safety_training_materials = row.get('safety_training_materials', None)

            # 将safety_training_time转换为"xxxx-xx-xx"格式
            safety_training_time = safety_training_time.strftime("%Y-%m-%d")

            aqpx_list.append({
                'safety_training_time': safety_training_time,
                'safety_training_materials': safety_training_materials
            })

        quantity = len(aqpx_list)  # 根据结果的数量更新数量

        data = {
            'quantity': quantity,
            'aqpx_list': aqpx_list
        }
        return data

    def safety_assessment(self, campus):
        """
            安全考核
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT safety_assessment_time, safety_assessment_materials " \
                    f"FROM safety_training WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        quantity = 0  # 初始化数量为0
        aqkh_list = []
        for row in result:
            safety_assessment_time = row.get('safety_assessment_time', None)
            safety_assessment_materials = row.get('safety_assessment_materials', None)

            # 将safety_assessment_time转换为"xxxx-xx-xx"格式
            safety_assessment_time = safety_assessment_time.strftime("%Y-%m-%d")

            aqkh_list.append({
                'safety_assessment_time': safety_assessment_time,
                'safety_assessment_materials': safety_assessment_materials
            })
        quantity = len(result)

        data = {
            'quantity': quantity,
            'aqkh_list': aqkh_list
        }
        return data

    def safety_disclosure(self, campus):
        """
            安全交底
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT safety_disclosure_time, safety_disclosure_materials " \
                    f"FROM safety_training WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        quantity = 0  # 初始化数量为0
        aqjd_list = []
        for row in result:
            safety_disclosure_time = row.get('safety_disclosure_time', None)
            safety_disclosure_materials = row.get('safety_disclosure_materials', None)

            # 将safety_disclosure_time转换为"xxxx-xx-xx"格式
            safety_disclosure_time = safety_disclosure_time.strftime("%Y-%m-%d")

            aqjd_list.append({
                'safety_disclosure_time': safety_disclosure_time,
                'safety_disclosure_materials': safety_disclosure_materials
            })

        quantity = len(result)

        data = {
            'quantity': quantity,
            'aqjd_list': aqjd_list
        }
        return data

    def safety_inspection(self, campus):
        """
            安全巡查
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT safety_inspection_time, safety_inspection_materials " \
                    f"FROM safety_training WHERE project_name = '{campus}' "
        result = db.get_all(query_sql)
        quantity = 0  # 初始化数量为0
        aqxc_list = []
        for row in result:
            safety_inspection_time = row.get('safety_inspection_time', None)
            safety_inspection_materials = row.get('safety_inspection_materials', None)

            # 将safety_inspection_time转换为"xxxx-xx-xx"格式
            safety_inspection_time = safety_inspection_time.strftime("%Y-%m-%d")

            aqxc_list.append({
                'safety_inspection_time': safety_inspection_time,
                'safety_inspection_materials': safety_inspection_materials
            })

        quantity = len(result)

        data = {
            'quantity': quantity,
            'aqxc_list': aqxc_list
        }
        return data

    def third_party_safety_inspection(self, project_name = None):
        """
        第三方安全飞检
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT date_format(t2.inspection_time,'%Y-%m-%d') inspection_time, "
            "date_format(t2.time_completion_demand_rectification,'%Y-%m-%d') time_completion_demand_rectification, "
            # "t2.campus, t2.project_name, t2.problem_report, "
            "t2.rectification_report, t2.ticket_id, "
            # "t1.project_name, t1.bdaqwm_overall_pass_rate, t1.fireproofing, t1.sb_sk_wlb_jsj, "
            # "t1.safety_utilization_electric_power, t1.lifting_erection, t1.civilized_construction "
            "t1.* "
            "FROM security_inspection_problem_classification t1 "
            "LEFT JOIN third_party_report_data t2 "
            "ON t1.ticket_id = t2.ticket_id "
            # f"WHERE t1.project_name = '{project_name}' "
            # "ORDER BY t2.inspection_time DESC"
        )
        if project_name:
            sql += f"WHERE t1.project_name = '{project_name}' "
        sql += "ORDER BY t2.inspection_time DESC"
        result = db.get_all(sql)
        data = {"quantity": len(result), "data_list": result}
        return data


class SecurityManagementInterface(object):

    def classification_of_security_issues(self, campus, problem_category, problem_level, rectify_state, page_size,
                                          page_number, responsible_unit, impact_classification,
                                          problem_put_forward_time, problem_end_time):
        """
            安全问题分类
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql1 = "SELECT sp.construction_unit, sp.problem_category, sp.problem_location, " \
                     "sp.problem_description, sp.problem_photo, sp.problem_impact, sp.problem_level, " \
                     "sp.rectification_reform_measures, sp.responsible_unit, sp.problem_put_forward_time, " \
                     "sp.problem_end_time, sp.photos_after_rectification " \
                     "FROM week_create_info wci " \
                     "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
                     f"WHERE wci.project_name = '{campus}' "
        query_sql2 = "SELECT construction_unit,problem_category,problem_location,problem_description,problem_photo," \
                     "problem_impact,problem_level,rectification_reform_measures,responsible_unit," \
                     "problem_put_forward_time,problem_end_time,photos_after_rectification" \
                     f" FROM safety_problem WHERE project_name='{campus}'"
        if responsible_unit:
            query_sql2 += f" AND responsible_unit = '{responsible_unit}'"
            query_sql1 += f" AND sp.responsible_unit = '{responsible_unit}'"
        if impact_classification:
            query_sql2 += f" AND impact_classification = '{impact_classification}'"
            query_sql1 += f" AND sp.impact_classification = '{impact_classification}'"

        if problem_category:
            query_sql1 += f" AND sp.problem_category = '{problem_category}'"
            query_sql2 += f" AND problem_category = '{problem_category}'"
        if problem_level:
            query_sql1 += f" AND sp.problem_level = '{problem_level}'"
            query_sql2 += f" AND problem_level = '{problem_level}'"
        if rectify_state == '已关闭':
            query_sql1 += " AND sp.problem_end_time IS NOT NULL"
            query_sql2 += " AND problem_end_time IS NOT NULL"
        elif rectify_state == '未关闭':
            query_sql1 += " AND sp.problem_end_time IS NULL"
            query_sql2 += " AND problem_end_time IS NULL"
        if problem_end_time and rectify_state == '未关闭':
            return {'code': 400, "msg": "传参错误"}
        if problem_put_forward_time and problem_end_time:
            if (type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2
                    and type(problem_end_time) == list and len(problem_end_time) == 2):
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                query_sql2 += (f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                               f"'{problem_put_end}') AND (problem_put_forward_time BETWEEN "
                               f"'{problem_end_start}' AND '{problem_end_end}')")
                query_sql1 += (f"AND (sp.problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                               f"'{problem_put_end}') AND (sp.problem_put_forward_time BETWEEN "
                               f"'{problem_end_start}' AND '{problem_end_end}')")
        elif problem_put_forward_time or problem_end_time:
            if type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2:
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                query_sql2 += f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"
                query_sql1 += f"AND (sp.problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"

            if type(problem_end_time) == list and len(problem_end_time) == 2:
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                query_sql2 += f"AND (problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"
                query_sql1 += f"AND (sp.problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"

        # 列表合并
        problem_list = list(db.get_all(query_sql1)) + list(db.get_all(query_sql2))

        data = []
        for row in problem_list:
            construction_unit = row.get('construction_unit', None)
            problem_category = row.get('problem_category', None)
            problem_location = row.get('problem_location', None)
            problem_description = row.get('problem_description', None)
            problem_photo = row.get('problem_photo', None)
            problem_impact = row.get('problem_impact', None)
            problem_level = row.get('problem_level', None)
            rectification_reform_measures = row.get('rectification_reform_measures', None)
            responsible_unit = row.get('responsible_unit', None)
            put_forward_time = row.get('problem_put_forward_time', None)
            end_time = row.get('problem_end_time', None)
            photos_after_rectification = row.get('photos_after_rectification', None)
            if put_forward_time is not None:
                new_put_forward_time = put_forward_time.strftime("%Y-%m-%d")
            else:
                new_put_forward_time = None

            if end_time == "0000-00-00 00:00:00" or end_time is None:
                new_end_time = None
                now = datetime.now()
                time_data = (now - put_forward_time).days + 1
                if 3 < time_data <= 7:
                    duration = "大于3天"
                elif 7 < time_data <= 15:
                    duration = "大于7天"
                elif time_data > 15:
                    duration = "大于15天"
            else:
                duration = (end_time - put_forward_time).days + 1
                new_end_time = end_time.strftime("%Y-%m-%d")

            data.append({
                'construction_unit': construction_unit,
                'problem_category': problem_category,
                'problem_location': problem_location,
                'problem_description': problem_description,
                'problem_photo': problem_photo,
                'problem_impact': problem_impact,
                'problem_level': problem_level,
                'rectification_reform_measures': rectification_reform_measures,
                'responsible_unit': responsible_unit,
                'problem_put_forward_time': new_put_forward_time,
                'problem_end_time': new_end_time,
                'photos_after_rectification': photos_after_rectification,
                'duration': str(duration)
            })
        # 计算总数
        total = len(data)
        # 计算总页数
        page_size = int(page_size)
        page_number = int(page_number)  # 将字符串转换为整数
        total_pages = (len(data) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = data[start_index:end_index]

        for page in page_list:
            if page.get('problem_photo'):
                if "," in page.get('problem_photo'):
                    problem_url_list = page.get('problem_photo').split(",")
                else:
                    problem_url_list = page.get('problem_photo').split(";")
                page['problem_photo'] = problem_url_list
            if page.get('photos_after_rectification'):
                if "," in page.get('photos_after_rectification'):
                    url_list = page.get('photos_after_rectification').split(",")
                else:
                    url_list = page.get('photos_after_rectification').split(";")
                page['photos_after_rectification'] = url_list
        return {
            'data': page_list,
            'total': total
        }

    def security_problem_category(self, project_name):
        """
            各分类数量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'"
        sql2 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}'"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql2))
        total_results = len(quality_list)
        if total_results == 0:
            return []
        # 设备信息
        sbskwlb = 0
        gc = 0
        aq = 0
        sg = 0
        fh = 0
        dz = 0
        qt = 0
        for e in quality_list:
            if e.get("problem_category") == '三宝四口五临边':
                sbskwlb += 1
            elif e.get("problem_category") == '高处作业':
                gc += 1
            elif e.get("problem_category") == '安全用电':
                aq += 1
            elif e.get("problem_category") == '施工机具':
                sg += 1
            elif e.get("problem_category") == '防火':
                fh += 1
            elif e.get("problem_category") == '起重吊装':
                dz += 1
            elif e.get("problem_category") == '其它':
                qt += 1
        quality_category_list = [
            {"value": sbskwlb, "name": '三宝四口五临边',
             "percentage": "{:.2f}%".format(round(float(sbskwlb) / float(total_results) * 100))},
            {"value": gc, "name": '高处作业',
             "percentage": "{:.2f}%".format(round(float(gc) / float(total_results) * 100))},
            {"value": aq, "name": '安全用电',
             "percentage": "{:.2f}%".format(round(float(aq) / float(total_results) * 100))},
            {"value": sg, "name": '施工机具',
             "percentage": "{:.2f}%".format(round(float(sg) / float(total_results) * 100))},
            {"value": fh, "name": '防火',
             "percentage": "{:.2f}%".format(round(float(fh) / float(total_results) * 100))},
            {"value": dz, "name": '吊装',
             "percentage": "{:.2f}%".format(round(float(dz) / float(total_results) * 100))},
            {"value": qt, "name": '其它',
             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
        ]
        return quality_category_list

    def safety_problem_category_unsolve(self, project_name):
        """
            各分类未完成数量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM safety_problem WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT sp.problem_category FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' " \
               f"AND sp.problem_end_time IS NULL"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return []
        # 设备信息
        sbskwlb = 0
        gc = 0
        aq = 0
        sg = 0
        fh = 0
        dz = 0
        qt = 0
        for e in quality_list:
            if e.get("problem_category") == '三宝四口五临边':
                sbskwlb += 1
            elif e.get("problem_category") == '高处作业':
                gc += 1
            elif e.get("problem_category") == '安全用电':
                aq += 1
            elif e.get("problem_category") == '施工机具':
                sg += 1
            elif e.get("problem_category") == '防火':
                fh += 1
            elif e.get("problem_category") == '起重吊装':
                dz += 1
            elif e.get("problem_category") == '其它':
                qt += 1
        quality_category_list = [
            {"value": sbskwlb, "name": '三宝四口五临边',
             "percentage": "{:.2f}%".format(round(float(sbskwlb) / float(total_results) * 100))},
            {"value": gc, "name": '高处作业',
             "percentage": "{:.2f}%".format(round(float(gc) / float(total_results) * 100))},
            {"value": aq, "name": '安全用电',
             "percentage": "{:.2f}%".format(round(float(aq) / float(total_results) * 100))},
            {"value": sg, "name": '施工机具',
             "percentage": "{:.2f}%".format(round(float(sg) / float(total_results) * 100))},
            {"value": fh, "name": '防火',
             "percentage": "{:.2f}%".format(round(float(fh) / float(total_results) * 100))},
            {"value": dz, "name": '吊装',
             "percentage": "{:.2f}%".format(round(float(dz) / float(total_results) * 100))},
            {"value": qt, "name": '其它',
             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
        ]
        return quality_category_list

    def safety_problem_influence_total(self, project_name):
        """
            安全问题影响总量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM safety_problem WHERE project_name='{project_name}'"
        sql4 = "SELECT sp.impact_classification FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return []
        safety_jd = 0
        safety_zl = 0
        safety_hg = 0
        for quality in quality_list:
            if quality.get("impact_classification") == "进度":
                safety_jd += 1
            elif quality.get("impact_classification") == "质量":
                safety_zl += 1
            elif quality.get("impact_classification") == "合规":
                safety_hg += 1
        quality_impact_list = [{"value": safety_jd, "name": '进度',
                                "percentage": "{:.2f}%".format(round(float(safety_jd) / float(total_results) * 100))},
                               {"value": safety_zl, "name": '质量',
                                "percentage": "{:.2f}%".format(round(float(safety_zl) / float(total_results) * 100))},
                               {"value": safety_hg, "name": '合规',
                                "percentage": "{:.2f}%".format(round(float(safety_hg) / float(total_results) * 100))}]
        return quality_impact_list

    def safety_problem_unsolve(self, project_name):
        """
            安全问题未解决
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM safety_problem WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT sp.impact_classification FROM week_create_info wci " \
               "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND sp.problem_end_time IS NULL"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return []
        safety_jd = 0
        safety_zl = 0
        safety_hg = 0
        for quality in quality_list:
            if quality.get("impact_classification") == "进度":
                safety_jd += 1
            elif quality.get("impact_classification") == "质量":
                safety_zl += 1
            elif quality.get("impact_classification") == "合规":
                safety_hg += 1
        quality_impact_list = [{"value": safety_jd, "name": '进度',
                                "percentage": "{:.2f}%".format(round(float(safety_jd) / float(total_results) * 100))},
                               {"value": safety_zl, "name": '质量',
                                "percentage": "{:.2f}%".format(round(float(safety_zl) / float(total_results) * 100))},
                               {"value": safety_hg, "name": '合规',
                                "percentage": "{:.2f}%".format(round(float(safety_hg) / float(total_results) * 100))}]
        return quality_impact_list

    def safety_problem_total(self, project_name):
        """
            安全问题总量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql1 = "SELECT sp.construction_unit, sp.problem_category, sp.problem_location, " \
                     "sp.problem_description, sp.problem_photo, sp.problem_impact, sp.problem_level, " \
                     "sp.rectification_reform_measures, sp.responsible_unit, sp.problem_put_forward_time, " \
                     "sp.problem_end_time, sp.photos_after_rectification " \
                     "FROM week_create_info wci " \
                     "JOIN safety_problem sp ON wci.ticketid = sp.ticketid " \
                     f"WHERE wci.project_name = '{project_name}' "
        query_sql2 = "SELECT construction_unit,problem_category,problem_location,problem_description,problem_photo," \
                     "problem_impact,problem_level,rectification_reform_measures,responsible_unit," \
                     "problem_put_forward_time,problem_end_time,photos_after_rectification" \
                     f" FROM safety_problem WHERE project_name='{project_name}'"
        # 列表合并
        problem_list = list(db.get_all(query_sql1)) + list(db.get_all(query_sql2))

        # 计算总数
        total = len(problem_list)
        if total == 0:
            return []
        solved = 0
        unsolved = 0
        for i in problem_list:
            if i.get('problem_end_time') is not None:
                solved += 1
            else:
                unsolved += 1
        data = {
            "total_results": int(total),
            "solved": solved,
            "unsolved": unsolved,
            "rate": "{:.2f}%".format(round((float(solved) / float(total) * 100), 2))
        }
        return data
