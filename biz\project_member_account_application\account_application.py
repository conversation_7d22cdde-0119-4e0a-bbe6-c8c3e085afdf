from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


# 账号申请
class AccountApplication(AjaxTodoBase):
    def __init__(self):
        super(AccountApplication, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("member_list")
        application_remark = process_data.get("application_remark")
        # 判断是否有重复开通的账号 （有的话“application_remark”为必填）
        account_repeat_list = []
        # 校验是否存在【同手机号不同姓名】的账号
        account_validate_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        for info in member_list:
            if "phone" in info:
                sql = f"SELECT * FROM project_role_account WHERE phone='{info.get('phone')}' and del_flag=0"
                result = db.get_all(sql)
                if result:
                    append_flag = False
                    for account in result:
                        # 通过手机号判断已存在的和新申请的姓名是否一致
                        name = info.get("name")
                        phone = info.get("phone")
                        application_account = info.get("application_account")
                        project_name = info.get("campus_name") + info.get(
                            "project_name"
                        )
                        role = info.get("role")
                        exist_name = account.get("name")
                        exist_phone = account.get("phone")
                        exist_project_name = account.get("project_name")
                        exist_role = account.get("role")
                        exist_account = account.get("account")
                        if exist_project_name == project_name and exist_name == name and exist_role == role:
                            continue
                        if (
                            "集成商" in role or "项管" in role or "监理" in role
                        ) and project_name not in account_repeat_list:
                            account_repeat_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
                        if name != exist_name:
                            # 新申请的展示在已存在的前面
                            if not append_flag:
                                account_validate_list.append(
                                    f"{name} {project_name} {role} {phone} {application_account}"
                                )
                            append_flag = True
                            account_validate_list.append(
                                f"{exist_name} {exist_project_name} {exist_role} {exist_phone} {exist_account}"
                            )
        if account_repeat_list:
            tips = ("<br>").join(account_repeat_list)
            if not application_remark:
                return {
                    "code": -1,
                    "msg": f"存在【已开通账号】，请在备注填写使用理由！<br>{tips}",
                }
        if account_validate_list:
            tips = ("<br>").join(account_validate_list)
            return {
                "code": -1,
                "msg": "【请核对】姓名电话是否对应！"
                # "<br>若是已开通的账号信息有误请先删除已开通的账号信息再继续开通；"
                # "若是本次开通的账号信息有误请先修改正确再继续开通。"
                f"<br>{tips}",
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "member_list": member_list,
            "application_remark": application_remark,
            "leader_approval": 1,
            "leader_remark": "",
            "leader_reject_reason": "",
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号申请审批
class AccountApplicationApproval(AjaxTodoBase):
    def __init__(self):
        super(AccountApplicationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval", 1)
        leader_remark = process_data.get("leader_remark", "")
        leader_reject_reason = ""
        if not leader_approval:
            leader_reject_reason = leader_remark
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "leader_approval": leader_approval,
            "leader_remark": leader_remark,
            "leader_reject_reason": leader_reject_reason,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号申请完成信息填入
class AccountInfoInput(AjaxTodoBase):
    def __init__(self):
        super(AccountInfoInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        member_list = process_data.get("member_list")
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "member_list": member_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 账号申请完成信息确认
class AccountInfoConfirm(AjaxTodoBase):
    def __init__(self):
        super(AccountInfoConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id)
