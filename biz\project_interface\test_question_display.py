import json
import re

from iBroker.lib import mysql, config


class TestQuestionDisplay(object):
    """
    测试问题跟踪
    """

    def get_module_name_by_project_name(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
                "SELECT module_name FROM project_module_mapping_relationship WHERE project_name='%s'"
                % project_name
        )
        module_query = db.get_all(sql)
        data = []
        if module_query:
            for item in module_query:
                data.append(item['module_name'])
        if data:
            return {"code": 0, "data": data, "msg": "查询成功"}
        return {"code": -1, "data": [], "msg": "无模组名称，请联系开发"}

    def basic_logic(self, question_result):
        # 构造工单号列表
        # ticket_id_list = []
        # for question in question_result:
        #     if question.get('fix_photo_url'):
        #         url_list = question.get('fix_photo_url').split(";")
        #         question['fix_photo_url'] = url_list
        #         question['state'] = '已完成'
        #         question["deal_users"] = ''
        #     elif question.get('cause_rejection'):
        #         question['fix_photo_url'] = []
        #         question['state'] = '已驳回'
        #     else:
        #         ticket_id_list.append(question.get('ticket_id'))
        #         question['fix_photo_url'] = []
        #     # 构造图片
        #     if question.get('problem_photo'):
        #         problem_url_list = question.get('problem_photo').split(";")
        #         question['problem_photo'] = problem_url_list
        #     else:
        #         question['problem_photo'] = []
        # # 通过工单号获取流程id
        # query_data = {
        #     "SchemaId": "ticket_base",
        #     "Data": {
        #         "ResultColumns": {
        #             "CurrentTasks": "",
        #             "CurrentAllProcessUsers": "",
        #             "TicketId": ""
        #         },
        #         "SearchCondition": {
        #             "TicketId": ticket_id_list
        #         }
        #     }
        # }
        # query_result = gnetops.request(
        #     action="QueryData",
        #     method="Run",
        #     ext_data=query_data
        # )
        # # 节点代办人
        # result_list = query_result.get("List")
        # for q in question_result:
        #     for item in result_list:
        #         deal_users = item.get('CurrentAllProcessUsers')
        #         current_tasks = item.get("CurrentTasks", [])
        #         if item.get('TicketId') == q.get('ticket_id'):
        #             if deal_users or '等待测试问题-整改计划反馈' in current_tasks:
        #                 q['deal_users'] = item.get('CurrentAllProcessUsers')
        #                 q['state'] = '进行中'
        #             else:
        #                 q['deal_users'] = '测试平台销项中'
        #                 q['state'] = '已整改'

        ticket_id_list = []
        for question in question_result:
            # 构造图片
            if question.get('problem_photo'):
                problem_url_list = question.get('problem_photo').split(";")
                question['problem_photo'] = problem_url_list
            else:
                question['problem_photo'] = []
            if question.get('fix_photo_url') and question.get("TicketStatus") == "END":
                fix_photo_url = question.get('fix_photo_url')
                try:
                    # 尝试解析为 JSON 格式
                    url_list = json.loads(fix_photo_url)
                except (json.JSONDecodeError, ValueError):
                    # 如果解析失败，则按分号分割的方式处理
                    url_list = fix_photo_url.split(';')

                question['fix_photo_url'] = url_list
                question['state'] = '已完成'
                question["deal_users"] = ''
                continue
            elif question.get('cause_rejection') and question.get("TicketStatus") == "END":
                question['fix_photo_url'] = []
                question['state'] = '已驳回'
                question["deal_users"] = ''
                continue
            elif question.get("TicketStatus") == "END":
                question['fix_photo_url'] = []
                question['state'] = '已完成'
                continue
            else:
                if not question.get("act_ticket_id"):
                    question['state'] = '进行中'
                    question['fix_photo_url'] = []
                    question["deal_users"] = '未同步数据，请稍后'
                    continue
                else:
                    fix_photo_url = question.get('fix_photo_url')
                    if fix_photo_url:
                        try:
                            # 尝试解析为 JSON 格式
                            url_list = json.loads(fix_photo_url)
                        except (json.JSONDecodeError, ValueError):
                            # 如果解析失败，则按分号分割的方式处理
                            url_list = fix_photo_url.split(';')

                        question['fix_photo_url'] = url_list
                    else:
                        question['fix_photo_url'] = []
                    current_tasks = question.get('CurrentTasks')
                    current_tasks = current_tasks if current_tasks else ""
                    current_tasks_list = json.loads(current_tasks)
                    if "等待测试平台审核结果" in current_tasks_list:
                        question['deal_users'] = '测试平台销项中'
                        question['state'] = '已整改'
                    else:
                        if question.get("CurrentAllProcessUsers"):
                            question['deal_users'] = question.get('CurrentAllProcessUsers')
                            question['state'] = '进行中'
                        else:
                            if "等待测试问题-整改结果提交" in current_tasks_list:
                                question['deal_users'] = '等待合作伙伴数据回传'
                                question['state'] = '进行中'
                            elif "等待测试问题-整改计划反馈" in current_tasks_list:
                                question['deal_users'] = '等待合作伙伴数据回传'
                                question['state'] = '进行中'
                            else:
                                question['deal_users'] = ''
                                question['state'] = '进行中'

        return question_result

        # for q in question_result:
        #     act_ticket_id = q.get('act_ticket_id')  # 判断是否有关联的工单数据
        #     if q.get('ticket_id') in ticket_id_list:
        #         # 如果没有工单数据，跳过复杂的判断逻辑，直接设为“进行中”
        #         if not act_ticket_id:
        #             q['deal_users'] = '未同步数据，请稍后'
        #             q['state'] = '进行中'
        #             continue
        #
        #         current_tasks = q.get('CurrentTasks')
        #         current_tasks = current_tasks if current_tasks else ""
        #
        #         if current_tasks and "等待测试平台审核结果" in current_tasks:
        #             q['deal_users'] = '测试平台销项中'
        #             q['state'] = '已整改'
        #         else:
        #             if q.get("CurrentAllProcessUsers"):
        #                 q['deal_users'] = q.get('CurrentAllProcessUsers')
        #                 q['state'] = '进行中'
        #             else:
        #                 if "等待测试问题-整改结果提交" in current_tasks:
        #                     q['deal_users'] = '等待合作伙伴数据回传'
        #                     q['state'] = '进行中'
        #                 else:
        #                     q['deal_users'] = '测试平台销项中'
        #                     q['state'] = '进行中'
        #     else:
        #         q['state'] = '进行中'
        #         q['deal_users'] = '未同步数据，请稍后'
        # return question_result

    def test_question_display(self, project_name, major, rectification_status, page_size, page_number, ticket_id,
                              question_ticket, module_name=None):
        page_size = page_size  # 每页的结果数量
        page_number = page_number  # 页码
        offset = (page_number - 1) * page_size  # 计算偏移量

        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT count(pr.ticket_id) "
               f"FROM problem_rectification pr "
               "LEFT JOIN all_construction_ticket_data act "
               "ON pr.ticket_id = act.TicketId "
               f"WHERE project_name='{project_name}'")

        sql1 = ("SELECT pr.room_id,pr.device_id,pr.device_name,pr.problem_photo,pr.responsible_unit,pr.fix_photo_url,"
                "pr.problem_type,pr.problem_location_and_description,pr.problem_level,pr.ticket_id,pr.question_ticket,"
                "pr.major,pr.cause_rejection,pr.module_name,"
                "act.TicketId AS act_ticket_id,act.TicketStatus,act.CurrentTasks,act.CurrentAllProcessUsers "
                "FROM problem_rectification pr "
                "LEFT JOIN all_construction_ticket_data act "
                "ON pr.ticket_id = act.TicketId "
                "WHERE pr.project_name = '%s'") % project_name
        if major:
            sql += f" AND major = '{major}'"
            sql1 += f" AND major = '{major}'"
        if ticket_id:
            sql += f" AND ticket_id = '{ticket_id}'"
            sql1 += f" AND pr.ticket_id = '{ticket_id}'"
        if question_ticket:
            sql += f" AND question_ticket = '{question_ticket}'"
            sql1 += f" AND question_ticket = '{question_ticket}'"
        if module_name:
            sql += f" AND module_name = '{module_name}'"
            sql1 += f" AND pr.module_name = '{module_name}'"
        if rectification_status:
            if rectification_status == '已完成':
                # sql += " AND act.TicketStatus == 'END'"
                # sql1 += " AND act.TicketStatus == 'END'"
                problem_list = []
                question_result = db.get_all(sql1)
                question_list = self.basic_logic(question_result)
                for i in question_list:
                    state = i.get('state')
                    if state == '已完成':
                        problem_list.append(i)
            else:
                problem_list = []
                if rectification_status == '已整改':
                    # 问题列表
                    question_result = db.get_all(sql1)
                    question_list = self.basic_logic(question_result)
                    for i in question_list:
                        state = i.get('state')
                        if state == '已整改':
                            problem_list.append(i)

                elif rectification_status == '进行中':
                    # sql += " AND act.TicketStatus == 'OPEN'"
                    # sql1 += " AND act.TicketStatus == 'OPEN'"
                    question_result = db.get_all(sql1)
                    question_list = self.basic_logic(question_result)
                    for i in question_list:
                        state = i.get('state')
                        if state == '进行中':
                            problem_list.append(i)

                elif rectification_status == '已驳回':
                    # sql += " AND pr.cause_rejection IS NOT NULL"
                    # sql1 += " AND pr.cause_rejection IS NOT NULL"
                    question_result = db.get_all(sql1)
                    question_list = self.basic_logic(question_result)
                    for i in question_list:
                        state = i.get('state')
                        if state == '已驳回':
                            problem_list.append(i)

            # 计算总数
            total = len(problem_list)
            # 计算总页数
            total_pages = (len(problem_list) + page_size - 1) // page_size
            # 检查请求的页码是否超出范围
            if page_number < 1:
                page_number = 1
            if page_number > total_pages:
                page_number = total_pages
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            # 检查索引是否超出范围
            if start_index < 0:
                start_index = 0
            end_index = min(end_index, total)
            page_list = problem_list[start_index:end_index]
            data = {
                'question_list': page_list,
                'total_results': total
            }
            return data
        else:
            sql1 += f" LIMIT {offset}, {page_size}"
            # 总数
            count_info = db.get_all(sql)
            total_results = 0
            if count_info:
                total_results = count_info[0].get('count(pr.ticket_id)', 0)
            # 问题列表
            question_result = db.get_all(sql1)
            question_list = self.basic_logic(question_result)

            data = {
                'question_list': question_list,
                'total_results': total_results
            }
            return data

    def problem_export(self, project_name, module_name=None):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = ("SELECT pr.room_id,pr.device_id,pr.device_name,pr.problem_photo,pr.responsible_unit,pr.fix_photo_url,"
                "pr.problem_type,pr.problem_location_and_description,pr.problem_level,pr.ticket_id,pr.question_ticket,"
                "pr.major,pr.cause_rejection,act.TicketId AS act_ticket_id,act.TicketStatus,act.CurrentTasks, "
                "act.CurrentAllProcessUsers,pr.module_name "
                "FROM problem_rectification pr "
                "LEFT JOIN all_construction_ticket_data act "
                "ON pr.ticket_id = act.TicketId "
                "WHERE pr.project_name = '%s'") % project_name
        if module_name:
            sql1 = (
                       "SELECT pr.room_id,pr.device_id,pr.device_name,pr.problem_photo,pr.responsible_unit,"
                       "pr.fix_photo_url,pr.problem_type,pr.problem_location_and_description,pr.problem_level,"
                       "pr.ticket_id,pr.question_ticket,pr.major,pr.cause_rejection,act.TicketId AS act_ticket_id,"
                       "act.TicketStatus,act.CurrentTasks,act.CurrentAllProcessUsers,pr.module_name "
                       "FROM problem_rectification pr "
                       "LEFT JOIN all_construction_ticket_data act "
                       "ON pr.ticket_id = act.TicketId "
                       "WHERE pr.project_name = '%s' and pr.module_name='%s'") % (project_name, module_name)
        # 问题列表
        question_result = db.get_all(sql1)
        question_list = self.basic_logic(question_result)
        data = {
            'question_list': question_list
        }
        return data

    def sort_by_prefix_and_number(self, item):
        prefix = item[:4]
        match = re.search(r'\d+', item)
        if match:
            return (prefix, int(match.group()))
        else:
            return (prefix, float('inf'))  # 使用正无穷大表示无数字的情况

    def authority_management(self, account):
        db = mysql.new_mysql_instance("tbconstruct")
        project_name = []

        if account in config.get_config_map("test_question_permissions"):
            sql1 = "SELECT project_name FROM project_role_account"
            project_result = db.get_all(sql1)
            for project in project_result:
                if project.get('project_name') not in project_name:
                    project_name.append(project.get('project_name'))
        else:
            sql1 = f"SELECT project_name FROM project_role_account WHERE account='{account}' " \
                   " AND (del_flag = 0 OR del_flag IS NULL)"
            project_result = db.get_all(sql1)
            for project in project_result:
                project_name.append(project.get('project_name'))
        project_name_list = list(set(project_name))
        project_name_list.sort(key=self.sort_by_prefix_and_number)
        return project_name_list

    def test_overview(self, project_name, module_name=None):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT count(ticket_id) FROM problem_rectification WHERE project_name='{project_name}' "
               f"AND cause_rejection IS NULL")
        if module_name:
            sql += f" AND module_name='{module_name}'"
        total_results = 0
        finished = 0
        unfinished = 0
        # 总数
        count_info = db.get_all(sql)
        if count_info:
            total_results = count_info[0].get('count(ticket_id)')
            if total_results == 0:
                return {
                    "total_results": 0,
                    "completed_num": 0,
                    "undone_num": 0,
                    "completion_rate": "0.00%"
                }
        # 计算已完成数量，包括 fix_photo_url 不为空和 cause_rejection 不为空的工单
        # sql_completed = (
        #     "SELECT COUNT(pr.ticket_id) AS completed_count "
        #     "FROM problem_rectification "
        #     "LEFT JOIN all_construction_ticket_data act "
        #     "ON pr.ticket_id = act.TicketId "
        #     f"WHERE (act.fix_photo_url IS NOT NULL OR cause_rejection IS NOT NULL) AND project_name='{project_name}'"
        # )

        sql_completed = (
                            "SELECT COUNT(pr.ticket_id) AS completed_count "
                            "FROM problem_rectification pr "
                            "LEFT JOIN all_construction_ticket_data act ON pr.ticket_id = act.TicketId "
                            "WHERE pr.project_name = '%s' AND pr.cause_rejection IS NULL "
                            "AND act.TicketStatus = 'END'"
                        ) % project_name
        if module_name:
            sql_completed += f" AND pr.module_name='{module_name}'"

        finished_list = db.get_all(sql_completed)
        if finished_list:
            finished = finished_list[0].get('completed_count', 0)

        # 计算未完成数量，排除被驳回的工单
        sql_undone = (
                         "SELECT COUNT(pr.ticket_id) AS undone_count "
                         "FROM problem_rectification pr "
                         "LEFT JOIN all_construction_ticket_data act ON pr.ticket_id = act.TicketId "
                         "WHERE pr.project_name = '%s' AND pr.cause_rejection IS NULL "
                         "AND (act.TicketStatus IS NULL OR act.TicketStatus != 'END')"
                     ) % project_name
        if module_name:
            sql_undone += f" AND pr.module_name='{module_name}'"
        unfinished_list = db.get_all(sql_undone)
        if unfinished_list:
            unfinished = unfinished_list[0].get('undone_count', 0)
        completion_rate = "{:.2f}%".format(round((float(finished) / float(total_results) * 100), 2))
        data = {
            "total_results": total_results,
            "completed_num": finished,
            "undone_num": unfinished,
            "completion_rate": completion_rate
        }
        return data

    def number_of_majors(self, project_name, module_name=None):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT count(major) FROM problem_rectification WHERE project_name='{project_name}' "
               f"AND cause_rejection IS NULL")
        if module_name:
            sql += f" AND module_name='{module_name}'"
        # 总数
        count_info = db.get_all(sql)
        major_num = []
        if count_info:
            total_results = count_info[0].get('count(major)')
            if total_results == 0:
                return [{
                    'name': '',
                    'value': 0,
                    'percentage': "0.00%"
                }]
            sql1 = ("SELECT major, count(major) FROM problem_rectification " 
                   f"WHERE project_name='{project_name}' GROUP BY major "
                    f"AND cause_rejection IS NULL")
            if module_name:
                sql1 = ("SELECT major, count(major) FROM problem_rectification " 
                       f"WHERE project_name='{project_name}' AND cause_rejection IS NULL "
                        f"AND module_name='{module_name}' GROUP BY major")
            major_total_list = db.get_all(sql1)
            for i in major_total_list:
                if i.get("major"):
                    if total_results == 0:
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': 0,
                                'percentage': "0.00%"
                            }
                        )
                    else:
                        value = int(i.get("count(major)"))
                        major_num.append(
                            {
                                'name': i.get("major"),
                                'value': value,
                                'percentage': "{:.2f}%".format(
                                    round((float(value) / float(total_results) * 100), 2))
                            }
                        )
        return major_num

    def number_undone_of_majors(self, project_name, module_name=None):
        db = mysql.new_mysql_instance("tbconstruct")

        # 计算问题总数
        sql_total = (
            f"SELECT COUNT(major) AS total_count "
            f"FROM problem_rectification "
            f"WHERE project_name='{project_name}' AND cause_rejection IS NULL"
        )
        if module_name:
            sql_total += f" AND module_name='{module_name}'"
        count_info = db.get_all(sql_total)

        major_num = []
        if count_info:
            total_results = count_info[0].get('total_count', 0)
            if total_results == 0:
                return [{
                    'name': '',
                    'value': 0,
                    'percentage': "0.00%"
                }]

            # 计算各专业的未完成数量
            sql_uncompleted = (
                "SELECT pr.major, COUNT(pr.ticket_id) AS uncompleted_count "
                "FROM problem_rectification pr "
                "LEFT JOIN all_construction_ticket_data act ON pr.ticket_id = act.TicketId "
                f"WHERE pr.project_name = '{project_name}' AND pr.cause_rejection IS NULL "
                "AND (act.TicketStatus IS NULL OR act.TicketStatus = 'OPEN') "
                "GROUP BY pr.major"
            )
            if module_name:
                sql_uncompleted = (
                    "SELECT pr.major, COUNT(pr.ticket_id) AS uncompleted_count "
                    "FROM problem_rectification pr "
                    "LEFT JOIN all_construction_ticket_data act ON pr.ticket_id = act.TicketId "
                    f"WHERE pr.project_name = '{project_name}' AND pr.module_name='{module_name}' "
                    "AND (act.TicketStatus IS NULL OR act.TicketStatus = 'OPEN') "
                    "AND pr.cause_rejection IS NULL "
                    "GROUP BY pr.major"
                )
            major_uncompleted_list = db.get_all(sql_uncompleted)

            for i in major_uncompleted_list:
                if i.get("major"):
                    value = int(i.get("uncompleted_count", 0))
                    major_num.append(
                        {
                            'name': i.get("major"),
                            'value': value,
                            'percentage': "{:.2f}%".format(
                                round((float(value) / float(total_results) * 100), 2)
                            ) if total_results > 0 else "0.00%"
                        }
                    )
        return major_num

    def number_of_problem_type(self, project_name, module_name=None):
        # 设备质量/安装工艺/设备调试/规划设计/其它
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT problem_type FROM problem_rectification WHERE project_name='{project_name}'"
               f" AND cause_rejection IS NULL")
        if module_name:
            sql += f" AND module_name='{module_name}'"
        # 总数
        count_info = db.get_all(sql)
        total_results = len(count_info)
        if total_results == 0:
            return [{"value": 0, "name": '设备质量',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '安装工艺',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '设备调试',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '规划设计',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '其它',
                     "percentage": "0.00%"},
                    ]
        zl = 0
        az = 0
        ts = 0
        gh = 0
        qt = 0
        for i in count_info:
            if i.get("problem_type") == '设备质量':
                zl += 1
            elif i.get("problem_type") == '安装工艺':
                az += 1
            elif i.get("problem_type") == '设备调试':
                ts += 1
            elif i.get("problem_type") == '规划设计':
                gh += 1
            elif i.get("problem_type") == '其它':
                qt += 1
        problem_type_num = [{"value": zl, "name": '设备质量',
                             "percentage": "{:.2f}%".format(round(float(zl) / float(total_results) * 100))},
                            {"value": az, "name": '安装工艺',
                             "percentage": "{:.2f}%".format(round(float(az) / float(total_results) * 100))},
                            {"value": ts, "name": '设备调试',
                             "percentage": "{:.2f}%".format(round(float(ts) / float(total_results) * 100))},
                            {"value": gh, "name": '规划设计',
                             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
                            {"value": qt, "name": '其它',
                             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
                            ]
        return problem_type_num

    def number_undone_of_problem_type(self, project_name, module_name=None):
        # 设备质量/安装工艺/设备调试/规划设计/其它
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT pr.problem_type "
            "FROM problem_rectification pr "
            "LEFT JOIN all_construction_ticket_data act ON pr.ticket_id = act.TicketId "
            f"WHERE pr.project_name = '{project_name}' AND cause_rejection IS NULL "
            "AND (act.TicketStatus IS NULL OR act.TicketStatus != 'END')"
        )
        if module_name:
            sql += f" AND pr.module_name='{module_name}'"
        # 总数
        count_info = db.get_all(sql)
        total_results = len(count_info)
        if total_results == 0:
            return [{"value": 0, "name": '设备质量',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '安装工艺',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '设备调试',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '规划设计',
                     "percentage": "0.00%"},
                    {"value": 0, "name": '其它',
                     "percentage": "0.00%"},
                    ]
        zl = 0
        az = 0
        ts = 0
        gh = 0
        qt = 0
        for i in count_info:
            if i.get("problem_type") == '设备质量':
                zl += 1
            elif i.get("problem_type") == '安装工艺':
                az += 1
            elif i.get("problem_type") == '设备调试':
                ts += 1
            elif i.get("problem_type") == '规划设计':
                gh += 1
            elif i.get("problem_type") == '其它':
                qt += 1
        problem_type_num = [{"value": zl, "name": '设备质量',
                             "percentage": "{:.2f}%".format(round(float(zl) / float(total_results) * 100))},
                            {"value": az, "name": '安装工艺',
                             "percentage": "{:.2f}%".format(round(float(az) / float(total_results) * 100))},
                            {"value": ts, "name": '设备调试',
                             "percentage": "{:.2f}%".format(round(float(ts) / float(total_results) * 100))},
                            {"value": gh, "name": '规划设计',
                             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
                            {"value": qt, "name": '其它',
                             "percentage": "{:.2f}%".format(round(float(qt) / float(total_results) * 100))},
                            ]
        return problem_type_num
