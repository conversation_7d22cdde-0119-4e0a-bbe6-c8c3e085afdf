from iBroker.lib.sdk import gnetops
from iBroker.lib import mysql

from datetime import datetime


class Tools(object):

    # 自动建单
    @staticmethod
    def create_ticket(flow_dict):
        """
        自动建单
        """
        data = {
            "CustomVariables": flow_dict.get("CustomVariables"),
            "ProcessDefinitionKey": flow_dict.get("ProcessDefinitionKey"),
            "Source": "",
            "TicketDescription": flow_dict.get("processTitle"),
            "TicketLevel": "3",
            "TicketTitle": flow_dict.get("processTitle"),
            "UserInfo": {
                "Concern": flow_dict.get("Concern"),  # 关注人 可填多个
                "Creator": "dcops",  # 建单人 只能填一个
                "Deal": flow_dict.get(
                    "Deal"
                ),  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
            },
        }
        res = gnetops.request(action="Ticket", method="Create", data=data)
        return res

    # 计算当前待办最大耗时
    @staticmethod
    def get_max_cost_time(data):
        """
        计算当前待办最大耗时
        """
        cost_time = ""
        min_start_time = None
        if data:
            for item in data:
                start_time = item.get("StartTime")
                if start_time:
                    start_time_temp = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    if not min_start_time:
                        min_start_time = start_time_temp
                    min_start_time = min(min_start_time, start_time_temp)
            now = datetime.today()
            time_difference = now - min_start_time
            days = time_difference.days
            hours = time_difference.seconds // 3600
            cost_time = f"{days}天{hours}小时"
        return cost_time

    # 查询工单by工单号
    def query_ticket(ticket_id, is_open=False):
        """
        查询工单by工单号
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT * FROM all_construction_ticket_data "
        if ticket_id:
            sql += f"WHERE TicketId = '{ticket_id}' "
        if is_open:
            sql += "AND TicketStatus != 'END' "
        sql += "AND IsForceEnd = '0' "
        res = db.query(sql)
        return res
