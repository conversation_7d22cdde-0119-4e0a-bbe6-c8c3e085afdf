from iBroker.lib import mysql
from iBroker.lib.sdk import gnetops


class ApplyToEnterProjectApi(object):

    @staticmethod
    def query_exist_account_info(account_info):
        """
        查已存在账号信息
        """
        phone = account_info.get("phone")
        name = account_info.get("exist_name")
        account = account_info.get("exist_account")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            f"SELECT * FROM project_role_account WHERE "
            f"phone='{phone}' "
            f"and name='{name}' "
            f"and account='{account}' "
            f"and del_flag=0"
        )
        result = db.get_all(sql)
        if result:
            data = result[len(result) - 1]
            account_info["exist_email"] = data.get("email")
            if not account_info.get("role"):
                account_info["role"] = data.get("role")
            if not account_info.get("service_provider"):
                account_info["service_provider"] = data.get("service_provider")
            if not account_info.get("position"):
                account_info["position"] = data.get("position")
        return account_info

    # 通过手机号查询账号信息
    @staticmethod
    def get_account_info_by_phone(table_list):
        for item in table_list:
            if item.get("phone"):
                # 查平台账号信息
                data = {"mobile": str(item.get("phone"))}
                query_result = gnetops.request(
                    action="Account", method="QueryByMobile", data=data
                )
                if query_result:
                    item["exist_name"] = query_result.get("chinese_name")
                    item["exist_account"] = query_result.get("keji_account")
                    item["exist_account_label"] = query_result.get("keji_user_name")
                    if not item.get("name"):
                        item["name"] = item["exist_name"]
                    # 查已有账号信息
                    item = ApplyToEnterProjectApi.query_exist_account_info(item)
        return table_list
