from iBroker.lib import mysql, curl
from iBroker.lib.sdk import gnetops


class Tools(object):
    # 校验项目名称、项目编号、需求单号是否配置齐全
    def check_project_cfg(project_name_list):
        error_project = []
        for project_name in project_name_list:
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
                "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
                f"WHERE rewd.project_name = '{project_name}' "
            )
            code_id_list = db.query(sql)
            if not code_id_list:
                error_project.append(project_name)
        return error_project

    # 获取po单号信息（集成商：集成工程、设备配套安装服务）
    @staticmethod
    def get_po_info(project_name):
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rewd.item_number, rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        code_id_list = db.query(sql)
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 品类匹配po信息表
            data_list = []
            # 物料id
            material_id_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                # 用需求单号获取po信息
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"},
                )
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    # 所有的物料id
                    po_material_info += po_info_list
                    for j in po_material_info:
                        material_id = j.get("materialId")
                        if material_id not in material_id_list:
                            material_id_list.append(material_id)
            # 通过物料id获取品名信息
            material_list = gnetops.request(
                action="Tbconstruct",
                method="ObtainMaterialInfoById",
                ext_data={"MaterialCodeId": material_id_list},
            )
            if material_list:
                material_info_list = material_list.get("materialList")
                # 匹配的物料id
                data = []
                for material in material_info_list:
                    # 物料id
                    material_id = material.get("materialQueryDTO").get("materialId")
                    # 品名
                    material_category_name = material.get("materialQueryDTO").get(
                        "materialCategoryName"
                    )
                    if (
                        material_category_name == "集成工程"
                        or material_category_name == "设备配套安装服务"
                    ):
                        data.append(material_id)
                # 去重po单号
                po_code_list = []
                for po in po_material_info:
                    if (
                        po.get("orderCode")
                        and po.get("orderCode") not in po_code_list
                        and po.get("materialId") in data
                    ):
                        po_code_list.append(po.get("orderCode"))
                        data_list.append(
                            {
                                "project_name": project_name,
                                "project_code": code_id_list[0]["item_number"],
                                "po_code": po.get("orderCode"),
                                "po_submit_time": po.get("poSubmitTime"),
                                "po_finish_time": po.get("poFinishTime"),
                                "po_issued_time": po.get("polssuedTime"),
                            }
                        )
            return data_list
        else:
            return False

    @staticmethod
    # 项目po信息存库
    def project_po_info_insert(insert_list):
        """
        主表付款信息insert
        """
        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_list:
            tb_db.insert_batch("project_po_info", insert_list)
        tb_db.commit()
        return True
