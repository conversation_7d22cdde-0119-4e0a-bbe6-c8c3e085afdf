import ast
import datetime
import json
import re

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


class DimensionConnectionWB(object):
    """
        接维资料
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(DimensionConnectionWB, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def dimension_connection_process_initiation(self, project_name):
        """
            创建工单--接维资料
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        ticket_id = self.ctx.variables.get("ticket_id")
        files_list = [
            {"FileType": "招采及合同文件", "FileName": "对应项目的集成商招标技术文件，包含技术应答文件、澄清应答", },
            {"FileType": "招采及合同文件", "FileName": "腾讯与TB集成商合同封面及合同中有关售后条款及附件", },
            {"FileType": "招采及合同文件",
             "FileName": "TB集成商与自采设备厂商（或工程分包）合同封面及合同中有关售后条款及附件", },
            {"FileType": "招采及合同文件",
             "FileName": "对应项目的腾讯集采设备的招标技术文件，包含技术应答文件、澄清应答", },
            {"FileType": "招采及合同文件",
             "FileName": "腾讯与腾讯集采设备厂家合同封面及合同中有关服务的条款及附件", },
            {"FileType": "招采及合同文件", "FileName": "电力批复文件（包含方案答复单等必要申请过程归档材料）", },
            {"FileType": "招采及合同文件", "FileName": "供电合同", },
            {"FileType": "招采及合同文件", "FileName": "供电调度协议", },
            {"FileType": "招采及合同文件", "FileName": "供水合同", },
            {"FileType": "招采及合同文件", "FileName": "供油协议", },
            {"FileType": "工厂预制资料", "FileName": "主要设备集成工厂厂验报告", },
            {"FileType": "厂验资料", "FileName": "甲供设备厂验检验报告", },
            {"FileType": "主要设备部件清单", "FileName": "主要设备部件清单", },
            {"FileType": "设备资料", "FileName": "各设备或系统合格证/质量证书/其他质量证明文件", },
            {"FileType": "设备资料", "FileName": "各设备或系统产品说明书/图纸", },
            {"FileType": "设备资料", "FileName": "各设备或系统操作手册/维护手册", },
            {"FileType": "设备资料", "FileName": "各设备或系统随机工具清单/随机备件清单", },
            {"FileType": "设备资料", "FileName": "各设备或系统软件或其他资料", },
            {"FileType": "培训资料", "FileName": "各设备或系统的培训资料", },
            {"FileType": "调试测试查验", "FileName": "中压电力设备试验报告", },
            {"FileType": "调试测试查验", "FileName": "自有变电站/开闭所送电手续材料", },
            {"FileType": "调试测试查验", "FileName": "各设备或系统调试/自测试报告", },
            {"FileType": "调试测试查验", "FileName": "第三方验证测试报告（简版）", },
            {"FileType": "调试测试查验", "FileName": "第三方验证测试报告（完整版）", },
            {"FileType": "调试测试查验", "FileName": "验证测试总结报告（内部）", },
            {"FileType": "专项验收", "FileName": "楼宇验收材料及其证明", },
            {"FileType": "专项验收", "FileName": "机房建设竣工验收材料及其证明", },
            {"FileType": "专项验收", "FileName": "消防验收材料及其证明", },
            {"FileType": "专项验收", "FileName": "环保验收材料及其证明", },
            {"FileType": "专项验收", "FileName": "供电验收材料及其证明", },
            {"FileType": "专项验收", "FileName": "防雷检测报告", },
            {"FileType": "专项验收", "FileName": "ODF端子验收报告", },
            {"FileType": "专项验收", "FileName": "水处理方仓出水水质检测报告", },
            {"FileType": "项目验收", "FileName": "相关承诺函件", },
            {"FileType": "项目验收", "FileName": "项目初验结论报告", },
            {"FileType": "工具备品备件清单", "FileName": "项目移交运维工具清单", },
            {"FileType": "工具备品备件清单", "FileName": "项目移交备品备件清单", },
            {"FileType": "标识命名清单", "FileName": "设备及路由标牌与设计图纸命名对照清单", },
            {"FileType": "标识命名清单", "FileName": "弱电及监控点位命名清单", },
            {"FileType": "标识命名清单", "FileName": "房间命名清单", },
            {"FileType": "数据备份", "FileName": "弱电、BA、电气逻辑等程序备份", },
            {"FileType": "数据备份", "FileName": "各设备或系统密码清单", },
            {"FileType": "厂商服务通讯录", "FileName": "各施工单位及维保单位责任人联系方式及升级方式", },
        ]
        data = {
            "TencentTicketId": ticket_id,
            "ProjectName": project_name,
            "FilesList": files_list
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/dimension_connection_process_initiation"
            }
        )
        PartnerTicketId = str(info['Data'])
        variables = {
            'info': str(info),
            'PartnerTicketId': PartnerTicketId
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_integrators_data_approval_results(self, project_name, examine_approve_result, dismiss_role, remark):
        """
            集成商接维资料审批结果反馈
        """
        Facilitator = self.ctx.variables.get("Facilitator")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "通过":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        data = {
            "ProjectName": project_name,  # 项目名称
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        info = gnetops.request(
            action='Request',
            method='RequestToFacilitator',
            ext_data={
                "Facilitator": Facilitator,
                "ReqData": data,
                "RequestMethod": "POST",
                "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/feedback_integrators_data_approval_results"
            }
        )
        if info.get('ErrorCode') != 0:
            raise Exception(f'报错信息为：{info.get("Message")}')
        variables = {
            'completion_audit_feedback': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceJwzl(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceJwzl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_integrators_data_upload(self):
        """
            集成商接维资料上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT file_name, file_type, url, uploader, upload_time " \
              "FROM maintenance_upload_data " \
              f"WHERE ticket_id = '{ticket_id}'" \
              f"AND project_name = '{project_name}' " \
              f"AND node = '集成商' "
        result_list = db.get_all(sql)
        url_file_list = []
        for record in result_list:
            # 获取 plan_url 字符串
            file_name = record.get('file_name', "")  # 文件名称
            file_type = record.get('file_type', "")  # 文件类型
            url = record.get('url', "[]")  # 文件
            uploader = record.get('uploader', "")  # 文件上传人
            upload_time = record.get('upload_time', "")  # 文件上传时间

            # 尝试将 url 字符串解析为列表
            try:
                urls = ast.literal_eval(url)  # 将字符串解析为列表
            except (ValueError, SyntaxError):
                urls = []  # 如果解析失败，设置为空列表

            url_list = []
            # 确保 urls 是一个列表
            if isinstance(urls, list):
                # 为每个 URL 创建一个新的字典
                for url in urls:
                    url_list.append({"url": url})

            url_file_list.append({
                'file_name': file_name,
                'file_type': file_type,
                'url': url_list,
                'uploader': uploader,
                'upload_time': upload_time
            })

        self.workflow_detail.add_kv_table('集成商接维资料上传信息', {'message': result_list})
        if result_list:
            variables = {
                "jcs_file_list": url_file_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}


class PartnerCallInterfaceJwzl(object):
    """
        合作方调用接口：传入数据
    """

    def __init__(self):
        super(PartnerCallInterfaceJwzl, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def integrators_data_upload(self, TencentTicketId, ProjectName, IntegratorsList, UploadTime, UploadPeople):
        """
            集成商接维资料上传
        """
        insert_data = []

        # 校验 TencentTicketId 和 ProjectName
        if not TencentTicketId or not ProjectName:
            return {'code': -1, 'msg': '工单号或项目名称缺失'}
        # 校验 UploadTime 和 UploadPeople
        if not UploadTime:
            return {'code': -1, 'msg': '上传时间缺失'}
        if not UploadPeople:
            return {'code': -1, 'msg': '上传人缺失'}

        for row in IntegratorsList:
            file_type = row.get('FileType')
            file_name = row.get('FileName')
            file = row.get('File')
            remark = row.get('Remark')

            insert_data.append({
                'ticket_id': TencentTicketId,
                'project_name': ProjectName,
                'file_type': file_type,
                'file_name': file_name,
                'url': str(file),
                'upload_time': UploadTime,
                'uploader': UploadPeople,
                'file_remark': remark,
                'node': "集成商"
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_data:
            tb_db.insert_batch("maintenance_upload_data", insert_data)
        tb_db.commit()
        return {'code': 200, 'msg': '成功'}


class JwzlSupervisionExaminationApproval(AjaxTodoBase):
    """
        集成商接维资料-监理审批
    """

    def __init__(self):
        super(JwzlSupervisionExaminationApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jwzl_supervision_approval = process_data.get('jwzl_supervision_approval')
        jwzl_supervision_remark = process_data.get('jwzl_supervision_remark')

        if jwzl_supervision_approval == '驳回':
            query_sql = "DELETE FROM maintenance_upload_data " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND node ='集成商'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jwzl_supervision_approval': jwzl_supervision_approval,
            'jwzl_supervision_remark': jwzl_supervision_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JwzlItemApproval(AjaxTodoBase):
    """
        集成商接维资料-项管审批
    """

    def __init__(self):
        super(JwzlItemApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jwzl_item_approval = process_data.get('jwzl_item_approval')
        jwzl_item_remark = process_data.get('jwzl_item_remark')

        if jwzl_item_approval == '驳回':
            query_sql = "DELETE FROM maintenance_training " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND node ='集成商'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jwzl_item_approval': jwzl_item_approval,
            'jwzl_item_remark': jwzl_item_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JwzlPmApproval(AjaxTodoBase):
    """
        集成商接维资料-PM审批
    """

    def __init__(self):
        super(JwzlPmApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        jwzl_PM_approval = process_data.get('jwzl_PM_approval')
        jwzl_PM_remark = process_data.get('jwzl_PM_remark')

        if jwzl_PM_approval == '驳回':
            query_sql = "DELETE FROM maintenance_training " \
                        f"WHERE ticket_id = '{ticket_id}' " \
                        f"AND project_name ='{project_name}'" \
                        "AND node ='集成商'"
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.query(query_sql)
            tb_db.commit()

        variables = {
            'jwzl_PM_approval': jwzl_PM_approval,
            'jwzl_PM_remark': jwzl_PM_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
