import ast
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue

from biz.cooperative_partner.project_application_partner.tools import Tools
from biz.project_member_account_delete.account_delete_api import (
    AccountDeleteApi,
)


class ConstructionApplicationProcessPartnerAutoTask(object):

    def __init__(self):
        super(ConstructionApplicationProcessPartnerAutoTask, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def empty_task(self):
        """
        空任务-等完工确认数据提交后扭转
        """
        pass

    def get_serial_number_cfg(self, wait_level1, wait_level2, wait_level3, wait_level4):
        """
        项目报建-等待子任务工单完成节点配置信息
        """
        variables = {
            "wait_level1": wait_level1,
            "wait_level2": wait_level2,
            "wait_level3": wait_level3,
            "wait_level4": wait_level4,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_construction_plan(self, project_name, role_list):
        """
        项目报建-获取全部子任务计划、流程进展待办处理人(PM、账号表对应角色)
        """
        construction_plan = Tools.query_construction_plan(project_name)
        role_list = ast.literal_eval(str(role_list))
        account = []
        xg = AccountDeleteApi.get_role_leader(project_name, "项管-项目经理")
        account.append(xg)
        for role in role_list:
            if role == "PM":
                PM = AccountDeleteApi.get_PM(project_name)
                account.append(PM)
            else:
                user = AccountDeleteApi.get_role_leader(project_name, role)
                account.append(user)
        flow_trace_user = list(set(account))
        variables = {
            "construction_plan": construction_plan,
            "flow_trace_user": flow_trace_user,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_subtask_ticket(self, project_name, serial_number_list: list = None):
        """
        项目报建-起子任务工单
        """
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        Facilitator = self.ctx.variables.get("Facilitator")
        subtask_ticket = self.ctx.variables.get("subtask_ticket", {})
        all_serial_number_list = self.ctx.variables.get("all_serial_number_list", [])
        all_subtask_plan = self.ctx.variables.get("all_subtask_plan", [])
        if serial_number_list:
            serial_number_list = ast.literal_eval(str(serial_number_list))
            all_serial_number_list.append(serial_number_list)
            main_ticket_id = self.ctx.variables.get("ticket_id")
            subtask_plan = Tools.query_subtask_plan(project_name, serial_number_list)
            all_subtask_plan = [*all_subtask_plan, subtask_plan]
            for p in subtask_plan:
                if p.get("not_involved") == 0:
                    data = {
                        "main_ticket_id": main_ticket_id,
                        "project_name": project_name,
                        "serial_number": p.get("serial_number"),
                        "work_content": p.get("work_content"),
                        "PartnerTicketId": PartnerTicketId,
                        "Facilitator": Facilitator,
                    }
                    create_ticket = Tools.create_ticket(data)
                    data["create_ticket_res"] = create_ticket
                    ticket_id = str(create_ticket.get("TicketId"))
                    subtask_ticket = {
                        **subtask_ticket,
                        p.get("serial_number"): ticket_id,
                    }
                else:
                    subtask_ticket = {
                        **subtask_ticket,
                        p.get("serial_number"): "不涉及",
                    }
        variables = {
            "subtask_ticket": subtask_ticket,
            "all_serial_number_list": all_serial_number_list,
            "all_subtask_plan": all_subtask_plan,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def wait_subtask_ticket(self, serial_number_list: list = None):
        """
        等待子任务工单完成
        serial_number_list: 指定子任务, 为空则取全部子任务
        """
        subtask_ticket = self.ctx.variables.get("subtask_ticket", {})
        subtask_ticket_id_list = []
        if serial_number_list:
            serial_number_list = ast.literal_eval(str(serial_number_list))
            subtask_ticket_id_list = [
                subtask_ticket.get(val)
                for val in serial_number_list
                if subtask_ticket.get(val) and subtask_ticket.get(val) != "不涉及"
            ]
        else:
            subtask_ticket_id_list = [
                val for key, val in subtask_ticket.items() if val and val != "不涉及"
            ]
        data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {"TicketStatus": "", "TicketId": ""},
                "SearchCondition": {"TicketId": subtask_ticket_id_list},
            },
        }
        res = gnetops.request(action="QueryData", method="Run", ext_data=data)
        res_list = res.get("List")
        for item in res_list:
            if item.get("TicketStatus") == "OPEN":
                return {
                    "success": False,
                    "data": f"子任务工单未完成：{item.get('TicketId')}",
                }
        WorkflowContinue(task_id=self.ctx.task_id).call()
        return {"success": True, "data": "子任务工单全部完成"}
