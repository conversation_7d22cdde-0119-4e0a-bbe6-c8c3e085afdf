from biz.quality_evaluation.standard import StandardQuery


# 团队需要评分的数据
class TeamRatingHandle(object):

    # 组装团队评分导入模板
    @staticmethod
    def get_team_rating_list_template(
        team_name, project_info_list, key_indicator, project_category_basic_info_dict
    ):
        """
        组装团队评分导入模板
        business_rating_list_template: {
            class1_template: [
                {
                    title: project_name(前端自增tab title)
                    项目: project_name
                    团队: team_name(前端写死)
                    AHU: ahu
                    柴发: cf
                    变压器: byq
                    中压柜: zyg
                    低压柜: dyg
                    综合配电柜+HVDC: hvdc
                }
            ],
        }
        """
        team_rating_list_template = {
            "class1_template": [],
            # "class2_template": [],
            # "class3_template": [],
            "class4_template": [],
            "class5_template": [],
            "class6_template": [],
            "class7_template": [],
        }
        if key_indicator["key_indicator1"]:
            # if (
            #     "黄线" in key_indicator["key_indicator1"]
            #     or "红线" in key_indicator["key_indicator1"]
            # ):
            key_indicator["key_indicator1"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    # "title": project_name,
                    "project_name": project_name,
                    "team_name": team_name,
                    # "rating_list": [
                    #     {
                    #         "key_indicator": "供应商",
                    #         **project_category_basic_info["category_supplier"],
                    #     }
                    # ],
                }
                category_key_dict1 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                }
                category_key_dict2 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    # 弱电的机柜、PDU、电池分数为0（不需要打分）
                    "weak_electricity_default": {
                        key: "0"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                }
                # 每个项目第一行塞入供应商数据
                team_rating_list_template["class1_template"].append(
                    {
                        **team_project_rating_data,
                        "key_indicator": "供应商",
                        "scoring_rubrics": "",
                        **project_category_basic_info["category_supplier"],
                    }
                )
                for key_indicator_item in key_indicator["key_indicator1"]:
                    standard_detail = StandardQuery.get_standard_detail(
                        team_name, 1, key_indicator_item
                    )
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_rating_list_template["class1_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict1["red_yellow_line"],
                                # **category_key_dict2["red_yellow_line"],
                                # 模板默认为空
                                **category_key_dict1["remark"],
                                **category_key_dict2["remark"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_rating_list_template["class1_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": "",
                                **category_key_dict1["remark"],
                                **category_key_dict2["remark"],
                            }
                        )
                    else:
                        # 弱电的机柜、PDU、电池分数为0（不需要打分）
                        if team_name == "弱电":
                            team_rating_list_template["class1_template"].append(
                                {
                                    **team_project_rating_data,
                                    "key_indicator": key_indicator_item,
                                    "scoring_rubrics": standard_detail[
                                        "scoring_rubrics"
                                    ],
                                    # **category_key_dict1["default"],
                                    # **category_key_dict2["weak_electricity_default"],
                                    # 模板默认为空
                                    **category_key_dict1["remark"],
                                    # **category_key_dict2["remark"],
                                }
                            )
                        else:
                            team_rating_list_template["class1_template"].append(
                                {
                                    **team_project_rating_data,
                                    "key_indicator": key_indicator_item,
                                    "scoring_rubrics": standard_detail[
                                        "scoring_rubrics"
                                    ],
                                    # **category_key_dict1["default"],
                                    # **category_key_dict2["default"],
                                    # 模板默认为空
                                    **category_key_dict1["remark"],
                                    **category_key_dict2["remark"],
                                }
                            )

        if key_indicator["key_indicator4"]:
            # if (
            #     "黄线" in key_indicator["key_indicator4"]
            #     or "红线" in key_indicator["key_indicator4"]
            # ):
            key_indicator["key_indicator4"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    # "rating_list": [
                    #     {
                    #         "key_indicator": "供应商",
                    #         **project_category_basic_info["category_supplier"],
                    #     }
                    # ],
                }
                category_key_dict4 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                }
                # 每个项目第一行塞入供应商数据
                team_rating_list_template["class4_template"].append(
                    {
                        **team_project_rating_data,
                        "key_indicator": "供应商",
                        **project_category_basic_info["category_supplier"],
                    }
                )
                for key_indicator_item in key_indicator["key_indicator4"]:
                    standard_detail = StandardQuery.get_standard_detail(
                        team_name, 4, key_indicator_item
                    )
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_rating_list_template["class4_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict4["red_yellow_line"],
                                # 模板默认为空
                                **category_key_dict4["remark"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_rating_list_template["class4_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": "",
                                **category_key_dict4["remark"],
                            }
                        )
                    else:
                        team_rating_list_template["class4_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict4["default"],
                                # 模板默认为空
                                **category_key_dict4["remark"],
                            }
                        )
        if key_indicator["key_indicator5"]:
            # if (
            #     "黄线" in key_indicator["key_indicator5"]
            #     or "红线" in key_indicator["key_indicator5"]
            # ):
            key_indicator["key_indicator5"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    # "rating_list": [
                    #     {
                    #         "key_indicator": "供应商",
                    #         **project_category_basic_info["category_supplier"],
                    #     }
                    # ],
                }
                category_key_dict5 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                }
                # 每个项目第一行塞入供应商数据
                team_rating_list_template["class5_template"].append(
                    {
                        **team_project_rating_data,
                        "key_indicator": "供应商",
                        **project_category_basic_info["category_supplier"],
                    }
                )
                for key_indicator_item in key_indicator["key_indicator5"]:
                    standard_detail = StandardQuery.get_standard_detail(
                        team_name, 5, key_indicator_item
                    )
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_rating_list_template["class5_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict5["red_yellow_line"],
                                # 模板默认为空
                                **category_key_dict5["remark"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_rating_list_template["class5_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": "",
                                **category_key_dict5["remark"],
                            }
                        )
                    else:
                        team_rating_list_template["class5_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict5["default"],
                                # 模板默认为空
                                **category_key_dict5["remark"],
                            }
                        )
        if key_indicator["key_indicator6"]:
            # if (
            #     "黄线" in key_indicator["key_indicator6"]
            #     or "红线" in key_indicator["key_indicator6"]
            # ):
            key_indicator["key_indicator6"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    # "rating_list": [
                    #     {
                    #         "key_indicator": "供应商",
                    #         **project_category_basic_info["category_supplier"],
                    #     }
                    # ],
                }
                category_key_dict6 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                }
                # 每个项目第一行塞入供应商数据
                team_rating_list_template["class6_template"].append(
                    {
                        **team_project_rating_data,
                        "key_indicator": "供应商",
                        **project_category_basic_info["category_supplier"],
                    }
                )
                for key_indicator_item in key_indicator["key_indicator6"]:
                    standard_detail = StandardQuery.get_standard_detail(
                        team_name, 6, key_indicator_item
                    )
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_rating_list_template["class6_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict6["red_yellow_line"],
                                # 模板默认为空
                                **category_key_dict6["remark"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_rating_list_template["class6_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": "",
                                **category_key_dict6["remark"],
                            }
                        )
                    else:
                        team_rating_list_template["class6_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict6["default"],
                                # 模板默认为空
                                **category_key_dict6["remark"],
                            }
                        )
        if key_indicator["key_indicator7"]:
            # if (
            #     "黄线" in key_indicator["key_indicator7"]
            #     or "红线" in key_indicator["key_indicator7"]
            # ):
            key_indicator["key_indicator7"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    # "rating_list": [
                    #     {
                    #         "key_indicator": "供应商",
                    #         **project_category_basic_info["category_supplier"],
                    #     }
                    # ],
                }
                category_key_dict7 = {
                    "default": {
                        key: "80"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                    "red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                }
                # 每个项目第一行塞入供应商数据
                team_rating_list_template["class7_template"].append(
                    {
                        **team_project_rating_data,
                        "key_indicator": "供应商",
                        **project_category_basic_info["category_supplier"],
                    }
                )
                for key_indicator_item in key_indicator["key_indicator7"]:
                    standard_detail = StandardQuery.get_standard_detail(
                        team_name, 7, key_indicator_item
                    )
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_rating_list_template["class7_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict7["red_yellow_line"],
                                # 模板默认为空
                                **category_key_dict7["remark"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_rating_list_template["class7_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": "",
                                **category_key_dict7["remark"],
                            }
                        )
                    else:
                        team_rating_list_template["class7_template"].append(
                            {
                                **team_project_rating_data,
                                "key_indicator": key_indicator_item,
                                "scoring_rubrics": standard_detail["scoring_rubrics"],
                                # **category_key_dict7["default"],
                                # 模板默认为空
                                **category_key_dict7["remark"],
                            }
                        )
        return team_rating_list_template

    # 组装团队需要评分的数据
    @staticmethod
    def get_team_rating_list(
        team_name, project_info_list, key_indicator, project_category_basic_info_dict
    ):
        """
        组装团队需要评分的数据
        business_rating_list: {
            class1_project_list: [
                {
                    title: project_name(前端自增tab title)
                    项目: project_name
                    团队: team_name(前端写死)
                    分数表格: rating_list: [
                        {
                            品类(关键评价指标):key_indicator
                            AHU: ahu
                            柴发: cf
                            变压器: byq
                            中压柜: zyg
                            低压柜: dyg
                            综合配电柜+HVDC: hvdc
                        }
                    ]
                }
            ],
        }
        """
        team_rating_list = {
            "class1_project_list": [],
            # "class2_project_list": [],
            # "class3_project_list": [],
            "class4_project_list": [],
            "class5_project_list": [],
            "class6_project_list": [],
            "class7_project_list": [],
        }
        if key_indicator["key_indicator1"]:
            # if (
            #     "黄线" in key_indicator["key_indicator1"]
            #     or "红线" in key_indicator["key_indicator1"]
            # ):
            key_indicator["key_indicator1"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "title": project_name,
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_list": [
                        {
                            "key_indicator": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
                category_key_dict1 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class1_category"]
                    },
                }
                category_key_dict2 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    # 弱电的机柜、PDU、电池分数为0（不需要打分）
                    "weak_electricity_default": {
                        key: "0"
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                    "weak_electricity_red_yellow_line": {
                        key: 0
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class2_category"]
                    },
                }
                for key_indicator_item in key_indicator["key_indicator1"]:
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        if team_name == "弱电":
                            team_project_rating_data["rating_list"].append(
                                {
                                    "key_indicator": key_indicator_item,
                                    **category_key_dict1["red_yellow_line"],
                                    **category_key_dict2[
                                        "weak_electricity_red_yellow_line"
                                    ],
                                }
                            )
                        else:
                            team_project_rating_data["rating_list"].append(
                                {
                                    "key_indicator": key_indicator_item,
                                    **category_key_dict1["red_yellow_line"],
                                    **category_key_dict2["red_yellow_line"],
                                }
                            )
                    elif key_indicator_item == "备注":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict1["remark"],
                                **category_key_dict2["remark"],
                            }
                        )
                    else:
                        # 弱电的机柜、PDU、电池分数为0（不需要打分）
                        if team_name == "弱电":
                            team_project_rating_data["rating_list"].append(
                                {
                                    "key_indicator": key_indicator_item,
                                    **category_key_dict1["default"],
                                    **category_key_dict2["weak_electricity_default"],
                                }
                            )
                        else:
                            team_project_rating_data["rating_list"].append(
                                {
                                    "key_indicator": key_indicator_item,
                                    **category_key_dict1["default"],
                                    **category_key_dict2["default"],
                                }
                            )
                team_rating_list["class1_project_list"].append(team_project_rating_data)

        """
        应业务需求，页面集采设备质量评价标准展示三合一：1、"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)"  2、"集采设备质量评价(机柜、PDU、电池)" 3、"集采设备质量评价(JDM等研发产品)" 
        1和2只是2的品类的团队占比不同。
        1和3暂定都一样。
        页面展示占比为维度占比，团队占比用户感知不到，只在后端评分汇总计算时使用。
        因此，页面集采设备质量评价展示统一取 1"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)"；
              品类评分数据展示 1"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)" 2"集采设备质量评价(机柜、PDU、电池)"
        """

        if key_indicator["key_indicator4"]:
            # if (
            #     "黄线" in key_indicator["key_indicator4"]
            #     or "红线" in key_indicator["key_indicator4"]
            # ):
            key_indicator["key_indicator4"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_list": [
                        {
                            "key_indicator": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
                category_key_dict4 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class4_category"]
                    },
                }
                for key_indicator_item in key_indicator["key_indicator4"]:
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict4["red_yellow_line"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict4["remark"],
                            }
                        )
                    else:
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict4["default"],
                            }
                        )
                team_rating_list["class4_project_list"].append(team_project_rating_data)

        if key_indicator["key_indicator5"]:
            # if (
            #     "黄线" in key_indicator["key_indicator5"]
            #     or "红线" in key_indicator["key_indicator5"]
            # ):
            key_indicator["key_indicator5"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_list": [
                        {
                            "key_indicator": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
                category_key_dict5 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class5_category"]
                    },
                }
                for key_indicator_item in key_indicator["key_indicator5"]:
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict5["red_yellow_line"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict5["remark"],
                            }
                        )
                    else:
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict5["default"],
                            }
                        )
                team_rating_list["class5_project_list"].append(team_project_rating_data)

        if key_indicator["key_indicator6"]:
            # if (
            #     "黄线" in key_indicator["key_indicator6"]
            #     or "红线" in key_indicator["key_indicator6"]
            # ):
            key_indicator["key_indicator6"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_list": [
                        {
                            "key_indicator": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
                category_key_dict6 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class6_category"]
                    },
                }
                for key_indicator_item in key_indicator["key_indicator6"]:
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict6["red_yellow_line"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict6["remark"],
                            }
                        )
                    else:
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict6["default"],
                            }
                        )
                team_rating_list["class6_project_list"].append(team_project_rating_data)

        if key_indicator["key_indicator7"]:
            # if (
            #     "黄线" in key_indicator["key_indicator7"]
            #     or "红线" in key_indicator["key_indicator7"]
            # ):
            key_indicator["key_indicator7"].append("备注")
            for project in project_info_list:
                project_name = project["project_name"]
                project_name = project["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                team_project_rating_data = {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_list": [
                        {
                            "key_indicator": "供应商",
                            **project_category_basic_info["category_supplier"],
                        }
                    ],
                }
                category_key_dict7 = {
                    "default": {
                        # key: "80"
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                    "remark": {
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                    "red_yellow_line": {
                        # key: 0
                        key: ""
                        for key in project_category_basic_info[
                            "standard_class_category_name_en"
                        ]["standard_class7_category"]
                    },
                }
                for key_indicator_item in key_indicator["key_indicator7"]:
                    if key_indicator_item == "黄线" or key_indicator_item == "红线":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict7["red_yellow_line"],
                            }
                        )
                    elif key_indicator_item == "备注":
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict7["remark"],
                            }
                        )
                    else:
                        team_project_rating_data["rating_list"].append(
                            {
                                "key_indicator": key_indicator_item,
                                **category_key_dict7["default"],
                            }
                        )
                team_rating_list["class7_project_list"].append(team_project_rating_data)

        return team_rating_list

    # 处理团队评分字段（团队领导审批需看到打分人）
    @staticmethod
    def add_field(team_rating_list, field, value):
        if "class1_project_list" in team_rating_list:
            for i in range(len(team_rating_list["class1_project_list"])):
                team_rating_list["class1_project_list"][i].setdefault(field, value)
        # if "class2_project_list" in team_rating_list:
        #     for i in range(len(team_rating_list["class2_project_list"])):
        #         team_rating_list["class2_project_list"][i].setdefault(field, value)
        if "class4_project_list" in team_rating_list:
            for i in range(len(team_rating_list["class4_project_list"])):
                team_rating_list["class4_project_list"][i].setdefault(field, value)
        if "class5_project_list" in team_rating_list:
            for i in range(len(team_rating_list["class5_project_list"])):
                team_rating_list["class5_project_list"][i].setdefault(field, value)
        if "class6_project_list" in team_rating_list:
            for i in range(len(team_rating_list["class6_project_list"])):
                team_rating_list["class6_project_list"][i].setdefault(field, value)
        if "class7_project_list" in team_rating_list:
            for i in range(len(team_rating_list["class7_project_list"])):
                team_rating_list["class7_project_list"][i].setdefault(field, value)
        return team_rating_list

    # 校验评分数据(单个团队)
    @staticmethod
    def checkout_team_rating_list(team_rating_list, project_category_basic_info_dict):
        score_rule = {
            "0_1": ["黄线", "红线"],
            "0_100": ["其余关键指标"],
            "default": ["备注", "供应商"],
        }
        score_0_1_tip = "评分只能输入0/1，(无则为0，有则为1)"
        score_0_100_tip = "评分只能输入0-100"
        score_value_error = "可能未输入值或有空格等非法字符，请仔细检查；"
        class_project_list_error = []
        # ignore_filed = ["key_indicator"]
        if "class1_project_list" in team_rating_list:
            for project_dict in team_rating_list["class1_project_list"]:
                project_name = project_dict["project_name"]
                team_name = project_dict["team_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class1_category"]
                    # 弱电 无机柜、PDU、电池
                    if team_name != "弱电":
                        category_list = [
                            *category_list,
                            *project_category_basic_info[
                                "standard_class_category_name_en"
                            ]["standard_class2_category"],
                        ]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class4_project_list" in team_rating_list:
            for project_dict in team_rating_list["class4_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class4_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"集成商质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"集成商质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class5_project_list" in team_rating_list:
            for project_dict in team_rating_list["class5_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class5_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"项管管理质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"项管管理质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class6_project_list" in team_rating_list:
            for project_dict in team_rating_list["class6_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class6_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"监理服务质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"监理服务质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class7_project_list" in team_rating_list:
            for project_dict in team_rating_list["class7_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class7_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"第三方测试质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"第三方测试质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if class_project_list_error:
            error = "；<br>".join(class_project_list_error)
            return {
                "code": -1,
                "msg": f"评分错误。<br>错误内容：<br>{error}。<br>请重新评分！",
            }, False
        return {"code": 0, "msg": "评分正确"}, True

    # 校验评分数据(评分汇总)
    @staticmethod
    def checkout_team_rating_standard_list(
        team_rating_standard_list, project_category_basic_info_dict
    ):
        score_rule = {
            "0_1": ["黄线", "红线"],
            "0_100": ["其余关键指标"],
            "default": ["备注", "供应商"],
        }
        score_0_1_tip = "评分只能输入0/1，(无则为0，有则为1)"
        score_0_100_tip = "评分只能输入0-100"
        score_value_error = "可能未输入值或有空格等非法字符，请仔细检查；"
        class_project_list_error = []
        # ignore_filed = ["key_indicator", "evaluation_dimensions", "dimension_weight", "assessment_point",
        # "team_name", "team_weight", "category_eq_class_name", "category_eq_class_id", "scoring_rubrics"]
        if "class1_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class1_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class1_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class2_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class2_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class2_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集采设备质量评价(机柜、PDU、电池)，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class4_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class4_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class4_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"集成商质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"集成商质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"集成商质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class5_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class5_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class5_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"项管管理质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"项管管理质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"项管管理质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class6_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class6_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class6_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"监理服务质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"监理服务质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"监理服务质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if "class7_project_list" in team_rating_standard_list:
            for project_dict in team_rating_standard_list["class7_project_list"]:
                project_name = project_dict["project_name"]
                project_category_basic_info = project_category_basic_info_dict[
                    project_name
                ]
                category_en_cn_field_dict = project_category_basic_info[
                    "category_en_cn_field_dict"
                ]
                for rating in project_dict["rating_standard_list"]:
                    key_indicator = rating["key_indicator"]
                    category_list = project_category_basic_info[
                        "standard_class_category_name_en"
                    ]["standard_class7_category"]
                    # 只能输入0/1
                    if key_indicator in score_rule["0_1"]:
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) != 0 and float(score) != 1:
                                    class_project_list_error.append(
                                        (
                                            f"第三方测试质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_1_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_1_tip}"
                                    )
                                )
                    # 只能输入0-100
                    elif (
                        key_indicator not in score_rule["0_1"]
                        and key_indicator not in score_rule["default"]
                    ):
                        # for category, score in rating.items():
                        for category in category_list:
                            try:
                                score = rating[category]
                                if float(score) < 0 or float(score) > 100:
                                    class_project_list_error.append(
                                        (
                                            f"第三方测试质量评价，{key_indicator}，"
                                            f"{category_en_cn_field_dict[category]}{score_0_100_tip}"
                                        )
                                    )
                            except ValueError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )
                            except KeyError:
                                class_project_list_error.append(
                                    (
                                        f"第三方测试质量评价，{key_indicator}，"
                                        f"{category_en_cn_field_dict[category]}{score_value_error}{score_0_100_tip}"
                                    )
                                )

        if class_project_list_error:
            error = "；<br>".join(class_project_list_error)
            return {
                "code": -1,
                "msg": f"评分错误。<br>错误内容：<br>{error}。<br>请重新评分！",
            }, False
        return {"code": 0, "msg": "评分正确"}, True

    # 组装团队评分数据和评价标准
    def get_team_rating_standard_list(
        team_rating_list, project_category_basic_info_dict
    ):
        team_rating_standard_list = {
            "class1_project_list": [],
            "class2_project_list": [],
            # "class3_project_list": [],
            "class4_project_list": [],
            "class5_project_list": [],
            "class6_project_list": [],
            "class7_project_list": [],
        }
        # 遍历团队打分数据(同时获取对应评价标准数据)
        for project in team_rating_list.get("class1_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 1, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class1_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class1_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        for project in team_rating_list.get("class1_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 2, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class2_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class2_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        for project in team_rating_list.get("class4_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 4, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class4_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class4_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        for project in team_rating_list.get("class5_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 5, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class5_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class5_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        for project in team_rating_list.get("class6_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 6, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class6_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class6_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        for project in team_rating_list.get("class7_project_list", []):
            project_name = project["project_name"]
            team_name = project["team_name"]
            rating_list = project["rating_list"]
            rating_standard_list = []
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in rating_list:
                key_indicator = rating["key_indicator"]
                standard_detail = StandardQuery.get_standard_detail(
                    team_name, 7, key_indicator
                )
                category_rating = {
                    category: rating.get(category, "")
                    for category in standard_class_category_name_en[
                        "standard_class7_category"
                    ]
                }
                rating_standard_list.append(
                    {
                        # "project_name": project_name,
                        "team_name": team_name,
                        "key_indicator": key_indicator,
                        "evaluation_dimensions": standard_detail[
                            "evaluation_dimensions"
                        ],
                        "dimension_weight": standard_detail["dimension_weight"],
                        "assessment_point": standard_detail["assessment_point"],
                        "scoring_rubrics": standard_detail["scoring_rubrics"],
                        "team_weight": standard_detail["team_weight"],
                        "category_eq_class_id": standard_detail["category_eq_class_id"],
                        "category_eq_class_name": standard_detail[
                            "category_eq_class_name"
                        ],
                        **category_rating,
                    }
                )
            team_rating_standard_list["class7_project_list"].append(
                {
                    "project_name": project_name,
                    "team_name": team_name,
                    "rating_standard_list": rating_standard_list,
                }
            )
        return team_rating_standard_list

    # 合并所有团队打分数据
    @staticmethod
    def get_all_team_rating_standard_list(
        business_rating_standard_list,
        supply_chain_rating_standard_list,
        planning_rating_standard_list,
        product_rating_standard_list,
        project_rating_standard_list,
        optimize_rating_standard_list,
        weak_electricity_rating_standard_list,
    ):
        # 合并 项目—团队-rating_standard_list
        temp = {
            "class1_project_list": [
                *business_rating_standard_list.get("class1_project_list", []),
                *supply_chain_rating_standard_list.get("class1_project_list", []),
                *planning_rating_standard_list.get("class1_project_list", []),
                *product_rating_standard_list.get("class1_project_list", []),
                *project_rating_standard_list.get("class1_project_list", []),
                *optimize_rating_standard_list.get("class1_project_list", []),
                *weak_electricity_rating_standard_list.get("class1_project_list", []),
            ],
            "class2_project_list": [
                *business_rating_standard_list.get("class2_project_list", []),
                *supply_chain_rating_standard_list.get("class2_project_list", []),
                *planning_rating_standard_list.get("class2_project_list", []),
                *product_rating_standard_list.get("class2_project_list", []),
                *project_rating_standard_list.get("class2_project_list", []),
                *optimize_rating_standard_list.get("class2_project_list", []),
                *weak_electricity_rating_standard_list.get("class2_project_list", []),
            ],
            # "class3_project_list": [],
            "class4_project_list": [
                *business_rating_standard_list.get("class4_project_list", []),
                *supply_chain_rating_standard_list.get("class4_project_list", []),
                *planning_rating_standard_list.get("class4_project_list", []),
                *product_rating_standard_list.get("class4_project_list", []),
                *project_rating_standard_list.get("class4_project_list", []),
                *optimize_rating_standard_list.get("class4_project_list", []),
                *weak_electricity_rating_standard_list.get("class4_project_list", []),
            ],
            "class5_project_list": [
                *business_rating_standard_list.get("class5_project_list", []),
                *supply_chain_rating_standard_list.get("class5_project_list", []),
                *planning_rating_standard_list.get("class5_project_list", []),
                *product_rating_standard_list.get("class5_project_list", []),
                *project_rating_standard_list.get("class5_project_list", []),
                *optimize_rating_standard_list.get("class5_project_list", []),
                *weak_electricity_rating_standard_list.get("class5_project_list", []),
            ],
            "class6_project_list": [
                *business_rating_standard_list.get("class6_project_list", []),
                *supply_chain_rating_standard_list.get("class6_project_list", []),
                *planning_rating_standard_list.get("class6_project_list", []),
                *product_rating_standard_list.get("class6_project_list", []),
                *project_rating_standard_list.get("class6_project_list", []),
                *optimize_rating_standard_list.get("class6_project_list", []),
                *weak_electricity_rating_standard_list.get("class6_project_list", []),
            ],
            "class7_project_list": [
                *business_rating_standard_list.get("class7_project_list", []),
                *supply_chain_rating_standard_list.get("class7_project_list", []),
                *planning_rating_standard_list.get("class7_project_list", []),
                *product_rating_standard_list.get("class7_project_list", []),
                *project_rating_standard_list.get("class7_project_list", []),
                *optimize_rating_standard_list.get("class7_project_list", []),
                *weak_electricity_rating_standard_list.get("class7_project_list", []),
            ],
        }

        all_team_rating_standard_list = {
            "class1_project_list": [],
            "class2_project_list": [],
            # "class3_project_list": [],
            "class4_project_list": [],
            "class5_project_list": [],
            "class6_project_list": [],
            "class7_project_list": [],
        }

        # 合并 项目-rating_standard_list 并排序
        project_rating_standard_dict1 = {}
        for project in temp["class1_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict1:
                project_rating_standard_dict1[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict1[project_name]
                    ):
                        project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict1[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict1[project_name]
                    ):
                        project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict1[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict1[project_name]
                    ):
                        project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict1[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict1[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict1.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class1_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        project_rating_standard_dict2 = {}
        for project in temp["class2_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict2:
                project_rating_standard_dict2[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict2[project_name]
                    ):
                        project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict2[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict2[project_name]
                    ):
                        project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict2[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict2[project_name]
                    ):
                        project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict2[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict2[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)

        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict2.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class2_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        project_rating_standard_dict4 = {}
        for project in temp["class4_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict4:
                project_rating_standard_dict4[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict4[project_name]
                    ):
                        project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict4[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict4[project_name]
                    ):
                        project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict4[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict4[project_name]
                    ):
                        project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict4[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict4[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict4.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class4_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        project_rating_standard_dict5 = {}
        for project in temp["class5_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict5:
                project_rating_standard_dict5[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict5[project_name]
                    ):
                        project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict5[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict5[project_name]
                    ):
                        project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict5[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict5[project_name]
                    ):
                        project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict5[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict5[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict5.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class5_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )
        project_rating_standard_dict6 = {}
        for project in temp["class6_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict6:
                project_rating_standard_dict6[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict6[project_name]
                    ):
                        project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict6[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict6[project_name]
                    ):
                        project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict6[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict6[project_name]
                    ):
                        project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict6[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict6[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict6.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class6_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )

        project_rating_standard_dict7 = {}
        for project in temp["class7_project_list"]:
            project_name = project["project_name"]
            if project_name not in project_rating_standard_dict7:
                project_rating_standard_dict7[project_name] = {
                    "供应商": {},
                    "商务": {},
                    "质量": {},
                    "交付": {},
                    "服务": {},
                    "扣分项": {},
                    "备注": {},
                }
            for rating_standard in project["rating_standard_list"]:
                key_indicator = rating_standard["key_indicator"]
                if key_indicator == "供应商":
                    evaluation_dimensions = rating_standard["key_indicator"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict7[project_name]
                    ):
                        project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ] = {}
                    rating_standard["team_name"] = ""
                    project_rating_standard_dict7[project_name][evaluation_dimensions][
                        key_indicator
                    ] = [rating_standard]
                elif key_indicator == "备注":
                    evaluation_dimensions = "备注"
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict7[project_name]
                    ):
                        project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict7[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
                else:
                    evaluation_dimensions = rating_standard["evaluation_dimensions"]
                    if (
                        evaluation_dimensions
                        not in project_rating_standard_dict7[project_name]
                    ):
                        project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ] = {}
                    if (
                        key_indicator
                        not in project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ]
                    ):
                        project_rating_standard_dict7[project_name][
                            evaluation_dimensions
                        ][key_indicator] = []
                    project_rating_standard_dict7[project_name][evaluation_dimensions][
                        key_indicator
                    ].append(rating_standard)
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_rating_standard_dict7.items():
            rating_standard_list = []
            for (
                evaluation_dimensions,
                rating_standard_key_indicator_dict,
            ) in evaluation_dimensions_dict.items():
                for (
                    key_indicator,
                    rating_standard,
                ) in rating_standard_key_indicator_dict.items():
                    rating_standard_list = [*rating_standard_list, *rating_standard]
            all_team_rating_standard_list["class7_project_list"].append(
                {
                    "project_name": project_name,
                    "rating_standard_list": rating_standard_list,
                }
            )
        return all_team_rating_standard_list

    # 处理合并后的打分数据(打平数据)
    @staticmethod
    def flat_all_team_rating_standard_list(
        all_team_rating_standard_list, project_category_basic_info_dict
    ):
        # 不处理的数据
        no_calc_key_indicator = ["供应商", "备注"]
        # 分类一
        project_category1_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class1_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class1_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class1_project_list"]
            else []
        )

        # 分类二
        project_category2_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class2_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class2_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class2_project_list"]
            else []
        )

        # 分类四
        project_category4_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class4_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class4_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class4_project_list"]
            else []
        )

        # 分类五
        project_category5_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class5_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class5_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class5_project_list"]
            else []
        )

        # 分类六
        project_category6_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class6_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class6_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class6_project_list"]
            else []
        )

        # 分类七
        project_category7_score_list = (
            [
                {
                    "project_name": project["project_name"],
                    "team_name": rating_data["team_name"],
                    "key_indicator": rating_data["key_indicator"],
                    "evaluation_dimensions": rating_data["evaluation_dimensions"],
                    "assessment_point": rating_data["assessment_point"],
                    "scoring_rubrics": rating_data["scoring_rubrics"],
                    "category_eq_class_id": rating_data["category_eq_class_id"],
                    "category_eq_class_name": rating_data["category_eq_class_name"],
                    "dimension_weight": rating_data.get("dimension_weight", 0),
                    "team_weight": rating_data.get("team_weight", 0),
                    "category_name": category,
                    "category_score": (
                        rating_data[category]
                        if category in rating_data and rating_data[category]
                        else 0
                    ),
                }
                for project in all_team_rating_standard_list["class7_project_list"]
                for rating_data in project["rating_standard_list"]
                for category in project_category_basic_info_dict[
                    project["project_name"]
                ]["standard_class_category_name_en"]["standard_class7_category"]
                if rating_data["key_indicator"] not in no_calc_key_indicator
            ]
            if all_team_rating_standard_list["class7_project_list"]
            else []
        )

        flat_all_team_rating_standard_list = [
            *project_category1_score_list,
            *project_category2_score_list,
            *project_category4_score_list,
            *project_category5_score_list,
            *project_category6_score_list,
            *project_category7_score_list,
        ]
        return flat_all_team_rating_standard_list

    # 取出特殊处理数据
    @staticmethod
    def get_special_data(
        all_team_rating_standard_list, project_category_basic_info_dict
    ):
        special_data = {
            "category_supplier": {},
            "yellow_line_project_category": {
                # "南京江宁B2-1":{
                #     "ahu": 1
                # }
            },
            "red_line_project_category": {},
            "red_yellow_line_remark": {},
        }
        for project in all_team_rating_standard_list["class1_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class1_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class1_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class1_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class1_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])

        for project in all_team_rating_standard_list["class2_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class2_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class2_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class2_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class2_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])

        for project in all_team_rating_standard_list["class4_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class4_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class4_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class4_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class4_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])

        for project in all_team_rating_standard_list["class5_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class5_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class5_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class5_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class5_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])

        for project in all_team_rating_standard_list["class6_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class6_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class6_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class6_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class6_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])

        for project in all_team_rating_standard_list["class7_project_list"]:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            standard_class_category_name_en = project_category_basic_info[
                "standard_class_category_name_en"
            ]
            for rating in project["rating_standard_list"]:
                if rating["key_indicator"] == "供应商":
                    for category in standard_class_category_name_en[
                        "standard_class7_category"
                    ]:
                        if project_name not in special_data["category_supplier"]:
                            special_data["category_supplier"][project_name] = {}
                        if (
                            category
                            not in special_data["category_supplier"][project_name]
                        ):
                            special_data["category_supplier"][project_name][
                                category
                            ] = ""
                        special_data["category_supplier"][project_name][category] = (
                            rating[category]
                        )
                if rating["key_indicator"] == "备注":
                    for category in standard_class_category_name_en[
                        "standard_class7_category"
                    ]:
                        if project_name not in special_data["red_yellow_line_remark"]:
                            special_data["red_yellow_line_remark"][project_name] = {}
                        if (
                            category
                            not in special_data["red_yellow_line_remark"][project_name]
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = ""
                        if (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            == ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] = rating[category]
                        elif (
                            rating.get(category)
                            and special_data["red_yellow_line_remark"][project_name][
                                category
                            ]
                            != ""
                        ):
                            special_data["red_yellow_line_remark"][project_name][
                                category
                            ] += ("\n" + rating[category])
                if rating["key_indicator"] == "黄线":
                    for category in standard_class_category_name_en[
                        "standard_class7_category"
                    ]:
                        if (
                            project_name
                            not in special_data["yellow_line_project_category"]
                        ):
                            special_data["yellow_line_project_category"][
                                project_name
                            ] = {}
                        if (
                            category
                            not in special_data["yellow_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["yellow_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["yellow_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
                if rating["key_indicator"] == "红线":
                    for category in standard_class_category_name_en[
                        "standard_class7_category"
                    ]:
                        if (
                            project_name
                            not in special_data["red_line_project_category"]
                        ):
                            special_data["red_line_project_category"][project_name] = {}
                        if (
                            category
                            not in special_data["red_line_project_category"][
                                project_name
                            ]
                        ):
                            special_data["red_line_project_category"][project_name][
                                category
                            ] = 0
                        special_data["red_line_project_category"][project_name][
                            category
                        ] += float(rating[category])
        return special_data


# 项目评分数据汇总
class ProjectRatingHandle(object):
    # 获取项目品类分数(项目 品类 分数 评级)
    @staticmethod
    def get_project_category_score(
        all_flat_score_list, special_data, project_category_basic_info_dict
    ):
        # 计算项目-维度-品类总分
        project_dimensions_category_score_dict = {}
        for score in all_flat_score_list:
            project_name = score["project_name"]
            evaluation_dimensions = score["evaluation_dimensions"]
            dimension_weight = (
                float(score["dimension_weight"] * 10**2)
                if score["dimension_weight"]
                else 0
            )
            team_weight = (
                float(score["team_weight"] * 10**2) if score["team_weight"] else 0
            )
            category_name = score["category_name"]
            category_score = (
                float(score["category_score"]) if score["category_score"] else 0
            )
            if project_name not in project_dimensions_category_score_dict:
                project_dimensions_category_score_dict[project_name] = {
                    "商务": {},
                    "交付": {},
                    "质量": {},
                    "服务": {},
                }
            if (
                evaluation_dimensions
                in project_dimensions_category_score_dict[project_name]
            ):
                if (
                    category_name
                    not in project_dimensions_category_score_dict[project_name][
                        evaluation_dimensions
                    ]
                ):
                    project_dimensions_category_score_dict[project_name][
                        evaluation_dimensions
                    ][category_name] = 0
                project_dimensions_category_score_dict[project_name][
                    evaluation_dimensions
                ][category_name] += float(
                    category_score * dimension_weight * team_weight / (10**8)
                )

        # 计算项目-品类总分
        yellow_line_project_category = special_data["yellow_line_project_category"]
        red_line_project_category = special_data["red_line_project_category"]
        project_category_score_dict = {}
        for (
            project_name,
            evaluation_dimensions_dict,
        ) in project_dimensions_category_score_dict.items():
            category_score = {}
            category_cn_en_field_dict = project_category_basic_info_dict[project_name][
                "category_cn_en_field_dict"
            ].values()
            for category in category_cn_en_field_dict:
                if category not in category_score:
                    category_score[category] = 0
                # 红线 只算维度==商务、交付的分
                if red_line_project_category[project_name][category]:
                    for (
                        evaluation_dimensions_name,
                        category_score_dict,
                    ) in evaluation_dimensions_dict.items():
                        if evaluation_dimensions_name in ["商务", "交付"]:
                            category_score[category] += category_score_dict[category]
                else:
                    # 全部分 = 商务+交付+质量+服务
                    for (
                        evaluation_dimensions_name,
                        category_score_dict,
                    ) in evaluation_dimensions_dict.items():
                        category_score[category] += category_score_dict[category]
                    # 黄线 -15*黄线数量
                    if yellow_line_project_category[project_name][category]:
                        category_score[category] -= (
                            15 * yellow_line_project_category[project_name][category]
                        )
            project_category_score_dict[project_name] = category_score

        # 扣分项 黄线/红线 数据处理
        red_yellow_line_dict = {}
        for peoject_name, yellow_line_data in yellow_line_project_category.items():
            temp = {}
            for category, yellow_line_data in yellow_line_data.items():
                text = []
                if yellow_line_data:
                    text.append("黄线")
                if red_line_project_category[peoject_name][category]:
                    text.append("红线")
                temp[category] = (";").join(text)
            red_yellow_line_dict[peoject_name] = temp

        # 组装评分汇总表格数据
        project_category_score_list = []
        for project_name, category_score in project_category_score_dict.items():
            category_rating_list = []
            category_score_dict = {}
            category_level_dict = {}
            for category, score in category_score.items():
                category_score_dict[category] = round(score, 2)
                category_level_dict[category] = ProjectRatingHandle.get_category_level(
                    round(score, 2)
                )
            category_rating_list.append(
                {
                    "row_header": "供应商",
                    **special_data["category_supplier"][project_name],
                }
            )
            category_rating_list.append(
                {"row_header": "综合评分", **category_score_dict}
            )
            category_rating_list.append(
                {"row_header": "综合评级", **category_level_dict}
            )
            category_rating_list.append(
                {
                    "row_header": "扣分项",
                    **red_yellow_line_dict[project_name],
                }
            )
            category_rating_list.append(
                {
                    "row_header": "备注",
                    **special_data["red_yellow_line_remark"][project_name],
                }
            )
            project_category_score_list.append(
                {
                    "project_name": project_name,
                    "category_rating_list": category_rating_list,
                }
            )
        return project_category_score_list

    # 获取项目-维度-关键指标-品类分数(项目 维度 关键指标 品类 分数 评级)
    @staticmethod
    def get_project_dimensions_indicator_category_score(
        all_flat_score_list, project_category_score_list, special_data
    ):
        # 计算项目-维度-关键指标-品类总分
        project_dimensions_indicator_category_score_dict = {
            "class1": {},
            # "class2": {},
            "class4": {},
            "class5": {},
            "class6": {},
            "class7": {},
        }
        for score in all_flat_score_list:
            class_name = "class" + str(score["category_eq_class_id"])
            # 特殊处理：class1 和 class2数据合并：
            if class_name == "class2":
                class_name = "class1"
            project_name = score["project_name"]
            evaluation_dimensions = score["evaluation_dimensions"]
            dimension_weight = (
                float(score["dimension_weight"] * 10**2)
                if score["dimension_weight"]
                else 0
            )
            team_weight = (
                float(score["team_weight"] * 10**2) if score["team_weight"] else 0
            )
            category_name = score["category_name"]
            key_indicator = score["key_indicator"]
            category_score = (
                float(score["category_score"]) if score["category_score"] else 0
            )
            if (
                project_name
                not in project_dimensions_indicator_category_score_dict[class_name]
            ):
                project_dimensions_indicator_category_score_dict[class_name][
                    project_name
                ] = {
                    "商务": {},
                    "交付": {},
                    "质量": {},
                    "服务": {},
                }
            if (
                evaluation_dimensions
                in project_dimensions_indicator_category_score_dict[class_name][
                    project_name
                ]
            ):
                if (
                    key_indicator
                    not in project_dimensions_indicator_category_score_dict[class_name][
                        project_name
                    ][evaluation_dimensions]
                ):
                    project_dimensions_indicator_category_score_dict[class_name][
                        project_name
                    ][evaluation_dimensions][key_indicator] = {}
                if (
                    category_name
                    not in project_dimensions_indicator_category_score_dict[class_name][
                        project_name
                    ][evaluation_dimensions][key_indicator]
                ):
                    project_dimensions_indicator_category_score_dict[class_name][
                        project_name
                    ][evaluation_dimensions][key_indicator][category_name] = 0
                # 特殊处理（只有一个团队评分，占比取1）
                """
                    所有的商务
                    集采设备的交付(class1 class2)
                    项管、监理的交付、质量、服务(class5 class6)
                """
                if (
                    evaluation_dimensions == "商务"
                    or (class_name == "class1" and evaluation_dimensions == "交付")
                    or (class_name == "class2" and evaluation_dimensions == "交付")
                    or (class_name == "class5" and evaluation_dimensions == "交付")
                    or (class_name == "class5" and evaluation_dimensions == "质量")
                    or (class_name == "class5" and evaluation_dimensions == "服务")
                    or (class_name == "class6" and evaluation_dimensions == "交付")
                    or (class_name == "class6" and evaluation_dimensions == "质量")
                    or (class_name == "class6" and evaluation_dimensions == "服务")
                ):
                    team_weight = float(100 * 10**2)
                project_dimensions_indicator_category_score_dict[class_name][
                    project_name
                ][evaluation_dimensions][key_indicator][category_name] += float(
                    category_score * team_weight / (10**4)
                )
        # 从评分汇总获取数据（供应商、综合评分、综合评级、黄线/红线备注）
        project_category_score_dict = {}
        for project_category_score in project_category_score_list:
            project_name = project_category_score["project_name"]
            if project_name not in project_category_score_dict:
                project_category_score_dict[project_name] = {}
            for category_rating in project_category_score["category_rating_list"]:
                row_header = category_rating["row_header"]
                temp = category_rating
                temp.pop("row_header", None)
                if row_header == "供应商":
                    project_category_score_dict[project_name].setdefault(
                        "category_supplier", temp
                    )
                elif row_header == "综合评分":
                    project_category_score_dict[project_name].setdefault(
                        "category_score", temp
                    )
                elif row_header == "综合评级":
                    project_category_score_dict[project_name].setdefault(
                        "category_level", temp
                    )
                elif row_header == "备注":
                    project_category_score_dict[project_name].setdefault(
                        "red_yellow_line_remark", temp
                    )
        # 获取红黄线
        yellow_line_project_category = special_data["yellow_line_project_category"]
        red_line_project_category = special_data["red_line_project_category"]
        # 组装项目-维度-关键指标-品类分数(项目 关键指标 品类 分数)(供应商、分数、黄线/红线备注、评分、评级)
        merge_project_dimensions_indicator_score_dict = {
            "class1": [
                # {
                #     "project_name": "项目",
                #     "evaluation_dimensions": "维度",
                #     "key_indicator": "关键指标",
                #     "ahu": "分数"
                # }
            ],
            # "class2": [],
            "class4": [],
            "class5": [],
            "class6": [],
            "class7": [],
        }
        for (
            class_name,
            score_dict,
        ) in project_dimensions_indicator_category_score_dict.items():
            for (
                project_name,
                evaluation_dimensions_dict,
            ) in score_dict.items():
                merge_project_dimensions_indicator_score_dict[class_name].append(
                    {
                        "project_name": project_name,
                        "evaluation_dimensions": "供应商",
                        "key_indicator": "",
                        **project_category_score_dict[project_name][
                            "category_supplier"
                        ],
                    }
                )
                for (
                    dimensions_name,
                    dimensions_indicator_dict,
                ) in evaluation_dimensions_dict.items():
                    for (
                        indicator_name,
                        indicator_dict,
                    ) in dimensions_indicator_dict.items():
                        temp = {
                            key: round(indicator_dict[key], 0) for key in indicator_dict
                        }
                        merge_project_dimensions_indicator_score_dict[
                            class_name
                        ].append(
                            {
                                "project_name": project_name,
                                "evaluation_dimensions": dimensions_name,
                                "key_indicator": indicator_name,
                                **temp,
                            }
                        )
                yellow_line = yellow_line_project_category[project_name]
                red_line = red_line_project_category[project_name]
                yellow_red_line = {
                    key: val + red_line[key] for key, val in yellow_line.items()
                }
                yellow_red_line_temp = {
                    key: "是" if val else "否" for key, val in yellow_red_line.items()
                }
                merge_project_dimensions_indicator_score_dict[class_name].append(
                    {
                        "project_name": project_name,
                        "evaluation_dimensions": "扣分项",
                        "key_indicator": "是否触发黄线/红线",
                        **yellow_red_line_temp,
                    }
                )
                merge_project_dimensions_indicator_score_dict[class_name].append(
                    {
                        "project_name": project_name,
                        "evaluation_dimensions": "备注",
                        "key_indicator": "",
                        **project_category_score_dict[project_name][
                            "red_yellow_line_remark"
                        ],
                    }
                )
                merge_project_dimensions_indicator_score_dict[class_name].append(
                    {
                        "project_name": project_name,
                        "evaluation_dimensions": "汇总结果",
                        "key_indicator": "综合评分",
                        **project_category_score_dict[project_name]["category_score"],
                    }
                )
                merge_project_dimensions_indicator_score_dict[class_name].append(
                    {
                        "project_name": project_name,
                        "evaluation_dimensions": "汇总结果",
                        "key_indicator": "综合评级",
                        **project_category_score_dict[project_name]["category_level"],
                    }
                )
        return merge_project_dimensions_indicator_score_dict

    # 提出项目用作页面展示(项目  维度-关键指标-品类分数)
    @staticmethod
    def get_project_all_team_rating_dict(merge_all_team_rating_list):
        project_all_team_rating_dict = {
            "class1": [],
            # "class2": [],
            "class4": [],
            "class5": [],
            "class6": [],
            "class7": [],
        }
        temp_dict = {
            "class1": {
                # "项目名称": [
                #     {
                #             "project_name": "项目",
                #             "evaluation_dimensions": "维度",
                #             "key_indicator": "关键指标",
                #             "ahu": "分数"
                #     }
                # ]
            },
            # "class2": {},
            "class4": {},
            "class5": {},
            "class6": {},
            "class7": {},
        }
        for class_name, score_list in merge_all_team_rating_list.items():
            for data in score_list:
                project_name = data.get("project_name")
                if project_name not in temp_dict[class_name]:
                    temp_dict[class_name].setdefault(project_name, [])
                temp_dict[class_name][project_name].append(data)
        for class_name, class_dict in temp_dict.items():
            for project_name, rating_list in class_dict.items():
                project_all_team_rating_dict[class_name].append(
                    {"project_name": project_name, "rating_list": rating_list}
                )
        return project_all_team_rating_dict

    # 获取项目品类评分评级
    @staticmethod
    def get_category_level(score):
        """
        95-100	S
        80-94	A
        65-79	B
        0-64	C
        """
        if score >= 0 and score < 65:
            return "C"
        elif score >= 65 and score < 80:
            return "B"
        elif score >= 80 and score < 95:
            return "A"
        elif score >= 95 and score <= 100:
            return "S"

    # 评分汇总表格数据提取
    @staticmethod
    def get_project_category_info(
        project_category_score_list, project_category_basic_info_dict
    ):
        row_header_field_map = {
            "供应商": "category_supplier",
            "综合评分": "category_score",
            "综合评级": "category_level",
            "扣分项": "red_yellow_line",
            "备注": "red_yellow_line_remark",
        }
        project_category_info = {}
        # 数组重组
        for project in project_category_score_list:
            project_name = project["project_name"]
            project_category_basic_info = project_category_basic_info_dict[project_name]
            category_cn_en_field_dict = project_category_basic_info[
                "category_cn_en_field_dict"
            ].values()
            category_info = {
                key: {
                    "category_supplier": "",
                    "category_score": "",
                    "category_level": "",
                    "red_yellow_line": "",
                    "red_yellow_line_remark": "",
                }
                for key in category_cn_en_field_dict
            }
            if project_name not in project_category_info:
                project_category_info[project_name] = {**category_info}
            for rating in project["category_rating_list"]:
                field_key = row_header_field_map[rating["row_header"]]
                for category in category_cn_en_field_dict:
                    project_category_info[project_name][category][field_key] = (
                        rating.get(category)
                    )
        return project_category_info
