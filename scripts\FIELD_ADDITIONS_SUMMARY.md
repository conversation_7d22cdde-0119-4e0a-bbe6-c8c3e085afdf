# 设备数据合并脚本字段添加总结

## 修改概述
在成功运行的 `optimized_data_consolidation.py` 脚本基础上，添加了三个缺失的字段：
1. `equipment_sla` - 设备SLA字段
2. `completion_time` - 完成时间字段  
3. `po_create_time` - PO创建时间字段

## 具体修改内容

### 1. 修改 `fetch_additional_data` 方法
**位置**: 第198-233行
**修改**: 在查询中添加了 `equipment_sla` 字段
```sql
SELECT project_name, supplier, device_type, equipment_sla,
       expected_time_equipment, estimated_time_delivery, delivery_gap
FROM delivery_schedule_management_table 
```

### 2. 新增 `fetch_equipment_sla_data` 方法
**位置**: 第235-260行
**功能**: 从 `equipment_category_mapping_relationship` 表获取设备SLA数据
```python
def fetch_equipment_sla_data(self, device_names):
    """第5步：获取设备SLA数据"""
    # 根据设备名称查询SLA信息
```

### 3. 新增 `fetch_completion_time_data` 方法
**位置**: 第262-287行
**功能**: 从 `construct_plan_data` 表获取完成时间数据
```python
def fetch_completion_time_data(self, project_names):
    """第6步：获取完成时间数据"""
    # 根据项目名称查询完成时间
```

### 4. 新增 `fetch_po_create_time_data` 方法
**位置**: 第289-314行
**功能**: 从 `payment_info` 表获取PO创建时间数据
```python
def fetch_po_create_time_data(self, project_names):
    """第7步：获取PO创建时间数据"""
    # 根据项目名称查询PO创建时间
```

### 5. 修改主数据合并流程
**位置**: 第363-382行
**修改**: 添加了新字段的数据获取步骤
```python
# 第5步：获取设备SLA数据
device_names = [equipment_data[tid]['device_name']
               for tid in equipment_data if equipment_data[tid].get('device_name')]
sla_data = self.fetch_equipment_sla_data(device_names)

# 第6步：获取完成时间数据
completion_data = self.fetch_completion_time_data(project_names)

# 第7步：获取PO创建时间数据
po_time_data = self.fetch_po_create_time_data(project_names)
```

### 6. 修改数据插入逻辑
**位置**: 第394-432行
**修改**: 
- 添加了新字段的数据处理逻辑
- 更新了INSERT SQL语句，包含所有新字段
- 更新了参数传递，确保所有字段都有对应的值

```python
# 获取设备名称和SLA信息
device_name = equipment_info.get('device_name', '')
equipment_sla = sla_data.get(device_name, additional_info.get('equipment_sla', ''))

# 获取完成时间和PO创建时间
completion_time = completion_data.get(project_name)
po_create_time = po_time_data.get(project_name)

# 更新的INSERT语句包含所有15个字段
INSERT IGNORE INTO consolidated_equipment_data (
    state, TicketId, Title, SchemeNameCn, IsForceEnd,
    project_name, device_name, supplier, device_type, equipment_sla,
    expected_time_equipment, estimated_time_delivery, delivery_gap,
    completion_time, po_create_time
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
```

## 数据源表说明

### 新增的数据源表：
1. **equipment_category_mapping_relationship** - 设备SLA数据
   - 字段: `device_name`, `equipment_sla`
   
2. **construct_plan_data** - 完成时间数据
   - 字段: `project_name`, `completion_time`
   
3. **payment_info** - PO创建时间数据
   - 字段: `project_name`, `po_create_time`

### 已有数据源表（已修改）：
1. **delivery_schedule_management_table** - 添加了 `equipment_sla` 字段查询

## 运行说明

1. 确保数据库中存在所有相关的源表
2. 确保 `consolidated_equipment_data` 表结构包含所有新字段（表结构在脚本中的 `create_tables` 方法已更新）
3. 运行脚本：
   ```bash
   python scripts/optimized_data_consolidation.py
   ```

## 预期结果

运行成功后，`consolidated_equipment_data` 表将包含：
- 原有的12个字段
- 新增的3个字段：`equipment_sla`, `completion_time`, `po_create_time`
- 总共15个业务字段 + 1个 `last_updated` 时间戳字段

这样就解决了页面显示中缺失SLA、完成时间和PO创建时间的问题。
