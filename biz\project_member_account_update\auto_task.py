from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.project_member_account_delete.account_delete_api import (
    AccountDeleteApi,
)


# 账号修改
class AccountUpdateAutoTask(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def to_tansfer_todo(self, del_member_list_confirm):
        """
        转派待办（更新获取同角色处理人，没有才转给上级）
        """
        data = {
            "Datalist": [
                # {
                #     "RawProcessUser": "[string]",  # 原处理人
                #     "TransProcessUser": "[string]",  # 转移处理人
                # },
            ]
        }
        datalist = []
        for item in del_member_list_confirm:
            if (
                item.get("is_truly_del_account")
                and item.get("del_account_status") == "删除成功"
            ):
                if item.get("account") and item.get("new_todo_user"):
                    # 更新下new_todo_user
                    project_name = self.ctx.variables.get("project_name")
                    role = item.get("role")
                    item["new_todo_user"] = AccountDeleteApi.get_same_role_new_todo_user(project_name, role)
                    datalist.append(
                        {
                            "RawProcessUser": item.get("account"),  # 原处理人
                            "TransProcessUser": item.get("new_todo_user"),  # 转移处理人
                        }
                    )
        if datalist:
            # 去重
            unique_users = set()
            temp = []
            for item in datalist:
                raw_process_user = item["RawProcessUser"]
                if raw_process_user not in unique_users:
                    unique_users.add(raw_process_user)
                    temp.append(item)
            data["Datalist"] = temp
            self.workflow_detail.add_kv_table("1.入参", {"message": data})
            # 调接口转派待办
            res = gnetops.request(
                action="TaskTodo", method="BatchUpdateProcessUseV1", data=data
            )
            self.workflow_detail.add_kv_table("2.出参", {"message": res})
        else:
            self.workflow_detail.add_kv_table(
                "1.待办转派信息为空", {"message": del_member_list_confirm}
            )
        variables = {
            "del_member_list_confirm": del_member_list_confirm,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
