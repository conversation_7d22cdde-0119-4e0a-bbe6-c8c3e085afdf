#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的性能和功能
验证分表查询和数据更新显示功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from biz.construction_big_disk.campus_resources import ProjectsUnderConstructionInPark
from scripts.optimized_data_consolidation import OptimizedDataConsolidator
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_consolidation_performance():
    """测试数据合并性能"""
    logger.info("=== 测试1: 数据合并性能 ===")
    
    start_time = time.time()
    
    try:
        consolidator = OptimizedDataConsolidator()
        
        # 创建表
        if not consolidator.create_tables():
            logger.error("✗ 创建表失败")
            return False
        
        # 执行数据合并
        if not consolidator.consolidate_and_insert():
            logger.error("✗ 数据合并失败")
            return False
        
        elapsed_time = time.time() - start_time
        logger.info(f"✓ 数据合并成功，耗时: {elapsed_time:.2f} 秒")
        
        # 性能评估
        if elapsed_time < 30:
            logger.info("🚀 性能优秀：合并时间 < 30秒")
        elif elapsed_time < 60:
            logger.info("⚡ 性能良好：合并时间 < 60秒")
        else:
            logger.warning("⚠️ 性能需要优化：合并时间 > 60秒")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据合并测试失败: {e}")
        return False

def test_query_performance():
    """测试查询性能"""
    logger.info("=== 测试2: 查询性能 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        
        # 测试无条件查询性能
        start_time = time.time()
        result = park_resources.tabular_data_query()
        elapsed_time = time.time() - start_time
        
        logger.info(f"✓ 无条件查询耗时: {elapsed_time:.3f} 秒")
        
        # 检查返回结果结构
        if isinstance(result, dict) and 'update_info' in result:
            logger.info("✓ 返回结构包含更新信息")
            update_info = result['update_info']
            logger.info(f"  最后更新: {update_info.get('last_update')}")
            logger.info(f"  记录数量: {update_info.get('record_count')}")
            logger.info(f"  状态: {update_info.get('status')}")
        else:
            logger.warning("⚠️ 返回结构不包含更新信息（可能是旧版本）")
        
        # 性能评估
        if elapsed_time < 1.0:
            logger.info("🚀 查询性能优秀：< 1秒")
        elif elapsed_time < 3.0:
            logger.info("⚡ 查询性能良好：< 3秒")
        else:
            logger.warning("⚠️ 查询性能需要优化：> 3秒")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 查询性能测试失败: {e}")
        return False

def test_filtered_queries():
    """测试筛选查询功能"""
    logger.info("=== 测试3: 筛选查询功能 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        
        # 测试项目名称筛选
        start_time = time.time()
        result1 = park_resources.tabular_data_query(project_name="清远清新")
        elapsed_time1 = time.time() - start_time
        logger.info(f"✓ 项目名称筛选耗时: {elapsed_time1:.3f} 秒")
        
        # 测试状态筛选
        start_time = time.time()
        result2 = park_resources.tabular_data_query(state="已交付")
        elapsed_time2 = time.time() - start_time
        logger.info(f"✓ 状态筛选耗时: {elapsed_time2:.3f} 秒")
        
        # 测试组合筛选
        start_time = time.time()
        result3 = park_resources.tabular_data_query(project_name="清远清新", state="已交付")
        elapsed_time3 = time.time() - start_time
        logger.info(f"✓ 组合筛选耗时: {elapsed_time3:.3f} 秒")
        
        # 验证结果结构
        for i, result in enumerate([result1, result2, result3], 1):
            if isinstance(result, dict) and 'data' in result:
                data = result['data']
                if isinstance(data, list):
                    logger.info(f"  查询{i}返回记录数: {len(data)}")
                elif isinstance(data, dict) and '13yue' in data:
                    logger.info(f"  查询{i}返回设备类型数: {len(data['13yue'])}")
            else:
                logger.warning(f"  查询{i}返回格式异常")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 筛选查询测试失败: {e}")
        return False

def test_update_info_functionality():
    """测试更新信息功能"""
    logger.info("=== 测试4: 更新信息功能 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        
        # 测试获取更新信息
        update_info = park_resources.get_data_update_info()
        
        logger.info("✓ 更新信息获取成功")
        logger.info(f"  最后更新时间: {update_info.get('last_update')}")
        logger.info(f"  记录数量: {update_info.get('record_count')}")
        logger.info(f"  状态: {update_info.get('status')}")
        logger.info(f"  消息: {update_info.get('message')}")
        logger.info(f"  更新间隔: {update_info.get('minutes_ago')} 分钟前")
        
        # 验证必要字段
        required_fields = ['last_update', 'record_count', 'status', 'message', 'minutes_ago']
        missing_fields = [field for field in required_fields if field not in update_info]
        
        if missing_fields:
            logger.warning(f"⚠️ 缺少字段: {missing_fields}")
        else:
            logger.info("✓ 更新信息字段完整")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 更新信息功能测试失败: {e}")
        return False

def test_data_freshness():
    """测试数据新鲜度"""
    logger.info("=== 测试5: 数据新鲜度 ===")
    
    try:
        park_resources = ProjectsUnderConstructionInPark()
        update_info = park_resources.get_data_update_info()
        
        minutes_ago = update_info.get('minutes_ago', 0)
        
        if minutes_ago <= 60:
            logger.info(f"✓ 数据新鲜：{minutes_ago} 分钟前更新")
        elif minutes_ago <= 180:
            logger.warning(f"⚠️ 数据较旧：{minutes_ago} 分钟前更新，建议同步")
        else:
            logger.error(f"❌ 数据过期：{minutes_ago} 分钟前更新，需要立即同步")
        
        # 检查记录数量
        record_count = update_info.get('record_count', 0)
        if record_count > 0:
            logger.info(f"✓ 数据量正常：{record_count} 条记录")
        else:
            logger.error("❌ 数据量异常：无记录")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据新鲜度测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始优化性能测试")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("数据合并性能", test_data_consolidation_performance()))
    test_results.append(("查询性能", test_query_performance()))
    test_results.append(("筛选查询功能", test_filtered_queries()))
    test_results.append(("更新信息功能", test_update_info_functionality()))
    test_results.append(("数据新鲜度", test_data_freshness()))
    
    # 统计测试结果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    logger.info(f"\n=== 测试结果汇总 ===")
    for test_name, result in test_results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！优化成功！")
        logger.info("\n📋 后续建议：")
        logger.info("1. 设置定时任务定期执行数据同步")
        logger.info("2. 在页面中集成数据更新信息显示")
        logger.info("3. 监控查询性能，必要时添加索引")
        return True
    else:
        logger.error(f"❌ {total - passed} 个测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
