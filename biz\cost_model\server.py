# -*- coding: utf-8 -*-
# 【DcopsEasyCode】
# ！！轻松优化import导入：Mac系统请使用 ⌃ + ⌥ + O 组合键；Windows系统请使用 CTRL + ALT + O 组合键
# ！！轻松调整代码规范：Mac系统请使用 option + command + L 组合键；Windows系统请使用 CTRL + ALT + L 组合键
# TODO *描述模块功能

# TODO *所有业务逻辑编写完成后，轻松优化import导入：Mac系统请使用 ⌃ + ⌥ + O 组合键；Windows系统请使用 CTRL + ALT + O 组合键
from iBroker.logone import logger
from iBroker.lib.sdk import idcdb
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowContinue
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class Server:
    """_summary_
    服务器
    """
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()
        self.variables = self.ctx.variables

    def get_power(self):
        """_summary_
        获取功率数据
        """
        logger.info("get_power!!!")
        data = idcdb.get_list(
            table="rack",
            fields=["rack_id", "rack_code", "rack_availablePow"],
            search_condition={"city_name": ["北京"]})
        logger.info(data)

    def my_dcops_go(self):

        # TODO 【DcopsEasyCode】为当前任务节点添加kv形式详情，在任务节点的【查看详情】中可以看到该信息
        self.workflow_detail.add_kv_table(name="标题-title",
                                          kv_table={"展示key": "展示value"})

        # TODO *【DcopsEasyCode】如果未在逻辑执行过程中更新变量，建议通过WorkflowContinue的方式流转流程，并更新流程变量
        WorkflowContinue(task_id=self.ctx.task_id,
                         variables_map={
                             "var_a": "var_a_value",
                             "var_b": "var_b_value"
                         }).call()


if __name__ == "__main__":
    s = Server()
    s.get_power()
