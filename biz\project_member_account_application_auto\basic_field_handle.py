from iBroker.lib import mysql
from iBroker.lib import config


# 模板操作
class BasicFieldHandle(object):

    # 获取项目成员基础表数据
    @staticmethod
    def get_basic_info(field_name):
        query_sql = f"select field_value from project_member_basic_info where field_name = '{field_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        list = []
        if query_result and query_result[0]["field_value"]:
            list = [
                field_value
                for field_value in query_result[0]["field_value"].split(";")
                if field_value != ""
            ]
        return list

    # 将list数据处理成前端下拉选项数据
    @staticmethod
    def get_options_info(list):
        option_list = []
        if list:
            option_list = [
                {"label": field_value, "value": field_value}
                for field_value in list
                if field_value != ""
            ]
        return option_list

    # 判断项目是否是合建项目（合建项目没有项目编号）
    @staticmethod
    def is_joint_construction_project(project_name):
        query_sql = f"select item_number from risk_early_warning_data where project_name = '{project_name}'"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        if query_result and query_result[0]["item_number"]:
            return False
        else:
            return True

    # 获取所属服务商
    def get_supplier():
        query_sql = f"select * from category_supplier_info"
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        list = []
        if query_result:
            for item in query_result:
                if item.get("supplier"):
                    supplier_list = item.get("supplier").split(";")
                    temp = [
                        supplier
                        for supplier in supplier_list
                        if supplier and supplier not in list
                    ]
                    list += temp
        list += config.get_config_map("project_member_account_supplier")
        return list
