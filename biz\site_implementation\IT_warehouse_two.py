import uuid
import numpy as np
import pandas as pd
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql
from biz.construction_process.cos_lib import COSLib


class ItTwo(AjaxTodoBase):
    """
        IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(ItTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_itemized_plan_is_upload(self, block2_sub_plan: list):
        try:
            datas, bools = block2_sub_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        return datas, bools

    def verify_whether_the_upload_is_correct(self, block2_sub_plan: list):
        url = block2_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel方仓2解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        for _, row in df.iterrows():
            serial_number = row['序号']
            task_name = row['工作内容']
            if serial_number == 3.2 and task_name == 'IT Block2/电池间':
                return {"code": 0, "msg": "分项计划上传正确"}, False
        return {"code": -1, "msg": "分项计划上传错误，请重新上传"}, True

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_sub_plan = process_data.get('block2_sub_plan', None)

        datas, bools = self.check_itemized_plan_is_upload(block2_sub_plan)
        sub_plan_data, sub_plan_bool = self.verify_whether_the_upload_is_correct(block2_sub_plan)
        if bools:
            return datas
        if sub_plan_bool:
            return sub_plan_data

        update_data = {}
        if block2_sub_plan and len(block2_sub_plan) > 0 and 'response' in block2_sub_plan[0]:
            update_data = {
                'block2_sub_plan': block2_sub_plan[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("it_block2", update_data, conditions)
        tb_db.commit()
        variables = {
            'block2_sub_plan': block2_sub_plan
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ElectricalEngineeringTwo(AjaxTodoBase):
    """
        电气工程_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(ElectricalEngineeringTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_qdgc_list = process_data.get("block2_qdgc_list", {})
        work_list = block2_qdgc_list.get("work_list", [])
        IT2_qdgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_qdgc_dict = {
                'work_content': '电气工程(强电工程)',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'IT2_qdgc_dict': IT2_qdgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update_data = {
                'dqgc_actual_start_time': actual_start_time,
                'dqgc_actual_finish_time': actual_finish_time,
                'dqgc_actual_construction_time': actual_construction_time,
                'dqgc_schedule_deviation': schedule_deviation,
                'dqgc_remarks': remark,
            }
            conditions = {
                'dcops_ticket_id': ticket_id,
                'task_name': '电气工程（强电工程）',
                'dqgc_work_content': work_content
            }
            tb_db.update("it_block2", update_data, conditions)
        complete_report = block2_qdgc_list.get('complete_report', None)
        complete_photo = block2_qdgc_list.get('complete_photo', None)
        complete_report_review_report = block2_qdgc_list.get('complete_report_review_report', None)

        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3

        insert_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            insert_data = {
                'dqgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'dqgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'dqgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
                'task_id': ticket_id
            }
        tb_db.insert("it2_data", insert_data)
        tb_db.commit()
        variables = {
            'block2_qdgc_list': block2_qdgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_qdgc_dict': IT2_qdgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WeakCurrentEngineeringTwo(AjaxTodoBase):
    """
        弱电工程_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(WeakCurrentEngineeringTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_rdgc_list = process_data.get("block2_rdgc_list", {})
        work_list = block2_rdgc_list.get("work_list", [])
        IT2_rdgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_rdgc_dict = {
                'work_content': '弱电系统(弱电工程)',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'IT2_rdgc_dict': IT2_rdgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'rdxt_actual_start_time': actual_start_time,
                'rdxt_actual_finish_time': actual_finish_time,
                'rdxt_actual_construction_time': actual_construction_time,
                'rdxt_schedule_deviation': schedule_deviation,
                'rdxt_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '弱电系统（弱电工程）',
                'rdxt_work_content': work_content
            }
            tb_db.update("it_block2", update2_data, conditions2)
        complete_report = block2_rdgc_list.get('complete_report', None)
        complete_photo = block2_rdgc_list.get('complete_photo', None)
        complete_report_review_report = block2_rdgc_list.get('complete_report_review_report', None)

        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3

        update1_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update1_data = {
                'rdgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'rdgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'rdgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions1 = {
            'task_id': ticket_id,
        }
        tb_db.update("it2_data", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_rdgc_list': block2_rdgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_rdgc_dict': IT2_rdgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SquareWarehouseInstallationTwo(AjaxTodoBase):
    """
        方仓及框架安装_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(SquareWarehouseInstallationTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_fckj_list = process_data.get("block2_fckj_list", {})
        work_list = block2_fckj_list.get("work_list", [])
        IT2_fckj_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_fckj_dict = {
                'work_content': '方仓及框架安装（结构工程）',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'IT2_fckj_dict': IT2_fckj_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)

            update2_data = {
                'kjaz_actual_start_time': actual_start_time,
                'kjaz_actual_finish_time': actual_finish_time,
                'kjaz_actual_construction_time': actual_construction_time,
                'kjaz_schedule_deviation': schedule_deviation,
                'kjaz_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '方仓及框架安装（结构工程）',
                'kjaz_work_content': work_content
            }
            tb_db.update("it_block2", update2_data, conditions2)
        complete_report = block2_fckj_list.get('complete_report', None)
        complete_photo = block2_fckj_list.get('complete_photo', None)
        complete_report_review_report = block2_fckj_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update1_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update1_data = {
                'kjaz_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'kjaz_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'kjaz_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions1 = {
            'task_id': ticket_id,
        }
        tb_db.update("it2_data", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_fckj_list': block2_fckj_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_fckj_dict': IT2_fckj_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RenovationProjectTwo(AjaxTodoBase):
    """
        装修工程_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(RenovationProjectTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_zxgc_list = process_data.get("block2_zxgc_list", {})
        work_list = block2_zxgc_list.get("work_list", [])
        IT2_zxgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_zxgc_dict = {
                'work_content': '装修工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'IT2_zxgc_dict': IT2_zxgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)

            update2_data = {
                'zxgc_actual_start_time': actual_start_time,
                'zxgc_actual_finish_time': actual_finish_time,
                'zxgc_actual_construction_time': actual_construction_time,
                'zxgc_schedule_deviation': schedule_deviation,
                'zxgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '装修工程',
                'zxgc_work_content': work_content
            }
            tb_db.update("it_block2", update2_data, conditions2)
        complete_report = block2_zxgc_list.get('complete_report', None)
        complete_photo = block2_zxgc_list.get('complete_photo', None)
        complete_report_review_report = block2_zxgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update1_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update1_data = {
                'zx_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'zx_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'zx_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions1 = {
            'task_id': ticket_id,
        }
        tb_db.update("it2_data", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_zxgc_list': block2_zxgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_zxgc_dict': IT2_zxgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class HeatingProjectTwo(AjaxTodoBase):
    """
        暖通工程_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(HeatingProjectTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_ntgc_list = process_data.get("block2_ntgc_list", {})
        work_list = block2_ntgc_list.get("work_list", [])
        IT2_ntgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_ntgc_dict = {
                'work_content': '暖通工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'IT2_ntgc_dict': IT2_ntgc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)

            update2_data = {
                'ntgc_actual_start_time': actual_start_time,
                'ntgc_actual_finish_time': actual_finish_time,
                'ntgc_actual_construction_time': actual_construction_time,
                'ntgc_schedule_deviation': schedule_deviation,
                'ntgc_remarks': remark,
            }

            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '暖通工程',
                'ntgc_work_content': work_content
            }
            tb_db.update("it_block2", update2_data, conditions2)
        complete_report = block2_ntgc_list.get('complete_report', None)
        complete_photo = block2_ntgc_list.get('complete_photo', None)
        complete_report_review_report = block2_ntgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update1_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update1_data = {
                'ntgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'ntgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'ntgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions1 = {
            'task_id': ticket_id,
        }
        tb_db.update("it2_data", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_ntgc_list': block2_ntgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_ntgc_dict': IT2_ntgc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class FirefightingProjectTwo(AjaxTodoBase):
    """
        消防工程_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(FirefightingProjectTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        block2_xfgc_list = process_data.get("block2_xfgc_list", {})
        work_list = block2_xfgc_list.get("work_list", [])
        IT2_xfgc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            IT2_xfgc_dict = {
                'work_content': '消防工程',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            IT2_qdgc_dict = self.ctx.variables.get('IT2_qdgc_dict')
            IT2_rdgc_dict = self.ctx.variables.get('IT2_rdgc_dict')
            IT2_fckj_dict = self.ctx.variables.get('IT2_fckj_dict')
            IT2_zxgc_dict = self.ctx.variables.get('IT2_zxgc_dict')
            IT2_ntgc_dict = self.ctx.variables.get('IT2_ntgc_dict')

            IT2_skip_list = []
            if IT2_qdgc_dict:
                IT2_skip_list.append(IT2_qdgc_dict)
            if IT2_rdgc_dict:
                IT2_skip_list.append(IT2_rdgc_dict)
            if IT2_fckj_dict:
                IT2_skip_list.append(IT2_fckj_dict)
            if IT2_zxgc_dict:
                IT2_skip_list.append(IT2_zxgc_dict)
            if IT2_ntgc_dict:
                IT2_skip_list.append(IT2_ntgc_dict)
            if IT2_xfgc_dict:
                IT2_skip_list.append(IT2_xfgc_dict)

            variables = {
                'IT2_xfgc_dict': IT2_xfgc_dict,
                'IT2_skip_list': IT2_skip_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            IT2_qdgc_dict = self.ctx.variables.get('IT2_qdgc_dict')
            IT2_rdgc_dict = self.ctx.variables.get('IT2_rdgc_dict')
            IT2_fckj_dict = self.ctx.variables.get('IT2_fckj_dict')
            IT2_zxgc_dict = self.ctx.variables.get('IT2_zxgc_dict')
            IT2_ntgc_dict = self.ctx.variables.get('IT2_ntgc_dict')

            IT2_skip_list = []
            if IT2_qdgc_dict:
                IT2_skip_list.append(IT2_qdgc_dict)
            if IT2_rdgc_dict:
                IT2_skip_list.append(IT2_rdgc_dict)
            if IT2_fckj_dict:
                IT2_skip_list.append(IT2_fckj_dict)
            if IT2_zxgc_dict:
                IT2_skip_list.append(IT2_zxgc_dict)
            if IT2_ntgc_dict:
                IT2_skip_list.append(IT2_ntgc_dict)
            if IT2_xfgc_dict:
                IT2_skip_list.append(IT2_xfgc_dict)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'xfgc_actual_start_time': actual_start_time,
                'xfgc_actual_finish_time': actual_finish_time,
                'xfgc_actual_construction_time': actual_construction_time,
                'xfgc_schedule_deviation': schedule_deviation,
                'xfgc_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '消防工程',
                'xfgc_work_content': work_content
            }
            tb_db.update("it_block2", update2_data, conditions2)
        complete_report = block2_xfgc_list.get('complete_report', None)
        complete_photo = block2_xfgc_list.get('complete_photo', None)
        complete_report_review_report = block2_xfgc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update1_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update1_data = {
                'xfgc_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'xfgc_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'xfgc_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions1 = {
            'task_id': ticket_id,
        }
        tb_db.update("it2_data", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_xfgc_list': block2_xfgc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'IT2_xfgc_dict': IT2_xfgc_dict,
            'IT2_skip_list': IT2_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class IT2PMApproval(AjaxTodoBase):
    """
        IT方仓2-PM审批
    """

    def __init__(self):
        super(IT2PMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        it2_PM_approval = process_data.get('it2_pm_approval')
        it2_PM_remark = process_data.get('it2_PM_remark')

        if it2_PM_approval == '驳回':
            it2_skip_list = []
            if not it2_PM_remark:
                return {'code': -1, 'msg': '驳回时，未填写备注'}

        variables = {
            'it2_PM_approval': it2_PM_approval,
            'it2_PM_remark': it2_PM_remark,
            'it2_skip_list': it2_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DevicePowerDebuggingTwo(AjaxTodoBase):
    """
        设备上电调试_IT方仓2
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(DevicePowerDebuggingTwo, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_power_debug_is_upload(self, power_debug: list):
        try:
            datas, bools = power_debug[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        block2_sdts_list = process_data.get('block2_sdts_list')
        ticket_id = self.ctx.variables.get("ticket_id")
        work_list = block2_sdts_list.get("work_list", [])
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'sdts_actual_start_time': actual_start_time,
                'sdts_actual_finish_time': actual_finish_time,
                'sdts_actual_construction_time': actual_construction_time,
                'sdts_schedule_deviation': schedule_deviation,
                'sdts_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '设备上电调试',
                'sdts_work_content': work_content,
            }
            tb_db.update("it_block2", update2_data, conditions2)
        power_debug = block2_sdts_list.get('power_debug', None)
        datas, bools = self.check_power_debug_is_upload(power_debug)
        if bools:
            return datas
        update1_data = {}
        if power_debug and len(power_debug) > 0 and 'response' in power_debug[0]:
            update1_data = {
                'block2_power_debug': power_debug[0]["response"]["FileList"][0]["url"],
            }
        conditions1 = {
            'dcops_ticket_id': ticket_id,
        }
        tb_db.update("it_block2", update1_data, conditions1)
        tb_db.commit()
        variables = {
            'block2_sdts_list': block2_sdts_list,
            'block2_power_debug': power_debug
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
