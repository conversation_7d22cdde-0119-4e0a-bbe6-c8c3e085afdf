-- 创建合并设备数据表（简化版本，不使用存储过程）
CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
    state VARCHAR(50) DEFAULT NULL,
    TicketId VARCHAR(100) NOT NULL,
    Title VARCHAR(255) DEFAULT NULL,
    SchemeNameCn VARCHAR(255) DEFAULT NULL,
    IsForceEnd VARCHAR(255) DEFAULT '0',
    project_name VARCHAR(255) DEFAULT NULL,
    device_name VARCHAR(255) DEFAULT NULL,
    supplier VARCHAR(255) DEFAULT NULL,
    device_type VARCHAR(100) DEFAULT NULL,
    equipment_sla VARCHAR(50) DEFAULT NULL,
    expected_time_equipment DATETIME DEFAULT NULL,
    estimated_time_delivery DATETIME DEFAULT NULL,
    delivery_gap INT DEFAULT NULL,
    completion_time DATETIME DEFAULT NULL,
    po_create_time DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (TicketId),
    INDEX idx_project_name (project_name),
    INDEX idx_state (state),
    INDEX idx_device_type (device_type)
);

-- 简单的数据插入示例（可选执行）
-- 注意：实际数据插入请使用 Python 脚本 scripts/consolidate_equipment_data.py

-- 清空表（如果需要重新插入）
-- DELETE FROM consolidated_equipment_data;

-- 注意：不再使用复杂的JOIN查询插入数据
-- 数据插入将通过Python脚本分表查询完成，性能更好

-- 创建数据更新记录表，用于显示最后更新时间
CREATE TABLE IF NOT EXISTS consolidated_data_update_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    record_count INT DEFAULT 0,
    status VARCHAR(50) DEFAULT 'success',
    message TEXT,
    INDEX idx_table_name (table_name),
    INDEX idx_update_time (update_time)
);

-- 插入初始更新记录
INSERT INTO consolidated_data_update_log (table_name, record_count, message)
VALUES ('consolidated_equipment_data', 0, '表已创建，等待数据同步')
ON DUPLICATE KEY UPDATE
    update_time = CURRENT_TIMESTAMP,
    message = '表结构已更新';

-- 查看插入结果
SELECT COUNT(*) as total_records FROM consolidated_equipment_data;
SELECT * FROM consolidated_equipment_data LIMIT 5;
