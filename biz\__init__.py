import inspect
from biz.discovery import update_discovery, ServiceDiscovery
from biz.TboxEventFlow.tbox_event_flow import TboxTodo
from biz.TboxEventFlow.tbox_alarm_event import GetTboxAlarm
from iBroker.sdk.todo.todo_base import NetOpsTodoCreateProxy
from iBroker.sdk.todo.exception_todo import AjaxExceptionTodo

# TB变更业务
from biz.tbbg.requirement import RequirementPush
from biz.tbbg.requirement import RequirementAudit
from biz.tbbg.requirement import RequirementReceive
from biz.tbbg.requirement import AuditCreate
from biz.tbbg.requirement import PlanAdjustment
from biz.tbbg.requirement import PlanConfirm
from biz.tbbg.change_plan import SendChangePlan
from biz.tbbg.change_plan import AffirmReceiveChangePlan
from biz.tbbg.change_plan import ReceiveDetails
from biz.tbbg.change_plan import ReceiveGmInfo
from biz.tbbg.budget import LockBudget
from biz.tbbg.budget import BudgetAudit
from biz.tbbg.audit import RequirementChiefAudit
from biz.tbbg.audit import IdcChiefAudit
from biz.tbbg.budget import DoaCheck
from biz.tbbg.audit import GmAudit
from biz.tbbg.audit import DoaGateWay
from biz.tbbg.audit import DoaAudit
from biz.tbbg.audit import AuditReject
from biz.tbbg.audit import FinancialManagerAudit
from biz.tbbg.budget import ReceiveBudgetAndSupplier
from biz.tbbg.after_audit import OutpufAuditResult
from biz.tbbg.after_audit import SendChangeRequire
from biz.tbbg.after_audit import SendChangeOrder
from biz.tbbg.after_audit import ReceivePoInfoAndSendMsg
from biz.tbbg.after_audit import ChangeCompleteAffirm
from biz.tbbg.after_audit import ReceiveSettleComplete
from biz.tbbg.audit import GmAuditInfo
from biz.tbbg.cron import TbCron
from biz.tbbg.cos_lib import COSLib

# TB外电数据获取
from biz.data_analysis.indicator_card import DatabaseConnector

# 项目评估接口
from biz.construction_process.construction_evaluation import Construction

# 建设大盘
from biz.construction_big_disk.campus_resources import ProjectsUnderConstructionInPark

# 供应商资源修改流程
from biz.construction_big_disk.campus_resources import ModifySupplierResources
from biz.construction_big_disk.campus_resources import ProjectInformationQuery

# TB建设业务流程
from biz.construction_process.task import PMAllocation
from biz.construction_process.task import PMChange
from biz.construction_process.task import PMChangeSendEmail
from biz.construction_process.task import ProjectTeamFormation
from biz.construction_process.task import WeeklyNewspaperAndDailyPaperDataStorage
from biz.construction_process.task import ProjectStartUp
from biz.construction_process.task import ProjectStartupConnectToIDC
from biz.construction_process.task import StorageParameter
from biz.construction_process.task import RiskWarningDisplay
from biz.construction_process.task import RiskEscalationOptions
from biz.construction_process.task import PORiskEscalationOptions
from biz.construction_process.task import RiskEscalationEmailEditing
from biz.construction_process.task import PORiskEscalationEmailEditing
from biz.construction_process.task import RiskEscalationApproval
from biz.construction_process.task import RiskEscalationEmailPreview
from biz.construction_process.task import SendRiskEscalationEmail
from biz.construction_process.task import WaitingForProjectStart
from biz.construction_process.task import RiskWarningDate
from biz.construction_process.task import AnalyzeTheMasterControlPlanBak
from biz.construction_process.task import QueryPOInformationDuringProjectStartup
from biz.construction_process.task import RiskWarningNotificationToBusinessHandlers
from biz.construction_process.task import WaitEarlyWarningContentConfirmation
from biz.construction_process.task import RiskWarningHandlerNotAccepted

# 项目建设对接IDC
from biz.construction_process.task import (
    ProjectStartupConnectToIDC,
    IDCTicketInfoFill,
    IDCTicketInfoFillPMReview,
)

# TB招采流程
from biz.examination_approval.recruit_recruit import EditApprovalPage
from biz.examination_approval.recruit_recruit import SendToEddiewu
from biz.examination_approval.recruit_recruit import SendToDirector
from biz.examination_approval.recruit_recruit import SendToGM
from biz.examination_approval.recruit_recruit import EmailInformationEditor
from biz.examination_approval.recruit_recruit import EmailPushing

# TB现场实施阶段流程——电力走廊
from biz.site_implementation.power_corridor import Corridor
from biz.site_implementation.power_corridor import SquareBinInstallation
from biz.site_implementation.power_corridor import ExhaustSystem
from biz.site_implementation.power_corridor import OutsidePowerProject
from biz.site_implementation.power_corridor import TestInspection
from biz.site_implementation.power_corridor import PowerTransmissionReport
from biz.site_implementation.power_corridor import DevicePowerDebuggingPowerCorridor
from biz.site_implementation.power_corridor import Precondition
from biz.site_implementation.power_corridor import UploadData
from biz.site_implementation.power_corridor import CosLib
from biz.site_implementation.power_corridor import UpdateStatus
from biz.site_implementation.power_corridor import DlzlPMApproval

# TB现场实施阶段流程——IT方仓1
from biz.site_implementation.IT_warehouse_one import ItOne
from biz.site_implementation.IT_warehouse_one import ElectricalEngineeringOne
from biz.site_implementation.IT_warehouse_one import WeakCurrentEngineeringOne
from biz.site_implementation.IT_warehouse_one import SquareWarehouseInstallationOne
from biz.site_implementation.IT_warehouse_one import RenovationProjectOne
from biz.site_implementation.IT_warehouse_one import HeatingProjectOne
from biz.site_implementation.IT_warehouse_one import FirefightingProjectOne
from biz.site_implementation.IT_warehouse_one import DevicePowerDebuggingOne
from biz.site_implementation.IT_warehouse_one import IT1PMApproval

# TB现场实施阶段流程——IT方仓2
from biz.site_implementation.IT_warehouse_two import ItTwo
from biz.site_implementation.IT_warehouse_two import ElectricalEngineeringTwo
from biz.site_implementation.IT_warehouse_two import WeakCurrentEngineeringTwo
from biz.site_implementation.IT_warehouse_two import SquareWarehouseInstallationTwo
from biz.site_implementation.IT_warehouse_two import RenovationProjectTwo
from biz.site_implementation.IT_warehouse_two import HeatingProjectTwo
from biz.site_implementation.IT_warehouse_two import FirefightingProjectTwo
from biz.site_implementation.IT_warehouse_two import DevicePowerDebuggingTwo
from biz.site_implementation.IT_warehouse_two import IT2PMApproval

# TB现场实施阶段流程——IT方仓3
from biz.site_implementation.IT_warehouse_three import ItThree
from biz.site_implementation.IT_warehouse_three import ElectricalEngineeringThree
from biz.site_implementation.IT_warehouse_three import WeakCurrentEngineeringThree
from biz.site_implementation.IT_warehouse_three import SquareWarehouseInstallationThree
from biz.site_implementation.IT_warehouse_three import RenovationProjectThree
from biz.site_implementation.IT_warehouse_three import HeatingProjectThree
from biz.site_implementation.IT_warehouse_three import FirefightingProjectThree
from biz.site_implementation.IT_warehouse_three import DevicePowerDebuggingThree
from biz.site_implementation.IT_warehouse_three import IT3PMApproval

# TB现场实施阶段流程——IT方仓4
from biz.site_implementation.IT_warehouse_four import ItFour
from biz.site_implementation.IT_warehouse_four import ElectricalEngineeringFour
from biz.site_implementation.IT_warehouse_four import WeakCurrentEngineeringFour
from biz.site_implementation.IT_warehouse_four import SquareWarehouseInstallationFour
from biz.site_implementation.IT_warehouse_four import RenovationProjectFour
from biz.site_implementation.IT_warehouse_four import HeatingProjectFour
from biz.site_implementation.IT_warehouse_four import FirefightingProjectFour
from biz.site_implementation.IT_warehouse_four import DevicePowerDebuggingFour
from biz.site_implementation.IT_warehouse_four import IT4PMApproval

# TB现场实施阶段流程——柴发和水的处理
from biz.site_implementation.treatment_area import FirewoodHairWaterTreatmentArea
from biz.site_implementation.treatment_area import InstallationChaiFaWarehouse
from biz.site_implementation.treatment_area import WaterTreatment
from biz.site_implementation.treatment_area import OilCircuitSystemChaiFa
from biz.site_implementation.treatment_area import DevicePowerDebuggingChaiFa
from biz.site_implementation.treatment_area import CfsclPMApproval

# TB现场实施阶段流程——AHU区域
from biz.site_implementation.AHU_region import AHURegion
from biz.site_implementation.AHU_region import ElectricalEngineeringAHU
from biz.site_implementation.AHU_region import WeakCurrentEngineeringAHU
from biz.site_implementation.AHU_region import RenovationProjectAHU
from biz.site_implementation.AHU_region import HeatingProjectAHU
from biz.site_implementation.AHU_region import DevicePowerDebuggingAHU
from biz.site_implementation.AHU_region import AHUPMApproval

# TB现场实施阶段流程——办公区域
from biz.site_implementation.administrative_area import AdministrativeArea
from biz.site_implementation.administrative_area import ElectricalEngineeringWork
from biz.site_implementation.administrative_area import WeakCurrentEngineeringWork
from biz.site_implementation.administrative_area import RenovationProjectWork
from biz.site_implementation.administrative_area import HeatingProjectWork
from biz.site_implementation.administrative_area import DevicePowerDebuggingWork
from biz.site_implementation.administrative_area import FirefightingProjectWork
from biz.site_implementation.administrative_area import BgqyPMApproval

# 周报建设
from biz.weekly_management.week_paper import ModifyDataTableUsingWeeklyReportData
from biz.weekly_management.week_paper import WeekPaperCommit
from biz.weekly_management.week_paper import WeekRepository
from biz.weekly_management.week_paper import WeekPaper
from biz.weekly_management.week_paper import ModifiedByWeeklyReport

# 新周报流程
from biz.weekly_management.week_paper import GetWeeklyReport
from biz.weekly_management.week_paper import SubmitWeeklyReport
from biz.weekly_management.week_paper import ProcessWeeklyReportData
from biz.weekly_management.week_paper import WeeklyReportInformationConfirmation
from biz.weekly_management.week_paper import WeeklyReportInformationStorage
from biz.weekly_management.weekly_report_api import WeeklyReportApi
from biz.weekly_management.week_paper import WeekPaperAutoTask

# 日报建设
from biz.schedule_management.daily_paper import DailyPaperCommit
from biz.schedule_management.daily_paper import DailyPaper
from biz.schedule_management.daily_paper import DailyDocumentDisplay

# 质量评估-标准
from biz.quality_evaluation.standard import StandardUpload
from biz.quality_evaluation.standard import StandardAnalysis

# 质量评估-项目信息
from biz.quality_evaluation.project_info import ProjectInfoConfirm

# 质量评估-评分
from biz.quality_evaluation.team_rating import BusinessRating
from biz.quality_evaluation.team_rating import SupplyChainRating
from biz.quality_evaluation.team_rating import PlanningRating
from biz.quality_evaluation.team_rating import ProductRating
from biz.quality_evaluation.team_rating import ProjectRating
from biz.quality_evaluation.team_rating import OptimizeRating
from biz.quality_evaluation.team_rating import WeakElectricityRating
from biz.quality_evaluation.team_rating import RatingSummary

# 质量评估-审批
from biz.quality_evaluation.rating_approval import QEBusinessRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QESupplyChainRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QEPlanningRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QEProductRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QEProjectRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QEOptimizeRaterLeaderApproval
from biz.quality_evaluation.rating_approval import QEWeakElectricityLeaderApproval
from biz.quality_evaluation.rating_approval import QualityEvaluationLeaderApproval
from biz.quality_evaluation.rating_approval import QualityEvaluationDirectorApproval
from biz.quality_evaluation.rating_approval import QualityEvaluationGMApproval

# 质量评估-自动任务
from biz.quality_evaluation.auto_task import QualityEvaluationAutoTask
from biz.quality_evaluation.into_db import QualityEvaluationIntoDb

# 质量评估-邮件
from biz.quality_evaluation.email_action import QualityEvaluationEmailSend

# 项目建设-施工准备-架构输入
from biz.construction_preparation_architecture_input.architecture_input import (
    ConstructionPreparationArchitectureInput,
)
from biz.construction_preparation_architecture_input.architecture_input import (
    ArchitectureInputIntoDB,
)

# 施工准备-报建流程-自动任务
from biz.construction_application_process.auto_task import (
    ConstructionApplicationProcessAutoTask,
)
from biz.construction_application_process.into_db import (
    ConstructionApplicationProcessIntoDb,
)

# 施工准备-报建流程-项目报建
from biz.construction_application_process.project_construction_application_task import (
    ProjectConstructionApplicationUpload,
)
from biz.construction_application_process.project_construction_application_task import (
    ProjecEstablishment,
)
from biz.construction_application_process.project_construction_application_task import (
    DesignContractSigning,
)
from biz.construction_application_process.project_construction_application_task import (
    DrawingsSubmittedReview,  # 无PM确认、状态回传星环节点；附件只有"送审图纸"、"审图报告"；
    NewDrawingsSubmittedReview,  # 有PM确认、状态回传星环节点。"审图报告"改为"审图合格证"、新增"结构力学书"附件上传。
    DrawingsSubmittedReviewPMCheck,
)
from biz.construction_application_process.project_construction_application_task import (
    SupervisionContractAwarding,
)
from biz.construction_application_process.project_construction_application_task import (
    EngineeringContractAwarding,
)
from biz.construction_application_process.project_construction_application_task import (
    SupervisionContractSigning,
)
from biz.construction_application_process.project_construction_application_task import (
    EngineeringContractSigning,
)
from biz.construction_application_process.project_construction_application_task import (
    MigrantWorkersSecurityDepositSupervisionContractSigning,
)
from biz.construction_application_process.project_construction_application_task import (
    WorkRelatedAccidentInsurance,
)
from biz.construction_application_process.project_construction_application_task import (
    ConstructionPermitIssuance,
)
from biz.construction_application_process.project_construction_application_task import (
    CompletionRecord,
)
from biz.construction_application_process.project_construction_application_task import (
    FireProtectionRecord,
)
from biz.construction_application_process.project_construction_application_task import (
    FireEngineeringThirdPartyInspectionReport,
)

# 合建项目-项目报建
from biz.construction_application_process.project_construction_application_task import (
    JointSupervisionContractAwarding,
    JointProjecEstablishment,
    JointConstructionPermitIssuance,
    JointFireProtectionRecord,
    JointCompletionRecord,
)

# 合建项目-市电专项
from biz.construction_application_process.mains_power_plan_task import (
    JointExternalPowerReport,
    JointPowerSupplyContract,
    JointDetectionBeforePowerTransmission,
    JointPowerTransmission,
)

# 施工准备-报建流程-市电计划
from biz.construction_application_process.mains_power_plan_task import (
    MainsPowerPlanUpload,
)
from biz.construction_application_process.mains_power_plan_task import (
    ExternalPowerReport,
)
from biz.construction_application_process.mains_power_plan_task import ExternalPowerPlan
from biz.construction_application_process.mains_power_plan_task import ReliabilityFee
from biz.construction_application_process.mains_power_plan_task import (
    PowerSupplyContract,
)
from biz.construction_application_process.mains_power_plan_task import DispatchProtocol
from biz.construction_application_process.mains_power_plan_task import (
    DetectionBeforePowerTransmission,
)
from biz.construction_application_process.mains_power_plan_task import PowerTransmission

# 第三方巡检报告
from biz.third_party_report.third_party_report_upload import FileInformation
from biz.third_party_report.third_party_report_upload import (
    RectifyingCorrectingDocuments,
)
from biz.third_party_report.third_party_report_upload import TraversingDatabase

# 设备到货管理流程
from biz.equipment_arrival_management.equipment_arrival import ProjectDataQuery
from biz.equipment_arrival_management.equipment_arrival import UploadShippingInformation
from biz.equipment_arrival_management.equipment_arrival import (
    UploadReceivingInformation,
    DeliveryDataAuditSupervision,
    DeliveryDataAuditItemTube,
    ReceiptDataAuditSupervision,
    ReceiptDataAuditItemTube,
)
from biz.equipment_arrival_management.equipment_arrival import AutomaticEquipmentArrival

# TB建设专项方案审批流程
from biz.special_construction_plan.examination_approval import DlzlApprovalFlow
from biz.special_construction_plan.examination_approval import ItOneApprovalFlow
from biz.special_construction_plan.examination_approval import ItTwoApprovalFlow
from biz.special_construction_plan.examination_approval import ItThreeApprovalFlow
from biz.special_construction_plan.examination_approval import ItFourApprovalFlow
from biz.special_construction_plan.examination_approval import CfApprovalFlow
from biz.special_construction_plan.examination_approval import AHUApprovalFlow
from biz.special_construction_plan.examination_approval import BgApprovalFlow
from biz.special_construction_plan.examination_approval import DlzlSupervisionApproval
from biz.special_construction_plan.examination_approval import ItOneSupervisionApproval
from biz.special_construction_plan.examination_approval import ItTwoSupervisionApproval
from biz.special_construction_plan.examination_approval import (
    ItThreeSupervisionApproval,
)
from biz.special_construction_plan.examination_approval import ItFourSupervisionApproval
from biz.special_construction_plan.examination_approval import CfSupervisionApproval
from biz.special_construction_plan.examination_approval import AHUSupervisionApproval
from biz.special_construction_plan.examination_approval import BgSupervisionApproval
from biz.special_construction_plan.examination_approval import SpecialWorkPlanStorage
from biz.special_construction_plan.examination_approval import (
    SpecialExaminationApproval,
)

# 施工进场-计划上传、项目开办
from biz.construction_before_eruipment.construction_before import ConstructionPlan
from biz.construction_before_eruipment.construction_before import FileParsing
from biz.construction_before_eruipment.construction_before import ProjectStartPlan
from biz.construction_before_eruipment.construction_before import ProjectStart
from biz.construction_before_eruipment.construction_before import GetProjectData
from biz.construction_before_eruipment.construction_before import (
    UploadWeeklyReportInformation,
)
from biz.construction_before_eruipment.construction_before import (
    UpdateConstructionProcessStatus,
)
from biz.construction_before_eruipment.construction_before import (
    StartPMAuditConfirmation,
    ConstructionPMAuditConfirmation,
    ConstructionSupervisionAuditConfirmation,
    ConstructionStatusFeedbackBefore,
)

# 合建项目开办及设备进场施工
from biz.construction_before_eruipment.construction_before import (
    JointProjectStart,
    JointStartPMAuditConfirmation,
    JointConstructionBeforeEquipmentArrives,
    JointConstructionSupervisionAuditConfirmation,
    JointConstructionProjectManagerAuditConfirmation,
    JointConstructionPMAuditConfirmation,
    AcquisitionOfJointConstructionProjectPlan,
)

# 施工进场-地面及放线
from biz.construction_before_eruipment.base_ground import GroundApprovedPlan
from biz.construction_before_eruipment.base_ground import GroundReport

# 施工进场-AHU基础
from biz.construction_before_eruipment.base_AHU import AHUApprovedPlan
from biz.construction_before_eruipment.base_AHU import AHUReport

# 施工进场-水处理基础
from biz.construction_before_eruipment.base_water import WaterApprovedPlan
from biz.construction_before_eruipment.base_water import WaterTreatmentReport

# 施工进场-假负载方仓基础
from biz.construction_before_eruipment.base_fangcang import FangcangApprovedPlan
from biz.construction_before_eruipment.base_fangcang import FangcangReport

# 施工进场-其他基础
from biz.construction_before_eruipment.base_other import OtherApprovedPlan
from biz.construction_before_eruipment.base_other import OtherReport

# 问题整改
from biz.problem_rectification.modify_question import ModifyProblemUpload
from biz.problem_rectification.modify_question import ModifyProblemSolve
from biz.problem_rectification.modify_question import DataCallback
from biz.problem_rectification.modify_question import StoreProblemRectificationData
from biz.problem_rectification.modify_question import WaitingReviewResults
from biz.problem_rectification.modify_question import GetRectifierInformation
from biz.problem_rectification.modify_question import ChooseRectifier
from biz.problem_rectification.modify_question import ProjectManagementReview
from biz.problem_rectification.modify_question import SupervisionAndReview

# 进度管理接口
from biz.project_interface.progress_management import ProgressManagement  # 进度管理
from biz.project_interface.project_weekly_report import (
    ProjectWeeklyReportQuery,
)  # 项目周报
from biz.project_interface.basic_project_information import (
    BasicInformationQuery,
    ProjectDeliveryAcceptanceItem,
)  # 基本信息
from biz.project_interface.project_human_resource_management import (
    ProjectManpowerInterface,
    DeleteDuplicateData,
)  # 项目人力接口

# 安全问题管理
from biz.project_interface.safety_management import AdvanceFieldExitManagement
from biz.project_interface.safety_management import SafetyTraining
from biz.project_interface.safety_management import SecurityManagementInterface

# 招采流程后半段评分
from biz.tb_service.review_bid import SelectReviewerTodo
from biz.tb_service.review_bid import BidLoopCreateTicket
from biz.tb_service.review_bid import BidLoopCreateTicketBak
from biz.tb_service.review_bid import UpdateDeviationInformation
from biz.tb_service.review_bid import BidMarkTodo
from biz.tb_service.review_bid import BidCheckTodo
from biz.tb_service.review_bid import BidLoopCreateTicket2
from biz.tb_service.review_bid import BidFirstApproveTodo
from biz.tb_service.review_bid import BidSecApproveTodo
from biz.tb_service.review_bid import BidThirdApproveTodo
from biz.tb_service.review_bid import BidEmailNoticeTodo
from biz.tb_service.review_bid import BidEmailNoticeShow

# 评标-文件推送 and 澄清答疑文件上传 and 废标
from biz.tb_service.review_bid import FileParsingSending
from biz.tb_service.review_bid import AnswerQuestionsFileUpload
from biz.tb_service.review_bid import ClarificationAnswerQuestionsFileUpload
from biz.tb_service.review_bid import ClarificationAnswerQuestionsFileCheck
from biz.tb_service.review_bid import WhetherBidNotarize

# 生成文件上传至cos，获取url
from biz.tb_service.review_bid import FilesGeneration
from biz.tb_service.review_bid import FilesGenerationBak

# 招采流程后半段评分汇总备份
from biz.tb_service.review_bid_bak import FilesGenerationOne
from biz.tb_service.review_bid_bak import SelectReviewerTodoOne
from biz.tb_service.review_bid_bak import BidLoopCreateTicketOne
from biz.tb_service.review_bid_bak import BidMarkTodoOne
from biz.tb_service.review_bid_bak import WhetherBidNotarizeOne
from biz.tb_service.review_bid_bak import FilesGenerationBakOne
from biz.tb_service.review_bid_bak import FileParsingSendingBak
from biz.tb_service.review_bid_bak import ClarificationAnswerQuestionsFileUploadBak

# 招采流程后半段评分以通过为评审内容
from biz.tb_service.review_bid_pass import BidMarkTodoPass
from biz.tb_service.review_bid_pass import FilesGenerationPass
from biz.tb_service.review_bid_pass import BidEmailNoticeTodoPass
from biz.tb_service.review_bid_pass import BidEmailNoticeShowPass
from biz.tb_service.review_bid_pass import SelectReviewerTodoPass
from biz.tb_service.review_bid_pass import WhetherBidNotarizePass

# 测试平台对接
from biz.interaction.interaction_test import TestDocking
from biz.interaction.problem_rectification import ProblemSubmit
from biz.interaction.problem_rectification import ProblemApproval
from biz.interaction.problem_rectification import PlanSubmit
from biz.interaction.problem_rectification import ProblemRectification

# 问题提出及整改
from biz.problem_raising_and_rectification.propose_and_modify import (
    SelectQuestionCategory,
    ProblemUpload,
    ProblemRectification,
    ProjectManagementReview,
    SupervisionAndAudit,
    BuildingBatchInfoData,
    ProblemSpecify,
)

# 质量管理接口
from biz.project_interface.quality_management_interface import (
    ArrivalManagementInterface,
)
from biz.project_interface.quality_management_interface import (
    ConstructionManagementInterface,
)
from biz.project_interface.quality_management_interface import (
    ProblemManagementInterface,
)

# 邮件测试
from biz.email_test.email_action import EditEmail
from biz.email_test.email_action import SendEmail

# 项目建设测试阶段
from biz.project_testing_phase.testing_phase import PrepareMaterialsUpload
from biz.project_testing_phase.testing_phase import CreatingTestFlow
from biz.project_testing_phase.testing_phase import VerifyTestResults
from biz.project_testing_phase.testing_phase import TestDataReturn
from biz.project_testing_phase.testing_phase import GetListFilesFromTestbench
from biz.project_testing_phase.testing_phase import VerifyTestSelectModuleName
from biz.project_testing_phase.testing_phase import DataBackToTestbench
from biz.project_testing_phase.testing_phase import TestFileReview

# 建设平台bom清单上传流程
from biz.construction_platform.upload_bom_list import UploadFiles
from biz.construction_platform.upload_bom_list import BomInventoryDetails

# 设备生产跟踪流程
from biz.equipment_production_tracking.equipment_production_management import (
    EquipmentProductionPlan,
    EquipmentProductionPreparation,
    EquipmentPrepareMaterials,
    EquipmentProduction,
    EquipmentFactoryInspection,
    EquipmentChangingState,
    EquipmentDeliverySupervision,
    EquipmentDeliveryItemTube,
    EquipmentDeliveryItemTubeNew,
    EquipmentReceiptSupervision,
    EquipmentReceiptItemTube,
    EquipmentReceiptPM,
    EquipmentDataTransfer,
    EquipmentAutomatic,
    EquipmentUploadReceiving,
    EquipmentUploadShipping,
    EquipmentUploadReceivingNew,
    EquipmentUploadShippingNew,
    GetEquipmentProductionDeliveryData,
)

# 规划设计平台对接
from biz.planning_and_design_docking.BOM_docking import BOMDockingBasicData
from biz.planning_and_design_docking.BOM_docking import BOMDockingSpecificationRetrieval
from biz.planning_and_design_docking.BOM_docking import BOMDockingSpecificationInterface
from biz.planning_and_design_docking.BOM_docking import BOMDockingJudgeSpecification
from biz.planning_and_design_docking.BOM_docking import BOMDockingEquipmentListInterface
from biz.planning_and_design_docking.BOM_docking import BOMDockingJudgeEquipmentList
from biz.planning_and_design_docking.BOM_docking import DisplayBOMData
from biz.planning_and_design_docking.design_changes import OnSiteProblemFeedback
from biz.planning_and_design_docking.design_changes import ChangesDesignDrawings
from biz.planning_and_design_docking.design_changes import ProblemFeedbackReview
from biz.planning_and_design_docking.design_changes import PushMessage

# 邮件发送
from biz.project_interface.email_push import EmailPush

# 规划设计数据接口
from biz.project_interface.planning_and_design import PlanningAndDesignInterface

# 文件url获取流程
from biz.url_extract import ExcelUrl

# 项目管理:问题与风险
from biz.project_interface.issues_and_risks_interface import ConstructionIssueManagement
from biz.project_interface.issues_and_risks_interface import ConstructionRisksManagement

# 测试问题整改单总览展示
from biz.project_interface.test_question_display import TestQuestionDisplay

# 项目人力流程
from biz.project_interface.project_human_resource_management import (
    ProjectHumanResourceManagement,
    ManpowerDataStorage,
    ProjectHumanResourceAutomaticallyPlaceOrders,
)

# 项目成员科技账号申请
from biz.project_member_account_application.auto_task import AccountApplicationAutoTask
from biz.project_member_account_application.into_db import AccountApplicationIntoDb
from biz.project_member_account_application.account_application import (
    AccountApplication,
    AccountApplicationApproval,
    AccountInfoInput,
    AccountInfoConfirm,
)
from biz.project_member_account_application.account_application_api import (
    AccountApplicationApi,
)

# 项目成员科技账号申请-自动
from biz.project_member_account_application_auto.auto_task import (
    AutoAccountApplicationAutoTask,
)
from biz.project_member_account_application_auto.into_db import (
    AutoAccountApplicationIntoDb,
)
from biz.project_member_account_application_auto.account_application import (
    AutoAccountApplication,
    AutoAccountApplicationJoint,
    AutoAccountApplicationApproval,
    AutoAccountInfoConfirm,
)
from biz.project_member_account_application_auto.account_application_api import (
    AutoAccountApplicationApi,
)

# 已有账号人员申请入项目
from biz.apply_to_enter_project.apply_to_enter_project_api import ApplyToEnterProjectApi
from biz.apply_to_enter_project.apply_to_enter_project_task import ApplyToEnterProject
from biz.apply_to_enter_project.into_db import ApplyToEnterProjectIntoDb

# 项目成员科技账号删除
from biz.project_member_account_delete.account_delete import (
    AccountDelete,
    AccountDeleteApproval,
    AccountDeleteInfoConfirm,
)
from biz.project_member_account_delete.auto_task import AccountDeleteAutoTask
from biz.project_member_account_delete.account_delete_api import (
    AccountDeleteApi,
)

# 项目成员科技账号修改
from biz.project_member_account_update.account_update import (
    AccountUpdate,
    AccountUpdateJoint,
    AccountUpdateApproval,
    AccountUpdateInfoConfirm,
)
from biz.project_member_account_update.auto_task import AccountUpdateAutoTask

# 组织架构
from biz.wework_dept.auto_task import WeworkDeptAutoTask
from biz.wework_dept.wework_dept_api import WeworkDeptApi
from biz.wework_dept.wework_dept_apply_task import (
    WeworkDeptApply,
    WeworkDeptActivate,
    WeworkDeptConfirm,
    WeworkDeptApplication,
)

# 建设接维平台对接
from biz.construction_maintenance_platform_docking.construction_maintenance_process import (
    ConstructionMaintenanceInitiate,
    ConstructionMaintenanceProcess,
    ConstructionMaintenanceCycle,
    ConstructionMaintenanceTrainingUpload,
    ConstructionMaintenanceMaterialUpload,
    ConstructionMaintenanceInitial,
    ConstructionMaintenanceFinalDocumentUpload,
    ConstructionMaintenanceFinal,
    ConstructionMaintenancePMMain,
    ConstructionMaintenanceItemMain,
    ConstructionMaintenanceSupervisionMain,
    ConstructionMaintenanceAcceptance,
    ConstructionMaintenanceArchitectureDesignReview,
    ConstructionMaintenanceArchitectureUpload,
    DesignPMFileRetransmission,
)

# 项目交付上传复盘文件
from biz.construction_maintenance_platform_docking.construction_maintenance_process import (
    PMProjectDeliveryReviewFileAudit,
    JCSProjectDeliveryReviewFileUpload,
    XGProjectDeliveryReviewFileUpload,
    JLProjectDeliveryReviewFileUpload,
    PMProjectDeliveryReviewFileAuditNew,
)

# 接维——现场查验：问题整改
from biz.construction_maintenance_platform_docking.construction_maintenance_inspection import (
    ConstructionMaintenanceInspection,
    MaintenanceChooseRectifier,
    MaintenanceModifyProblemSolve,
    MaintenanceModifyProblemUpload,
    MaintenanceWaitingResults,
)

# 接维资料上传：集成商、pm、监理
from biz.construction_maintenance_platform_docking.construction_maintenance_upload_info import (
    ConstructionIntegratorStoragePersonCheck,
    ConstructionIntegratorFileUpload,
    ConstructionIntegratorFileCheck,
    ConstructionIntegratorFileStore,
    ConstructionMaintenanceFileStorage,
    ConstructionMaintenanceSupervision,
    ConstructionMaintenanceProjectManagement,
    ConstructionMaintenanceDataIntegrators,
    ConstructionMaintenanceDataSupervision,
    ConstructionMaintenancePM,
    RoleCheck,
    Info,
    RoleCreate,
    ContractInformationByProject,
)

# 深化设计对接
from biz.construction_process.detail_design_phase import (
    InformationAcquisitionDuringDesignPhase,
)
from biz.construction_process.detail_design_phase import DetailDesignDifferentFileView

# 设备标牌信息流程
from biz.decive_signage_information.generate_signage_information import (
    DeviceAutomaticTasks,
    DeviceArchitectureConfirmation,
    DevicePMConfirmation,
    DeviceStickingSigns,
)

# 机柜标牌流程
from biz.decive_signage_information.cabinet_sign import CabinetQRCodeSign
from biz.decive_signage_information.cabinet_sign import CabinetQRCodeSignPM
from biz.decive_signage_information.my_coslib import Tool

# 建设平台待办转派
from biz.construction_todo_transfer.todo_transfer import TodoTransferConfig
from biz.construction_todo_transfer.auto_task import TodoTransferAutoTask

# 项目成员接口
from biz.project_interface.project_member import ProjectMemberTable
from biz.weekly_report_analysis import parse_and_store_data

# 建设问题
from biz.project_interface.construction_issues import Construction_issues

# 项目工作台
from biz.construction_big_disk.campus_resources import ConstructionTable

# 风险预警邮件发送
from biz.construction_big_disk.campus_resources import MailInformationEditing
from biz.construction_big_disk.campus_resources import LeaderExamineApprove
from biz.construction_big_disk.campus_resources import Mailing
from biz.construction_big_disk.campus_resources import DataProcessing

# 建设总览
from biz.construction_big_disk.campus_resources import ConstructionOverview
from biz.construction_big_disk.campus_resources import ConstructionReserve

# 货期管理
from biz.construction_big_disk.campus_resources import DeliveryScheduleManagement

# PO信息表
from biz.construction_big_disk.campus_resources import PoInformationForm
from biz.construction_big_disk.campus_resources import ExportPoInformationTable

# 建设问题外网建单权限
from biz.project_interface.permission_control import PermissionControl

# 解析总控计划
from biz.project_interface.parsing_interface import InterfaceMasterControlPlan

# 初验反馈流程
from biz.construction_maintenance_platform_docking.initial_test_feedback import (
    InitialTestFeedbackUploadData,
    InitialTestFeedbackCallingInterface,
)

# 反馈:人员进场
from biz.construction_maintenance_platform_docking.construction_status_feedback import (
    ConstructionStatusFeedback,
    SendEmailNotification,
    ConstructionStatusFeedbackUpload,
)

# 进度可视化
from biz.project_interface.project_progress_visualization import ProgressVisualization

# 接维问题整改单总览展示
from biz.project_interface.maintenance_question_display import (
    MaintenanceQuestionDisplay,
)

# 催办功能
from biz.project_interface.construction_work_list import ConstructionWorkOrder

# 合建方招采
from biz.joint_construction_party.recruit import ApprovalPage
from biz.joint_construction_party.recruit import ApprovalPageSummary
from biz.joint_construction_party.recruit import AnswerQuestionsFileUploaded
from biz.joint_construction_party.recruit import ClarifyAnswerQuestionsDocument
from biz.joint_construction_party.recruit import BusinessConfirmation
from biz.joint_construction_party.recruit import DesignatedBidEvaluation
from biz.joint_construction_party.recruit import Designation
from biz.joint_construction_party.recruit import BidderConfirms
from biz.joint_construction_party.recruit import Notice
from biz.joint_construction_party.recruit import UploadTagFile
from biz.joint_construction_party.recruit import RecruitJointing
from biz.joint_construction_party.recruit import CircularOrderCreation
from biz.joint_construction_party.recruit import CircularOrderCreation2
from biz.joint_construction_party.recruit import RecruitSendEmail
from biz.joint_construction_party.recruit import BusinessProcessorConfirmation
from biz.joint_construction_party.recruit import SendToEddiewuTenderEvaluation
from biz.joint_construction_party.recruit import SendToCenterTenderEvaluation
from biz.joint_construction_party.recruit import SendToEddiewuStandard
from biz.joint_construction_party.recruit import SendToGMTenderEvaluation
from biz.joint_construction_party.recruit import SendToGMStandard
from biz.joint_construction_party.recruit import SendToDirectorStandard
from biz.joint_construction_party.recruit import RecruitStandard
from biz.joint_construction_party.recruit import BiddingProcessPlan
from biz.joint_construction_party.recruit import BusinessBidReturnCompleted
from biz.joint_construction_party.recruit import DesignatedBidEvaluationTeamLeader
from biz.joint_construction_party.recruit import BasicInformationOfBidEvaluation
from biz.joint_construction_party.recruit import SendToBidEvaluationTeamMembers

# 合建方安装施工
from biz.special_construction_plan.joint_construction_installation import (
    AnalysisOfInstallationConstructionMasterControlPlan,
    AnalysisOfInstallationConstructionPeopleInfo,
    InstallationConstructionManagerReview,
    InstallationConstructionZBCommit,
)

# 文档管理接口 & 项目文档流程 & 合同管理
from biz.document_management.document_query import (
    ProjectDocumentation,
    GetSevenColoredStones,
    ProjectDocumentUpload,
    PMExamineAndApprove,
    ProjectContract,
)

# 修改总控计划
from biz.site_implementation.modification_overall_control_plan import (
    GetDataInLibrary,
    ModificationOverallControlPlan,
    ProjectManagementApproval,
    SupervisionApproval,
    PMApproval,
    ImportTheOverallControlPlan,
    # 总控计划展示接口
    MasterControlPlan,
)

# PM周报模板
from biz.weekly_report_template.PM_weekly_report_template import PMGetBasicInformation
from biz.weekly_report_template.PM_weekly_report_template import PMEditingMessage
from biz.weekly_report_template.PM_weekly_report_template import PMMessageSend
from biz.weekly_report_template.PM_weekly_report_template import PMMessageStorage

# 内网权限管控接口
from biz.project_interface.intranet_permission_management import (
    ProjectPersonnelDataQuery,
)

# 流程进展by项目
from biz.project_interface.process_progress_by_project import ProcessProgressByProject
from biz.project_interface.process_progress_by_project import TestSendEmailTBBid
from biz.project_interface.process_progress_by_project import (
    ProjectEquipmentInformationDeliveryQuery,
)
from biz.project_interface.procurement_stage_progress_display import (
    ProcurementStageProgressDisplay,
)

# 组织架构人员信息获取
from biz.project_interface.organizational_structure_membership_details import (
    OrganizationStructureMemberDetails,
)

# 流程连接
from biz.project_connection.project_connection import CreateTicket

# 刷新流程变量
from biz.project_interface.parsing_interface import RefreshVariables
from biz.cooperative_partner.error_message_prompt import ErrorMessagePrompt

# 查看流程进度
from biz.cooperative_partner.cooperative_partner_general_method import (
    CheckProgressWorkOrder,
    CooperativePartnerGeneralMethod,
    MasterControlPlanTemplate,
    IsPartnerProcessAvailable,
    IsItOrNotCooperativePartner,
)

# 合作版：设备生成跟踪（Dcops）
from biz.cooperative_partner.Dcops.cooperative_partner_equipment_production_arrival import (
    RequestMergerInterface,
    PartnerCallInterface,
    WaitPartnerCallInterface,
    ProcessNodeInterface,
    YGJHXgApproval,
    YGSCZBXgApproval,
    YGJDXgApprovalBl,
    YGJDXgApprovalSC,
    YGJDXgApprovalCc,
    DeliveryDataReviewJl,
    DeliveryDataReviewXg,
    ArrivalDataReviewJl,
    ArrivalDataReviewXg,
    SignReceivedGoodsSiteJl,
    SignReceivedGoodsSiteXg,
    SignReceivedGoodsSitePM,
)

# 合作版：项目开办（外部）
from biz.cooperative_partner.WB.cooperative_partner_construction_before import (
    ProjectOpen,
    WaitPartnerCallInterfaceKb,
    PartnerCallInterfaceKb,
    SgfzSupervisionExaminationApproval,
    WgqrSupervisionExaminationApproval,
    WgqrItemApproval,
    WgqrPmApproval,
)
from biz.cooperative_partner.Dcops.cooperative_partner_construction_before import (
    WaitPartnerCallInterfaceKbDcops,
)

# 合作版：总控计划修改（外部）
from biz.cooperative_partner.WB.cooperative_partner_modification_overall_control_plan import (
    ModificationOverallControlPlanWB,
    WaitPartnerCallInterfaceZkjhxg,
    PartnerCallInterfaceZkjhxg,
    ZkjhxgSupervisionExaminationApproval,
    ZkjhxgItemApproval,
    ZkjhxgPmApproval,
)

# 合作版：设备进场前施工准备（外部）
from biz.cooperative_partner.WB.cooperative_partner_construction_preparation import (
    WhetherThereAnyOtherConstruction,
    ConstructionPreparation,
    WaitPartnerCallInterfaceSbjc,
    PartnerCallInterfaceSbjc,
    SbjcSgfaGroundSupervisionExaminationApproval,
    SbjcSgfaAHUSupervisionExaminationApproval,
    SbjcSgfaWaterTreatmentSupervisionExaminationApproval,
    SbjcSgfaOtherSupervisionExaminationApproval,
    SbjcWgqrGroundSupervisionExaminationApproval,
    SbjcWgqrAHUSupervisionExaminationApproval,
    SbjcWgqrWaterTreatmentSupervisionExaminationApproval,
    SbjcWgqrOtherSupervisionExaminationApproval,
    SbjcWgqrGroundItemApproval,
    SbjcWgqrAHUItemApproval,
    SbjcWgqrWaterTreatmentItemApproval,
    SbjcWgqrOtherItemApproval,
    SbjcWgqrGroundPmApproval,
    SbjcWgqrAHUPmApproval,
    SbjcWgqrWaterTreatmentPmApproval,
    SbjcWgqrOtherPmApproval,
)
from biz.cooperative_partner.Dcops.cooperative_partner_construction_preparation import (
    WaitPartnerCallInterfaceSbjcDcops,
)

# 合作版：市电专项（外部）
from biz.cooperative_partner.WB.cooperative_partner_mains_special import (
    MainsSpecial,
    WaitPartnerCallInterfaceSdzx,
    PartnerCallInterfaceSdzx,
    SdzxWdbzSupervisionExaminationApproval,
    SdzxWdfaSupervisionExaminationApproval,
    SdzxKkfSupervisionExaminationApproval,
    SdzxGdhtSupervisionExaminationApproval,
    SdzxDdxySupervisionExaminationApproval,
    SdzxSdqjcSupervisionExaminationApproval,
    SdzxWgqrSdSupervisionExaminationApproval,
    SdzxWgqrSdItemApproval,
    SdzxWgqrSdPmApproval,
)

# 合作版：市电专项（Dcops）
from biz.cooperative_partner.Dcops.cooperative_partner_mains_special import (
    MainsSpecialDcops,
    WaitPartnerCallInterfaceSdzxDcops,
)

# 合作版：安装施工（外部）
from biz.cooperative_partner.WB.cooperative_partner_installation_construction import (
    InstallationConstructionPreparationWB,
    WaitPartnerCallInterfaceAzsg,
    PartnerCallInterfaceAzsg,
    ProjectPlanSupervisionExaminationApproval,
    ItBloack1SupervisionExaminationApproval,
    ItBloack2SupervisionExaminationApproval,
    ItBloack3SupervisionExaminationApproval,
    ItBloack4SupervisionExaminationApproval,
    DlzlSupervisionExaminationApproval,
    CfsclSupervisionExaminationApproval,
    AhuSupervisionExaminationApproval,
    BgqySupervisionExaminationApproval,
    ItBloack1FckjSupervisionExaminationApproval,
    ItBloack1DqgcSupervisionExaminationApproval,
    ItBloack1RdgcSupervisionExaminationApproval,
    ItBloack1ZxgcSupervisionExaminationApproval,
    ItBloack1XfgcSupervisionExaminationApproval,
    ItBloack1NtgcSupervisionExaminationApproval,
    ItBloack1SdtsSupervisionExaminationApproval,
    ItBloack2FckjSupervisionExaminationApproval,
    ItBloack2DqgcSupervisionExaminationApproval,
    ItBloack2RdgcSupervisionExaminationApproval,
    ItBloack2ZxgcSupervisionExaminationApproval,
    ItBloack2XfgcSupervisionExaminationApproval,
    ItBloack2NtgcSupervisionExaminationApproval,
    ItBloack2SdtsSupervisionExaminationApproval,
    ItBloack3FckjSupervisionExaminationApproval,
    ItBloack3DqgcSupervisionExaminationApproval,
    ItBloack3RdgcSupervisionExaminationApproval,
    ItBloack3ZxgcSupervisionExaminationApproval,
    ItBloack3XfgcSupervisionExaminationApproval,
    ItBloack3NtgcSupervisionExaminationApproval,
    ItBloack3SdtsSupervisionExaminationApproval,
    ItBloack4FckjSupervisionExaminationApproval,
    ItBloack4DqgcSupervisionExaminationApproval,
    ItBloack4RdgcSupervisionExaminationApproval,
    ItBloack4ZxgcSupervisionExaminationApproval,
    ItBloack4XfgcSupervisionExaminationApproval,
    ItBloack4NtgcSupervisionExaminationApproval,
    ItBloack4SdtsSupervisionExaminationApproval,
    DlzlDlfcSupervisionExaminationApproval,
    DlzlBfxtSupervisionExaminationApproval,
    DlzlWsdSupervisionExaminationApproval,
    DlzlSdtsSupervisionExaminationApproval,
    CfsclCffcSupervisionExaminationApproval,
    CfsclSclSupervisionExaminationApproval,
    CfsclCflySupervisionExaminationApproval,
    CfsclSdtsSupervisionExaminationApproval,
    AhuZxgcSupervisionExaminationApproval,
    AhuNtgcSupervisionExaminationApproval,
    AhuDqgcSupervisionExaminationApproval,
    AhuRdgcSupervisionExaminationApproval,
    AhuSdtsSupervisionExaminationApproval,
    BgqyZxgcSupervisionExaminationApproval,
    BgqyNtgcSupervisionExaminationApproval,
    BgqyDqgcSupervisionExaminationApproval,
    BgqyRdgcSupervisionExaminationApproval,
    BgqyXfgcSupervisionExaminationApproval,
    BgqySdtsSupervisionExaminationApproval,
    AzsgJyReportSupervisionExaminationApproval,
    AzsgJyReportItemApproval,
    AzsgJyReportPmApproval,
    AzsgSdReportSupervisionExaminationApproval,
    AzsgSdReportItemApproval,
    AzsgSdReportPmApproval,
)

# 合作版：问题整改（外部）
from biz.cooperative_partner.WB.cooperative_partner_rectification_problem import (
    FeedbackApprovalResultsTestPlatform,
    RectificationProblemPreparationWB,
    WaitPartnerCallInterfaceWtzg,
    PartnerCallInterfaceWtzgCs,
    WtzgSupervisionAndReview,
    IsItOrNotCooperativePartnerWd,
    WtzgProjectManagementReview,
)

# 合作版：验收转维（外部）
from biz.cooperative_partner.WB.cooperative_partner_acceptance_transfer import (
    AcceptanceTransferDimensionWB,
    WaitPartnerCallInterfaceYszw,
    PartnerCallInterfaceYszw,
    PxSupervisionExaminationApproval,
    PxItemApproval,
    PxPmApproval,
    WzSupervisionExaminationApproval,
    WzItemApproval,
    WzPmApproval,
)

# 合作版：验收转维
from biz.cooperative_partner.cooperative_partner_acceptance_transfer import (
    AcceptanceTransferDimension
)

# 合作版：接维资料上传（外部）
from biz.cooperative_partner.WB.cooperative_partner_dimension_connection_data import (
    DimensionConnectionWB,
    WaitPartnerCallInterfaceJwzl,
    PartnerCallInterfaceJwzl,
    JwzlSupervisionExaminationApproval,
    JwzlItemApproval,
    JwzlPmApproval,
)

# 合作版：深化设计(外部)
from biz.cooperative_partner.WB.cooperative_partner_deepening_design import (
    DeepeningDesignWB,
    WaitPartnerCallInterfaceShsj,
    PartnerCallInterfaceShsj,
    ShsjSupervisionExaminationApproval,
    ShsjItemApproval,
    ShsjPmApproval,
)

# 合作版：深化设计(Dcops)
from biz.cooperative_partner.Dcops.cooperative_partner_deepening_design import (
    DeepeningDesignDcops,
    WaitPartnerCallInterfaceShsjDcops,
)

# 合作版：项目报建（外部）
from biz.cooperative_partner.WB.cooperative_partner_project_application import (
    ProjectApplicationWB,
    WaitPartnerCallInterfaceXmbj,
    PartnerCallInterfaceXmbj,
    XmlxItemApproval,
    SjhtItemApproval,
    TzssSdItemApproval,
    JlzfSdItemApproval,
    GczfSdItemApproval,
    JlhtSdItemApproval,
    GchtSdItemApproval,
    NmghtSdItemApproval,
    GsywSdItemApproval,
    SgxkSdItemApproval,
    XfbaSdItemApproval,
    JgbaSdItemApproval,
    XfbgPmApproval,
)

# 合作版：项目报建（Dcops）
from biz.cooperative_partner.Dcops.cooperative_partner_project_application import (
    ProjectApplicationDcops,
    WaitPartnerCallInterfaceXmbjDcops,
)

# 合作版：项目启动（外部）
from biz.cooperative_partner.WB.cooperative_partner_project_start_up import (
    ProjectStartUpSupplier,
    WaitPartnerCallInterfaceXmqd,
    PartnerCallInterfaceXmqd,
    ZkjhSupervisionExaminationApproval,
    ZkjhItemApproval,
    ZkjhPMApproval,
    QdwdPMApproval,
    ExternalInterfacePersonInput,
    StartFileUpload,
)

# 合作版：项目启动（Dcops）
from biz.cooperative_partner.Dcops.cooperative_partner_project_start_up import (
    ProjectStartUpDcops,
    ZkjhSupervisionExaminationApprovalDcops,
    ZkjhItemApprovalDcops,
    ZkjhPMApprovalDcops,
    WaitPartnerCallInterfaceXmqdDcops,
)

# 合作版：测试验证（外部）
from biz.cooperative_partner.WB.cooperative_partner_test_verification import (
    TestVerificationWB,
    WaitPartnerCallInterfaceCsyz,
    PartnerCallInterfaceCsyz,
    JszlSupervisionExaminationApproval,
    JszlItemApproval,
    JszlPMApproval,
)

# 混元
from biz.large_model_intelligence.hunyuan import (
    HunYuan,
)

# IDCDB数据写入
from biz.IDCDB_acquisition_scheme.acquisition_scheme import OperationsManagementInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import BusinessInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import DataPassageInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import NiansiInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import LayhuangInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import JackxzhangInput
from biz.IDCDB_acquisition_scheme.acquisition_scheme import IDCDBDataWriting

# 技术小组组建
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationDesignatedLeader,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationDesignatedTeam,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationAutomaticTasks,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationDesignatedTeamMembers,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationMemberApprovalDirector,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationMemberApprovalGM,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationMemberApprovalCenterLeader,
)
from biz.joint_construction_party.technical_team_formation import NotifyMembers
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationBasicInformation,
)
from biz.joint_construction_party.technical_team_formation import (
    TeamFormationBasicInformationFaq,
)

# 技术文件制定
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentSubmission,
)
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentAutomaticTasks,
)
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentApproval,
)
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentUpdate,
)
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentDirectorApproval,
)
from biz.joint_construction_party.technical_documentation_preparation import (
    TechnicalDocumentUpdateSelect,
)

# 终验反馈-给星辰
from biz.construction_final_acceptance.final_acceptance_task import (
    FinalAcceptanceInfoInput,
)
from biz.construction_final_acceptance.auto_task import FinalAcceptanceAutoTask
from biz.construction_final_acceptance.into_db import FinalAcceptanceIntoDb

# 招采终验资料上传
from biz.construction_maintenance_platform_docking.construction_maintenance_process import (
    Final_Inspection_Data_Upload,
)

# 初验反馈-给星辰
from biz.construction_initial_acceptance.initial_acceptance_task import (
    InitialAcceptanceInfoInput,
    InitialAcceptanceEmailPush,
)
from biz.construction_initial_acceptance.auto_task import InitialAcceptanceAutoTask
from biz.construction_initial_acceptance.into_db import InitialAcceptanceIntoDb

# 商务付款信息查询
from biz.business_payment.auto_task import BusinessPaymentAutoTask

# 刷新流程变量
from biz.update_problem_flow_variables.update_problem_flow_variables import (
    UpdateProblemFlowVariables,
)

# 通过模组获取项目角色
from biz.project_role.project_role_api import ProjectRoleApi

# 项目po信息查询（集成商、时间）
from biz.project_po_info.project_po_info_api import ProjectPoInfo

# 财管审批节点修改测试块
from biz.tb_service.review_bid import GmAuditBakTest

# 供应商份额分配
from biz.supplier_share_allocation.auto_task import SupplierShareAllocationAutoTask
from biz.supplier_share_allocation.supplier_share_allocation_task import (
    SupplierShareAllocationInput,
)
from biz.supplier_share_allocation.supplier_share_allocation_task import (
    ObtainSupplierInformation,
)
from biz.supplier_share_allocation.into_db import SupplierShareAllocationIntoDb

# 查找项目po所属设备的负责人
from biz.supplier_share_allocation.equipment_manager import GetEquipmentManager

# 交付时间变更流程
from biz.time_change_flow.auto_task import TimeChangeAutoTask
from biz.time_change_flow.time_change_form_task import TimeChangeForm
from biz.time_change_flow.time_change_form_task import TimeChangeOMApproval
from biz.time_change_flow.time_change_form_task import TimeChangeBusinessApproval
from biz.time_change_flow.time_change_form_task import TimeChangePMApproval

# 项目日报填写流程
from biz.project_daily_report.auto_task import ProjectDailyReportAutoTask
from biz.project_daily_report.project_daily_report_artificial_agent import (
    ProjectReportSupervisionWrite,
)
from biz.project_daily_report.project_daily_report_artificial_agent import (
    ProjectManagerConfirm,
)
from biz.project_daily_report.project_daily_report_artificial_agent import (
    ProjectManagerWrite,
)
from biz.project_daily_report.project_daily_report_summary import DailyReportSend
from biz.project_daily_report.daily_report_api import DailyReportApi

# 建设项目总控计划自动化
from biz.plan_automation.auto_task import (
    PlanAutomationAutoTask,
    WaitProjectTicketInfoConfirm,
)
from biz.plan_automation.reminder_ticket_info_confirm import ReminderTicketInfoConfirm
from biz.plan_automation.plan_automation_api import PlanAutomationApi
from biz.plan_automation.rental_auto_task import RentalPlanAutomationAutoTask

# 工单更新流程
from biz.ticket_sync.ticket_sync import SyncTicketData

# 测试用手动扭转工单状态
from biz.utils.ticket_processing_tools import TicketProcessingTools

# 混元ai调用
from biz.ai_query.hunyuan_query_api import HunyuanQueryApi

# 项目名称修改
from biz.project_name_change.auto_task import ProjectNameChangeAutoTask
from biz.project_name_change.wework_dept_update import WeworkDeptUpdate
from biz.project_name_change.project_name_change import (
    ProjectNameChange,
    ProjectNameChangeLeaderApproval,
)

# 项目报建-合作伙伴
from biz.cooperative_partner.project_application_partner.auto_task import (
    ConstructionApplicationProcessPartnerAutoTask,
)
from biz.cooperative_partner.project_application_partner.project_construction_application_task import (
    ProjectApplicationXgApproval,
    ProjectApplicationPmApproval,
    ProjectApplicationFlowTrace,
    XfbgPmApproval,
)
from biz.cooperative_partner.project_application_partner.construction_application_process_partner_api import (
    ConstructionApplicationProcessPartnerApi,
)
from biz.cooperative_partner.project_application_partner.into_db import (
    ConstructionApplicationProcessPartnerIntoDb,
)
from biz.cooperative_partner.project_application_partner.cooperative_partner_project_application import (
    ProjectApplication,
    PartnerCallInterfaceXmbj,
)

entries = locals()


def discover_entry(local_entries):
    all_discover = []
    for entry in local_entries:
        if entry.startswith("Ajax"):
            continue
        if inspect.isclass(local_entries[entry]):
            for member_name, member in inspect.getmembers(local_entries[entry]):
                if not member_name.startswith("_") and inspect.isfunction(member):
                    func_signature = inspect.signature(member)
                    call_signature = []
                    for param in func_signature.parameters:
                        if param == "self":
                            continue
                        # call_signature.append((param, func_signature.parameters[param].annotation.__name__,
                        #                        func_signature.parameters[param].default))
                        call_signature.append(
                            (
                                param,
                                func_signature.parameters[param].annotation.__name__,
                            )
                        )
                    path = inspect.getfile(local_entries[entry])
                    item = {
                        "service": entry,
                        "method": member_name,
                        "call_signature": call_signature,
                        "path": path.split("biz")[-1],
                        "return_signature": func_signature.return_annotation.__name__,
                        "doc": member.__doc__,
                    }
                    all_discover.append(item)
    return all_discover


update_discovery(discover_entry(entries))
