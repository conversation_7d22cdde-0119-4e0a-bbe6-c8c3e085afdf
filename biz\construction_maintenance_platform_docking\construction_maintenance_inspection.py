import datetime

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate
from iBroker.lib import mysql

from biz.construction_before_eruipment.tools import Tools


class ConstructionMaintenanceInspection(object):
    """
    接维：现场查验——问题整改
    """

    def __init__(self):
        super(ConstructionMaintenanceInspection, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def hire_project_get_rectifier_information(self, problem_list, project_name, db, current_time, ticket_id):
        zb_account = ""
        xg_account = ""
        jl_account = ""
        zb_service_provider = ""
        zb_list = []
        xg_list = []
        jl_list = []
        major = problem_list[0].get("major")
        module_name = problem_list[0].get("project_name")
        question_ticket = problem_list[0].get("question_ticket")
        rectifier_list = []
        rectifier_data = []

        sql_all = (
                "SELECT account,role,service_provider FROM project_role_account"
                " WHERE project_name='%s' and del_flag = '0'" % project_name
        )
        all_person_list = db.get_all(sql_all)
        rectifier = ''
        if major == "配电":
            major = "电气"
        for row in all_person_list:
            role = row.get("role", "")
            account = row.get("account", "")
            if major in role and "总包" in role:
                rectifier = account
            if role == "总包-项目经理":
                zb_account = account
                zb_service_provider = row.get("service_provider")
            if "总包" in role:
                zb_list.append(account)
                zb_service_provider = row.get("service_provider")
                rectifier_data.append({"role": f"集成商-{major}专业接口",
                                       "account": account,
                                       "service_provider":zb_service_provider})
                rectifier_list.append({"label": f"{role}（{account}）", "value": account})
            elif role == "监理-总监理工程师":
                jl_account = account
                jl_list.append(account)
            elif role == "合建方-项目经理":
                xg_account = account
                self.workflow_detail.add_kv_table("xg_account", {"xg_account": xg_account})
                xg_list.append(xg_account)
                self.workflow_detail.add_kv_table("xg_list", {"xg_list": xg_list})
            elif major in role and "监理" in role:
                jl_list.append(account)
            elif major in role and "合建方" in role:
                xg_list.append(account)
        data = {}
        for i in problem_list:
            i["problem_put_forward_time"] = str(current_time)
            problem_photo = i.get("issue_photo")
            problem_photo_url_list = []
            if problem_photo:
                for j in problem_photo:
                    if j.get("url") is not None:
                        problem_photo_url_list.append(j.get("url"))
            problem_photo_url = ";".join(problem_photo_url_list)
            data["question_ticket"] = i.get("question_ticket")
            data["ticket_id"] = ticket_id
            data["project_name"] = project_name
            data["module_name"] = module_name
            data["major"] = i.get("major")
            data["source"] = i.get("source")
            data["device_type"] = i.get("device_type")
            data["device_number"] = i.get("device_number")
            data["issue_description"] = i.get("issue_description")
            data["issue_photo"] = problem_photo_url
            data["problem_type"] = i.get("problem_type")
            data["problem_put_forward_time"] = str(current_time)
        tb_db = mysql.new_mysql_instance("tbconstruct")
        # 存数据库
        tb_db.begin()
        tb_db.insert("maintenance_rectification", data)
        tb_db.commit()

        variables = {
            "problem_list": problem_list,
            "project_name": project_name,
            "module_name": module_name,
            "zb_account": zb_account,
            "jl_account": jl_account,
            "xg_account": xg_account,
            "rectifier_data": rectifier_data,
            "rectifier_list": rectifier_list,
            "zb_service_provider": zb_service_provider,
            "zb_list": zb_list,
            "xg_list": xg_list,
            "jl_list": jl_list,
            "rectifier": rectifier,
            "data": data
        }
        return variables

    def get_rectifier_info(self):
        current_time = datetime.date.today()
        ticket_id = self.ctx.variables.get("ticket_id")
        problem_list = self.ctx.variables.get("problem_list")
        rectifier_list = []
        if problem_list:
            zb_account = ""
            xg_account = ""
            jl_account = ""
            zb_service_provider = ""
            zb_list = []
            xg_list = []
            jl_list = []
            major = problem_list[0].get("major")
            module_name = problem_list[0].get("mozu_name")
            db = mysql.new_mysql_instance("tbconstruct")
            sql = (
                    "SELECT project_name FROM project_module_mapping_relationship "
                    " WHERE module_name='%s'" % module_name
            )
            project_name_list = db.get_all(sql)
            if project_name_list:
                project_name = project_name_list[0].get("project_name")
                sql_is_hire = ("SELECT construction_mode FROM risk_early_warning_data "
                               f"WHERE project_name='{project_name}'")
                is_hire = False if db.get_row(sql_is_hire).get("construction_mode", "") == "自建" else True
                if is_hire:
                    variables = self.hire_project_get_rectifier_information(problem_list, project_name, db,
                                                                            current_time, ticket_id)
                    variables.update({"is_hire": "1"})
                    flow.complete_task(self.ctx.task_id, variables=variables)
                    return
                sql_all = (
                        "SELECT account,name FROM project_role_account"
                        " WHERE project_name='%s' and del_flag = '0'" % project_name
                )
                name_all = db.get_all(sql_all)
                count_all = []
                name_list = []
                if name_all:
                    for name in name_all:
                        value = name.get("account")
                        if value not in name_list:
                            count_all.append(
                                {
                                    "label": name.get("account")
                                             + "("
                                             + name.get("name")
                                             + ")",
                                    "value": value,
                                }
                            )
                            name_list.append(value)
                # 获取厂家信息
                sql = (
                        "SELECT role,account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' AND role LIKE '厂家%%' and del_flag = '0'"
                        % project_name
                )
                rectifier_data = db.get_all(sql)
                if rectifier_data:
                    for i in rectifier_data:
                        rectifier_list.append(
                            {"label": f'{i.get("role")}（{i.get("account")}）', "value": i.get("account")}
                        )
                # 获取集成商、监理、项管总接口
                zb_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '集成商-项目经理' and del_flag = '0'"
                        % project_name
                )
                jl_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '监理-总监理工程师' and del_flag = '0'"
                        % project_name
                )
                xg_sql = (
                        "SELECT account,service_provider FROM project_role_account"
                        " WHERE project_name='%s' and role = '项管-项目经理' and del_flag = '0'"
                        % project_name
                )
                zb_boss_sql = db.get_all(zb_sql)
                jl_boss_sql = db.get_all(jl_sql)
                xg_boss_sql = db.get_all(xg_sql)
                # 节点人都加入总接口
                if zb_boss_sql:
                    zb_account = zb_boss_sql[0].get("account")
                    zb_service_provider = zb_boss_sql[0].get("service_provider")
                    rectifier_data.append({
                        "role": "集成商-项目经理",
                        "account": zb_account,
                        "service_provider": zb_service_provider
                    })
                    zb_list.append(zb_account)
                    rectifier_list.append({"label": f"集成商-项目经理（{zb_account}）", "value": zb_account})
                if jl_boss_sql:
                    jl_account = jl_boss_sql[0].get("account")
                    jl_list.append(jl_account)
                if xg_boss_sql:
                    xg_account = xg_boss_sql[0].get("account")
                    xg_list.append(xg_account)

                if major:
                    if major == "配电":
                        major = "电气"
                    sql1 = (
                            "SELECT role,account,service_provider FROM project_role_account"
                            " WHERE project_name='%s' AND role LIKE '%%%s%%'and del_flag = '0'"
                            % (project_name, major)
                    )
                    account_list = db.get_all(sql1)
                    if account_list:
                        for i in account_list:
                            if "集成商" in i.get("role") or "总包" in i.get("role"):
                                zb_account = i.get("account")
                                zb_list.append(zb_account)
                                zb_service_provider = i.get("service_provider")
                                rectifier_data.append({
                                    "role": f"集成商-{major}专业接口",
                                    "account": zb_account,
                                    "service_provider": zb_service_provider
                                })
                                rectifier_list.append(
                                    {"label": f'{i.get("role")}（{i.get("account")}）', "value": i.get("account")}
                                )
                            elif "监理" in i.get("role"):
                                jl_list.append(i.get("account"))
                            elif "项管" in i.get("role"):
                                xg_list.append(i.get("account"))

                data = {}
                for i in problem_list:
                    i["problem_put_forward_time"] = str(current_time)
                    problem_photo = i.get("issue_photo")
                    problem_photo_url_list = []
                    if problem_photo:
                        for j in problem_photo:
                            if j.get("url") is not None:
                                problem_photo_url_list.append(j.get("url"))
                    problem_photo_url = ";".join(problem_photo_url_list)
                    data["question_ticket"] = i.get("question_ticket")
                    data["ticket_id"] = ticket_id
                    data["project_name"] = project_name
                    data["module_name"] = module_name
                    data["major"] = i.get("major")
                    data["source"] = i.get("source")
                    data["device_type"] = i.get("device_type")
                    data["device_number"] = i.get("device_number")
                    data["issue_description"] = i.get("issue_description")
                    data["issue_photo"] = problem_photo_url
                    data["problem_type"] = i.get("problem_type")
                    data["problem_put_forward_time"] = str(current_time)
                tb_db = mysql.new_mysql_instance("tbconstruct")
                # 存数据库
                tb_db.begin()
                tb_db.insert("maintenance_rectification", data)
                tb_db.commit()
                variables = {
                    "problem_list": problem_list,
                    "project_name": project_name,
                    "module_name": module_name,
                    "zb_account": zb_account,
                    "jl_account": jl_account,
                    "xg_account": xg_account,
                    "rectifier_data": rectifier_data,
                    "rectifier_list": rectifier_list,
                    "zb_service_provider": zb_service_provider,
                    "zb_list": zb_list,
                    "xg_list": xg_list,
                    "jl_list": jl_list,
                    "count_all": count_all,
                    # 'data': data
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
            else:
                self.workflow_detail.add_kv_table(
                    "自动获取数据节点", {"success": False, "data": "没有获取到项目名称"}
                )
        else:
            self.workflow_detail.add_kv_table(
                "自动获取数据节点", {"success": False, "data": "没有传问题信息"}
            )

    def storing_data(self):
        """
        存储数据
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        insert_data = self.ctx.variables.get("insert_data")
        jl_rejection = self.ctx.variables.get("jl_rejection")
        xg_rejection = self.ctx.variables.get("xg_rejection")
        jl_remark = self.ctx.variables.get("jl_remark")
        xg_remark = self.ctx.variables.get("xg_remark")
        reason_rejection = self.ctx.variables.get("reason_rejection")
        problem_level = self.ctx.variables.get("problem_level")

        insert_data["jl_rejection"] = jl_rejection
        insert_data["xg_rejection"] = xg_rejection
        insert_data["jl_remark"] = jl_remark
        insert_data["xg_remark"] = xg_remark
        insert_data["reason_rejection"] = reason_rejection
        insert_data["problem_level"] = problem_level
        tb_db = mysql.new_mysql_instance("tbconstruct")
        cond = {"ticket_id": ticket_id}
        # 存数据库
        tb_db.begin()
        tb_db.update("maintenance_rectification", insert_data, cond)
        tb_db.commit()

        flow.complete_task(self.ctx.task_id)

    def maintenance_return_data(self):
        solve_list = self.ctx.variables.get("solve_list")
        process_id = self.ctx.variables.get("process_id")

        problem_tickets_info = {"solve_list": solve_list, "ticket_status": "关闭"}
        WorkflowVarUpdate(
            instance_id=process_id,
            variables={"problem_tickets_info": problem_tickets_info},
        ).call()
        rep = gnetops.request(action="Nbroker",
                              method="UrlProxy",
                              ext_data={
                                  "NbrokerData": {
                                      "context": {
                                          "service_name": "ProblemOnsiteCommonFlow",
                                          "method_name": "wait_problem_rply_api"
                                      },
                                      "args": {
                                          'pti': problem_tickets_info,
                                          'instance_id': process_id,
                                      },
                                  },
                                  "ServiceUrl": "http://ibroker-dcmanage:8080/nBroker/api/v1/task",
                              }
                              )
        variables = {"problem_tickets_gb": problem_tickets_info}
        flow.complete_task(self.ctx.task_id, variables=variables)


class MaintenanceChooseRectifier(AjaxTodoBase):
    """
    上传计划
    """

    def __init__(self):
        super(MaintenanceChooseRectifier, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        instance_id = self.ctx.instance_id
        zb_service_provider = self.ctx.variables.get("zb_service_provider")
        zb_account = self.ctx.variables.get("zb_account")
        ticket_id = self.ctx.variables.get("ticket_id")
        rectifier_data = self.ctx.variables.get("rectifier_data")
        problem_list = process_data.get("problem_list")
        responsible = process_data.get("rectifier")
        responsible_unit_list = []
        # 获取责任单位数据源
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT field_value FROM project_member_basic_info WHERE field_name='所属服务商'"
        field_value_list = db.get_all(sql)
        if field_value_list:
            field_value = field_value_list[0].get("field_value").split(";")
            if type(field_value) == list:
                for i in field_value:
                    if i:
                        responsible_unit_list.append({
                            "label": i,
                            "value": i,
                        })

        responsible_manufacturer = ""
        if responsible == zb_account:
            responsible_manufacturer = zb_service_provider
        elif rectifier_data and responsible:
            for i in rectifier_data:
                if responsible == i.get("account"):
                    responsible_manufacturer = i.get("service_provider")
        if problem_list:
            for i in problem_list:
                i["responsible_manufacturer"] = responsible_manufacturer

        # 存数据库
        data = {
            "responsible_manufacturer": responsible_manufacturer,
            "rectifier": responsible,
        }
        cond = {
            "ticket_id": ticket_id,
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("maintenance_rectification", data, cond)
        tb_db.commit()
        variables = {
            "responsible": responsible,
            "rectifier": responsible,
            "responsible_manufacturer": responsible_manufacturer,
            "problem_list": problem_list,
            "instance_id": instance_id,
            "responsible_unit_list": responsible_unit_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class MaintenanceModifyProblemUpload(AjaxTodoBase):
    """
    问题整改上传
    """

    def __init__(self):
        super(MaintenanceModifyProblemUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        personnel_selection = process_data.get("personnel_selection")
        problem_list = process_data.get("problem_list")
        if personnel_selection:
            if personnel_selection == "通过" and problem_list:
                if problem_list[0].get("plan_resolution_date") and problem_list[0].get(
                        "responsible_manufacturer"
                ):

                    variables = {
                        "problem_list": problem_list,
                        "solve_list": problem_list,
                        "personnel_selection": personnel_selection,
                    }
                else:
                    return {"code": -1, "msg": "责任单位和计划解决日期为必填项"}
            else:
                variables = {"personnel_selection": personnel_selection}
        else:
            return {"code": -1, "msg": "请进行选择"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class MaintenanceModifyProblemSolve(AjaxTodoBase):
    """
    问题整改确认
    """

    def __init__(self):
        super(MaintenanceModifyProblemSolve, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        solve_list = process_data.get("solve_list")
        reason_rejection = self.ctx.variables.get("cause_rejection")
        appendix = process_data.get("appendix")
        modify_remake = process_data.get("modify_remake")
        progress_desc = process_data.get("progress_desc")

        process_id = ""

        data = {"appendix": str(appendix)}
        if solve_list and len(solve_list) > 0:
            for i in solve_list:
                fix_photo = i.get("issue_close_photo")
                issue_close_photo = str(fix_photo)
                url_list = []
                show_photo = []
                if fix_photo:
                    for j in fix_photo:
                        url, result = Tools.check_photo(j)
                        if result:
                            return url
                        url_list.append(url)
                        show_photo.append(
                            {
                                "url": url,
                                "height": "150px",
                                "width": "150px",
                                "marginRight": "5px",
                            }
                        )
                    i["fix_photo_url"] = ";".join(url_list)
                i["photo"] = show_photo
                process_id = i.get("Process_id")

                data["cause_solution_process"] = i.get("cause_solution_process")
                data["solve_type"] = i.get("solve_type")
                data["responsible_manufacturer"] = i.get("responsible_manufacturer")
                data["plan_resolution_date"] = i.get("plan_resolution_date")
                data["fix_photo_url"] = i.get("fix_photo_url")
                data["issue_close_photo"] = issue_close_photo
                data["modify_remake"] = modify_remake
                data["remark"] = i.get("remark")
                data["problem_end_time"] = i.get("problem_end_time")

        problem_tickets_info = {
            "solve_list": solve_list,
            "ticket_status": "更新",
            "progress_desc": progress_desc,
        }
        WorkflowVarUpdate(
            instance_id=process_id,
            variables={"problem_tickets_info": problem_tickets_info},
        ).call()
        variables = {
            "solve_list": solve_list,
            "insert_data": data,
            "process_user": process_user,
            "reason_rejection": reason_rejection,
            "modify_remake": modify_remake,
            "progress_desc": progress_desc,
            "process_id": process_id,
            "problem_tickets_gx": problem_tickets_info,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class MaintenanceWaitingResults(object):
    """
    等待测试平台审核结果
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def maintenance_audit_results(self):
        pass
