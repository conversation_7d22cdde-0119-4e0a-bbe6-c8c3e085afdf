[supervisord]
nodaemon=true
loglevel=info
logfile_backups=10
logfile_maxbytes=50MB
logfile=/data/wwwlogs/supervisord.log
pidfile=/var/run/supervisord.pid

[program:python-trpc-server]
command=/data/nbroker/start_trpc_server.sh
autostart=true
autorestart=true
startsecs=5
startretries=3
user=root
stdout_logfile=/data/wwwlogs/stdout.log
stderr_logfile=/data/wwwlogs/stderr.log

[program:zylog]
command=/data/nbroker/start_zylog.sh
autostart=true
autorestart=true
startsecs=5
startretries=3
user=root
stdout_logfile=/data/wwwlogs/stdout.log
stderr_logfile=/data/wwwlogs/stderr.log
