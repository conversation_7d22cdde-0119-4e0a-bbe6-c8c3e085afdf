import uuid
import numpy as np
import pandas as pd

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.lib import mysql
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_process.cos_lib import COSLib


# 质量评估评价标准查询
class StandardQuery(object):

    # 质量评估评价标准分类查询(by team_name)
    @staticmethod
    def get_standard_class_list(team_name, n=0):
        query_sql = (
            "select * from quality_evaluation_standard where team_name = '%s' "
            % team_name
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        standard_class_dict = {
            "standard_class1_list": [],
            "standard_class2_list": [],
            "standard_class3_list": [],
            "standard_class4_list": [],
            "standard_class5_list": [],
            "standard_class6_list": [],
            "standard_class7_list": [],
        }
        standard_class_field_dict = {
            1: "standard_class1_list",
            2: "standard_class2_list",
            3: "standard_class3_list",
            4: "standard_class4_list",
            5: "standard_class5_list",
            6: "standard_class6_list",
            7: "standard_class7_list",
        }
        for item in query_result:
            standard_class_dict[
                standard_class_field_dict[item["category_eq_class_id"]]
            ].append(
                {
                    "team_name": team_name,
                    "category_eq_class_id": item["category_eq_class_id"],
                    "category_eq_class_name": item["category_eq_class_name"],
                    "evaluation_dimensions": item["evaluation_dimensions"],
                    "key_indicator": item["key_indicator"],
                    "assessment_point": item["assessment_point"],
                    "dimension_weight": (
                        float(item["dimension_weight"] / 10**n)
                        if item["dimension_weight"] and n > 0
                        else item["dimension_weight"] if item["dimension_weight"] else 0
                    ),
                    "team_weight": (
                        float(item["team_weight"] / 10**n)
                        if item["team_weight"] and n > 0
                        else item["team_weight"] if item["team_weight"] else 0
                    ),
                    "scoring_rubrics": item["scoring_rubrics"],
                },
            )
        return standard_class_dict

    # 质量评估评价标准详情查询(by team_name, category_eq_class_id, key_indicator)
    def get_standard_detail(team_name, category_eq_class_id, key_indicator):
        query_sql = (
            "select * from quality_evaluation_standard "
            "where team_name = '%s' and category_eq_class_id = '%d' and  key_indicator = '%s'"
            % (team_name, category_eq_class_id, key_indicator)
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        standard_class_name = StandardQuery.get_standard_class_name(
            category_eq_class_id
        )
        temp = {
            "team_name": team_name,
            "category_eq_class_id": category_eq_class_id,
            "category_eq_class_name": standard_class_name,
            "evaluation_dimensions": "",
            "key_indicator": "",
            "target": "",
            "assessment_point": "",
            "scoring_rubrics": "",
            "dimension_weight": "",
            "team_weight": "",
        }
        query_result = query_result[0] if query_result else temp
        standard_detail = {
            "team_name": query_result["team_name"],
            "category_eq_class_id": query_result["category_eq_class_id"],
            "category_eq_class_name": query_result["category_eq_class_name"],
            "evaluation_dimensions": query_result["evaluation_dimensions"],
            "key_indicator": query_result["key_indicator"],
            "target": query_result["target"],
            "assessment_point": query_result["assessment_point"],
            "scoring_rubrics": query_result["scoring_rubrics"],
            "dimension_weight": (
                float(query_result["dimension_weight"] / 10**2)
                if query_result["dimension_weight"]
                else ""
            ),
            "team_weight": (
                float(query_result["team_weight"] / 10**2)
                if query_result["dimension_weight"]
                else ""
            ),
        }
        return standard_detail

    # 质量评估评价标准分类名称查询(by id)
    @staticmethod
    def get_standard_class_name(category_eq_class_id):
        query_sql = (
            "select category_eq_class_name from quality_evaluation_standard_class where id = '%d'"
            % category_eq_class_id
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        standard_class_name = ""
        if query_result:
            standard_class_name = query_result[0]["category_eq_class_name"]
        return standard_class_name

    # 团队对应标准分类的关键指标查询(by team_name)
    @staticmethod
    def get_key_indicator(team_name):
        query_sql = (
            "select category_eq_class_id, key_indicator from quality_evaluation_standard where team_name = '%s' "
            % team_name
        )
        tb_db = mysql.new_mysql_instance("tbconstruct")
        query_result = tb_db.query(query_sql)
        key_indicator_dict = {
            "key_indicator1": [],
            "key_indicator2": [],
            "key_indicator3": [],
            "key_indicator4": [],
            "key_indicator5": [],
            "key_indicator6": [],
            "key_indicator7": [],
        }
        key_indicator_field_dict = {
            1: "key_indicator1",
            2: "key_indicator2",
            3: "key_indicator3",
            4: "key_indicator4",
            5: "key_indicator5",
            6: "key_indicator6",
            7: "key_indicator7",
        }
        for item in query_result:
            key_indicator_dict[
                key_indicator_field_dict[item["category_eq_class_id"]]
            ].append(item["key_indicator"])
        return key_indicator_dict

    # 内控-各团队质量评价标准表格数据组装
    @staticmethod
    def get_team_eq_standard_list(team_standard_class_list):
        team_eq_standard_list = []
        # 集采设备质量评价(AHU、柴发、变压器、中压柜、低压柜、综合配电柜)
        if (
            "standard_class1_list" in team_standard_class_list
            and team_standard_class_list["standard_class1_list"]
        ):
            team_standard_class_list["standard_class1_list"]
            team_eq_standard_list.append(
                {
                    "team_name": team_standard_class_list["standard_class1_list"][0][
                        "team_name"
                    ],
                    "category_eq_class_name": team_standard_class_list[
                        "standard_class1_list"
                    ][0]["category_eq_class_name"].split("(")[0],
                    "standard_list": [
                        *team_standard_class_list["standard_class1_list"]
                    ],
                }
            )
        # 集采设备质量评价(机柜、PDU、电池)
        # if "standard_class2_list" in team_standard_class_list and team_standard_class_list["standard_class2_list"]:
        #     team_standard_class_list["standard_class2_list"]
        #     team_eq_standard_list.append({
        #         "team_name": team_standard_class_list["standard_class2_list"][0]["team_name"],
        #         "category_eq_class_name":
        #             team_standard_class_list["standard_class2_list"][0]["category_eq_class_name"].split("(")[0],
        #         "standard_list": [*team_standard_class_list["standard_class2_list"]]
        #     })
        # 集采设备质量评价(JDM等研发产品)
        # if "standard_class3_list" in team_standard_class_list and team_standard_class_list["standard_class3_list"]:
        #     team_standard_class_list["standard_class3_list"]
        #     team_eq_standard_list.append({
        #         "team_name": team_standard_class_list["standard_class3_list"][0]["team_name"],
        #         "category_eq_class_name":
        #             team_standard_class_list["standard_class3_list"][0]["category_eq_class_name"].split("(")[0],
        #         "standard_list": [*team_standard_class_list["standard_class3_list"]]
        #     })
        # 集成商质量评价
        if (
            "standard_class4_list" in team_standard_class_list
            and team_standard_class_list["standard_class4_list"]
        ):
            team_standard_class_list["standard_class4_list"]
            team_eq_standard_list.append(
                {
                    "team_name": team_standard_class_list["standard_class4_list"][0][
                        "team_name"
                    ],
                    "category_eq_class_name": team_standard_class_list[
                        "standard_class4_list"
                    ][0]["category_eq_class_name"].split("(")[0],
                    "standard_list": [
                        *team_standard_class_list["standard_class4_list"]
                    ],
                }
            )
        # 项目管理质量评价
        if (
            "standard_class5_list" in team_standard_class_list
            and team_standard_class_list["standard_class5_list"]
        ):
            team_standard_class_list["standard_class5_list"]
            team_eq_standard_list.append(
                {
                    "team_name": team_standard_class_list["standard_class5_list"][0][
                        "team_name"
                    ],
                    "category_eq_class_name": team_standard_class_list[
                        "standard_class5_list"
                    ][0]["category_eq_class_name"].split("(")[0],
                    "standard_list": [
                        *team_standard_class_list["standard_class5_list"]
                    ],
                }
            )
        # 监理服务质量评价
        if (
            "standard_class6_list" in team_standard_class_list
            and team_standard_class_list["standard_class6_list"]
        ):
            team_standard_class_list["standard_class6_list"]
            team_eq_standard_list.append(
                {
                    "team_name": team_standard_class_list["standard_class6_list"][0][
                        "team_name"
                    ],
                    "category_eq_class_name": team_standard_class_list[
                        "standard_class6_list"
                    ][0]["category_eq_class_name"].split("(")[0],
                    "standard_list": [
                        *team_standard_class_list["standard_class6_list"]
                    ],
                }
            )
        # 第三方测试质量评价
        if (
            "standard_class7_list" in team_standard_class_list
            and team_standard_class_list["standard_class7_list"]
        ):
            team_standard_class_list["standard_class7_list"]
            team_eq_standard_list.append(
                {
                    "team_name": team_standard_class_list["standard_class7_list"][0][
                        "team_name"
                    ],
                    "category_eq_class_name": team_standard_class_list[
                        "standard_class7_list"
                    ][0]["category_eq_class_name"].split("(")[0],
                    "standard_list": [
                        *team_standard_class_list["standard_class7_list"]
                    ],
                }
            )
        return team_eq_standard_list


# 质量评估评价标准上传
class StandardUpload(AjaxTodoBase):
    def __init__(self):
        super(StandardUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取流程数据
        quality_evaluation_standard = process_data.get(
            "quality_evaluation_standard", None
        )
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 存流程变量
        variables = {"quality_evaluation_standard": quality_evaluation_standard}
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)


# 质量评估评价标准解析、存库
class StandardAnalysis(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def analysis(self, quality_evaluation_standard):
        # 获取url
        url = quality_evaluation_standard[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine="openpyxl")
        df = df.replace({np.nan: None})
        # 解析
        insert_standard_data = []
        for _, row in df.iterrows():
            team_name = row["团队名称"]
            category_eq_class_id = row["品类质量评价标准分类id"]
            category_eq_class_name = row["品类质量评价分类名称"]
            evaluation_dimensions = row["评估维度"]
            key_indicator = row["关键指标"]
            target = row["目标"]
            assessment_point = row["考核点"]
            scoring_rubrics = row["评分细则"]
            dimension_weight = row[
                "同分类标准、不同维度权重(不同维度在同标准分类的占比)"
            ]
            team_weight = row[
                "同分类标准、同维度、同关键指标、不同团队权重(不同团队在同分类标准、同纬度、同关键指标的占比)"
            ]
            insert_standard_data.append(
                {
                    "team_name": team_name,
                    "category_eq_class_id": category_eq_class_id,
                    "category_eq_class_name": category_eq_class_name,
                    "evaluation_dimensions": evaluation_dimensions,
                    "key_indicator": key_indicator,
                    "target": target,
                    "assessment_point": assessment_point,
                    "scoring_rubrics": scoring_rubrics,
                    "dimension_weight": dimension_weight,
                    "team_weight": team_weight,
                }
            )
        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        # 插入数据
        tb_db.insert_batch("quality_evaluation_standard", insert_standard_data)
        tb_db.commit()
        # 结束流程
        flow.complete_task(
            self.ctx.task_id,
        )
