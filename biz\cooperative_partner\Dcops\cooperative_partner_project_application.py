import ast
import datetime
import hashlib
import hmac
import json
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, config, curl


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers)
    result = resp.json()
    return result


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环
    return system_id, corp_id


class ProjectApplicationDcops(object):
    """
        项目报建
        腾讯方调用接口：输出数据(合作伙伴外部系统)
    """

    def __init__(self):
        super(ProjectApplicationDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_application_confirmation_of_completion_review(self, project_name, number_name, task_name,
                                                              examine_approve_result, dismiss_role, remark):
        """
            项目报建子项任务完工确认审核反馈
        """
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        role = 0
        if examine_approve_result == "同意":
            examine_approve = 1
        elif examine_approve_result == "驳回":
            examine_approve = 2

        if dismiss_role == "监理":
            role = 1
        elif dismiss_role == "项管":
            role = 2
        elif dismiss_role == "PM":
            role = 3
        elif not dismiss_role:
            role = 4

        project_application_confirmation_of_completion_review_data = {
            "ProjectName": project_name,  # 项目名称
            # 子项编号名称（2.4.1、2.4.2、2.4.3、2.4.4、2.4.5、2.4.6、2.4.7、2.4.8、2.4.9、2.4.10、2.4.11、2.4.12）
            "NumberName": number_name,
            # 子项任务名称（项目立项、设计合同签订上传、图纸送审、（监理直接发包、上传）、（工程直接发包、上传）、监理合同签订上传、
            # 工程合同签订上传、农民工保证金监管合同签订上传、工伤意外险、施工许可证下发、消防备案、竣工备案）
            "TaskName": task_name,
            "ExaminationApprovalConclusion": examine_approve,  # 审批结论（1、同意，2、驳回）
            "DismissRole": role,  # 驳回角色 1、监理；2、项管；3、PM；4、无
            "Remark": remark,  # 备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "scheme_review_feedback",  # (*必填) 云函数名称
                    "data": project_application_confirmation_of_completion_review_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)

        variables = {
            'project_application_confirmation_of_completion_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def fire_third_party_test_report_PM_review(self, project_name, PM_approval, PM_remark):
        """
            消防第三方检测报告PM审核反馈
        """
        system_id = self.ctx.variables.get("system_id")
        PartnerTicketId = self.ctx.variables.get("PartnerTicketId")
        examine_approve = 0
        if PM_approval == "同意":
            examine_approve = 1
        elif PM_approval == "驳回":
            examine_approve = 2

        if not PM_remark:
            PM_remark = '无'

        fire_third_party_test_report_PM_review_data = {
            "ProjectName": project_name,  # 项目名称
            "PmApproval": examine_approve,  # PM审批（1、同意，2、驳回）
            "PMRemark": PM_remark,  # PM备注
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        # 此节点再七彩石配置中的key
        interface_info = 'create_order'
        # 构造请求数据
        data = {
            "SystemId": "13",
            "Action": "Nbroker",
            "Method": "UrlProxy",
            "NbrokerData": {
                "context": {
                    "service_name": "TCForward",  # (*必填) 原样填写
                    "method_name": "index"  # (*必填) 原样填写
                },
                "args": {
                    "function_name": "scheme_review_feedback",  # (*必填) 云函数名称
                    "data": fire_third_party_test_report_PM_review_data,  # 传递给云函数的入参
                    "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                }
            },
            "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
        }
        info = calling_external_interfaces(interface_info, data)
        result_str = info['data']['result']

        # 将 result 字符串解析为字典
        result_dict = json.loads(result_str)

        # 获取 code 的值
        code = result_dict.get('code')
        message = result_dict.get('message')
        if code != 0:
            raise Exception(f"报错信息为： {message}")
        variables = {
            'fire_third_party_test_report_PM_review': str(info)
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitPartnerCallInterfaceXmbjDcops(object):
    """
        等待合作方调用接口的接口：查询是否传入数据
    """

    def __init__(self):
        super(WaitPartnerCallInterfaceXmbjDcops, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_fire_third_party_test_report_uploaded(self):
        """
            等待消防第三方检测报告上传
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT fire_test_report " \
                   "FROM construction_application_second_level " \
                   f"WHERE ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' "
        word_list = db.get_all(word_sql)
        xf_word_table = []
        fire_test_report_table = []
        for row in word_list:
            fire_test_report = row.get('fire_test_report', '')
            fire_test_report = fire_test_report.replace("'", '"')
            fire_test_report = json.loads(fire_test_report)
            if fire_test_report is not None:
                for doc in fire_test_report:
                    if doc is not None and "response" in doc and "FileList" in doc["response"]:
                        file_list = doc["response"]["FileList"]
                        if len(file_list) > 0 and "url" in file_list[0]:
                            url = file_list[0]["url"]
                            name = file_list[0]['name']
                            fire_test_report_table.append({
                                "fire_test_report": url,
                                'fire_test_report_name': name
                            })

            xf_word_table.append({
                "wait_fire_third_party_test_report_uploaded": word_list,
                "fire_test_report_table": fire_test_report_table,
            })
        self.workflow_detail.add_kv_table('消防第三方检测报告上传数据', {'message': word_list})
        if word_list:
            variables = {
                'xf_word_table': xf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_xmlx_completion_confirmation_data_submission(self):
        """
            等待项目立项完工确认数据提交
        """
        appendix_table = []
        xmbj_dict = {}
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND (serial_number = '2.4' OR serial_number = '2.4.1')"
        word_list = db.get_all(word_sql)
        xmlx_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            # 根据 serial_number 选择需要的字段
            if serial_number == '2.4':
                xmbj_work_content = row.get('work_content', '')
                xmbj_start_time = row.get('start_time', '')
                xmbj_completion_time = row.get('completion_time', '')
                xmbj_responsible_person = row.get('responsible_person', '')

                xmbj_dict = {
                    'work_content': xmbj_work_content,
                    'start_time': xmbj_start_time,
                    'finish_time': xmbj_completion_time,
                    'responsible_person': xmbj_responsible_person,
                }

            if serial_number == '2.4.1':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                xmlx_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "construction_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'xmbj_dict': xmbj_dict,
                'xmlx_word_table': xmlx_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sjht_completion_confirmation_data_submission(self):
        """
            等待设计合同签订上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.2'"
        word_list = db.get_all(word_sql)
        sjht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.2':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })
                sjht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sjht_word_table': sjht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_tzss_completion_confirmation_data_submission(self):
        """
            等待图纸送审完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.3'"
        word_list = db.get_all(word_sql)
        tzss_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.3':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                tzss_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'tzss_word_table': tzss_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jlzf_completion_confirmation_data_submission(self):
        """
            等待监理直接发包、上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.4'"
        word_list = db.get_all(word_sql)
        jlzf_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.4':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                jlzf_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jlzf_word_table': jlzf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gczf_completion_confirmation_data_submission(self):
        """
            等待工程直接发包、上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.5'"
        word_list = db.get_all(word_sql)
        gczf_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.5':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })
                gczf_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gczf_word_table': gczf_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jlht_completion_confirmation_data_submission(self):
        """
            等待监理合同签订上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.6'"
        word_list = db.get_all(word_sql)
        jlht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.6':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                jlht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jlht_word_table': jlht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gcht_completion_confirmation_data_submission(self):
        """
            等待工程合同签订上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.7'"
        word_list = db.get_all(word_sql)
        gcht_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.7':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                gcht_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gcht_word_table': gcht_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_nmght_completion_confirmation_data_submission(self):
        """
            等待农民工保证金监管合同签订上传完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.8'"
        word_list = db.get_all(word_sql)
        nmght_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.8':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                nmght_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'nmght_word_table': nmght_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_gsyw_completion_confirmation_data_submission(self):
        """
            等待工伤意外险完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.9'"
        word_list = db.get_all(word_sql)
        gsyw_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.9':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                gsyw_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'gsyw_word_table': gsyw_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_sgxk_completion_confirmation_data_submission(self):
        """
            等待施工许可证下发完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.10'"
        word_list = db.get_all(word_sql)
        sgxk_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.10':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                sgxk_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'sgxk_word_table': sgxk_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_xfba_completion_confirmation_data_submission(self):
        """
            等待消防备案完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.11'"
        word_list = db.get_all(word_sql)
        xfba_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.11':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })
                xfba_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'xfba_word_table': xfba_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}

    def wait_jgba_completion_confirmation_data_submission(self):
        """
            等待竣工备案完工确认数据提交
        """
        appendix_table = []
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        db = mysql.new_mysql_instance("tbconstruct")
        word_sql = "SELECT serial_number, work_content, start_time, completion_time, actual_start_time," \
                   "responsible_person, actual_finish_time, schedule_skewing_explain, appendix " \
                   "FROM excel_data " \
                   f"WHERE dcops_ticket_id = '{ticket_id}' " \
                   f"AND project_name = '{project_name}' " \
                   "AND now_time = " \
                   "(SELECT MAX(now_time) " \
                   "FROM excel_data " \
                   f"WHERE project_name = '{project_name}')" \
                   "AND serial_number = '2.4.12'"
        word_list = db.get_all(word_sql)
        jgba_word_table = []
        for row in word_list:
            serial_number = row.get('serial_number', '')

            if serial_number == '2.4.12':
                work_content = row.get('work_content', '')
                start_time = row.get('start_time', '')
                completion_time = row.get('completion_time', '')
                actual_start_time = row.get('actual_start_time', '')
                actual_finish_time = row.get('actual_finish_time', '')
                schedule_skewing_explain = row.get('schedule_skewing_explain', '')
                appendix = row.get('appendix', '')
                appendix = appendix.replace("'", '"')
                appendix = json.loads(appendix)

                if appendix is not None:
                    for doc in appendix:
                        if doc is not None and "response" in doc and "FileList" in doc["response"]:
                            file_list = doc["response"]["FileList"]
                            if len(file_list) > 0 and "url" in file_list[0]:
                                url = file_list[0]["url"]
                                name = file_list[0]['name']
                                appendix_table.append({
                                    "appendix": url,
                                    'appendix_name': name
                                })

                jgba_word_table.append({
                    "serial_number": serial_number,
                    "work_content": work_content,
                    "start_time": start_time,
                    "completion_time": completion_time,
                    "actual_start_time": actual_start_time,
                    "actual_finish_time": actual_finish_time,
                    "schedule_skewing_explain": schedule_skewing_explain,
                    "appendix": appendix_table
                })
        self.workflow_detail.add_kv_table('完工确认数据', {'message': word_list})
        if word_list:
            variables = {
                'jgba_word_table': jgba_word_table,
                'word_list': word_list
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
            return {"success": True, "data": "流程已结束"}
