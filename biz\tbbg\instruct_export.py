# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: instruct_export.py
# @time: 2023/6/25 9:38
# @描述: 【请添加描述】
import os
import shutil
import openpyxl
from iBroker.lib import mysql
from iBroker.lib.sdk import tof
from iBroker.logone import logger

if os.environ.get('PythonDeployFlag', 'dev') == 'dev':
    from cos_lib import COSLib
else:
    from biz.tbbg.cos_lib import COSLib


class InstructDataExport:

    def change_type(self, type_str: str):
        if type_str == "JC":
            return "集成"
        elif type_str == "XG":
            return "项管"
        elif type_str == "JL":
            return "监理"
        elif type_str == "CS":
            return "测试"

    def send_email(self, to_user: list, title: str, content: str, file: str, file_name: str):
        print(file)
        tof.send_attachment_email({file: file_name}, title, content, to_user)

    def export_and_upload(self, requirement_id: int, to_users: list, hand_time: str, target_time: str,
                          instruct_change_content: str,
                          other_content: str):

        # 查询数据
        tb_db = mysql.new_mysql_instance("tb_change")
        query_sql = """
        select
  tb_erp_project.ProjectName,
  requir_instruct.Type,
  requirement.ChangeTitle,
  stars_order.StarsId,
  stars_order.Supplier,
  requir_instruct.TimeStr,
  tb_erp_project.CampusAbbr,
  tb_erp_project.ProjectStageAbbr,
  requir_instruct.Seq
from
  requir_instruct
  left join requirement on requir_instruct.RequirementId = requirement.Id
  left join tb_erp_project on requir_instruct.ProjectId = tb_erp_project.Id
  left join stars_order on stars_order.RequirementId = requirement.Id
where
  requirement.Id = %s
        """ % requirement_id

        instruct_list = tb_db.get_all(query_sql)

        ret_list = []

        # 遍历instruct_list
        for instruct in instruct_list:
            instructTypeStr = instruct["Type"]
            instruct["Type"] = self.change_type(instruct["Type"])

            # 设置标题
            title_str = instruct["ProjectName"] + instruct["Type"] + "变更指令"

            # 加载模板文件
            template_file = os.path.dirname(os.path.abspath(__file__)) + "/data/instruct_export.xlsx"

            path = "excel_export_cache/barcode/"
            if not os.path.exists(path):
                os.makedirs(path)
                os.chmod(path, 0o777)
            # 复制模板
            shutil.copyfile(template_file, f'./excel_export_cache/barcode/{title_str}.xlsx')
            work_book = openpyxl.load_workbook(f'./excel_export_cache/barcode/{title_str}.xlsx')
            template_sheet = work_book.worksheets[0]

            target = work_book.copy_worksheet(template_sheet)

            # 标题数据
            target['B2'] = title_str
            # 变更单号
            target['C3'] = instruct["TimeStr"] + instruct["CampusAbbr"] + instruct[
                "ProjectStageAbbr"] + instructTypeStr + instruct["Seq"]
            # 系统单号
            target['F3'] = instruct["StarsId"]
            # 指令发出时间
            target['C4'] = hand_time
            # 变更名称
            target['F4'] = instruct["ChangeTitle"]
            # 执行单位
            target['C5'] = instruct["Supplier"]
            # 要求交付时间
            target['F5'] = target_time
            # 变更内容
            target['C6'] = instruct_change_content
            # 其他内容
            target['C7'] = other_content

            work_book.remove(template_sheet)
            work_book.save(f'./excel_export_cache/barcode/{title_str}.xlsx')

            # 上传cos
            cos_relative_url = COSLib.uploadFile(f'./excel_export_cache/barcode/{title_str}.xlsx')

            ret_list.append({"filename": title_str + ".xlsx", "cosurl": cos_relative_url})
            # 发送邮件
            mail_content = "<html><body><div>" + title_str + "，请查看附件</div></body></html>"

            # 使用logger.info打印send_email的参数
            logger.info("send_email参数：to_users:%s, title_str:%s, mail_content:%s, file_path:%s, file_name:%s", to_users,
                        title_str, mail_content, f'./excel_export_cache/barcode/{title_str}.xlsx', title_str + ".xlsx")

            # # 获取文件的绝对路径
            # file_path = os.path.dirname(os.path.abspath(__file__)) + f'/excel_export_cache/barcode/{title_str}.xlsx'
            self.send_email(to_users, title_str, mail_content, f'./excel_export_cache/barcode/{title_str}.xlsx',
                            title_str + ".xlsx")
            # 删除目录
            shutil.rmtree(path)
        return ret_list


if __name__ == '__main__':
    InstructDataExport().export_and_upload(159, ["<EMAIL>"], "2023-06-25", "2023-06-25",
                                           "instruct_change_content", "other_content")
