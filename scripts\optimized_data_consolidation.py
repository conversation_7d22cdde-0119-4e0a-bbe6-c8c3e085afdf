#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的数据合并脚本 - 包含时间处理和设备名称映射
使用标准的pymysql库连接数据库
"""

import pymysql
import logging
from datetime import datetime
import time
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据库配置 - 需要根据实际情况修改
DB_CONFIG = {
    'host': 'localhost',  # 修改为实际的数据库主机
    'user': 'root',       # 修改为实际的用户名
    'password': '',       # 修改为实际的密码
    'database': 'tbconstruct',
    'charset': 'utf8mb4'
}

class OptimizedDataConsolidator:
    def __init__(self):
        self.start_time = time.time()

        # 设备名称映射字典
        self.device_name_mapping = {
            'HVDC': '弹性一体柜(HVDC)',
            '弹性一体柜(HVDC)': 'HVDC',
            'T-Box': 'T-Box',
            '交换机': '交换机',
            '传感器': '传感器'
        }

    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**DB_CONFIG)

    def execute_query(self, sql, params=None):
        """执行查询并返回结果"""
        connection = self.get_db_connection()
        try:
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute(sql, params)
            result = cursor.fetchall()
            return result
        finally:
            connection.close()

    def execute_update(self, sql, params=None):
        """执行更新操作"""
        connection = self.get_db_connection()
        try:
            cursor = connection.cursor()
            cursor.execute(sql, params)
            connection.commit()
            return cursor.rowcount
        finally:
            connection.close()

    def parse_time_range(self, time_str):
        """解析时间范围字符串，返回开始时间和结束时间"""
        if not time_str or time_str == 'NULL':
            return None, None

        try:
            # 处理时间范围格式：2024-07-07~2024-07-15
            if '~' in str(time_str):
                start_str, end_str = str(time_str).split('~')
                start_date = datetime.strptime(start_str.strip(), '%Y-%m-%d')
                end_date = datetime.strptime(end_str.strip(), '%Y-%m-%d')
                return start_date, end_date
            else:
                # 单个日期格式
                single_date = datetime.strptime(str(time_str).strip(), '%Y-%m-%d')
                return single_date, single_date
        except Exception as e:
            logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
            return None, None

    def calculate_delivery_gap(self, expected_time, estimated_time):
        """计算交付时间差"""
        try:
            if not expected_time or not estimated_time:
                return None

            # 解析预期时间和预计时间
            exp_start, exp_end = self.parse_time_range(expected_time)
            est_start, est_end = self.parse_time_range(estimated_time)

            if exp_end and est_end:
                # 计算天数差异
                gap = (est_end - exp_end).days
                return gap
            return None
        except Exception as e:
            logger.warning(f"计算交付时间差失败: {e}")
            return None

    def format_completion_time(self, completion_time):
        """格式化完成时间，只返回日期部分"""
        if not completion_time:
            return None
        try:
            if isinstance(completion_time, str):
                # 如果是字符串，尝试解析
                dt = datetime.strptime(completion_time.split(' ')[0], '%Y-%m-%d')
                return dt.date()
            elif hasattr(completion_time, 'date'):
                # 如果是datetime对象，返回日期部分
                return completion_time.date()
            return completion_time
        except Exception as e:
            logger.warning(f"格式化完成时间失败: {completion_time}, 错误: {e}")
            return None

    def get_mapped_device_name(self, device_name):
        """获取映射后的设备名称"""
        if not device_name:
            return device_name
        return self.device_name_mapping.get(device_name, device_name)

    def log_update_status(self, table_name, record_count, status='success', message=''):
        """记录更新状态，用于页面显示"""
        try:
            self.execute_update("""
                INSERT INTO consolidated_data_update_log (table_name, record_count, status, message)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                    update_time = CURRENT_TIMESTAMP,
                    record_count = VALUES(record_count),
                    status = VALUES(status),
                    message = VALUES(message)
            """, (table_name, record_count, status, message))
        except Exception as e:
            logger.warning(f"记录更新状态失败: {e}")

    def create_tables(self):
        """创建必要的表"""
        logger.info("🏗️ 创建数据表...")

        # 创建合并表
        create_main_table = """
        CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
            state VARCHAR(50) DEFAULT NULL,
            TicketId VARCHAR(100) NOT NULL,
            Title VARCHAR(255) DEFAULT NULL,
            SchemeNameCn VARCHAR(255) DEFAULT NULL,
            IsForceEnd VARCHAR(255) DEFAULT '0',
            project_name VARCHAR(255) DEFAULT NULL,
            device_name VARCHAR(255) DEFAULT NULL,
            supplier VARCHAR(255) DEFAULT NULL,
            device_type VARCHAR(100) DEFAULT NULL,
            equipment_sla VARCHAR(50) DEFAULT NULL,
            expected_time_equipment DATETIME DEFAULT NULL,
            estimated_time_delivery DATETIME DEFAULT NULL,
            delivery_gap INT DEFAULT NULL,
            completion_time DATETIME DEFAULT NULL,
            po_create_time DATETIME DEFAULT NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (TicketId),
            INDEX idx_project_name (project_name),
            INDEX idx_state (state),
            INDEX idx_device_type (device_type),
            INDEX idx_last_updated (last_updated)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """

        # 创建更新日志表
        create_log_table = """
        CREATE TABLE IF NOT EXISTS consolidated_data_update_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            table_name VARCHAR(100) NOT NULL,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            record_count INT DEFAULT 0,
            status VARCHAR(50) DEFAULT 'success',
            message TEXT,
            INDEX idx_table_name (table_name),
            INDEX idx_update_time (update_time)
        )
        """

        try:
            self.execute_update(create_main_table)
            self.execute_update(create_log_table)
            logger.info("✅ 数据表创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ 创建数据表失败: {e}")
            return False

    def fetch_base_tickets(self):
        """第1步：获取基础工单数据（最快的查询）"""
        logger.info("📋 第1步：获取基础工单数据...")

        query = """
        SELECT TicketId, Title, SchemeNameCn, IsForceEnd
        FROM all_construction_ticket_data 
        WHERE SchemeNameCn LIKE '%设备生产%'
            AND IsForceEnd = 0
            AND TicketId IS NOT NULL 
            AND TicketId != ''
        ORDER BY TicketId
        LIMIT 2000
        """

        try:
            results = self.execute_query(query)
            logger.info(f"✅ 获取基础工单: {len(results)} 条")
            return results
        except Exception as e:
            logger.error(f"❌ 获取基础工单失败: {e}")
            return []

    def fetch_equipment_process_data(self, ticket_ids):
        """第2步：获取设备生产流程数据"""
        logger.info("🔧 第2步：获取设备生产流程数据...")

        if not ticket_ids:
            return {}

        # 分批查询，避免IN子句过长
        batch_size = 100
        all_results = {}

        for i in range(0, len(ticket_ids), batch_size):
            batch_ids = ticket_ids[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_ids))

            query = f"""
            SELECT ticket_id, project_name, device_name, equipment_type
            FROM equipment_production_racking_process 
            WHERE ticket_id IN ({placeholders})
                AND equipment_type = '甲供'
            """

            try:
                results = self.execute_query(query, batch_ids)
                for row in results:
                    all_results[row['ticket_id']] = row
            except Exception as e:
                logger.warning(f"批次查询失败: {e}")
                continue

        logger.info(f"✅ 获取设备流程数据: {len(all_results)} 条")
        return all_results

    def fetch_project_states(self, project_names):
        """第3步：获取项目状态数据"""
        logger.info("📊 第3步：获取项目状态数据...")

        if not project_names:
            return {}

        # 去重并过滤空值
        unique_projects = list(set([p for p in project_names if p]))
        if not unique_projects:
            return {}

        batch_size = 50
        all_states = {}

        for i in range(0, len(unique_projects), batch_size):
            batch_projects = unique_projects[i:i + batch_size]

            # 使用LIKE查询匹配项目名称
            conditions = []
            params = []
            for project in batch_projects:
                conditions.append("CONCAT(campus, project_name) LIKE %s")
                params.append(f"%{project}%")

            if conditions:
                query = f"""
                SELECT DISTINCT CONCAT(campus, project_name) as full_name, state
                FROM summary_data 
                WHERE ({' OR '.join(conditions)})
                    AND state IS NOT NULL 
                    AND state != ''
                """

                try:
                    results = self.execute_query(query, params)
                    for row in results:
                        # 匹配最相似的项目名
                        full_name = row['full_name']
                        for project in batch_projects:
                            if project in full_name or full_name in project:
                                all_states[project] = row['state']
                                break
                except Exception as e:
                    logger.warning(f"获取项目状态失败: {e}")
                    continue

        logger.info(f"✅ 获取项目状态: {len(all_states)} 条")
        return all_states

    def fetch_additional_data(self, project_names):
        """第4步：获取补充数据（供应商、设备类型等）"""
        logger.info("📦 第4步：获取补充数据...")

        if not project_names:
            return {}

        unique_projects = list(set([p for p in project_names if p]))
        batch_size = 30
        all_additional = {}

        for i in range(0, len(unique_projects), batch_size):
            batch_projects = unique_projects[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_projects))

            query = f"""
            SELECT project_name, supplier, device_type, equipment_sla,
                   expected_time_equipment, estimated_time_delivery, delivery_gap
            FROM delivery_schedule_management_table
            WHERE project_name IN ({placeholders})
            LIMIT 200
            """

            try:
                results = self.execute_query(query, batch_projects)
                for row in results:
                    project = row['project_name']
                    if project not in all_additional:
                        all_additional[project] = []
                    all_additional[project].append(row)
            except Exception as e:
                logger.warning(f"获取补充数据失败: {e}")
                continue

        logger.info(f"✅ 获取补充数据: {len(all_additional)} 个项目")
        return all_additional

    def fetch_equipment_sla_data(self, device_names):
        """第5步：获取设备SLA数据"""
        logger.info("🔧 第5步：获取设备SLA数据...")

        if not device_names:
            return {}

        unique_devices = list(set([d for d in device_names if d]))
        if not unique_devices:
            return {}

        batch_size = 50
        all_sla = {}

        for i in range(0, len(unique_devices), batch_size):
            batch_devices = unique_devices[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_devices))

            query = f"""
            SELECT device_name, equipment_sla
            FROM equipment_category_mapping_relationship
            WHERE device_name IN ({placeholders})
            """

            try:
                results = self.execute_query(query, batch_devices)
                for row in results:
                    all_sla[row['device_name']] = row['equipment_sla']
            except Exception as e:
                logger.warning(f"获取设备SLA数据失败: {e}")
                continue

        logger.info(f"✅ 获取设备SLA数据: {len(all_sla)} 条")
        return all_sla

    def fetch_completion_time_data(self, project_names):
        """第6步：获取完成时间数据"""
        logger.info("⏰ 第6步：获取完成时间数据...")

        if not project_names:
            return {}

        unique_projects = list(set([p for p in project_names if p]))
        batch_size = 30
        all_completion = {}

        for i in range(0, len(unique_projects), batch_size):
            batch_projects = unique_projects[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_projects))

            query = f"""
            SELECT project_name, work_content, completion_time
            FROM construct_plan_data
            WHERE project_name IN ({placeholders})
                AND completion_time IS NOT NULL
            """

            try:
                results = self.execute_query(query, batch_projects)
                for row in results:
                    project = row['project_name']
                    if project not in all_completion:
                        all_completion[project] = row['completion_time']
            except Exception as e:
                logger.warning(f"获取完成时间数据失败: {e}")
                continue

        logger.info(f"✅ 获取完成时间数据: {len(all_completion)} 条")
        return all_completion

    def fetch_po_create_time_data(self, project_names):
        """第7步：获取PO创建时间数据"""
        logger.info("💰 第7步：获取PO创建时间数据...")

        if not project_names:
            return {}

        unique_projects = list(set([p for p in project_names if p]))
        batch_size = 30
        all_po_time = {}

        for i in range(0, len(unique_projects), batch_size):
            batch_projects = unique_projects[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_projects))

            query = f"""
            SELECT project_name, po_create_time
            FROM payment_info
            WHERE project_name IN ({placeholders})
                AND po_create_time IS NOT NULL
            """

            try:
                results = self.execute_query(query, batch_projects)
                for row in results:
                    all_po_time[row['project_name']] = row['po_create_time']
            except Exception as e:
                logger.warning(f"获取PO创建时间数据失败: {e}")
                continue

        logger.info(f"✅ 获取PO创建时间数据: {len(all_po_time)} 条")
        return all_po_time

    def consolidate_and_insert(self):
        """主要的数据合并流程"""
        logger.info("🚀 开始优化的数据合并流程...")

        # 清空目标表
        try:
            self.execute_update("DELETE FROM consolidated_equipment_data")
            logger.info("🗑️ 清空目标表成功")
        except Exception as e:
            logger.error(f"清空目标表失败: {e}")
            return False

        # 第1步：获取基础工单
        base_tickets = self.fetch_base_tickets()
        if not base_tickets:
            logger.error("❌ 无基础工单数据")
            return False

        ticket_ids = [row['TicketId'] for row in base_tickets]

        # 第2步：获取设备流程数据
        equipment_data = self.fetch_equipment_process_data(ticket_ids)

        # 第3步：获取项目状态
        project_names = [equipment_data[tid]['project_name']
                        for tid in equipment_data if equipment_data[tid].get('project_name')]
        project_states = self.fetch_project_states(project_names)

        # 第4步：获取补充数据
        additional_data = self.fetch_additional_data(project_names)

        # 第5步：获取设备SLA数据
        device_names = [equipment_data[tid]['device_name']
                       for tid in equipment_data if equipment_data[tid].get('device_name')]
        sla_data = self.fetch_equipment_sla_data(device_names)

        # 第6步：获取完成时间数据
        completion_data = self.fetch_completion_time_data(project_names)

        # 第7步：获取PO创建时间数据
        po_time_data = self.fetch_po_create_time_data(project_names)

        # 第8步：组装并插入数据
        logger.info("🔧 第8步：组装并插入数据...")
        insert_count = 0

        for ticket_row in base_tickets:
            ticket_id = ticket_row['TicketId']

            # 获取设备流程信息
            equipment_info = equipment_data.get(ticket_id, {})
            project_name = equipment_info.get('project_name', '')

            # 获取项目状态
            state = project_states.get(project_name, '未知状态')

            # 获取补充信息
            additional_info = additional_data.get(project_name, [{}])[0] if additional_data.get(project_name) else {}

            # 获取设备名称和SLA信息
            device_name = equipment_info.get('device_name', '')
            # 尝试使用映射后的设备名称查找SLA
            mapped_device_name = self.get_mapped_device_name(device_name)
            equipment_sla = sla_data.get(device_name, sla_data.get(mapped_device_name, additional_info.get('equipment_sla', '')))

            # 获取完成时间和PO创建时间
            completion_time = completion_data.get(project_name)
            po_create_time = po_time_data.get(project_name)

            # 格式化完成时间（只保留日期部分）
            formatted_completion_time = self.format_completion_time(completion_time)

            # 处理时间范围和计算交付时间差
            expected_time = additional_info.get('expected_time_equipment')
            estimated_time = additional_info.get('estimated_time_delivery')

            # 重新计算交付时间差
            calculated_delivery_gap = self.calculate_delivery_gap(expected_time, estimated_time)

            # 如果原始数据中的delivery_gap为0或NULL，使用计算出的值
            original_gap = additional_info.get('delivery_gap')
            final_delivery_gap = calculated_delivery_gap if (original_gap is None or original_gap == 0) else original_gap

            # 插入数据
            try:
                insert_sql = """
                INSERT IGNORE INTO consolidated_equipment_data (
                    state, TicketId, Title, SchemeNameCn, IsForceEnd,
                    project_name, device_name, supplier, device_type, equipment_sla,
                    expected_time_equipment, estimated_time_delivery, delivery_gap,
                    completion_time, po_create_time
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                self.execute_update(insert_sql, (
                    state,
                    ticket_id,
                    ticket_row.get('Title', ''),
                    ticket_row.get('SchemeNameCn', ''),
                    ticket_row.get('IsForceEnd', '0'),
                    project_name,
                    device_name,
                    additional_info.get('supplier', '待确认'),
                    additional_info.get('device_type', ''),
                    equipment_sla,
                    expected_time,  # 保持原始时间范围格式
                    estimated_time,  # 保持原始时间范围格式
                    final_delivery_gap,  # 使用重新计算的交付时间差
                    formatted_completion_time,  # 使用格式化后的完成时间（只有日期）
                    po_create_time
                ))
                insert_count += 1

            except Exception as e:
                logger.warning(f"插入记录失败 {ticket_id}: {e}")
                continue

        # 记录更新状态
        elapsed_time = time.time() - self.start_time
        message = f"数据合并完成，耗时 {elapsed_time:.2f} 秒"
        self.log_update_status('consolidated_equipment_data', insert_count, 'success', message)

        logger.info(f"✅ 数据合并完成！插入 {insert_count} 条记录，耗时 {elapsed_time:.2f} 秒")
        return True

def main():
    """主函数"""
    logger.info("🚀 启动优化的数据合并程序")

    consolidator = OptimizedDataConsolidator()

    # 创建表
    if not consolidator.create_tables():
        return False

    # 执行数据合并
    if not consolidator.consolidate_and_insert():
        return False

    logger.info("🎉 优化的数据合并完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
