from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql, curl
import datetime


class BusinessPaymentAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # 获取项目信息、po信息、付款信息
    def get_project_info(self):
        """
        获取项目信息、po信息、付款信息
        """
        # 获取项目信息
        db = mysql.new_mysql_instance("tbconstruct")
        sql = "SELECT project_name FROM risk_early_warning_data"
        project_info_list = db.query(sql)
        self.workflow_detail.add_kv_table("1.项目信息", {"message": project_info_list})
        # 主表信息insert
        insert_data_dict_main = {}
        # 遍历项目
        for i in range(len(project_info_list)):
            item = project_info_list[i]
            project_name = item.get("project_name")
            if project_name:
                po_code_list = []
                # 获取po信息
                po_material_info = self.get_po_info(project_name)
                # self.workflow_detail.add_kv_table(
                #     f"2.{i+1}.1{project_name}最终po信息", {"message": po_material_info}
                # )
                # 获取po单号
                # 全部po
                po_code_list = []
                material_list = []
                if po_material_info:
                    for po in po_material_info:
                        po_code = po.get("orderCode")
                        if po_code:
                            po_code_list.append(po_code)
                            material_id = po.get("materialId")
                            po_supplier= po.get("venderName")
                            # 查询对应po的状态是否有数据，有则无需再查询
                            payment_info_sql = (
                                f"SELECT * FROM payment_info WHERE project_name = '{project_name}' "
                                f"AND po_code = '{po_code}'"
                            )
                            material_name=""
                            # 通过物料id获取品名信息
                            material_list = gnetops.request(action="Tbconstruct",
                                                            method="ObtainMaterialInfoById",
                                                            ext_data={
                                                                "MaterialCodeId": [material_id]
                                                            }
                                                            )
                            if material_list:
                                material_info = material_list.get("materialList")
                                if material_info:
                                    if material_info[0].get("materialQueryDTO"):
                                        material_name = material_info[0].get(
                                            "materialQueryDTO"
                                        ).get( "materialCategoryName"),
                            project_payment_info_list = db.query(payment_info_sql)
                            po_create_time = po.get("poFinishTime")
                            if not project_payment_info_list:
                                # 主表先写入一条数据（主表无数据时先写入一条数据，后续只需更新数据）
                                insert_list_main = self.payment_info_insert(
                                    project_name=project_name, po_code=po_code,
                                    material_name=material_name,supplier=po_supplier,
                                    po_create_time=po_create_time,
                                )
                                # self.workflow_detail.add_kv_table(
                                #     f"2.{i+1}.2{project_name}主表付款信息insert",
                                #     {"message": po_material_info},
                                # )
                                item["insert_list_main"] = insert_list_main
                                insert_data_dict_main[project_name] = insert_list_main
                    item["po_code_list"] = po_code_list
                    # self.workflow_detail.add_kv_table(
                    #     f"2.{i+1}.3{project_name}po单号(全)", {"message": po_code_list}
                    # )
                # 获取付款信息
                # 已到货
                arrived_payment_info = self.get_payment_info(po_code_list, "已到货")
                # 已安装
                installed_payment_info = self.get_payment_info(po_code_list, "已安装")
                # 已初验
                initial_inspection_payment_info = self.get_payment_info(
                    po_code_list, "已初验"
                )
                # 已终验
                final_inspection_payment_info = self.get_payment_info(
                    po_code_list, "已终验"
                )
                # 人员已入场
                people_arrived_payment_info = self.get_payment_info(
                    po_code_list, "人员已入场"
                )
                # 图纸已送审时间
                drawings_submitted_payment_info = self.get_payment_info(
                    po_code_list, "图纸已送审"
                )
                payment_info_dict = {
                    "已到货": arrived_payment_info,
                    "已安装": installed_payment_info,
                    "已初验": initial_inspection_payment_info,
                    "已终验": final_inspection_payment_info,
                    "人员已入场": people_arrived_payment_info,
                    "图纸已送审": drawings_submitted_payment_info,
                }
                item["payment_info_dict"] = payment_info_dict
                # self.workflow_detail.add_kv_table(
                #     f"2.{i+1}.5{project_name}商务付款信息",
                #     {"message": payment_info_dict},
                # )
        self.workflow_detail.add_kv_table(
            f"3{project_name}主表付款信息insert(全)",
            {"message": insert_data_dict_main},
        )
        variables = {
            "project_info_list": project_info_list,
            "insert_data_dict_main": insert_data_dict_main,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 子表付款信息insert(自动任务)
    def payment_info_into_db(self, project_info_list):
        """
        子表付款信息insert
        """
        # 已到货
        arrived_insert_list = []
        # 已安装
        installed_insert_list = []
        # 已初验
        initial_inspection_insert_list = []
        # 已终验
        final_inspection_insert_list = []
        # 人员已入场
        people_arrived_insert_list = []
        # 图纸已送审
        drawings_submitted_insert_list = []
        for i in range(len(project_info_list)):
            item = project_info_list[i]
            project_name = item.get("project_name")
            # po_code_dict = item.get("po_code_dict")
            payment_info_dict = item.get("payment_info_dict")
            if payment_info_dict.get("已到货"):
                temp1 = self.get_payment_info_child_insert_list(
                    "已到货",
                    project_name,
                    payment_info_dict.get("已到货").get("orderPayments"),
                )
                arrived_insert_list = [*arrived_insert_list, *temp1]
            if payment_info_dict.get("已安装"):
                temp2 = self.get_payment_info_child_insert_list(
                    "已安装",
                    project_name,
                    payment_info_dict.get("已安装").get("orderPayments"),
                )
                installed_insert_list = [*installed_insert_list, *temp2]
            if payment_info_dict.get("已初验"):
                temp3 = self.get_payment_info_child_insert_list(
                    "已初验",
                    project_name,
                    payment_info_dict.get("已初验").get("orderPayments"),
                )
                initial_inspection_insert_list = [
                    *initial_inspection_insert_list,
                    *temp3,
                ]
            if payment_info_dict.get("已终验"):
                temp4 = self.get_payment_info_child_insert_list(
                    "已终验",
                    project_name,
                    payment_info_dict.get("已终验").get("orderPayments"),
                )
                final_inspection_insert_list = [*final_inspection_insert_list, *temp4]
            if payment_info_dict.get("人员已入场"):
                temp5 = self.get_payment_info_child_insert_list(
                    "人员已入场",
                    project_name,
                    payment_info_dict.get("人员已入场").get("orderPayments"),
                )
                people_arrived_insert_list = [*people_arrived_insert_list, *temp5]
            if payment_info_dict.get("图纸已送审"):
                temp6 = self.get_payment_info_child_insert_list(
                    "图纸已送审",
                    project_name,
                    payment_info_dict.get("图纸已送审").get("orderPayments"),
                )
                drawings_submitted_insert_list = [
                    *drawings_submitted_insert_list,
                    *temp6,
                ]
            insert_data_dict_child_project = {
                "已到货": temp1,
                "已安装": temp2,
                "已初验": temp3,
                "已终验": temp4,
                "人员已入场": temp5,
                "图纸已送审": temp6,
            }
            # self.workflow_detail.add_kv_table(
            #     f"{i+1}{project_name}子表付款信息",
            #     {"message": insert_data_dict_child_project},
            # )
            item["insert_data_dict_child_project"] = insert_data_dict_child_project

        insert_data_dict_child = {
            "已到货": arrived_insert_list,
            "已安装": installed_insert_list,
            "已初验": initial_inspection_insert_list,
            "已终验": final_inspection_insert_list,
            "人员已入场": people_arrived_insert_list,
            "图纸已送审": drawings_submitted_insert_list,
        }
        self.workflow_detail.add_kv_table(
            f"子表付款信息(全)",
            {"message": insert_data_dict_child},
        )
        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if arrived_insert_list:
            tb_db.insert_batch("payment_info_arrived", arrived_insert_list)
        if installed_insert_list:
            tb_db.insert_batch("payment_info_installed", installed_insert_list)
        if initial_inspection_insert_list:
            tb_db.insert_batch(
                "payment_info_initial_inspection", initial_inspection_insert_list
            )
        if final_inspection_insert_list:
            tb_db.insert_batch(
                "payment_info_final_inspection", final_inspection_insert_list
            )
        if people_arrived_insert_list:
            tb_db.insert_batch(
                "payment_info_people_arrived", people_arrived_insert_list
            )
        if drawings_submitted_insert_list:
            tb_db.insert_batch(
                "payment_info_drawings_submitted", drawings_submitted_insert_list
            )
        tb_db.commit()
        variables = {
            "insert_data_dict_child": insert_data_dict_child,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 获取po单号信息
    def get_po_info(self, project_name):
        # 获取需求单号
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        code_id_list = db.query(sql)
        # self.workflow_detail.add_kv_table("1.获取需求id", {"message": code_id_list})
        if code_id_list:
            # po信息列表
            po_material_info = []
            # 需求单号
            for i in code_id_list:
                is_test = self.ctx.variables.get("is_test")
                if is_test:
                    # 测试环境 模拟数据处理
                    po_info = self.ctx.variables.get("po_info")
                else:
                    code_id = i.get("idcrmOrderCode")
                    # 用需求单号获取po信息
                    resp = curl.get(
                        title="Dcops获取po信息",
                        system_name="Dcops",
                        url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/queryOrderByFormCode/{code_id}",
                        postdata="",
                        append_header={"Content-Type": "application/json"},
                    )
                    po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
            # 去重po单号
            final_po_material_info = []
            po_code_list = []
            for po in po_material_info:
                if po.get("orderCode") not in po_code_list:
                    po_code_list.append(po.get("orderCode"))
                    final_po_material_info.append(po)
            return final_po_material_info

    # 获取商务付款信息
    def get_payment_info(self, po_code_list, status):
        """
        获取商务付款信息
        """
        if po_code_list and status:
            is_test = self.ctx.variables.get("is_test")
            if is_test:
                info = self.ctx.variables.get("payment_info")
            else:
                info = gnetops.request(
                    action="Tbconstruct",
                    method="ObtainPaymentInfo",
                    ext_data={
                        "OrderIdList": po_code_list,
                        "Status": status,
                    },
                    scheme="ifob-infrastructure",
                )
            return info
        else:
            return False

    # 获取子表付款信息insert数据
    def get_payment_info_child_insert_list(
        self, status, project_name, payment_info_list
    ):
        """
        子表付款信息
        """
        status_db_map = {
            "已到货": "payment_info_arrived",
            "已安装": "payment_info_installed",
            "已初验": "payment_info_initial_inspection",
            "已终验": "payment_info_final_inspection",
            "人员已入场": "payment_info_people_arrived",
            "图纸已送审": "payment_info_drawings_submitted",
        }
        insert_list = []
        for item in payment_info_list:
            for info in item.get("payments"):
                # 查询付款信息是否有变化
                orderId = item.get("orderId")
                paymentNo = info.get("paymentNo")
                sql = (
                    f"SELECT * FROM {status_db_map[status]} WHERE "
                    f"projectName = '{project_name}' "
                    f"AND poCode = '{orderId}' "
                    f"AND paymentNo = '{paymentNo}'"
                )
                db = mysql.new_mysql_instance("tbconstruct")
                project_info_list = db.query(sql)
                # 有数据 判断状态是否一致（之前的"支付完成"可能会变为"已撤回"）
                if project_info_list:
                    # 不一致需要更新状态
                    if project_info_list[0].get("finStatusName") != info.get(
                        "finStatusName"
                    ):
                        tb_db = mysql.new_mysql_instance("tbconstruct")
                        id = project_info_list[0].get("id")
                        update_data = {
                            "finStatusName": info.get("finStatusName"),
                            "payTime": info.get("payTime"),
                        }
                        tb_db.begin()
                        tb_db.update_by_id(status_db_map[status], update_data, id)
                        tb_db.commit()
                # 无数据 支付状态"支付完成"才需存数据库
                elif info.get("finStatusName") == "支付完成":
                    insert_list.append(
                        {
                            "projectName": project_name,
                            "ticketId": self.ctx.variables.get("ticket_id"),
                            "poCode": item.get("orderId"),
                            "paymentNo": info.get("paymentNo"),
                            "ownerName": info.get("ownerName"),
                            "departmentId": info.get("departmentId"),
                            "departmentName": info.get("departmentName"),
                            "vendorId": info.get("vendorId"),
                            "vendorName": info.get("vendorName"),
                            "bizStatusCode": info.get("bizStatusCode"),
                            "bizStatusName": info.get("bizStatusName"),
                            "finStatusCode": info.get("finStatusCode"),
                            "finStatusName": info.get("finStatusName"),
                            "payTime": info.get("payTime"),
                            "planPayTime": info.get("planPayTime"),
                            "paymentType": info.get("paymentType"),
                            "createTime": info.get("createTime"),
                            # "paymentSceneCode": info.get("paymentSceneCode"),
                            # "paymentSceneName": info.get("paymentSceneName"),
                            # "paymentSubSceneCode": info.get("paymentSubSceneCode"),
                            # "paymentSubSceneName": info.get("paymentSubSceneName"),
                            # "id": info.get("paymentNo"),
                            # "purchaseId": info.get("paymentNo"),
                            # "orderId": info.get("paymentNo"),
                            # "createByName": info.get("paymentNo"),
                            # "createBy": info.get("paymentNo"),
                            # "businessNo": info.get("paymentNo"),
                        }
                    )
        return insert_list

    # 主表付款信息insert
    def payment_info_insert(self, project_name, po_code,material_name: str='',supplier:str ='',po_create_time:str=''):
        """
        主表付款信息insert
        """
        insert_list = []
        insert_list.append(
            {
                "project_name": project_name,
                "po_code": po_code,
                "material_name": material_name,
                "supplier": supplier,
                "po_create_time": po_create_time,
                "arrived_time": None,
                "installed_time": None,
                "initial_inspection_time": None,
                "final_inspection_time": None,
                "people_arrived_time": None,
                "drawings_submitted_time": None,
            }
        )
        # 连接数据库
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        if insert_list:
            tb_db.insert_batch("payment_info", insert_list)
        tb_db.commit()
        return insert_list

    # 主表付款信息update(自动任务)
    def payment_info_update(self, project_info_list):
        """
        主表付款信息update
        """
        update_data_dict = {}
        for i in range(len(project_info_list)):
            item = project_info_list[i]
            project_name = item.get("project_name")
            payment_info_dict = item.get("payment_info_dict")
            po_code_payment_dict = {}
            if payment_info_dict.get("已到货"):
                orderPayments = payment_info_dict.get("已到货").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code]["arrived_time"] = (
                                        date.strftime("%Y-%m-%d %H:%M:%S")
                                    )
                                else:
                                    po_code_payment_dict[code][
                                        "arrived_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code]["arrived_time"] = None
            if payment_info_dict.get("已安装"):
                orderPayments = payment_info_dict.get("已安装").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code]["installed_time"] = (
                                        date.strftime("%Y-%m-%d %H:%M:%S")
                                    )
                                else:
                                    po_code_payment_dict[code][
                                        "installed_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code]["installed_time"] = None
            if payment_info_dict.get("已初验"):
                orderPayments = payment_info_dict.get("已初验").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code][
                                        "initial_inspection_time"
                                    ] = date.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    po_code_payment_dict[code][
                                        "initial_inspection_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code][
                                    "initial_inspection_time"
                                ] = None
            if payment_info_dict.get("已终验"):
                orderPayments = payment_info_dict.get("已终验").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code][
                                        "final_inspection_time"
                                    ] = date.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    po_code_payment_dict[code][
                                        "final_inspection_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code][
                                    "final_inspection_time"
                                ] = None
            if payment_info_dict.get("人员已入场"):
                orderPayments = payment_info_dict.get("人员已入场").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code][
                                        "people_arrived_time"
                                    ] = date.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    po_code_payment_dict[code][
                                        "people_arrived_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code]["people_arrived_time"] = None
            if payment_info_dict.get("图纸已送审"):
                orderPayments = payment_info_dict.get("图纸已送审").get("orderPayments")
                if orderPayments:
                    for info in orderPayments:
                        code = info.get("orderId")
                        if code not in po_code_payment_dict:
                            po_code_payment_dict[code] = {}
                        payments = info.get("payments")
                        if payments:
                            # 以最后一条付款信息为准
                            payments_info = payments[len(payments) - 1]
                            # "支付完成"取"支付完成"的时间
                            if payments_info.get("finStatusName") == "支付完成":
                                pay_time = payments_info.get("payTime")
                                if pay_time:
                                    # 时间戳转日期
                                    timestamp = int(pay_time) / 1000
                                    date = datetime.datetime.fromtimestamp(timestamp)
                                    po_code_payment_dict[code][
                                        "drawings_submitted_time"
                                    ] = date.strftime("%Y-%m-%d %H:%M:%S")
                                else:
                                    po_code_payment_dict[code][
                                        "drawings_submitted_time"
                                    ] = pay_time
                            # 其余状态置空
                            else:
                                po_code_payment_dict[code][
                                    "drawings_submitted_time"
                                ] = None
            item["po_code_payment_dict"] = po_code_payment_dict
            # self.workflow_detail.add_kv_table(
            #     f"{i+1}.1{project_name}主表付款信息", {"message": po_code_payment_dict}
            # )
            update_data_dict[project_name] = po_code_payment_dict
            tb_db = mysql.new_mysql_instance("tbconstruct")
            for code, time_dict in po_code_payment_dict.items():
                if time_dict:
                    update_data = time_dict
                    conditions = {"project_name": project_name, "po_code": code}
                    tb_db.begin()
                    tb_db.update("payment_info", update_data, conditions)
                    tb_db.commit()
        self.workflow_detail.add_kv_table(
            f"主表付款信息(全)", {"message": update_data_dict}
        )
        variables = {
            "update_data_dict": update_data_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 获取项目列表
    def get_project_list(self):
        """
        获取项目列表
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT t1.project_name FROM risk_early_warning_data t1 "
            "JOIN summary_data t2 "
            "WHERE CONCAT(t2.campus, t2.project_name) = t1.project_name "
            "AND t2.state = '建设中' AND t1.construction_mode = '自建'"
        )
        project_list = db.query(sql)
        variables = {
            "project_list": project_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    # 更新供应商资源信息（供应商资源表、suymmay_data集成商 供应商份额分配时间）
    def update_supplier_resources_info(self, project_list):
        """
        更新供应商资源信息
        供应商资源表、供应商份额分配时间（只更新自建建设中项目）
        """
        # # 获取项目信息
        db = mysql.new_mysql_instance("tbconstruct")
        # sql = (
        #     "SELECT t1.project_name FROM risk_early_warning_data t1 "
        #     "JOIN summary_data t2 "
        #     "WHERE CONCAT(t2.campus, t2.project_name) = t1.project_name "
        #     "AND t2.state = '建设中' AND t1.construction_mode = '自建'"
        # )
        # project_info_list = db.query(sql)
        # 品类-表 字段映射
        field_mapping = {
            "集成商": "integrators",
            "项管": "project_manager",
            "监理": "supervision",
            "第三方测试": "examination",
            "AHU方仓": "AHU",
            "柴发方仓": "chaifa",
            "低压柜": "low_voltage_cabinet",
            "中压柜": "medium_voltage_cabinet",
            "蓄电池": "battery",
            "机柜": "cabinet",
            "变压器": "transformer",
            "PDU": "PDU",
            # '高压直流 &列头柜': 'headboard',
            "HVDC": "headboard",
            "综合配电柜": "headboard",
            "弹性一体柜(HVDC)": "headboard",
            "T-Box、交换机、传感器等": "tbox",
            # # 集成商po下发时间
            # '供应商份额分配时间': 'supplier_share_allocation_time',
        }
        # 项目-品类-供应商
        project_category_supplier_dict = {}
        # 项目-集成商信息（供应商份额分配时间、集成商）
        project_jcs_dict = {}
        # error
        error_list = []
        # 获取项目-品类-供应商信息
        for item in project_list:
            project_name = item.get("project_name")
            if project_name:
                info_sql = (
                    "SELECT t1.project_name, t1.po_create_time, t1.material_name, t1.supplier, "
                    "t2.device_name, t3.js_supplier_name "
                    "FROM payment_info t1 "
                    "LEFT JOIN equipment_category_mapping_relationship t2 "
                    "ON t2.material_category_name = t1.material_name "
                    "LEFT JOIN supplier_name_mapping t3 ON t3.sw_supplier_name = t1.supplier "
                    f"WHERE project_name = '{project_name}'"
                )
                info_list = db.query(info_sql)
                update_data = {}
                project_jcs = {
                    "po_time": None,
                    "jcs": None,
                }
                for info in info_list:
                    po_create_time = info.get("po_create_time")
                    category_name = info.get("device_name")
                    js_supplier_name = info.get("js_supplier_name")
                    if po_create_time:
                        if js_supplier_name:
                            if category_name and category_name in field_mapping:
                                update_data[field_mapping.get(category_name)] = (
                                    js_supplier_name
                                )
                            if category_name == "集成商":
                                if project_jcs.get("po_time"):
                                    exit_time_temp = datetime.datetime.strptime(
                                        project_jcs.get("po_time"), "%Y-%m-%d %H:%M:%S"
                                    )
                                    po_time_temp = datetime.datetime.strptime(
                                        info.get("po_create_time"), "%Y-%m-%d %H:%M:%S"
                                    )
                                    min_time_temp = min(exit_time_temp, po_time_temp)
                                    min_time = min_time_temp.strftime(
                                        "%Y-%m-%d %H:%M:%S"
                                    )
                                    project_jcs["po_time"] = min_time
                                    project_jcs["jcs"] = js_supplier_name
                                else:
                                    project_jcs["po_time"] = info.get("po_create_time")
                                    project_jcs["jcs"] = js_supplier_name
                        else:
                            if info.get("supplier") not in error_list:
                                error_list.append(info.get("supplier"))
                project_category_supplier_dict[project_name] = update_data
                project_jcs_dict[project_name] = project_jcs
        if error_list:
            raise Exception(f"未获取到供应商名称映射：{('; ').join(error_list)}")
        # 更新供应商资源表
        for project_name, category_supplier in project_category_supplier_dict.items():
            if category_supplier:
                query_sql = f"SELECT id FROM supplier_resources WHERE CONCAT(campus, project) = '{project_name}'"
                result = db.get_row(query_sql)
                if result:
                    id = result.get("id")
                    update_data = category_supplier
                    db.update_by_id("supplier_resources", update_data, id)
        # 更新summary_data 集成单位: Integration_unit 供应商份额分配时间: supplier_share_allocation_time
        for project_name, jcs_info in project_jcs_dict.items():
            if jcs_info.get("jcs"):
                query_sql = (
                    "SELECT sn, supplier_share_allocation_time "
                    f"FROM summary_data WHERE CONCAT(campus, project_name) = '{project_name}'"
                )
                result = db.get_row(query_sql)
                if result:
                    id = result.get("sn")
                    exit_time = result.get("supplier_share_allocation_time")
                    po_time = jcs_info.get("po_time")
                    jcs = jcs_info.get("jcs")
                    min_time = po_time
                    if exit_time:
                        exit_time_temp = datetime.datetime.strptime(
                            exit_time, "%Y-%m-%d %H:%M:%S"
                        )
                        po_time_temp = datetime.datetime.strptime(
                            po_time, "%Y-%m-%d %H:%M:%S"
                        )
                        min_time_temp = min(exit_time_temp, po_time_temp)
                        min_time = min_time_temp.strftime("%Y-%m-%d %H:%M:%S")
                    update_data = {
                        "supplier_share_allocation_time": min_time,
                        "Integration_unit": jcs,
                    }
                    db.update_by_id("summary_data", update_data, id)
                    project_jcs_dict[project_name]["exit_time"] = exit_time
                    project_jcs_dict[project_name]["min_time"] = min_time
        db.commit()
        variables = {
            "project_category_supplier_dict": project_category_supplier_dict,
            "project_jcs_dict": project_jcs_dict,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
