from venv import logger

from iBroker.lib import config, mysql
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.workflow.workflow import WorkflowVarUpdate
from biz.project_daily_report.tools import Tools
from biz.utils.change_url import get_accout_by_role

# 字段对应中文映射
report_key_dict = {
    "project_name": "项目名称",
    "integrator": "集成商",
    "project_manager": "项管",
    "supervision": "监理",
    "report_date": "日期",
    "week": "星期",
    "weather_morning": "上午天气",
    "weather_afternoon": "下午天气",
    "max_temp": "温度最高",
    "min_temp": "温度最低",
    "humidity": "湿度",
    "wind_direction": "风向",
    "wind_level": "风级",
    "manager_num": "管理人员",
    "on_site_workers_num": "现场作业人员",
    "quality_situation": "质量情况",
    "safety_situation": "安全情况",
    "other_situation": "其它情况",
    "quality_problem": "质量问题",
    "safety_problem": "安全问题",
    "data_problem": "资料问题",
    "other_problem": "其它问题",
    "other_related_matters": "其他有关事项",
    "material_situation_table": "材料进场情况",
    "device_situation_table": "设备进场情况",
    "construction_situation_table": "施工情况表格",
}
# 材料进场情况
material_situation_table_key = [
    "material_name",
    "brand",
    "specification",
    "quantity",
    "unit",
]
# 设备进场情况
device_situation_table_key = [
    "device_name",
    "brand",
    "specification",
    "quantity",
    "unit",
    "type",
]
# 施工情况表格
construction_situation_table_key = [
    "zone",
    "sub_item",
    # "specific_content",
    "person_num",
    "progress",
]


# 日报监理填写
class ProjectReportSupervisionWrite(AjaxTodoBase):
    def __init__(self):
        super(ProjectReportSupervisionWrite, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        project_name = self.ctx.variables.get("project_name")
        accout = get_accout_by_role("监理-总监理工程师",project_name)
        variables = {
            "accout": accout,
        }
        WorkflowVarUpdate(
            instance_id=self.ctx.instance_id,
            variables={
                "supervision_account": accout}
        ).call()
        self.transfer(accout)

    def end(self, process_data, process_user):
        report_data = process_data.get("dailyReportDataUpdate")
        for key in report_key_dict.keys():
            if key == "on_site_workers_num" or key == "construction_situation_table":
                continue
            if key == "material_situation_table":
                if report_data.get(key):
                    if not all(
                        all(i_key in element for i_key in material_situation_table_key)
                        for element in report_data.get(key)
                    ) or not all(
                        all(
                            j_key in element and element[j_key]
                            for j_key in material_situation_table_key
                        )
                        for element in report_data.get(key)
                    ):
                        return {
                            "code": -1,
                            "msg": f"请完善材料进场清单，如无信息请清空表格",
                        }
                else:
                    continue
            if key == "device_situation_table":
                if report_data.get(key):
                    if not all(
                        all(i_key in element for i_key in device_situation_table_key)
                        for element in report_data.get(key)
                    ) or not all(
                        all(
                            j_key in element and element[j_key]
                            for j_key in device_situation_table_key
                        )
                        for element in report_data.get(key)
                    ):
                        return {
                            "code": -1,
                            "msg": f"请完善设备进场清单，如无信息请清空表格",
                        }
                else:
                    continue
            if key not in report_data or not report_data.get(key):
                return {"code": -1, "msg": f"请填写{report_key_dict[key]}"}
        pictures_submit = process_data.get("pictures_submit")
        dailyReportData = self.ctx.variables.get("dailyReportData")
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        dailyReport = {**report_data, **dailyReportData}
        variables = {
            "dailyReportData": dailyReport,
            "dailyReportDataUpdate": dailyReport,
            "pictures_submit": pictures_submit,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 日报项管填写
class ProjectManagerWrite(AjaxTodoBase):
    def __init__(self):
        super(ProjectManagerWrite, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        construction_situation_table = process_data.get("construction_situation_table")
        if construction_situation_table:
            flag, msg = Tools.validate_today_construction(
                construction_situation_table,
                self.ctx.variables.get("overall_control_plan_options"),
            )
            if not flag:
                return {
                    "code": -1,
                    "msg": msg,
                }
        dailyReportData = self.ctx.variables.get("dailyReportData")
        construction_situation_remark = process_data.get(
            "construction_situation_remark"
        )
        on_site_workers_num = 0
        for data in construction_situation_table:
            if "person_num" in data:
                on_site_workers_num += int(data["person_num"])
        dailyReportData["construction_situation_table"] = construction_situation_table
        dailyReportData["on_site_workers_num"] = str(on_site_workers_num)
        dailyReportData["construction_situation_remark"] = construction_situation_remark
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "dailyReportData": dailyReportData,
            "construction_situation_table": construction_situation_table,
            "construction_situation_remark": construction_situation_remark,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


# 日报项管确认
class ProjectManagerConfirm(AjaxTodoBase):
    def __init__(self):
        super(ProjectManagerConfirm, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        report_data = process_data.get("dailyReportDataUpdate")
        for key in report_key_dict.keys():
            if key == "material_situation_table":
                if report_data.get(key):
                    if not all(
                        all(i_key in element for i_key in material_situation_table_key)
                        for element in report_data.get(key)
                    ) or not all(
                        all(
                            j_key in element and element[j_key]
                            for j_key in material_situation_table_key
                        )
                        for element in report_data.get(key)
                    ):
                        return {
                            "code": -1,
                            "msg": f"请完善材料进场清单，如无信息请清空表格",
                        }
                else:
                    continue
            if key == "device_situation_table":
                if report_data.get(key):
                    if not all(
                        all(i_key in element for i_key in device_situation_table_key)
                        for element in report_data.get(key)
                    ) or not all(
                        all(
                            j_key in element and element[j_key]
                            for j_key in device_situation_table_key
                        )
                        for element in report_data.get(key)
                    ):
                        return {
                            "code": -1,
                            "msg": f"请完善设备进场清单，如无信息请清空表格",
                        }
                else:
                    continue
            if key == "construction_situation_table":
                if report_data.get(key):
                    flag, msg = Tools.validate_today_construction(
                        report_data.get(key),
                        self.ctx.variables.get("overall_control_plan_options"),
                    )
                    if not flag:
                        return {
                            "code": -1,
                            "msg": msg,
                        }
                else:
                    continue
            if key not in report_data or not report_data.get(key):
                return {"code": -1, "msg": f"请填写{report_key_dict[key]}"}
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "isReadonly": True,
            "dailyReportData": report_data,
            "dailyReportDataUpdate": report_data,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
