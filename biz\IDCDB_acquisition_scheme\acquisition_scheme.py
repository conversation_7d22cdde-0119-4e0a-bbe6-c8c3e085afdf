from iBroker.lib.sdk import flow
from iBroker.lib.sdk import idcdb
from iBroker.lib import common
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


class OperationsManagementInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            运管输入
        """
        yg_data = []
        project_name = process_data.get('project_name')
        whether_cloud_services_included = process_data.get('whether_cloud_services_included')
        resource_manager = process_data.get('resource_manager')
        head_of_network_assets = process_data.get('head_of_network_assets')
        yg_data.append({
            'project_name': project_name,
            'whether_cloud_services_included': whether_cloud_services_included,
            'resource_manager': resource_manager,
            'head_of_network_assets': head_of_network_assets
        })

        variables = {
            "yg_data": yg_data,
            "project_name": project_name,
            "whether_cloud_services_included": whether_cloud_services_included,
            "resource_manager": resource_manager,
            "head_of_network_assets": head_of_network_assets
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BusinessInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            商务输入
        """
        sw_data = []
        SP_number = process_data.get('SP_number')
        rack_charging_mode = process_data.get('rack_charging_mode')
        machine_room_operator = process_data.get('machine_room_operator')
        sw_data.append({
            'SP_number': SP_number,
            'rack_charging_mode': rack_charging_mode,
            'machine_room_operator': machine_room_operator
        })

        variables = {
            "sw_data": sw_data,
            "SP_number": SP_number,
            "rack_charging_mode": rack_charging_mode,
            "machine_room_operator": machine_room_operator
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DataPassageInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            数经输入
        """
        sj_data = []
        head_of_infrastructure = process_data.get('head_of_infrastructure')
        person_in_charge = process_data.get('person_in_charge')
        machine_room_manager = process_data.get('machine_room_manager')
        sj_data.append({
            'head_of_infrastructure': head_of_infrastructure,
            'person_in_charge': person_in_charge,
            'machine_room_manager': machine_room_manager
        })

        variables = {
            "sj_data": sj_data,
            "head_of_infrastructure": head_of_infrastructure,
            "person_in_charge": person_in_charge,
            "machine_room_manager": machine_room_manager
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class NiansiInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            niansi输入
        """
        niansi_data = []
        physical_campus_attribute = process_data.get('physical_campus_attribute')
        property_unit = process_data.get('property_unit')
        cooperative_operator_building = process_data.get('cooperative_operator_building')
        cooperative_operator_room = process_data.get('cooperative_operator_room')
        cooperative_carrier_telephone = process_data.get('cooperative_carrier_telephone')
        carrier_fax = process_data.get('carrier_fax')
        carrier_standby_fax = process_data.get('carrier_standby_fax')
        carrier_customer_service_email = process_data.get('carrier_customer_service_email')
        carrier_authorization_mode = process_data.get('carrier_authorization_mode')
        carrier_room_name = process_data.get('carrier_room_name')
        niansi_data.append({
            'physical_campus_attribute': physical_campus_attribute,
            'property_unit': property_unit,
            'cooperative_operator_building': cooperative_operator_building,
            'cooperative_operator_room': cooperative_operator_room,
            'cooperative_carrier_telephone': cooperative_carrier_telephone,
            'carrier_fax': carrier_fax,
            'carrier_standby_fax': carrier_standby_fax,
            'carrier_customer_service_email': carrier_customer_service_email,
            'carrier_authorization_mode': carrier_authorization_mode,
            'carrier_room_name': carrier_room_name
        })

        variables = {
            "niansi_data": niansi_data,
            "physical_campus_attribute": physical_campus_attribute,
            "property_unit": property_unit,
            "cooperative_operator_building": cooperative_operator_building,
            "cooperative_operator_room": cooperative_operator_room,
            "cooperative_carrier_telephone": cooperative_carrier_telephone,
            "carrier_fax": carrier_fax,
            "carrier_standby_fax": carrier_standby_fax,
            "carrier_customer_service_email": carrier_customer_service_email,
            "carrier_authorization_mode": carrier_authorization_mode,
            "carrier_room_name": carrier_room_name
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class LayhuangInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            layhuang输入
        """
        layhuang_data = []
        IT_operation_maintenance = process_data.get('IT_operation_maintenance')
        IT_person_in_charge = process_data.get('IT_person_in_charge')
        layhuang_data.append({
            'IT_operation_maintenance': IT_operation_maintenance,
            'IT_person_in_charge': IT_person_in_charge
        })

        variables = {
            "layhuang_data": layhuang_data,
            "IT_operation_maintenance": IT_operation_maintenance,
            "IT_person_in_charge": IT_person_in_charge
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class JackxzhangInput(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
            jackxzhang输入
        """
        jackxzhang_data = []
        infrastructure_operation_and_maintenance_party = process_data.get(
            'infrastructure_operation_and_maintenance_party')
        jackxzhang_data.append({
            'infrastructure_operation_and_maintenance_party': infrastructure_operation_and_maintenance_party
        })

        variables = {
            "jackxzhang_data": jackxzhang_data,
            "infrastructure_operation_and_maintenance_party": infrastructure_operation_and_maintenance_party
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class IDCDBDataWriting(object):
    """
        IDCDB数据写入
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def campus_writing(self):
        """
            物理园区数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        campus_name = self.ctx.variables.get('campus_name')
        physical_campus_attribute = self.ctx.variables.get('physical_campus_attribute')

        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "campus"

        campus_info = idcdb.get_dictionary(table, ["campus_id", "campus_name"], {"campus_name": [campus_name]})
        if common.empty(campus_info):
            print("填写错误,找不到{}的园区".format(campus_name))
            return

        campus_data = {
            # 物理园区属性
            'campus_attr': physical_campus_attribute
        }

        err_message = idcdb.update_one(table, business, user, reason, campus_info["campus_id"], campus_data)
        if not common.empty(err_message):
            print("修改数据失败:{}".format(err_message))
            return

        print("修改{}成功，data:{}".format(campus_name, campus_data))

        variables = {"campus_data": campus_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def building_writing(self):
        """
            楼宇输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "building"
        property_unit = self.ctx.variables.get('property_unit')
        cooperative_operator_building = self.ctx.variables.get('cooperative_operator_building')

        building_data = {
            # 产权单位
            'building_propertyRightsCompany': property_unit,
            # 合作运营商（楼宇）
            'cmdbIdcOperation_id': cooperative_operator_building
        }

        insert_id, err_msg = idcdb.add_one(table, business, user, reason, building_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"building_data": building_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def mozu_writing(self):
        """
            模组输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "mozu"
        whether_cloud_services_included = self.ctx.variables.get('whether_cloud_services_included')
        head_of_infrastructure = self.ctx.variables.get('head_of_infrastructure')
        IT_operation_maintenance = self.ctx.variables.get('IT_operation_maintenance')
        IT_person_in_charge = self.ctx.variables.get('IT_person_in_charge')
        infrastructure_operation_and_maintenance_party = self.ctx.variables.get(
            'infrastructure_operation_and_maintenance_party')

        mozu_data = {
            # 是否包含云业务
            'mozu_isYunDataCenter': whether_cloud_services_included,
            # 基础设施负责人
            'mozu_infrastructurePrincipal': head_of_infrastructure,
            # IT运维维护方
            'ITmtc_id': IT_operation_maintenance,
            # IT负责人
            'mozu_ITPrincipal': IT_person_in_charge,
            # 基础设施运维方
            'mozu_infrastructureCompany': infrastructure_operation_and_maintenance_party
        }

        insert_id, err_msg = idcdb.add_one(table, business, user, reason, mozu_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"mozu_data": mozu_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def room_writing(self):
        """
           房间输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "room"
        cooperative_operator_room = self.ctx.variables.get('cooperative_operator_room')
        cooperative_carrier_telephone = self.ctx.variables.get('cooperative_carrier_telephone')
        carrier_fax = self.ctx.variables.get('carrier_fax')
        carrier_standby_fax = self.ctx.variables.get('carrier_standby_fax')
        carrier_customer_service_email = self.ctx.variables.get('carrier_customer_service_email')
        carrier_authorization_mode = self.ctx.variables.get('carrier_authorization_mode')
        carrier_room_name = self.ctx.variables.get('carrier_room_name')

        room_data = {
            # 合作运营商（房间）
            'cmdbIdcOperation_id': cooperative_operator_room,
            # 合作运营商电话
            'room_iopTel': cooperative_carrier_telephone,
            # 运营商传真
            'room_iopFax': carrier_fax,
            # 运营商备用传真
            'room_iopFaxBackup': carrier_standby_fax,
            # 运营商客服邮箱
            'room_iopSupportMail': carrier_customer_service_email,
            # 运营商授权方式
            'room_iopAuthMode': carrier_authorization_mode,
            # 运营商房间名称
            'room_iopName': carrier_room_name
        }
        insert_id, err_msg = idcdb.add_one(table, business, user, reason, room_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"room_data": room_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def bizArea_writing(self):
        """
            一级机房输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "bizArea"
        resource_manager = self.ctx.variables.get('resource_manager')
        machine_room_operator = self.ctx.variables.get('machine_room_operator')

        bizArea_data = {
            # 资源负责人
            'bizArea_operator': resource_manager,
            # 机房运营商
            'cmdbIdcOperation_id': machine_room_operator
        }

        insert_id, err_msg = idcdb.add_one(table, business, user, reason, bizArea_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"bizArea_data": bizArea_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def idcUnit_writing(self):
        """
            管理单元输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "idcUnit"
        head_of_network_assets = self.ctx.variables.get('head_of_network_assets')
        person_in_charge = self.ctx.variables.get('person_in_charge')
        machine_room_manager = self.ctx.variables.get('machine_room_manager')

        idcUnit_data = {
            # 网络资产负责人
            'idcUnit_netOperator': head_of_network_assets,
            # 责任人
            'idcUnit_idcOperator': person_in_charge,
            # 机房经理
            'idcUnit_idcManger': machine_room_manager
        }
        insert_id, err_msg = idcdb.add_one(table, business, user, reason, idcUnit_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"idcUnit_data": idcUnit_data}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def rack_writing(self):
        """
            机架输入数据写入IDCDB
        """
        project_name = self.ctx.variables.get('project_name')
        business = "cfgAutoFlow"  # 配置自动生成流程， 不能修改
        user = "v_mmywang"
        reason = f"{project_name}建设项目"

        table = "rack"
        SP_number = self.ctx.variables.get('SP_number')
        rack_charging_mode = self.ctx.variables.get('rack_charging_mode')

        rack_data = {
            # SP编号
            'rack_spCode': SP_number,
            # 机架计费模式
            'rack_billingMethod': rack_charging_mode
        }

        insert_id, err_msg = idcdb.add_one(table, business, user, reason, rack_data)
        if insert_id > 0:
            print("添加{}成功，ID:{}".format(table, insert_id))
        else:
            print("添加{}失败，err:{}".format(table, err_msg))

        variables = {"rack_data": rack_data}
        flow.complete_task(self.ctx.task_id, variables=variables)
