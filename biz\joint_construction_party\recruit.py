import datetime
import json
import re
import time
import uuid

import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, tof
from iBroker.lib.sdk import gnetops
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate

from biz.common.cycle_task_tools import ticket_is_over
from biz.common.workflow_tools import cycle_task_abort
from biz.construction_process.cos_lib import COSLib


class ApprovalPage(AjaxTodoBase):
    """
        编辑审批页面
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        table = process_data.get('table')
        remark = process_data.get('remark')
        project_name = process_data.get('project_name')
        project_desc = process_data.get('project_desc')
        leader = process_data.get('leader')
        center = process_data.get('center')
        gm = process_data.get('gm')
        file_name = None
        adjustment_part = None
        attachment_uploading_url = []
        attachment_uploading_table = []

        supplier_list = []
        supplier_table = process_data.get('supplier_table')
        for i in supplier_table:
            if not i.get('supplier_name'):
                return {"code": -1, "msg": "请检查供应商表格，供应商名称不能为空"}
            supplier_list.append(i.get('supplier_name'))
        if not supplier_list:
            return {"code": -1, "msg": "请填写供应商名称表格"}

        attachment_uploading = process_data.get("attachment_uploading", None)
        if attachment_uploading is not None:
            for doc in attachment_uploading:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        attachment_uploading_url.append(url)
                        attachment_uploading_table.append({
                            "attachment_uploading": url,
                            'attachment_uploading_name': name
                        })
        attachment_uploadingt_join = ', '.join(attachment_uploading_url)
        insert_list = []
        if table:
            for i in table:
                file_name = i.get('file_name')
                adjustment_part = i.get('adjustment_part')
                file = i.get('file')
                uploader = i.get('uploader')
                insert_data = {
                    'project_name': project_name,
                    'file_name': file_name,
                    'uploader': uploader,
                    'adjustment_part': adjustment_part,
                    'file': str(file),
                    'coverage_area': process_data.get('coverage_area'),
                    'supplier_list': ','.join(supplier_list),
                    'attachment_uploadingt': attachment_uploadingt_join,
                    'evaluation_weight': process_data.get('evaluation_weight'),
                    'ticket_id': ticket_id
                }
                insert_list.append(insert_data)
        if insert_list:
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("information_joint_builder", insert_list)
            tb_db.commit()
        next_op = 0
        GM_approval = ''
        if center == gm:
            next_op = 1
            GM_approval = "同意"
        variables = {
            'file_name': file_name,
            'adjustment_part': adjustment_part,
            'table': table,
            'remark': remark,
            'coverage_area': process_data.get('coverage_area'),
            'supplier_list': supplier_list,
            'attachment_uploading': attachment_uploading,
            'attachment_uploading_table': attachment_uploading_table,
            'evaluation_weight': process_data.get('evaluation_weight'),
            'project_desc': project_desc,
            'project_name': project_name,
            'next_op': next_op,
            'center': center,
            'leader': leader,
            'gm': gm,
            'GM_approval': GM_approval
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ApprovalPageSummary(AjaxTodoBase):
    """
        审批信息汇总 + 商务同事指定
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        ticket_id = self.ctx.variables.get("ticket_id")
        business_affairs = process_data.get('business_affairs')
        update_data = {
            'business_affairs': business_affairs
        }
        conditions = {
            'ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, conditions)
        tb_db.commit()

        variables = {
            'business_affairs': business_affairs
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AnswerQuestionsFileUploaded(AjaxTodoBase):
    """
        答疑文件上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        main_id = self.ctx.variables.get("main_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        question_answer_document_url = []
        question_answer_document_table = []
        question_answer_document = process_data.get("question_answer_document", None)
        dy_remark = process_data.get("dy_remark", None)
        if not question_answer_document:
            question_answer_document = process_data.get("question_answer_document_2", None)
        if not dy_remark:
            dy_remark = process_data.get("dy_remark_2", None)
        is_need = process_data.get("is_need", None)
        if question_answer_document is not None:
            for doc in question_answer_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        question_answer_document_url.append(url)
                        question_answer_document_table.append({
                            "question_answer_document": url,
                            'question_answer_document_name': name
                        })
        attachment_uploadingt_join = ', '.join(question_answer_document_url)

        update_data = {
            'question_answer_document': attachment_uploadingt_join,
        }
        conditions = {
            'ticket_id': main_id
        }
        insert_data = {
            'ticket_id': ticket_id,
            'question_answer_document': attachment_uploadingt_join,
            'project_name': project_name
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, conditions)
        tb_db.insert("information_joint_builder_faq", insert_data)
        tb_db.commit()
        feedback_time = ""
        if is_need == "否":
            # 获取当前日期
            current_date = datetime.date.today()
            # 格式化日期为字符串
            feedback_time = current_date.strftime("%Y-%m-%d")
        variables = {
            'question_answer_document_table': question_answer_document_table,
            'dy_remark': dy_remark,
            'question_answer_document': question_answer_document,
            'question_answer_document_url': question_answer_document_url,
            'is_need': is_need,
            'feedback_time': feedback_time
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BiddingProcessPlan(AjaxTodoBase):
    """
        商务上传计划
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name')
        project_desc = process_data.get('project_desc')
        tender_issuance_plan = process_data.get('tender_issuance_plan')
        faq_feedback_plan = process_data.get('faq_feedback_plan')
        faq_reply_plan = process_data.get('faq_reply_plan')
        bid_return_plan = process_data.get('bid_return_plan')
        tender_opening_plan = process_data.get('tender_opening_plan')
        ticket_id = self.ctx.variables.get('ticket_id')
        variables = {
            "ticket_id": ticket_id,
            "project_name": project_name,
            "project_desc": project_desc,
            "tender_issuance_plan": tender_issuance_plan,
            "faq_feedback_plan": faq_feedback_plan,
            "faq_reply_plan": faq_reply_plan,
            "bid_return_plan": bid_return_plan,
            "tender_opening_plan": tender_opening_plan
        }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("bidding_process_plan", variables)
        tb_db.commit()
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ClarifyAnswerQuestionsDocument(AjaxTodoBase):
    """
        商务上传澄清答疑文件
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader = process_data.get('leader')
        center = process_data.get('center')
        gm = process_data.get('gm')
        approval_email = process_data.get('approval_email', None)

        main_id = self.ctx.variables.get("main_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        clarification_document_url = []
        clarification_document_table = []
        clarification_document = process_data.get("clarification_document", None)
        cq_remark = process_data.get("cq_remark", None)
        if clarification_document is not None:
            for doc in clarification_document:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        clarification_document_url.append(url)
                        clarification_document_table.append({
                            "clarification_document": url,
                            'clarification_document_name': name
                        })

        approval_email_url = []
        approval_email_table = []
        if approval_email is not None:
            for i in approval_email:
                if i is not None and "response" in i and "FileList" in i["response"]:
                    file = i["response"]["FileList"]
                    if len(file) > 0 and "url" in file[0]:
                        url = file[0]["url"]
                        name = file[0]['name']
                        approval_email_url.append(url)
                        approval_email_table.append({
                            "approval_email": url,
                            'approval_email_name': name
                        })
        clarification_document_join = ','.join(clarification_document_url)

        update_data = {
            'clarification_document': clarification_document_join
        }
        conditions = {
            'ticket_id': main_id
        }
        con = {
            'ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, conditions)
        tb_db.update("information_joint_builder_faq", update_data, con)
        tb_db.commit()
        next_op = 0
        GM_approval = ''
        if center == gm:
            next_op = 1
            GM_approval = "同意"

        variables = {
            'clarification_document_table': clarification_document_table,
            'cq_remark': cq_remark,
            'clarification_document_url': clarification_document_url,
            'clarification_document': clarification_document,
            'next_op': next_op,
            'center': center,
            'leader': leader,
            'gm': gm,
            'approval_email': approval_email,
            "approval_email_url": approval_email_url,
            'approval_email_table': approval_email_table,
            'GM_approval': GM_approval
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BusinessConfirmation(AjaxTodoBase):
    """
        商务确认 + 时间
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_user, process_data):
        main_id = self.ctx.variables.get("main_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        feedback_time = process_data.get("feedback_time", None)

        update_data = {
            'feedback_time': feedback_time
        }
        conditions = {
            'ticket_id': main_id
        }
        con = {
            'ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, conditions)
        tb_db.update("information_joint_builder_faq", update_data, con)
        tb_db.commit()

        variables = {
            'feedback_time': feedback_time
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BusinessBidReturnCompleted(AjaxTodoBase):
    """
        商务：回标完成确认
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        main_id = self.ctx.variables.get("main_id")
        ticket_id = self.ctx.variables.get("ticket_id")
        back_remark = process_data.get("back_remark", None)
        return_time = process_data.get("return_time")
        back_label_file = process_data.get("back_label_file", [])
        url_list = []
        if back_label_file:
            for i in back_label_file:
                try:
                    url = i["response"]["FileList"][0]["url"]
                    url_list.append(url)
                except KeyError as e:
                    return {"code": -1, "msg": f"上传错误：{e}"}
                except IndexError as e:
                    return {"code": -1, "msg": f"上传错误：{e}"}
        back_label_file_url = ', '.join(url_list)
        update_data = {
            'back_label_file': back_label_file_url,
            'return_time': return_time,
        }
        conditions = {
            'ticket_id': ticket_id
        }
        con = {
            'ticket_id': main_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, con)
        tb_db.update("information_joint_builder_faq", update_data, conditions)
        tb_db.commit()
        variables = {
            'back_remark': back_remark,
            'return_time': return_time,
            'back_label_file': back_label_file,
            'back_url_list': url_list
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DesignatedBidEvaluation(AjaxTodoBase):
    """
        指定评标
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        main_id = self.ctx.variables.get("main_id")
        instance_id = self.ctx.instance_id
        project_name = self.ctx.variables.get('project_name', '')
        project_desc = self.ctx.variables.get('project_desc', '')
        tender_opening_plan = process_data.get('tender_opening_plan')
        reporting_time = process_data.get('reporting_time')
        clarify_completion_time = process_data.get('clarify_completion_time')
        scoring_and_approval = process_data.get('scoring_and_approval')
        team_table = process_data.get('team_table')
        member_dict = self.ctx.variables.get('member_dict', '')
        leader = process_data.get('leader')
        GM = process_data.get('GM', '')
        center = process_data.get('center', '')
        party_name_list = []
        personnel_demand_list = []
        team_member_dict = {}
        # personnel_demand_sum = ""
        if team_table:
            for row in team_table:
                if 'team' in row:
                    team_member_dict[row["team"]] = member_dict.get(row["team"])
                if 'party_name' in row:
                    party_name = row['party_name']
                    party_name_list.append(party_name)
                if 'personnel_demand' in row:
                    personnel_demand = row['personnel_demand']
                    personnel_demand_list.append(personnel_demand)
                    # personnel_demand_sum = str(personnel_demand_list)
        party_name_str = ', '.join(party_name_list)
        estimated_evaluation_time = json.dumps([{
            "clarify_completion_time": clarify_completion_time,
            "reporting_time": reporting_time,
            "scoring_and_approval": scoring_and_approval,
            "tender_opening_plan": tender_opening_plan
        }])

        insert_data = {
            'project_name': project_name,
            'project_desc': project_desc,
            'estimated_evaluation_time': estimated_evaluation_time,
            'party_name': party_name_str,
            'personnel_demand': str(personnel_demand_list),
            'dcops_ticket_id': ticket_id,
            'main_id': main_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("audit_basic_information", insert_data)
        tb_db.commit()
        variables = {
            'project_name': project_name,
            'project_desc': project_desc,
            'party_name': party_name_list,
            'dcops_ticket_id': ticket_id,
            'instance_id': instance_id,
            'team_table': team_table,
            'director_approval_standard': '',
            'director_remarks_standard': '',
            'leader': leader,
            'center': center,
            'GM': GM,
            'leader_approval': '',
            'leader_remarks': '',
            'GM_approval': '',
            'GM_remarks': '',
            "personnel_demand_list": personnel_demand_list,
            'team_member_dict': team_member_dict,
            'clarify_completion_time': clarify_completion_time,
            'reporting_time': reporting_time,
            'scoring_and_approval': scoring_and_approval,
            'tender_opening_plan': tender_opening_plan
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DesignatedBidEvaluationTeamLeader(AjaxTodoBase):
    """
        Team Leader 指定评标组长
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get('project_name')
        project_desc = self.ctx.variables.get('project_desc')
        # 组长
        bid_evaluation_team_leader = process_data.get('bid_evaluation_team_leader')
        GM = self.ctx.variables.get('GM', "")
        leader = self.ctx.variables.get('leader', "")
        center = self.ctx.variables.get('center', "")
        business_affairs = self.ctx.variables.get('business_affairs', "")
        try:
            f_instance_id = self.ctx.variables.get("f_instance_id")
            GM = flow.get_variables(f_instance_id, ["gm"])["gm"]
            center = flow.get_variables(f_instance_id, ["center"])["center"]
            business_affairs = flow.get_variables(f_instance_id, ["business_affairs"])["business_affairs"]
        except Exception:
            self.workflow_detail.add_kv_table("父流程查询GM错误信息", {"error": Exception})
        try:
            member_dict = self.ctx.variables.get('member_dict')
            found_match = False
            for values in member_dict.values():
                for i in values["member_list"]:
                    if bid_evaluation_team_leader == re.sub(r'\(.*?\)', '', i):
                        center = re.sub(r'\(.*?\)', '', values["center_leader"])
                        GM = "sagezou"
                        leader = "eddiewu"
                        found_match = True
                        break
                if found_match:
                    break
        except Exception:
            self.workflow_detail.add_kv_table("从人员列表获取GM错误信息", {"error": Exception})
        basic_info = {
            'project_desc': project_desc,
            'project_name': project_name,
            'bid_evaluation_team_leader': bid_evaluation_team_leader,
            'GM': GM,
            'center': center,
            'business_affairs': business_affairs
        }
        variables = {
            'project_desc': project_desc,
            'project_name': project_name,
            'bid_evaluation_team_leader': bid_evaluation_team_leader,
            'basic_info': basic_info,
            'leader': leader,
            'GM': GM,
            'center': center,
            'business_affairs': business_affairs
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class Notice(object):
    """
        通知leader指定评标人
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.ctx.eop_flag = 1

    def designatedbidder(self, staff_name, msg_content, msg_type):
        """
            推消息到个人
            :param staff_name: 英文名
            :param msg_content: 文本
            :param msg_type: text/markdown
            :return:
        """
        for row in staff_name:
            ChatOpsSend(
                user_name=row,
                msg_content=msg_content,
                msg_type=msg_type,
                des_type='single'
            ).call()

        WorkflowContinue(self.ctx.task_id).call()


def cycle_is_over(ticket_id_list: list, task_id):
    flag = ticket_is_over(ticket_id_list)  # flag为未结束的工单的数量
    if flag:
        return {"success": False, "data": "尚有子流程未结束"}
    else:
        WorkflowContinue(task_id=task_id).call()
        return {"success": True, "data": "所有子流程结束"}


class CircularOrderCreation(object):
    """
        循环建单，创建指定评标人和评标人确定工单
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def create_designated_bid_evaluator(self, people_info, flow_key):
        """
            创建指定评标人工单
        """
        time.sleep(3)
        all_personnel_list = []
        project_name = self.ctx.get_var('project_name', '')
        project_desc = self.ctx.get_var('project_desc', '')
        return_time = self.ctx.get_var('return_time', '')
        clarify_completion_time = self.ctx.variables.get("clarify_completion_time"),
        reporting_time = self.ctx.variables.get("reporting_time"),
        scoring_and_approval = self.ctx.variables.get("scoring_and_approval"),
        tender_opening_plan = self.ctx.variables.get("tender_opening_plan")

        bid_evaluation_team_leader = self.ctx.get_var('bid_evaluation_team_leader', '')
        team_table = self.ctx.get_var('team_table', '')
        party_name = self.ctx.get_var('party_name', '')
        team_member_dict = self.ctx.get_var('team_member_dict', '')
        party_name_str = ';'.join(party_name)
        people_info_len = len(people_info)
        f_instance_id = self.ctx.instance_id
        personnel_demand_list = self.ctx.get_var('personnel_demand_list')

        variables_map = {
            'project_name': project_name,
            'project_desc': project_desc,
            'clarify_completion_time': clarify_completion_time,
            'reporting_time': reporting_time,
            'scoring_and_approval': scoring_and_approval,
            'tender_opening_plan': tender_opening_plan,
            'team_table': team_table,
            'party_name': party_name,
            'people_info_list': people_info,
            'people_info_len': people_info_len,
            'f_instance_id': f_instance_id,
            'return_time': return_time,
            'all_personnel_list': all_personnel_list,
            "personnel_demand_list": personnel_demand_list,
            "bid_evaluation_team_leader": bid_evaluation_team_leader
        }
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})

        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_mmywang')
        # 用于保存创建的工单的单号
        ticket_id_list = []
        for i in range(people_info_len):
            # 子流程人员列表构建
            teamOptions = []
            variables_map.update({'people_info': people_info[i], 'personnel_demand': personnel_demand_list[i]})
            team_member_list = team_member_dict.get(team_table[i]["team"])["member_list"]
            for j in team_member_list:
                teamOptions.append({
                    "label": j,
                    "value": re.sub(r'\(.*?\)', '', j)
                })
            variables_map["teamOptions"] = teamOptions
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '"招采指定评标人',
                ticket_level=3,
                title=project_name + ' - 指定评标人',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=re.sub(r'\(.*?\)', '', people_info[i]),  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list.append(ticket_info.get('TicketId'))

        # 门户页面库数据添加
        ticket_id = self.ctx.variables.get("ticket_id")
        db = mysql.new_mysql_instance("tbconstruct")
        bid_evaluation_team_formation = json.dumps(
            {"ticket_id": [ticket_id], "children_ticket_id": ticket_id_list})
        conditions = {"project_name": project_name}
        update_data = {"bid_evaluation_team_formation": bid_evaluation_team_formation}
        db.begin()
        db.update("procurement_process_information", conditions=conditions, data=update_data)
        db.commit()

        variables_map.update({'ticket_id_list': ticket_id_list})
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    @cycle_task_abort
    def wait_mark(self):
        """
        等待所有人员指定评标人完成
        """
        ticket_id_list = self.variables.get('ticket_id_list', [])
        task_id = self.ctx.task_id
        cycle_is_over(ticket_id_list, task_id)


class Designation(AjaxTodoBase):
    """
        指定评标人
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        time.sleep(3)
        f_instance_id = self.ctx.variables.get("f_instance_id")
        f_data = flow.get_variables(f_instance_id, ["all_personnel_list"])
        all_personnel_list = f_data.get("all_personnel_list")
        ticket_id = self.ctx.variables.get("ticket_id")
        bid_evaluation_team_leader = self.ctx.variables.get("bid_evaluation_team_leader")
        bid_evaluator = process_data.get("bid_evaluator")
        personnel_demand = process_data.get("personnel_demand")
        if len(bid_evaluator) > int(personnel_demand):
            return {"code": -1, "msg": "评标人数不能超过需求人数"}
        people_info = self.ctx.variables.get("people_info")
        self.workflow_detail.add_kv_table("测试显示格式bid_evaluation_team_leader", bid_evaluation_team_leader)
        # 冲流程变量获取数据
        main_flow_vars = flow.get_variables(f_instance_id,
                                            ["team_table", "bid_evaluation_team_leader", "bid_evaluator"])
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': main_flow_vars})
        team_table = main_flow_vars.get("team_table", [])
        team_leader = [main_flow_vars.get("bid_evaluation_team_leader", [])]
        team_member = main_flow_vars.get("bid_evaluator", [])

        if bid_evaluation_team_leader:
            if type(bid_evaluation_team_leader) == str:
                bid_evaluation_team_leader_list = [bid_evaluation_team_leader]
            else:
                bid_evaluation_team_leader_list = bid_evaluation_team_leader
            self.workflow_detail.add_kv_table("测试显示格式bid_evaluation_team_leader_list",
                                              bid_evaluation_team_leader_list)
        else:
            bid_evaluation_team_leader_list = []

        if bid_evaluator:
            bid_evaluator_list = bid_evaluator
        else:
            bid_evaluator_list = []

        all_personnel_list.extend(bid_evaluator_list)
        all_personnel_list.extend(bid_evaluation_team_leader_list)
        self.workflow_detail.add_kv_table("测试显示格式all_personnel_list", all_personnel_list)
        unique_list = list(set(all_personnel_list))
        bid_evaluation_team_leader_str = ';'.join(bid_evaluation_team_leader_list)
        bid_evaluator_str = ';'.join(bid_evaluator_list)

        if team_table:
            for i in team_table:
                data = i.get("party_name")
                if data == people_info:
                    i["personnel_details"] = bid_evaluator_str

        variables = {
            'bid_evaluation_team_leader': bid_evaluation_team_leader,
            'bid_evaluator': bid_evaluator,
            'bid_evaluation_team_leader_str': bid_evaluation_team_leader_str,
            'bid_evaluator_str': bid_evaluator_str,
            'all_personnel_list': unique_list,
            'team_table': team_table
        }
        variables2 = {
            'bid_evaluation_team_leader': bid_evaluation_team_leader,
            'bid_evaluator': bid_evaluator_list + team_member,
            'bid_evaluation_team_leader_str': bid_evaluation_team_leader_str,
            'bid_evaluator_list': bid_evaluator_list,
            'all_personnel_list': unique_list,
            'team_table': team_table
        }

        # 将流程变量更新至 父流程
        if f_instance_id:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables=variables2
            ).call()
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(
            task_id=self.ctx.task_id,
            variables_map=variables
        ).call()


class CircularOrderCreation2(object):
    """
        循环建单，创建评标人员确认
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def create_bid_evaluator_confirmation(self, flow_key):
        """
            创建评标人员确认
        """
        time.sleep(3)
        bid_evaluation_team_leader = self.ctx.variables.get("bid_evaluation_team_leader")
        bid_evaluator = self.ctx.variables.get("bid_evaluator")
        ticket_id = self.ctx.variables.get("ticket_id")
        update_data = {
            'bid_evaluation_team_leader': str(bid_evaluation_team_leader),
            'bid_evaluator': str(bid_evaluator)
        }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("audit_basic_information", update_data, conditions)
        tb_db.commit()
        whether_to_participate_list = []
        project_name = self.ctx.get_var('project_name', '')
        project_desc = self.ctx.get_var('project_desc', '')
        return_time = self.ctx.get_var('return_time', '')
        estimated_evaluation_time = self.ctx.get_var('estimated_evaluation_time', '')
        all_personnel_list = self.ctx.get_var('all_personnel_list', '')
        all_personnel_len = len(all_personnel_list)
        all_personnel_str = ';'.join(all_personnel_list)
        f_instance_id = self.ctx.instance_id
        bid_evaluation_team_leader = self.ctx.get_var('bid_evaluation_team_leader', '')
        people_info = self.ctx.get_var('people_info', '')

        variables_map = {
            'project_name': project_name,
            'project_desc': project_desc,
            'estimated_evaluation_time': estimated_evaluation_time,
            'all_personnel_len': all_personnel_len,
            'all_personnel_str': all_personnel_str,
            'all_personnel_list': all_personnel_list,
            'return_time': return_time,
            'f_instance_id': f_instance_id,
            'whether_to_participate_list': whether_to_participate_list,
            'bid_evaluation_team_leader': bid_evaluation_team_leader,
            'people_info': people_info
        }
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})

        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_mmywang')
        # 用于保存创建的工单的单号
        ticket_id_list2 = []
        for i in range(all_personnel_len):
            variables_map.update({'all_personnel_list': all_personnel_list[i]})
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '评标人确定',
                ticket_level=3,
                title=project_name + ' - 评标人确定',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=all_personnel_list[i],  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list2.append(ticket_info.get('TicketId'))
        variables_map.update({'ticket_id_list2': ticket_id_list2})
        for i in range(all_personnel_len):
            variables_map.update({'whether_to_participate' + str(i): ''})
            #                     'whether_to_participate' + str(i)表示第i个一级审批人的审批结果
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    @cycle_task_abort
    def wait_mark2(self):
        """
            等待所有人员指定评标人完成
        """
        ticket_id_list = self.variables.get('ticket_id_list2', [])
        task_id = self.ctx.task_id
        cycle_is_over(ticket_id_list, task_id)


class BidderConfirms(AjaxTodoBase):
    """
        评标人确定
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        f_instance_id = self.ctx.variables.get("f_instance_id")
        f_data = flow.get_variables(f_instance_id, ["whether_to_participate_list", "ticket_id"])
        whether_to_participate_list = f_data.get("whether_to_participate_list")
        f_ticket_id = f_data.get("ticket_id")
        # ticket_id = self.ctx.variables.get("ticket_id")
        whether_to_participate = process_data.get("whether_to_participate")
        whether_to_participate_list.append(whether_to_participate)
        if '不参加' in whether_to_participate_list:
            result = '不参加'
        else:
            result = '参加'
        update_data = {
            'whether_to_participate': result
        }
        conditions = {
            'dcops_ticket_id': f_ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("audit_basic_information", update_data, conditions)
        tb_db.commit()
        variables = {
            'whether_to_participate_list': whether_to_participate_list,
            'result': result
        }
        variables2 = {
            'whether_to_participate_list': whether_to_participate_list,
            'result': result,
            'whether_to_participate': whether_to_participate
        }
        f_instance_id = self.ctx.get_var('f_instance_id')
        if f_instance_id:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables=variables
            ).call()
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(
            task_id=self.ctx.task_id,
            variables_map=variables2
        ).call()


class UploadTagFile(AjaxTodoBase):
    """
        开标、上传回标文件
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        main_id = self.ctx.variables.get("main_id")
        faq_id = self.ctx.variables.get("faq_id")
        bid_evaluation_standard_documents_table = process_data.get("bid_evaluation_standard_documents_table")
        # 获取供应商短名单
        supplier_list = self.ctx.variables.get("supplier_list")
        back_label_file_table = []
        for row in bid_evaluation_standard_documents_table:
            back_label_file = row.get("back_label_file", [])
            url_dict = {}
            if back_label_file:
                for file_url in back_label_file:
                    file_name = file_url.get("name")
                    if file_url.get("response", {}).get("FileList", []):
                        back_label_file_url = file_url.get("response", {}).get("FileList", [])[0].get("url")
                        supplier = next((sup for sup in supplier_list if sup in file_name), '供应商')
                        # 检查供应商是否已经在字典中，如果不在则初始化为空列表
                        if supplier not in url_dict:
                            url_dict[supplier] = []

                        # 将文件 URL 添加到供应商对应的列表中
                        url_dict[supplier].append(back_label_file_url)
                if not url_dict:
                    url_dict = {}
                back_label_file_table.append({
                    'section_name': row.get("section_name", ""),
                    'standard_remark': row.get("standard_remark", ""),
                    'back_label_file': url_dict
                })
            else:
                return {"code": -1, "msg": "回标文件不能为空"}
        update_data = {
            'back_label_file': json.dumps(back_label_file_table) if back_label_file_table else "",
        }
        conditions = {
            'ticket_id': faq_id
        }
        con = {
            'ticket_id': main_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, con)
        tb_db.update("information_joint_builder_faq", update_data, conditions)
        tb_db.commit()

        variables = {
            'back_label_file_table': back_label_file_table,
            'bid_evaluation_standard_documents_table': bid_evaluation_standard_documents_table,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RecruitJointing(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def parsing_standard_files(self, standard, select_template):
        """
        五阶段自动起单解析标准文件
        """
        # 获取url
        url = standard[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + ".xlsx"
        # 下载文件
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        excl_path = "/data/nbroker/" + new_file_name
        df = pd.read_excel(excl_path, engine="openpyxl")
        # 定义两种表头对应关系
        header_mapping_1 = {
            'project': '项目',
            'weight': '权重（%）',
            'eval_content': '评审内容',
            'first_level': '优秀(86~100分)',
            'second_level': '较好(71~85分)',
            'third_level': '一般(70分以下)'
        }
        header_mapping_2 = {
            'project': '项目',
            'eval_content': '评审内容',
            'first_level': '合格',
            'second_level': '不合格',
            'remark': '备注'
        }
        # 根据 select_template 动态选择表头映射
        if select_template == "1":
            header_mapping = header_mapping_1
        else:
            header_mapping = header_mapping_2
        # 初始化 ybpf_table
        ybpf_table = []
        filtered_ybpf_table = []
        # 获取 Excel 表格的列名
        excel_columns = df.columns.tolist()

        # 检查表头是否匹配
        missing_headers = [key for key, value in header_mapping.items() if value not in excel_columns]
        if missing_headers:
            pass
        else:
            # 移除空行
            df.dropna(how='all', inplace=True)

            # 定义需要移除的说明信息
            instructions = [
                {"project": "！！！1.请不要合并单元格\n2.不要修改列头！！！"},
                {"project": "！！！1.请不要合并单元格\n2.不要修改列头！！！"},
                {"project": "！！！记得删除该说明"}
            ]

            # 将说明信息转换为 DataFrame 的形式以便比较
            instructions_df = pd.DataFrame(instructions)

            # 检查并移除说明信息
            for i in range(len(df) - len(instructions_df) + 1):
                if df.iloc[i:i + len(instructions_df)].equals(instructions_df):
                    df.drop(index=range(i, i + len(instructions_df)), inplace=True)
                    break

            # 解析数据并拼接到 ybpf_table
            for _, row in df.iterrows():
                try:
                    # 初始化 parsed_row
                    parsed_row = {}
                    for key, col in header_mapping.items():
                        value = row[col]
                        # 检查是否为 np.nan 或者 None，然后将其转换为空字符串 ""
                        if pd.isna(value):
                            parsed_row[key] = ""
                        else:
                            parsed_row[key] = value
                    ybpf_table.append(parsed_row)
                except KeyError:
                    # 如果解析过程中出现 KeyError，则跳过该行
                    print("解析错误，跳过当前行")
                    continue
            filtered_ybpf_table = [
                item for item in ybpf_table
                if not all(value == "" for value in item.values())
            ]
        return filtered_ybpf_table

    def automatic_order_creation(self, project_desc, project_name, main_id, faq_id, bid_evaluation_team_leader,
                                 supplier_list):
        rater_leader = bid_evaluation_team_leader
        all_personnel_list = self.ctx.variables.get("all_personnel_list")
        all_personnel_str = ';'.join(all_personnel_list)
        rater = all_personnel_str
        f_instance_id = self.ctx.instance_id
        bid_evaluation_standard_documents_table = self.ctx.variables.get("bid_evaluation_standard_documents_table")
        ticket_id = self.ctx.variables.get("ticket_id")
        res_list = []
        res_ticket_list = []
        leader = self.ctx.variables.get("leader")
        center = self.ctx.variables.get("center")
        GM = self.ctx.variables.get("GM")
        business_affairs = self.ctx.variables.get("business_affairs")
        select_template = self.ctx.variables.get("select_template", "")
        if select_template == "1":
            flow_key = "TB_bid_evaluation_process_one"
        else:
            flow_key = "TB_bid_evaluation_process_pass"
        for row in bid_evaluation_standard_documents_table:
            back_label_file_list = row.get("back_label_file", [])
            tender_document_table = []
            for i in back_label_file_list:
                tender_document_table.append({
                    "tender_document_name": i["response"]["FileList"][0]["name"],
                    "tender_document": i["response"]["FileList"][0]["url"]
                })
            section_name = row.get("section_name")
            if project_desc:
                custom_variables = {
                    "project_desc": project_desc,
                    "project_name": project_name,
                    "raters": rater,
                    "rater_leader": rater_leader,
                    "suppliers": supplier_list,
                    'f_instance_id': f_instance_id,
                    "tender_document_table": tender_document_table,
                    "leader": leader,
                    "center": center,
                    "GM": GM,
                    "business_affairs": business_affairs,
                    "section_name": section_name
                }
            else:
                custom_variables = {
                    "project_name": project_name,
                    "raters": rater,
                    "rater_leader": rater_leader,
                    "suppliers": supplier_list,
                    'f_instance_id': f_instance_id,
                    "tender_document_table": tender_document_table,
                    "leader": leader,
                    "center": center,
                    "GM": GM,
                    "business_affairs": business_affairs,
                    "section_name": section_name
                }
            standard = row.get("standard")
            if standard:
                ybpf_table = self.parsing_standard_files(standard, select_template)
                if ybpf_table:
                    custom_variables.update({"ybpf_table": ybpf_table})
            # 获取当前日期和时间
            now = datetime.datetime.now()
            year = now.year
            month = now.month
            day = now.day
            date_string = f"{year}年{month}月{day}号"
            processTitle = date_string + f"{project_name}评分工单"
            data = {
                "CustomVariables": custom_variables,
                "ProcessDefinitionKey": flow_key,
                "Source": "",
                "TicketDescription": "TB评标流程",
                "TicketLevel": "3",
                "TicketTitle": processTitle,
                "UserInfo": {
                    "Concern": "youngshi",
                    "Creator": "youngshi",
                    "Deal": "youngshi"
                }
            }
            # 起单，并抛入data
            res = gnetops.request(action="Ticket", method="Create", data=data)
            res_list.append(res)
            if res:
                order_id = res.get("TicketId")
                if str(order_id).isdigit():
                    res_ticket_list.append(order_id)

        # 门户页面库数据添加
        db = mysql.new_mysql_instance("tbconstruct")
        tender_evaluation = json.dumps(
            {"ticket_id": res_ticket_list})
        conditions = {"project_name": project_name}
        update_data = {"tender_evaluation": tender_evaluation}
        db.begin()
        db.update("procurement_process_information", conditions=conditions, data=update_data)
        db.commit()

        update_data = {
            'tb_order': json.dumps(res_ticket_list)
        }
        conditions = {
            'faq_order': faq_id
        }
        con = {
            'ticket_id': faq_id
        }
        con2 = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, conditions)
        tb_db.update("information_joint_builder_faq", update_data, con)
        tb_db.update("audit_basic_information", update_data, con2)
        tb_db.commit()
        variables = {
            'res_list': res_list,
            'tb_order': res_ticket_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def FAQ_order(self, project_desc, project_name, business_affairs, ticket_id, supplier_list, team_leader,
                  all_person_list):
        if project_desc:
            custom_variables = {
                "project_desc": project_desc,
                "project_name": project_name,
                "business_affairs": business_affairs,
                "main_id": ticket_id,
                "supplier_list": supplier_list,
                "team_leader": team_leader
            }
        else:
            custom_variables = {
                "project_name": project_name,
                "business_affairs": business_affairs,
                "main_id": ticket_id,
                "supplier_list": supplier_list,
                "team_leader": team_leader
            }
        if not all_person_list:
            all_person_list = [team_leader]
        custom_variables["all_person_list"] = all_person_list
        # 获取当前日期和时间
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号"
        processTitle = date_string + f"{project_name}招采答疑工单"
        data = {
            "CustomVariables": custom_variables,
            "ProcessDefinitionKey": "joint_construction_party_recruitment_faq",
            "Source": "",
            "TicketDescription": "合建方招采—答疑工单",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "youngshi",
                "Creator": "youngshi",
                "Deal": "youngshi"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        if res:
            table = self.ctx.variables.get("table")
            order_id = res.get("TicketId")
            business_affairs = self.ctx.variables.get("business_affairs")
            email_data = f"【项目名称】:{project_name}<br>"
            email_data += f"【内容】:{project_name}已完成技术审批，请商务推进后续招采事宜！<br>"
            email_data += (f"【工单链接】:<br>"
                           f"https://dcops.test.woa.com/operateManage/business/ToDoDetails?params={order_id}<br>")
            for row in table:
                file_name = row.get("file_name")
                file_url = row.get("file")[0]["response"]["FileList"][0]["url"]
                row_text = f"文件名: {file_name} <br>下载链接: {file_url}<br>"
                email_data += row_text
            qw_data = f"【项目名称】:{project_name}\n"
            qw_data += f"【内容】:{project_name}已完成技术审批，请商务推进后续招采事宜！\n"
            qw_data += f"【工单链接】:https://dcops.test.woa.com/operateManage/business/ToDoDetails?params={order_id}"

            if str(order_id).isdigit():
                update_data = {
                    'faq_order': order_id
                }
                conditions = {
                    'ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("information_joint_builder", update_data, conditions)
                tb_db.commit()

                variables = {
                    'faq_order': res,
                    "faq_order_id": str(order_id),
                    'email_data': email_data,
                    'email': business_affairs,
                    'title': project_name,
                    'qw_data': qw_data
                }
                flow.complete_task(self.ctx.task_id, variables=variables)

    def tender_evaluation_order(self, project_desc, project_name, main_id, supplier_list):

        # 获取当前日期和时间
        now = datetime.datetime.now()
        year = now.year
        month = now.month
        day = now.day
        date_string = f"{year}年{month}月{day}号"
        processTitle = date_string + f"{project_name}招采评标工单"
        ticket_id = self.ctx.variables.get("ticket_id")
        business_affairs = self.ctx.variables.get("business_affairs")
        tender_opening_plan = self.ctx.variables.get("tender_opening_plan")
        f_instance_id = self.ctx.instance_id
        if project_desc:
            custom_variables = {
                "project_desc": project_desc,
                "project_name": project_name,
                "main_id": main_id,
                "faq_id": ticket_id,
                "supplier_list": supplier_list,
                "business_affairs": business_affairs,
                "tender_opening_plan": tender_opening_plan,
                "f_instance_id": f_instance_id
            }
        else:
            custom_variables = {
                "project_name": project_name,
                "main_id": main_id,
                "faq_id": ticket_id,
                "supplier_list": supplier_list,
                "business_affairs": business_affairs,
                "tender_opening_plan": tender_opening_plan,
                "f_instance_id": f_instance_id
            }

        data = {
            "CustomVariables": custom_variables,
            "ProcessDefinitionKey": "joint_construction_party_recruitment_tender_evaluation",
            "Source": "",
            "TicketDescription": "合建方招采—评标工单",
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": "youngshi",
                "Creator": "youngshi",
                "Deal": "youngshi"
            }
        }
        # 起单，并抛入data
        res = gnetops.request(action="Ticket", method="Create", data=data)
        if res:
            order_id = res.get("TicketId")
            email_data = f"【项目名称】:{project_name}<br>"
            email_data += f"【内容】:{project_name}已完成答疑澄清审批，请推进后续招采事宜！<br>"
            email_data += (f"【工单链接】:<br>"
                           f"https://dcops.test.woa.com/operateManage/business/ToDoDetails?params={order_id}")
            qw_data = f"【项目名称】:{project_name}\n"
            qw_data += f"【内容】:{project_name}已完成答疑澄清审批，请推进后续招采事宜！\n"
            qw_data += f"【工单链接】:https://dcops.test.woa.com/operateManage/business/ToDoDetails?params={order_id}"

            # 门户页面库数据添加
            db = mysql.new_mysql_instance("tbconstruct")
            bid_evaluation_team_formation = json.dumps(
                {"ticket_id": [order_id]})
            conditions = {"project_name": project_name}
            update_data = {"bid_evaluation_team_formation": bid_evaluation_team_formation}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()

            if str(order_id).isdigit():
                update_data = {
                    'tender_evaluation_order': order_id
                }
                conditions = {
                    'ticket_id': main_id
                }
                con = {
                    'ticket_id': ticket_id
                }
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.update("information_joint_builder", update_data, conditions)
                tb_db.update("information_joint_builder_faq", update_data, con)
                tb_db.commit()
                variables = {
                    'tender_evaluation_order': res,
                    'email_data': email_data,
                    'title': project_name,
                    'qw_data': qw_data
                }
                flow.complete_task(self.ctx.task_id, variables=variables)

    def technical_documentation_order(self, team_leader, all_person_list, project_name, project_desc):
        team_info = self.ctx.variables.get("team_info")
        variables_map = {
            "project_name": project_name,
            "project_desc": project_desc,
            "team_leader": team_leader,
            "all_person_list": all_person_list
        }
        if team_info:
            variables_map.update({"team_info": team_info})
        ticket_info = gnetops.create_ticket(
            flow_key="joint_construction_party_recruitment",
            description='该工单为："' + project_name + '技术文件制定及审批"',
            ticket_level=3,
            title=project_name + '技术文件制定及审批',
            creator="v_zongxiyu",
            concern="",  # 关注人, 这里暂为空
            deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var=variables_map  # 自定义流程变量，抛给派单子流程
        )
        if ticket_info:
            db = mysql.new_mysql_instance("tbconstruct")
            technical_documentation_preparation = json.dumps({"ticket_id": [ticket_info.get("TicketId")]})
            conditions = {"project_name": project_name}
            update_data = {"technical_documentation_preparation": technical_documentation_preparation}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()
        variables = {
            'technical_documentation_order': ticket_info,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def wait_return_bid_completed(self):
        db = mysql.new_mysql_instance("tbconstruct")
        faq_id = self.ctx.variables.get("faq_id")
        project_name = self.ctx.variables.get("project_name")
        sql = "SELECT return_time FROM information_joint_builder_faq " \
              f"WHERE ticket_id='{faq_id}' and project_name = '{project_name}'"
        info = db.get_all(sql)
        self.workflow_detail.add_kv_table('获取表中数据', {'info': info})
        if info:
            return_time = info[0].get("return_time")
            if return_time:
                variables = {
                    'return_time': return_time,
                }
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}


class RecruitSendEmail(AjaxTodoBase):
    """
        发送邮件企微信息
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get("logo", "")
        if logo and logo == "澄清答疑阶段发送企微信息":
            title = process_data.get("title")
            email_data = process_data.get("email_data")
            qw_data = self.ctx.variables.get("qw_data")
            email = process_data.get("email")
            chat_list = email.split(";")
            email_list = []
            for i in chat_list:
                i += "@tencent.com"
                email_list.append(i)
            # 企微
            for i in chat_list:
                ChatOpsSend(
                    user_name=i,
                    msg_content=qw_data,
                    msg_type='text',
                    des_type='single'
                ).call()
            # 邮件
            tof.send_email(sendTitle=title, msgContent=email_data, sendTo=email_list)
            # 企微
            tof.send_company_wechat_message(receivers=chat_list, title=title, message=qw_data)
            variables = {
                "title": title,
                "email": email,
                "email_data": email_data,
                "email_list": email_list,
                "chat_list": chat_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
            return None
        table = self.ctx.variables.get("table")
        project_name = self.ctx.variables.get("project_name")
        team_leader = self.ctx.variables.get("team_leader")
        team_leader += "@tencent.com"
        title = process_data.get("title")
        ticket_id = self.ctx.variables.get("ticket_id")
        email_data = process_data.get("email_data")
        qw_data = process_data.get("qw_data")
        email = process_data.get("email")
        chat_list = email.split(";")
        business_processor = chat_list[0]
        # email_data = f"【项目名称】:{project_name}<br>【内容】:<br>" + email_data
        # email_data += f"https://dcops.test.woa.com/appManage/tickets/details?params={ticket_id}"
        # email_data += "<br><br>"
        # qw_data = process_data.get("qw_data")
        # email = process_data.get("email")
        # for row in table:
        #     file_name = row.get("file_name")
        #     file_url = row.get("file")[0]["response"]["FileList"][0]["url"]
        #     row_text = f"文件名: {file_name} <br>下载链接: {file_url}<br><br>"
        #     email_data += row_text
        email_list = []
        for i in chat_list:
            i += "@tencent.com"
            email_list.append(i)
        # 企微
        for i in chat_list:
            ChatOpsSend(
                user_name=i,
                msg_content=qw_data,
                msg_type='text',
                des_type='single'
            ).call()
        # 邮件
        tof.send_email(sendTitle=title, msgContent=email_data, sendTo=email_list, sendCopy=[f"{team_leader}"], )
        # 企微
        tof.send_company_wechat_message(receivers=chat_list, title=title, message=email_data)

        variables = {
            "title": title,
            "email": email,
            "email_data": email_data,
            "email_list": email_list,
            "chat_list": chat_list,
            "business_processor": business_processor
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BusinessProcessorConfirmation(AjaxTodoBase):
    """
        业务处理人确认
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_pass = process_data.get("is_pass")
        if is_pass == "2":
            business_processor = self.ctx.variables.get("business_processor")
            title = self.ctx.variables.get("title")
            project_name = self.ctx.variables.get("project_name")
            team_leader = self.ctx.variables.get("team_leader")
            email_list = [team_leader + "@tencent.com"]
            email = f"【{project_name}招采】商务无法查看技术文件，请联系商务负责人{business_processor}"
            # 邮件
            tof.send_email(sendTitle=title, msgContent=email, sendTo=email_list, sendCopy=[f"{business_processor}"], )
        variables = {"is_pass": is_pass}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToEddiewuTenderEvaluation(AjaxTodoBase):
    """
        发送信息给eddiewu
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval")
        leader_remarks = process_data.get("leader_remarks")
        variables = {
            'leader_approval': leader_approval,
            'leader_remarks': leader_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToCenterTenderEvaluation(AjaxTodoBase):
    """
        发送信息给总监
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        center_approval = process_data.get("center_approval")
        center_remarks = process_data.get("center_remarks")
        variables = {
            'center_approval': center_approval,
            'center_remarks': center_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToGMTenderEvaluation(AjaxTodoBase):
    """
        发送信息给GM
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        GM_approval = process_data.get("GM_approval")
        GM_remarks = process_data.get("GM_remarks")
        variables = {
            'GM_approval': GM_approval,
            'GM_remarks': GM_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToEddiewuStandard(AjaxTodoBase):
    """
        发送信息给eddiewu
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        leader_approval = process_data.get("leader_approval_standard")
        leader_remarks = process_data.get("leader_remarks_standard", )
        variables = {
            'leader_approval_standard': leader_approval,
            'leader_remarks_standard': leader_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToGMStandard(AjaxTodoBase):
    """
        发送信息给GM
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        GM_approval = process_data.get("GM_approval_standard")
        GM_remarks = process_data.get("GM_remarks_standard")
        variables = {
            'GM_approval_standard': GM_approval,
            'GM_remarks_standard': GM_remarks
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToDirectorStandard(AjaxTodoBase):
    """
        发送信息给中心负责人
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        director_approval_standard = process_data.get("director_approval_standard")
        director_remarks_standard = process_data.get("director_remarks_standard")
        center = self.ctx.variables.get("center")
        GM = self.ctx.variables.get("GM")
        next_op = 0
        if center == GM:
            next_op = 1
            variables = {
                'director_approval_standard': director_approval_standard,
                'director_remarks_standard': director_remarks_standard,
                'GM_approval_standard': director_approval_standard,
                'next_op': next_op
            }
        else:
            variables = {
                'director_approval_standard': director_approval_standard,
                'director_remarks_standard': director_remarks_standard,
                'next_op': next_op
            }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RecruitStandard(AjaxTodoBase):
    """
        上传标准
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        bid_evaluation_standard_documents_table = process_data.get("bid_evaluation_standard_documents_table")
        main_id = self.ctx.variables.get("main_id")
        faq_id = self.ctx.variables.get("faq_id")
        self.workflow_detail.add_kv_table('上传标准', {"data": bid_evaluation_standard_documents_table})
        data = []
        select_template = process_data.get("select_template", "")
        for row in bid_evaluation_standard_documents_table:
            standard = row.get("standard")
            url_list = []
            if standard:
                for file_url in standard:
                    if file_url.get("response", {}).get("FileList", []):
                        standard_url = file_url.get("response", {}).get("FileList", [])[0].get("url")
                        url_list.append(standard_url)
                if not url_list:
                    url_list = []
                data.append({
                    'section_name': row.get("section_name", ""),
                    'standard_remark': row.get("standard_remark", ""),
                    'standard': url_list
                })
            else:
                return {"code": -1, 'msg': '请上传标准文件'}
        update_data = {
            'standard': json.dumps(data) if data else "",
        }

        conditions = {
            'ticket_id': faq_id
        }
        con = {
            'ticket_id': main_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("information_joint_builder", update_data, con)
        tb_db.update("information_joint_builder_faq", update_data, conditions)
        tb_db.commit()
        variables = {
            "bid_evaluation_standard_documents_table": bid_evaluation_standard_documents_table,
            "select_template": select_template
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BasicInformationOfBidEvaluation(AjaxTodoBase):
    """
        评标流程：基本信息
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name', '')
        project_desc = process_data.get('project_desc', '')
        teamOptions = []
        teamInfoData = {}
        feedback_time = self.ctx.variables.get('feedback_time', "")
        clarify_completion_time = ""
        if feedback_time:
            clarify_completion_time = feedback_time

        sql = "select center,group_name,member,leader,center_leader from organizational_structure_membership_details"
        db = mysql.new_mysql_instance("tbconstruct")
        query = db.get_all(sql)
        member_dict = {}
        for row in query:
            center = row.get("center")
            group_name = row.get("group_name")
            member = row.get("member")
            leader = row.get("leader")
            center_leader = row.get("center_leader")
            if center == "数据中心":
                # 如果组名为运营优化组或运营质量组，按照常规小组处理数据
                if group_name in ["运营优化组", "运营质量组"]:
                    if group_name not in member_dict:
                        member_dict[group_name] = {}
                        member_dict[group_name]["member_list"] = []
                        teamOptions.append({"label": group_name, "value": group_name})
                        teamInfoData[group_name] = {"party_name": leader}
                        member_dict[group_name]["leader"] = leader
                        member_dict[group_name]["center_leader"] = center_leader
                    member_dict[group_name]["member_list"].append(member)
                else:
                    # 如果组名不是运营优化组或运营质量组，按center逻辑处理
                    if center not in member_dict:
                        member_dict[center] = {}
                        member_dict[center]["member_list"] = []
                        teamOptions.append({"label": center, "value": center})
                        teamInfoData[center] = {"party_name": center_leader}
                        member_dict[center]["leader"] = leader
                        member_dict[center]["center_leader"] = center_leader
                    member_dict[center]["member_list"].append(member)
            else:
                if group_name not in member_dict:
                    member_dict[group_name] = {}
                    member_dict[group_name]["member_list"] = []
                    teamOptions.append({
                        "label": group_name,
                        "value": group_name
                    })
                    teamInfoData[group_name] = {
                        "party_name": leader
                    }
                    member_dict[group_name]["leader"] = leader
                    member_dict[group_name]["center_leader"] = center_leader
                member_dict[group_name]["member_list"].append(member)
        variables = {
            'project_name': project_name,
            "project_desc": project_desc,
            "teamOptions": teamOptions,
            "teamInfoData": teamInfoData,
            "member_dict": member_dict,
            "clarify_completion_time": clarify_completion_time
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class SendToBidEvaluationTeamMembers(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def send_message(self):
        project_name = self.ctx.variables.get("project_name")
        project_desc = self.ctx.variables.get("project_desc")
        bid_evaluation_team_leader = self.ctx.variables.get("bid_evaluation_team_leader")
        bid_evaluator = self.ctx.variables.get("bid_evaluator")
        ticket_id = self.ctx.variables.get("ticket_id")
        bid_evaluator_list = list(set(bid_evaluator))
        personnel_email_list = []
        for row in bid_evaluator_list:
            row += "@tencent.com"
            personnel_email_list.append(row)
        qw_data = f'【项目名称】：{project_name}\n'
        qw_data += f'【项目概况】：{project_desc}\n'
        qw_data += f'【内容】：你已成为{project_name}技术小组成员，请多关注此类信息！\n'
        email_data = f'<p>【项目名称】：{project_name}</p><br>'
        email_data += f'<p>【项目概况】：{project_desc}</p><br>'
        email_data += f'<p>【内容】：你已成为{project_name}技术小组成员，组长：{bid_evaluation_team_leader}，请多关注此类信息！</p><br>'
        # 企微
        for i in bid_evaluator_list:
            ChatOpsSend(
                user_name=i,
                msg_content=qw_data,
                msg_type='text',
                des_type='single'
            ).call()
        # 邮件
        tof.send_email(sendTitle=project_name, msgContent=email_data, sendTo=personnel_email_list)
        # 企微
        tof.send_company_wechat_message(receivers=bid_evaluator_list, title=project_name, message=qw_data)
        team_table = self.ctx.variables.get("team_table")
        insert_list = []
        for row in team_table:
            insert_list.append({
                'ticket_id': ticket_id,
                'project_name': project_name,
                'leader': bid_evaluation_team_leader,
                'team_leader': row.get("party_name"),
                'team_members': row.get("personnel_details"),
                'team_num': row.get("personnel_demand"),
                "team": row.get("team")
            })
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("technical_team_formation", insert_list)
        tb_db.commit()

        variables = {
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
