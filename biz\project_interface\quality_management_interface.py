from datetime import datetime

from iBroker.lib import mysql


# 到货管理
class ArrivalManagementInterface(object):
    def arrival_data_total(self, project_name, campus, page_size, page_number):
        db = mysql.new_mysql_instance("tbconstruct")

        sql2 = "SELECT ead.equipment_category,ead.actual_arrival_time,ead.upload_receiving_information,ead.supplier " \
               "FROM equipment_arrival_data ead WHERE project_name='%s' and campus = '%s'" \
               "AND upload_receiving_information IS NOT NULL" % (project_name, campus)

        receiving_list = db.get_all(sql2)
        # 计算总数
        total = len(receiving_list)
        # 计算总页数
        total_pages = (len(receiving_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = receiving_list[start_index:end_index]
        # 到货详细信息
        receiving_info = []
        for i in page_list:
            if i.get('upload_receiving_information'):
                i["upload_receiving_information"] = i.get('upload_receiving_information').split(";")
            # 转换格式
            time_ob = i.get('actual_arrival_time')
            if time_ob is not None:
                actual_arrival_time = time_ob.strftime("%Y-%m-%d")
            else:
                actual_arrival_time = time_ob
            receiving_info.append({
                'equipment_manufacturer': i.get('supplier'),
                'arrival_time': actual_arrival_time,
                'equipment_name': i.get('equipment_category'),
                'upload_receiving_information': i.get('upload_receiving_information')
            })

        data = {
            # 到货数量
            'receiving_number': total,
            # 到货详细信息
            'receiving_info': receiving_info
        }

        return data

    def shipping_data_total(self, project_name, campus, page_size, page_number):
        db = mysql.new_mysql_instance("tbconstruct")

        sql1 = "SELECT ead.equipment_category,ead.upload_shipping_information,ead.supplier,ead.actual_delivery_time " \
               "FROM equipment_arrival_data ead WHERE project_name='%s' " \
               "and campus = '%s'AND upload_shipping_information IS NOT NULL" % (project_name, campus)
        shipping_list = db.get_all(sql1)
        # 计算总数
        total = len(shipping_list)
        # 计算总页数
        total_pages = (len(shipping_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = shipping_list[start_index:end_index]
        # 发货详细信息
        shipping_info = []
        for i in page_list:
            if i.get('upload_shipping_information'):
                i["upload_shipping_information"] = i.get('upload_shipping_information').split(";")
            # 转换格式
            time_ob = i.get('actual_delivery_time')
            if time_ob is not None:
                actual_arrival_time = time_ob.strftime("%Y-%m-%d")
            else:
                actual_arrival_time = time_ob
            shipping_info.append({
                'equipment_manufacturer': i.get('supplier'),
                'equipment_name': i.get('equipment_category'),
                'upload_shipping_information': i.get('upload_shipping_information'),
                'shipping_time': actual_arrival_time
            })
        data = {
            # 发货数量
            'shipping_number': total,
            # 发货详细信息
            'shipping_list': shipping_info,
        }
        return data


# 施工管理
class ConstructionManagementInterface(object):
    """
        专项方案审核
    """

    def construction_data_total(self, project_name, page_size, page_number):

        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT task_name,finish_time,special_construction_plan_document FROM special_program_data " \
               f"WHERE project_name = '{project_name}' "
        sql2 = "SELECT task_name,finish_time,special_construction_plan_document FROM week_create_info wci " \
               "JOIN special_program_data spd ON wci.ticketid = spd.dcops_ticket_id " \
               f"WHERE wci.project_name = '{project_name}' "
        count_list = list(db.get_all(sql1)) + list(db.get_all(sql2))
        # 列表去重
        inquiry_list = [dict(t) for t in {tuple(d.items()) for d in count_list}]

        # 计算总数
        total = len(inquiry_list)
        # 计算总页数
        total_pages = (len(inquiry_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = inquiry_list[start_index:end_index]
        for i in page_list:
            if i.get('special_construction_plan_document'):
                i["special_construction_plan_document"] = i.get('special_construction_plan_document').split(";")
            time_ob = i.get('finish_time', None)
            if time_ob is not None:
                finish_time = time_ob.strftime("%Y-%m-%d")
                i['finish_time'] = finish_time
        data = {
            'construction_number': total,
            'construction_total_list': page_list
        }
        return data

    # 完工分部分审核
    def completion_part_acceptance(self, project_name, page_size, page_number):
        offset = (page_number - 1) * page_size  # 计算偏移量
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(ticket_id) FROM completion_part_acceptance WHERE project_name='{project_name}'"

        sql1 = "SELECT item_name,acceptance_time,acceptance_report_url FROM completion_part_acceptance " \
               f"WHERE project_name = '{project_name}' "
        sql1 += f" LIMIT {offset}, {page_size}"
        # 总数
        count_info = db.get_all(sql)
        total_results = 0
        if count_info:
            total_results = count_info[0].get('count(ticket_id)')
        part_acceptance_list = db.get_all(sql1)
        for i in part_acceptance_list:
            if i.get('acceptance_report_url'):
                i["acceptance_report_url"] = i.get('acceptance_report_url').split(";")
            time_ob = i.get('acceptance_time', None)
            if time_ob is not None:
                i['acceptance_time'] = time_ob.strftime("%Y-%m-%d")
        data = {
            'part_acceptance_number': total_results,
            'part_acceptance_list': part_acceptance_list
        }

        return data

    # 质量巡查
    def quality_inspection(self, project_name, page_size, page_number):
        offset = (page_number - 1) * page_size  # 计算偏移量
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT count(ticket_id) FROM quality_inspection WHERE project_name='{project_name}'"

        sql1 = "SELECT inspection_time,	inspection_report_url FROM quality_inspection " \
               f"WHERE project_name = '{project_name}' "
        sql1 += f" LIMIT {offset}, {page_size}"
        # 总数
        count_info = db.get_all(sql)
        total_results = 0
        if count_info:
            total_results = count_info[0].get('count(ticket_id)')

        quality_inspection_list = db.get_all(sql1)
        for i in quality_inspection_list:
            if i.get('inspection_report_url'):
                i["inspection_report_url"] = i.get('inspection_report_url').split(";")
            time_ob = i.get('inspection_time', None)
            if time_ob is not None:
                i['inspection_time'] = time_ob.strftime("%Y-%m-%d")
        data = {
            'quality_inspection_number': total_results,
            'quality_inspection_list': quality_inspection_list
        }
        return data


# 问题管理
class ProblemManagementInterface(object):
    """
    责任单位：responsible_unit
    提出时间：problem_put_forward_time
    关闭时间：problem_end_time
    影响分类：impact_classification
    """

    def problem_classification_query(self, project_name, problem_category, problem_level, rectification_status,
                                     page_size, page_number, responsible_unit, impact_classification,
                                     problem_put_forward_time, problem_end_time):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT question_item,belong_professional,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact,impact_classification" \
               f" FROM quality_technics_management WHERE project_name='{project_name}'"
        sql2 = "SELECT qtm.question_item,qtm.belong_professional,qtm.problem_category,qtm.problem_location," \
               "qtm.problem_description,qtm.problem_photo,qtm.rectification_reform_measures,qtm.problem_level," \
               "qtm.responsible_unit,qtm.problem_put_forward_time,qtm.problem_impact, qtm.impact_classification," \
               "qtm.problem_end_time,qtm.photos_after_rectification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        if responsible_unit:
            sql1 += f" AND responsible_unit = '{responsible_unit}'"
            sql2 += f" AND qtm.responsible_unit = '{responsible_unit}'"
        if impact_classification:
            sql1 += f" AND impact_classification = '{impact_classification}'"
            sql2 += f" AND qtm.impact_classification = '{impact_classification}'"

        if problem_category:
            sql1 += f" AND problem_category = '{problem_category}'"
            sql2 += f" AND qtm.problem_category = '{problem_category}'"
        if problem_level:
            sql1 += f" AND problem_level = '{problem_level}'"
            sql2 += f" AND qtm.problem_level = '{problem_level}'"
        if problem_end_time and rectification_status == '未关闭':
            return {'code': 400, "msg": "传参错误"}
        if problem_put_forward_time and problem_end_time:
            if (type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2
                    and type(problem_end_time) == list and len(problem_end_time) == 2):
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                sql1 += (f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                         f"'{problem_put_end}') AND (problem_put_forward_time BETWEEN "
                         f"'{problem_end_start}' AND '{problem_end_end}')")
                sql2 += (f"AND (qtm.problem_put_forward_time BETWEEN '{problem_put_start}' AND "
                         f"'{problem_put_end}') AND (qtm.problem_put_forward_time BETWEEN "
                         f"'{problem_end_start}' AND '{problem_end_end}')")
        elif problem_put_forward_time or problem_end_time:
            if type(problem_put_forward_time) == list and len(problem_put_forward_time) == 2:
                problem_put_start = datetime.strptime(problem_put_forward_time[0], "%Y-%m-%d")
                problem_put_end = datetime.strptime(problem_put_forward_time[1], "%Y-%m-%d")
                sql1 += f"AND (problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"
                sql2 += f"AND (qtm.problem_put_forward_time BETWEEN '{problem_put_start}' AND '{problem_put_end}')"

            if type(problem_end_time) == list and len(problem_end_time) == 2:
                problem_end_start = datetime.strptime(problem_end_time[0], "%Y-%m-%d")
                problem_end_end = datetime.strptime(problem_end_time[1], "%Y-%m-%d")
                sql1 += f"AND (problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"
                sql2 += f"AND (qtm.problem_end_time BETWEEN '{problem_end_start}' AND '{problem_end_end}')"
        if rectification_status == '已关闭':
            sql1 += " AND problem_end_time IS NOT NULL "
            sql2 += " AND qtm.problem_end_time IS NOT NULL"
        elif rectification_status == '未关闭':
            sql1 += " AND problem_end_time IS NULL"
            sql2 += " AND qtm.problem_end_time IS NULL"
        # 列表合并
        problem_inquiry_list = list(db.get_all(sql1)) + list(db.get_all(sql2))
        problem_solved_list = []
        problem_unsolved_list = []
        for i in problem_inquiry_list:
            i['problem_impact_classification'] = i.get('impact_classification')
            put_forward_time = i.get('problem_put_forward_time')
            end_time = i.get('problem_end_time')
            # 转换日期数据格式
            if put_forward_time is not None:
                problem_put_forward_time = put_forward_time.strftime("%Y-%m-%d")
                i['problem_put_forward_time'] = problem_put_forward_time
            # 转换日期格式
            # 判断问题是否关闭
            if end_time is not None:
                problem_end_time = end_time.strftime("%Y-%m-%d")
                i['problem_end_time'] = problem_end_time
                i['correction_status'] = '已关闭'
                duration = (end_time - put_forward_time).days + 1
                i['duration'] = str(duration)
                problem_solved_list.append(i)
            else:
                problem_unsolved_list.append(i)
                i['correction_status'] = '未关闭'
                now = datetime.now()
                duration = (now - put_forward_time).days + 1
                if 3 < duration <= 7:
                    i['duration'] = "大于3天"
                elif 7 < duration <= 15:
                    i['duration'] = "大于7天"
                elif duration > 15:
                    i['duration'] = "大于15天"

        # 计算总数
        total = len(problem_inquiry_list)
        # 计算总页数
        total_pages = (len(problem_inquiry_list) + page_size - 1) // page_size
        # 检查请求的页码是否超出范围
        if page_number < 1:
            page_number = 1
        if page_number > total_pages:
            page_number = total_pages
        start_index = (page_number - 1) * page_size
        end_index = start_index + page_size
        # 检查索引是否超出范围
        if start_index < 0:
            start_index = 0
        end_index = min(end_index, total)
        page_list = problem_inquiry_list[start_index:end_index]
        for page in page_list:
            if page.get('problem_photo'):
                if "," in page.get('problem_photo'):
                    problem_url_list = page.get('problem_photo').split(",")
                else:
                    problem_url_list = page.get('problem_photo').split(";")
                page['problem_photo'] = problem_url_list
            if page.get('photos_after_rectification'):
                if "," in page.get('photos_after_rectification'):
                    url_list = page.get('photos_after_rectification').split(",")
                else:
                    url_list = page.get('photos_after_rectification').split(";")
                page['photos_after_rectification'] = url_list

        data = {
            "problem_inquiry_list": page_list,
            'problem_solved_number': len(problem_solved_list),
            'problem_unsolved_number': len(problem_unsolved_list),
            'problem_total_number': total
        }

        return data

    def question_category_total(self, project_name):
        """
        按问题分类：各分类数量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'"
        sql2 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}'"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql2))
        total_results = len(quality_list)
        if total_results == 0:
            return [
                {"value": 0, "name": '建设管理',
                 "percentage": "0.00%"},
                {"value": 0, "name": 'TB设计',
                 "percentage": "0.00%"},
                {"value": 0, "name": '乙供设备供货',
                 "percentage": "0.00%"},
                {"value": 0, "name": '乙供产品质量',
                 "percentage": "0.00%"},
                {"value": 0, "name": '土建建设',
                 "percentage": "0.00%"},
                {"value": 0, "name": '资源配套(政府)',
                 "percentage": "0.00%"},
            ]
        # 设备信息
        js = 0
        tb = 0
        gh = 0
        cp = 0
        tj = 0
        zy = 0
        for e in quality_list:
            if e.get("problem_category") == '建设管理':
                js += 1
            elif e.get("problem_category") == 'TB设计':
                tb += 1
            elif e.get("problem_category") == '乙供设备供货':
                gh += 1
            elif e.get("problem_category") == '乙供产品质量':
                cp += 1
            elif e.get("problem_category") == '土建建设':
                tj += 1
            elif e.get("problem_category") == '资源配套(政府)':
                zy += 1
        quality_category_list = [
            {"value": js, "name": '建设管理',
             "percentage": "{:.2f}%".format(round(float(js) / float(total_results) * 100))},
            {"value": tb, "name": 'TB设计',
             "percentage": "{:.2f}%".format(round(float(tb) / float(total_results) * 100))},
            {"value": gh, "name": '乙供设备供货',
             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
            {"value": cp, "name": '乙供产品质量',
             "percentage": "{:.2f}%".format(round(float(cp) / float(total_results) * 100))},
            {"value": tj, "name": '土建建设',
             "percentage": "{:.2f}%".format(round(float(tj) / float(total_results) * 100))},
            {"value": zy, "name": '资源配套(政府)',
             "percentage": "{:.2f}%".format(round(float(zy) / float(total_results) * 100))},
        ]
        return quality_category_list

    def question_category_unsolve(self, project_name):
        """
        按问题分类：各分类未完成数量
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT problem_category FROM quality_technics_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT qtm.problem_category FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND qtm.problem_end_time IS NULL"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return [
                {"value": 0, "name": '建设管理',
                 "percentage": "0.00%"},
                {"value": 0, "name": 'TB设计',
                 "percentage": "0.00%"},
                {"value": 0, "name": '乙供设备供货',
                 "percentage": "0.00%"},
                {"value": 0, "name": '乙供产品质量',
                 "percentage": "0.00%"},
                {"value": 0, "name": '土建建设',
                 "percentage": "0.00%"},
                {"value": 0, "name": '资源配套(政府)',
                 "percentage": "0.00%"},
            ]
        # 设备信息
        js = 0
        tb = 0
        gh = 0
        cp = 0
        tj = 0
        zy = 0
        for e in quality_list:
            if e.get("problem_category") == '建设管理':
                js += 1
            elif e.get("problem_category") == 'TB设计':
                tb += 1
            elif e.get("problem_category") == '乙供设备供货':
                gh += 1
            elif e.get("problem_category") == '乙供产品质量':
                cp += 1
            elif e.get("problem_category") == '土建建设':
                tj += 1
            elif e.get("problem_category") == '资源配套(政府)':
                zy += 1
        quality_category_list = [
            {"value": js, "name": '建设管理',
             "percentage": "{:.2f}%".format(round(float(js) / float(total_results) * 100))},
            {"value": tb, "name": 'TB设计',
             "percentage": "{:.2f}%".format(round(float(tb) / float(total_results) * 100))},
            {"value": gh, "name": '乙供设备供货',
             "percentage": "{:.2f}%".format(round(float(gh) / float(total_results) * 100))},
            {"value": cp, "name": '乙供产品质量',
             "percentage": "{:.2f}%".format(round(float(cp) / float(total_results) * 100))},
            {"value": tj, "name": '土建建设',
             "percentage": "{:.2f}%".format(round(float(tj) / float(total_results) * 100))},
            {"value": zy, "name": '资源配套(政府)',
             "percentage": "{:.2f}%".format(round(float(zy) / float(total_results) * 100))},
        ]
        return quality_category_list

    def question_impact_total(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM quality_technics_management WHERE project_name='{project_name}'"
        sql4 = "SELECT qtm.impact_classification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return [
                {"value": 0, "name": '进度',
                 "percentage": "0.00%"},
                {"value": 0, "name": '质量',
                 "percentage": "0.00%"},
                {"value": 0, "name": '合规',
                 "percentage": "0.00%"},
            ]
        quality_jd = 0
        quality_zl = 0
        quality_hg = 0
        for quality in quality_list:
            if quality.get("impact_classification") == "进度":
                quality_jd += 1
            elif quality.get("impact_classification") == "质量":
                quality_zl += 1
            elif quality.get("impact_classification") == "合规":
                quality_hg += 1
        quality_impact_list = [{"value": quality_jd, "name": '进度',
                                "percentage": "{:.2f}%".format(round(float(quality_jd) / float(total_results) * 100))},
                               {"value": quality_zl, "name": '质量',
                                "percentage": "{:.2f}%".format(round(float(quality_zl) / float(total_results) * 100))},
                               {"value": quality_hg, "name": '合规',
                                "percentage": "{:.2f}%".format(round(float(quality_hg) / float(total_results) * 100))}]
        return quality_impact_list

    def question_impact_unsolve(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = f"SELECT impact_classification FROM quality_technics_management WHERE project_name='{project_name}'" \
               "AND problem_end_time IS NULL"
        sql4 = "SELECT qtm.impact_classification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' AND qtm.problem_end_time IS NULL"
        quality_list = list(db.get_all(sql1)) + list(db.get_all(sql4))
        total_results = len(quality_list)
        if total_results == 0:
            return [
                {"value": 0, "name": '进度',
                 "percentage": "0.00%"},
                {"value": 0, "name": '质量',
                 "percentage": "0.00%"},
                {"value": 0, "name": '合规',
                 "percentage": "0.00%"},
            ]
        quality_jd = 0
        quality_zl = 0
        quality_hg = 0
        for quality in quality_list:
            if quality.get("impact_classification") == "进度":
                quality_jd += 1
            elif quality.get("impact_classification") == "质量":
                quality_zl += 1
            elif quality.get("impact_classification") == "合规":
                quality_hg += 1
        quality_impact_list = [{"value": quality_jd, "name": '进度',
                                "percentage": "{:.2f}%".format(round(float(quality_jd) / float(total_results) * 100))},
                               {"value": quality_zl, "name": '质量',
                                "percentage": "{:.2f}%".format(round(float(quality_zl) / float(total_results) * 100))},
                               {"value": quality_hg, "name": '合规',
                                "percentage": "{:.2f}%".format(round(float(quality_hg) / float(total_results) * 100))}]
        return quality_impact_list

    def total(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = "SELECT question_item,belong_professional,problem_category,problem_location,problem_description," \
               "problem_photo,rectification_reform_measures,problem_level,responsible_unit,problem_put_forward_time," \
               "problem_end_time,photos_after_rectification,problem_impact,impact_classification" \
               f" FROM quality_technics_management WHERE project_name='{project_name}'"
        sql2 = "SELECT qtm.question_item,qtm.belong_professional,qtm.problem_category,qtm.problem_location," \
               "qtm.problem_description,qtm.problem_photo,qtm.rectification_reform_measures,qtm.problem_level," \
               "qtm.responsible_unit,qtm.problem_put_forward_time,qtm.problem_impact, qtm.impact_classification," \
               "qtm.problem_end_time,qtm.photos_after_rectification FROM week_create_info wci " \
               "JOIN quality_technics_management qtm ON wci.ticketid = qtm.ticketid " \
               f"WHERE wci.project_name = '{project_name}' "
        # 列表合并
        problem_inquiry_list = list(db.get_all(sql1)) + list(db.get_all(sql2))
        # 计算总数
        total = len(problem_inquiry_list)
        if total == 0:
            return {
                "total_results": 0,
                "solved": 0,
                "unsolved": 0,
                "rate": "0.00%"
            }
        solved = 0
        unsolved = 0
        for i in problem_inquiry_list:
            if i.get('problem_end_time') is not None:
                solved += 1
            else:
                unsolved += 1
        data = {
            "total_results": int(total),
            "solved": solved,
            "unsolved": unsolved,
            "rate": "{:.2f}%".format(round((float(solved) / float(total) * 100), 2))
        }
        return data

    def responsible_unit(self):
        responsible_unit_list = []
        db = mysql.new_mysql_instance("tbconstruct")
        sql = f"SELECT field_value FROM project_member_basic_info WHERE field_name='所属服务商'"
        field_value_list = db.get_all(sql)
        if field_value_list:
            field_value = field_value_list[0].get("field_value").split(";")
            if type(field_value) == list:
                responsible_unit_list = [value for value in field_value if value]
        return responsible_unit_list
