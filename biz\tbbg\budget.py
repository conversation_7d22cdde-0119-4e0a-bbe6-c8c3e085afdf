# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: budget.py
# @time: 2023/3/6 14:29
# @描述: 【请添加描述】
import time
import datetime

from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.logone import logger
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql, config, exception


# 接收变更估算已经供应商
class ReceiveBudgetAndSupplier:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        logger.info("ReceiveBudgetAndSupplier-ServiceMethod-" + str(time.time()))
        # 流程流转
        flow.complete_task(self.ctx.task_id)


# 锁定预算
class LockBudget:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):

        # 查询星辰推送的信息

        # 竞争性综合评估/比价，已报备独家，指定品牌/供应商独家（未报备）
        requirement_id = self.ctx.variables.get("requirement_id")
        # tb_db = mysql.new_mysql_instance("tb_change")

        doaData = gnetops.request(action="TbChangeQuery", method="GetRealStarsOrderData",
                                  ext_data={"RequirementId": requirement_id})

        doa_type = doaData.get("DoaType")
        budget = doaData.get("ProjectAddup")

        # 将budget 转为浮点数
        budget = float("{:.2f}".format(float(budget)))

        # query_sql = "select * from stars_order where RequirementId = %s" % requirement_id
        # query_result = tb_db.query(query_sql)
        # if not query_result:
        #     raise exception.SDKException("查询星辰推送的信息失败")
        # stars_info = query_result[0]
        #
        # doa_type = stars_info.get("DoaType")
        # # budget = float(stars_info.get("ChangeBudget"))
        # # 将字符串转回浮点数
        # decoded = base64.b64decode(stars_info.get("ProjectAddup").encode()).decode()
        # budget = float("{:.2f}".format(float(decoded)))

        doa_audit_if = False
        if doa_type == "指定品牌/供应商独家（未报备）":
            if budget >= 1900000.00:
                doa_audit_if = True
        else:
            if budget >= 190000000.00:
                doa_audit_if = True

        # 获取当前时间，时间格式为2023-06
        current_time = time.strftime('%Y-%m', time.localtime(time.time()))
        # 触发gnetops接口锁定预算
        gnetops.request(action="TbChangeJoint", method="FreezeBudget", data={
            "RequirementId": self.ctx.variables.get("requirement_id"),
            "PeriodName": current_time,
            "CreatedBy": "dcops-auto",
            "Balance": budget,
        })

        # 判断result的Data的result是否为1，如果不为1，则抛出异常
        # if not result.get("Data").get("result") or result.get("Data").get("result") != 1:
        #     raise exception.LogicException("锁定预算异常")

        # 获取七彩石配置
        tb_auidit_users = config.get_config_map("tb_auidit_users")

        variables = {
            "doa_audit_if": 1 if doa_audit_if else 0,
            "tb_auidit_users": tb_auidit_users,
        }

        if doa_audit_if == False:
            variables["doa_check_gateway"] = 0

        requir_group = self.ctx.variables.get("requir_group")

        reuqir_chief_audit_user = tb_auidit_users["idc_audit_users"][requir_group]

        # 如果reuqir_chief_audit_user为空，则设置默认值为v_binjiesu
        if not reuqir_chief_audit_user:
            reuqir_chief_audit_user = "v_binjiesu"

        variables["reuqir_chief_audit_user"] = reuqir_chief_audit_user

        variables["budget_audit_user"] = tb_auidit_users["budget_audit_user"]
        variables["idc_chief_audit_user"] = tb_auidit_users["idc_chief_audit_user"]
        variables["idc_gm_audit_user"] = tb_auidit_users["idc_gm_audit_user"]
        variables["financial_audit_user"] = tb_auidit_users["financial_audit_user"]
        variables["doa_audit_user"] = tb_auidit_users["doa_audit_user"]

        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


# 预算审核
class BudgetAudit(AjaxTodoBase):
    def __init__(self):
        super(BudgetAudit, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        requirement_id = self.ctx.variables.get("requirement_id")
        # 获取第二天的时间，格式为2020-01-01
        tomorrow = (datetime.datetime.now() + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        tb_db = mysql.new_mysql_instance("tb_change")
        budget_audit_user = self.ctx.variables.get("budget_audit_user")

        tb_db.insert("requirement_cron", {
            "RequirementId": requirement_id,
            "TicketId": self.ctx.variables.get("TicketId"),
            "TaskId": self.ctx.task_id,
            "NotifyUser": budget_audit_user,
            "NotifyType": "mail",
            "Content": "您有一个来自TB变更的预算审核任务，请及时处理！",
            "Status": "wait",
            "CreateTime": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "NotifyDate": tomorrow,
            "TodoKey": "BudgetAudit"
        })

        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        idc_audit_list = [
            {
                "task": "预算审核",
                "audit_status": "通过" if audit_result else "不通过",
                "audit_opinion": audit_opinion,
                "audit_user": process_user,
                "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
                "todo_key": "BudgetAudit",
            }
        ]

        variables = {
            "idc_audit_list": idc_audit_list,
            "budget_audit_gateway": 1 if audit_result else 0,
        }
        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "BudgetAudit",
                     })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


class DoaCheck(AjaxTodoBase):
    def __init__(self):
        super(DoaCheck, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        variables = {
            "doa_check_gateway": 1 if process_data.get("doa_able") else 0,
        }

        if process_data.get("doa_able"):
            # 发送邮件
            dcops_domain = config.get_config_string("dcopsDomain")
            ticket_id = self.ctx.variables.get("ticket_id")
            msglines = []
            msglines.append("【TB变更】预算已经达到额定值，PM触发了DOA审批，点击链接即可查看详情")
            linkhref = f"http://{dcops_domain}/appManage/tickets/details?params={ticket_id}"
            # 将linkhref转换为超链接，拼接到msglines中
            msglines.append(f"工单链接：<a href='{linkhref}'>工单链接</a>")
            content = str.join("<br />", msglines)
            # 获取七彩石配置
            notify_user = config.get_config_string("DoaCheckNotifyUser")
            tof.send_email(sendTitle="TB变更DOA触发", msgContent=content, sendTo=[notify_user + "@tencent.com"])

        doa_able = process_data.get("doa_able")
        doa_reason = process_data.get("doa_reason")
        # 如果doa_able为False，则doa_reason为空
        if not doa_able:
            doa_reason = ""

        tb_db = mysql.new_mysql_instance("tb_change")
        requirement_id = self.ctx.variables.get("requirement_id")
        tb_db.update_by_id("requirement", {"DoaAble": doa_able, "DoaReason": doa_reason,
                                           "UpdateTime": time.strftime('%Y-%m-%d %H:%M:%S',
                                                                       time.localtime(time.time()))}, requirement_id)
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


if __name__ == '__main__':
    LockBudget().ServiceMethod()
