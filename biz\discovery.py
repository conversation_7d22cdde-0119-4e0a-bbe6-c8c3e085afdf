"""
服务发现相关业务逻辑
<AUTHOR>
@created 2020-8-11
"""

from iBroker.logone import logger

ALL_DISCOVER = []


def update_discovery(discovery):
    global ALL_DISCOVER
    logger.info(len(ALL_DISCOVER))
    ALL_DISCOVER = discovery


class ServiceDiscovery(object):

    def api_discovery(self) -> list:
        """
        获取所有被发现的API方法
        :return: API发现列表
        """
        # all_discovery, _ = discovery.service_discover("biz")
        logger.info(len(ALL_DISCOVER))
        return ALL_DISCOVER
