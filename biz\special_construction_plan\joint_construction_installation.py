import datetime
import re
import json
import uuid

from iBroker.lib.sdk import gnetops
from iBroker.logone import logger
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql
import pandas as pd
from openpyxl import load_workbook
from collections import defaultdict

from biz.construction_process.cos_lib import COSLib


def read_excel(file_path):
    # 使用 openpyxl 引擎读取 Excel 文件
    wb = load_workbook(filename=file_path, data_only=True)
    ws = wb.active

    # 提取项目名称
    project_name = ws['A1'].value

    # 解析合并单元格的表头（第二到三行为表头）
    header_rows = []
    for row in ws.iter_rows(min_row=2, max_row=3, values_only=True):
        header_rows.append(row)

    # 确保只对文本类型的列进行字符串连接
    headers = []
    for col in range(len(header_rows[0])):
        try:
            # 合并两行表头，并去除多余空格
            header = ' '.join(
                [str(header_rows[row][col]).strip() for row in range(2) if header_rows[row][col] is not None])
            headers.append(header.strip())
        except KeyError as e:
            raise KeyError(f"无法找到键值：{e}. 检查输入数据中的行和列索引是否正确。")
        except IndexError as e:
            raise IndexError(f"索引超出范围：{e}. 确保每一行都有足够的列。")
        except AttributeError as e:
            raise AttributeError(f"属性错误：{e}. 确保所有表头元素都是可转换为字符串的类型。")
        except TypeError as e:
            raise TypeError(f"类型错误：{e}. 确保输入数据结构正确且无 None 值。")

    # 读取所有数据行，跳过前三行
    data_rows = []
    for row in ws.iter_rows(min_row=3, values_only=True):
        data_rows.append(row)

    # 创建DataFrame
    df_all = pd.DataFrame(data_rows, columns=headers)

    # 打印原始列名以检查
    # print("Original column names after setting headers:", df_all.columns.tolist())
    # 设置表头对应字段映射
    column_names = {
        '序号': 'index',
        '工作内容': 'task',
        '工期': 'construction_time',
        '计划开始日期': 'planned_start_date',
        '计划完成日期': 'planned_end_date',
    }

    # 过滤并重命名我们关心的列
    df_filtered = df_all[list(column_names.keys())].rename(columns=column_names)
    return df_filtered, project_name


# 处理多级标题
def process_hierarchy(df_filtered):
    parent_map = {}
    hierarchy_data = []
    level_map = defaultdict(list)

    for index, row in df_filtered.iterrows():
        level = len(str(row['index']).split('.'))
        task_info = {
            'level': level,
            'index': row['index'],
            'task': row['task'],
            'construction_time': '' if pd.isna(row.get('construction_time')) else row.get('construction_time'),
        }

        # 使用 .dt.strftime() 方法格式化日期，同时处理空值
        start_time = row['planned_start_date'].strftime("%Y-%m-%d") \
            if pd.notnull(row['planned_start_date']) and isinstance(row['planned_start_date'],
                                                                    datetime.datetime) else ''
        completion_time = row['planned_end_date'].strftime("%Y-%m-%d") \
            if pd.notnull(row['planned_end_date']) and isinstance(row['planned_end_date'],
                                                                  datetime.datetime) else ''

        task_info.update({
            'planned_start_date': start_time,
            'planned_end_date': completion_time,
        })

        if level > 1:
            # 找到父级任务
            parent_index = '.'.join(str(row['index']).split('.')[:-1])
            parent_task = parent_map.get(parent_index)
            if parent_task:
                task_info['parent'] = parent_task['index']

        parent_map[row['index']] = task_info
        hierarchy_data.append(task_info)

    return pd.DataFrame(hierarchy_data)


# print(processed_df)


def prepare_level_map(df_filtered):
    level_map = defaultdict(list)

    for _, row in df_filtered.iterrows():
        if pd.isnull(row['task']) or str(row['task']).strip() == '示例说明':
            continue  # 跳过工作内容为空或值为“实例说明”的项

        level = int(row['level'])
        index = str(row['index'])  # 确保 index 是字符串类型
        task = str(row['task'])  # 确保 task 是字符串类型
        construction_time = '' if pd.isna(row['construction_time']) else row['construction_time']
        planned_start_date = row['planned_start_date'] \
            if isinstance(row['planned_start_date'], str) else ''
        planned_end_date = row['planned_end_date'] \
            if isinstance(row['planned_end_date'], str) else ''

        # 创建任务信息字典，确保所有值是可序列化的
        task_info = {
            'title': task,
            'serial_number': index,
            'work_content': [],  # 初始化空的工作内容列表
            'planned_start_date': planned_start_date,  # 计划开始日期
            'planned_end_date': planned_end_date,  # 计划结束日期
            'construction_time': construction_time,
        }

        # 将任务添加到对应层级的列表中
        level_map[level].append((index, task_info))

    return level_map


def add_to_hierarchy(level_map, parent_index=None, current_level=1):
    children = []
    max_level = max(level_map.keys()) if level_map else 0

    for idx, info in level_map[current_level]:
        if parent_index is None or (parent_index + '.') in idx:
            child_dict = {
                'title': info['title'],
                'serial_number': info['serial_number'],
                'planned_start_date': info['planned_start_date'],  # 包含计划开始日期
                'planned_end_date': info['planned_end_date'],  # 包含计划结束日期
                'construction_time': info['construction_time'],
                'work_content': []
            }
            if current_level < max_level:
                child_dict['work_content'] = add_to_hierarchy(level_map, idx, current_level + 1)
            else:
                child_dict['work_content'].append({
                    'title': info['title'],
                    'serial_number': info['serial_number'],
                    'planned_start_date': info['planned_start_date'],
                    'planned_end_date': info['planned_end_date'],
                    'construction_time': info['construction_time'],
                })
            children.append(child_dict)

    return children


def flatten_hierarchy(hierarchy, ticket_id, project_name, insert_data):
    for item in hierarchy:
        serial_number = item['serial_number']
        work_title = item['title']
        planned_start_date = item['planned_start_date']
        planned_end_date = item['planned_end_date']

        insert_data.append({
            'ticket_id': ticket_id,
            'project_name': project_name,
            'serial_number': serial_number,
            'work_content': work_title,  # 只记录当前级别的标题
            'start_time': planned_start_date,
            'completion_time': planned_end_date
        })

        # 如果有子任务，则递归处理子任务
        if item.get('work_content'):
            flatten_hierarchy(item['work_content'], ticket_id, project_name, insert_data)


class AnalysisOfInstallationConstructionPeopleInfo(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_people_info(self, project_name):
        """
        获取合建项目经理及总包、监理、PM人选
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql1 = ("SELECT account, role FROM project_role_account "
                "WHERE project_name = '%s' "
                "AND role IN ('合建方-项目经理','总包-项目经理','监理-总监理工程师')"
                "AND del_flag = '0'") % project_name
        query = db.get_all(sql1)
        hj = ''
        zb = ''
        jl = ''
        if query:
            for i in query:
                if i.get("role") == "合建方-项目经理":
                    hj = i.get("account")
                elif i.get("role") == "总包-项目经理":
                    zb = i.get("account")
                elif i.get("role") == "监理-总监理工程师":
                    jl = i.get("account")
        sql2 = f"SELECT campus,PM FROM risk_early_warning_data WHERE project_name = '{project_name}'"
        query2 = db.get_all(sql2)
        PM = ''
        if query2:
            PM = query2[0].get("PM")
            campus = query2[0].get("campus")
        variables = {
            'hj': hj,
            'zb': zb,
            'jl': jl,
            'PM': PM,
            'campus': campus
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def personnel_information_adaptation(self, hj, zb, jl, PM):
        """
            合建项目开办及设备进场施工流程人员信息适配
        """
        # 集成商对应总包
        integrator = zb
        # 项管对应合建方项管
        project_manager = hj
        # 监理
        supervision = jl
        PM = PM
        variables = {
            'integrator': integrator,
            'project_manager': project_manager,
            'supervision': supervision,
            'PM': PM
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class AnalysisOfInstallationConstructionMasterControlPlan(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def excel_data_save(self, file_url):
        """
        解析总控计划存库
        """
        ticket_id = self.ctx.variables.get("ticket id")
        relative_url = file_url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        # print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        # 读取Excel文件
        df_filtered, project_name = read_excel(exclpath)
        # 应用函数处理多级标题
        processed_df = process_hierarchy(df_filtered)
        # 应用函数处理多级标题并将结果以嵌套字典列表返回
        level_map = prepare_level_map(processed_df)
        project_name = self.ctx.variables.get("project_name")
        hierarchy = []
        top_level_tasks = add_to_hierarchy(level_map)
        for task in top_level_tasks:
            hierarchy.append(task)
        insert_data = []
        insert_info = ''
        if hierarchy:
            flatten_hierarchy(hierarchy, ticket_id, project_name, insert_data)
        if insert_data:
            try:
                tb_db = mysql.new_mysql_instance("tbconstruct")
                tb_db.begin()
                tb_db.insert_batch("excel_data", insert_data)
                tb_db.commit()
                insert_info = '写入成功'
            except ValueError as ve:
                insert_info = f'写入失败：{ve}'

        return insert_info
        # variables = {
        #     "insert_info":insert_info
        # }
        # flow.complete_task(self.ctx.task_id)

    def construct_hierarchy_bak(self, file_url):
        """
        获取安装施工工作项
        """
        ticket_id = self.ctx.variables.get("ticket id")
        relative_url = file_url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        # print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        exclpath = "/data/nbroker/" + new_file_name
        # 读取Excel文件
        df_filtered, project_name = read_excel(exclpath)
        # 应用函数处理多级标题
        processed_df = process_hierarchy(df_filtered)
        # 应用函数处理多级标题并将结果以嵌套字典列表返回
        level_map = prepare_level_map(processed_df)
        hierarchy = []
        top_level_tasks = add_to_hierarchy(level_map)
        for task in top_level_tasks:
            hierarchy.append(task)
        area_list = []
        for task in hierarchy:
            if task.get('title') == '安装施工':
                installation_construction_data = task.get('work_content')
                for item in installation_construction_data:
                    area_name = item.get('title')
                    area_work_list = (item.get('work_content'))
                    # print(json.dumps(area_work_list, ensure_ascii=False, indent=4))
                    for row in area_work_list:
                        single_work_list = []
                        for actual_work in row.get('work_content'):
                            work_name = actual_work.get('title')
                            single_work_list.append({
                                'work_type': row.get('title'),
                                'work_name': work_name
                            })
                            single_work_list[-1].update(actual_work.get('work_content')[0])
                        if single_work_list:
                            area_list.append({
                                'area_name': area_name,
                                'work_type': single_work_list[0].get('work_type'),
                                'area_work_list': single_work_list
                            })
        variables = {
            'ticket_id': ticket_id,
            'area_list': area_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def construct_hierarchy(self, project_name):
        sql = ("SELECT serial_number,work_content,start_time,completion_time,project_progress FROM construct_plan_data "
               f"WHERE project_name='{project_name}' AND not_involved='0'")
        db = mysql.new_mysql_instance("tbconstruct")
        query_data = db.get_all(sql)

        # 用于存放结果的列表
        area_list = []
        if query_data:

            # 查找所有属于"安装施工"（一级标题）下的二级标题，并构建area_list
            for item in query_data:
                parts = item['serial_number'].split('.')
                if len(parts) == 2:  # 只处理二级标题
                    parent_serial = parts[0]
                    # 找到对应的一级标题
                    for possible_parent in query_data:
                        if (possible_parent['serial_number'] == parent_serial and
                                possible_parent['work_content'] == '安装施工'):
                            # 初始化一个新的area对象
                            area_entry = {
                                'area_name': item['work_content'],  # 这里用二级标题作为area_name
                                'work_type': '',  # 在这个上下文中保持为空
                                'area_work_list': []
                            }

                            # 添加对应的三级及四级标题到area_work_list
                            for sub_item in query_data:
                                sub_parts = sub_item['serial_number'].split('.')
                                if len(sub_parts) >= 3 and '.'.join(sub_parts[:2]) == item['serial_number']:
                                    # 获取三级标题的工作内容
                                    work_type = sub_item['work_content']

                                    # 查找该三级标题下的所有四级标题
                                    for sub_sub_item in query_data:
                                        sub_sub_parts = sub_sub_item['serial_number'].split('.')
                                        if (len(sub_sub_parts) > 3 and '.'.join(sub_sub_parts[:3]) ==
                                                sub_item['serial_number']):
                                            area_entry['area_work_list'].append({
                                                'work_type': work_type,  # 使用三级标题作为work_type
                                                'work_name': sub_sub_item['work_content'],  # 四级标题的工作内容
                                                'planned_start_date': sub_sub_item['start_time'],  # 计划开始时间
                                                'planned_end_date': sub_sub_item['completion_time'],  # 计划结束时间
                                                'construction_time': sub_sub_item['project_progress']  # 计划工期
                                            })

                            # 如果area_work_list不为空，则添加到最终结果中
                            if area_entry['area_work_list']:
                                area_list.append(area_entry)
                            break
        variables = {
            'area_list': area_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def construct_create_tickets(self, project_name, area_list):
        """
        根据总控计划解析的区域建子单
        """
        hj = self.ctx.variables.get('hj')
        zb = self.ctx.variables.get('zb')
        jl = self.ctx.variables.get('jl')
        PM = self.ctx.variables.get('PM')
        f_instance_id = self.ctx.instance_id
        ticket_id_list = []
        for row in area_list:
            variables_map = {
                'project_name': project_name,
                'area_name': row.get('area_name'),
                'work_type': row.get('work_type'),
                'area_work_list': row.get('area_work_list'),
                'f_instance_id': f_instance_id,
                'hj': hj,
                'zb': zb,
                'jl': jl,
                'PM': PM
            }
            ticket_info = gnetops.create_ticket(
                flow_key='joint_area_implementation',
                description='该工单为："' + project_name + '-' + row.get('area_name')
                            + '-' + row.get('work_type') + '安装施工"',
                ticket_level=3,
                title=project_name + '-' + row.get('area_name') + '-' + row.get('work_type') + '安装施工"',
                creator='v_zongxiyu',
                concern="",  # 关注人, 这里暂为空
                deal='v_zongxiyu',  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list.append(ticket_info.get('TicketId'))
        variables = {
            'ticket_id_list': ticket_id_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def wait_area_tickets(self):
        """
        等待区域安装子单状态
        """
        ticket_id_list = self.ctx.variables.get('ticket_id_list')
        data = {
            "ResultColumns": {
                "CreateTime": "",
                "EndTime": "",
                "InstanceId": "",
                # 是否强制结单
                "IsForceEnd": "",
                # 流程定义标识
                "ProcessDefinitionKey": "",
                "ServiceRecoverTime": "",
                "StartProcessTime": "",
                # 单据状态[OPEN:运行中;END:已结束]
                "TicketStatus": "",
                "Title": "",
                "CustomRequestVarKV": "",
            },
            "SearchCondition": {'TicketId': ticket_id_list}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']
        cnt = 0  # 用于记录有多少个工单正在运行中
        for item in res_list:
            if item.get('TicketStatus') == 'OPEN':
                cnt += 1
        if cnt:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            WorkflowContinue(task_id=self.ctx.task_id).call()
            return {"success": True, "data": "所有子流程结束"}

    def area_tickets_data_back(self):
        """
        区域安装子单数据回写
        """
        area_name = self.ctx.variables.get('area_name')
        work_type = self.ctx.variables.get('work_type')
        area_work_list = self.ctx.variables.get('area_work_list')
        f_instance_id = self.ctx.variables.get('f_instance_id')
        area_list_dict = flow.get_variables(f_instance_id, ["area_list"])
        if area_list_dict:
            area_list = area_list_dict.get('area_list')
        else:
            return {'code': -1, 'msg': '未获取到area_list'}
        for row in area_list:
            if row.get('area_name') == area_name and row.get('work_type') == work_type:
                row["area_work_list"] = area_work_list
        WorkflowVarUpdate(
            instance_id=f_instance_id,
            variables={
                'area_list': area_list
            }
        ).call()
        flow.complete_task(self.ctx.task_id)

    def area_tickets_data_save(self, area_work_list):
        """
        区域安装子单数据存库
        """
        f_instance_id = self.ctx.variables.get('f_instance_id')
        ticket_id = self.ctx.variables.get('ticket_id')
        campus = self.ctx.variables.get('campus')
        project_name = self.ctx.variables.get('project_name')
        dcops_ticket_id = flow.get_variables(f_instance_id, ["ticket_id"]).get("ticket_id")
        insert_data = []
        for item in area_work_list:
            insert_data.append({
                "ticket_id": ticket_id,
                'work_name': item.get('work_name'),
                "work_type": item.get('work_type'),
                "planned_start_date": item.get('planned_start_date'),
                "planned_end_date": item.get('planned_end_date'),
                "construction_time": item.get('construction_time'),
                "complete_report": json.dumps(item.get('complete_report')),
                "campus": campus,
                "project_name": project_name,
                "dcops_ticket_id": dcops_ticket_id,
                "actual_start_time": item.get('actual_start_time'),
                "actual_finish_time": item.get('actual_finish_time'),
                "actual_construction_time": item.get('actual_construction_time'),
                "complete_photo": json.dumps(item.get('complete_photo')),
                "complete_report_review_report": json.dumps(item.get('complete_report_review_report')),
                "construction_time_deviation": item.get('construction_time_deviation'),
                "schedule_deviation": item.get('schedule_deviation'),
            })
        db = mysql.new_mysql_instance("tbconstruct")
        db.begin()
        db.insert_batch("joint_construction_project_installation", insert_data)
        db.commit()
        flow.complete_task(self.ctx.task_id)


class InstallationConstructionManagerReview(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        hj_review = process_data.get('hj_review')
        hj_rejection = process_data.get('hj_rejection')
        if hj_review == '通过':
            variables = {
                'hj_review': hj_review
            }
        else:
            if not hj_rejection:
                return {'code': -1, 'msg': '请输入驳回原因'}
            else:
                variables = {
                    'hj_review': hj_review,
                    'hj_rejection': hj_rejection
                }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class InstallationConstructionZBCommit(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        area_work_list = process_data.get('area_work_list')
        un_pack_item_list = []
        for item in area_work_list:
            if not item.get('actual_start_time'):
                un_pack_item_list.append(item.get('work_name'))
            if not item.get('actual_finish_time'):
                un_pack_item_list.append(item.get('work_name'))
            if not item.get('complete_report'):
                un_pack_item_list.append(item.get('work_name'))
            if not item.get('complete_photo'):
                un_pack_item_list.append(item.get('work_name'))
            if not item.get('complete_report_review_report'):
                un_pack_item_list.append(item.get('work_name'))
        if un_pack_item_list:
            return {'code': -1, 'msg': '以下工作项有内容未完成：' + ','.join(un_pack_item_list)}
        variables = {
            'area_work_list': area_work_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
