from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


# 待办转派信息设置
class TodoTransferConfig(AjaxTodoBase):
    def __init__(self):
        super(TodoTransferConfig, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        transfer_info_list = process_data.get("transfer_info_list")
        if not transfer_info_list:
            return {"code": -1, "msg": "请添加待办转派信息设置"}
        for item in transfer_info_list:
            # 原处理人
            if not item.get("origin_user"):
                return {"code": -1, "message": "原处理人不能为空"}
            # 转移处理人
            if not item.get("target_user"):
                return {"code": -1, "message": "转移处理人不能为空"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        variables = {
            "transfer_info_list": transfer_info_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)
