# -*- coding: utf-8 -*-
# @author: v_binjiesu
# @software: PyCharm
# @file: cron.py
# @time: 2023/6/1 15:25
# @描述: 【请添加描述】
import datetime
from iBroker.lib import mysql, config
from iBroker.lib.sdk import tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow, gnetops


class TbCron:
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def ServiceMethod(self):
        db = mysql.new_mysql_instance("tb_change")
        gnetops_db = mysql.new_mysql_instance("gnetops")
        # 查询推送任务
        mail_list = db.get_list("requirement_cron", None, {
            "Status": "wait",
            "NotifyType": "mail"
        })

        dcops_domain = config.get_config_string("dcopsDomain")
        for mail in mail_list:
            # 查询待办是否存在
            todo_data = gnetops_db.get_list("task_center_todo_runtime", None, {
                "TaskId": mail["TaskId"],
            }, "", "1")

            # 如果todo_data为空，说明待办不存在，跳过此次循环
            if not todo_data:
                print("没有数据")
                continue

            print("有数据")
            msglines = []

            reuqirementData = db.get_dictionary("requirement", None, conditions={
                "Id": mail["RequirementId"]
            })

            msglines.append("【" + reuqirementData["ChangeTitle"] + "】" + "变更需求已经提交，请及时审核：")
            linkhref = f"http://{dcops_domain}/appManage/tickets/details?params={mail['TicketId']}"
            # 将linkhref转换为超链接，拼接到msglines中
            msglines.append(f"工单链接：<a href='{linkhref}'>工单链接</a>")
            content = str.join("<br />", msglines)

            projectData = db.get_dictionary("tb_erp_project", None, conditions={
                "Id": reuqirementData["ProjectId"]
            })
            sendCopy = [projectData["Pm"] + "@tencent.com"]

            # 发送邮件
            tof.send_email(sendTitle="TB变更审核提醒", msgContent=content, sendTo=[mail["NotifyUser"] + "@tencent.com"],
                           sendCopy=sendCopy)
            # 获取提单人信息
            GreateData= db.get_dictionary("requirement", None, conditions={
                "Id": mail["RequirementId"]
            })
            # 给提单人发送邮件，邮件内容同上
            tof.send_email(sendTitle="TB变更审核提醒", msgContent=content,
                           sendTo=[GreateData["SubmitPeople"] + "@tencent.com"],
                           sendCopy=sendCopy)

            flow.complete_task(self.ctx.task_id)

    def hand_single(self, task_id: str):
        db = mysql.new_mysql_instance("tb_change")
        gnetops_db = mysql.new_mysql_instance("gnetops")
        dcops_domain = config.get_config_string("dcopsDomain")
        cron_data = db.get_dictionary("requirement_cron", None, {
            "TaskId": task_id,
            "Status": "wait"
        })

        todo_data = gnetops_db.get_list("task_center_todo_runtime", None, {
            "TaskId": task_id,
        }, "", "1")

        if not todo_data:
            return
        msglines = []
        reuqirementData = db.get_dictionary("requirement", None, conditions={
            "Id": cron_data["RequirementId"]
        })
        msglines.append("【" + reuqirementData["ChangeTitle"] + "】" + "变更需求已经提交，请及时审核：")
        linkhref = f"http://{dcops_domain}/appManage/tickets/details?params={cron_data['TicketId']}"
        # 将linkhref转换为超链接，拼接到msglines中
        msglines.append(f"工单链接：<a href='{linkhref}'>工单链接</a>")
        content = str.join("<br />", msglines)
        # 发送邮件
        tof.send_email(sendTitle="TB变更审核提醒", msgContent=content,
                       sendTo=[cron_data["NotifyUser"] + "@tencent.com"])

    def sync_data(self):
        gnetops.request(action="TbChangeJoint", method="SyncPurchaseOrder")
        gnetops.request(action="TbChangeJoint", method="SyncPayments")
        flow.complete_task(self.ctx.task_id)


if __name__ == '__main__':
    TbCron().hand_single("7ec8d25b-2212-11ee-8733-cef6cb5835dc")
