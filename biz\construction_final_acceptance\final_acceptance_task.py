import datetime

from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from biz.construction_application_process.template import TemplateAction


# 终验信息填写
class FinalAcceptanceInfoInput(AjaxTodoBase):
    def __init__(self):
        super(FinalAcceptanceInfoInput, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 获取数据
        info_dict = process_data.get("info_dict")
        final_files = info_dict.get("final_files")
        appendix_list = []
        # 判断文件是否上传成功
        file_exit_msg, file_exit_flag = TemplateAction.get_multiple_files_exit_flag(
            final_files, "附件上传"
        )
        if not file_exit_flag:
            return file_exit_msg
        for file in final_files:
            appendix_list.append(file["response"]["FileList"][0]["url"])
        # 获取验收po数据
        selected_switch = process_data.get("selected_switch")
        po_category_dict = process_data.get("po_category_dict")
        # 取终验实际完成时间
        date = datetime.datetime.strptime(
            info_dict.get("actual_finish_time"), "%Y-%m-%d"
        )
        real_time = date.strftime("%Y-%m-%d %H:%M:%S")
        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程变量回存
        variables = {
            "info_dict": info_dict,
            "appendix_list": appendix_list,
            "real_time": real_time,
            "selected_switch": selected_switch,
            "po_category_dict": po_category_dict,
        }
        # 扭转流程
        flow.complete_task(self.ctx.task_id, variables=variables)
