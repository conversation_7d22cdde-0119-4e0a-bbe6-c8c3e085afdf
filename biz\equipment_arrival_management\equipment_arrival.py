import datetime
from iBroker.lib.sdk import gnetops
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.lib import mysql
from biz.project_name import Database


class ProjectDataQuery(object):
    """
        项目数据查询
    """

    def __init__(self):
        super(ProjectDataQuery, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def project_name_query(self):
        """
            项目名称查询
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT campus, project FROM risk_early_warning_data"
        project_result = db.get_all(query_sql)
        data = []
        for row in project_result:
            campus = row["campus"]
            project = row["project"]
            data.append({'campus': campus, 'project_name': project})

        variables = {
            'data': data
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class UploadShippingInformation(AjaxTodoBase):
    """
        设备发货信息上传
    """

    def __init__(self):
        super(UploadShippingInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        insert_data = {}
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = process_data.get("campus", None)
        project = process_data.get('project', None)
        fh_remark = process_data.get("fh_remark", None)
        equipment_category = process_data.get('equipment_category', None)
        supplier = process_data.get('supplier', None)
        scheduled_delivery_time = process_data.get('scheduled_delivery_time', None)
        actual_delivery_time = process_data.get('actual_delivery_time', None)
        upload_shipping_information = process_data.get('upload_shipping_information', None)
        if upload_shipping_information and len(upload_shipping_information) > 0 and 'response' in \
                upload_shipping_information[0]:
            insert_data = {
                'upload_shipping_information': upload_shipping_information[0]["response"]["FileList"][0]["url"],
                'campus': campus,
                'project_name': project,
                'equipment_category': equipment_category,
                'supplier': supplier,
                'scheduled_delivery_time': scheduled_delivery_time,
                'actual_delivery_time': actual_delivery_time,
                'ticket_id': ticket_id
            }
        project_name = campus + project
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT project_name, role, account " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}'" \
                    f"and del_flag = 0"
        project_result = db.get_all(query_sql)
        account_dict = {}
        for row in project_result:
            for role in ['集成商-项目经理', '项管-项目经理', '监理-总监理工程师']:
                if role in row['role']:
                    account_dict[role] = row['account']
        integrator = account_dict.get('集成商-项目经理')
        item_tube = account_dict.get('项管-项目经理')
        supervision_management = account_dict.get('监理-总监理工程师')
        variables = {
            'upload_shipping_information': upload_shipping_information,
            'campus': campus,
            'project': project,
            'project_name': project_name,
            'equipment_category': equipment_category,
            'supplier': supplier,
            'scheduled_delivery_time': scheduled_delivery_time,
            'actual_delivery_time': actual_delivery_time,
            'integrator': integrator,
            'item_tube': item_tube,
            'supervision_management': supervision_management,
            'insert_data': insert_data,
            'fh_remark': fh_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class UploadReceivingInformation(AjaxTodoBase):
    """
        设备收货信息上传
    """

    def __init__(self):
        super(UploadReceivingInformation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        planned_arrival_time = process_data.get('planned_arrival_time', None)
        actual_arrival_time = process_data.get('actual_arrival_time', None)
        dh_remark = process_data.get('dh_remark', None)
        update_data = {}
        upload_receiving_information = process_data.get('upload_receiving_information', None)
        if upload_receiving_information and len(upload_receiving_information) > 0 and 'response' in \
                upload_receiving_information[0]:
            update_data = {
                'upload_receiving_information': upload_receiving_information[0]["response"]["FileList"][0]["url"],
                'planned_arrival_time': planned_arrival_time,
                'actual_arrival_time': actual_arrival_time,
            }
        variables = {
            'upload_receiving_information': upload_receiving_information,
            'planned_arrival_time': planned_arrival_time,
            'actual_arrival_time': actual_arrival_time,
            'update_data': update_data,
            'dh_remark': dh_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class AutomaticEquipmentArrival(object):
    """
        设备到货流程自动起单
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def arrival_goods_generate(self):
        db = mysql.new_mysql_instance("tbconstruct")
        fh_paper_info = db.get_list("equipment_arrival_information",
                                    fields=['serial_number', 'work_content', 'start_time', 'finish_time',
                                            'responsible_person', 'ticket_id'],
                                    conditions={})
        res_list = []
        current_time = datetime.datetime.now()
        current_date = current_time.date()
        for i in fh_paper_info:
            start_time = i.get('start_time')
            finish_time = i.get('finish_time')
            if isinstance(start_time, str):
                if start_time != '0000-00-00 00:00:00':
                    start_time = datetime.datetime.strptime(start_time, "%Y-%m-%d")
                else:
                    start_time = None  # 或者设置为其他默认值
            if isinstance(finish_time, str):
                if finish_time != '0000-00-00 00:00:00':
                    finish_time = datetime.datetime.strptime(finish_time, "%Y-%m-%d")
                else:
                    finish_time = None  # 或者设置为其他默认值
            if start_time is not None:
                three_days_before = (start_time - datetime.timedelta(days=3)).date()
                if current_date == three_days_before:
                    serial_number = i.get('serial_number')
                    if serial_number == '2.5' or serial_number == '2.6':
                        continue  # 跳过当前循环，不执行建单操作
                    if serial_number >= '2.5' and serial_number < '2.6':
                        processTitle = '集采设备' + f'{i.get("work_content")}' + '到货管理'
                    else:
                        processTitle = '乙供' + f'{i.get("work_content")}' + '到货管理'
                    data = {
                        "CustomVariables": {
                            "work_content": i.get('work_content', None),
                            # 'start_time': start_time.strftime("%Y-%m-%d") if start_time else None,
                            # 'finish_time': str(finish_time) if finish_time else None,
                            'responsible_person': i.get('responsible_person', None),
                        },
                        "ProcessDefinitionKey": "upload_equipment_arrival",
                        "Source": "",
                        "TicketDescription": "设备及方仓到货签收流程，包含发货资料审核上传、到货签收环节",
                        "TicketLevel": "3",
                        "TicketTitle": processTitle,
                        "UserInfo": {
                            "Concern": "keketan,youngshi,v_mmywang",
                            "Creator": "keketan,youngshi,v_mmywang",
                            "Deal": "keketan,youngshi,v_mmywang"
                        }
                    }
                    # 起单，并抛入data
                    res = gnetops.request(action="Ticket", method="Create", data=data)
                    res_list.append(res)

        variables = {
            'res_list': res_list
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeliveryDataAuditSupervision(AjaxTodoBase):
    """
        发货资料审批（监理）
    """

    def __init__(self):
        super(DeliveryDataAuditSupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fh_jl_approver = process_data.get('fh_jl_approver', None)
        fh_jl_remark = process_data.get('fh_jl_remark', None)

        variables = {
            'fh_jl_approver': fh_jl_approver,
            'fh_jl_remark': fh_jl_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeliveryDataAuditItemTube(AjaxTodoBase):
    """
        发货资料审批（项管）
    """

    def __init__(self):
        super(DeliveryDataAuditItemTube, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        fh_xg_approver = process_data.get('fh_xg_approver', None)
        fh_xg_remark = process_data.get('fh_xg_remark', None)
        insert_data = self.ctx.variables.get("insert_data")

        if fh_xg_approver == '同意':
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert("equipment_arrival_data", insert_data)
            tb_db.commit()

        variables = {
            'fh_xg_approver': fh_xg_approver,
            'fh_xg_remark': fh_xg_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ReceiptDataAuditSupervision(AjaxTodoBase):
    """
        收货资料审批（监理）
    """

    def __init__(self):
        super(ReceiptDataAuditSupervision, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dh_jl_approver = process_data.get('dh_jl_approver', None)
        dh_jl_remark = process_data.get('dh_jl_remark', None)

        variables = {
            'dh_jl_approver': dh_jl_approver,
            'dh_jl_remark': dh_jl_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ReceiptDataAuditItemTube(AjaxTodoBase):
    """
        收货资料审批（项管）
    """

    def __init__(self):
        super(ReceiptDataAuditItemTube, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        dh_xg_approver = process_data.get('dh_xg_approver', None)
        dh_xg_remark = process_data.get('dh_xg_remark', None)

        ticket_id = self.ctx.variables.get("ticket_id")
        update_data = self.ctx.variables.get("update_data")
        conditions = {
            'ticket_id': ticket_id
        }
        if dh_xg_approver == '同意':
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.update("equipment_arrival_data", update_data, conditions)
            tb_db.commit()

        variables = {
            'dh_xg_approver': dh_xg_approver,
            'dh_xg_remark': dh_xg_remark
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
