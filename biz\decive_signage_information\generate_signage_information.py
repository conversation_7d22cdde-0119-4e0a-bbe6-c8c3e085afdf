
from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from iBroker.lib import mysql


class DeviceAutomaticTasks(object):
    """
        设备标牌信息
    """

    def __init__(self):
        super(DeviceAutomaticTasks, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def trigger_the_generation(self):
        """
            触发生成标牌信息
        """

        variables = {
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def feedback_on_label_pasting(self):
        """
            标牌粘贴情况回传给deviceDB
        """
        variables = {
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def waiting_review_results(self):
        """
            等待deviceDB审核结果
        """
        variables = {
        }

        flow.complete_task(self.ctx.task_id, variables=variables)


class DeviceArchitectureConfirmation(AjaxTodoBase):
    """
        架构确认是否为最终版设备信息
    """

    def __init__(self):
        super(DeviceArchitectureConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        jg_confirm = process_data.get("jg_confirm")
        if jg_confirm == '是':
            variables = {
                "jg_confirm": jg_confirm
            }
        else:
            return {"code": -1, "msg": "不可结单"}

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DevicePMConfirmation(AjaxTodoBase):
    """
        PM确认生成的标牌信息
    """

    def __init__(self):
        super(DevicePMConfirmation, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        pm_confirm = process_data.get("pm_confirm")
        email_Cc = process_data.get("email_Cc")
        email_receiver = process_data.get("email_receiver")
        rich_text_template = process_data.get("rich_text_template")

        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"

        tof.send_email(sendTitle='设备标牌', msgContent=rich_text_template,
                       sendTo=email_receiver_list,
                       sendCopy=email_Cc_list)

        if pm_confirm == '正确':
            variables = {
                "pm_confirm": pm_confirm,
                'email_Cc': email_Cc,
                'email_receiver': email_receiver,
                'rich_text_template': rich_text_template,
                'email_Cc_list': email_Cc_list,
                'email_receiver_list': email_receiver_list
            }
        else:
            return {"code": -1, "msg": "不可结单"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeviceConfirmSign(AjaxTodoBase):
    """
            总包确认标牌信息
        """

    def __init__(self):
        super(DeviceConfirmSign, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        variables = {
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class DeviceStickingSigns(AjaxTodoBase):
    """
        总包粘贴标牌
    """

    def __init__(self):
        super(DeviceStickingSigns, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        signage_list = process_data.get("signage_list")
        unconfirmed = 0
        for signage in signage_list:
            if not signage.get("confirmed"):
                unconfirmed += 1
        if unconfirmed != 0:
            return {"code": -1, "msg": "请全部粘贴完成后再结单"}
        variables = {
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
