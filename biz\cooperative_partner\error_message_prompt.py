import datetime
import hashlib
import hmac
import json
import re
import time

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.notification.chatops import ChatOpsSend
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowVarUpdate
from iBroker.lib import mysql, curl, config


def MakeAuthorization(systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(algoName, timestamp,
                                                                           signature, systemId),
    }


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(title="Dcops请求外部接口",
                     system_name="Dcops",
                     url=url,
                     postdata=str_data,
                     append_header=headers,
                     timeout=60)
    result = resp.json()
    return result


class ErrorMessagePrompt(object):
    """
        错误信息提示
    """

    def __init__(self):
        super(ErrorMessagePrompt, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def error_message_prompt(self):
        supplier = self.ctx.variables.get("supplier")

        if supplier:
            variables = {
                "supplier": supplier
            }
        else:
            raise Exception('供应商不存在')

        flow.complete_task(self.ctx.task_id, variables=variables)


class CheckProgressWorkOrder(object):
    """
        检查工单进度
    """

    def tencent_work_order_progress(self, TencentTicketId):
        """
            查询腾讯工单进度
        """
        tencent_work_order_progress_data = []
        progress_count = 0
        data = {
            "ResultColumns": {
                "InstanceId": ""
            },
            "SearchCondition": {'TicketId': TencentTicketId}
        }

        extra_data = {"SchemaId": "ticket_base"}
        ticket_info = gnetops.request(
            action="QueryData", method="Run", data=data, ext_data=extra_data
        )
        res_list = ticket_info['List']

        for row in res_list:
            instance_id = row.get('InstanceId')

        data_instanceId = {
            "InstanceId": instance_id
        }
        # 请求获取工作流日志
        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)
        for row in res:
            if row.get('TaskStatus') == '进行中':
                tencent_work_order_progress_data.append({
                    "CurrentNode": row.get("TaskName"),
                    "Processor": row.get('Operator')
                })
                progress_count += 1
            elif row.get('TaskStatus') is not None:  # 处理其他非空状态的情况
                pass
        if progress_count > 0:
            return {
                "code": 0,
                "msg": "查询成功",
                "data": tencent_work_order_progress_data
            }
        elif progress_count == 0:
            return {
                "code": -1,
                "msg": "查询失败,工单中无进行中的节点"
            }

    def cooperative_partner_work_order_progress(self, Facilitator, system_id, system_type, PartnerTicketId):
        """
            查询合作伙伴工单进度
        """
        cooperative_partner_work_order_progress_data = {
            "PartnerTicketId": PartnerTicketId  # 合作伙伴工单号
        }
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = 'create_order'
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index"  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "cooperative_partner_work_order_progress",  # (*必填) 云函数名称
                        "data": cooperative_partner_work_order_progress_data,  # 传递给云函数的入参
                        "systemId": f"{system_id}"  # 为服务商分配的systemId，用于区分服务商云函数
                    }
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task"
            }
            info = calling_external_interfaces(interface_info, data)
            return info
        elif system_type == "外部":
            info = gnetops.request(
                action='Request',
                method='RequestToFacilitator',
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": cooperative_partner_work_order_progress_data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/cooperative_partner_work_order_progress"
                }
            )
            return info

    def test_work_order_progress(self):
        """
            测试工单进度
        """
        pass
