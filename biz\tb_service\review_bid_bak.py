#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import copy
import datetime
import json
import os
import re
import time
import uuid

import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow, gnetops, tof
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue, WorkflowVarUpdate

from biz.common.workflow_tools import cycle_task_abort
from biz.common.cycle_task_tools import ticket_is_over
from biz.construction_process.cos_lib import COSLib


def disagree(process_data, variables) -> dict:
    # 公共方法 -- 被’驳回‘时 更新流程变量cur_rater
    variables_map = {}
    check_remark = process_data.get("check_remark", '无')
    project_name = variables.get('project_name')
    # bid_info = variables.get('bid_info')
    msg_content = (
            '项目："' + project_name + '"被驳回, 请重新评分， 评分入口为：https://dcops.test.woa.com/appManage/tickets/needToDo\n'
            + '驳回原因：' + check_remark)
    # 更新流程变量cur_rater，评标人员重新评分
    # bid_info.update({'msg_content': msg_content})

    rater_list = variables.get('rater_list', [])
    variables_map.update({'cur_rater': rater_list[0], 'msg_content': msg_content})
    return variables_map


def create_msg_content(project_name, pass_or_not='同意', operation_type='审核', reject_reason='') -> str:
    """
    生成通知信息的内容
    :param pass_or_not: 同意 or 驳回
    :param project_name: 项目名
    :param operation_type: 操作类型， 审核 or 评分 or 审批
    :param reject_reason: 驳回原因
    :return:
    """
    if pass_or_not == '同意':
        msg_content = '请尽快对 "' + project_name + '" ' + operation_type + ', ' + operation_type + \
                      '入口为：https://dcops.test.woa.com/appManage/tickets/needToDo'
    else:
        msg_content = (
                '项目："' + project_name + '"被驳回, 请重新评分， 评分入口为：https://dcops.test.woa.com/appManage/tickets/needToDo\n'
                + '驳回原因：' + reject_reason
        )
    return msg_content


def create_msg_content2(pass_or_not, project_name, reject_reason='') -> dict:
    """
    生成通知信息的内容
    :param pass_or_not: 同意 or 驳回
    :param project_name: 项目名
    :param reject_reason: 驳回原因
    :return:
    """
    # 信息内容
    variables_map = {}
    if pass_or_not == '同意':
        msg_content = create_msg_content(pass_or_not=pass_or_not, project_name=project_name, operation_type='审批')
    elif pass_or_not == '驳回':
        msg_content = create_msg_content(pass_or_not=pass_or_not, project_name=project_name, operation_type='评分',
                                         reject_reason=reject_reason)
        # # 被’驳回‘时 需要更新流程变量cur_rater
        # variables_map.update({'cur_rater': cur_rater, 'msg_content': msg_content})
    variables_map.update({'msg_content': msg_content})
    return variables_map


def cycle_is_over(ticket_id_list: list, task_id):
    flag = ticket_is_over(ticket_id_list)  # flag为未结束的工单的数量
    if flag:
        return {"success": False, "data": "尚有子流程未结束"}
    else:
        WorkflowContinue(task_id=task_id).call()
        return {"success": True, "data": "所有子流程结束"}


class SelectReviewerTodoOne(AjaxTodoBase):
    # 指定评标小组

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 指定评标人、初审人、供应商
        variables_map, people_info = {}, {}  # people_info 保存 供应商名单和评标人名单
        project_name = process_data.get("project_name", '')  # 项目名称
        project_desc = process_data.get("project_desc", '')  # 项目概况
        rater_str = process_data.get("raters", '')  # 评标人
        rater_list = rater_str.split(';')
        rater_list = [item for item in rater_list if item]
        rater_leader = process_data.get("rater_leader", '')  # 评审组长 即初审人
        rater_list.append(rater_leader)
        # 防止用户在评分人的输入框中也将评审组长输入 对列表去重
        rater_list = list(set(rater_list))
        result = ';'.join(rater_list)
        suppliers = process_data.get('suppliers', '')
        # 创建新的供应商列表
        technical_score_summary = []
        for index, manufacturer in enumerate(suppliers):
            supplier_key = f"supplier_{chr(97 + index)}"  # chr(97) 是 'a'
            technical_score_summary.append({
                "supplier_key": supplier_key,
                "manufacturers": manufacturer,
            })
        supplier_list = process_data.get('suppliers', [])
        # 过滤用户输入  保证供应商名单 非空，前后不含空格
        supplier_list = [item.strip() for item in supplier_list if item]
        ybpf_table = process_data.get('ybpf_table')
        # 遍历 ybpf_table，更新 weight 字段
        for item in ybpf_table:
            if isinstance(item['weight'], (int, float)):  # 检查 weight 是否为数字
                item['weight'] = f"{int(item['weight'] * 100)}%"  # 转换为整数并加上 %
        ybpf_table.append({
            "eval_content": "",
            "first_level": "",
            "project": "合计",
            "second_level": "",
            "third_level": "",
            "weight": "0%"
        })
        # msg_content = create_msg_content(project_name=project_name, operation_type='评分')
        people_info.update({'rater_list': rater_list, 'supplier_list': supplier_list})
        variables_map.update({
            'people_info': people_info,
            # 'msg_content': msg_content,
            'project_name': project_name,
            'project_desc': project_desc, 'rater_leader': rater_leader, 'suppliers': suppliers, 'raters': rater_str,
            'ybpf_table': ybpf_table, 'technical_score_summary': technical_score_summary
        })
        variables_map.update({
            'result': result
        })

        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidLoopCreateTicketOne(object):
    """
    循环建单 以达到多人并行打分
    """

    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def loop_item(self, people_info: dict, flow_key: str):
        """
        flow_key 传入创建的子流程的 流程标识
        title 子流程的标题
        """
        project_name = self.ctx.get_var('project_name', '')
        project_desc = self.ctx.get_var('project_desc', '')
        rater_list = people_info.get('rater_list', [])
        rater_cnt = len(rater_list)
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        msg_content = self.ctx.get_var('msg_content')
        ybpf_table = self.ctx.get_var('ybpf_table')
        ybpf_first_file_url = self.ctx.get_var('ybpf_first_file_url')
        technical_score_summary = self.ctx.get_var('technical_score_summary')
        import_table_head = self.ctx.get_var('import_table_head')
        f_instance_id = self.ctx.instance_id
        variables_map = {
            'project_name': project_name,
            'project_desc': project_desc,
            'ybpf_table': ybpf_table,
            'technical_score_summary': technical_score_summary,
            'people_info': people_info,
            'msg_content': msg_content,
            'supplier_cnt': supplier_cnt,
            'rater_cnt': rater_cnt,
            'f_instance_id': f_instance_id,
            "ybpf_first_file_url": ybpf_first_file_url,
            "import_table_head": import_table_head
        }
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})

        for i in range(supplier_cnt):
            # 生成流程变量
            variables_map.update({'supplier_' + chr(ord('a') + i) + "_name": supplier_list[i]})
        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_zongxiyu')
        # 用于保存创建的工单的单号
        ticket_id_list = []

        for rater in rater_list:
            variables_map.update({'cur_rater': rater})
            people_table = self.ctx.get_var(f"{rater}_ybpf_table")
            if people_table:
                ybpf_table = people_table[0]
                ybpf_table.append({"project": "合计"})
                variables_map.update({'ybpf_table': ybpf_table})
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '的具体的评分功能"服务',
                ticket_level=3,
                title=project_name + ' - 评分',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=rater,  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list.append(ticket_info.get('TicketId'))

        # 门户页面库数据添加
        db = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT tender_evaluation FROM procurement_process_information "
               f"WHERE project_name='{project_name}'")
        conditions = {"project_name": project_name}
        procurement_portal_error_message = ''
        query = db.get_row(sql)
        if query:
            all_ticket_id = json.loads(query.get('tender_evaluation', ''))
            if all_ticket_id.get("children_ticket_id", []):
                all_ticket_id["children_ticket_id"].extend(ticket_id_list)
            else:
                all_ticket_id["children_ticket_id"] = ticket_id_list
            update_data = {"tender_evaluation": json.dumps(all_ticket_id)}
            db.begin()
            db.update("procurement_process_information", conditions=conditions, data=update_data)
            db.commit()
        else:
            procurement_portal_error_message = "招采门户数据更新失败，请手动补充"

        variables_map.update({'ticket_id_list': ticket_id_list,
                              'procurement_portal_error_message': procurement_portal_error_message})
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    # def wait_mark(self):
    #     """
    #     等待所有人员评分完成
    #     """
    #     rater_list = self.variables.get('rater_list', [])
    #     flag = False
    #     for item in rater_list:
    #         # 判断子流程是否结束，如果流程已结束，便结束定时任务
    #         c_instance_id = self.variables.get(item + 'c_instance_id', '')
    #         if (not c_instance_id) or (not flow.is_instance_end(instance_id=c_instance_id)):
    #             flag = True
    #             continue
    #
    #     if flag:
    #         return {"success": False, "data": "尚有子流程未结束"}
    #     else:
    #         WorkflowContinue(task_id=self.ctx.task_id).call()
    #         return {"success": True, "data": "所有子流程结束"}
    @cycle_task_abort
    def wait_mark(self):
        """
        等待所有人员评分完成
        """
        ticket_id_list = self.variables.get('ticket_id_list', [])
        task_id = self.ctx.task_id
        cycle_is_over(ticket_id_list, task_id)

    def sort_to_score(self):
        """
        对各个供应商的得分 进行排序
        :return:
        """
        people_info = self.ctx.get_var('people_info', {})
        rater_list = people_info.get('rater_list', [])
        rater_cnt = len(rater_list)
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        raters = []
        for i in rater_list:
            rater = self.ctx.get_var(f'{i}')

            raters.append({
                f"{i}": rater
            })

        # 提取时间并排序
        sorted_a = sorted(raters, key=lambda x: datetime.datetime.strptime(list(x.values())[0], '%Y-%m-%d %H:%M'))

        # 存放结果的列表
        result = []

        # 为每个字典分配顺序编号
        for key, item in enumerate(sorted_a, start=1):
            # 获取字典的键
            value = list(item.keys())[0]
            # 将结果添加到列表中
            result.append({f"rater_{key}": value})

        # 修改_ybpf_table的key
        # 遍历 result 列表
        ybpf_gather = []
        for rater in result:
            # 获取 rater 的值
            rater_key = list(rater.values())[0]
            new_remark_key = f"{list(rater.keys())[0]}_remark"  # 'rater_1_remark'
            ybpf_name = self.ctx.get_var(rater_key + '_ybpf_table')
            # 遍历 v_mmywang_ybpf_table
            for table in ybpf_name[0]:  # 只处理第一个子列表
                if table['cur_rater'] == rater_key:
                    # 修改 rater_remark 为新的键
                    table[new_remark_key] = table.pop('rater_remark')

            ybpf_gather.append({
                rater_key + '_ybpf_table': ybpf_name
            })

        # 修改_mspf_table的key
        # 遍历 result 列表
        mspf_gather = []
        for rater in result:
            # 获取 rater 的值
            rater_key = list(rater.values())[0]
            new_remark_key = f"{list(rater.keys())[0]}_remark"  # 'rater_1_remark'
            mspf_name = self.ctx.get_var(rater_key + '_mspf_table')
            # 遍历 v_mmywang_ybpf_table
            for table in mspf_name[0]:  # 只处理第一个子列表
                if table['cur_rater'] == rater_key:
                    # 修改 rater_remark 为新的键
                    table[new_remark_key] = table.pop('rater_remark')

            mspf_gather.append({
                rater_key + '_mspf_table': mspf_name
            })

        # 汇总数据 ybpf_table 的评分数据
        ybpf_table = [[] for i in range(supplier_cnt)]  # 第i个下标， 保存供应商（A+i）的评分情况
        invalid_bid_idx1 = []
        for item in rater_list:
            for row in ybpf_gather:
                ybpf_info = row.get(item + '_ybpf_table')
                table = ybpf_info[0]  # 获取评分表
                ybpf_temp_table = []  # 保存该评分人的打分情况
                invalid_idx = ybpf_info[1]  # 获取废标的下标
                invalid_bid_idx1 += invalid_idx
                for entry in table:
                    row = []  # 保存某个评分表中的一行
                    for i in range(supplier_cnt):
                        row.append(int(entry['supplier_' + chr(ord('a') + i)]))  # 访问每个供应商的评分
                    row.append(int(entry['weight'][0:-1]) / 100)  # 获取权重  （string类型，如5%, 去掉%）
                    ybpf_temp_table.append(row)
                ybpf_temp_table = list(map(list, zip(*ybpf_temp_table)))  # 行列转置，[[1, 2],[3, 4]] -> [[1, 3], [2, 4]]
                for i in range(supplier_cnt):
                    ybpf_table[i].append(ybpf_temp_table[i])
                # ybpf_temp_table的最后一列是权重
                ybpf_table.append(ybpf_temp_table[-1])
        invalid_bid_idx1 = list(set(invalid_bid_idx1))

        # 汇总数据 mspf_table 的评分数据
        mspf_table = [[] for i in range(supplier_cnt)]  # 第i个下标， 保存供应商（A+i）的评分情况
        invalid_bid_idx2 = []
        for item in rater_list:
            for row in mspf_gather:
                mspf_info = row.get(item + '_mspf_table')
                table = mspf_info[0]  # 获取评分表
                mspf_temp_table = []  # 保存该评分人的打分情况
                invalid_idx = mspf_info[1]  # 获取废标的下标
                invalid_bid_idx2 += invalid_idx
                for entry in table:
                    row = []  # 保存某个评分表中的一行
                    for i in range(supplier_cnt):
                        row.append(int(entry['supplier_' + chr(ord('a') + i)]))  # 访问每个供应商的评分
                    row.append(int(entry['weight'][0:-1]) / 100)  # 获取权重  （string类型，如5%, 去掉%）
                    mspf_temp_table.append(row)
                mspf_temp_table = list(map(list, zip(*mspf_temp_table)))  # 行列转置，[[1, 2],[3, 4]] -> [[1, 3], [2, 4]]
                for i in range(supplier_cnt):
                    mspf_table[i].append(mspf_temp_table[i])
                # mspf_temp_table的最后一列是权重
                mspf_table.append(mspf_temp_table[-1])
        invalid_bid_idx2 = list(set(invalid_bid_idx2))

        # 汇总technical_score_summary数据
        # 创建一个字典来存储共同的 manufacturers 的数据
        combined_scores = {}
        technical_score_summary = []
        # 遍历第一个列表
        for item in rater_list:
            hz_info = self.ctx.get_var(item + '_technical_score_summary')
            for row in hz_info:
                manufacturers = row.get("manufacturers")
                interview_score = float(row.get("interview_score"))
                response_score = float(row.get("response_score"))

                if manufacturers not in combined_scores:
                    combined_scores[manufacturers] = {
                        "interview_score": interview_score,
                        "response_score": response_score,
                        "count": 1  # 计数器，用于计算平均值
                    }
                else:
                    combined_scores[manufacturers]["interview_score"] += interview_score
                    combined_scores[manufacturers]["response_score"] += response_score
                    combined_scores[manufacturers]["count"] += 1
        # 计算平均值并生成新的列表
        for manufacturers, scores in combined_scores.items():
            if scores["count"] > 1:  # 只考虑共同的 manufacturers
                avg_interview_score = round(scores["interview_score"] / scores["count"], 2)
                avg_response_score = round(scores["response_score"] / scores["count"], 2)

                # 计算 skill_score
                skill_score = round(avg_response_score * 0.7 + avg_interview_score * 0.3, 2)

                technical_score_summary.append({
                    "interview_score": avg_interview_score,
                    "manufacturers": manufacturers,
                    "response_score": avg_response_score,
                    "skill_score": skill_score,  # 根据公式计算的 skill_score
                })
        # 提取所有技术评分
        skill_scores = [entry['skill_score'] for entry in technical_score_summary]

        # 计算最低和最高技术评分
        min_skill_score = min(skill_scores)
        max_skill_score = max(skill_scores)

        # 计算每个制造商的技术分
        for entry in technical_score_summary:
            skill_score = entry['skill_score']
            if max_skill_score != min_skill_score:  # 避免除以零
                technical_score = ((skill_score - min_skill_score) * 100) / (max_skill_score - min_skill_score)
            else:
                technical_score = 0  # 如果所有技术评分相同，技术分为0

            # 将技术分添加到字典中
            entry['technical_score'] = technical_score

        # 对技术分列表进行排序
        technical_score_summary = sorted(technical_score_summary, key=lambda x: x['skill_score'])

        self.workflow_detail.add_kv_table(name='评分temp_table', kv_table={
            'ybpf_temp_table': ybpf_temp_table,
            'mspf_temp_table': mspf_temp_table
        })

        ybpf_supplier_avg = []  # 供应商的加权平均分
        ybpf_remark_list = []
        for i in range(supplier_cnt):
            # 计算表格中的 平均值
            temp_sum = list(map(sum, zip(*ybpf_table[i])))
            avg_data = list(map(lambda x: round(x / rater_cnt, 2), temp_sum))  # 平均值
            ybpf_table[i].append(avg_data)

            # 计算 方差 和 加权平均值， 将废标的加权平均值设为 -1
            ybpf_table_length = len(avg_data)
            if i in invalid_bid_idx1:
                diff_data = ['废标' for _ in range(ybpf_table_length)]
                ybpf_supplier_avg.append(-1)  # 加权平均分
            else:
                temp_min = list(map(min, zip(*ybpf_table[i])))
                temp_max = list(map(max, zip(*ybpf_table[i])))
                # 计算差异并格式化结果，去掉 .0
                diff_data = list(map(lambda x, y: f"{int(round((y - x) / x * 100, 0))}%", temp_min, temp_max))
                temp = list(map(lambda x, y: x * y, avg_data, ybpf_table[-1]))
                ybpf_supplier_avg.append(round(sum(temp), 2))  # 加权平均分
            for entry in table:
                for j in range(rater_cnt):
                    remark = entry[f'rater_{str(j + 1)}_remark']
                ybpf_remark_list.append(remark)
            ybpf_table[i].append(diff_data)
            ybpf_table[i].append(ybpf_remark_list)

        mspf_supplier_avg = []  # 供应商的加权平均分
        mspf_remark_list = []
        for i in range(supplier_cnt):
            # 计算表格中的 平均值
            temp_sum = list(map(sum, zip(*mspf_table[i])))
            avg_data = list(map(lambda x: round(x / rater_cnt, 2), temp_sum))  # 平均值
            mspf_table[i].append(avg_data)

            # 计算 方差 和 加权平均值， 将废标的加权平均值设为 -1
            mspf_table_length = len(avg_data)
            if i in invalid_bid_idx2:
                diff_data = ['废标' for _ in range(mspf_table_length)]
                mspf_supplier_avg.append(-1)  # 加权平均分
            else:
                temp_min = list(map(min, zip(*mspf_table[i])))
                temp_max = list(map(max, zip(*mspf_table[i])))
                # 计算差异并格式化结果，去掉 .0
                diff_data = list(map(lambda x, y: f"{int(round((y - x) / x * 100, 0))}%", temp_min, temp_max))
                temp = list(map(lambda x, y: x * y, avg_data, mspf_table[-1]))
                mspf_supplier_avg.append(round(sum(temp), 2))  # 加权平均分
            for entry in table:
                for j in range(rater_cnt):
                    remark = entry[f'rater_{str(j + 1)}_remark']
                mspf_remark_list.append(remark)
            mspf_table[i].append(diff_data)
            mspf_table[i].append(mspf_remark_list)

        ybpf_temp_list = []
        for i in range(supplier_cnt):
            ybpf_temp_list.append(list(map(list, zip(*ybpf_table[i]))))
        ybpf_import_table = [[] for _ in range(ybpf_table_length)]

        mspf_temp_list = []
        for i in range(supplier_cnt):
            mspf_temp_list.append(list(map(list, zip(*mspf_table[i]))))
        mspf_import_table = [[] for _ in range(mspf_table_length)]

        # 获取 ybpf_table 和 mspf_table
        ybpf_list = self.ctx.get_var('ybpf_table')
        mspf_list = self.ctx.get_var('mspf_table')

        # 从 ybpf_table 中提取数据并填充 import_table
        for j in range(ybpf_table_length):
            ybpf_temp_item = [
                ybpf_list[j]['stage'],
                ybpf_list[j]['project'],
                ybpf_list[j]['weight'],
                ybpf_list[j]['eval_content'],
                ybpf_list[j]['first_level'],
                ybpf_list[j]['second_level'],
                ybpf_list[j]['third_level'],
                ybpf_list[j]['four_level'],
                ybpf_list[j]['rater_remark']
            ]
            ybpf_import_table[j].extend(ybpf_temp_item)

        # 从 mspf_table 中提取数据并填充 import_table
        for j in range(mspf_table_length):
            mspf_temp_item = [
                mspf_list[j]['stage'],
                mspf_list[j]['project'],
                mspf_list[j]['weight'],
                mspf_list[j]['eval_content'],
                mspf_list[j]['first_level'],
                mspf_list[j]['second_level'],
                mspf_list[j]['third_level'],
                mspf_list[j]['four_level'],
                mspf_list[j]['rater_remark']
            ]
            mspf_import_table[j].extend(mspf_temp_item)
        for i in range(supplier_cnt):
            for j in range(ybpf_table_length):
                ybpf_import_table[j].extend(ybpf_temp_list[i][j])

        for i in range(supplier_cnt):
            for j in range(mspf_table_length):
                mspf_import_table[j].extend(mspf_temp_list[i][j])

        # 添加三个空行
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        # 添加三个空行
        mspf_import_table.append([])
        mspf_import_table.append([])
        mspf_import_table.append([])
        mspf_import_table.append([])
        # 表格中的最后两行特别处理
        ybpf_import_table[-2].append('**说明信息**')
        ybpf_supplier_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        ybpf_rater_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        for i in range(supplier_cnt):
            ybpf_supplier_str = ybpf_supplier_str + chr(ord('a') + i) + '是' + supplier_list[i] + '； '
        for item in result:
            for key, value in item.items():
                ybpf_rater_str = ybpf_rater_str + f"{key} 是 {value}； "
        ybpf_import_table[-2].append(ybpf_supplier_str)
        ybpf_import_table[-1].append(ybpf_rater_str)

        # 表格中的最后两行特别处理
        mspf_import_table[-2].append('**说明信息**')
        mspf_supplier_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        mspf_rater_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        for i in range(supplier_cnt):
            mspf_supplier_str = mspf_supplier_str + chr(ord('a') + i) + '是' + supplier_list[i] + '； '
        for item in result:
            for key, value in item.items():
                mspf_rater_str = mspf_rater_str + f"{key} 是 {value}； "
        mspf_import_table[-2].append(mspf_supplier_str)
        mspf_import_table[-1].append(mspf_rater_str)

        # 生成json数据格式 用于表单导出
        keys = ['stage', 'project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level',
                'four_level', 'rater_remark']
        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                keys.append('rater_' + str(j + 1) + chr(ord('a') + i))  # rater_1a

            keys.append('avg_' + chr(ord('a') + i))  # avg_a
            keys.append('dif_' + chr(ord('a') + i))  # dif_a
            keys.append('rater_' + str(j + 1) + '_remark')  # rater_1_remark

        ybpf_data_json = []
        for i in range(ybpf_table_length + 4):
            ybpf_data_json.append(dict(zip(keys, ybpf_import_table[i])))
        mspf_data_json = []
        for i in range(mspf_table_length + 4):
            mspf_data_json.append(dict(zip(keys, mspf_import_table[i])))

        # 偏差信息表格
        deviation_information = []
        supplier = []
        data = []
        for i, row in enumerate(supplier_list):
            key = chr(ord('a') + i)  # 'a' for 本贸, 'b' for 德恒
            supplier.append({
                key: row
            })

        # 去除空字典
        ybpf_data_json_new = [item for item in ybpf_data_json if item]  # 过滤掉空字典
        mspf_data_json_new = [item for item in mspf_data_json if item]  # 过滤掉空字典

        # 去除最后一个字典
        ybpf_data_json_new = ybpf_data_json_new[:-1]  # 去掉最后一个字典
        mspf_data_json_new = mspf_data_json_new[:-1]  # 去掉最后一个字典

        # 合并两个列表
        combined_data = ybpf_data_json_new + mspf_data_json_new

        for row in combined_data:
            for i in range(supplier_cnt):
                dif = row.get('dif_' + chr(ord('a') + i))
                if dif is not None:
                    # 确保 dif 是字符串类型
                    if isinstance(dif, str):
                        # 去掉 '%' 并转换为浮点数
                        dif_value = float(dif.replace('%', ''))
                    elif isinstance(dif, (int, float)):
                        # 如果 dif 是 int 或 float，直接使用它
                        dif_value = float(dif)  # 转换为浮点数
                    else:
                        continue  # 如果 dif 不是字符串、int 或 float，跳过
                    if dif_value > 15:
                        stage = row.get('stage')
                        eval_content = row.get('eval_content')

                        # 收集当前 supplier 的分数和评委名称
                        for j in range(rater_cnt):
                            str01 = chr(ord('a') + i)
                            str02 = str(j + 1)
                            rater = f"rater_{str02}{str01}"
                            number = row.get(rater)

                            # 只添加非 None 的 number
                            if number is not None:
                                data.append({
                                    "number": number,
                                    "rater": rater
                                })

                    # 计算最大值和最小值
                    if len(data) > 0:  # 确保 data 不为空
                        max_value = max(data, key=lambda x: x['number'])['number']
                        min_value = min(data, key=lambda x: x['number'])['number']

                        # 找到所有最大值和最小值的条目
                        max_entries = [entry for entry in data if entry['number'] == max_value]
                        min_entries = [entry for entry in data if entry['number'] == min_value]

                        # 将结果添加到 deviation_information
                        deviation_information.append({
                            'supplier': str01,
                            'stage': stage,
                            'scoring_item': eval_content,
                            'deviation_value': dif,
                            'top_score': max_value,
                            'top_score_name': [entry['rater'] for entry in max_entries],  # 添加所有最大值的 rater
                            'lowest_score': min_value,
                            'lowest_score_name': [entry['rater'] for entry in min_entries],  # 添加所有最小值的 rater
                        })

                    # 清空 data 列表以便下一个 supplier 使用
                    data.clear()

        # 创建一部字典来存储 supplier 的键值对
        supplier_dict = {list(supplier_item.keys())[0]: list(supplier_item.values())[0] for supplier_item in supplier}
        # 创建一个映射字典
        rater_mapping = {list(item.keys())[0]: list(item.values())[0] for item in result}

        # 替换 deviation_information 中的 supplier 值
        for entry in deviation_information:
            supplier_key = entry['supplier']
            if supplier_key in supplier_dict:
                entry['supplier'] = supplier_dict[supplier_key]

        # 替换 deviation_information 中的 max_rater 和 min_rater 的值
        for entry in deviation_information:
            # 替换 max_rater
            max_rater = entry.get('top_score_name', [])
            entry['top_score_name'] = [rater_mapping.get(rater[:-1], rater) for rater in max_rater]

            # 替换 min_rater
            min_rater = entry.get('lowest_score_name', [])
            entry['lowest_score_name'] = [rater_mapping.get(rater[:-1], rater) for rater in min_rater]

            # 将列表转换为用逗号分隔的字符串
            entry['top_score_name'] = ', '.join(entry['top_score_name'])
            entry['lowest_score_name'] = ', '.join(entry['lowest_score_name'])

        # # 生成导出表的表头
        # import_table_head = {
        #     'project': '项目', 'weight': '权重', 'eval_content': '评审内容', 'first_level': '优(86~100分)',
        #     'second_level': '中(70~85分)', 'third_level': '较低(69分以下)'
        # }
        # for i in range(supplier_cnt):
        #     for j in range(rater_cnt):
        #         import_table_head.update({
        #             'rater_' + str(j + 1) + chr(ord('a') + i): '评委' + str(j + 1) + chr(ord('a') + i)
        #         })  # {rater_1a：'评委1a'}
        #     import_table_head.update({'avg_' + chr(ord('a') + i): '平均分' + chr(ord('a') + i)})
        #     # {'avg_e': '平均分e',}
        #     import_table_head.update({'dif_' + chr(ord('a') + i): '偏差' + chr(ord('a') + i) + '(%)'})
        #     # {'dif_e': '偏差e（%）'}

        self.workflow_detail.add_kv_table(name='测试', kv_table={
            'ybpf_info': ybpf_info, 'mspf_info': mspf_info, 'ybpf_table': ybpf_table, 'mspf_table': mspf_table,
            'ybpf_invalid_bid_idx': invalid_bid_idx1, 'mspf_invalid_bid_idx': invalid_bid_idx2,
            'ybpf_temp_list': ybpf_temp_list, 'mspf_temp_list': mspf_temp_list, 'ybpf_import_table': ybpf_import_table,
            'mspf_import_table': mspf_import_table, 'ybpf_data_json': ybpf_data_json, 'mspf_data_json': mspf_data_json,
            'deviation_information': deviation_information
            # 'import_table_head': import_table_head
        })

        # # 剔除废标  不参与汇总表的排序
        # invalid_supplier_list = []
        # for idx in invalid_bid_idx:
        #     invalid_supplier_list.append(supplier_list.pop(idx))
        #     supplier_avg.pop(idx)
        #     supplier_cnt -= 1

        ybpf_score_list = [supplier_list, ybpf_supplier_avg]
        ybpf_remark = []
        for i in range(supplier_cnt):
            if i in invalid_bid_idx1:
                ybpf_remark.append('废标')
            else:
                ybpf_remark.append('')
        ybpf_score_list.append(ybpf_remark)

        mspf_score_list = [supplier_list, mspf_supplier_avg]
        mspf_remark = []
        for i in range(supplier_cnt):
            if i in invalid_bid_idx2:
                mspf_remark.append('废标')
            else:
                mspf_remark.append('')
        mspf_score_list.append(mspf_remark)

        variables = {'ybpf_score_list': ybpf_score_list}  # 可删  =======
        # 把 二维数组score_list行列转置 如：[[1, 2], [3,4]] -> [[1, 3], [2, 4]]
        ybpf_score_list2 = list(map(list, zip(*ybpf_score_list)))
        # 根据供应商的加权平均分排序
        ybpf_score_list2 = sorted(ybpf_score_list2, key=(lambda x: x[-2]), reverse=True)
        variables.update({'ybpf_score_list2': ybpf_score_list2})  # 可删    =====
        # 生成各个供应商的排名
        idx = 1
        rank = [1]
        for i in range(1, supplier_cnt):
            if ybpf_score_list2[i][1] < ybpf_score_list2[i - 1][1]:
                idx += 1
            rank.append(idx)
        for i in range(supplier_cnt):
            ybpf_score_list2[i].append(rank[i])

        variables = {'mspf_score_list': mspf_score_list}  # 可删  =======
        # 把 二维数组score_list行列转置 如：[[1, 2], [3,4]] -> [[1, 3], [2, 4]]
        mspf_score_list2 = list(map(list, zip(*mspf_score_list)))
        # 根据供应商的加权平均分排序
        mspf_score_list2 = sorted(mspf_score_list2, key=(lambda x: x[-2]), reverse=True)
        variables.update({'score_list2': mspf_score_list2})  # 可删    =====
        # 生成各个供应商的排名
        idx = 1
        rank = [1]
        for i in range(1, supplier_cnt):
            if mspf_score_list2[i][1] < mspf_score_list2[i - 1][1]:
                idx += 1
            rank.append(idx)
        for i in range(supplier_cnt):
            mspf_score_list2[i].append(rank[i])

        ybpf_increment_table = []  # 自增表格
        for item in ybpf_score_list2:
            data = {
                'supplier': item[0],
                'avg': item[1],
                'remark': item[2],
                'rank': item[3],
            }
            ybpf_increment_table.append(data)

        mspf_increment_table = []  # 自增表格
        for item in mspf_score_list2:
            data = {
                'supplier': item[0],
                'avg': item[1],
                'remark': item[2],
                'rank': item[3],
            }
            mspf_increment_table.append(data)

        # 生成下一个节点的通知信息内容
        project_name = self.variables.get('project_name', '')
        msg_content = create_msg_content(project_name=project_name, operation_type='审核')
        variables.update({
            'ybpf_data_json': ybpf_data_json,
            'mspf_data_json': mspf_data_json,
            'msg_content': msg_content,
            'ybpf_increment_table': ybpf_increment_table,
            'mspf_increment_table': mspf_increment_table,
            'technical_score_summary': technical_score_summary,
            'deviation_information': deviation_information,
            'result': result,
            # 'import_table_head': str(import_table_head)
        })

        # # 重置流程变量里面保存的子流程的 instance_id
        # for item in rater_list:
        #     variables.update({item + 'c_instance_id': ''})
        self.workflow_detail.add_kv_table('流程变量', {
            'variables': variables, 'ybpf_table': ybpf_table, 'mspf_table': mspf_table,
            'ybpf_import_table': ybpf_import_table, 'mspf_import_table': mspf_import_table
        })
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables).call()


class BidMarkTodoOne(AjaxTodoBase):
    # 子流程  评标人员对标进行打分

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ybpf_table = process_data.get('ybpf_table')
        technical_score_summary = process_data.get('technical_score_summary')

        cur_rater = self.ctx.get_var('cur_rater', '')
        # 获取当前时间
        current_time = datetime.datetime.now()
        # 格式化为 'yyyy-mm-dd hh:mm:ss'
        now = current_time.strftime('%Y-%m-%d %H:%M')
        # 移除项目为 "合计" 的条目
        ybpf_table = [item for item in ybpf_table if item.get('project') != '合计']

        # 添加 cur_rater 字段
        for item in ybpf_table:
            if not item.get("rater_remark"):
                item['rater_remark'] = '无'
            item['cur_rater'] = cur_rater  # 添加 cur_rater 字段

        # 添加 cur_rater 字段
        for item in ybpf_table:
            item['stage'] = "评标阶段"  # 添加 stage 字段

        self.workflow_detail.add_kv_table(name='自增表格',
                                          kv_table={'ybpf_table': ybpf_table})

        # 检查废标
        people_info = self.ctx.get_var('people_info', {})
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        ybpf_invalid_idx_lt = list()  # 用于保存废标的下标
        for item in ybpf_table:
            for i in range(supplier_cnt):
                temp = int(item['supplier_' + chr(ord('a') + i)])
                if temp == 0:
                    ybpf_invalid_idx_lt.append(i)
        ybpf_invalid_idx_lt = list(set(ybpf_invalid_idx_lt))

        # 将流程变量更新至 父流程
        f_instance_id = self.variables.get('f_instance_id', '')
        if f_instance_id and cur_rater:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables={
                    cur_rater + '_ybpf_table': [ybpf_table, ybpf_invalid_idx_lt],  # 使用列表  # 使用列表
                    cur_rater + '_technical_score_summary': technical_score_summary,
                    'ybpf_table': ybpf_table,  # 使用字符串键
                    f'{cur_rater}': now}  # 确保键是字符串
            ).call()
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(
            task_id=self.ctx.task_id,
            variables_map={cur_rater + '_ybpf_table': [ybpf_table, ybpf_invalid_idx_lt],  # 使用列表  # 使用列表
                           cur_rater + '_technical_score_summary': technical_score_summary,  # 确保键是字符串
                           'ybpf_table': ybpf_table, }  # 使用字符串键
        ).call()


class BidCheckTodo(AjaxTodoBase):
    # 初审人员对标进行审核

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        project_name = process_data.get('project_name')  # 项目名
        project_desc = process_data.get('project_desc')  # 项目简述
        increment_table = process_data.get('increment_table')  # 自增表格组件
        deviation_information = process_data.get('deviation_information')  # 自增表格组件

        # 初审人员(即评标组长)审核情况  同意 or 驳回
        first_check = process_data.get("first_check", None)
        # 初审人员填写的备注
        check_remark = process_data.get("check_remark", '无')
        # 附件
        attachment = process_data.get("attachment", '')
        attachment_table = []
        if attachment:
            # 附件非空， 整理成 自增表格的数据结构
            for item in attachment:
                content = item['name']
                href = item['response']['FileList'][0]['url']
                attachment_table.append({'file': [content, href]})

        variables_map = {
            'project_name': project_name, 'project_desc': project_desc, 'increment_table': increment_table,
            'first_check': first_check, 'check_remark': check_remark, 'attachment_table': attachment_table,
            'attachment': attachment, 'deviation_information': deviation_information,
        }
        # 信息内容
        # variables_map.update(temp_dict)
        if first_check == '同意':
            msg_content = create_msg_content(project_name=project_name, pass_or_not='同意', operation_type='审批')
        elif first_check == '驳回':
            msg_content = create_msg_content(project_name=project_name, pass_or_not='驳回', operation_type='评分',
                                             reject_reason=check_remark)
        variables_map.update({'msg_content': msg_content})
        # # 被’驳回‘时 需要更新流程变量cur_rater
        # rater_list = self.variables.get('rater_list', [])
        # variables_map.update({'cur_rater': rater_list[0], 'msg_content': msg_content})
        # variables_map.update(disagree(process_data, self.variables))

        self.workflow_detail.add_kv_table(name='比较区别(测试用)', kv_table={
            'process_data': process_data, 'self.variables': self.variables, 'variables_map': variables_map
        })

        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidLoopCreateTicket2:
    """
    根据一级审批人员的数量建单
    """

    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def loop_item2(self, flow_key: str):
        first_approve = self.ctx.get_var('first_approve')
        first_approve_list = first_approve.split(';')
        cnt = len(first_approve_list)
        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_hxhaoliu')

        project_name = self.ctx.get_var('project_name')
        project_desc = self.ctx.get_var('project_desc')
        increment_table = self.ctx.get_var('increment_table')
        first_check = self.ctx.get_var('first_check')
        check_remark = self.ctx.get_var('check_remark')
        attachment_table = self.ctx.get_var('attachment_table')
        msg_content = self.ctx.get_var('msg_content')
        data_json = self.ctx.get_var('data_json')
        supplier_cnt = self.ctx.get_var('supplier_cnt')
        rater_cnt = self.ctx.get_var('rater_cnt')
        f_instance_id = self.ctx.instance_id

        # 用于保存创建的工单的单号
        ticket_id_list2 = []
        for i in range(cnt):
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '一级审批功能"服务',
                ticket_level=3,
                title=project_name + ' - 审批',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=first_approve_list[i],  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var={
                    'project_name': project_name, 'project_desc': project_desc, 'increment_table': increment_table,
                    'first_check': first_check, 'check_remark': check_remark, 'todo_user': first_approve_list[i],
                    'msg_content': msg_content, 'f_instance_id': f_instance_id, 'idx': i,  # idx记录的是第i个 一级审批人的下标
                    'attachment_table': attachment_table, 'data_json': data_json, 'supplier_cnt': supplier_cnt,
                    'rater_cnt': rater_cnt,
                }  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list2.append(ticket_info.get('TicketId'))
        variables_map = {'ticket_id_list2': ticket_id_list2}
        for i in range(cnt):
            variables_map.update({'approve_result' + str(i): ''})
            #                     'approve_result' + str(i)表示第i个一级审批人的审批结果
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    @cycle_task_abort
    def wait_approve(self):
        """
        等待所有一级审批人员 审批完成
        """
        ticket_id_list = self.variables.get('ticket_id_list2', [])
        flag = ticket_is_over(ticket_id_list)  # flag为未结束的工单的数量
        if flag:
            return {"success": False, "data": "尚有子流程未结束"}
        else:
            first_approve = self.ctx.get_var('first_approve')
            first_approve_list = first_approve.split(';')
            cnt = len(first_approve_list)
            first_approve_flag = True  # 流条件， 用于流程流转
            first_approve_remark = ''  # 保存所有一级审批人员的驳回原因
            for i in range(cnt):
                first_approve_res = self.ctx.get_var('approve_result' + str(i))
                if first_approve_res == '驳回':
                    first_approve_flag = False
                    temp_remark = self.ctx.get_var('approve_remark' + str(i))
                    first_approve_remark = first_approve_remark + temp_remark + '; '
            # 更新通知消息
            project_name = self.variables.get('project_name', '')
            if first_approve_flag:
                msg_content = create_msg_content(project_name=project_name, pass_or_not='同意', operation_type='审批')
            else:
                msg_content = create_msg_content(project_name=project_name, pass_or_not='驳回', operation_type='评分',
                                                 reject_reason=first_approve_remark)

            WorkflowContinue(
                task_id=self.ctx.task_id,
                variables_map={
                    'first_approve_flag': first_approve_flag,
                    'first_approve_remark': first_approve_remark,
                    'msg_content': msg_content
                }
            ).call()
            return {"success": True, "data": "所有子流程结束"}


class BidFirstApproveTodo(AjaxTodoBase):
    """
    一级审批人员对标进行审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        approve_result = process_data.get('approve_result')
        approve_remark = process_data.get('approve_remark')

        idx = self.variables.get('idx', '')

        variables_map = {'approve_result' + str(idx): approve_result, 'approve_remark' + str(idx): approve_remark}
        # 生成信息内容
        project_name = self.variables.get('project_name', '')
        # 信息内容
        # temp_dict = create_msg_content2(approve_result, project_name, approve_remark)
        # variables_map.update(temp_dict)

        # if approve_result == '同意':
        #     msg_content = create_msg_content(pass_or_not='同意', project_name=project_name, operation_type='审批')
        #     variables_map.update({'msg_content': msg_content})
        # elif approve_result == '驳回':
        #     msg_content = create_msg_content(pass_or_not='驳回', project_name=project_name, operation_type='评分')
        #     # 被’驳回‘时 需要更新流程变量cur_rater
        #     rater_list = self.variables.get('rater_list', [])
        #     variables_map.update({'cur_rater': rater_list[0], 'msg_content': msg_content})
        #     # variables_map.update(disagree(process_data, self.variables))

        # 将流程变量更新至 父流程
        f_instance_id = self.ctx.get_var('f_instance_id')
        if f_instance_id:
            WorkflowVarUpdate(
                instance_id=f_instance_id,
                variables=variables_map
            ).call()

        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidSecApproveTodo(AjaxTodoBase):
    """
    二级审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        sec_approve = process_data.get('sec_approve')
        sec_approve_remark = process_data.get('sec_approve_remark')
        variables_map = {'sec_approve': sec_approve, 'sec_approve_remark': sec_approve_remark}

        # 信息内容
        project_name = self.variables.get('project_name', '')
        if sec_approve == '同意':
            msg_content = create_msg_content(project_name=project_name, pass_or_not='同意', operation_type='审批')
        else:
            msg_content = create_msg_content(project_name=project_name, pass_or_not='驳回', operation_type='评分',
                                             reject_reason=sec_approve_remark)
        variables_map.update({'msg_content': msg_content})

        # project_name = self.variables.get('project_name', '')
        # msg_content = '请尽快将评标流程中的"' + project_name\
        #               + '"记录数据库并查看审批结果\n查看入口为：https://dcops.test.woa.com/appManage/tickets/needToDo'

        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidThirdApproveTodo(AjaxTodoBase):
    """
    三级审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        th_approve = process_data.get('th_approve')
        th_approve_remark = process_data.get('th_approve_remark')
        variables_map = {'th_approve': th_approve, 'th_approve_remark': th_approve_remark}
        # 信息内容
        project_name = self.variables.get('project_name', '')
        # msg_content = '请尽快对"' + project_name + '"审核， 审核入口为：https://dcops.test.woa.com/appManage/tickets/needToDo'
        # project_name = self.variables.get('project_name', '')
        # msg_content = '请尽快将评标流程中的"' + project_name\
        #               + '"记录数据库并查看审批结果\n查看入口为：https://dcops.test.woa.com/appManage/tickets/needToDo'

        if th_approve == '同意':
            msg_content = create_msg_content(project_name=project_name, pass_or_not='同意', operation_type='审批')
        else:
            msg_content = create_msg_content(project_name=project_name, pass_or_not='驳回', operation_type='评分',
                                             reject_reason=th_approve_remark)
        variables_map.update({'msg_content': msg_content})
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables_map).call()


class BidEmailNoticeTodo(AjaxTodoBase):
    """
    数据写入， 和 邮件通知
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()
        self.my_db = mysql.new_mysql_instance("tbconstruct")

    def end(self, process_data, process_user):
        # 数据写入
        # # 生成导出表的表头
        # column_names = ['project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level', 'four_level']
        # supplier_cnt = self.variables.get('supplier_cnt', 0)
        # rater_cnt = self.variables.get('rater_cnt', 0)
        #
        # 邮件通知
        project_name = self.variables.get('project_name', '')
        project_desc = self.variables.get('project_desc', '')
        whether_bid_suppliers = self.variables.get('whether_bid_suppliers', [])
        whether_bid_remark = self.variables.get('whether_bid_remark', '')

        # 将列表转换为以逗号隔开的字符串
        suppliers_string = ', '.join(whether_bid_suppliers)

        msg_content = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>邮件通知</title>
                <style>
                    .title{{
                        margin-bottom: 10px;
                        color: #212529;
                        text-align: center;
                    }}
                    .info-title{{
                        display: inline-block;
                        width: 7rem;
                        text-align: right;
                        font-weight: bold;
                    }}
                    .table-head {{
                        color: #fff;
                        background-color: #343a40;
                        border-color: #454d55;
                        padding: 0.75rem;
                        font-size: 0;
                        font-weight: bold;
                        text-align: center;
                    }}
                    .table-head-item{{
                        display: inline-block;
                        font-size: 16px;
                    }}
                    .row-box{{
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                        font-size: 0;
                        display: flex;
                        align-items: center;
                        text-align: center;
                    }}
                    .row-item{{
                        display: inline-block; font-size: 16px;
                    }}
                    .approve-info-title01{{
                        display: inline-block;
                        width: 15%;
                        text-align: right; font-weight: bold;
                    }}
                    .approve-info-title02{{
                        display: inline-block; vertical-align: top; width: 15%; font-size: 16px; text-align: right; font-weight: bold;
                    }}
                    .approve-info-remark02{{
                        display: inline-block; vertical-align: top; width: 85%; font-size: 16px;
                    }}
                    .attachment-box{{
                        width: 15%;
                        text-align: right;
                        padding-left: 40px;
                    }}
                </style>
            </head>
            <body>
                <div style="font-size: 16px;">
                    <h2 class="title">{project_name}</h2>
                    <div>
                        <h3>1. 基础信息</h3>
                        <p><span class="info-title">项目名：</span>{project_name}</p>
                        <p><span class="info-title">项目概况：</span>{project_desc}</p>
                    </div>
                    <div>
                        <h3>2. 汇总表</h3>
                    </div>
                </div>

                <div class="table-head">
                    <span class="table-head-item" style="width: 30%;">序号</span>
                    <span class="table-head-item" style="width: 35%;">厂家</span>
                    <span class="table-head-item" style="width: 35%;">技术评分</span>
                </div>
            """

        technical_score_summary = self.variables.get('technical_score_summary', '')
        for index, value in enumerate(technical_score_summary):
            msg_content += f"""
                <div class="row-box">
                    <span class="row-item" style="width: 30%;">{index + 1}</span>
                    <span class="row-item" style="width: 35%;">{value.get('manufacturers')}</span>
                    <span class="row-item" style="width: 35%;">{value.get('skill_score')}</span>
                </div>
                """
        msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">废标供应商： </span>
                        <span>{suppliers_string}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">废标备注： </span>
                        <span class="approve-info-remark02">{whether_bid_remark}</span>
                    </div>
                </div>
                """
        first_check = self.variables.get('first_check', '')
        check_remark = self.variables.get('check_remark', '')
        if first_check and check_remark:
            msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">评分组长审核： </span>
                        <span>{first_check}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">备注： </span>
                        <span class="approve-info-remark02">{check_remark}</span>
                    </div>
                </div>
                """

        leader = self.variables.get('leader', '')
        leader_approval_standard = self.variables.get('leader_approval_standard', '')  # 同意 or 驳回
        leader_remarks_standard = self.variables.get('leader_remarks_standard', '')  # 备注
        msg_content += f"""
            <div style="margin: 1rem 0 1rem 1rem;">
                <div style="font-size: 16px;">
                    <span class="approve-info-title01">{leader}审核： </span>
                    <span>{leader_approval_standard}</span>
                </div>
                <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                    <span class="approve-info-title02">备注： </span>
                    <span class="approve-info-remark02">{leader_remarks_standard}</span>
                </div>
            </div>
            """

        center = self.variables.get('center', '')  # 审批人
        director_approval_standard = self.variables.get('director_approval_standard', '')  # 同意 or 驳回
        director_remarks_standard = self.variables.get('director_remarks_standard', '')  # 备注
        msg_content += f"""
                <div style="margin: 1rem 0 1rem 1rem;">
                    <div style="font-size: 16px;">
                        <span class="approve-info-title01">{center}审核： </span>
                        <span>{director_approval_standard}</span>
                    </div>
                    <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                        <span class="approve-info-title02">备注： </span>
                        <span class="approve-info-remark02">{director_remarks_standard}</span>
                    </div>
                </div>
                """

        next_op = self.variables.get('next_op', '')  # 审批人
        GM = self.variables.get('GM', '')  # 审批人
        GM_approval_standard = self.variables.get('GM_approval_standard', '')  # 同意 or 驳回
        GM_remarks_standard = self.variables.get('GM_remarks_standard', '')  # 备注
        if next_op == 0:
            msg_content += f"""
                    <div style="margin: 1rem 0 1rem 1rem;">
                        <div style="font-size: 16px;">
                            <span class="approve-info-title01">{GM}审核： </span>
                            <span>{GM_approval_standard}</span>
                        </div>
                        <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                            <span class="approve-info-title02">备注： </span>
                            <span class="approve-info-remark02">{GM_remarks_standard}</span>
                        </div>
                    </div>
                    """
        elif next_op == 1:
            msg_content += f"""
                    <div style="margin: 1rem 0 1rem 1rem;">
                        <div style="font-size: 16px;">
                            <span class="approve-info-title01">{center}审核： </span>
                            <span>{director_approval_standard}</span>
                        </div>
                        <div style='font-size: 0; vertical-align: baseline; margin-top: 0.3rem;'>
                            <span class="approve-info-title02">备注： </span>
                            <span class="approve-info-remark02">{director_remarks_standard}</span>
                        </div>
                    </div>
                    """

        other_desc = process_data.get('other_desc', '')
        if other_desc:
            msg_content += f"""
                        </span>
                    </div>
                </div>

                <div>
                    <h3>3. 其他说明</h3>
                    <div>
                        &nbsp;&nbsp;&nbsp;&nbsp;{other_desc}
                    </div>
                </div>
            </body>
            </html>
            """
        else:
            msg_content += """
                        </span>
                    </div>
                </div>
            </body>
            </html>
            """

        title = process_data.get('title', '')
        receivers = process_data.get('receivers', [])
        transmits = process_data.get('transmits', [])
        # tof.send_email(sendTitle=title, msgContent=msg_content, sendTo=receivers, sendCopy=transmits)
        #
        # for i in range(supplier_cnt):
        #     for j in range(rater_cnt):
        #         column_names.append('rater_' + str(j + 1) + chr(ord('a') + i))  # 形如rater_1a
        #     column_names.append('avg_' + chr(ord('a') + i))  # 形如avg_e
        #     column_names.append('dif_' + chr(ord('a') + i))  # 形如dif_e
        # try:
        #     ybpf_data_json = self.ctx.get_var('ybpf_data_json', '')
        #     mspf_data_json = self.ctx.get_var('mspf_data_json', '')
        #     insert_datas = []
        #     # 导出表的数据data_json中，最后三行为 - 两个空行，一个说明信息，故需要截掉
        #     for item in ybpf_data_json[0:-4]:
        #         ybpf_data = {}
        #         for i in range(len(column_names)):
        #             ybpf_data.update({column_names[i]: item.get(column_names[i], '')})
        #         insert_datas.append(ybpf_data)
        #
        #     for item in mspf_data_json[0:-4]:
        #         mspf_data = {}
        #         for i in range(len(column_names)):
        #             mspf_data.update({column_names[i]: item.get(column_names[i], '')})
        #         insert_datas.append(mspf_data)
        #
        #     self.workflow_detail.add_kv_table(
        #         name="流程变量",
        #         kv_table={
        #             "数据表的插入数据": insert_datas
        #         }
        #     )
        #
        #     self.my_db.insert_batch(table='idc_tb_procurement_evaluation', insertdatas=insert_datas)
        #
        # except ValueError as ve:
        #     print(f'捕获到异常 {ve}')
        #
        # except TypeError as te:
        #     print(f'捕获到异常 {te}')

        self.workflow_detail.add_kv_table(
            name="邮件内容",
            kv_table={
                "邮件内容": msg_content,
                "title": title,
                "receivers": receivers,
                "transmits": transmits,
            }
        )
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(
            task_id=self.ctx.task_id,
            variables={
                'msg_content': msg_content,
                'title': title,
                "receivers": receivers,
                "transmits": transmits,
            }
        )


class BidEmailNoticeShow(AjaxTodoBase):
    """
    数据写入， 和 邮件通知
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()
        self.my_db = mysql.new_mysql_instance("tbconstruct")

    def end(self, process_data, process_user):
        # 生成导出表的表头
        is_send = process_data.get('is_send')
        if is_send == "否":
            GnetopsTodoEnd(self.ctx.task_id)
            flow.complete_task(
                task_id=self.ctx.task_id,
                variables={
                    "is_send": is_send
                })
            return None
        column_names = ['project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level']
        supplier_cnt = self.variables.get('supplier_cnt', 0)
        rater_cnt = self.variables.get('rater_cnt', 0)
        title = process_data.get('title', '')
        receivers = process_data.get('receivers', [])
        transmits = self.variables.get('transmits', [])
        if receivers and isinstance(receivers, str):
            receivers = [receivers + "@tencent.com"]
        if receivers and isinstance(receivers, list):
            for i in range(len(receivers)):
                receivers[i] = receivers[i] + "@tencent.com"
        if transmits:
            for i in range(len(transmits)):
                transmits[i] = transmits[i] + "@tencent.com"
        msg_content = process_data.get('msg_content', '')
        tof.send_email(sendTitle=title, msgContent=msg_content, sendTo=receivers, sendCopy=transmits)

        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                column_names.append('rater_' + str(j + 1) + chr(ord('a') + i))  # 形如rater_1a
            column_names.append('avg_' + chr(ord('a') + i))  # 形如avg_e
            column_names.append('dif_' + chr(ord('a') + i))  # 形如dif_e
        try:
            ybpf_data_json = self.ctx.get_var('ybpf_data_json', '')
            mspf_data_json = self.ctx.get_var('mspf_data_json', '')
            insert_datas = []
            # 导出表的数据data_json中，最后三行为 - 两个空行，一个说明信息，故需要截掉
            for item in ybpf_data_json[0:-4]:
                ybpf_data = {}
                for i in range(len(column_names)):
                    ybpf_data.update({column_names[i]: item.get(column_names[i], '')})
                insert_datas.append(ybpf_data)

            for item in mspf_data_json[0:-4]:
                mspf_data = {}
                for i in range(len(column_names)):
                    mspf_data.update({column_names[i]: item.get(column_names[i], '')})
                insert_datas.append(mspf_data)

            self.workflow_detail.add_kv_table(
                name="流程变量",
                kv_table={
                    "数据表的插入数据": insert_datas
                }
            )

            self.my_db.insert_batch(table='idc_tb_procurement_evaluation', insertdatas=insert_datas)

        except ValueError as ve:
            print(f'捕获到异常 {ve}')

        except TypeError as te:
            print(f'捕获到异常 {te}')
        ticket_id = self.ctx.variables.get('ticket_id')
        rater_list = self.ctx.variables.get('people_info')["rater_list"]
        rater_leader = self.ctx.variables.get("rater_leader")
        project_name = self.variables.get('project_name', '')
        email_list = []
        for i in rater_list:
            i += "@tencent.com"
            email_list.append(i)
        msg_to_rater = (f"【{project_name}】技术评标审批已完成\n\n"
                        f"感谢大家的支持，谢谢！")
        tof.send_email(sendTitle=title, msgContent=msg_to_rater, sendTo=email_list, sendCopy=[rater_leader])
        GnetopsTodoEnd(self.ctx.task_id)
        flow.complete_task(
            task_id=self.ctx.task_id,
            variables={
                'msg_content': msg_content,
                'title': title,
                "receivers": receivers,
                "transmits": transmits,
                "is_send": is_send
            }
        )


class WhetherBidNotarizeOne(AjaxTodoBase):
    """
        废标确认
    """

    def __init__(self):
        super(WhetherBidNotarizeOne, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        whether_bid_file = process_data.get('whether_bid_file')
        whether_bid_rejection = process_data.get('whether_bid_rejection')
        whether_bid_suppliers = process_data.get('whether_bid_suppliers', [])
        whether_bid_remark = process_data.get('whether_bid_remark')
        technical_score_summary = self.ctx.variables.get('technical_score_summary', '')
        deviation_information = self.ctx.variables.get('deviation_information', '')
        ybpf_data_json = self.ctx.variables.get('ybpf_data_json', '')
        people_info = self.ctx.variables.get('people_info', '')
        supplier_list = people_info.get('supplier_list', '')
        supplier_cnt = self.ctx.variables.get('supplier_cnt', '')
        rater_cnt = self.ctx.variables.get('rater_cnt', '')

        whether_bid_suppliers_len = len(whether_bid_suppliers)
        supplier_cnt = supplier_cnt - whether_bid_suppliers_len

        # 创建评分字段字典
        field_dict = {}
        for i in range(min(rater_cnt, len(supplier_list))):
            for j in range(supplier_cnt):
                letter = chr(ord('a') + i)
                rater_key = 'rater_' + str(j + 1) + chr(ord('a') + i)
                dif_key = 'dif_' + chr(ord('a') + i)
                avg_key = 'avg_' + chr(ord('a') + i)
                field_dict[letter] = supplier_list[i]

                field_dict[rater_key] = supplier_list[i]
                field_dict[dif_key] = supplier_list[i]
                field_dict[avg_key] = supplier_list[i]

        # 定义一个函数来删除废标数据
        def delete_bid_fields(data_json):
            for entry in data_json:
                for key, supplier in field_dict.items():
                    if supplier in whether_bid_suppliers:
                        if key in entry:
                            del entry[key]
                # 删除类似于 "x是xx" 的描述
                if 'project' in entry:
                    for letter, supplier in field_dict.items():
                        if supplier in whether_bid_suppliers:
                            # 构造正则表达式，匹配 "x是xx" 的描述
                            pattern = rf'{letter}是{supplier}；?'
                            entry['project'] = re.sub(pattern, '', entry['project'])

                    # 清理多余的分号和空格
                    entry['project'] = entry['project'].replace('；；', '；').strip('； ')

        # 删除 ybpf_data_json 和 mspf_data_json 中的废标数据
        delete_bid_fields(ybpf_data_json)

        whether_bid_file_url = []
        whether_bid_file_table = []

        if whether_bid_file is not None:
            for doc in whether_bid_file:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        whether_bid_file_url.append(url)
                        whether_bid_file_table.append({
                            "whether_bid_file": url,
                            'whether_bid_file_name': name
                        })

        # 假设 technical_score_smary 是一个字典列表，每个字典都有一个 'manufacturers' 字段
        if isinstance(technical_score_summary, list):
            # 使用列表推导式过滤掉不需要的条目
            technical_score_summary = [
                item for item in technical_score_summary
                if not any(supplier in item.get('manufacturers', []) for supplier in whether_bid_suppliers)
            ]
        # 假设 deviation_information 是一个字典列表，每个字典都有一个 'supplier' 字段
        # if isinstance(deviation_information, list):
        #     # 使用列表推导式过滤掉不需要的条目
        #     deviation_information = [
        #         item for item in deviation_information
        #         if not any(supplier in item.get('supplier', []) for supplier in whether_bid_suppliers)
        #     ]

        variables = {
            "whether_bid_file": whether_bid_file,
            "whether_bid_file_table": whether_bid_file_table,
            "whether_bid_rejection": whether_bid_rejection,
            "whether_bid_suppliers": whether_bid_suppliers,
            "whether_bid_remark": whether_bid_remark,
            "technical_score_summary": technical_score_summary,
            "deviation_information": deviation_information,
            "ybpf_data_json": ybpf_data_json,
            "supplier_cnt": supplier_cnt
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class FileParsingSendingBak(object):
    """
        回标文件文件发送
    """

    def __init__(self):
        super(FileParsingSendingBak, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def tender_documents_sent(self):
        """
            标书文件发送
        """
        ticket_id = self.ctx.variables.get('ticket_id')
        project_name = self.ctx.variables.get('project_name')
        # tender_document_table = self.ctx.variables.get('tender_document_table')
        # 评标组长
        rater_leader = self.ctx.variables.get("rater_leader")
        # 评标成员
        raters = self.ctx.variables.get("raters")

        # 将 raters 字符串分割成列表
        rater_list = raters.split(';')

        # 检查 rater_leader 是否在列表中，如果不在则添加
        if rater_leader not in rater_list:
            rater_list.append(rater_leader)

        # 将列表重新组合成字符串
        combined_raters = ';'.join(rater_list)

        # 将评标成员字符串按分号分割成列表
        raters_list = raters.split(';')
        # 将评标组长添加到列表的开头
        raters_list.insert(0, rater_leader)
        raters_list = list(set(raters_list))
        tender_document_table = self.ctx.variables.get('tender_document_table')
        # 邮件抄送人
        if raters_list:
            for j in range(len(raters_list)):
                raters_list[j] += "@tencent.com"
        documents_info = "<br>"
        for i in tender_document_table:
            documents_info += f"【{i['tender_document_name']}】：{i['tender_document']}<br>"
        rich_text_template = f"【项目名称】：{project_name}<br>" \
                             "【内容】：标书文件已上传，请下载查看！<br>" \
                             f"【工单链接】：https://dcops.test.woa.com/operateManage/b" \
                             f"usiness/ToDoDetails?params={ticket_id}<br>" \
                             f"{documents_info}"
        email_title = f"{project_name}-标书文件"

        # tof.send_email(sendTitle=email_title, msgContent=rich_text_template, sendTo=raters_list)

        variables = {
            'combined_raters': combined_raters,
            "rich_text_template": rich_text_template,
            "email_title": email_title,
            "raters_list": raters_list
        }

        flow.complete_task(self.ctx.task_id, variables=variables)

    def clarification_file_sending(self):
        """
            澄清答疑文件发送
        """
        project_name = self.ctx.variables.get('project_name')
        clarification_document_file = self.ctx.variables.get('clarification_document_file')
        # 评标组长
        rater_leader = self.ctx.variables.get("rater_leader")
        # 评标成员
        raters = self.ctx.variables.get("raters")
        # 将评标成员字符串按分号分割成列表
        raters_list = raters.split(';')
        # 将评标组长添加到列表的开头
        raters_list.insert(0, rater_leader)
        raters_list = list(set(raters_list))

        # 邮件抄送人
        if raters_list:
            for j in range(len(raters_list)):
                raters_list[j] += "@tencent.com"

        email_title = f"{project_name}-澄清答疑文件"
        # 使用列表推导式和 join 方法构建 rich_text_template
        clarification_document_table = []
        for row in clarification_document_file[-1].get("clarification_document"):
            clarification_document_table.append({
                "clarification_document_name": row.get("name"),
                "clarification_document": row["response"]["FileList"][0]["url"]
            })
        self.workflow_detail.add_kv_table("发送邮件文件列表",
                                          {"clarification_document_table": clarification_document_table})
        documents_info = ""
        for item in clarification_document_table:
            # 构造单个信息项
            documents_info += (
                f"【{item.get('clarification_document_name')}】:"
                f"{item.get('clarification_document')}<br>"
            )
        self.workflow_detail.add_kv_table("邮件内容展示", {"documents_info": documents_info})
        rich_text_template = f"【项目名称】：{project_name}<br>" \
                             "【内容】：澄清答疑文件已上传，请下载查看！<br>" \
                             f"{documents_info}"

        # tof.send_attachment_email(filePathAndfileName=combined_dict, sendTitle=email_title,
        #                           msgContent=rich_text_template, sendTo=raters_list)

        tof.send_email(sendTitle=email_title, msgContent=rich_text_template, sendTo=raters_list)
        variables = {
            "rich_text_template": rich_text_template,
            "email_title": email_title,
            "raters_list": raters_list
        }

        flow.complete_task(self.ctx.task_id, variables=variables)


class AnswerQuestionsFileUpload(AjaxTodoBase):
    """
        答疑文件上传
    """

    def __init__(self):
        super(AnswerQuestionsFileUpload, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    # def end(self, process_data, process_user):
    #     question_answer_document = process_data.get('question_answer_document')
    #     dy_remark = process_data.get('dy_remark')
    #
    #     question_answer_document_url = []
    #     question_answer_document_table = []
    #
    #     if question_answer_document is not None:
    #         for doc in question_answer_document:
    #             if doc is not None and "response" in doc and "FileList" in doc["response"]:
    #                 file_list = doc["response"]["FileList"]
    #                 if len(file_list) > 0 and "url" in file_list[0]:
    #                     url = file_list[0]["url"]
    #                     name = file_list[0]['name']
    #                     question_answer_document_url.append(url)
    #                     question_answer_document_table.append({
    #                         "question_answer_document": url,
    #                         'question_answer_document_name': name
    #                     })
    #
    #     variables = {
    #         "question_answer_document": question_answer_document,
    #         "question_answer_document_table": question_answer_document_table,
    #         "dy_remark": dy_remark
    #     }
    #
    #     GnetopsTodoEnd(self.ctx.task_id, process_user)
    #     flow.complete_task(self.ctx.task_id, variables=variables)
    def end(self, process_data, process_user):
        question_answer_upload = process_data.get('question_answer_upload')
        # question_answer_document = process_data.get('question_answer_document')
        dy_remark = process_data.get('dy_remark')
        # question_answer_document_url = []
        # question_answer_document_table = []
        # if question_answer_upload is not None:
        #     for doc in question_answer_upload:
        #         count = doc.get("count")
        #         if doc.get("question_answer_document"):
        #             for i in doc.get("question_answer_document"):
        #                 if i is not None and "response" in doc and "FileList" in doc["response"]:
        #                     file_list = doc["response"]["FileList"]
        #                     if len(file_list) > 0 and "url" in file_list[0]:
        #                         url = file_list[0]["url"]
        #                         name = file_list[0]['name']
        #                         question_answer_document_url.append(url)
        #                         question_answer_document_table.append({
        #                             "count": f"第{count}次答疑文件上传",
        #                             "question_answer_document": url,
        #                             'question_answer_document_name': name
        #                         })
        #         else:
        #             return {"code":-1, "msg":"请上传必要文件"}

        variables = {
            "question_answer_upload": question_answer_upload,
            # "question_answer_document_table": question_answer_document_table,
            "dy_remark": dy_remark
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ClarificationAnswerQuestionsFileUploadBak(AjaxTodoBase):
    """
        澄清答疑文件上传
    """

    def __init__(self):
        super(ClarificationAnswerQuestionsFileUploadBak, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        logo = process_data.get('logo', "")
        if logo and logo == "是否需要澄清答疑":
            is_need = process_data.get('is_need', "")
            question_answer_upload = []
            clarification_document_file = []
            if is_need == "需要":
                question_answer_upload.append({"count": 1})
                clarification_document_file.append({"count": 1})
                variables = {
                    "is_need": is_need,
                    "question_answer_upload": question_answer_upload,
                    "clarification_document_file": clarification_document_file
                }
            else:
                variables = {
                    "is_need": is_need
                }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
            return None
        elif logo and logo == "标书文件下载":
            email_title = process_data.get('email_title')
            rich_text_template = process_data.get('rich_text_template')
            receivers_1 = process_data.get('raters')
            if not receivers_1:
                return {"code": -1, "msg": "请选择收件人"}
            receivers_1_list = receivers_1.split(";")
            email_receiver_list = []
            for i in receivers_1_list:
                email_receiver_list.append(i+"@tencent.com")
            variables = {"email_title": email_title,
                         "receivers_1": receivers_1,
                         "receivers_1_list": receivers_1_list,
                         "email_receiver_list": email_receiver_list,
                         "rich_text_template": rich_text_template}
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
            return None
        elif logo and logo == "邮件预览":
            email_title = process_data.get('email_title')
            rich_text_template = process_data.get('rich_text_template')
            receivers_1 = process_data.get('receivers_1')
            email_receiver_list = self.ctx.variables.get('email_receiver_list')
            is_send = process_data.get('is_send')
            if not is_send:
                return {"code": -1, "msg": "请选择是否发送邮件"}
            if is_send == "是":
                tof.send_email(sendTitle=email_title, msgContent=rich_text_template, sendTo=email_receiver_list)
            variables = {
                "email_title": email_title,
                "receivers_1": receivers_1,
                "email_receiver_list": email_receiver_list,
                "rich_text_template": rich_text_template,
                "is_send": is_send
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
            return None
        clarification_document_file = process_data.get('clarification_document_file')
        cq_remark = process_data.get('cq_remark')
        # clarification_document = process_data.get('clarification_document')
        # dy_remark = process_data.get('dy_remark')
        # clarification_document_url = []
        # clarification_document_table = []
        #
        # if clarification_document is not None:
        #     for doc in clarification_document:
        #         if doc is not None and "response" in doc and "FileList" in doc["response"]:
        #             file_list = doc["response"]["FileList"]
        #             if len(file_list) > 0 and "url" in file_list[0]:
        #                 url = file_list[0]["url"]
        #                 name = file_list[0]['name']
        #                 clarification_document_url.append(url)
        #                 clarification_document_table.append({
        #                     "clarification_document": url,
        #                     'clarification_document_name': name
        #                 })
        variables = {
            "clarification_document_file": clarification_document_file,
            # "clarification_document_table": clarification_document_table,
            "cq_remark": cq_remark,
        }

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ClarificationAnswerQuestionsFileCheck(AjaxTodoBase):
    """
    澄清答疑文件审核
    """

    def __init__(self):
        super(ClarificationAnswerQuestionsFileCheck, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_accept = process_data.get('is_accept')
        question_answer_upload = process_data.get('question_answer_upload')
        clarification_document_file = process_data.get('clarification_document_file')
        rater_leader_remark = process_data.get('rater_leader_remark')
        if is_accept == "否":
            variables = {
                "is_accept": is_accept
            }
        else:
            if rater_leader_remark:
                count = int(question_answer_upload[-1].get("count")) + 1
                question_answer_upload.append({"count": f"{count}"})
                clarification_document_file.append({"count": f"{count}"})
                variables = {
                    "question_answer_upload": question_answer_upload,
                    "clarification_document_file": clarification_document_file,
                    "is_accept": is_accept,
                    "rater_leader_remark": rater_leader_remark
                }
            else:
                return {"code": -1, "msg": "请填写驳回备注"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class FilesGenerationOne(object):
    """
        文件生成
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def file_uploaded_to_cos(self):
        """
            文件上传至cos
        """
        process_tile = self.ctx.variables.get('ProcessTitle')
        ybpf_data_json = self.ctx.variables.get('ybpf_data_json')
        ybpf_dif_json = self.ctx.variables.get('ybpf_dif_json')
        supplier_cnt = self.ctx.variables.get('supplier_cnt')
        rater_cnt = self.ctx.variables.get('rater_cnt')
        technical_score_summary = self.ctx.variables.get('technical_score_summary')
        whether_bid_suppliers = self.ctx.variables.get('whether_bid_suppliers')
        if whether_bid_suppliers is not None:
            whether_bid_suppliers_len = len(whether_bid_suppliers)
            supplier_cnt = supplier_cnt - whether_bid_suppliers_len

        url_table = []

        # 创建表头
        import_table_head = {
            "stage": "阶段",
            "project": "项目",
            "weight": "权重（%）",
            "eval_content": "评审内容",
            "first_level": "优秀(86~100分)",
            "second_level": "较好(71~85分)",
            "third_level": "一般(70分以下)",
        }
        import_table_head_2 = copy.deepcopy(import_table_head)
        for i in range(supplier_cnt):
            str01 = chr(ord('a') + i)  # 'a', 'b', ...
            for j in range(rater_cnt):
                str02 = str(j + 1)
                import_table_head[f"rater_{str02}{str01}"] = f"评委{str02}{str01}"
                import_table_head_2[f"rater_{str02}{str01}"] = f"评委{str02}{str01}"

            import_table_head[f"avg_{str01}"] = f"平均分{str01}"
            import_table_head_2[f"avg_{str01}"] = f"最大偏差{str01}"

        for i in range(supplier_cnt):
            str01 = chr(ord('a') + i)  # 'a', 'b', ...
            for j in range(rater_cnt):
                str02 = str(j + 1)
                import_table_head[f"rater_{str02}_remark"] = f"评委{str02}备注"
                import_table_head_2[f"rater_{str02}_remark"] = f"评委{str02}备注"
        # 评分汇总表格按列名重构
        all_scores_list = [{
            "stage": "厂家",
            "project": "技术评分"
        }]
        for row in technical_score_summary:
            all_scores_list.append({
                "stage": row.get("manufacturers"),
                "project": row.get("skill_score"),
            })

        ybpf_data_json = (ybpf_data_json[:-4] + [{}, {}, {}] + [import_table_head_2] + ybpf_dif_json +
                          [{}, {}, {}] + all_scores_list + [{}, {}, {}] + ybpf_data_json[-4:])
        final_data_json = ybpf_data_json
        # 将数据转换为 DataFrame
        ybpf_df = pd.DataFrame(ybpf_data_json)

        # 重新排列列顺序
        ybpf_df = ybpf_df.reindex(columns=import_table_head.keys())
        ybpf_output_file = os.path.join(f'{process_tile}评分汇总.xlsx')

        # 保存为 Excel 文件
        ybpf_df.to_excel(ybpf_output_file, index=False, encoding='utf-8', engine='openpyxl',
                         header=list(import_table_head.values()))

        cos_path = '/data_json/'
        COSLib.uploadFile(file_name=ybpf_output_file, cos_path=cos_path)
        ybpf_file_url = COSLib.get_file_url(file_name=ybpf_output_file, cos_path=cos_path)

        url_table.append({
            'yppf_url': ybpf_file_url,
        })

        variables = {
            "ybpf_file_url": ybpf_file_url,
            "url_table": url_table,
            "final_data_json": final_data_json
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class BidLoopCreateTicketBak(object):
    """
    循环建单 评委对偏差过大数据进行说明
    """

    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def loop_item(self, people_info: dict, flow_key: str):
        """
        flow_key 传入创建的子流程的 流程标识
        title 子流程的标题
        """
        project_name = self.ctx.get_var('project_name', '')
        project_desc = self.ctx.get_var('project_desc', '')
        technical_score_summary = self.ctx.get_var('technical_score_summary')
        deviation_information = self.ctx.get_var('deviation_information')
        f_instance_id = self.ctx.instance_id
        variables_map = {
            'project_name': project_name,
            'project_desc': project_desc,
            'technical_score_summary': technical_score_summary,
            'people_info': people_info,
            'f_instance_id': f_instance_id,
        }
        self.workflow_detail.add_kv_table('表单数据', {'variables_map': variables_map})
        person_list = []
        message_by_person = {}
        for i in deviation_information:
            person = i.get('lowest_score')
            if person not in person_list:
                person_list.append(person)
                message_by_person[person] = [i]
            else:
                message_by_person[person].append(i)
        # 获取流程的 创建者
        creator = self.variables.get('Creator', 'v_zongxiyu')
        # 用于保存创建的工单的单号
        ticket_id_list_2 = []
        for key, values in message_by_person.items():
            cur_message = values
            deal_person = key
            variables_map.update({'cur_message': cur_message,
                                  'deal_person': deal_person})
            ticket_info = gnetops.create_ticket(
                flow_key=flow_key,
                description='该工单为："' + project_name + '的评分偏差过大说明填写"服务',
                ticket_level=3,
                title=project_name + ' - 偏差说明填写',
                creator=creator,
                concern="",  # 关注人, 这里暂为空
                deal=deal_person,  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            ticket_id_list_2.append(ticket_info.get('TicketId'))
        variables_map.update({'ticket_id_list_2': ticket_id_list_2})
        WorkflowContinue(self.ctx.task_id, variables_map=variables_map).call()

    @cycle_task_abort
    def wait_mark(self):
        """
        等待所有人员说明填写
        """
        ticket_id_list_2 = self.variables.get('ticket_id_list_2', [])
        if not ticket_id_list_2:
            flow.complete_task(self.ctx.task_id, variables={})
            return None
        task_id = self.ctx.task_id
        cycle_is_over(ticket_id_list_2, task_id)


class UpdateDeviationInformation(AjaxTodoBase):
    def __init__(self):
        super(UpdateDeviationInformation).__init__()
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        """
        更新 deviation_information
        """
        cur_message = process_data.get('cur_message')
        f_instance_id = self.variables.get("f_instance_id")
        deviation_information = flow.get_variables(f_instance_id, ['deviation_information'])[
            "deviation_information"]
        for row in cur_message:
            for i in deviation_information:
                if not row['deviation_specification']:
                    return {"code": -1, "msg": "请填写偏差说明"}
                elif i['lowest_score'] == row['lowest_score'] and i['scoring_item'] == row['scoring_item'] \
                        and i["supplier"] == row["supplier"]:
                    i.update({'deviation_specification': row['deviation_specification'],
                              'bak_url': row.get('bak_url')})
                    break
        variables = {
            "f_instance_id": f_instance_id,
            "cur_message": cur_message,
            "deviation_information": deviation_information
        }
        WorkflowVarUpdate(
            instance_id=f_instance_id,
            variables={
                "deviation_information": deviation_information
            }  # 确保键是字符串
        ).call()
        GnetopsTodoEnd(self.ctx.task_id)
        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables).call()


class GmAuditBakTest(AjaxTodoBase):
    def __init__(self):
        super(GmAuditBakTest, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 从process_data中获取audit_result和audit_opinion
        audit_result = process_data.get("audit_result")
        audit_opinion = process_data.get("audit_opinion")

        if audit_result == "通过":
            audit_result = True
        else:
            audit_result = False

        # 从流程变量中获取idc_audit_list
        idc_audit_list = self.ctx.variables.get("idc_audit_list")

        idc_audit_list.append({
            "task": "GM审批",
            "audit_status": "通过" if audit_result else "不通过",
            "audit_opinion": audit_opinion,
            "audit_user": process_user,
            "audit_time": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())),
            "todo_key": "GmAudit"
        })

        variables = {
            "idc_audit_list": idc_audit_list,
            "gm_audit_gateway": 1 if audit_result else 0
        }

        tb_db = mysql.new_mysql_instance("tb_change")
        tb_db.update("requirement_cron", {
            "Status": "end"
        }, {
                         "TicketId": self.ctx.variables.get("TicketId"),
                         "TaskId": self.ctx.task_id,
                         "TodoKey": "GmAudit",
                     })

        # 结束dcops待办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        # 流程流转
        flow.complete_task(self.ctx.task_id, variables=variables)


class FilesGenerationBakOne(object):
    """
        评分模板文件生成
    """

    def __init__(self):
        self.ctx = get_context()
        self.variables = self.ctx.variables
        self.workflow_detail = WorkflowDetailService()

    def file_uploaded_to_cos(self, people_info: dict):
        """
            文件上传至cos
        """
        ybpf_table = self.ctx.variables.get('ybpf_table')
        supplier_list = people_info.get('supplier_list')
        supplier_cnt = len(supplier_list)
        # rater_cnt = self.ctx.variables.get('rater_cnt')
        # supplier_list = people_info.get('supplier_list')
        # whether_bid_suppliers = self.ctx.variables.get('whether_bid_suppliers')
        # if whether_bid_suppliers is not None:
        #     whether_bid_suppliers_len = len(whether_bid_suppliers)
        #     supplier_cnt = supplier_cnt - whether_bid_suppliers_len

        # 创建表头
        import_table_head = {
            "project": "项目",
            "weight": "权重（%）",
            "eval_content": "评审内容",
            "first_level": "优秀(86~100分)",
            "second_level": "较好(71~85分)",
            "third_level": "一般(70分以下)",
        }
        for i in range(supplier_cnt):
            import_table_head[f'supplier_{chr(ord("a") + i)}'] = supplier_list[i]
        import_table_head['rater_remark'] = '备注'

        # for i in range(supplier_cnt):
        #     str01 = chr(ord('a') + i)  # 'a', 'b', ...
        #     for j in range(rater_cnt):
        #         str02 = str(j + 1)
        #         import_table_head[f"rater_{str02}{str01}"] = f"评委{str02}{str01}"
        #         import_table_head[f"rater_{str02}_remark"] = f"评委{str02}备注"
        #     import_table_head[f"avg_{str01}"] = f"平均分{str01}"
        #     import_table_head[f"dif_{str01}"] = f"偏差{str01}(%)"

        # 将数据转换为 DataFrame
        # ybpf_data = json.loads(ybpf_table)
        ybpf_df = pd.DataFrame(ybpf_table)

        # 重新排列列顺序
        ybpf_df = ybpf_df.reindex(columns=import_table_head.keys())

        # 设置 rater_remark 列的默认值为 "无"
        ybpf_df['rater_remark'].fillna('无', inplace=True)

        for i in range(supplier_cnt):
            ybpf_df[f'supplier_{chr(ord("a") + i)}'] = None

        ybpf_output_file = os.path.join('应标评分模板.xlsx')

        # 保存为 Excel 文件
        ybpf_df.to_excel(ybpf_output_file, index=False, encoding='utf-8', engine='openpyxl',
                         header=list(import_table_head.values()))

        cos_path = '/data_json/'
        COSLib.uploadFile(file_name=ybpf_output_file, cos_path=cos_path)
        ybpf_file_url = COSLib.get_file_url(file_name=ybpf_output_file, cos_path=cos_path)

        variables = {
            "ybpf_first_file_url": ybpf_file_url,
            "import_table_head": import_table_head,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def sort_to_score(self):
        """
        对各个供应商的得分 进行排序
        :return:
        """
        people_info = self.ctx.get_var('people_info', {})
        rater_list = people_info.get('rater_list', [])
        rater_cnt = len(rater_list)
        supplier_list = people_info.get('supplier_list', [])
        supplier_cnt = len(supplier_list)
        raters = []
        for i in rater_list:
            rater = self.ctx.get_var(f'{i}')

            raters.append({
                f"{i}": rater
            })

        # 提取时间并排序
        sorted_a = sorted(raters, key=lambda x: datetime.datetime.strptime(list(x.values())[0], '%Y-%m-%d %H:%M'))

        # 根据排序后的评委数据更新 rater_list
        rater_list = [list(sorted_a[i].keys())[0] for i in range(len(sorted_a))]

        # 存放结果的列表
        result = []

        # 为每个字典分配顺序编号
        for key, item in enumerate(sorted_a, start=1):
            # 获取字典的键
            value = list(item.keys())[0]
            # 将结果添加到列表中
            result.append({f"rater_{key}": value})

        # 修改_ybpf_table的key
        # 遍历 result 列表
        ybpf_gather = []
        for rater in result:
            # 获取 rater 的值
            rater_key = list(rater.values())[0]
            new_remark_key = f"{list(rater.keys())[0]}_remark"  # 'rater_1_remark'
            ybpf_name = self.ctx.get_var(rater_key + '_ybpf_table')
            # 遍历 v_mmywang_ybpf_table
            for table in ybpf_name[0]:  # 只处理第一个子列表
                if table['cur_rater'] == rater_key:
                    # 修改 rater_remark 为新的键
                    if table.get('rater_remark'):
                        table[new_remark_key] = table.pop('rater_remark')

            ybpf_gather.append({
                rater_key + '_ybpf_table': ybpf_name
            })

        ybpf_test_table = []
        # 汇总数据 ybpf_table 的评分数据
        ybpf_table = [[] for i in range(supplier_cnt)]  # 第i个下标， 保存供应商（A+i）的评分情况
        invalid_bid_idx1 = []
        # self.workflow_detail.add_kv_table("ybpf_gather", {"ybpf_gather": ybpf_gather, "mspf_gather": mspf_gather})
        for item in rater_list:
            for j, row in enumerate(ybpf_gather):
                ybpf_info = row.get(item + '_ybpf_table')
                if ybpf_info and isinstance(ybpf_info, list) and len(ybpf_info) > 0:
                    table = ybpf_info[0]
                    ybpf_test_table.extend(table)
                    # 在这里可以安全地使用 table 变量
                    ybpf_temp_table = []  # 保存该评分人的打分情况
                    invalid_idx = ybpf_info[1]  # 获取废标的下标
                    invalid_bid_idx1 += invalid_idx
                    for entry in table:
                        row = []  # 保存某个评分表中的一行
                        for i in range(supplier_cnt):
                            row.append(int(entry['supplier_' + chr(ord('a') + i)]))  # 访问每个供应商的评分
                        row.append(int(entry['weight'][0:-1]) / 100)  # 获取权重  （string类型，如5%, 去掉%）
                        ybpf_temp_table.append(row)
                    ybpf_temp_table = list(map(list, zip(*ybpf_temp_table)))  # 行列转置，[[1, 2],[3, 4]] -> [[1, 3], [2, 4]]
                    for i in range(supplier_cnt):
                        ybpf_table[i].append(ybpf_temp_table[i])
                    # ybpf_temp_table的最后一列是权重
                    ybpf_table.append(ybpf_temp_table[-1])
                else:
                    # 如果 ybpf_info 为空或不是列表，或者列表为空，则处理这种情况
                    pass
        invalid_bid_idx1 = list(set(invalid_bid_idx1))

        # 汇总technical_score_summary数据
        # 创建一个字典来存储共同的 manufacturers 的数据
        combined_scores = {}
        technical_score_summary = []
        # 遍历第一个列表
        for item in rater_list:
            hz_info = self.ctx.get_var(item + '_technical_score_summary')
            for row in hz_info:
                manufacturers = row.get("manufacturers")
                response_score = float(row.get("response_score"))

                if manufacturers not in combined_scores:
                    combined_scores[manufacturers] = {
                        "response_score": response_score,
                        "count": 1  # 计数器，用于计算平均值
                    }
                else:
                    combined_scores[manufacturers]["response_score"] += response_score
                    combined_scores[manufacturers]["count"] += 1
        # 计算平均值并生成新的列表
        for manufacturers, scores in combined_scores.items():
            if scores["count"] >= 1:  # 只考虑共同的 manufacturers
                avg_response_score = round(scores["response_score"] / scores["count"], 2)

                # 计算 skill_score
                skill_score = avg_response_score

                technical_score_summary.append({
                    "manufacturers": manufacturers,
                    "response_score": avg_response_score,
                    "skill_score": skill_score,  # 根据公式计算的 skill_score
                })
        # 提取所有技术评分
        skill_scores = [entry['skill_score'] for entry in technical_score_summary]

        # 计算最低和最高技术评分
        min_skill_score = min(skill_scores)
        max_skill_score = max(skill_scores)

        # 计算每个制造商的技术分
        for entry in technical_score_summary:
            skill_score = entry['skill_score']
            if max_skill_score != min_skill_score:  # 避免除以零
                technical_score = ((skill_score - min_skill_score) * 100) / (max_skill_score - min_skill_score)
            else:
                technical_score = 0  # 如果所有技术评分相同，技术分为0

            # 将技术分添加到字典中
            entry['technical_score'] = technical_score

        # 对技术分列表进行排序
        technical_score_summary = sorted(technical_score_summary, key=lambda x: x['skill_score'], reverse=True)

        # self.workflow_detail.add_kv_table(name='评分temp_table', kv_table={
        #     'ybpf_temp_table': ybpf_temp_table,
        #     'mspf_temp_table': mspf_temp_table
        # })

        ybpf_supplier_avg = []  # 供应商的加权平均分
        ybpf_remark_list = []
        for i in range(supplier_cnt):
            # 计算表格中的 平均值
            temp_sum = list(map(sum, zip(*ybpf_table[i])))
            avg_data = list(map(lambda x: round(x / rater_cnt, 2), temp_sum))  # 平均值
            ybpf_table[i].append(avg_data)

            # 计算 方差 和 加权平均值， 将废标的加权平均值设为 -1
            ybpf_table_length = len(avg_data)
            if i in invalid_bid_idx1:
                diff_data = ['废标' for _ in range(ybpf_table_length)]
                ybpf_supplier_avg.append(-1)  # 加权平均分
            else:
                temp_min = list(map(min, zip(*ybpf_table[i])))
                temp_max = list(map(max, zip(*ybpf_table[i])))
                # 计算差异并格式化结果，去掉 .0
                diff_data = list(map(lambda x, y: f"{int(round((y - x) / x * 100, 0))}%", temp_min, temp_max))
                temp = list(map(lambda x, y: x * y, avg_data, ybpf_table[-1]))
                ybpf_supplier_avg.append(round(sum(temp), 2))  # 加权平均分
            if ybpf_test_table:
                for entry in ybpf_test_table:
                    for j in range(rater_cnt):
                        key = f'rater_{j}_remark'
                        if key in entry:  # 检查键是否存在于字典中
                            remark = entry[key]
                            ybpf_remark_list.append(remark)  # 如果键存在，则添加到列表中
            ybpf_table[i].append(diff_data)
            ybpf_table[i].append(ybpf_remark_list)
        ybpf_temp_list = []
        for i in range(supplier_cnt):
            # 过滤掉空列表
            filtered_data = [item for item in ybpf_table[i] if item]
            ybpf_temp_list.append(list(map(list, zip(*filtered_data))))
        ybpf_import_table = [[] for _ in range(ybpf_table_length)]

        # 获取 ybpf_table
        ybpf_list = self.ctx.get_var('ybpf_table')

        # 从 ybpf_table 中提取数据并填充 import_table
        for j in range(ybpf_table_length):
            ybpf_temp_item = [
                ybpf_list[j]['stage'],
                ybpf_list[j]['project'],
                ybpf_list[j]['weight'],
                ybpf_list[j]['eval_content'],
                ybpf_list[j]['first_level'],
                ybpf_list[j]['second_level'],
                ybpf_list[j]['third_level'],
                ybpf_list[j]['rater_remark']
            ]
            ybpf_import_table[j].extend(ybpf_temp_item)

        for i in range(supplier_cnt):
            for j in range(ybpf_table_length):
                ybpf_import_table[j].extend(ybpf_temp_list[i][j])

        # 添加三个空行
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        ybpf_import_table.append([])
        # 表格中的最后两行特别处理
        ybpf_import_table[-1].append('**说明信息**')
        ybpf_supplier_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        # ybpf_rater_str = '”评委1a”代表评委1对a供应商评分；其中：\n'
        for i in range(supplier_cnt):
            ybpf_supplier_str = ybpf_supplier_str + chr(ord('a') + i) + '是' + supplier_list[i] + '； '
        # for item in result:
        #     for key, value in item.items():
        #         ybpf_rater_str = ybpf_rater_str + f"{key} 是 {value}； "
        ybpf_import_table[-1].append(ybpf_supplier_str)
        # ybpf_import_table[-1].append(ybpf_rater_str)

        # 生成json数据格式 用于表单导出
        keys = ['stage', 'project', 'weight', 'eval_content', 'first_level', 'second_level', 'third_level',
                'rater_remark']
        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                keys.append('rater_' + str(j + 1) + chr(ord('a') + i))  # rater_1a

            keys.append('avg_' + chr(ord('a') + i))  # avg_a
            keys.append('dif_' + chr(ord('a') + i))  # dif_a
            keys.append('rater_' + str(j + 1) + '_remark')

        ybpf_data_json = []
        for i in range(ybpf_table_length + 4):
            ybpf_data_json.append(dict(zip(keys, ybpf_import_table[i])))
        # 正则匹配备注字段名
        pattern = re.compile(r'^rater_\d*_remark$')
        # 循环应标汇总评分结果表
        for item in rater_list:
            for table in ybpf_gather:
                ybpf_info = table.get(item + '_ybpf_table')
                if ybpf_info:
                    person_table = ybpf_info[0]
                    for index, row in enumerate(person_table):
                        if ybpf_data_json[index].get("rater_remark"):
                            ybpf_data_json[index].pop("rater_remark")
                        # 将对应行备注信息刷入结果表
                        ybpf_data_json[index].update({
                            key: value for key, value in row.items() if pattern.match(key)
                        })
                        # 遍历row中的所有键，找到匹配的键并更新ybpf_data_json
        # 构建偏差信息数组
        ybpf_dif_json = copy.deepcopy(ybpf_data_json)
        # 构建权重评分汇总
        ybpf_weight_dict = {
            "project": "评分权重汇总",
            "weight": "",
            "eval_content": "",
            "first_level": "",
            "second_level": "",
            "third_level": "",
        }
        # 评分数据汇总计算
        for i in range(supplier_cnt):
            for j in range(rater_cnt):
                weighted_score = 0
                avg_weighted_score = 0
                for row in ybpf_data_json[:-4]:
                    score_key = 'rater_' + str(j + 1) + chr(ord('a') + i)
                    avg_key = 'avg_' + chr(ord('a') + i)
                    correct_score = float(row[score_key])
                    avg_correct_score = float(row[avg_key])
                    weight = int(row['weight'].replace('%', ''))
                    weighted_score += correct_score * weight / 100
                    avg_weighted_score += avg_correct_score * weight / 100
                    weighted_score = round(weighted_score, 2)
                ybpf_weight_dict.update({score_key: weighted_score,
                                         avg_key: avg_weighted_score})
        # 汇总数据加入总数据
        ybpf_data_json = ybpf_data_json[:-4] + [ybpf_weight_dict] + ybpf_data_json[-4:]

        # 去除空字典
        ybpf_dif_json = [item for item in ybpf_dif_json if item]  # 过滤掉空字典

        # 去除最后一个字典
        ybpf_dif_json = ybpf_dif_json[:-1]  # 去掉最后一个字典
        # self.workflow_detail.add_kv_table("测试信息", {'ybpf_dif_json': ybpf_dif_json,
        #                                                'mspf_dif_json': mspf_dif_json})
        # 循环计算并更新应标偏差值数组
        for items in ybpf_dif_json:
            for i in range(supplier_cnt):
                # 存储不同项目每一项的最大偏差
                ybpf_max_dif = float('-inf')
                for j in range(rater_cnt):
                    score_key = 'rater_' + str(j + 1) + chr(ord('a') + i)
                    avg_key = 'avg_' + chr(ord('a') + i)
                    score = items.get(score_key, "")
                    avg = items.get(avg_key, "")
                    if score and avg:
                        if score is None or score == '无' or avg == '无':
                            continue  # 如果没有评分或评分值为 '无'，跳过
                        if score and avg:
                            if score == '无' or avg == '无':
                                continue  # 如果没有评分或评分值为 '无'，跳过
                            dif = round((float(score) - float(avg)) / float(avg), 2)
                            ybpf_max_dif = max(ybpf_max_dif, abs(dif))
                            items[score_key] = dif
                if ybpf_max_dif == float('-inf'):
                    ybpf_max_dif = 0  # 如果所有偏差均为零，设置最大偏差为0
                items[avg_key] = ybpf_max_dif

        # 偏差信息表格
        deviation_information = []
        supplier = []
        data = []
        for i, row in enumerate(supplier_list):
            key = chr(ord('a') + i)  # 'a' for 本贸, 'b' for 德恒
            supplier.append({
                key: row
            })

        # 合并两个列表
        # combined_data = ybpf_dif_json_new + mspf_dif_json_new

        for counts, row in enumerate(ybpf_dif_json):
            for i in range(supplier_cnt):
                for j in range(rater_cnt):
                    dif = row.get('rater_' + str(j + 1) + chr(ord('a') + i))
                    if dif is not None and dif != '无':
                        # 确保 dif 是字符串类型
                        if isinstance(dif, str):
                            # 去掉 '%' 并转换为浮点数
                            dif_value = float(dif.replace('%', ''))
                        elif isinstance(dif, (int, float)):
                            # 如果 dif 是 int 或 float，直接使用它
                            dif_value = float(dif)  # 转换为浮点数
                        else:
                            continue  # 如果 dif 不是字符串、int 或 float，跳过

                        # 将结果添加到 deviation_information
                        if abs(dif_value) >= 0.2:
                            lowest_score_name = None
                            supplier_name = supplier_list[i]
                            stage = row.get('stage')
                            eval_content = row.get('eval_content')
                            rater = rater_list[j]
                            top_score = ybpf_data_json[counts].get('rater_' + str(j + 1) + chr(ord('a') + i))
                            avg_score = ybpf_data_json[counts].get('avg_' + chr(ord('a') + i))
                            if top_score and avg_score:
                                if top_score > avg_score:
                                    lowest_score_name = '高于平均分'
                                else:
                                    lowest_score_name = '低于平均分'

                            deviation_information.append({
                                'supplier': supplier_name,
                                'stage': stage,
                                'scoring_item': eval_content,
                                'deviation_value': dif_value,
                                'top_score': top_score,  # 分数
                                'top_score_name': lowest_score_name,  # 分数相对情况
                                'lowest_score': rater,  # 分数对应rater
                                'deviation_specification': '',  # 说明
                                'bak_url': ''  # 附件链接
                            })

                    # 清空 data 列表以便下一个 supplier 使用
                    data.clear()
        # # 剔除废标  不参与汇总表的排序
        # invalid_supplier_list = []
        # for idx in invalid_bid_idx:
        #     invalid_supplier_list.append(supplier_list.pop(idx))
        #     supplier_avg.pop(idx)
        #     supplier_cnt -= 1

        ybpf_score_list = [supplier_list, ybpf_supplier_avg]
        ybpf_remark = []
        for i in range(supplier_cnt):
            if i in invalid_bid_idx1:
                ybpf_remark.append('废标')
            else:
                ybpf_remark.append('')
        ybpf_score_list.append(ybpf_remark)

        variables = {'ybpf_score_list': ybpf_score_list}  # 可删  =======
        # 把 二维数组score_list行列转置 如：[[1, 2], [3,4]] -> [[1, 3], [2, 4]]
        ybpf_score_list2 = list(map(list, zip(*ybpf_score_list)))
        # 根据供应商的加权平均分排序
        ybpf_score_list2 = sorted(ybpf_score_list2, key=(lambda x: x[-2]), reverse=True)
        variables.update({'ybpf_score_list2': ybpf_score_list2})  # 可删    =====
        # 生成各个供应商的排名
        idx = 1
        rank = [1]
        for i in range(1, supplier_cnt):
            if ybpf_score_list2[i][1] < ybpf_score_list2[i - 1][1]:
                idx += 1
            rank.append(idx)
        for i in range(supplier_cnt):
            ybpf_score_list2[i].append(rank[i])

        ybpf_increment_table = []  # 自增表格
        for item in ybpf_score_list2:
            data = {
                'supplier': item[0],
                'avg': item[1],
                'remark': item[2],
                'rank': item[3],
            }
            ybpf_increment_table.append(data)

        # 生成下一个节点的通知信息内容
        project_name = self.variables.get('project_name', '')
        msg_content = create_msg_content(project_name=project_name, operation_type='审核')
        variables.update({
            'ybpf_data_json': ybpf_data_json,
            'msg_content': msg_content,
            'ybpf_increment_table': ybpf_increment_table,
            'technical_score_summary': technical_score_summary,
            'deviation_information': deviation_information,
            'result': result,
            'ybpf_dif_json': ybpf_dif_json,
            # 'import_table_head': str(import_table_head)
        })

        WorkflowContinue(task_id=self.ctx.task_id, variables_map=variables).call()
