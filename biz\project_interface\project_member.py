from iBroker.lib import mysql


class ProjectMemberTable(object):
    """
        项目成员接口
    """

    def form_query(self, project_name):
        """
            项目成员查询
        """
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT role, name, account, phone, email " \
                    "FROM project_role_account " \
                    f"WHERE project_name = '{project_name}' and del_flag = 0"
        result = db.get_all(query_sql)

        data = {}
        for row in result:
            role = row.get("role")
            name = row.get("name")
            account = row.get("account")
            phone = row.get("phone")
            email = row.get("email")
            if '-' in role:
                key = role.split('-')[0] + '团队通信录'
                job = role.split('-')[1]
            else:
                key = role + '团队通信录'
                job = role

            if key not in data:
                data[key] = []

            data[key].append({
                "job": job,
                "person": name,
                "account": account,
                "phone": phone,
                "email": email
            })

        return data
