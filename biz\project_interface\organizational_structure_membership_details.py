# 组织架构成员获取
import re
import uuid

import numpy as np
import pandas as pd
from iBroker.lib import mysql
from iBroker.lib.sdk import flow
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService

from biz.construction_process.cos_lib import COSLib


class OrganizationStructureMemberDetails(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_member_details_by_team(self):
        """
            根据组织获取所有成员信息
            team_options返回格式为[{"label":"小组名称界面显示","value":"小组名称选择后返回值"},~~~~]，显示值与选定后返回值相同
            member_dict返回格式为{
                                    {
                                        "小组名称":{
                                            "group_leader": 小组组长,
                                            "center_leader": 中心负责人,
                                            "gm": 项管,
                                            "member_list": 人员列表
                                        }
                                    },~~~
                                }
        """
        sql = "select center,group_name,member,leader,center_leader,GM from organizational_structure_membership_details"
        db = mysql.new_mysql_instance("tbconstruct")
        query = db.get_all(sql)
        member_dict = {}
        team_options = []
        for row in query:
            center = row.get("center", "")
            group_name = row.get("group_name", "")
            member = row.get("member", "")
            leader = row.get("leader", "")
            center_leader = row.get("center_leader", "")
            gm = row.get("GM", "")
            if center == "数据中心":
                # 如果组名为运营优化组或运营质量组，按照常规小组处理数据
                if group_name in ["运营优化组", "运营质量组"]:
                    if group_name not in member_dict:
                        team_options.append({"label": group_name, "value": group_name})
                        member_dict[group_name] = {
                            "group_leader": re.sub(r'\(.*?\)', '', leader),
                            "center_leader": re.sub(r'\(.*?\)', '', center_leader),
                            "gm": re.sub(r'\(.*?\)', '', gm),
                            "member_list": []
                        }
                    member_dict[group_name]["member_list"].append(member)
                else:
                    # 如果组名不是运营优化组或运营质量组，按center逻辑处理
                    if center not in member_dict:
                        team_options.append({"label": center, "value": center})
                        member_dict[center] = {
                            "group_leader": re.sub(r'\(.*?\)', '', center_leader),
                            "center_leader": re.sub(r'\(.*?\)', '', center_leader),
                            "gm": re.sub(r'\(.*?\)', '', gm),
                            "member_list": []
                        }
                    member_dict[center]["member_list"].append(member)
            else:
                if group_name not in member_dict:
                    team_options.append({"label": group_name, "value": group_name})
                    member_dict[group_name] = {
                        "group_leader": re.sub(r'\(.*?\)', '', leader),
                        "center_leader": re.sub(r'\(.*?\)', '', center_leader),
                        "gm": re.sub(r'\(.*?\)', '', gm),
                        "member_list": []
                    }
                member_dict[group_name]["member_list"].append(member)
        variables = {
            "team_options": team_options,  # 团队选项,全部团队
            "member_dict": member_dict,  # 根据小组获取所有成员及leader和center_leader
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def get_leader_details_by_member(self):
        """
            根据人员获取对应leader和小组信息
        """
        sql = "select center,group_name,member,leader,center_leader,GM from organizational_structure_membership_details"
        db = mysql.new_mysql_instance("tbconstruct")
        query = db.get_all(sql)
        member_options = []
        leader_info = {}
        for row in query:
            center = row.get("center", "")
            group_name = row.get("group_name", "")
            member = row.get("member", "")
            leader = row.get("leader", "")
            center_leader = row.get("center_leader", "")
            gm = row.get("GM", "")
            member_options.append({"label": member, "value": re.sub(r'\(.*?\)', '', member)})
            if center == "数据中心":
                # 如果组名为运营优化组或运营质量组，按照常规小组处理数据
                if group_name in ["运营优化组", "运营质量组"]:
                    leader_info[re.sub(r'\(.*?\)', '', member)] = {"group_name": group_name,
                                                                   "center_leader": re.sub(r'\(.*?\)', '',
                                                                                           center_leader),
                                                                   "group_leader": re.sub(r'\(.*?\)', '', leader),
                                                                   "gm": re.sub(r'\(.*?\)', '', gm)}
                else:
                    leader_info[re.sub(r'\(.*?\)', '', member)] = {"group_name": center,
                                                                   "center_leader": re.sub(r'\(.*?\)', '',
                                                                                           center_leader),
                                                                   "group_leader": re.sub(r'\(.*?\)', '',
                                                                                          center_leader),
                                                                   "gm": re.sub(r'\(.*?\)', '', gm)}
            else:
                leader_info[re.sub(r'\(.*?\)', '', member)] = {"group_name": group_name,
                                                               "center_leader": re.sub(r'\(.*?\)', '', center_leader),
                                                               "group_leader": re.sub(r'\(.*?\)', '', leader),
                                                               "gm": re.sub(r'\(.*?\)', '', gm)}
        variables = {
            "member_options": member_options,  # 团队选项,全部团队
            "leader_info": leader_info,  # 根据小组获取所有成员及leader和center_leader
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def update_member_details(self, progress):
        """
        修改组织架构表数据接口
        """
        # 修改之前先清除之前表中数据
        if progress:
            url = progress[0]["response"]["FileList"][0]["url"]
            relative_url = url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            print(new_file_name)
            COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
            """
                excel解析总控计划
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl')
            df = df.replace({np.nan: None})
            insert_data = []
            for _, row in df.iterrows():
                insert_data.append({
                    "center": row["中心"],
                    "group_name": row["小组"],
                    "member": row["成员"],
                    "leader": row["leader"],
                    "center_leader": row["中心负责人"],
                    "GM": row["GM"],
                })
            db = mysql.new_mysql_instance("tbconstruct")
            db.begin()
            db.insert_batch("organizational_structure_membership_details", insert_data)
            db.commit()
        return {"code": 0, "message": "ok", "success": True}

def refresh_corresponding_account_of_role(account, project_name):
    db = mysql.new_mysql_instance("tbconstruct")
    is_str = isinstance(account, str)
    if isinstance(account, str):
        accounts = [account]  # 将字符串转换为包含单个元素的列表
    elif isinstance(account, list):
        accounts = account  # 直接使用列表
    else:
        return account

    new_account_list = []
    placeholders = ', '.join(['%s'] * len(accounts))
    sql_old = (
        "SELECT account, role "
        "FROM project_role_account "
        "WHERE project_name=%s AND account IN ({})".format(placeholders)
    )
    accounts_old = db.get_all(sql_old, (project_name,) + tuple(accounts))
    if accounts_old:
        for item in accounts_old:
            role = item['role']
            sql_new = (
                "SELECT account, role "
                "FROM project_role_account "
                "WHERE project_name=%s and role=%s and del_flag='0'"
            )
            account_new = db.get_row(sql_new, (project_name, role))
            if account_new:
                new_account = account_new['account']
                new_account_list.append(new_account)
    if is_str:
        return new_account_list[0] if new_account_list else account  # 如果输入是字符串，返回第一个元素或原值
    else:
        return new_account_list if new_account_list else account
