# -*- coding: utf-8 -*-
import os

from iBroker.lib import config
from iBroker.lib import exception
from qcloud_cos import CosConfig, CosS3Client, CosClientError, CosServiceError


class COSLib:
    # 存储桶，测试环境使用idc-iflowtest-appid，正式环境使用idc-iflow-appid
    This_Bucket = config.get_config_string("cos_bucket", "", False)

    @staticmethod
    def getCosClient() -> CosS3Client:
        cos_config = config.get_config_map("cos_config")
        if cos_config is None:
            raise exception.LogicException("获取COS配置失败", exception.EC_INNER_ERR)

        cfg = CosConfig(Region=cos_config["Region"], SecretId=cos_config["SecretId"],
                        SecretKey=cos_config["SecretKey"], Token=None, Scheme="https")
        return CosS3Client(cfg)

    @staticmethod
    def uploadFile(file_name: str, cos_path: str = "soc/retire/"):
        """上传文件到COS

        Args:
            file_name (str): 文件名，带路径

        Return: True
        """
        # 取绝对路径
        file_name = os.path.abspath(file_name)
        if not os.path.exists(file_name):
            raise exception.LogicException("文件不存在:" + file_name, exception.EC_INNER_ERR)

            # 取文件名
        path = file_name.split(os.sep)
        simple_name = path[-1]

        client = COSLib.getCosClient()
        try:
            # 使用'rb'打开文件
            with open(file_name, 'rb') as fp:
                response = client.put_object(
                    Bucket=COSLib.This_Bucket,
                    Body=fp,
                    Key=cos_path + simple_name,
                    StorageClass='STANDARD'
                )
                return response
        except (CosServiceError, CosClientError) as e:
            raise exception.LogicException("上传COS出错:" + str(e), exception.EC_INNER_ERR)

    @staticmethod
    def download(file_name: str, save_to_file: str, cos_path: str = "soc/retire/"):
        """从COS下载文件

        Args:
            file_name (str): 文件名
            save_to_file (str): 保存到新的文件名，含服务器路径
            cos_path (str, optional): cos目录，默认soc/retire/

        Raises:
            exception.LogicException 获取失败抛异常

        Returns:
            True
        """
        cos_bucket = config.get_config_string("cos_bucket", "", False)

        cos_config = config.get_config_map("cos_config")
        if cos_config is None:
            raise exception.LogicException("获取COS配置失败", exception.EC_INNER_ERR)

        cfg = CosConfig(Region=cos_config["Region"], SecretId=cos_config["SecretId"],
                        SecretKey=cos_config["SecretKey"], Token=None, Scheme="https")
        client = CosS3Client(cfg)

        path = cos_path + file_name
        # 获取映射后的文件名
        gnetops_data = {
            "original_file_name": path,
        }
        resp = gnetops.request(action="Cos", method="GetFileNameMap", data=gnetops_data)

        if resp != "":
            try:
                response = client.get_object(cos_bucket, resp)
                response['Body'].get_stream_to_file(save_to_file)
                return True
            except (CosServiceError, CosClientError) as e:
                try:
                    response = client.get_object(cos_bucket, path)
                    response['Body'].get_stream_to_file(save_to_file)
                    return True
                except (CosServiceError, CosClientError) as e:
                    raise exception.LogicException("获取COS文件失败:" + str(e), exception.EC_INNER_ERR)

        try:
            response = client.get_object(cos_bucket, path)
            response['Body'].get_stream_to_file(save_to_file)
            return True
        except (CosServiceError, CosClientError) as e:
            raise exception.LogicException("获取COS文件失败:" + str(e), exception.EC_INNER_ERR)

    # def download(file_name: str, save_to_file: str, cos_path: str = "soc/retire/"):
    #     """从COS下载文件
    #
    #     Args:
    #         file_name (str): 文件名
    #         save_to_file (str): 保存到新的文件名，含服务器路径
    #         cos_path (str, optional): cos目录，默认soc/retire/
    #
    #     Raises:
    #         exception.LogicException 获取失败抛异常
    #
    #     Returns:
    #         True
    #     """
    #     client = COSLib.getCosClient()
    #     path = cos_path + file_name
    #     try:
    #         response = client.get_object(COSLib.This_Bucket, path)
    #         response['Body'].get_stream_to_file(save_to_file)
    #         return True
    #     except (CosServiceError, CosClientError) as e:
    #         raise exception.LogicException("获取COS文件失败:" + str(e), exception.EC_INNER_ERR)

    @staticmethod
    def download_2(file_name: str, save_to_file: str):
        """
        使用相对路径从COS下载文件

        Args:
            file_name (str): 文件名
            save_to_file (str): 保存到新的文件名，含服务器路径
            cos_path (str, optional): cos目录，默认soc/retire/

        Raises:
            exception.LogicException 获取失败抛异常

        Returns:
            True
        """
        client = COSLib.getCosClient()
        try:
            response = client.get_object(COSLib.This_Bucket, file_name)
            response['Body'].get_stream_to_file(save_to_file)
            return True
        except (CosServiceError, CosClientError) as e:
            raise exception.LogicException("获取COS文件失败:" + str(e), exception.EC_INNER_ERR)

    @staticmethod
    def get_file_url(file_name: str, expired: int = 300, cos_path: str = "soc/retire/"):
        """获取文件临时访问url

        Args:
            file_name (str): 文件名
            expired (int): url过期时间，单位是秒，默认300秒
            cos_path (str, optional): cos目录，默认soc/retire/

        Raises:
            exception.LogicException 获取失败抛异常

        Returns:
            url (str): 临时访问url
        """
        cos_domain = config.get_config_string("cos_domain", "", False)
        if not cos_domain:
            raise exception.LogicException("获取COS域名失败", exception.EC_INNER_ERR)
        return cos_domain + cos_path + file_name


if __name__ == "__main__":
    pass
    # print(COSLib.uploadFile("1.txt"))
    # COSLib.download("1.txt", "2.txt")
    # url = COSLib.getFileUrl("data_export_2022_11_09_15_21_29.xlsx", 1000)
    # print(url)
    # #add new line1
    # data = {
    #     "SendTo": ['hermanhuang'],
    #     "SendTo": ['hermanhuang|kerwinzlin|pengleifu'],
    #     "MsgContent": url
    # }
    # result = gnetops.request(action="WeWork", method="SendFileMsg", data=data)
    # print(result)
