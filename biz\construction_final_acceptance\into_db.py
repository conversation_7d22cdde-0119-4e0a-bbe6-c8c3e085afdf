import json
from iBroker.lib import mysql
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib.sdk import flow
from biz.construction_final_acceptance.tools import Tools


# 终验反馈流程 数据入库类
class FinalAcceptanceIntoDb(object):

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def final_acceptance_into_db(self, project_name, project_code, info_dict):
        """
        终验反馈流程数据入库(construction_feedback_to_xingchen)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account = self.ctx.variables.get("PM")
        files_json = Tools.get_files_json(info_dict.get("final_files"))
        # 终验状态反馈存库-to星辰
        insert_list = []
        insert_list.append(
            {
                "project_name": project_name,
                "project_code": project_code,
                "ticket_id": ticket_id,
                "type": "终验",
                "actual_date": info_dict.get("actual_finish_time"),
                "account": account,
                "files_json": json.dumps(files_json, ensure_ascii=False),
                "remark": info_dict.get("remark"),
            }
        )
        if insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("construction_feedback_to_xingchen", insert_list)
            tb_db.commit()
        variables = {"insert_list": insert_list}
        flow.complete_task(self.ctx.task_id, variables=variables)

    def final_acceptance_po_details_into_db(
        self, project_name, project_code, info_dict, po_category_all_selected_list
    ):
        """
        终验反馈流程: 品类详情详情数据入库(final_acceptance_po_details)
        """
        ticket_id = self.ctx.variables.get("ticket_id")
        account = self.ctx.variables.get("PM")
        files_json = Tools.get_files_json(info_dict.get("final_files"))
        # 终验验收详情存库(品类详情)
        po_details_insert_list = []
        for item in po_category_all_selected_list:
            po_details_insert_list.append(
                {
                    "project_name": project_name,
                    "project_code": project_code,
                    "ticket_id": ticket_id,
                    "code_id": item.get("code_id"),
                    "order_code": item.get("po_code"),
                    # "material_id": item.get("material_id"),
                    # "material_category_name": item.get("material_category_name"),
                    "category_name": item.get("category_name"),
                    # "order_item_id": item.get("order_item_id"),
                    # "order_amount": item.get("order_amount"),
                    "actual_date": info_dict.get("actual_finish_time"),
                    "account": account,
                    "files_json": json.dumps(files_json, ensure_ascii=False),
                    "remark": info_dict.get("remark"),
                    # "acceptance_flag": (
                    #     item.get("selected")[0] if item.get("selected") else None
                    # ),
                }
            )
        if po_details_insert_list:
            # 连接数据库
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            # 插入数据
            tb_db.insert_batch("final_acceptance_category_details", po_details_insert_list)
            tb_db.commit()
        variables = {"po_details_insert_list": po_details_insert_list}
        flow.complete_task(self.ctx.task_id, variables=variables)
