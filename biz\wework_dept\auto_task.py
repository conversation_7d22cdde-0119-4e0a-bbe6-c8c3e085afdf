from iBroker.sdk.task.context import get_context
from iBroker.lib.sdk import flow, tof, gnetops
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import config
from iBroker.sdk.notification.chatops import ChatOpsSend
from biz.wework_dept.wework_dept_api import (
    WeworkDeptApi,
)


class WeworkDeptAutoTask(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def get_dept(self):
        """
        获取已有组织架构
        """
        dept_list = WeworkDeptApi.query_dept()
        variables = {
            "dept_list": dept_list,
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def create_wework_dept_determine(self, project_name_list):
        """
        判断是否需要开通新的组织架构
        """
        # 建单人（一般是PM）
        creator = self.ctx.variables.get("Creator")
        # 邮件推送配置信息
        eml_cfg = config.get_config_map("wework_dept_apply_email_info")
        # 流程负责人
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        # 项目去重
        project_name_list = list(set(project_name_list))
        create_dept_flag = 0
        create_dept_list = []
        dept_list = WeworkDeptApi.query_dept()
        dept_name_list = [item.get("label") for item in dept_list]
        for project_name in project_name_list:
            if project_name not in dept_name_list:
                create_dept_flag = 1
                create_dept_list.append(project_name)
        if create_dept_flag:
            # 给 邮件推送配置信息 的 receiver 推送邮件
            eml_cfg_apply = eml_cfg.get("apply")
            receiver = eml_cfg_apply.get("receiver")
            dept_apply_handler = (";").join(receiver)
            title = eml_cfg_apply.get("title")
            Cc = eml_cfg_apply.get("Cc")
            Cc.append(creator)
            Cc.append(flow_responsible_person)
            Cc = list(set(Cc))
            email_title = f"{(',').join(create_dept_list)}{title}"
            email_content = (
                f"{(',').join(receiver)}：<br>"
                f"<p style='text-indent: 2em;'>请开通组织架构：{(', ').join(create_dept_list)}</p>"
                f"<p style='text-indent: 2em;'>谢谢！</p>"
            )
            email_receiver = receiver
            email_Cc = Cc
            for i in range(len(email_Cc)):
                email_Cc[i] += "@tencent.com"
            for j in range(len(email_receiver)):
                email_receiver[j] += "@tencent.com"
            tof.send_email(
                sendTitle=email_title,
                msgContent=email_content,
                sendTo=email_receiver,
                sendCopy=email_Cc,
            )
            email_info_apply = {
                "email_title": email_title,
                "email_content": email_content,
                "email_receiver": email_receiver,
                "email_Cc": email_Cc,
            }
            variables = {
                "dept_list": dept_list,
                "dept_name_list": dept_name_list,
                "create_dept_flag": create_dept_flag,
                "create_dept_list": create_dept_list,
                "eml_cfg": eml_cfg,
                "eml_cfg_apply": eml_cfg_apply,
                "email_info_apply": email_info_apply,
                "dept_apply_handler": dept_apply_handler,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            # 给 建单人（一般是PM）推送消息
            eml_cfg_confirm = eml_cfg.get("confirm")
            account_apply_page = eml_cfg_confirm.get("account_apply_page")
            receiver = [creator]
            title = eml_cfg_confirm.get("title")
            content = eml_cfg_confirm.get("content")
            Cc = eml_cfg_confirm.get("Cc")
            Cc.append(flow_responsible_person)
            Cc = list(set(Cc))
            email_title = f"{(',').join(project_name_list)}{title}"
            email_content = (
                f"{(',').join(receiver)}：<br>"
                f"<p style='text-indent: 2em;'>{(', ').join(create_dept_list)}{content}</p>"
                f"<p style='text-indent: 2em;'>账号开通入口：<a href='{account_apply_page}'> {account_apply_page}</a></p>"
                f"<p style='text-indent: 2em;'>谢谢！</p>"
            )
            email_receiver = receiver
            email_Cc = Cc
            for i in range(len(email_Cc)):
                email_Cc[i] += "@tencent.com"
            for j in range(len(email_receiver)):
                email_receiver[j] += "@tencent.com"
            tof.send_email(
                sendTitle=email_title,
                msgContent=email_content,
                sendTo=email_receiver,
                sendCopy=email_Cc,
            )
            email_info_confirm = {
                "email_title": email_title,
                "email_content": email_content,
                "email_receiver": email_receiver,
                "email_Cc": email_Cc,
                "receiver": receiver,
            }
            msg_receiver = [
                creator,
                flow_responsible_person,
                *eml_cfg_confirm.get("Cc", []),
            ]
            msg_receiver = list(set(msg_receiver))
            msg_title = f"{(',').join(project_name_list)}{title}"
            msg_message = f"{(', ').join(project_name_list)}{content}"
            # 应用消息
            tof.send_company_wechat_message(
                receivers=msg_receiver, title=msg_title, message=msg_message
            )
            msg_info = {
                "msg_receiver": msg_receiver,
                "msg_title": msg_title,
                "msg_message": msg_message,
            }
            # dcops消息
            for account in msg_receiver:
                ChatOpsSend(
                    user_name=account,
                    msg_content=msg_message,
                    msg_type="text",
                    des_type="single",
                ).call()
            variables = {
                "dept_list": dept_list,
                "dept_name_list": dept_name_list,
                "create_dept_flag": create_dept_flag,
                "create_dept_list": create_dept_list,
                "eml_cfg": eml_cfg,
                "eml_cfg_confirm": eml_cfg_confirm,
                "email_info_confirm": email_info_confirm,
                "msg_info": msg_info,
            }
            flow.complete_task(self.ctx.task_id, variables=variables)

    # 企微组织架构开通申请-自动起单
    def create_wework_dept_apply_flow(self, project_name_list, ticket_creator):
        eml_cfg = config.get_config_map("wework_dept_apply_email_info")
        flow_responsible_person = config.get_config_string("flow_responsible_person")
        eml_cfg_create = eml_cfg.get("create")
        title = eml_cfg_create.get("title")
        # 输入
        processTitle = f"{(',').join(project_name_list)}{title}"
        data = {
            "CustomVariables": {
                "project_name_list": project_name_list,
            },
            "ProcessDefinitionKey": "wework_dept_apply",
            "Source": "",
            "TicketDescription": processTitle,
            "TicketLevel": "3",
            "TicketTitle": processTitle,
            "UserInfo": {
                "Concern": f"{ticket_creator};{flow_responsible_person}",  # 关注人 可填多个
                "Creator": ticket_creator,  # 建单人 只能填一个
                "Deal": f"{flow_responsible_person}",  # 负责人(处理人) 只能填一个 找不到处理人的待办都会给负责人
            },
        }
        # 输出
        res = gnetops.request(action="Ticket", method="Create", data=data)
        variables = {"wework_dept_apply_ticket": res}
        flow.complete_task(self.ctx.task_id, variables=variables)
