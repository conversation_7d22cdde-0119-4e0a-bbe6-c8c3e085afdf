import datetime
import re
import uuid

import lunardate
import numpy as np
import pandas as pd
import requests
from iBroker.lib import mysql, curl, config
from iBroker.lib.sdk import flow, tof, gnetops, idcdb
from iBroker.sdk.task.context import get_context
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.workflow.workflow import WorkflowDetailService, WorkflowContinue
from openpyxl import load_workbook

from biz.construction_process.cos_lib import COSLib
from biz.site_implementation.modification_overall_control_plan import GetDataInLibrary


class StorageParameter(object):
    """
    传递参数
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def PassParameters(self, project_name=None, region=None, province=None, campus=None, demand_distribution=None,
                       demand_delivery=None, electric_power_demand=None, estimation_period=None, construction_mode=None,
                       number_of_rack=None, stand_alone_work=None, rack_capacity=None, item_number=None):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus_bak = campus

        # project_map = config.get_config_map("operation_management_project_name_mapping")
        # for item in project_map:
        #     if province == item.get("operation_province") or campus == item.get("operation_campus"):
        #         province = item.get("construct_province")
        #         campus = item.get("construct_campus")
        #         if item.get("operation_project") in project_name:
        #             project_name = item.get("construct_project") + project_name[-4:]
        split_index = project_name.find("B")
        if split_index != -1:
            project = project_name[split_index:]
        else:
            project = project_name[4:]
        #
        # if province == '贵阳' or campus == '贵阳七星':
        #     province = '贵安'
        #     campus = '贵安七星'
        #     if '贵阳七星' in project_name:
        #         project_name = '贵安七星' + project_name[4:]
        #
        # elif province == '张家口' or campus == '张家口-怀来瑞北':
        #     province = '河北'
        #     campus = '怀来瑞北'
        #     if '张家口怀来瑞北' in project_name:
        #         project_name = '怀来瑞北' + project_name[7:]
        # elif province == '张家口' or campus == '张家口-怀来东园':
        #     province = '河北'
        #     campus = '怀来东园'
        #     if '张家口怀来东园' in project_name:
        #         project_name = '怀来东园' + project_name[7:]
        if "-" in campus:
            campus = campus.replace("-", "")
        # 项目数据存库
        insert_data = {
            'project_name': project_name,
            'region': region,
            'province': province,
            'campus': campus,
            'project': project,
            'demand_distribution': demand_distribution,
            'demand_delivery': demand_delivery,
            'electric_power_demand': electric_power_demand,
            'estimation_period': estimation_period,
            'construction_mode': construction_mode,
            'number_of_rack': number_of_rack,
            'stand_alone_work': stand_alone_work,
            'rack_capacity': rack_capacity,
            'dcopsTicketId': ticket_id,
            'item_number': item_number
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("risk_early_warning_data", insert_data)
        tb_db.commit()
        insert_summary_data = {
            "state": '建设中',
            "campus": campus,
            'project_name': project,
            "area": region,
            "construction_mode": construction_mode,
            "delivery_rack": number_of_rack,
            "rack_constructing": number_of_rack,
            "RequestAcceptanceTime": datetime.datetime.now().strftime("%Y-%m-%d"),
        }
        sql = ("SELECT campus,project_name,area FROM summary_data "
               F"WHERE campus='{campus}' AND project_name='{project}'")
        if tb_db.get_row(sql):
            # 修改summary_data表中的项目状态，建设中
            for key, value in insert_summary_data.items():
                if key not in ['campus', 'project_name']:
                    if value:
                        response_data = gnetops.request(
                            action="Project",
                            method="UpdateSummaryData",
                            ext_data={
                                "campus": campus,
                                "project_name": project,
                                "ticket_id": ticket_id,
                                "update_filed": key,
                                "update_value": value
                            },
                            scheme="ifob-infrastructure",
                        )
        else:

            tb_db.insert("summary_data", insert_summary_data)
        # 结束任务，写入流程变量
        variables = {
            'project_name': project_name,
            'province': province,
            'campus': campus,
            'project': project,
            "campus_bak": campus_bak,
            'construction_mode': construction_mode
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def joint_pass_parameters(self, project_name=None, region=None, province=None, campus=None,
                              demand_distribution=None,
                              demand_delivery=None, electric_power_demand=None, estimation_period=None,
                              construction_mode=None,
                              number_of_rack=None, stand_alone_work=None, rack_capacity=None, item_number=None,
                              project=None):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus_bak = campus
        if "-" in campus:
            campus = campus.replace("-", "")
        # 项目数据存库
        insert_data = {
            'project_name': project_name,
            'region': region,
            'province': province,
            'campus': campus,
            'project': project,
            'demand_distribution': demand_distribution,
            'demand_delivery': demand_delivery,
            'electric_power_demand': electric_power_demand,
            'estimation_period': estimation_period,
            'construction_mode': construction_mode,
            'number_of_rack': number_of_rack,
            'stand_alone_work': stand_alone_work,
            'rack_capacity': rack_capacity,
            'dcopsTicketId': ticket_id,
            'item_number': item_number
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("risk_early_warning_data", insert_data)
        tb_db.commit()

        # 更新summary_data表
        insert_summary_data = {
            "state": '建设中',
            "campus": campus,
            'project_name': project,
            "area": region,
            "construction_mode": construction_mode,
            "delivery_rack": number_of_rack,
            "rack_constructing": number_of_rack,
            "RequestAcceptanceTime": datetime.datetime.now().strftime("%Y-%m-%d"),
        }
        sql = ("SELECT campus,project_name,area FROM summary_data "
               F"WHERE campus='{campus}' AND project_name='{project}'")
        if tb_db.get_row(sql):
            # 修改summary_data表中的项目状态，建设中
            for key, value in insert_summary_data.items():
                if key not in ['campus', 'project_name']:
                    response_data = gnetops.request(
                        action="Project",
                        method="UpdateSummaryData",
                        ext_data={
                            "campus": campus,
                            "project_name": project,
                            "ticket_id": ticket_id,
                            "update_filed": key,
                            "update_value": value
                        },
                        scheme="ifob-infrastructure",
                    )
        else:
            tb_db.insert("summary_data", insert_summary_data)

        # 更新账号开通配置表
        sql_1 = ("SELECT field_name,field_value FROM project_member_basic_info "
                 F"WHERE field_name IN ('合建项目','合建园区')")
        role_query_data = tb_db.get_all(sql_1)
        joint_project_str = ""
        joint_campus_str = ""
        if role_query_data:
            for row in role_query_data:
                if row.get('field_name') == "合建项目":
                    joint_project_list = row.get('field_value', "").split(";")
                    joint_project_list.append(project)
                    joint_project_list = [i for i in set(joint_project_list) if i]
                    joint_project_str = ";".join(joint_project_list)
                if row.get('field_name') == "合建园区":
                    joint_campus_list = row.get('field_value', "").split(";")
                    joint_campus_list.append(campus)
                    joint_campus_list = [i for i in set(joint_campus_list) if i]
                    joint_campus_str = ";".join(joint_campus_list)
        if joint_project_str:
            tb_db.update("project_member_basic_info",
                         data={"field_value": joint_project_str},
                         conditions={"field_name": "合建项目"})
        if joint_campus_str:
            tb_db.update("project_member_basic_info",
                         data={"field_value": joint_campus_str},
                         conditions={"field_name": "合建园区"})

        # 结束任务，写入流程变量
        variables = {
            'project_name': project_name,
            'province': province,
            'campus': campus,
            'project': project,
            "campus_bak": campus_bak,
            'construction_mode': construction_mode
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def organizational_structure_sub_list(self):
        project_name = self.ctx.variables.get("project_name")
        f_instance_id = self.ctx.instance_id
        PM = self.ctx.variables.get("PM")
        variables_map = {
            'f_instance_id': f_instance_id,
            'project_name_list': [project_name],
        }
        tb = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT campus,project FROM risk_early_warning_data "
               f"WHERE project_name='{project_name}'")
        query = tb.get_row(sql)
        campus, project = query.get('campus'), query.get('project')
        ticket_info = gnetops.create_ticket(
            flow_key="wework_dept_apply",
            description='该工单为："' + project_name + '的企微账号组织架构开通申请"服务',
            ticket_level=3,
            title=project_name + ':企微账号组织架构开通申请',
            creator=f"{PM}",
            concern="",  # 关注人, 这里暂为空
            deal="v_zongxiyu",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var=variables_map  # 自定义流程变量，抛给派单子流程
        )
        organizational_structure_ticket = [ticket_info.get('TicketId')]
        variables = {
            'campus': campus,
            'project': project,
            'f_instance_id': f_instance_id,
            'organizational_structure_ticket': organizational_structure_ticket
        }
        flow.complete_task(self.ctx.task_id, variables=variables)


class RiskWarningDate(object):
    """
        风险预警数据
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def display_data(self, campus, project):
        db = mysql.new_mysql_instance("tbconstruct")
        # 需求下发时间
        demand_distribution = self.ctx.variables.get("demand_distribution")
        # 需求交付时间
        demand_delivery = self.ctx.variables.get("demand_delivery")
        # 风险预警-交付延期
        # 提取月份并且计算差值
        precondition_list = []
        demand_distribution = datetime.datetime.strptime(demand_distribution, '%Y-%m-%d').date()
        demand_delivery = datetime.datetime.strptime(demand_delivery, '%Y-%m-%d').date()
        month_difference = (demand_delivery.year - demand_distribution.year) * 12 + \
                           demand_delivery.month - demand_distribution.month
        if month_difference < 7:
            precondition_list.append({
                'risk_item': "交付延期",
                'state': '预警',
            })
        # 风险预警-电力资源（有无变电站）
        dlzy_sql = f"SELECT with_or_without_substation " \
                   f"FROM construction_risk_data " \
                   f"WHERE project_name = '{campus}'"
        dlzy_result = db.get_all(dlzy_sql)
        for row in dlzy_result:
            with_or_without_substation = row.get('with_or_without_substation', None)
            if with_or_without_substation == '无':
                precondition_list.append({
                    'risk_item': "无变电站",
                    'state': '预警',
                })
        # 风险预警-是否跨春节
        # 获取需求下发时间和需求交付时间的年份
        start_year = demand_distribution.year
        end_year = demand_delivery.year
        # 需求下发时间-春节
        lunar_date = lunardate.LunarDate(start_year, 1, 1)
        spring_festival_start = lunar_date.toSolarDate()
        # 需求交付时间-春节
        lunar_date = lunardate.LunarDate(end_year, 1, 1)
        spring_festival_end = lunar_date.toSolarDate()
        # 判断春节是否在需求下发时间和需求交付时间之间
        if demand_distribution <= spring_festival_start <= demand_delivery \
                or demand_distribution <= spring_festival_end <= demand_delivery:
            precondition_list.append({
                'risk_item': "需要跨春节",
                'state': '预警',
            })
        # 风险预警-是否涉及冬施
        campus_list = ['怀来瑞北', '怀来东园', '天津高新']
        start_month = demand_distribution.month
        end_month = demand_delivery.month
        if campus in campus_list:
            if (start_year == end_year and (10 <= start_month <= 12 or 1 <= end_month <= 3)) or \
                    (start_year < end_year and (10 <= start_month or end_month <= 3)):
                precondition_list.append({
                    'risk_item': "涉及冬施",
                    'state': '预警',
                })
        # 风险预警-是否需要新建电力管沟
        xjdlgg_sql = f"SELECT whether_create_new " \
                     f"FROM construction_risk_data " \
                     f"WHERE project_name = '{campus}'"
        xjdlgg_result = db.get_all(xjdlgg_sql)
        for row in xjdlgg_result:
            whether_create_new = row.get('whether_create_new', None)
            if whether_create_new == '是':
                precondition_list.append({
                    'risk_item': "需新建管沟",
                    'state': '预警'
                })
        # 风险预警-供应商资源
        match = re.match(r"^(.*?)([A-Za-z]\d+-\d+)$", project)
        if match:
            campus = match.group(1)
            project = match.group(2)
        else:
            # 如果无法匹配到校区和项目名称，则将校区和项目名称设置为原始字符串
            project = project
        gyszy_sql = "SELECT integrators,project_manager,supervision,examination,AHU," \
                    "low_voltage_cabinet,medium_voltage_cabinet,battery,chaifa,transformer,cabinet," \
                    f"headboard,PDU,tbox FROM supplier_resources WHERE project = '{project}' AND campus = '{campus}'"
        gyszy_result = db.get_all(gyszy_sql)
        gyszy_list = []
        for row in gyszy_result:
            integrators = row.get('integrators', None)
            project_manager = row.get('project_manager', None)
            supervision = row.get('supervision', None)
            examination = row.get('examination', None)
            AHU = row.get('AHU', None)
            low_voltage_cabinet = row.get('low_voltage_cabinet', None)
            medium_voltage_cabinet = row.get('medium_voltage_cabinet', None)
            cabinet = row.get('cabinet', None)
            battery = row.get('battery', None)
            chaifa = row.get('chaifa', None)
            transformer = row.get('transformer', None)
            headboard = row.get('headboard', None)
            PDU = row.get('PDU', None)
            tbox = row.get('tbox', None)
            if any(value is None for value in [integrators, project_manager, supervision, examination, AHU,
                                               low_voltage_cabinet, medium_voltage_cabinet, battery, chaifa,
                                               transformer, headboard, PDU, tbox, cabinet]):
                gyszy_list.append({
                    'risk_item': "供应商资源不足",
                    'state': '预警'
                })
        if len(gyszy_list) > 0:
            precondition_list.append({
                'risk_item': "供应商资源不足",
                'state': '预警'
            })
        # 风险预警消防备案、园区房产证
        sql = "select completion_acceptance,with_without_real_estate_certificate " \
              f"from construction_risk_data WHERE project_name = '{campus}' "
        data_list = db.get_all(sql)
        for i in data_list:
            # 消防备案
            if i['completion_acceptance'] == '无':
                precondition_list.append({
                    'risk_item': "政府侧竣工验收/消防备案",
                    'state': '预警'
                })
            # 园区房产证
            if i['with_without_real_estate_certificate'] == '无':
                precondition_list.append({
                    'risk_item': "园区有无房产证",
                    'state': '预警'
                })
        sql = f"select ability_comment_rest from park_resources WHERE campus = '{campus}' "
        comment_list = db.get_all(sql)
        # 剩余可用能评
        for i in comment_list:
            data_number = i['ability_comment_rest']
            if float(data_number) <= 0:
                precondition_list.append({
                    'risk_item': "剩余能评（当量值）",
                    'state': '预警'
                })
        precondition_list.append({'risk_item': "环保监察，工厂停工"})
        precondition_list.append({'risk_item': "限电政策"})
        precondition_list.append({'risk_item': "大型会议/活动，施工暂停"})
        variables = {
            'precondition_list': precondition_list
        }
        # 结束任务，写入流程变量
        flow.complete_task(self.ctx.task_id, variables)

    def display_data_new(self, campus, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT risk_item,state,effect,details_reason FROM construction_risk_warning_data "
               "WHERE project_name='%s'" % project_name)
        data_list = db.get_all(sql)
        precondition_list = []
        if data_list:
            for item in data_list:
                risk_item = item.get('risk_item')
                state = item.get('state')
                effect = item.get('effect')
                details_reason = item.get('details_reason')
                precondition_list.append({
                    'risk_item': risk_item,
                    'state': state,
                    'effect': effect,
                    'details_reason': details_reason
                })
        flow.complete_task(self.ctx.task_id, variables={'precondition_list': precondition_list})

    def get_project_information(self):
        project_name = self.ctx.variables.get('project_name')
        db = mysql.new_mysql_instance("tbconstruct")
        sql = ("SELECT region,province,campus,demand_distribution,demand_delivery,electric_power_demand,"
               "estimation_period,construction_mode,number_of_rack,stand_alone_work,rack_capacity,item_number,PM "
               f"FROM risk_early_warning_data WHERE project_name = '{project_name}'")
        data_dict = db.get_row(sql)
        if data_dict:
            region = data_dict.get('region')
            province = data_dict.get('province')
            campus = data_dict.get('campus')
            demand_distribution = data_dict.get('demand_distribution')
            demand_delivery = data_dict.get('demand_delivery')
            electric_power_demand = data_dict.get('electric_power_demand')
            estimation_period = data_dict.get('estimation_period')
            construction_mode = data_dict.get('construction_mode')
            number_of_rack = data_dict.get('number_of_rack')
            stand_alone_work = data_dict.get('stand_alone_work')
            rack_capacity = data_dict.get('rack_capacity')
            item_number = data_dict.get('item_number')
            PM = data_dict.get('PM')
            variables = {
                'region': region,
                'province': province,
                'campus': campus,
                'demand_distribution': demand_distribution,
                'demand_delivery': demand_delivery,
                'electric_power_demand': electric_power_demand,
                'estimation_period': estimation_period,
                'construction_mode': construction_mode,
                'number_of_rack': number_of_rack,
                'stand_alone_work': stand_alone_work,
                'rack_capacity': rack_capacity,
                'item_number': item_number,
                'PM': PM
            }
        else:
            variables = {
                'region': '',
                'province': '',
                'campus': '',
                'demand_distribution': '',
                'demand_delivery': '',
                'electric_power_demand': '',
                'estimation_period': '',
                'construction_mode': '',
                'number_of_rack': '',
                'stand_alone_work': '',
                'rack_capacity': '',
                'item_number': '',
                'PM': ''
            }
        flow.complete_task(self.ctx.task_id, variables=variables)


class RiskWarningDisplay(AjaxTodoBase):
    """
        风险提示数据展示
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        precondition_list = process_data.get("precondition_list")
        risk_warning_notes = process_data.get("risk_warning_notes")
        variables = {
            'precondition_list': precondition_list,
            'risk_warning_notes': risk_warning_notes,
        }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PMAllocation(AjaxTodoBase):
    """
        PM分配
    """

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query(self):
        return self.parse()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project = self.ctx.variables.get("project")
        update_data = {
            'PM': process_data.get('PM'),
            'deputy_PM': process_data.get('deputy_PM')
        }
        conditions = {
            'dcopsTicketId': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("risk_early_warning_data", update_data, conditions)
        tb_db.commit()
        response_data = gnetops.request(
            action="Project",
            method="UpdateSummaryData",
            ext_data={
                "campus": campus,
                "project_name": project,
                "ticket_id": ticket_id,
                "update_filed": "PM",
                "update_value": process_data.get('PM')
            },
            scheme="ifob-infrastructure",
        )

        variables = {
            'PM': process_data.get('PM'),
            'deputy_PM': process_data.get('deputy_PM')
        }

        # 结束dcops代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class PMChange(AjaxTodoBase):
    """
        PM变更
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()

    def end(self, process_data, process_user):
        project_name = process_data.get("project_name", "")
        PM_new = process_data.get("PM_new", "")
        deputy_PM_new = process_data.get("deputy_PM_new", "")
        db = mysql.new_mysql_instance("tbconstruct")
        change_remark = process_data.get("change_remark", "")
        # 初始化一个列表来保存要更新的字段
        fields_to_update = {}

        # 如果deputy_PM_new不为空，则添加到要更新的字段列表中
        if deputy_PM_new:
            fields_to_update.update({
                "deputy_PM": deputy_PM_new
            })

        # PM_new 字段前端必填
        fields_to_update.update({
            "PM": PM_new
        })
        conditions = {
            "project_name": project_name
        }

        db.begin()
        db.update("risk_early_warning_data", data=fields_to_update, conditions=conditions)
        db.commit()
        variables = {
            'PM_new': PM_new,
            'deputy_PM_new': deputy_PM_new,
            'change_remark': change_remark
        }
        tb_db = mysql.new_mysql_instance("tbchange")
        conditions_bak={
            "ProjectName": project_name
        }
        fields_to_update_bak = {
            "Pm": PM_new
        }
        tb_db.begin()
        tb_db.update("tb_erp_project", fields_to_update_bak, conditions_bak)
        tb_db.commit()

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class PMChangeSendEmail(object):
    def __init__(self):
        self.ctx = get_context()

    def creat_PM_change_ticket(self, account: str = None, project_name: str = None):
        """
            接口，用于创建PM变更工单
        """
        if not (account == "eddiewu" or account == "v_zongxiyu"):
            return {"code": -1, "msg": "权限不足,如需变更PM请联系eddiewu"}
        else:
            variables_map = {
                "project_name": project_name,
                "account": account
            }
            ticket_info = gnetops.create_ticket(
                flow_key="project_manger_change",
                description='该工单为："' + project_name + 'PM变更"',
                ticket_level=3,
                title=project_name + '-PM变更',
                creator=account,
                concern="",  # 关注人, 这里暂为空
                deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
                source="",
                custom_var=variables_map  # 自定义流程变量，抛给派单子流程
            )
            data = {
                "TicketId": str(ticket_info.get("TicketId"))
            }
            return {"code": 0, "msg": "创建成功", "data": data}

    def get_message(self, project_name):
        """
            变更PM流程获取信息
        """
        db = mysql.new_mysql_instance("tbconstruct")
        sql = """
            SELECT
                PM,
                deputy_PM
            FROM
                risk_early_warning_data
            WHERE
                project_name = '{project_name}'
        """.format(project_name=project_name)
        query_data = db.get_row(sql)
        PM = query_data.get("PM", "")
        deputy_PM = query_data.get("deputy_PM", "")
        variables = {
            'PM': PM,
            'deputy_PM': deputy_PM
        }
        flow.complete_task(self.ctx.task_id, variables=variables)

    def send_email(self, project_name, PM_new, deputy_PM_new, change_remark, PM, deputy_PM):
        """
            变更PM发送邮件
        """
        email_title = "【PM变更】{project_name}-PM变更".format(project_name=project_name)
        email_receiver_list = [PM_new + "@tencent.com", PM + "@tencent.com"]
        if deputy_PM:
            email_receiver_list.append(deputy_PM + "@tencent.com")
        if deputy_PM_new:
            email_receiver_list.append(deputy_PM_new + "@tencent.com")
        email_data = f"【{project_name}】项目经理已调整。具体变更如下：<br>"
        email_data += f"原项目经理A：【{PM}】变更为【{PM_new}】<br>"
        if deputy_PM_new:
            email_data += f"原项目经理B：【{deputy_PM}】变更为【{deputy_PM_new}】<br>"
        email_data += f"请知悉<br>"
        # tof.send_email(sendTitle=email_title, msgContent=email_data,
        #                sendTo=email_receiver_list,
        #                sendCopy=email_receiver_list)
        flow.complete_task(self.ctx.task_id)


class RiskEscalationOptions(AjaxTodoBase):
    """
        风险升级选择
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project_name = self.ctx.variables.get("project_name")
        PM = self.ctx.variables.get("PM")
        process_title = self.ctx.variables.get("ProcessTitle")
        next_op = process_data.get("next_op")
        choose_list = process_data.get("choose_list")
        precondition_list = process_data.get("precondition_list")
        if precondition_list:
            for i in precondition_list:
                i["ticket_id"] = ticket_id
                i['campus'] = campus
                i['project_name'] = project_name
                i['stage'] = process_title
                if 'details_reason' not in i:
                    i['details_reason'] = '无'
                if 'effect' not in i:
                    i['effect'] = '无'
                if 'risk_item' not in i:
                    i['risk_item'] = '无'
                if 'state' not in i:
                    i['state'] = '无'
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("construction_risk_warning_data", precondition_list)
            tb_db.commit()
        if next_op == '是':
            if choose_list:
                if len(process_title) == 0:
                    email_title = '风险升级邮件'
                else:
                    email_title = process_title + '风险升级邮件'
                rich_text_template = f"<h1>{email_title}</h1>\n"
                count = 0
                for item in choose_list:
                    count += 1
                    rich_text_template += (
                        f"<div>"
                        f"<p>{count}.风险项：{item.get('risk_item')}</p>"
                        f"<p>状态：<strong>{item.get('state')}</strong></p>"
                        f"<p>影响(进度/质量/合规)：{item.get('effect')}</p>"
                        f"<p>具体情况及原因：{item.get('details_reason')}</p>"
                        f"</div>\n"
                    )
                variables = {
                    'choose_list': choose_list,
                    'next_op': next_op,
                    'rich_text_template': rich_text_template,
                    'email_title': email_title,
                    'email_receiver': PM,
                    'precondition_list': precondition_list,
                }
            else:
                return {"code": -1, "msg": "未选择风险项，请勾选需要进行升级的风险项"}
        else:
            variables = {
                'precondition_list': precondition_list,
                'next_op': next_op,
            }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PORiskEscalationOptions(AjaxTodoBase):
    """
        PO风险升级选择
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        campus = self.ctx.variables.get("campus")
        project_name = self.ctx.variables.get("project_name")
        PM = self.ctx.variables.get("PM")
        process_title = self.ctx.variables.get("ProcessTitle")
        next_op = process_data.get("next_op")
        choose_list = process_data.get("choose_list")
        precondition_list_2 = process_data.get("precondition_list_2")
        if precondition_list_2:
            for i in precondition_list_2:
                i["ticket_id"] = ticket_id
                i['campus'] = campus
                i['project_name'] = project_name
                i['stage'] = process_title
                if 'details_reason' not in i:
                    i['details_reason'] = '无'
                if 'effect' not in i:
                    i['effect'] = '无'
                if 'risk_item' not in i:
                    i['risk_item'] = '无'
                if 'state' not in i:
                    i['state'] = '无'
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("construction_risk_warning_data", precondition_list_2)
            tb_db.commit()
        if next_op == '是':
            # 获取邮件接收人
            list_commercial_personnel = config.get_config_map(
                "list_commercial_personnel"
            )  # 商务人员名单

            choose_list_2 = choose_list
            if len(process_title) == 0:
                email_title_2 = '风险升级邮件'
            else:
                email_title_2 = process_title + '风险升级邮件'
            rich_text_template_2 = f"<h1>{email_title_2}</h1>\n"
            count = 0
            for item in choose_list_2:
                count += 1
                rich_text_template_2 += (
                    f"<div>"
                    f"<p>{count}.风险项：{item.get('risk_item')}</p>"
                    f"<p>状态：<strong>{item.get('state')}</strong></p>"
                    f"<p>影响(进度/质量/合规)：{item.get('effect')}</p>"
                    f"<p>具体情况及原因：{item.get('details_reason')}</p>"
                    f"</div>\n"
                )
            variables = {
                'choose_list_2': choose_list_2,
                'next_op': next_op,
                'rich_text_template_2': rich_text_template_2,
                'email_title_2': email_title_2,
                'email_receiver': PM,
                'precondition_list_2': precondition_list_2,
                'email_receiver_2': list_commercial_personnel
            }
        else:
            variables = {
                'precondition_list_2': precondition_list_2,
                'next_op': next_op,
            }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RiskEscalationEmailEditing(AjaxTodoBase):
    """
        风险升级：邮件内容编辑
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        email_Cc = process_data.get("email_Cc")
        email_receiver = process_data.get("email_receiver")
        rich_text_template = process_data.get("rich_text_template")
        email_title = process_data.get("email_title")
        if not email_title:
            email_title = self.ctx.variables.get("email_title")
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        warning_handler = process_data.get("warning_handler")
        choose_list = process_data.get("choose_list")
        variables = {
            'email_Cc': email_Cc,
            'email_receiver': email_receiver,
            'rich_text_template': rich_text_template,
            'email_Cc_list': email_Cc_list,
            'email_receiver_list': email_receiver_list,
            'warning_handler': warning_handler,
            'email_title': email_title,
            'choose_list': choose_list
        }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class PORiskEscalationEmailEditing(AjaxTodoBase):
    """
        PO风险升级：邮件内容编辑
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        email_Cc_2 = process_data.get("email_Cc_2")
        email_receiver_2 = process_data.get("email_receiver_2")
        rich_text_template_2 = process_data.get("rich_text_template_2")
        email_title_2 = process_data.get("email_title")
        if not email_title_2:
            email_title_2 = self.ctx.variables.get("email_title_2")
        if email_Cc_2:
            email_Cc_list_2 = email_Cc_2.split(";")
        else:
            email_Cc_list_2 = []
        email_receiver_list_2 = email_receiver_2.split(";")
        for i in range(len(email_Cc_list_2)):
            email_Cc_list_2[i] += "@tencent.com"
        for j in range(len(email_receiver_list_2)):
            email_receiver_list_2[j] += "@tencent.com"
        warning_handler_2 = process_data.get("warning_handler_2")
        choose_list_2 = process_data.get("choose_list_2")
        variables = {
            'email_Cc_2': email_Cc_2,
            'email_receiver_2': email_receiver_2,
            'rich_text_template_2': rich_text_template_2,
            'email_Cc_list_2': email_Cc_list_2,
            'email_receiver_list_2': email_receiver_list_2,
            'warning_handler_2': warning_handler_2,
            "email_title_2": email_title_2,
            'choose_list_2': choose_list_2
        }

        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RiskEscalationApproval(AjaxTodoBase):
    """
        邮件审批
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        email_Cc = process_data.get("email_Cc")
        email_receiver = process_data.get("email_receiver")
        email_approval = process_data.get("email_approval")
        email_rejection = process_data.get("email_rejection")
        rich_text_template = process_data.get("rich_text_template")
        email_title = process_data.get("email_title")
        if not email_title:
            email_title = self.ctx.variables.get("email_title")
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        else:
            email_Cc_list = []
        email_receiver_list = email_receiver.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        if email_approval == '驳回':
            if email_rejection:
                variables = {
                    'email_approval': email_approval,
                    'email_rejection': email_rejection,
                    'email_rejection_show': email_rejection
                }
            else:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            variables = {
                'email_approval': email_approval,
                'email_rejection': email_rejection,
            }
        variables.update({
            'email_Cc': email_Cc,
            'email_receiver': email_receiver,
            'rich_text_template': rich_text_template,
            'email_title': email_title,
            'email_Cc_list': email_Cc_list,
            'email_receiver_list': email_receiver_list,
        })
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class RiskEscalationEmailPreview(AjaxTodoBase):
    """
    审批邮件预览确认发送
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_send = process_data.get("is_send", "")
        if not is_send:
            return {"code": -1, "msg": "请选择是否发送邮件"}
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables={"is_send": is_send})


class SendRiskEscalationEmail(object):
    """
        风险升级：邮件发送
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def email_sending(self):
        ticket_id = self.ctx.variables.get("ticket_id")
        email_Cc_list = self.ctx.variables.get("email_Cc_list", [])
        email_receiver_list = self.ctx.variables.get("email_receiver_list")
        email_title = self.ctx.variables.get("email_title")
        choose_list = self.ctx.variables.get("choose_list")
        email_receiver = self.ctx.variables.get("email_receiver")
        email_Cc = self.ctx.variables.get("email_Cc")
        email_rejection_show = self.ctx.variables.get("email_rejection_show")
        rich_text_template = self.ctx.variables.get("rich_text_template")
        campus = self.ctx.variables.get("campus")
        project_name = self.ctx.variables.get("project_name")

        # po预警信息获取
        email_title_2 = self.ctx.variables.get("email_title_2")
        if email_title_2:
            email_title = email_title_2
        email_Cc_list_2 = self.ctx.variables.get("email_Cc_list_2")
        if email_Cc_list_2:
            email_Cc_list = email_Cc_list_2
        email_receiver_list_2 = self.ctx.variables.get("email_receiver_list_2", [])
        if email_receiver_list_2:
            email_receiver_list = email_receiver_list_2
        choose_list_2 = self.ctx.variables.get("choose_list_2")
        if choose_list_2:
            choose_list = choose_list_2
        email_receiver_2 = self.ctx.variables.get("email_receiver_2")
        if email_receiver_2:
            email_receiver = email_receiver_2
        email_Cc_2 = self.ctx.variables.get("email_Cc_2")
        if email_Cc_2:
            email_Cc = email_Cc_2
        rich_text_template_2 = self.ctx.variables.get("rich_text_template_2")
        if rich_text_template_2:
            rich_text_template = rich_text_template_2

        tof.send_email(sendTitle=email_title, msgContent=rich_text_template,
                       sendTo=email_receiver_list,
                       sendCopy=email_Cc_list)
        if choose_list:
            for i in choose_list:
                i["ticket_id"] = ticket_id
                i["email_Cc"] = email_Cc
                i['email_receiver'] = email_receiver
                i['email_title'] = email_title
                i['email_content'] = rich_text_template
                i['email_rejection'] = email_rejection_show
                i['campus'] = campus
                i['project_name'] = project_name
                if 'details_reason' not in i:
                    i['details_reason'] = '无'
                if 'effect' not in i:
                    i['effect'] = '无'
                if 'risk_item' not in i:
                    i['risk_item'] = '无'
                if 'state' not in i:
                    i['state'] = '无'
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("construction_risk_upgrade_data", choose_list)
            tb_db.commit()
        flow.complete_task(self.ctx.task_id)


class RiskWarningNotificationToBusinessHandlers(AjaxTodoBase):
    """
        PO预警通知给处理人
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_know = process_data.get("is_know")
        if is_know:
            if is_know == '是':
                variables = {
                    'is_know': is_know
                }
            else:
                warning_handler_rejection = process_data.get("warning_handler_rejection")
                if not warning_handler_rejection:
                    return {"code": -1, "msg": "请填写驳回原因"}
                variables = {
                    'is_know': is_know,
                    'warning_handler_rejection': warning_handler_rejection
                }
        is_know_2 = process_data.get("is_know_2")
        if is_know_2:
            if is_know_2 == '是':
                variables = {
                    'is_know_2': is_know_2
                }
            else:
                warning_handler_rejection_2 = process_data.get("warning_handler_rejection_2")
                if not warning_handler_rejection_2:
                    return {"code": -1, "msg": "请填写驳回原因"}
                variables = {
                    'is_know_2': is_know_2,
                    'warning_handler_rejection_2': warning_handler_rejection_2
                }
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaitEarlyWarningContentConfirmation(object):
    """
        等待预警内容确认
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait_confirmation(self):
        """
            循环等待确认
        """
        f_instance_id = self.ctx.variables.get("f_instance_id")
        data_instanceId = {
            "InstanceId": f_instance_id
        }

        # 请求获取工作流日志
        res = gnetops.request(action="TaskCenter", method="QueryWorkflowLog", data=data_instanceId)

        now_time = datetime.datetime.now()

        for row in res:
            TaskName = row.get('TaskName')
            TaskStatus = row.get('TaskStatus')
            # 处理 "PO预警内容确认" 任务
            if TaskName == "PO预警内容确认":
                CreateTime = row.get('CreateTime')
                create_time = datetime.datetime.strptime(CreateTime, '%Y-%m-%d %H:%M:%S')
                TaskId = row.get('TaskId')
                if TaskStatus != "已完成":
                    if create_time:  # 确保 CreateTime 已经被设置
                        time_difference = now_time - create_time
                        if time_difference.days == 1:
                            variables = {
                                "is_know": "是"
                            }
                            WorkflowContinue(task_id=TaskId).call()
                            flow.complete_task(self.ctx.task_id, variables=variables)
                            return {"success": True, "data": "节点已结束"}
                elif TaskStatus == "已完成":
                    flow.complete_task(self.ctx.task_id, variables={})
                    return {"success": True, "data": "节点已结束"}


class RiskWarningHandlerNotAccepted(AjaxTodoBase):
    """
        预警处理人未接受
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        variables = {}
        # 结束代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class ProjectTeamFormation(AjaxTodoBase):
    """
        项目团队组建
    """

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        PM = self.ctx.variables.get("PM")

        insert_data = {
            'project_name': process_data.get('project_name'),
            'dcopsTicketId': ticket_id,
            'PM': PM,
            'architecture': process_data.get('architecture'),
            'beautification': process_data.get('beautification'),
            'tree_sutra': process_data.get('tree_sutra'),
            'weak_current': process_data.get('weak_current'),
            'business': process_data.get('business'),
            'supply_chain': process_data.get('supply_chain'),
            'inform_a_person': process_data.get('inform_a_person'),
        }

        variables = {
            # 周日报处理人以及周报上传时间
            'week_date': process_data.get('week_date'),
            'deal_with_human': process_data.get('deal_with_human'),
            'daily_processor': process_data.get('daily_processor'),
            'architecture': process_data.get('architecture'),
            'beautification': process_data.get('beautification'),
            'tree_sutra': process_data.get('tree_sutra'),
            'weak_current': process_data.get('weak_current'),
            'business': process_data.get('business'),
            'supply_chain': process_data.get('supply_chain'),
            'inform_a_person': process_data.get('inform_a_person'),
            'team_info_insert_data': insert_data
        }

        # 结束dcops代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class WeeklyNewspaperAndDailyPaperDataStorage(object):
    """
        周报和日报数据存储
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def data_storage(self):
        """
            存储周报日报起单数据
        """
        PM = self.ctx.variables.get("PM")
        ticket_id = self.ctx.variables.get("ticket_id")
        project_name = self.ctx.variables.get("project_name")
        week_date = self.ctx.variables.get("week_date")
        deal_with_human = self.ctx.variables.get("deal_with_human")
        daily_processor = self.ctx.variables.get("daily_processor")

        week_whether_there = False
        daily_whether_there = False
        db = mysql.new_mysql_instance("tbconstruct")
        week_sql = (
            "SELECT project_name "
            "FROM week_create_info "
            f"WHERE project_name = '{project_name}'"
        )
        daily_sql = (
            "SELECT project_name "
            "FROM daily_create_info "
            f"WHERE project_name = '{project_name}'"
        )
        week_result = db.get_all(week_sql)
        daily_result = db.get_all(daily_sql)
        if week_result:
            week_whether_there = True
        if daily_result:
            daily_whether_there = True

        db.begin()
        if week_whether_there == False:
            insert_data = {
                "ticketid": ticket_id,
                "week_date": week_date,
                "deal_with_human": deal_with_human,
                "project_name": project_name,
                'handlers': PM
            }
            db.insert("week_create_info", insert_data)
        if daily_whether_there == False:
            insert_data2 = {
                'ticketid': ticket_id,
                'daily_processor': daily_processor,
                'project_name': project_name,
                'handlers': PM
            }
            db.insert("daily_create_info", insert_data2)

        db.commit()

        variables = {
            'week_date': week_date,
            'deal_with_human': deal_with_human,
            'daily_processor': daily_processor
        }
        flow.complete_task(self.ctx.task_id, variables)


class ProjectStartUp(AjaxTodoBase):
    """
        项目启动
    """

    def update(self, process_data, process_user):
        pass

    def start(self):
        pass

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def plan_check(self, progress, ticket_id, project_name):
        url = progress[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel解析总控计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl', sheet_name='总控计划模版')
        df = df.replace({np.nan: None})
        plan_data = []
        for _, row in df.iterrows():
            serial_number = row['序号']
            work_content = row['工作内容']
            plan_duration_construction = row['工期']
            start_time = row['计划开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['计划开始时间（年/月/日）']) else None
            completion_time = row['计划完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                row['计划完成时间（年/月/日）']) else None
            responsible_person = row['责任人（开通账号人员）']
            output_object = row['输出物']
            input_object = row['输入物']
            construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
            whethe_involves = row['是否涉及（是/否）']
            plan_data.append({
                'ticket_id': ticket_id,
                'project_name': project_name,
                'serial_number': serial_number,
                'work_content': work_content,
                'plan_duration_construction': plan_duration_construction,
                'start_time': start_time,
                'completion_time': completion_time,
                'responsible_person': responsible_person,
                'output_object': output_object,
                'input_object': input_object,
                'construction_attribute': construction_attribute,
                'whethe_involves': whethe_involves
            })
        for row in plan_data:
            if row.get('whethe_involves') == '是' or not row.get('whethe_involves'):
                if row.get('work_content') != '其他':
                    if not row.get('start_time'):
                        return {
                            'code': -1,
                            'msg': f'{row.get("serial_number")}，{row.get("work_content")}是涉及的工作项，'
                                   f'计划开始时间/计划结束时间不能为空'
                        }

                    elif not row.get('completion_time'):
                        return {
                            'code': -1,
                            'msg': f'{row.get("serial_number")}，{row.get("work_content")}是涉及的工作项，'
                                   f'计划开始时间/计划结束时间不能为空'
                        }
                    else:
                        return {
                            'code': 0,
                            'msg': 'success',
                            'data': plan_data
                        }

    def end(self, process_data, process_user):
        project_name = self.ctx.variables.get("project_name")
        ticket_id = self.ctx.variables.get("ticket_id")
        team_info_insert_data = self.ctx.variables.get("team_info_insert_data")
        minutes_of_start_up_meeting = process_data.get("minutes_of_start_up_meeting", None)
        progress = process_data.get("progress", None)

        # 解析总控计划校验时间
        time_verification = GetDataInLibrary()
        url = progress[0]["response"]["FileList"][0]["url"]
        msg_data = time_verification.analyze_control_plan_bak(project_name, url)
        if msg_data.get("verification_passed") == 0:
            return {
                'code': -1,
                'msg': msg_data.get("msg")
            }

        safety = process_data.get("safety", None)
        quality_and_other = process_data.get("quality_and_other", None)
        # mozu_code = process_data.get("mozu_code", None)
        # building_no = process_data.get("building_no", None)
        # infra_ops_provider = process_data.get("infra_ops_provider", "")

        # 需求交付时间
        demand_delivery = self.ctx.variables.get("demand_delivery")
        PM = self.ctx.variables.get("PM")
        deputy_PM = self.ctx.variables.get("deputy_PM")
        architecture = self.ctx.variables.get('architecture')
        beautification = self.ctx.variables.get('beautification')
        tree_sutra = self.ctx.variables.get('tree_sutra')
        weak_current = self.ctx.variables.get('weak_current')
        business = self.ctx.variables.get('business')
        supply_chain = self.ctx.variables.get('supply_chain')
        inform_a_person = self.ctx.variables.get('inform_a_person')
        # 项目启动完成时间
        startup_time = process_data.get("startup_time", "")
        campus = self.ctx.variables.get("campus", "")
        project = self.ctx.variables.get("project", "")
        if startup_time:
            response_data = gnetops.request(
                action="Project",
                method="UpdateSummaryData",
                ext_data={
                    "campus": campus,
                    "project_name": project,
                    "ticket_id": ticket_id,
                    "update_filed": "ProjectStartTime",
                    "update_value": startup_time
                },
                scheme="ifob-infrastructure",
            )
        if PM:
            PM_list = PM.split(';')
        else:
            PM_list = []
        if deputy_PM:
            deputy_PM_list = deputy_PM.split(';')
        else:
            deputy_PM_list = []
        if architecture:
            architecture_list = architecture.split(';')
        else:
            architecture_list = []
        if beautification:
            beautification_list = beautification.split(';')
        else:
            beautification_list = []
        if tree_sutra:
            tree_sutra_list = tree_sutra.split(';')
        else:
            tree_sutra_list = []
        if weak_current:
            weak_current_list = weak_current.split(';')
        else:
            weak_current_list = []
        if business:
            business_list = business.split(';')
        else:
            business_list = []
        if supply_chain:
            supply_chain_list = supply_chain.split(';')
        else:
            supply_chain_list = []
        if inform_a_person:
            inform_a_person_list = inform_a_person.split(';')
        else:
            inform_a_person_list = []

        receivers_list = (
                PM_list + deputy_PM_list + architecture_list + beautification_list + tree_sutra_list +
                supply_chain_list + weak_current_list + business_list + inform_a_person_list)
        # 企微发送人
        new_list = list(set(filter(None, receivers_list)))
        # 邮件发送人
        email_list = []
        for i in new_list:
            i += "@tencent.com"
            email_list.append(i)

        # 企微内容
        chat_data = f"{project_name}已启动，交付目标为{demand_delivery},请各团队知悉，谢谢！"
        # 邮件内容
        email_data = ''
        email_data += f"<p><strong>【项目名称】:</strong> {project_name}</p>"
        email_data += f"<p><strong>【阶段】:</strong> 项目启动阶段</p>"
        email_data += f"<p><strong>【交付时间】:</strong> {demand_delivery}</p>"
        email_data += (f"<p><strong>【团队成员】:</strong>"
                       f" PM：{PM}，架构：{architecture}，优化：{beautification}，数经：{tree_sutra}，弱电：{weak_current}，"
                       f"商务：{business}，供应链：{supply_chain}，知会人：{inform_a_person}</p>")
        email_data += (f"<p><strong>【工单链接】:</strong> "
                       f"<a href='https://dcops.woa.com/operateManage/business/ToDoDetails?params={ticket_id}"
                       f"&isHistory=1'>点击此处查看工单详情</a></p>")

        title = f"{project_name} 项目启动已完成"
        # 企微
        tof.send_company_wechat_message(receivers=new_list, title=title, message=chat_data)
        # 邮件
        tof.send_email(sendTitle=title, msgContent=email_data, sendTo=email_list)

        minutes_of_start_up_meeting_url = []
        minutes_of_start_up_meeting_table = []
        if minutes_of_start_up_meeting is not None:
            for doc in minutes_of_start_up_meeting:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        minutes_of_start_up_meeting_url.append(url)
                        minutes_of_start_up_meeting_table.append({
                            "minutes_of_start_up_meeting": url,
                            'minutes_of_start_up_meeting_name': name
                        })

        safety_url = []
        safety_table = []
        if safety is not None:
            for doc in safety:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        safety_url.append(url)
                        safety_table.append({
                            "safety": url,
                            'safety_name': name
                        })

        quality_and_other_url = []
        quality_and_other_table = []
        if quality_and_other is not None:
            for doc in quality_and_other:
                if doc is not None and "response" in doc and "FileList" in doc["response"]:
                    file_list = doc["response"]["FileList"]
                    if len(file_list) > 0 and "url" in file_list[0]:
                        url = file_list[0]["url"]
                        name = file_list[0]['name']
                        quality_and_other_url.append(url)
                        quality_and_other_table.append({
                            "quality_and_other": url,
                            'quality_and_other_name': name
                        })
        minutes_of_start_up_meeting_join = ', '.join(minutes_of_start_up_meeting_url)
        safety_join = ', '.join(safety_url)
        quality_and_other_join = ', '.join(quality_and_other_url)
        insert_data = {}
        if minutes_of_start_up_meeting and len(minutes_of_start_up_meeting) > 0 and 'response' in \
                minutes_of_start_up_meeting[0]:
            insert_data = {
                'project_name': process_data['project_name'],
                'dcopsTicketId': ticket_id,
                'minutes_of_start_up_meeting': minutes_of_start_up_meeting_join,
                'progress': progress[0]["response"]["FileList"][0]["url"],
                'safety': safety_join,
                'quality_and_other': quality_and_other_join
            }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("file_storage_data", insert_data)
        tb_db.commit()

        team_info_insert_data['AHU'] = process_data.get('AHU')
        team_info_insert_data['firewood_hair'] = process_data.get('firewood_hair')
        team_info_insert_data['low_pressure_cabinet'] = process_data.get('low_pressure_cabinet')
        team_info_insert_data['medium_pressure_cabinet'] = process_data.get('medium_pressure_cabinet')
        team_info_insert_data['transformers'] = process_data.get('transformers')
        team_info_insert_data['synthesis_distribution_cabinet'] = process_data.get('synthesis_distribution_cabinet')
        team_info_insert_data['HVDC'] = process_data.get('HVDC')
        team_info_insert_data['battery'] = process_data.get('battery')
        team_info_insert_data['PDU'] = process_data.get('transformers')
        team_info_insert_data['cabinet'] = process_data.get('cabinet')
        team_info_insert_data['integrator'] = process_data.get('integrator')
        team_info_insert_data['item_tube'] = process_data.get('item_tube')
        team_info_insert_data['supervision_and_management'] = process_data.get('supervision_and_management')
        team_info_insert_data['third_party_testing'] = process_data.get('third_party_testing')

        tb_db.begin()
        tb_db.insert("project_team_information_data", team_info_insert_data)
        tb_db.commit()

        variables = {
            'AHU': process_data.get('AHU'),
            'firewood_hair': process_data.get('firewood_hair'),
            'low_pressure_cabinet': process_data.get('low_pressure_cabinet'),
            'medium_pressure_cabinet': process_data.get('medium_pressure_cabinet'),
            'transformers': process_data.get('transformers'),
            'synthesis_distribution_cabinet': process_data.get('synthesis_distribution_cabinet'),
            'HVDC': process_data.get('HVDC'),
            'battery': process_data.get('battery'),
            'PDU': process_data.get('PDU'),
            'cabinet': process_data.get('cabinet'),
            'integrator': process_data.get('integrator'),
            'item_tube': process_data.get('item_tube'),
            'supervision_and_management': process_data.get('supervision_and_management'),
            'third_party_testing': process_data.get('third_party_testing'),
            "minutes_of_start_up_meeting": minutes_of_start_up_meeting,
            "progress": progress,
            "safety": safety,
            "quality_and_other": quality_and_other,
            "startup_time": startup_time,
            "insert_data": insert_data,
        }

        # 结束dcops代办
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class ProjectStartupConnectToIDC(object):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def idc_ticket_info_fill(self, tree_sutra, project_name, construction_mode, PM, campus_bak, demand_delivery):
        """
        idc平台起单参数填写工单
        """
        idc_manager = tree_sutra
        sql = (f"SELECT item_number FROM risk_early_warning_data WHERE project_name = '{project_name}'")
        db = mysql.new_mysql_instance("tbconstruct")
        data_dict = db.get_row(sql)
        project_code = data_dict.get("item_number")
        search_condition = {"campus_name": [campus_bak]}
        data_campus_list = idcdb.get_list(table="mozu", fields=["mozu_name", "mozu_belongSystemName"],
                                          search_condition=search_condition)
        building_no_list = [f"{i}" for i in range(1, 11)]
        # 过滤出属于 Tblock 系统的模组
        mozu_name_list = [i for i in data_campus_list if i["mozu_belongSystemName"] == "Tblock系统"]

        # 提取已有楼宇编号和模组编号
        existing_buildings = set()
        existing_codes = {}

        for item in mozu_name_list:
            mozu_name = item["mozu_name"]
            building_no = mozu_name[-2]
            mozu_code = mozu_name[-1]
            existing_buildings.add(building_no)
            if building_no not in existing_codes:
                existing_codes[building_no] = set()
            existing_codes[building_no].add(mozu_code)

        # 找到未建的最小楼宇编号
        unused_building = None
        for building in building_no_list:
            if building not in existing_buildings:
                unused_building = building
                break

        # 查找半栋楼宇（即没有模组1或没有模组2）
        half_buildings = []
        for building in building_no_list:
            if building in existing_codes and (
                    '1' not in existing_codes[building] or '2' not in existing_codes[building]):
                half_buildings.append(building)

        # 初始化 min_building_no 和 min_mozu_code
        min_building_no = None
        min_mozu_code = None

        if half_buildings:
            # 如果有半栋楼宇，则选择最小的半栋楼宇编号，并设置 mozu_code
            min_building_no = min(half_buildings, key=int)  # 找到最小的半栋楼宇编号
            if "2" in existing_codes[min_building_no]:
                min_mozu_code = min_building_no + '1'
            else:
                min_mozu_code = min_building_no + '2'
        else:
            # 如果没有半栋楼宇，则选择未建的最小楼宇编号，并设置 mozu_code 为1
            if unused_building:
                min_building_no = unused_building
                min_mozu_code = unused_building + '1'
        ticket_info = gnetops.create_ticket(
            flow_key="project_start_connect_idc_info",
            description='该工单为："' + project_name + '-idc平台起单数据填写',
            ticket_level=3,
            title=project_name + '：idc平台起单数据填写',
            creator="v_zongxiyu",
            concern="youngshi",  # 关注人, 这里暂为空
            deal="youngshi",  # 这里只能是一个字符串，如以上deal格式，则多人可以同时收到待办工单
            source="",
            custom_var={
                "project_name": project_name,
                "idc_manager": idc_manager,
                "construction_mode": construction_mode,
                "PM": PM,
                "campus_name": campus_bak,
                "project_code": project_code,
                "building_no": min_building_no,
                "mozu_code": min_mozu_code,
                "building_no_bak": min_building_no,
                "mozu_code_bak": min_mozu_code,
                "mozu_name_list": mozu_name_list,
                "demand_delivery": demand_delivery
            }
        )
        variables = {
            "idc_ticket_info_fill_ticket": ticket_info.get("TicketId"),
        }
        flow.complete_task(self.ctx.task_id, variables)

    def create_ticket_idc_service(self):
        """
        启idc平台单
        """
        mozu_code = self.ctx.variables.get("mozu_code")
        building_no = self.ctx.variables.get("building_no")
        campus_name = self.ctx.variables.get("campus_name")
        idc_manager = self.ctx.variables.get("idc_manager", "")
        assistant_data_manager = self.ctx.variables.get("assistant_data_manager", "")
        infra_ops_provider = self.ctx.variables.get("infra_ops_provider", "")
        infrastructure_owner = self.ctx.variables.get("infrastructure_owner", "")
        construction_mode = self.ctx.variables.get("construction_mode", "")
        project_name = self.ctx.variables.get("project_name")
        project_code = self.ctx.variables.get("project_code")
        mozu_beginTime = self.ctx.variables.get("demand_delivery")
        PM = self.ctx.variables.get("PM")
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (f"SELECT campus,project,dcopsTicketId FROM risk_early_warning_data "
               f"WHERE project_name = '{project_name}'")
        query_data = db.get_row(sql)
        if query_data:
            campus = query_data.get("campus")
            project = query_data.get("project")
            ticket_id = query_data.get("dcopsTicketId")
            if campus and project and ticket_id:
                response_data = gnetops.request(
                    action="Project",
                    method="UpdateSummaryData",
                    ext_data={
                        "campus": campus,
                        "project_name": project,
                        "ticket_id": ticket_id,
                        "update_filed": "building_name",
                        "update_value": f"{campus_name}{building_no}号楼"
                    },
                    scheme="ifob-infrastructure",
                )
        response = gnetops.request(
            action="Nbroker",
            method="UrlProxy",
            ext_data={
                "NbrokerData": {
                    "args": {
                        "project_owner": PM,
                        "build_scenario_code": 1 if construction_mode == "自建" else 2,
                        "mozu_code": mozu_code,
                        "idcrm_project_code": project_code,
                        "project_code": project_name,
                        "idc_manager": idc_manager,
                        "assistant_data_manager": assistant_data_manager,
                        "building_no": building_no,
                        "campus_name": campus_name,
                        "infra_ops_provider": infra_ops_provider,
                        "infrastructure_owner": infrastructure_owner,
                        "mozu_beginTime": mozu_beginTime
                    },
                    "context": {
                        "method_name": "ticket_start",
                        "service_name": "DcOpsMozuIdcdbInitFlow"
                    }
                },
                "ServiceUrl": "http://ibroker-cfgmanage:8080/nBroker/api/v1/task"
            }
        )
        variables = {
            "idc_response": response
        }
        flow.complete_task(self.ctx.task_id, variables)


class IDCTicketInfoFill(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        mozu_code_bak = self.ctx.variables.get("mozu_code_bak")
        building_no_bak = self.ctx.variables.get("building_no_bak")
        mozu_code = process_data.get("mozu_code")
        building_no = process_data.get("building_no")
        assistant_data_manager = process_data.get("assistant_data_manager")
        infra_ops_provider = process_data.get("infra_ops_provider")
        infrastructure_owner = process_data.get("infrastructure_owner")
        mozu_name = process_data.get("mozu_name", "")
        variables = {
            "mozu_code": mozu_code,
            "building_no": building_no,
            "assistant_data_manager": assistant_data_manager,
            "infra_ops_provider": infra_ops_provider,
            "infrastructure_owner": infrastructure_owner,
            "mozu_name": mozu_name
        }
        if str(mozu_code) == mozu_code_bak and str(building_no) == building_no_bak:
            correspond = "1"
        else:
            correspond = "0"
        variables.update({"correspond": correspond})
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class IDCTicketInfoFillPMReview(AjaxTodoBase):
    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        is_pass = process_data.get("is_pass")
        PM_remark = process_data.get("PM_remark", "")
        PM_rejection = process_data.get("PM_rejection", "")
        mozu_name = process_data.get("mozu_name", "")
        project_name = self.ctx.variables.get("project_name")
        project_code = self.ctx.variables.get("project_code")
        variables = {
            "is_pass": is_pass,
            "PM_remark": PM_remark,
            "PM_rejection": PM_rejection,

        }
        if is_pass == "驳回":
            if not PM_rejection:
                return {"code": -1, "msg": "请填写驳回原因"}
        else:
            insert_data = {
                "module_name": mozu_name,
                "project_name": project_name,
                "item_number": project_code
            }
            db = mysql.new_mysql_instance("tbconstruct")
            db.insert("project_module_mapping_relationship", insert_data)
            variables.update({
                "mozu_code": process_data.get("mozu_code"),
                "building_no": process_data.get("building_no"),
            })

        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables)


class CosFileDownload(object):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def Download(self):
        ticket_id = self.ctx.variables.get("ticket_id")
        minutes_of_start_up_meeting = self.ctx.variables.get('minutes_of_start_up_meeting')
        progress = self.ctx.variables.get('progress')
        safety = self.ctx.variables.get('safety')
        quality_and_other = self.ctx.variables.get('quality_and_other')

        file_names = [minutes_of_start_up_meeting, progress, safety, quality_and_other]
        for file_name in file_names:
            download_url = COSLib.getFileUrl(file_name)

            response = requests.get(download_url)
            if response.status_code == 200:
                save_to_file = f'downloaded_{file_name}'
                with open(save_to_file, 'wb') as f:
                    f.write(response.content)
                print(f'文件{file_name}下载成功')
                wb = load_workbook(filename=save_to_file)
                for sheet_name in wb.sheetnames:
                    sheet = wb[sheet_name]
                    print(f'工作表:{sheet_name}')
                    for row in sheet.iter_rows(values_only=True):
                        for cell_value in row:
                            print(cell_value)
                print(f"文件{file_name}数据读取完成")
            else:
                print(f'文件{file_name}下载失败')


class WaitingForProjectStart(object):
    """
    自动任务
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def wait(self):
        # 等待审核信息
        db = mysql.new_mysql_instance("tbconstruct")
        item_number = self.ctx.variables.get('item_number')
        sql = f"select audit_result,demand_code from risk_early_warning_data WHERE item_number = '{item_number}' "
        info_list = db.get_all(sql)
        if info_list:
            audit_result = info_list[0].get('audit_result')
            demand_code = info_list[0].get('demand_code')
            variables = {
                "demand_code": demand_code,
                "audit_result": audit_result
            }
            if audit_result == "通过":
                campus = self.ctx.variables.get('campus')
                project = self.ctx.variables.get('project')
                ticket_id = self.ctx.variables.get('ticket_id')
                pass_time = datetime.datetime.now().strftime('%Y-%m-%d')
                response_data = gnetops.request(
                    action="Project",
                    method="UpdateSummaryData",
                    ext_data={
                        "campus": campus,
                        "project_name": project,
                        "ticket_id": ticket_id,
                        "update_filed": "SupplyStartTime",
                        "update_value": pass_time
                    },
                    scheme="ifob-infrastructure",
                )
                flow.complete_task(self.ctx.task_id, variables=variables)
                return {"success": True, "data": "流程已结束"}

    def waiting_pass_send_email(self, project_name, PM):
        email_title = "采购需求审批结果通知"
        email_content = f"【{project_name}】采购需求审批完毕，请督促商务尽快下发po"
        email_list = [PM + "@tencent.com"]
        # 邮件
        tof.send_email(sendTitle=email_title, msgContent=email_content, sendTo=email_list)
        flow.complete_task(self.ctx.task_id)


class AnalyzeTheMasterControlPlanBak(object):
    """
        解析总控计划
    """

    def __init__(self):
        super(AnalyzeTheMasterControlPlanBak, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def analyze_control_plan(self, project_name):
        progress = self.ctx.variables.get("progress")
        # 获取当前时间
        now_time = datetime.datetime.now().strftime('%Y-%m-%d')
        if progress:
            ticket_id = self.ctx.variables.get("ticket_id")
            url = progress[0]["response"]["FileList"][0]["url"]
            relative_url = url.split("cosfile")[1]
            new_file_name = str(uuid.uuid4()) + '.xlsx'
            print(new_file_name)
            COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
            """
                excel解析总控计划
            """
            exclpath = "/data/nbroker/" + new_file_name
            df = pd.read_excel(exclpath, engine='openpyxl', sheet_name='总控计划模版')
            df = df.replace({np.nan: None})

            # 获取所有列名，用于判断
            columns = df.columns.tolist()

            """
                所有数据存库
            """
            insert_data = []
            insert_data_new = []
            for _, row in df.iterrows():
                serial_number = row['序号']
                work_content = row['工作内容']
                plan_duration_construction = row['工期']
                start_time = row['计划开始时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划开始时间（年/月/日）']) else None
                completion_time = row['计划完成时间（年/月/日）'].strftime("%Y-%m-%d") if not pd.isnull(
                    row['计划完成时间（年/月/日）']) else None
                responsible_person = row['责任人（开通账号人员）']
                output_object = row['输出物']
                input_object = row['输入物']
                construction_attribute = row['施工属性（电气/暖通/弱电/装修（结构）/消防）']
                whethe_involves = row.get('是否涉及（是/否）') if '是否涉及（是/否）' in columns else None

                # 可以根据需要进一步判断 whethe_involves 是否为“是”或“否”，并做校验
                if whethe_involves not in (None, '是', '否'):
                    whethe_involves = "是"  # 非法值统一置为是
                insert_data.append({
                    'ticket_id': ticket_id,
                    'project_name': project_name,
                    'serial_number': serial_number,
                    'work_content': work_content,
                    'plan_duration_construction': plan_duration_construction,
                    'start_time': start_time,
                    'completion_time': completion_time,
                    'responsible_person': responsible_person,
                    'output_object': output_object,
                    'input_object': input_object,
                    'construction_attribute': construction_attribute,
                    'whethe_involves': whethe_involves,
                    'now_time': now_time
                })
            time_verification = GetDataInLibrary()
            zkjh_table = time_verification.time_disposal_new(insert_data, project_name)
            msg = ""
            # 时间校验判断
            verification_passed = 1
            # 新增判断：是否是错误信息？
            if isinstance(zkjh_table, dict) and zkjh_table.get("code") == -1:
                # 返回错误提示给前端
                verification_passed = 0
                msg = zkjh_table.get("msg")
            # 确保 zkjh_table 是列表
            if not isinstance(zkjh_table, list):
                verification_passed = 0
            if verification_passed == 0:
                self.workflow_detail.add_kv_table('解析总控计划', {"success": False, "data": msg})
                return {"success": False, "data": msg}
            else:
                for i in zkjh_table:
                    insert_data_new.append({
                        "ticket_id": ticket_id,
                        "project_name": project_name,
                        "serial_number": i.get("serial_number"),
                        "work_content": i.get("work_content"),
                        "plan_duration_construction": i.get("plan_duration_construction"),
                        "start_time": i.get("start_time"),
                        "completion_time": i.get("completion_time"),
                        "responsible_person": i.get("responsible_person"),
                        "output_object": i.get("output_object"),
                        "input_object": i.get("input_object"),
                    })
            tb_db = mysql.new_mysql_instance("tbconstruct")
            tb_db.begin()
            tb_db.insert_batch("excel_data", insert_data)
            tb_db.insert_batch("construct_plan_data", insert_data_new)
            tb_db.commit()
        else:
            self.workflow_detail.add_kv_table('解析总控计划', {"success": False, "data": "未获取到总控计划"})
        flow.complete_task(self.ctx.task_id)


class QueryPOInformationDuringProjectStartup(object):
    """
        项目启动阶段查询PO信息
    """

    def __init__(self):
        super(QueryPOInformationDuringProjectStartup, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def query_po_information(self, project_name):
        db = mysql.new_mysql_instance("tbconstruct")
        po_material_info = []
        # 获取po
        sql = (
            "SELECT rn.idcrmOrderCode FROM risk_early_warning_data rewd "
            "JOIN request_number rn ON rewd.item_number = rn.idcrmProjectCode "
            f"WHERE rewd.project_name = '{project_name}' "
        )
        code_id_list = db.query(sql)
        diff_set = ''
        self.workflow_detail.add_kv_table("1.获取需求id", {"len(code_id_list)": code_id_list})
        if code_id_list:
            material_id_list = []
            # 需求单号
            for i in code_id_list:
                code_id = i.get("idcrmOrderCode")
                resp = curl.get(
                    title="Dcops获取po信息",
                    system_name="Dcops",
                    url=f"http://api.xlink.woa.com/tscm-service-idc-order/idc-order/"
                        f"queryOrderByFormCode/{code_id}",
                    postdata="",
                    append_header={"Content-Type": "application/json"})
                po_info = resp.json()
                if po_info:
                    po_info_list = []
                    # 所有的物料id
                    for data in po_info.get("data"):
                        id = data.get("orderItemId")
                        data["orderItemId"] = str(id)
                        po_info_list.append(data)
                    po_material_info += po_info_list
                    self.workflow_detail.add_kv_table("po信息及物料id",
                                                      {"len(po_info_list)": len(po_info_list)})
            if po_material_info:
                # 提取所有的物料id
                for j in po_material_info:
                    material_id = j.get("materialId")
                    if material_id not in material_id_list:
                        material_id_list.append(material_id)
                self.workflow_detail.add_kv_table("物料id", {"message": material_id_list})

                # 通过物料id获取品名信息
                material_list = gnetops.request(action="Tbconstruct",
                                                method="ObtainMaterialInfoById",
                                                ext_data={
                                                    "MaterialCodeId": material_id_list
                                                }
                                                )

                if material_list:
                    material_info_list = material_list.get("materialList")
                    # 构造品名、物料编码、物料id
                    data = []
                    if material_info_list:
                        for material in material_info_list:
                            if material.get("materialQueryDTO"):
                                data.append(
                                    {
                                        # 物料编码
                                        "material_code": material.get("materialCode"),
                                        # 物料id
                                        "material_id": material.get("materialQueryDTO").get(
                                            "materialId"
                                        ),
                                        # 物料名称
                                        "material_name": material.get("materialQueryDTO").get(
                                            "materialName"
                                        ),
                                        # 品名
                                        "material_category_name": material.get(
                                            "materialQueryDTO"
                                        ).get("materialCategoryName"),
                                        # 品名编码
                                        "material_category_code": material.get(
                                            "materialQueryDTO"
                                        ).get("materialCategoryCode"),
                                    }
                                )
                        # self.workflow_detail.add_kv_table("构造的数据", {"message": data})

                        # 查询品名合设备名称关系
                        db = mysql.new_mysql_instance("tbconstruct")
                        sql = (
                            "SELECT device_name,material_category_name FROM equipment_category_mapping_relationship"
                        )
                        material_category_name_query = db.query(sql)
                        # 对应品名后的物料信息

                        if material_category_name_query:
                            exist_product_name_set = set()
                            device_name_set = set()
                            exist_device_name_set = set()
                            # 一个设备所对应的所以品名
                            material_category_name = []
                            for category in material_category_name_query:
                                material_category_name.append(
                                    category.get("material_category_name")
                                )
                                device_name_set.add(category.get("device_name"))
                            for d in data:
                                # 获取所有品名信息并去重
                                if (
                                        d.get("material_category_name")
                                        in material_category_name
                                ):
                                    exist_product_name_set.add(d.get("material_category_name"))
                            for item in material_category_name_query:
                                for key, value in item.items():
                                    if value in exist_product_name_set:
                                        exist_device_name_set.add(item.get("device_name"))
                            diff_set = device_name_set - exist_device_name_set
                        else:
                            self.workflow_detail.add_kv_table(
                                "获取po信息", {"message": "此物料id无法获取品名信息"}
                            )
                else:
                    self.workflow_detail.add_kv_table(
                        "获取po信息", {"message": "此物料id无法获取品名信息"}
                    )
            else:
                elements_to_remove = {"第三方测试", "项管", "监理"}
                db = mysql.new_mysql_instance("tbconstruct")
                sql = (
                    "SELECT device_name,material_category_name FROM equipment_category_mapping_relationship"
                )
                material_category_name_query = db.query(sql)
                device_name_set = set()
                for category in material_category_name_query:
                    device_name_set.add(category.get("device_name"))
                diff_set = device_name_set - elements_to_remove
        if diff_set:
            elements_to_remove = {"第三方测试", "项管", "监理"}
            is_get_po = "1"
            precondition_list_2 = []
            risk_item = ''
            diff_set -= elements_to_remove
            for item in diff_set:
                risk_item += f'{item}、'
            risk_item = risk_item.rstrip('、') + '未获取到PO信息'
            precondition_list_2.append({
                "risk_item": '部分PO未下发',
                "state": "预警",
                "effect": '供应商暂不具备资源投入条件，影响项目交付时间，存在延期风险',
                "details_reason": f"设备：{risk_item}，'项目即将启动，请确认供应商可参与启动会并配合项目推进，"
                                  f"如有问题请于 24h 内回复邮件并同步 pm ，如无异议将按计划启动项目。'"
            })
            variables = {
                "precondition_list_2": precondition_list_2,
                "is_get_po": is_get_po
            }
        else:
            is_get_po = "0"
            variables = {
                "is_get_po": is_get_po
            }
        flow.complete_task(self.ctx.task_id, variables=variables)
