from iBroker.lib.sdk import flow, tof
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService


# 邮件推送
class QualityEvaluationEmailSend(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        email_title = process_data.get("email_title")
        email_content = process_data.get("email_content")
        email_receiver = process_data.get("email_receiver")
        email_Cc = process_data.get("email_Cc")
        email_receiver_list = email_receiver.split(";")
        email_Cc_list = []
        if email_Cc:
            email_Cc_list = email_Cc.split(";")
        for i in range(len(email_Cc_list)):
            email_Cc_list[i] += "@tencent.com"
        for j in range(len(email_receiver_list)):
            email_receiver_list[j] += "@tencent.com"
        tof.send_email(
            sendTitle=email_title,
            msgContent=email_content,
            sendTo=email_receiver_list,
            sendCopy=email_Cc_list,
        )
        variables = {
            "email_title": email_title,
            "email_content": email_content,
            "email_receiver": email_receiver,
            "email_Cc": email_Cc,
            "email_receiver_list": email_receiver_list,
            "email_Cc_list": email_Cc_list,
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
