import re

from iBroker.lib.sdk import flow, gnetops
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql


class BOMDockingBasicData(AjaxTodoBase):
    """
        规划设计平台对接：
            数据上传
    """

    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        # 工单号
        ticket_id = self.ctx.variables.get("ticket_id")
        # 项目名称
        project_name = process_data.get('project_name')
        db = mysql.new_mysql_instance('tbconstruct')
        sql = "SELECT item_number FROM risk_early_warning_data " \
              f"WHERE project_name = '{project_name}' "
        code_id_list = db.query(sql)
        if code_id_list:
            code_id = code_id_list[0].get('item_number')
        else:
            code_id = ''
        upload_plan = process_data.get('upload_plan')
        if upload_plan:
            upload_plan_url = upload_plan[0]["response"]["FileList"][0]["url"]
        else:
            return {"code": -1, "msg": "未上传计划"}
        insert_data = {
            "project_name": project_name,
            "ticket_id": ticket_id,
            "upload_plan_url": upload_plan_url
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert("program_and_planning_process", insert_data)
        tb_db.commit()

        # 存流程变量
        variables = {
            "project_name": project_name,
            "upload_plan": upload_plan,
            "upload_plan_url": upload_plan_url,
            "code_id": code_id
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class BOMDockingSpecificationRetrieval(object):
    """
        规划设计平台对接：
            调规划设计平台接口
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def specification_retrieval(self):
        instance_id = self.ctx.instance_id
        code_id = self.ctx.variables.get('code_id')
        data = {
            "SceneType": "数据分析",
            "TicketDescription": 'tb建设图纸深化设计_规划设计平台',
            "TicketLevel": "3",
            "TicketTitle": 'tb建设图纸深化设计_规划设计平台',
            "ProcessDefinitionKey": 'tb建设图纸深化设计_规划设计平台',
            "UserInfo": {
                "Concern": 'v_zhenlyang',
                "Creator": 'v_zhenlyang',
                "Deal": 'v_zhenlyang'
            },
            "CustomVariables": {
                "receivers": ['v_mtdcwang'],
                "parent_instance_id": instance_id,
                "construction_code": code_id,
                "step": 1,
            }
        }
        gnetops.request(action='Ticket', method='Create', data=data)
        flow.complete_task(self.ctx.task_id)


class BOMDockingJudgeSpecification(object):
    """
        规划设计平台对接：
            规格书是回传成功
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def specification_judge(self):
        instance_id = self.ctx.instance_id
        code_id = self.ctx.variables.get("code_id")
        # 规格书
        technical_specification = self.ctx.variables.get('Technical_Specification')

        if technical_specification:
            data = {
                "SceneType": "数据分析",
                "TicketDescription": 'tb建设图纸深化设计_规划设计平台',
                "TicketLevel": "3",
                "TicketTitle": 'tb建设图纸深化设计_规划设计平台',
                "ProcessDefinitionKey": 'tb建设图纸深化设计_规划设计平台',
                "UserInfo": {
                    "Concern": 'v_zhenlyang',
                    "Creator": 'v_zhenlyang',
                    "Deal": 'v_zhenlyang'
                },
                "CustomVariables": {
                    "receivers": ['v_mtdcwang'],
                    "parent_instance_id": instance_id,
                    "construction_code": code_id,
                    "step": 2,
                }
            }
            gnetops.request(action='Ticket', method='Create', data=data)

            flow.complete_task(self.ctx.task_id)
            return {"success": True, "data": "流程已结束"}


class BOMDockingSpecificationInterface(object):
    """
        规划设计平台对接：
            规格书存库
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def specification_callback(self):
        ticket_id = self.ctx.variables.get('ticket_id')
        project_name = self.ctx.variables.get('project_name')
        # 项目名称
        project_code = self.ctx.variables.get('Specification_Project_Name')
        # 模组信息
        mozu_data = self.ctx.variables.get('tb_version')
        module_and_project = {}
        if mozu_data:
            mozu_data['ticket_id'] = ticket_id
            mozu_data['project_name'] = project_name
            module_and_project['project_name'] = project_name
            module_and_project["module_name"] = mozu_data.get('mozu')
            module_and_project['item_number'] = mozu_data.get('construction_code')
        else:
            mozu_data = {
                'ticket_id': ticket_id,
                'project_name': project_name
            }
        # 规格书版本
        technical_specification_version = self.ctx.variables.get('tech_specs_version')
        # 规格书
        technical_specification = self.ctx.variables.get('Technical_Specification')
        # 时间
        time = self.ctx.variables.get('Specification_Time')
        # 提交人
        submitter = self.ctx.variables.get('Specification_Submitter')
        if project_code is None or technical_specification is None:
            return {"code": -1, "message": "参数不全"}
        # 计划规格书
        # 规格书
        specification = technical_specification.get('规格书部分')
        # 图纸
        drawing = technical_specification.get('图纸部分')
        # 计划文档
        document = technical_specification.get('技术文档部分')
        # 判断各部分是否存在
        if specification is None or drawing is None or document is None:
            return {"code": -1, "message": "规格书/图纸/技术文档传参错误"}
        specification_list = []
        # 构造规格书存库
        insert_list = []
        for title, content in specification.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '技术规格书':
                    content = content_str
                elif key == '会议纪要':
                    summary = content_str
            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '规格书部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                })
            specification_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '规格书部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': submitter,
                'time': time
            })
        # 图纸
        drawing_list = []
        for title, content in drawing.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '图纸':
                    content = content_str
                elif key == '概述':
                    summary = content_str
            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '图纸部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                }
            )
            drawing_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '图纸部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': submitter,
                'time': time
            })
        # 技术文档
        document_list = []
        for title, content in document.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '参数':
                    content = content_str
                elif key == '概述':
                    summary = content_str

            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '技术文档部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                }
            )
            document_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '技术文档部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': submitter,
                'time': time
            })
        update_data = {
            "plan_specifications_time": time,
            "plan_specifications_submitter": submitter,
            'technical_specification_version': technical_specification_version
        }
        conditions = {"project_name": project_name,
                      "ticket_id": ticket_id, }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("program_and_planning_process", update_data, conditions)
        tb_db.insert_batch("program_specifications_data", insert_list)
        tb_db.insert("project_module_information", mozu_data)
        tb_db.insert("project_module_mapping_relationship", module_and_project)
        tb_db.commit()
        variables = {
            "specification_list": specification_list,
            "drawing_list": drawing_list,
            "document_list": document_list,
        }

        flow.complete_task(self.ctx.task_id, variables=variables)


class BOMDockingJudgeEquipmentList(object):
    """
        规划设计平台对接：
            判断设计文件是否回传成功
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def equipment_list_judge(self):
        # []
        device_drawing = self.ctx.variables.get('Design_Drawing')
        #
        material_and_identification = self.ctx.variables.get('Material_and_Identification')

        if device_drawing and material_and_identification:
            flow.complete_task(self.ctx.task_id)
            return {"success": True, "data": "流程已结束"}


class BOMDockingEquipmentListInterface(object):
    """
        规划设计平台对接：
            设计文件数据回传
    """

    def __init__(self):
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def equipment_list_callback(self):
        ticket_id = self.ctx.variables.get('ticket_id')
        # 项目名称
        project_name = self.ctx.variables.get('project_name')
        project_code = self.ctx.variables.get('Device_Project_Name')
        # 图纸：[]+提交人
        device_drawing_data = self.ctx.variables.get('Design_Drawing')
        material_and_identification_data = self.ctx.variables.get('Material_and_Identification')
        # 时间
        time = self.ctx.variables.get('Device_Time')
        if project_code is None or device_drawing_data is None or material_and_identification_data is None:
            return {"code": -1, "message": "参数不全"}

        # 材料清单
        material_and_identification = material_and_identification_data.get('data')
        material_inset_data = []
        if material_and_identification:
            for i in material_and_identification:
                material_inset_data.append({
                    'project_name': project_name,
                    'ticket_id': ticket_id,
                    "device_name_chinese": i.get('device_name_chinese'),
                    "device_name_english": i.get('device_name_english'),
                    "device_routine_name": i.get('device_routine_name'),
                    "device_display_name": i.get('device_display_name'),
                    "ApplicationTypeCode": i.get('ApplicationTypeCode'),
                    "ApplicationTypeName": i.get('ApplicationTypeName'),
                    "BelongApplicationTypeName": i.get('BelongApplicationTypeName'),
                    "CompleteMachine": i.get('CompleteMachine'),
                    "BelongApplicationName": i.get('BelongApplicationName'),
                    "zone_name": i.get('zone_name'),
                    "function_room_name_cn": i.get('function_room_name_cn'),
                    "function_room_name_en": i.get('function_room_name_en'),
                    "quantity": i.get('quantity'),
                    "device_No": i.get('device_No'),
                    "device_category": i.get('device_category'),
                    "whether_monitor": i.get('whether_monitor')
                })

        # 材料清单提交人
        material_and_identification_submitter = material_and_identification_data.get('deal_user')
        # 设计图纸
        device_drawing = device_drawing_data.get('data')
        # 设计图纸提交人
        device_drawing_submitter = device_drawing_data.get('deal_user')
        # 深化设计图纸
        # 规格书
        specification = device_drawing.get('规格书部分')
        # 图纸
        drawing = device_drawing.get('图纸部分')
        # 计划文档
        document = device_drawing.get('技术文档部分')
        # 判断各部分是否存在
        if specification is None or drawing is None or document is None:
            return {"code": -1, "message": "规格书/图纸/技术文档传参错误"}
        deepen_specification_list = []
        # 构造规格书存库
        insert_list = []
        for title, content in specification.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '技术规格书':
                    content = content_str
                elif key == '会议纪要':
                    summary = content_str
            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '规格书部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                })
            deepen_specification_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '规格书部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': device_drawing_submitter,
                'time': time
            })
        # 图纸
        deepen_drawing_list = []
        for title, content in drawing.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '图纸':
                    content = content_str
                elif key == '概述':
                    summary = content_str
            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '图纸部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                }
            )
            deepen_drawing_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '图纸部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': device_drawing_submitter,
                'time': time
            })
        # 技术文档
        deepen_document_list = []
        for title, content in document.items():
            for key, value in content.items():
                if isinstance(value, list):
                    # 如果值是列表，则将列表中的值合成字符串
                    content_str = ";".join(value)
                else:
                    content_str = value
                if key == '参数':
                    content = content_str
                elif key == '概述':
                    summary = content_str

            insert_list.append(
                {
                    "ticket_id": ticket_id,
                    "project_name": project_name,
                    'father': '技术文档部分',
                    'name': title,
                    'content': content,
                    'summary': summary
                }
            )
            deepen_document_list.append({
                "ticket_id": ticket_id,
                "project_name": project_name,
                'father': '技术文档部分',
                'name': title,
                'content': content,
                'summary': summary,
                'submitter': device_drawing_submitter,
                'time': time
            })

        # 存库
        insert_data = {
            "device_documents_time": time,
            "design_drawings_submitter": device_drawing_submitter,
            "material_and_identification_submitter": material_and_identification_submitter
        }
        conditions = {"project_name": project_name,
                      "ticket_id": ticket_id, }

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.insert_batch("program_equipment_material_list", material_inset_data)
        tb_db.insert_batch("program_design_drawings", insert_list)
        tb_db.update('program_and_planning_process', insert_data, conditions)

        tb_db.commit()
        variables = {
            "device_drawings": device_drawing,
            "device_drawings_submitter": device_drawing_submitter,
            "device_time": time,
            "deepen_document_list": deepen_document_list,
            "deepen_drawing_list": deepen_document_list,
            "deepen_specification_list": deepen_document_list
        }

        flow.complete_task(self.ctx.task_id, variables=variables)


class DisplayBOMData(AjaxTodoBase):
    def __init__(self):
        super().__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id)
