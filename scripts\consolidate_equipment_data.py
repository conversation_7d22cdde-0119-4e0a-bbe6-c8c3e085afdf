#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备数据合并脚本
用于将复杂的多表JOIN查询结果合并到单一表中，避免重复数据和性能问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iBroker.lib import mysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_consolidated_table():
    """创建合并表"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS consolidated_equipment_data (
        state VARCHAR(50) DEFAULT NULL,
        TicketId VARCHAR(100) NOT NULL,
        Title VARCHAR(255) DEFAULT NULL,
        SchemeNameCn VARCHAR(255) DEFAULT NULL,
        IsForceEnd VARCHAR(255) DEFAULT '0',
        project_name VARCHAR(255) DEFAULT NULL,
        device_name VARCHAR(255) DEFAULT NULL,
        supplier VARCHAR(255) DEFAULT NULL,
        device_type VARCHAR(100) DEFAULT NULL,
        equipment_sla VARCHAR(50) DEFAULT NULL,
        expected_time_equipment DATETIME DEFAULT NULL,
        estimated_time_delivery DATETIME DEFAULT NULL,
        delivery_gap INT DEFAULT NULL,
        completion_time DATETIME DEFAULT NULL,
        po_create_time DATETIME DEFAULT NULL,
        PRIMARY KEY (TicketId)
    );
    """
    
    try:
        db.execute(create_table_sql)
        logger.info("合并表创建成功")
        return True
    except Exception as e:
        logger.error(f"创建合并表失败: {e}")
        return False

def consolidate_data():
    """合并数据到新表 - 基于实际表结构的分步查询版本"""
    db = mysql.new_mysql_instance("tbconstruct")

    # 先清空目标表
    try:
        db.execute("DELETE FROM consolidated_equipment_data")
        logger.info("清空目标表成功")
    except Exception as e:
        logger.error(f"清空目标表失败: {e}")
        return False

    try:
        # 第一步：获取基础工单数据 (all_construction_ticket_data + equipment_production_racking_process)
        logger.info("第一步：获取基础工单数据")
        base_query = """
        SELECT DISTINCT
            actd.TicketId,
            actd.Title,
            actd.SchemeNameCn,
            actd.IsForceEnd,
            eprp.project_name,
            eprp.device_name,
            eprp.equipment_type
        FROM all_construction_ticket_data actd
        LEFT JOIN equipment_production_racking_process eprp
            ON eprp.ticket_id = actd.TicketId
        WHERE actd.SchemeNameCn LIKE '%设备生产%'
            AND (actd.IsForceEnd IS NULL OR actd.IsForceEnd != '1')
            AND eprp.equipment_type = '甲供'
            AND actd.TicketId IS NOT NULL
            AND actd.TicketId != ''
            AND eprp.project_name IS NOT NULL
            AND eprp.device_name IS NOT NULL
        ORDER BY actd.TicketId
        LIMIT 5000
        """

        base_results = db.get_all(base_query)
        logger.info(f"获取基础数据: {len(base_results)} 条记录")

        # 第二步：获取交付计划管理数据
        logger.info("第二步：获取交付计划管理数据")
        dsm_query = """
        SELECT project_name, device_type, supplier, equipment_sla,
               expected_time_equipment, estimated_time_delivery, delivery_gap
        FROM delivery_schedule_management_table
        WHERE project_name IS NOT NULL
        """
        dsm_results = db.get_all(dsm_query)

        # 构建交付计划数据字典 - 使用项目名称和设备类型作为key
        dsm_dict = {}
        for row in dsm_results:
            project_name = row.get('project_name')
            device_type = row.get('device_type')
            if project_name and device_type:
                key = (project_name, device_type)
                dsm_dict[key] = row
        logger.info(f"获取交付计划数据: {len(dsm_results)} 条记录，构建字典: {len(dsm_dict)} 条")

        # 第三步：获取设备分类映射数据
        logger.info("第三步：获取设备分类映射数据")
        ecmr_query = """
        SELECT device_name, equipment_sla, material_category_name
        FROM equipment_category_mapping_relationship
        WHERE device_name IS NOT NULL
        """
        ecmr_results = db.get_all(ecmr_query)

        # 构建设备分类映射字典
        ecmr_dict = {}
        for row in ecmr_results:
            device_name = row.get('device_name')
            if device_name:
                ecmr_dict[device_name] = {
                    'equipment_sla': row.get('equipment_sla'),
                    'material_category_name': row.get('material_category_name')
                }
        logger.info(f"获取设备分类映射数据: {len(ecmr_results)} 条记录，构建字典: {len(ecmr_dict)} 条")

        # 第四步：获取项目状态数据 (summary_data)
        logger.info("第四步：获取项目状态数据")
        sd_query = """
        SELECT campus, project_name, state
        FROM summary_data
        WHERE campus IS NOT NULL AND project_name IS NOT NULL AND state IS NOT NULL
        """
        sd_results = db.get_all(sd_query)

        # 构建项目状态字典 - 使用campus+project_name作为key
        sd_dict = {}
        for row in sd_results:
            campus = row.get('campus')
            project_name = row.get('project_name')
            if campus and project_name:
                full_project_name = f"{campus}{project_name}"
                sd_dict[full_project_name] = row.get('state')
        logger.info(f"获取项目状态数据: {len(sd_results)} 条记录，构建字典: {len(sd_dict)} 条")

        # 第五步：获取建设计划数据 (construct_plan_data)
        logger.info("第五步：获取建设计划数据")
        cpd_query = """
        SELECT project_name, work_content, completion_time
        FROM construct_plan_data
        WHERE project_name IS NOT NULL AND work_content IS NOT NULL
        """
        cpd_results = db.get_all(cpd_query)

        # 构建建设计划字典
        cpd_dict = {}
        for row in cpd_results:
            project_name = row.get('project_name')
            work_content = row.get('work_content')
            if project_name and work_content:
                key = (project_name, work_content)
                cpd_dict[key] = row.get('completion_time')
        logger.info(f"获取建设计划数据: {len(cpd_results)} 条记录，构建字典: {len(cpd_dict)} 条")

        # 第六步：获取付款信息数据 (payment_info)
        logger.info("第六步：获取付款信息数据")
        pi_query = """
        SELECT project_name, po_create_time
        FROM payment_info
        WHERE project_name IS NOT NULL
        """
        pi_results = db.get_all(pi_query)

        # 构建付款信息字典
        pi_dict = {}
        for row in pi_results:
            project_name = row.get('project_name')
            if project_name:
                pi_dict[project_name] = row.get('po_create_time')
        logger.info(f"获取付款信息数据: {len(pi_results)} 条记录，构建字典: {len(pi_dict)} 条")

        # 第七步：合并数据并插入
        logger.info("第七步：开始合并数据并插入")
        insert_count = 0
        for row in base_results:
            ticket_id = row.get('TicketId')
            project_name = row.get('project_name')
            device_name = row.get('device_name')

            if not ticket_id:
                continue

            # 尝试多种方式匹配交付计划数据
            dsm_data = {}
            # 方式1: 使用项目名称和设备名称匹配
            dsm_key1 = (project_name, device_name)
            if dsm_key1 in dsm_dict:
                dsm_data = dsm_dict[dsm_key1]
            else:
                # 方式2: 遍历查找包含设备名称的记录
                for (proj, dev_type), data in dsm_dict.items():
                    if proj == project_name and (device_name in dev_type or dev_type in device_name):
                        dsm_data = data
                        break

            # 获取供应商和设备类型信息
            supplier = dsm_data.get('supplier') or '待确认'
            device_type = dsm_data.get('device_type') or device_name or '未知设备'

            # 获取设备SLA信息
            equipment_sla = dsm_data.get('equipment_sla')
            if not equipment_sla:
                ecmr_info = ecmr_dict.get(device_name, {})
                equipment_sla = ecmr_info.get('equipment_sla') or '待确认'

            # 获取时间信息
            expected_time_equipment = dsm_data.get('expected_time_equipment')
            estimated_time_delivery = dsm_data.get('estimated_time_delivery')
            delivery_gap = dsm_data.get('delivery_gap')

            # 获取项目状态
            state = sd_dict.get(project_name) or '未知状态'

            # 获取完成时间
            completion_time = cpd_dict.get((project_name, device_name))

            # 获取PO创建时间
            po_create_time = pi_dict.get(project_name)

            # 插入合并数据
            insert_sql = """
            INSERT IGNORE INTO consolidated_equipment_data (
                state, TicketId, Title, SchemeNameCn, IsForceEnd,
                project_name, device_name, supplier, device_type, equipment_sla,
                expected_time_equipment, estimated_time_delivery, delivery_gap,
                completion_time, po_create_time
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            try:
                db.execute(insert_sql, (
                    state,
                    ticket_id,
                    row.get('Title'),
                    row.get('SchemeNameCn'),
                    row.get('IsForceEnd', 0),
                    project_name,
                    device_name,
                    supplier,
                    device_type,
                    equipment_sla,
                    expected_time_equipment,
                    estimated_time_delivery,
                    delivery_gap,
                    completion_time,
                    po_create_time
                ))
                insert_count += 1
            except Exception as e:
                logger.warning(f"插入记录失败 {ticket_id}: {e}")
                continue

        logger.info(f"数据合并成功，插入记录数: {insert_count}")

        # 查询合并后的记录数
        count_result = db.get_one("SELECT COUNT(*) as count FROM consolidated_equipment_data")
        logger.info(f"合并表总记录数: {count_result['count']}")

        return True
    except Exception as e:
        logger.error(f"数据合并失败: {e}")
        return False

def clean_and_fix_data():
    """清理和修复数据中的异常值"""
    db = mysql.new_mysql_instance("tbconstruct")

    try:
        # 修复异常的供应商数据
        fix_supplier_sql = """
        UPDATE consolidated_equipment_data
        SET supplier = '待确认'
        WHERE supplier IN ('错了', '尖没有', 'NULL', '')
           OR supplier IS NULL
        """
        db.execute(fix_supplier_sql)

        # 修复异常的设备类型数据
        fix_device_type_sql = """
        UPDATE consolidated_equipment_data
        SET device_type = device_name
        WHERE device_type IN ('NULL', '')
           OR device_type IS NULL
        """
        db.execute(fix_device_type_sql)

        # 修复异常的equipment_sla数据
        fix_sla_sql = """
        UPDATE consolidated_equipment_data
        SET equipment_sla = '待确认'
        WHERE equipment_sla IN ('NULL', '')
           OR equipment_sla IS NULL
        """
        db.execute(fix_sla_sql)

        # 修复异常的状态数据
        fix_state_sql = """
        UPDATE consolidated_equipment_data
        SET state = '未知状态'
        WHERE state IN ('NULL', '')
           OR state IS NULL
        """
        db.execute(fix_state_sql)

        logger.info("数据清理完成")
        return True
    except Exception as e:
        logger.error(f"数据清理失败: {e}")
        return False

def verify_data():
    """验证数据完整性"""
    db = mysql.new_mysql_instance("tbconstruct")

    try:
        # 检查是否有重复的TicketId
        duplicate_check = db.get_one("""
            SELECT COUNT(*) as count
            FROM consolidated_equipment_data
            GROUP BY TicketId
            HAVING COUNT(*) > 1
            LIMIT 1
        """)

        if duplicate_check:
            logger.warning("发现重复的TicketId记录")
        else:
            logger.info("没有发现重复的TicketId记录")

        # 检查空值情况
        null_check = db.get_one("""
            SELECT
                SUM(CASE WHEN TicketId IS NULL OR TicketId = '' THEN 1 ELSE 0 END) as null_ticket_count,
                SUM(CASE WHEN project_name IS NULL OR project_name = '' THEN 1 ELSE 0 END) as null_project_count,
                SUM(CASE WHEN state IS NULL OR state = '' THEN 1 ELSE 0 END) as null_state_count,
                SUM(CASE WHEN supplier IS NULL OR supplier = '' THEN 1 ELSE 0 END) as null_supplier_count,
                SUM(CASE WHEN device_type IS NULL OR device_type = '' THEN 1 ELSE 0 END) as null_device_type_count,
                SUM(CASE WHEN equipment_sla IS NULL OR equipment_sla = '' THEN 1 ELSE 0 END) as null_sla_count
            FROM consolidated_equipment_data
        """)

        logger.info(f"数据质量检查 - 空TicketId: {null_check['null_ticket_count']}, "
                   f"空项目名: {null_check['null_project_count']}, "
                   f"空状态: {null_check['null_state_count']}, "
                   f"空供应商: {null_check['null_supplier_count']}, "
                   f"空设备类型: {null_check['null_device_type_count']}, "
                   f"空SLA: {null_check['null_sla_count']}")

        # 显示数据样本
        sample_data = db.get_all("""
            SELECT TicketId, project_name, device_name, supplier, device_type, equipment_sla, state
            FROM consolidated_equipment_data
            LIMIT 10
        """)

        logger.info("数据样本:")
        for row in sample_data:
            logger.info(f"  {row}")

        return True
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始设备数据合并操作")

    # 创建合并表
    if not create_consolidated_table():
        logger.error("创建合并表失败，退出")
        return False

    # 合并数据
    if not consolidate_data():
        logger.error("数据合并失败，退出")
        return False

    # 清理和修复数据
    if not clean_and_fix_data():
        logger.error("数据清理失败，退出")
        return False

    # 验证数据
    if not verify_data():
        logger.error("数据验证失败，退出")
        return False

    logger.info("设备数据合并操作完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
