from iBroker.lib.sdk import gnetops, flow
from iBroker.lib import mysql, curl, config
from biz.cooperative_partner.project_application_partner.tools import Tools
from biz.project_interface.process_progress_by_project import ProcessProgressByProject
import hashlib
import hmac
import json
import time


def MakeAuthorization(
    systemId: str, secretKey: str, requestBodyData: str, isHmacSha512: bool = True
) -> dict:
    """
    生成一个签名头部, 支持HMAC-SHA-256 / HMAC-SHA-512 两种签名算法
    :param systemId:
    :param secretKey: 签名密钥
    :param requestBodyData: 向API接口所发送的请求数据的body部分
    :param isHmacSha512: 指定为 true 使用 HMAC-SHA-512算法签名, 否则使用HMAC-SHA-256算法签名
    :return: 返回完成签名的头部数据
    """
    timestamp = int(time.time())

    # 拼装需要参与签名的数据
    strToSig = "{}{}".format(timestamp, requestBodyData)

    # 确定签名算法
    algo = hashlib.sha512
    algoName = "HMAC-SHA-512"
    if not isHmacSha512:
        algo = hashlib.sha256
        algoName = "HMAC-SHA-256"

    # 开始签名
    h = hmac.new(bytes.fromhex(secretKey), bytes(strToSig, "utf-8"), digestmod=algo)

    # 签名结果转换为16进制的形式
    signature = h.hexdigest()

    return {
        "Authorization": "{} Timestamp={},Signature={},SystemId={}".format(
            algoName, timestamp, signature, systemId
        ),
    }


def obtain_supplier_information(supplier_info, supplier):
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(supplier_info)

    # 初始化一个变量来存储找到的 SystemId
    system_id = None
    corp_id = None

    # 遍历 info 列表
    for item in info:
        if item["supplier"] == supplier:
            system_id = item["SystemId"]
            corp_id = item["CorpId"]
            break  # 找到后可以退出循环

    return system_id, corp_id


def calling_external_interfaces(interface_info, data):
    str_data = json.dumps(data)
    # 七彩石获取德衡的systemId\appkey\url
    info = config.get_config_map(interface_info)
    url = info.get("Url")
    system_id = info.get("SystemId")
    secret_key = info.get("secretKey")
    # 生成http头部
    headers = MakeAuthorization(system_id, secret_key, str_data, True)
    # 发送请求
    resp = curl.post(
        title="Dcops请求外部接口",
        system_name="Dcops",
        url=url,
        postdata=str_data,
        append_header=headers,
        timeout=1800,
    )
    result = resp.json()
    return result


class ConstructionApplicationProcessPartnerApi(object):

    # 建单调用
    @staticmethod
    def create_partner_ticket(project_name):
        result = ConstructionApplicationProcessPartnerApi.create_construction_application_ticket(
            project_name
        )
        if result:
            if result.get("code") == 0:
                data = result.get("data")
                ticket_id = data.get("TencentTicketId")
                return {
                    "ticket_id": ticket_id,
                    "msg": "已创建工单",
                    "code": 0,
                }
            else:
                return {
                    "ticket_id": "",
                    "msg": f"建单接口调用失败, 错误信息: {result}",
                    "code": -1,
                }
        else:
            return {
                "ticket_id": "",
                "msg": "建单接口调用异常，无数据返回",
                "code": -1,
            }

    # 创建合作伙伴-项目报建工单
    @staticmethod
    def create_construction_application_ticket(project_name):
        data = {
            "ProjectName": project_name,  # 项目名称
        }
        Facilitator = ""
        db = mysql.new_mysql_instance("tbconstruct")
        sql = (
            "SELECT integrators FROM supplier_resources "
            f"WHERE CONCAT(campus, project) = '{project_name}' "
        )
        res = db.get_row(sql)
        if res:
            Facilitator = res.get("integrators")
        # 获取供应商系统名单
        cooperative_partner_supplier_distinguish = config.get_config_map(
            "cooperative_partner_supplier_distinguish"
        )
        if Facilitator in cooperative_partner_supplier_distinguish["Dcops"]:
            system_type = "Dcops"
        elif Facilitator in cooperative_partner_supplier_distinguish["外部"]:
            system_type = "外部"
        else:
            system_type = "未知"
        # 此节点再七彩石配置中的key
        interface_info = "create_order"
        supplier_info = "supplier_differentiation"
        system_id, corp_id = obtain_supplier_information(supplier_info, Facilitator)
        if system_type == "Dcops":
            # 此节点再七彩石配置中的key
            interface_info = "create_order"
            # 构造请求数据
            data = {
                "SystemId": "13",
                "Action": "Nbroker",
                "Method": "UrlProxy",
                "NbrokerData": {
                    "context": {
                        "service_name": "TCForward",  # (*必填) 原样填写
                        "method_name": "index",  # (*必填) 原样填写
                    },
                    "args": {
                        "function_name": "create_project_application_construction",  # (*必填) 云函数名称
                        "data": data,  # 传递给云函数的入参
                        "systemId": f"{system_id}",  # 为服务商分配的systemId，用于区分服务商云函数
                    },
                },
                "ServiceUrl": "http://nbroker-common:8080/nBroker/api/v1/task",
            }
            info = calling_external_interfaces(interface_info, data)
            if info.get("status") == 0:
                info_data = info.get("data", {})
                if info_data.get("result"):
                    result_data = json.loads(info_data.get("result"))
                    if ConstructionApplicationProcessPartnerApi.check_result_format(
                        info, "Dcops"
                    ):
                        result = {
                            "code": result_data.get("code"),
                            "data": result_data.get("data"),
                            "message": result_data.get("message"),
                        }
                    else:
                        result = {
                            "code": 400,
                            "data": result_data,
                            "message": "数据返回格式有误：{code,data,message}",
                        }
            else:
                result = info
            # # 获取 code 的值
            # code = result_str.get("code")
            # message = result_str.get("msg")
            # if code != 0:
            #     raise Exception(f"报错信息为： {message}")
        elif system_type == "外部":
            info = gnetops.request(
                action="Request",
                method="RequestToFacilitator",
                ext_data={
                    "Facilitator": Facilitator,
                    "ReqData": data,
                    "RequestMethod": "POST",
                    "RequestUrl": "api/custom/new_vertiv_hiddenapi_dcops/Build/create_project_application_construction",
                },
                timeout=1800,
            )
            # if info.get("ErrorCode") != 0:
            #     raise Exception(f'报错信息为：{info.get("Message")}')
            if "status" in info and info.get("status") != 0:
                result = info
            else:
                if ConstructionApplicationProcessPartnerApi.check_result_format(
                    info, "外部"
                ):
                    result = {
                        "code": info.get("ErrorCode"),
                        "data": info.get("Data"),
                        "message": info.get("Message"),
                    }
                else:
                    result = {
                        "code": 400,
                        "data": info,
                        "message": "数据返回格式有误：{ErrorCode,Data,Message}",
                    }
        return result

    # 校验数据返回格式
    @staticmethod
    def check_result_format(result, type):
        if type == "Dcops":
            required_keys = ["data", "code", "message"]
        else:
            required_keys = ["Data", "ErrorCode", "Message"]
        if not all(key in result for key in required_keys):
            return False
        return True

    # 更新子任务工单信息
    @staticmethod
    def query_subtask_ticket_info(construction_plan, ticket_id):
        # 获取工单信息
        instance_id = Tools.get_instance_id(ticket_id)
        main_flow_vars = flow.get_variables(
            instance_id,
            ["subtask_ticket", "PartnerTicketId", "project_name"],
        )
        subtask_ticket = main_flow_vars.get("subtask_ticket", {})
        PartnerTicketId = main_flow_vars.get("PartnerTicketId", "")
        project_name = main_flow_vars.get("project_name", "")
        # 查询子单信息
        subtask_ticket_id_list = [
            val for key, val in subtask_ticket.items() if val and val != "不涉及"
        ]
        data = {
            "SchemaId": "ticket_base",
            "Data": {
                "ResultColumns": {
                    "InstanceId": "",
                    "TicketId": "",
                    "TicketStatus": "",
                    "CurrentTasks": "",
                    "CurrentAllProcessUsers": "",
                },
                "SearchCondition": {"TicketId": subtask_ticket_id_list},
            },
        }
        res = gnetops.request(action="QueryData", method="Run", ext_data=data)
        res_list = res.get("List")
        # 处理子单信息
        subtask_ticket_info = {}
        for t in res_list:
            ticket_id = t.get("TicketId")
            subtask_ticket_info[ticket_id] = {
                "instance_id": t.get("InstanceId"),
                "ticket_id": ticket_id,
                "ticket_status": (
                    "进行中" if t.get("TicketStatus") == "OPEN" else "已完成"
                ),
                "current_tasks": t.get("CurrentTasks"),
                "current_all_process_users": t.get("CurrentAllProcessUsers"),
            }
        # 查询合作伙伴平台工单信息
        partner_ticket_res = ProcessProgressByProject.partner_work_order_status_query(
            object, PartnerTicketId, project_name
        )
        for item in construction_plan:
            serial_number = item.get("serial_number")
            not_involved = item.get("not_involved")
            if not_involved == 1:
                item["ticket_id"] = "不涉及"
            else:
                subtask_ticket_id = subtask_ticket.get(serial_number)
                item["ticket_id"] = subtask_ticket_id
                ticket_info = subtask_ticket_info.get(subtask_ticket_id, {})
                if ticket_info:
                    item["ticket_status"] = ticket_info.get("ticket_status")
                    item["current_tasks"] = ticket_info.get("current_tasks")
                    item["current_all_process_users"] = ticket_info.get(
                        "current_all_process_users"
                    )
                    # 驳回后 等待集成商反馈 无需展示数据
                    if "等待集成商反馈" not in ticket_info.get("current_tasks"):
                        subtask_instance_id = ticket_info.get("instance_id")
                        subtask_flow_vars = flow.get_variables(
                            subtask_instance_id,
                            ["work_list"],
                        )
                        work_list = subtask_flow_vars.get("work_list", [])
                        if work_list:
                            work_dict = work_list[0]
                            for key, val in work_dict.items():
                                item[key] = val
                    else:
                        # 节点在合作伙伴平台
                        if partner_ticket_res["code"] == 0:
                            for info in partner_ticket_res["data"]:
                                if item["work_content"] in info.get("current_task"):
                                    item["current_all_process_users"] = info.get(
                                        "process_user"
                                    )
                        else:
                            item["current_all_process_users"] = partner_ticket_res.get(
                                "msg"
                            )
        return construction_plan
