from iBroker.lib import mysql


class DatabaseConnector(object):
    def query_data(self):
        db = mysql.new_mysql_instance("tbconstruct")
        query_sql = "SELECT area, campus, project_name, integration_unit," \
                    " external_electrical_mode, external_electrical_capacity," \
                    "state FROM summary_data"

        result = db.get_all(query_sql)

        area_dict = {}
        for row in result:
            area = row["area"]
            campus_name = row["campus"]
            project_name = row["project_name"]
            integration_unit = row["integration_unit"]
            external_electrical_mode = row["external_electrical_mode"]
            external_electrical_capacity = row["external_electrical_capacity"]
            state = row['state']

            # 检查是否已存在该 area
            if area in area_dict:
                campus_dict = area_dict[area]["campus"]
                # 检查是否已存在该 campusName
                if campus_name in campus_dict:
                    campus = campus_dict[campus_name]
                    campus["project"].append({
                        "ProjectName": project_name,
                        "IntegrationUnit": integration_unit,
                        "ExternalElectricalMode": external_electrical_mode,
                        "ExternalElectricalCapacity": external_electrical_capacity,
                        'State': state
                    })
                else:
                    campus_dict[campus_name] = {
                        "campusName": campus_name,
                        "project": [{
                            "ProjectName": project_name,
                            "IntegrationUnit": integration_unit,
                            "ExternalElectricalMode": external_electrical_mode,
                            "ExternalElectricalCapacity": external_electrical_capacity,
                            'State': state
                        }]
                    }
            else:
                area_dict[area] = {
                    "area": area,
                    "campus": {
                        campus_name: {
                            "campusName": campus_name,
                            "project": [{
                                "ProjectName": project_name,
                                "IntegrationUnit": integration_unit,
                                "ExternalElectricalMode": external_electrical_mode,
                                "ExternalElectricalCapacity": external_electrical_capacity,
                                'State': state
                            }]
                        }
                    }
                }

        data = []
        for area_data in area_dict.values():
            area_data["campus"] = list(area_data["campus"].values())
            data.append(area_data)

        # 过滤掉 area 为空的项
        data = [d for d in data if d['area']]
        return data

    def query_area(self, area, campus, projectName):
        db = mysql.new_mysql_instance("tbconstruct")
        if area and not campus and not projectName:
            query_sql = "SELECT SUM(external_electrical_capacitytotal) as total_capacity," \
                        "external_power_utilization_rate," \
                        "(SELECT " \
                        "   count(external_power_lines) " \
                        "   FROM summary_data " \
                        f"   WHERE area = '{area}'" \
                        ") " \
                        "   as external_power_lines," \
                        "transformer_station," \
                        "external_electrical_mode," \
                        "external_electrical_capacity," \
                        "superior_station," \
                        "DTU," \
                        "outer_line_diameter," \
                        "line_mode " \
                        "FROM summary_data" \
                        f" WHERE area = '{area}' " \
                        "GROUP BY external_power_utilization_rate, " \
                        "transformer_station, external_electrical_mode, " \
                        "superior_station, DTU, outer_line_diameter, line_mode, external_electrical_capacity"
        elif campus and area and not projectName:
            query_sql = "SELECT SUM(external_electrical_capacitytotal) as total_capacity," \
                        "external_power_utilization_rate," \
                        "(SELECT " \
                        "   count(external_power_lines) " \
                        "   FROM summary_data " \
                        f"  WHERE campus = '{campus}'" \
                        f"  AND area = '{area}'" \
                        ") " \
                        "   as external_power_lines," \
                        "transformer_station," \
                        "external_electrical_mode," \
                        "external_electrical_capacity," \
                        "superior_station," \
                        "DTU," \
                        "outer_line_diameter," \
                        "line_mode " \
                        "FROM summary_data" \
                        f" WHERE campus = '{campus}' AND area = '{area}' " \
                        "GROUP BY external_power_utilization_rate, " \
                        "transformer_station, external_electrical_mode, " \
                        "superior_station, DTU, outer_line_diameter, line_mode, external_electrical_capacity"
        elif campus and projectName and not area:
            query_sql = "SELECT SUM(external_electrical_capacitytotal) as total_capacity," \
                        "external_power_utilization_rate," \
                        "(SELECT " \
                        "   count(external_power_lines) " \
                        "   FROM summary_data " \
                        f"  WHERE campus = '{campus}'" \
                        f"  AND project_name = '{projectName}'" \
                        ") " \
                        "   as external_power_lines," \
                        "transformer_station," \
                        "external_electrical_mode," \
                        "external_electrical_capacity," \
                        "superior_station," \
                        "DTU," \
                        "outer_line_diameter," \
                        "line_mode " \
                        "FROM summary_data" \
                        f" WHERE campus = '{campus}' AND project_name = '{projectName}' " \
                        "GROUP BY external_power_utilization_rate, " \
                        "transformer_station, external_electrical_mode, " \
                        "superior_station, DTU, outer_line_diameter, line_mode, external_electrical_capacity"
        result = db.query(query_sql)

        data = {}
        for row in result:
            total_capacity = row.get("total_capacity", None)
            external_power_lines = row.get("external_power_lines", None)
            external_power_utilization_rate = row.get("external_power_utilization_rate", None)
            transformer_station = row.get("transformer_station", None)
            external_electrical_mode = row.get("external_electrical_mode", None)
            external_electrical_capacity = row.get("external_electrical_capacity", None)
            superior_station = row.get("superior_station", None)
            DTU = row.get("DTU", None)
            outer_line_diameter = row.get("outer_line_diameter", None)
            line_mode = row.get("line_mode", None)

            if area and not campus and not projectName:
                data = {
                    'ExternalPowerLinesTotal': external_power_lines,
                    'ExternalElectricalCapacitytotal': total_capacity,
                    'ExternalPowerUtilizationRate': external_power_utilization_rate,
                    'TransformerStation': transformer_station
                }
            elif campus and area and not projectName:
                data = {
                    'ExternalPowerLinesTotal': external_power_lines,
                    'ExternalElectricalCapacitytotal': total_capacity,
                    'ExternalPowerUtilizationRate': external_power_utilization_rate,
                    'TransformerStation': transformer_station
                }
            elif campus and projectName and not area:
                data = {
                    'ExternalElectricalMode': external_electrical_mode,
                    'ExternalElectricalCapacity': external_electrical_capacity,
                    'SuperiorStation': superior_station,
                    'DTU': DTU,
                    'ExternalPowerUtilizationRate': external_power_utilization_rate,
                    'OuterLineDiameter': outer_line_diameter,
                    'LineMode': line_mode
                }

        return data
