#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备数据合并脚本 - 简化版本用于验证修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from iBroker.lib import mysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_data_quality():
    """测试数据质量"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    try:
        # 检查合并表是否存在
        table_check = db.get_one("SHOW TABLES LIKE 'consolidated_equipment_data'")
        if not table_check:
            logger.error("合并表不存在")
            return False
        
        # 检查总记录数
        count_result = db.get_one("SELECT COUNT(*) as count FROM consolidated_equipment_data")
        total_count = count_result['count']
        logger.info(f"合并表总记录数: {total_count}")
        
        if total_count == 0:
            logger.warning("合并表为空")
            return False
        
        # 检查各字段的数据质量
        quality_check = db.get_one("""
            SELECT 
                COUNT(*) as total_count,
                SUM(CASE WHEN supplier IS NULL OR supplier = '' OR supplier = 'NULL' THEN 1 ELSE 0 END) as null_supplier,
                SUM(CASE WHEN device_type IS NULL OR device_type = '' OR device_type = 'NULL' THEN 1 ELSE 0 END) as null_device_type,
                SUM(CASE WHEN equipment_sla IS NULL OR equipment_sla = '' OR equipment_sla = 'NULL' THEN 1 ELSE 0 END) as null_sla,
                SUM(CASE WHEN state IS NULL OR state = '' OR state = 'NULL' THEN 1 ELSE 0 END) as null_state,
                SUM(CASE WHEN supplier = '待确认' THEN 1 ELSE 0 END) as pending_supplier,
                SUM(CASE WHEN equipment_sla = '待确认' THEN 1 ELSE 0 END) as pending_sla,
                SUM(CASE WHEN state = '未知状态' THEN 1 ELSE 0 END) as unknown_state
            FROM consolidated_equipment_data
        """)
        
        logger.info("数据质量报告:")
        logger.info(f"  总记录数: {quality_check['total_count']}")
        logger.info(f"  空供应商: {quality_check['null_supplier']}")
        logger.info(f"  空设备类型: {quality_check['null_device_type']}")
        logger.info(f"  空SLA: {quality_check['null_sla']}")
        logger.info(f"  空状态: {quality_check['null_state']}")
        logger.info(f"  待确认供应商: {quality_check['pending_supplier']}")
        logger.info(f"  待确认SLA: {quality_check['pending_sla']}")
        logger.info(f"  未知状态: {quality_check['unknown_state']}")
        
        # 显示前10条记录作为样本
        sample_data = db.get_all("""
            SELECT TicketId, project_name, device_name, supplier, device_type, equipment_sla, state
            FROM consolidated_equipment_data 
            ORDER BY TicketId
            LIMIT 10
        """)
        
        logger.info("数据样本 (前10条):")
        for i, row in enumerate(sample_data, 1):
            logger.info(f"  {i}. TicketId: {row['TicketId']}, "
                       f"项目: {row['project_name']}, "
                       f"设备: {row['device_name']}, "
                       f"供应商: {row['supplier']}, "
                       f"设备类型: {row['device_type']}, "
                       f"SLA: {row['equipment_sla']}, "
                       f"状态: {row['state']}")
        
        # 检查是否还有异常值
        abnormal_data = db.get_all("""
            SELECT TicketId, supplier, device_type, equipment_sla
            FROM consolidated_equipment_data 
            WHERE supplier IN ('错了', '尖没有') 
               OR device_type IN ('错了', '尖没有')
               OR equipment_sla IN ('错了', '尖没有')
            LIMIT 5
        """)
        
        if abnormal_data:
            logger.warning("发现异常数据:")
            for row in abnormal_data:
                logger.warning(f"  TicketId: {row['TicketId']}, "
                              f"供应商: {row['supplier']}, "
                              f"设备类型: {row['device_type']}, "
                              f"SLA: {row['equipment_sla']}")
        else:
            logger.info("未发现异常数据")
        
        return True
        
    except Exception as e:
        logger.error(f"数据质量检查失败: {e}")
        return False

def show_table_structure():
    """显示表结构"""
    db = mysql.new_mysql_instance("tbconstruct")
    
    try:
        structure = db.get_all("DESCRIBE consolidated_equipment_data")
        logger.info("合并表结构:")
        for field in structure:
            logger.info(f"  {field['Field']}: {field['Type']} "
                       f"{'NOT NULL' if field['Null'] == 'NO' else 'NULL'} "
                       f"{'DEFAULT ' + str(field['Default']) if field['Default'] else ''}")
        return True
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始测试设备数据合并结果")
    
    # 显示表结构
    if not show_table_structure():
        logger.error("显示表结构失败")
        return False
    
    # 测试数据质量
    if not test_data_quality():
        logger.error("数据质量测试失败")
        return False
    
    logger.info("测试完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
