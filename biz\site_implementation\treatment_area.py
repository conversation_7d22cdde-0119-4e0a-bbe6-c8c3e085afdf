import uuid
import numpy as np
import pandas as pd
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.netops_todo import GnetopsTodoEnd
from iBroker.sdk.todo.todo_base import AjaxTodoBase
from iBroker.sdk.task.context import get_context
from iBroker.sdk.workflow.workflow import WorkflowDetailService
from iBroker.lib import mysql

from biz.construction_process.cos_lib import COSLib


class FirewoodHairWaterTreatmentArea(AjaxTodoBase):
    """
        柴发和水的处理区域
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(FirewoodHairWaterTreatmentArea, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_itemized_plan_is_upload(self, cfscl_sub_plan: list):
        try:
            datas, bools = cfscl_sub_plan[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1, "msg": "分项计划未上传完成，请等待分项计划上传完成后再做结单处理"}, True
        return datas, bools

    def verify_whether_the_upload_is_correct(self, cfscl_sub_plan: list):
        url = cfscl_sub_plan[0]["response"]["FileList"][0]["url"]
        relative_url = url.split("cosfile")[1]
        new_file_name = str(uuid.uuid4()) + '.xlsx'
        print(new_file_name)
        COSLib.download_2(file_name=relative_url, save_to_file=new_file_name)
        """
            excel柴发和水处理区域解析分项计划
        """
        exclpath = "/data/nbroker/" + new_file_name
        df = pd.read_excel(exclpath, engine='openpyxl')
        df = df.replace({np.nan: None})
        for _, row in df.iterrows():
            serial_number = row['序号']
            task_name = row['工作内容']
            if serial_number == 3.6 and task_name == '柴发和水处理区域':
                return {"code": 0, "msg": "分项计划上传正确"}, False
        return {"code": -1, "msg": "分项计划上传错误，请重新上传"}, True

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        cfscl_sub_plan = process_data.get('cfscl_sub_plan', None)
        datas, bools = self.check_itemized_plan_is_upload(cfscl_sub_plan)
        sub_plan_data, sub_plan_bool = self.verify_whether_the_upload_is_correct(cfscl_sub_plan)
        if bools:
            return datas
        if sub_plan_bool:
            return sub_plan_data
        update_data = {}
        if cfscl_sub_plan and len(cfscl_sub_plan) > 0 and 'response' in cfscl_sub_plan[0]:
            update_data = {
                'cfscl_sub_plan': cfscl_sub_plan[0]["response"]["FileList"][0]["url"]
            }
        conditions = {
            'dcops_ticket_id': ticket_id
        }
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'cfscl_sub_plan': cfscl_sub_plan
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class InstallationChaiFaWarehouse(AjaxTodoBase):
    """
        柴发仓库安装
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(InstallationChaiFaWarehouse, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        cfscl_cffc_list = process_data.get("cfscl_cffc_list", {})
        work_list = cfscl_cffc_list.get("work_list", [])

        cfscl_cffc_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            cfscl_cffc_dict = {
                'work_content': '柴发方仓安装',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'cfscl_cffc_dict': cfscl_cffc_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'cffcaz_actual_start_time': actual_start_time,
                'cffcaz_actual_finish_time': actual_finish_time,
                'cffcaz_actual_construction_time': actual_construction_time,
                'cffcaz_schedule_deviation': schedule_deviation,
                'cffcaz_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '柴发方仓安装',
                'cffcaz_work_content': work_content
            }
            tb_db.update("firewoodhair_water_data", update2_data, conditions2)
        complete_report = cfscl_cffc_list.get('complete_report', None)
        complete_photo = cfscl_cffc_list.get('complete_photo', None)
        complete_report_review_report = cfscl_cffc_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'cffcaz_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'cffcaz_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'cffcaz_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '柴发方仓安装'
        }
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'cfscl_cffc_list': cfscl_cffc_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'cfscl_cffc_dict': cfscl_cffc_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class WaterTreatment(AjaxTodoBase):
    """
        水处理
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(WaterTreatment, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        cfscl_scl_list = process_data.get("cfscl_scl_list", {})
        work_list = cfscl_scl_list.get("work_list", [])

        cfscl_scl_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            cfscl_scl_dict = {
                'work_content': '水处理',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            variables = {
                'cfscl_scl_dict': cfscl_scl_dict
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'scl_actual_start_time': actual_start_time,
                'scl_actual_finish_time': actual_finish_time,
                'scl_actual_construction_time': actual_construction_time,
                'scl_schedule_deviation': schedule_deviation,
                'scl_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '水处理',
                'scl_work_content': work_content
            }
            tb_db.update("firewoodhair_water_data", update2_data, conditions2)
        complete_report = cfscl_scl_list.get('complete_report', None)
        complete_photo = cfscl_scl_list.get('complete_photo', None)
        complete_report_review_report = cfscl_scl_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'scl_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'scl_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'scl_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '水处理'
        }
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'cfscl_scl_list': cfscl_scl_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'cfscl_scl_dict': cfscl_scl_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class OilCircuitSystemChaiFa(AjaxTodoBase):
    """
        柴发油路系统
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(OilCircuitSystemChaiFa, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_complete_report_is_upload(self, complete_report: list):
        try:
            datas1, bools1 = complete_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        except IndexError:
            datas1, bools1 = {"code": -1, "msg": "完成报告未上传完成，请等待完成报告上传完成后再做结单处理"}, True
        return datas1, bools1

    def check_complete_photo_is_upload(self, complete_photo: list):
        try:
            datas2, bools2 = complete_photo[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        except IndexError:
            datas2, bools2 = {"code": -1, "msg": "完成照片未上传完成，请等待完成照片上传完成后再做结单处理"}, True
        return datas2, bools2

    def check_audit_report_is_upload(self, complete_report_review_report: list):
        try:
            datas3, bools3 = complete_report_review_report[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        except IndexError:
            datas3, bools3 = {"code": -1, "msg": "审核报告未上传完成，请等待审核报告上传完成后再做结单处理"}, True
        return datas3, bools3

    def end(self, process_data, process_user):
        ticket_id = self.ctx.variables.get("ticket_id")
        cfscl_cfly_list = process_data.get("cfscl_cfly_list", {})
        work_list = cfscl_cfly_list.get("work_list", [])

        cfscl_cfly_dict = {}
        skip_or_not = process_data.get("skip_or_not", '')
        skip_or_not_remark = process_data.get("skip_or_not_remark", '')

        if skip_or_not == '是':
            if not skip_or_not_remark:
                return {'code': -1, 'msg': '跳过该节点时请填写原因'}
            cfscl_cfly_dict = {
                'work_content': '柴发油路系统',
                'skip_or_not': skip_or_not,
                'skip_or_not_remark': skip_or_not_remark
            }
            cfscl_cffc_dict = self.ctx.variables.get('cfscl_cffc_dict')
            cfscl_scl_dict = self.ctx.variables.get('cfscl_scl_dict')

            cfscl_skip_list = []
            if cfscl_cffc_dict:
                cfscl_skip_list.append(cfscl_cffc_dict)
            if cfscl_scl_dict:
                cfscl_skip_list.append(cfscl_scl_dict)
            if cfscl_cfly_dict:
                cfscl_skip_list.append(cfscl_cfly_dict)


            variables = {
                'cfscl_cfly_dict': cfscl_cfly_dict,
                'cfscl_skip_list': cfscl_skip_list
            }
            GnetopsTodoEnd(self.ctx.task_id, process_user)
            flow.complete_task(self.ctx.task_id, variables=variables)
        else:
            cfscl_cffc_dict = self.ctx.variables.get('cfscl_cffc_dict')
            cfscl_scl_dict = self.ctx.variables.get('cfscl_scl_dict')

            cfscl_skip_list = []
            if cfscl_cffc_dict:
                cfscl_skip_list.append(cfscl_cffc_dict)
            if cfscl_scl_dict:
                cfscl_skip_list.append(cfscl_scl_dict)
            if cfscl_cfly_dict:
                cfscl_skip_list.append(cfscl_cfly_dict)

        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'ylxt_actual_start_time': actual_start_time,
                'ylxt_actual_finish_time': actual_finish_time,
                'ylxt_actual_construction_time': actual_construction_time,
                'ylxt_schedule_deviation': schedule_deviation,
                'ylxt_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '柴发油路系统',
                'ylxt_work_content': work_content
            }
            tb_db.update("firewoodhair_water_data", update2_data, conditions2)
        complete_report = cfscl_cfly_list.get('complete_report', None)
        complete_photo = cfscl_cfly_list.get('complete_photo', None)
        complete_report_review_report = cfscl_cfly_list.get('complete_report_review_report', None)
        datas1, bools1 = self.check_complete_report_is_upload(complete_report)
        datas2, bools2 = self.check_complete_photo_is_upload(complete_photo)
        datas3, bools3 = self.check_audit_report_is_upload(complete_report_review_report)
        if bools1:
            return datas1
        if bools2:
            return datas2
        if bools3:
            return datas3
        update_data = {}
        if complete_report and len(complete_report) > 0 and 'response' in complete_report[0]:
            update_data = {
                'ylxt_complete_report': complete_report[0]["response"]["FileList"][0]["url"],
                'ylxt_complete_photo': complete_photo[0]["response"]["FileList"][0]["url"],
                'ylxt_complete_report_review_report': complete_report_review_report[0]["response"]["FileList"][0][
                    "url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '柴发油路系统'
        }
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'cfscl_cfly_list': cfscl_cfly_list,
            'complete_report': complete_report,
            'complete_photo': complete_photo,
            'complete_report_review_report': complete_report_review_report,
            'cfscl_skip_list': cfscl_skip_list,
            'cfscl_cfly_dict': cfscl_cfly_dict
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)


class CfsclPMApproval(AjaxTodoBase):
    """
        柴发水处理方仓-PM审批
    """

    def __init__(self):
        super(CfsclPMApproval, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def end(self, process_data, process_user):
        cfscl_PM_approval = process_data.get('cfscl_pm_approval')
        cfscl_PM_remark = process_data.get('cfscl_PM_remark')

        if cfscl_PM_approval == '驳回':
            cfscl_skip_list = []
            if not cfscl_PM_remark:
                return {'code': -1, 'msg': '驳回时，未填写备注'}

        variables = {
            'cfscl_PM_approval': cfscl_PM_approval,
            'cfscl_PM_remark': cfscl_PM_remark,
            'cfscl_skip_list': cfscl_skip_list
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)

class DevicePowerDebuggingChaiFa(AjaxTodoBase):
    """
        设备上电调试——柴发
    """

    def update(self, process_data, process_user):
        pass

    def __init__(self):
        super(DevicePowerDebuggingChaiFa, self).__init__()
        self.ctx = get_context()
        self.workflow_detail = WorkflowDetailService()

    def start(self):
        pass

    def check_power_debug_is_upload(self, power_debug: list):
        try:
            datas, bools = power_debug[0]["response"]["FileList"][0]["url"], False
        except KeyError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        except IndexError:
            datas, bools = {"code": -1,
                            "msg": "设备上电调试报告未上传完成，请等待设备上电调试报告上传完成后再做结单处理"}, True
        return datas, bools

    def end(self, process_data, process_user):
        cfscl_sdts_list = process_data.get('cfscl_sdts_list')
        ticket_id = self.ctx.variables.get("ticket_id")
        work_list = cfscl_sdts_list.get("work_list", [])
        tb_db = mysql.new_mysql_instance("tbconstruct")
        tb_db.begin()
        for work in work_list:
            actual_start_time = work.get("actual_start_time", None)
            actual_finish_time = work.get("actual_finish_time", None)
            actual_construction_time = work.get("actual_construction_time", None)
            schedule_deviation = work.get("schedule_deviation", None)
            remark = work.get("remark", None)
            work_content = work.get("work_content", None)
            update2_data = {
                'sdts_actual_start_time': actual_start_time,
                'sdts_actual_finish_time': actual_finish_time,
                'sdts_actual_construction_time': actual_construction_time,
                'sdts_schedule_deviation': schedule_deviation,
                'sdts_remarks': remark,
            }
            conditions2 = {
                'dcops_ticket_id': ticket_id,
                'task_name': '设备上电调试',
                'sdts_work_content': work_content,
            }
            tb_db.update("firewoodhair_water_data", update2_data, conditions2)
        power_debug = cfscl_sdts_list.get('power_debug', None)
        datas, bools = self.check_power_debug_is_upload(power_debug)
        if bools:
            return datas
        update_data = {}
        if power_debug and len(power_debug) > 0 and 'response' in power_debug[0]:
            update_data = {
                'cfscl_power_debug': power_debug[0]["response"]["FileList"][0]["url"],
            }
        conditions = {
            'dcops_ticket_id': ticket_id,
            'task_name': '设备上电调试'
        }
        tb_db.update("firewoodhair_water_data", update_data, conditions)
        tb_db.commit()
        variables = {
            'cfscl_sdts_list': cfscl_sdts_list,
            'cfscl_power_debug': power_debug
        }
        GnetopsTodoEnd(self.ctx.task_id, process_user)
        flow.complete_task(self.ctx.task_id, variables=variables)
