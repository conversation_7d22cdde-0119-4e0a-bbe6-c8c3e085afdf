"""
转岗确认相关逻辑
<AUTHOR>
@created 2021/4/19
"""
from iBroker.lib import common, exception
from iBroker.lib.sdk import flow
from iBroker.sdk.todo.todo_base import AjaxTodoBase


class AjaxRolePermAudit(AjaxTodoBase):
    """
    权限审批确认相关逻辑
    """

    def __init__(self):
        super(AjaxRolePermAudit, self).__init__()

    def start(self):
        pass

    def update(self, process_data, process_user):
        pass

    def query(self):
        return self.parse()

    def end(self, process_data: dict, process_user):
        is_pass = int(process_data.get("IsPass"))
        if is_pass not in [1, 2]:
            raise exception.LogicException("IsPass参数不合法", exception.EC_DATA_INVALID)
        flow_vars = {
            "RealAuditor": process_user,
            "IsPass": int(process_data.get("IsPass")),
            "RejectReason": process_data.get("RejectReason", ""),
            "AuditTime": common.date(),
        }
        flow.complete_task(self.ctx.task_id, flow_vars)
