# 设备数据合并解决方案

## 问题描述

在`campus_resources.py`的`tabular_data_query`方法中遇到以下问题：

1. **变量引用错误**: `UnboundLocalError: local variable 'device_record' referenced before assignment`
2. **SQL查询复杂**: 需要JOIN多个表（`summary_data`）来获取项目状态
3. **数据重复问题**: 添加JOIN后需要去掉`GROUP BY`和`ORDER BY`会导致重复数据
4. **性能问题**: 复杂的多表JOIN查询导致查询缓慢

## 解决方案

### 1. 修复变量引用错误

**问题位置**: `campus_resources.py` 第2775行
**修复方法**: 确保`device_record`变量在使用前正确赋值

```python
# 修复前（有问题）
device_record[f'{project_key}production_work_order'] = row.get('TicketId') or ''

# 修复后
device_record = processed_devices[device_key]
device_record[f'{project_key}production_work_order'] = row.get('TicketId') or ''
```

### 2. 创建数据合并表

为了解决复杂JOIN和重复数据问题，创建了`consolidated_equipment_data`合并表：

**表结构**:
```sql
CREATE TABLE consolidated_equipment_data (
    state VARCHAR(50) DEFAULT NULL,           -- 项目状态（来自summary_data表）
    TicketId VARCHAR(100) NOT NULL,          -- 工单号（主键）
    Title VARCHAR(255) DEFAULT NULL,         -- 标题
    SchemeNameCn VARCHAR(255) DEFAULT NULL,  -- 方案名称
    IsForceEnd VARCHAR(255) DEFAULT '0',     -- 是否强制结束
    project_name VARCHAR(255) DEFAULT NULL,  -- 项目名称
    device_name VARCHAR(255) DEFAULT NULL,   -- 设备名称
    supplier VARCHAR(255) DEFAULT NULL,      -- 供应商
    device_type VARCHAR(100) DEFAULT NULL,   -- 设备类型
    equipment_sla VARCHAR(50) DEFAULT NULL,  -- 设备SLA
    expected_time_equipment DATETIME DEFAULT NULL,    -- 预期设备时间
    estimated_time_delivery DATETIME DEFAULT NULL,    -- 预计交付时间
    delivery_gap INT DEFAULT NULL,           -- 交付间隔
    completion_time DATETIME DEFAULT NULL,   -- 完成时间
    po_create_time DATETIME DEFAULT NULL,    -- PO创建时间
    PRIMARY KEY (TicketId)
);
```

### 3. 数据合并脚本

创建了`scripts/consolidate_equipment_data.py`脚本来执行数据合并：

**主要功能**:
- 创建合并表
- 执行复杂的多表JOIN查询并将结果插入合并表
- 使用`DISTINCT`和`ON DUPLICATE KEY UPDATE`避免重复数据
- 数据质量验证

**使用方法**:
```bash
cd iBroker-construct
python scripts/consolidate_equipment_data.py
```

### 4. 简化查询逻辑

修改`tabular_data_query`方法，使用合并表进行简单查询：

```python
# 修改前（复杂JOIN）
unified_query = f"""
    SELECT DISTINCT actd.TicketId, ...
    FROM all_construction_ticket_data actd
    LEFT JOIN equipment_production_racking_process eprp ON ...
    LEFT JOIN summary_data sd ON ...
    WHERE {complex_conditions}
    GROUP BY actd.TicketId
    ORDER BY actd.TicketId, eprp.device_name
"""

# 修改后（简单查询）
unified_query = f"""
    SELECT TicketId, Title, ..., state as project_state
    FROM consolidated_equipment_data
    WHERE 1=1
    {additional_conditions}
    ORDER BY TicketId, device_name LIMIT 3000
"""
```

## 实施步骤

### 步骤1: 执行数据合并
```bash
# 运行数据合并脚本
python scripts/consolidate_equipment_data.py
```

### 步骤2: 验证修复效果
1. 测试不选择任何条件的查询（应该不再报`device_record`错误）
2. 测试选择园区的查询（应该返回数据且页面显示正常）
3. 测试选择项目状态的查询（应该能正确过滤）

### 步骤3: 定期数据同步
建议设置定时任务，定期执行数据合并脚本以保持数据同步：

```bash
# 添加到crontab，每小时执行一次
0 * * * * cd /path/to/iBroker-construct && python scripts/consolidate_equipment_data.py
```

## 优势

1. **性能提升**: 避免复杂的多表JOIN，查询速度显著提升
2. **数据一致性**: 通过合并表确保数据一致性，避免重复
3. **维护简便**: 查询逻辑简化，易于维护和调试
4. **扩展性好**: 可以轻松添加新字段到合并表
5. **错误修复**: 彻底解决了`device_record`变量引用错误

## 注意事项

1. **数据同步**: 需要定期运行合并脚本保持数据最新
2. **存储空间**: 合并表会占用额外的存储空间
3. **数据一致性**: 源表数据变更时需要及时更新合并表
4. **监控**: 建议添加监控确保合并脚本正常运行

## 测试验证

### 测试用例1: 无条件查询
- **操作**: 不选择任何筛选条件，直接查询
- **预期**: 不再报`device_record`错误，返回合并表中的所有数据

### 测试用例2: 园区筛选
- **操作**: 选择特定园区进行查询
- **预期**: 返回该园区的相关数据，页面正常显示

### 测试用例3: 项目状态筛选
- **操作**: 选择项目状态（如"已交付"）进行查询
- **预期**: 返回对应状态的项目数据，不再出现重复

### 测试用例4: 组合条件查询
- **操作**: 同时选择园区和项目状态
- **预期**: 返回满足所有条件的数据，查询速度快

## 后续优化建议

1. **索引优化**: 为合并表的常用查询字段添加索引
2. **分区表**: 如果数据量很大，考虑按时间分区
3. **缓存机制**: 对频繁查询的结果进行缓存
4. **实时同步**: 考虑使用触发器或CDC实现实时数据同步
